SELECT
    DISTINCT
    jb.area as '所属一级企业',
    COUNT(DISTINCT jb.rbiId) as '发起审批的次数'
FROM
    (
        SELECT
            o.ORGANIZATION_NAME ,
            rbi.RG_UNITSTATE,
            jbxx.JB_SHZT,
            rbi.CREATE_TIME,
            o.ORGANIZATION_ID,
            o.area,
            jbxx.JB_ZZXS,
            rbi.id as rbiId
        FROM
            (
                SELECT
                    os.IDs,os.ORGANIZATION_NAME as area,os.ORGANIZATION_CODE,
                    o.ORGANIZATION_NAME,
                    o.ORGANIZATION_ID
                FROM
                    sys_organization o
                        JOIN (
                        SELECT o.ORGANIZATION_ID as IDs, o.ORGANIZATION_NAME,o.ORGANIZATION_CODE
                        FROM `sys_organization` o
                        WHERE o.ORGANIZATION_NAME IN (
                                                      '物产中大集团股份有限公司',
                                                      '浙江省海港投资运营集团有限公司',
                                                      '浙江省机电集团有限公司',
                                                      '浙江省国际贸易集团有限公司',
                                                      '浙江省农村发展集团有限公司',
                                                      '浙江省国有资本运营有限公司',
                                                      '杭州钢铁集团有限公司',
                                                      '浙江省能源集团有限公司',
                                                      '浙江省建设投资集团股份有限公司',
                                                      '浙江大学控股集团有限公司',
                                                      '巨化集团有限公司',
                                                      '浙江省自然资源集团有限公司',
                                                      '浙江省机场集团有限公司',
                                                      '浙江省旅游投资集团有限公司',
                                                      '浙江省交通投资集团有限公司',
                                                      '浙江省手工业合作社联合社',
                                                      '安邦护卫集团股份有限公司'
                            )

                    ) as os ON FIND_IN_SET(os.IDs,o.PARENTS )

                WHERE
                        o.PARENTS LIKE '%39DC82B5A000000125E54F37FE103416%'
                  AND
                    o.BUSINESS_LEVEL is not null
            ) o
                LEFT JOIN
            cq_jbxxb jbxx  on (o.ORGANIZATION_ID = jbxx.UNITID)
                left JOIN
            (
                SELECT  rbi.CREATE_TIME,his.id ,rbi.JBXX_ID ,
                        rbi.RG_UNITSTATE,
                        rbi.RG_TIMEMARK
                FROM   `rg_business_info` rbi
                           LEFT JOIN rg_auditflow_history his on his.BUSINESS_INFO_ID = rbi.ID
                WHERE
                    rbi.CREATE_TIME BETWEEN '2025-04-01 00:00:00' and  '2025-04-30 23:59:59'
                  AND
                        his.AF_PROCESSGROUP = '发起人'
            ) rbi on rbi.JBXX_ID = jbxx.ID
        ORDER BY
            rbi.RG_TIMEMARK DESC
        LIMIT ***********
    )jb
GROUP BY
    jb.area