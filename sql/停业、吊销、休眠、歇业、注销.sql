SELECT
    DISTINCT
    jb.JB_QYMC as '企业名称',
    jb.JB_ZZJGDM as '统一信用代码',
    dic.text as '	国资监管机构类型',
    dic2.text as '国资监管机构明细',
    gjcz.text as '国家出资企业',
    SUBSTRING_INDEX(gjcz.val,'_',-1) as '国家出资企业统一社会信用编码',
    dic3.text as '与国家出资企业关系',
    dic4.text as '企业类别',
    if(jb.JB_HGQY != 1,'否',if(jb.JB_HGQY is not null,'是',null))  '是否混改企业',
    dic5.text '组织形式',
    if(jb.jb_sf_ss != 1,'否',if(jb.jb_sf_ss is not null,'是',null)) '是否上市公司',
    if(jb.jb_sf_bb != 1,'否',if(jb.jb_sf_bb is not null,'是',null)) as '是否并表',
    dic6.text '企业产权级次',
    dic7.text '企业管理级次',
    dic14.text '经营状况'
FROM
    (
        SELECT
            jb.id,
            jb.CREATE_TIME
        FROM
            (
                SELECT
                    jb.id,
                    jb.CREATE_TIME ,
                    UNITID
                FROM
                    sys_organization o
                        JOIN view_cq_jbxxb jb ON jb.UNITID = o.ORGANIZATION_ID
                WHERE
                        jb.JB_SHZT != '8'
                  AND o.isdeleted = 'N'
                ORDER BY
                    jb.CREATE_TIME  desc
                LIMIT 1000000000
            ) jb
        GROUP BY
            jb.UNITID
    )jbo JOIN  view_cq_jbxxb jb on jb.ID = jbo.id
         LEFT JOIN
    (
        select
            t.id,
            t.jbxx_id,
            t.unitid,
            t.name,
            t.hhr_code,
            t.type,
            t.category,
            t.fd_cze,
            t.rjcze,
            t.rjczbl,
            t.sjcze,
            t.czfs,
            t.jfqx,
            t.create_user,
            t.create_time,
            t.last_update_user,
            t.last_update_time,sd1.text typeStr,sd2.text categoryStr,
            sd3.text czfsStr
        from
            cq_hhrqkfd t
                left join sys_dictionary sd1 on sd1.val=t.type and sd1.type_id=(select id from sys_dictionary where type_code='HHRLX')
                left join sys_dictionary sd2 on sd2.val=t.category and sd2.type_id=(select id from sys_dictionary where type_code='HHRLB')
                left join sys_dictionary sd3 on sd3.val=t.czfs and sd3.type_id=
                                                                   (select id from sys_dictionary where type_code=
                                                                                                        case when t.type = '1' then 'CZFSPT' else 'CZFSYX' end)
    )hhqy  on hhqy.jbxx_id = jb.id

         LEFT JOIN cq_czfd cz on cz.JBXX_ID = jb.id

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='GZWJKJGLX')) dic
                   on dic.val = jb.jb_qzwjkjglx

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='GZJGJG')) dic2
                   on dic2.val = jb.jb_gzwjkjgmx

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='GJCZQY')) gjcz
                   on gjcz.val  = jb.JB_GJCZQY

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='RELA')) dic3
                   on dic3.val = jb.JB_RELA

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='QYLB')) dic4
                   on dic4.val = jb.jb_qylb

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='ZZXS')) dic5
                   on dic5.val = jb.jb_ZZXS

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='QYJC')) dic6
                   on dic6.val = jb.JB_QYJC

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='QYGLJC')) dic7
                   on dic7.val = jb.jb_qygljc

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='ZCD')) dic8
                   on dic8.val = jb.jb_zcd

         LEFT JOIN sys_organization zycz on zycz.ORGANIZATION_ID = jb.JB_CZRZZJGID

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='BZXZ')) dic9
                   on dic9.val = IF(jb.JB_RJZBBZ is null or jb.JB_RJZBBZ = '',IF(jb.jb_Zczbbz is not null and jb.jb_Zczbbz != '' ,jb.jb_Zczbbz,null),jb.JB_RJZBBZ)

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic10
                   on dic10.val = jb.JB_ZYHY

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic11
                   on dic11.val = jb.JB_ZYHY1

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic12
                   on dic12.val = jb.JB_ZYHY2

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic13
                   on dic13.val = jb.JB_ZYHY3

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='JYZK')) dic14
                   on dic14.val = jb.jb_JYZK

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='JNJW')) dic15
                   on dic15.val = jb.JB_JNJW

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='SFYBGS')) dic16
                   on dic16.val = jb.JB_SFYBGS

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='ZYCQDJQX')) dic17
                   on dic17.val = jb.JB_ZYCQDJQX

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='BDCQDJQX')) dic18
                   on dic18.val = jb.JB_BDCQDJQX

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='ZXCQDJQX')) dic19
                   on dic19.val = jb.JB_ZXCQDJQX

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='ZCD')) dic20
                   on dic20.val = jb.HH_ZYJYCS

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='RJCZEBC')) dic21
                   on dic21.val = jb.HH_RJCZEBZ

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='RJCZEBC')) dic22
                   on dic22.val = jb.HH_SJCZEBZ

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='CZRLB')) dic23
                   on dic23.val = cz.FD_CZRLB

         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='CZFS')) dic24
                   on dic24.val = cz.FD_CZFS


         left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                  (SELECT id FROM sys_dictionary WHERE type_code ='ZCMD')) dic25
                   on dic25.val = jb.JB_ZCMD

-- 	WHERE
-- 	jb.JB_QYMC ='杭州紫金港未来创新投资合伙企业（有限合伙）'
