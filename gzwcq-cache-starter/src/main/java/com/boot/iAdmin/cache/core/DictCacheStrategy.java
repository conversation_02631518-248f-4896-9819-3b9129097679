package com.boot.iAdmin.cache.core;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.IAdmin.dict.service.api.IDictionaryService;

/**
 * 字典缓存创建
 * */
@SuppressWarnings("unchecked")
public class DictCacheStrategy<T extends DictionaryVo> implements CacheStrategy<T>{
	
	private final ThreadLocal<Map<String,T>> dictCacheHolder = new ThreadLocal<Map<String,T>>();
	
	@Autowired
	private IDictionaryService dictionaryService;
	
	@Autowired
	private RedisTemplate<Object,Object> redisTemplate;//缓存
	
	private static final String CACHE_KEY = "DICT";//缓存key
	
	private String cacheNameSpace;//命名空间
	
	public DictCacheStrategy(String cacheNameSpace) {
		this.cacheNameSpace = cacheNameSpace;
	}
	
	@Override
	public void init() {
		this.buildCache();
	}
	
	//创建缓存
	@Override
	public void buildCache() {
		Map<String,T> dictCache = new HashMap<String,T>();
		//获取所有字典数据
		List<DictionaryVo> allDcit = (List<DictionaryVo>)(Object)dictionaryService.selectForList(null);
		//构造字典类型集合
		for(Dictionary dict : allDcit) {
			if(dict.getParent() != null && dict.getParent().longValue() == -1L && StringUtils.isNotBlank(dict.getType_code())) {//是字典类型
				dictCache.put(dict.getType_code(), (T)dict);
			}
		}
		//构造字典对象
		for(DictionaryVo dict : dictCache.values()) {
			buildDict(dict,allDcit);//构造具体字典
		}
		//缓存为hash
		for(Entry<String,T> e : dictCache.entrySet()) {
			redisTemplate.opsForHash().put(getCacheKey(), e.getKey(), e.getValue());
		}
	}
	
	//构造具体字典
	private void buildDict(DictionaryVo parentDict, List<DictionaryVo> allDcit) {
		for(DictionaryVo dict : allDcit) {
			if(dict.getParent() != null && dict.getParent().longValue() == parentDict.getId().longValue()) {//子节点
				if(parentDict.getDictionaryList() == null) {
					parentDict.setDictionaryList(new ArrayList<DictionaryVo>());
				}
				parentDict.getDictionaryList().add(dict);
				buildDict(dict,allDcit);//递归构造子节点
			}
		}
	}
	
	//刷新缓存
	@Override
	public void refresh() {
		this.buildCache();
	}
	
	/**
	 * 获取缓存对象集合
	 * <P>使用ThreadLocal缓存字典对象，防止单个线程多次查询redis
	 * */
	@Override
	@Deprecated
	public Map<String,T> getCache(){
		throw new UnsupportedOperationException();
	}
	
	/**
	 * 获取缓存对象
	 * <P>此处根据字典类型编码获取字典类型树对象
	 * <P>使用ThreadLocal缓存字典对象，防止单个线程多次查询redis
	 * */
	@Override
	public T getCache(String key) {
		Map<String,T> dict = dictCacheHolder.get();
		if(dict != null && dict.get(key) != null) {
			return dict.get(key);
		}else {
			Object obj = redisTemplate.opsForHash().get(getCacheKey(), key);
			if(obj != null) {
				if(dict == null) {
					dict = new HashMap<String,T>();
				}
				dict.put(key, (T)obj);
				dictCacheHolder.set(dict);
				return (T)obj;
			}
		}
		return null;
	}
	
	/**
	 * 根据字典类型编码获取该类型下的字典列表
	 * <P>先从缓存中获取，如果缓存中无法获取则从数据库获取
	 * */
	public List<Dictionary> getByTypeCodeCommon(String type_code){
		DictionaryVo dictType = this.getCache(type_code);//获取字典类型对象
		if(dictType != null && dictType.getDictionaryList() != null) {
			return (List<Dictionary>)(Object)dictType.getDictionaryList();
		}else {
			//todo 报错源头
			List<Dictionary> dicts = dictionaryService.getByTypeCodeCommon(type_code);
			if(dicts != null && dicts.size() > 0) {//如果数据库存在则需要更新缓存
				this.refresh();
			}
			return dicts;
		}
	}
	
	public DictionaryVo getDicByVal(String type_code, String val) {
		if(StringUtils.isBlank(val)) return null;
		DictionaryVo dic = this.getCache(type_code);
		if(dic != null) {
			return getDicByVal(dic,val);
		}
		return null;
	}
	
	public DictionaryVo getDicByVal(DictionaryVo child, String val) {
		if(StringUtils.equals(child.getVal(), val)) return child;
		if(child.getDictionaryList() == null) return null;
		for(DictionaryVo ch : child.getDictionaryList()) {
			//判断子字典
			DictionaryVo c = this.getDicByVal(ch,val);
			if(c != null) return c;
		}
		return null;
	}
	
	/**
	 * 根据字典类型和父节点ID获取字典列表
	 * */
	public List<Dictionary> getByParent(String type_code,Long parent){
		DictionaryVo dictType = this.getCache(type_code);//获取字典类型对象
		DictionaryVo parentDict = getByParent(dictType,parent);
		if(parentDict != null && parentDict.getDictionaryList() != null) {//缓存中存在则直接返回
			return (List<Dictionary>)(Object)parentDict.getDictionaryList();
		}else {//缓存中未找到，从数据库查找
			List<Dictionary> dicts = dictionaryService.getByParent(type_code,parent);
			if(dicts != null && dicts.size() > 0) {//如果数据库存在且缓存中不存在则需要更新
				this.refresh();
			}
			return dicts;
		}
	}
	
	/**
	 * 根据字典类型编码以及Val获取字典对象
	 * */
	public Dictionary getByTypeCodeAndVal(String type_code,String val){
		List<Dictionary> dicts = this.getByTypeCodeCommon(type_code);
		for(Dictionary dict : dicts) {
			//todo bug原因 val的值没有符合字典表的数据，导致返回null
			if(StringUtils.equalsIgnoreCase(val, dict.getVal())) {
				return dict;
			}
		}
		return null;
	}
	
	/**
	 * 递归查询父字典对象
	 * */
	private DictionaryVo getByParent(DictionaryVo dict, Long parent) {
		if(dict == null) return null;
		if(dict.getId().longValue() == parent.longValue()) {
			return dict;
		}else {
			if(dict.getDictionaryList() != null) {
				for(DictionaryVo childDict : dict.getDictionaryList()) {
					DictionaryVo target = getByParent(childDict,parent);
					if(target != null) return target;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取缓存KEY
	 * */
	private String getCacheKey() {
		return this.cacheNameSpace + ":" + CACHE_KEY;
	}
	
	/**
	 * 根据值获取文本
	 * */
	public String getTextByVal(String type_code, String val) {
		if(StringUtils.isBlank(val)) return null;
		DictionaryVo dic = this.getCache(type_code);
		if(dic != null) {
			return getTextByVal(dic,val);
		}
		return null;
	}
	
	/**
	 * 从字典查找文本，递归
	 * */
	private String getTextByVal(DictionaryVo child, String val) {
		if(StringUtils.equals(child.getVal(), val)) return child.getText();
		if(child.getDictionaryList() == null) return null;
		for(DictionaryVo ch : child.getDictionaryList()) {
			//判断子字典
			String text = this.getTextByVal(ch,val);
			if(text != null) return text;
		}
		return null;
	}

	public ThreadLocal<Map<String, T>> getDictCacheHolder() {
		return dictCacheHolder;
	}
	
	/**
	 * 根据文本获取文值，不支持多层级
	 * * */
	public String getValByTextOne(String type_code, String text) {
		if(StringUtils.isBlank(text)) return null;
		DictionaryVo dic = this.getCache(type_code);
		for(DictionaryVo dict : dic.getDictionaryList()) {
			if(StringUtils.equals(text, dict.getText())) {
				return dict.getVal();
			}
		}
		return null;
	}
	
}
