package com.boot.iAdmin.cache.config;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import com.boot.iAdmin.cache.core.DictCacheStrategy;

/**
 * 线程缓存过滤器
 */
public class CacheThreadLocalCacheFilter implements Filter {
	
	private DictCacheStrategy<?> dictCacheStrategy;
	
	public CacheThreadLocalCacheFilter(DictCacheStrategy<?> dictCacheStrategy) {
		this.dictCacheStrategy = dictCacheStrategy;
	}
	
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        filterChain.doFilter(servletRequest, servletResponse);
        // 执行完后清除缓存
        dictCacheStrategy.getDictCacheHolder().remove();
    }
}