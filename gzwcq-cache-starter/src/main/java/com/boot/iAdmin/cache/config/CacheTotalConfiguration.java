package com.boot.iAdmin.cache.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.iAdmin.cache.core.CacheBeanFactory;
import com.boot.iAdmin.cache.core.DictCacheStrategy;

/**
 * 框架缓存配置类
 * <P>主要用于缓存字典表等相对静态的数据
 * <AUTHOR>
 * */
@Configuration
@ConditionalOnProperty(value = "com.auto.cache.enable", havingValue = "true")
public class CacheTotalConfiguration {
	
	private static final String DEFAULT_NAMESPACE = "iadmin_cache";//缓存命名空间默认值
	
	@Value(value="${com.auto.cache.nameSpace}")
	private String cacheNameSpace = DEFAULT_NAMESPACE;//缓存命名空间
	
	/**
	 * 构造字典缓存
	 * */
	@Bean(initMethod="init")
	public DictCacheStrategy<DictionaryVo> dictCache() {
		return new DictCacheStrategy<DictionaryVo>(getNameSpace());
	}
	
	/**
	 * 获取命名空间
	 * */
	private String getNameSpace() {
		return this.cacheNameSpace.trim() + ":caches";
	}
	
	@Bean
	public CacheBeanFactory cacheBeanFactory() {
		CacheBeanFactory factory = new CacheBeanFactory();
		factory.setDictCacheStrategy(dictCache());
		return factory;
	}
	
	/**
	 * 字典缓存过滤器
	 * */
	@Bean
    public FilterRegistrationBean<CacheThreadLocalCacheFilter> idempotentParamtFilter() {
        FilterRegistrationBean<CacheThreadLocalCacheFilter> registration = new FilterRegistrationBean<CacheThreadLocalCacheFilter>();
        CacheThreadLocalCacheFilter filter = new CacheThreadLocalCacheFilter(dictCache());
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("thread-local-cache-filter");
        registration.setOrder(1);
        return registration;
    }
	
}
