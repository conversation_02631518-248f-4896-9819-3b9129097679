package com.boot.iAdmin.access.controller;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.resource.SysResource;
import com.boot.iAdmin.access.service.api.IResourceService;

import io.swagger.annotations.Api;

/**
 * 资源管理控制器
 * */
@Controller
@RequestMapping("/resourceManageController")
@Api(value="资源管理",tags="资源管理")
public class ResourceManageController {
	
	private final static Log logger = LogFactory.getLog(ResourceManageController.class);
	
	@Autowired
	private IResourceService resourceService;
	
	@RequestMapping("/index")
	public String index(){
		return "/system/resource_manage/index.html";
	}
	
	/**
	 * 异步加载资源树信息
	 * 
	 * @param id 父节点ID
	 * */
	@RequestMapping("/load")
	@ResponseBody
	public Collection<ZTreeNode> load(@RequestParam(value="id",defaultValue="0") String id){
		return resourceService.loadResourceByParentId(id);
	}
	
	/**
	 * 异步加载资源树信息
	 * <P>当前权限已关联的资源默认选中
	 * @param nodeId 父节点ID
	 * @param authority_id 权限ID
	 * */
	@RequestMapping("/loadWithAuth")
	@ResponseBody
	public Collection<ZTreeNode> loadWithAuth(@RequestParam(value="id",defaultValue="0") String nodeId,Long authority_id){
		return resourceService.loadResourceByParentId(nodeId,authority_id);
	}
	
	/**
	 * 获得资源详细信息
	 * 
	 * @param id 资源ID
	 * */
	@RequestMapping("/detail")
	@ResponseBody
	public ResponseEnvelope detail(Long id){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新资源信息
			re.setResult(resourceService.selectResource(new SysResource(id)));
		}catch(Exception e){
			logger.error("查询资源详细信息异常",e);
			re.setMessage("查询资源详细信息失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 更新资源信息
	 * */
	@RequestMapping("/update")
	@ResponseBody
	public ResponseEnvelope update(SysResource resource){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新资源信息
			resourceService.updateResource(resource);
		}catch(Exception e){
			logger.error("更新资源信息异常",e);
			re.setMessage("修改失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 新增资源
	 * */
	@RequestMapping("/add")
	@ResponseBody
	public ResponseEnvelope add(SysResource resource){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			resource.setResource_name("未命名");
			//更新资源信息
			resourceService.insertResource(resource);
		}catch(Exception e){
			logger.error("新增资源异常",e);
			re.setMessage("新增失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 删除资源，包括其下子资源
	 * */
	@RequestMapping("/remove")
	@ResponseBody
	public ResponseEnvelope remove(SysResource resource){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//判断该资源及其子资源是否被(菜单或者权限)引用
			if(resourceService.getTotalAuthAndResource(resource) > 0){
				throw new Exception("该资源或者其子资源被权限引用，无法删除");
			}
			if(resourceService.getTotalMoudleAndResource(resource) > 0){
				throw new Exception("该资源或者其子资源被菜单引用，无法删除");
			}
			//删除资源及其子资源信息
			resourceService.deleteResource(resource);
		}catch(Exception e){
			logger.error("删除资源异常",e);
			re.setMessage(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 弹出资源树
	 * */
	@RequestMapping("/showResourceTreeWindow")
	public String showResourceTreeWindow(){
		return "/system/resource_manage/resourceTree.html";
	}
	
	/**
	 * 参数唯一性验证
	 * */
	@RequestMapping("/validateUniqueParam")
	@ResponseBody
	public ResponseEnvelope validateUniqueParam(SysResource resource) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(resourceService.validateUniqueParam(resource));
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
}
