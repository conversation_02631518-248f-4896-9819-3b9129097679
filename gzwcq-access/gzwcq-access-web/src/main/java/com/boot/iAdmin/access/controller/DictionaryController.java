package com.boot.iAdmin.access.controller;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.domain.ztree.ZTreeNode;
import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.service.api.IDictionaryService;

@Controller
@RequestMapping("dictionaryController")
@Api(tags="字典管理")
public class DictionaryController{
	private final Logger logger = LoggerFactory.getLogger(DictionaryController.class);
	@Autowired
	private IDictionaryService dictionaryService;

	@Autowired
	private DictCacheStrategy<DictionaryVo> dictCacheStrategy;

	/**
	 * 初始化单条记录到编辑页面
	 * @Title: toEdit
	 * <AUTHOR>
	 * @param model
	 * @param dictionary
	 * @return
	 */
	/*@RequestMapping(path="/toEdit",method= RequestMethod.GET)
	public String toEdit(Model model,Dictionary dictionary) {
		DictionaryVo dictionaryVo=dictionaryService.queryDictionaryById(dictionary);
		model.addAttribute("dictionary", dictionaryVo);
		return "/system/dictionary_manage/dictionary_edit.jsp";
	}*/

	/*@RequestMapping(path="/toAdd",method= RequestMethod.GET)
	public String toAdd(Model model,Dictionary dictionary) {
		if(dictionary.getId() > 0) {//不是根节点
			dictionary = dictionaryService.queryDictionaryById(dictionary);
		}
		model.addAttribute("dictionary", dictionary);
		return "/system/dictionary_manage/dictionary_add.jsp";
	}*/
	/**
	 * 分页展示字典表信息
	 * @Title: list
	 * <AUTHOR>
	 * @param BootstrapTableModel
	 * @param Dictionary
	 * @return String
	 */
//	@RequestMapping(path="/list",method= RequestMethod.POST)
//	@ResponseBody
	@Deprecated
	public BootstrapTableModel<Dictionary> list(BootstrapTableModel<Dictionary> bootModel,Dictionary dictionary) {
		bootModel.setObj(dictionary);
		return dictionaryService.queryDictionaryByPage(bootModel);
	}


	/**
	 * 加载字典树
	 * */
	@PostMapping(path="/load")
	@ResponseBody
	@ApiOperation("加载字典树")
	public Collection<ZTreeNode> load(Dictionary dictionary) {
		return dictionaryService.loadByParentId(dictionary);
	}

	/**
	 * 获取一个对象
	 * */
	@RequestMapping(path="/loadOne",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope loadOne(Dictionary dictionary) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dictionaryService.queryDictionaryById(dictionary));
		}catch(Exception e) {
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setMessage(ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	/**
	 * 字典数据创建方法
	 * @Title: list
	 * <AUTHOR>
	 * @param BootstrapTableModel
	 * @param Dictionary
	 * @return String
	 */
	@RequestMapping(path="/create",method= RequestMethod.POST)
	public @ResponseBody ResponseEnvelope create(Dictionary dictionary) {
		ResponseEnvelope re = new ResponseEnvelope();
		if (dictionary != null) {
			//去重处理
//			Dictionary dic=new Dictionary();
//			dic.setType(dictionary.getType());
//			dic.setVal(dictionary.getVal());
//			List<Dictionary> list=dictionaryService.selectForList(dic);
//			if(list.size()>0){
//				return new ResponseEnvelope("字段类型"+dictionary.getType()+"对应值"+dictionary.getVal()+"已存在!");
//			}else{
//				dictionaryService.save(dictionary);
//				return new ResponseEnvelope("保存成功!");
//			}
			dictionaryService.save(dictionary);
			re.setResult(dictionary);
		} else {
			logger.info("数据传输失败!");
			re.setSuccess(false);
		}
		return re;
	}
	/**
	 * 更新程序
	 * @Title: update
	 * <AUTHOR>
	 * @param Dictionary
	 * @return ResponseEnvelope
	 */
	@RequestMapping(path="/update",method= RequestMethod.POST)
	public @ResponseBody ResponseEnvelope update(Dictionary dictionary) {
		if (dictionary != null) {
			dictionaryService.updateIgnoreNull(dictionary);
			return new ResponseEnvelope("保存成功!");
		} else {
			return new ResponseEnvelope("数据传输失败!");
		}
	}
	/**
	 * 删除程序
	 * @Title: delete
	 * <AUTHOR>
	 * @param  String
	 * @return ResponseEnvelope
	 */
	@RequestMapping(path="/delete",method= RequestMethod.POST)
	public @ResponseBody ResponseEnvelope delete(String id,String typeId) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("id",id);
		map.put("typeId",typeId);
		dictionaryService.delete(map);
		return new ResponseEnvelope("删除成功!");
	}

	/**
	 * 根据值获取文本
	 * @Title: getTextByVal
	 * <AUTHOR>
	 * @param val
	 * @param type
	 * @return
	 */
	@RequestMapping(path="/getTextByVal",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope getTextByVal(String val,String type){
		ResponseEnvelope re = new ResponseEnvelope();
		List<DictionaryVo> lists = dictionaryService.selectForList(new Dictionary(type));
		for(Dictionary dic : lists){
			if(val.equals(dic.getVal())) {
				re.setData(dic.getText());
				break;
			}
		}
		return re;
	}

	/**
	 * 根据类型获取文本
	 * @Title: getTextByType
	 * <AUTHOR>
	 * @param type
	 * @return
	 */
//	@RequestMapping(path="/getTextByType",method= RequestMethod.POST)
//	@ResponseBody
	@Deprecated
	public ResponseEnvelope getTextByType(String type){
		ResponseEnvelope re = new ResponseEnvelope();
		List<DictionaryVo> lists = dictionaryService.selectForList(new Dictionary(type));
		re.setResult(lists);
		return re;
	}

	/**
	 * 参数唯一性验证
	 * */
	@RequestMapping(path="/validateUniqueParam",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope validateUniqueParam(Dictionary dictionary) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(dictionaryService.validateUniqueParam(dictionary));
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 根据字段类型获取字典列表
	 * */
	@RequestMapping(path="/getDicsByTypeCommon",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope getDicListByTypeCommon(Dictionary dictionary) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dictionaryService.getByTypeCommon(dictionary.getType()));
		} catch (Exception e) {
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setSuccess(false);
		}
		return re;
	}


	/**
	 * 根据字段类型获取字典列表
	 * */
	@PostMapping(path="/getDicsByTypeCodeCommon")
	@ResponseBody
	@ApiOperation("查询")
	public ResponseEnvelope getDicsByTypeCodeCommon(@RequestBody Dictionary dictionary) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dictionaryService.getByTypeCodeCommon(dictionary.getType_code()));
		} catch (Exception e) {
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 根据字段类型获取字典列表
	 * */
	@PostMapping(path="/getDicsByTypeCodeCommonVo")
	@ResponseBody
	@ApiOperation("查询")
	public ResponseEnvelope getByTypeCodeCommonVo(@RequestBody Dictionary dictionary) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dictionaryService.getByTypeCodeCommonVo(dictionary.getType_code()));
		} catch (Exception e) {
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 根据父节点获取子节点列表
	 * */
	@RequestMapping(path="/getByParent",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope getByParent(@RequestBody Dictionary dictionary) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dictionaryService.getByParent(dictionary.getId()));
		} catch (Exception e) {
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setSuccess(false);
		}
		return re;
	}


	/**
	 * 	根据条件查询所有编码字典信息
	 * */
	@RequestMapping(path="/listAll",method= RequestMethod.POST)
	@ResponseBody
	public Object listAll(Dictionary dictionary) {
		return dictionaryService.selectForList(dictionary);
	}

	/**
	 * 获取指定type_code下所有的节点,以树形输出
	 */
	@PostMapping("/getTreeByTypeCode")
	@ResponseBody
	@ApiOperation("获取指定type_code下所有的节点,以树形输出")
	public ResponseEnvelope getTreeByTypeCode(Dictionary dictionary){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			//先从缓存中查询
			DictionaryVo dictType = dictCacheStrategy.getCache(dictionary.getType_code());//获取字典类型对象
			if(dictType != null && dictType.getDictionaryList() != null) {
				re.setResult(dictType.getDictionaryList());
				return re;
			}else {
				synchronized (this){
					//再去缓存中查一次
					DictionaryVo dictionaryVo = dictCacheStrategy.getCache(dictionary.getType_code());
					if(dictionaryVo != null && dictionaryVo.getDictionaryList() != null) {
						re.setResult(dictionaryVo.getDictionaryList());
						return re;
					}
					//缓存中还没有,再去数据库查询
					List<DictionaryVo> dicts = dictionaryService.getTreeByTypeCode(dictionary.getType_code());
					if(!CollectionUtils.isEmpty(dicts)) {//如果数据库存在则需要更新缓存
						dictCacheStrategy.refresh();
					}
					re.setResult(dicts);
				}
			}
		} catch (Exception e) {
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setSuccess(false);
		}
		return re;
	}

	@PostMapping("/getRootNode")
	@ApiOperation("条件获取根节点")
	@ResponseBody
	public ResponseEnvelope getRootNode(Dictionary dictionary){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dictionaryService.getRootNode(dictionary));
		} catch (Exception e) {
			re.setMessage("QUERY ERROR"+ExceptionUtils.getMessage(e));
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setSuccess(false);
		}
		return re;
	}
}
