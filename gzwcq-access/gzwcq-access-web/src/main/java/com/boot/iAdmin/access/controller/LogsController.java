package com.boot.iAdmin.access.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.sys_logs.Logs;
import com.boot.iAdmin.access.model.sys_logs.LogsVo;
import com.boot.iAdmin.access.service.api.ILogsService;

import io.swagger.annotations.Api;

@Controller
@RequestMapping("sys_logsController")
@Api(value="日志管理",tags="日志管理")
public class LogsController{
//	private final Logger logger = LoggerFactory.getLogger(LogsController.class);
	@Autowired
	private ILogsService logsService;
	/**
	 * 跳转到功能主页面
	 * @Title: index
	 * <AUTHOR>
	 * @return String
	 */
	@RequestMapping("/index")
	public String index() {
		return "/system/logs/logs_index.html";
	}
	/**
	 * 分页查询列表
	 * @Title: list
	 * <AUTHOR>
	 * @param BootstrapTableModel
	 * @param Logs
	 * @return String
	 */
	@RequestMapping("/load")
	@ResponseBody
	public BootstrapTableModel<LogsVo> list(BootstrapTableModel<LogsVo> bootModel,LogsVo logs) {
		bootModel.setObj(logs);
		return logsService.queryLogsByPage(bootModel);
	}
	
	/**
	 * 查询日志详情
	 * @param model
	 * @param Logs
	 * @return String
	 */
	@RequestMapping("/showDetail")
	public String showDetail(Model model,Logs logs) {
		LogsVo logsVo=logsService.queryLogsById(logs);
		model.addAttribute("logs", logsVo);
		return "/system/logs/log_detail.html";
	}

}
