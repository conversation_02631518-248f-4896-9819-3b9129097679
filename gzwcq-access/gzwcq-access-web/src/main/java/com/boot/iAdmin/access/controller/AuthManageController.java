package com.boot.iAdmin.access.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.model.authority.AuthVo;
import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IAuthorityService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 权限管理控制器
 * */
@Controller
@RequestMapping("/authManageController")
@Api(value="权限管理",tags="权限控制")
public class AuthManageController {
	
	private final static Log logger = LogFactory.getLog(AuthManageController.class);
	
	@Autowired
	private IAuthorityService authService;
	
//	@Autowired
//	private IResourceService resourceService;
	
	@RequestMapping("/index")
	public String index(){
		return "/system/auth_manage/index.html";
	}
	
	/**
	 * 权限查询方法
	 * <P>兼容分页
	 * @param page 当前页
	 * @param pageNum 每页显示条数
	 * */
	@RequestMapping(path="/load")
	@ResponseBody
	public BootstrapTableModel<AuthVo> load(BootstrapTableModel<AuthVo> model,AuthVo auth){
		model.setObj(auth);
		return authService.loadAuthByPage(model);
	}
	
	/**
	 * 权限删除方法
	 * <P>逻辑删除
	 * @param authId 角色主键
	 * */
	@RequestMapping(path="/disable")
	@ResponseBody
	@Deprecated
	public ResponseEnvelope disable(Long authId){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			authService.disableAuthById(authId);
		}catch(Exception e){
			logger.error("权限删除异常",e);
			re.setMessage("删除失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 角色恢复方法
	 * <P>逻辑恢复
	 * @param authId 角色主键
	 * */
	@RequestMapping(path="/recover")
	@ResponseBody
	@Deprecated
	public ResponseEnvelope recover(Long authId){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			authService.recoverAuthById(authId);
		}catch(Exception e){
			logger.error("权限恢复异常",e);
			re.setMessage("恢复失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 新增权限界面
	 * 
	 * */
	@RequestMapping(path="/toAdd")
	public String toAdd(Model model){
		return "/system/auth_manage/auth_add.html";
	}
	
	/**
	 * 删除权限方法
	 * <P>物理删除
	 * @param authIds 权限主键集合
	 * */
	@RequestMapping(path="/delete")
	@ResponseBody
	public ResponseEnvelope delete(String authIds){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//判断当前待删除权限是否被用户引用
			List<SysUser> user_lists = authService.judgeDeleteAuthsByAuthIds(authIds);
			if(user_lists != null && user_lists.size() > 0){//如果权限信息被用户引用
				re.setMessage("权限已经被用户信息引用，暂时无法删除，请先删除对应的用户信息");
				re.setSuccess(false);
			}else{
				authService.deleteAuthByIds(authIds);
			}
			
		}catch(Exception e){
			logger.error("权限删除异常",e);
			re.setMessage("删除失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 新增权限
	 * 
	 * @param auth 权限
	 * @param resourceIds 资源集合
	 * */
	@RequestMapping(path="/add")
	@ResponseBody
	public ResponseEnvelope add(Model model,SysAuthorities auth,
			@RequestParam(value="resourceIds",defaultValue="") String resourceIds){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			authService.insertAuth(auth,resourceIds);
		}catch(Exception e){
			logger.error("权限新增异常",e);
			re.setMessage("新增失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	@RequestMapping("/validateAuthorityName")
	public @ResponseBody ResponseEnvelope validateAuthorityName(SysAuthorities auth,HttpServletRequest request) {
		ResponseEnvelope re = new ResponseEnvelope();
		List<String> results = authService.validateAuthorityName(auth);
		if(results != null && results.size() >0 && results.get(0) != null){//查询数据库存在相同的角色名称
			re.setSuccess(false);
		}else{
			re.setSuccess(true);
		}
		return re;
	}
	
	/**
	 * 修改权限
	 * 
	 * @param auth 权限
	 * */
	@RequestMapping("/toEdit")
	public String toEdit(Model model,SysAuthorities auth){
		AuthVo authVo = (AuthVo)authService.selectAuthById(auth);
		model.addAttribute("auth",authVo/*.addResources(resourceService.getResourcesByAuthId(auth.getAuthority_id()))*/);
		/*String resourcesIds = "";
		for(SysResource res : authVo.getResources()){
			resourcesIds += res.getResource_id()+",";
		}
		model.addAttribute("resourcesIds", resourcesIds.substring(0, resourcesIds.length()-1));*/
		return "/system/auth_manage/auth_edit.html";
	}
	
	/**
	 * 修改确认
	 * 
	 * @param auth 权限信息
	 * @param selResourcesIds 所选中的资源ID
	 * @param allResourcesIds 所涉及到的资源ID
	 * */
	@RequestMapping("/edit")
	@ResponseBody
	public ResponseEnvelope edit(SysAuthorities auth,String allResourcesIds,String selResourcesIds){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			logger.info(String.format("========权限%s更新，所涉及资源为%s，选中资源为%s========", auth.getAuthority_name(),allResourcesIds,selResourcesIds));
			//更新权限信息
			authService.updateAuth(auth,allResourcesIds,selResourcesIds);
		}catch(Exception e){
			logger.error("权限修改异常",e);
			re.setMessage("修改失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 获取权限列表
	 * */
	@PostMapping("/getAuthList")
	@ApiOperation(value="获取权限列表")
	@ResponseBody
	public ResponseEnvelope getAuthList() {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			re.setResult(authService.getAllAuth());
		}catch(Exception e){
			re.setMessage(ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
}
