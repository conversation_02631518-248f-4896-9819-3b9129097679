package com.boot.iAdmin.access.controller;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.module.Module;
import com.boot.iAdmin.access.service.api.IMenuManageService;

import io.swagger.annotations.Api;

/**
 * 菜单管理控制器
 * */
@Controller
@RequestMapping("/menuManageController")
@Api(value="菜单管理",tags="菜单管理")
public class MenuManageController {
	
	private final static Log logger = LogFactory.getLog(MenuManageController.class);
	
	@Autowired
	private IMenuManageService menuManageService;
	
	@RequestMapping("/index")
	public String index(){
		return "/system/menu_manage/index.html";
	}
	
	/**
	 * 异步加载菜单树信息
	 * 
	 * @param id 父节点ID
	 * */
	@RequestMapping("/load")
	@ResponseBody
	public Collection<ZTreeNode> load(@RequestParam(value="id",defaultValue="0") String id){
		return menuManageService.loadMenuByParentId(id);
	}
	
	/**
	 * 获得菜单详细信息
	 * 
	 * @param id 资源ID
	 * */
	@RequestMapping("/detail")
	@ResponseBody
	public ResponseEnvelope detail(Long id){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新资源信息
			re.setResult(menuManageService.selectModule(new Module(id)));
		}catch(Exception e){
			logger.error("查询菜单详细信息异常",e);
			re.setMessage("查询菜单详细信息失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 新增菜单
	 * */
	@RequestMapping("/add")
	@ResponseBody
	public ResponseEnvelope add(Module module){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			module.setModule_name("未命名");
			//更新资源信息
			menuManageService.insertModule(module);
		}catch(Exception e){
			logger.error(e.toString());
			re.setMessage("新增失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 删除菜单，包括其下子菜单
	 * */
	@RequestMapping("/remove")
	@ResponseBody
	public ResponseEnvelope remove(Module module){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新资源信息
			menuManageService.deleteModule(module);
		}catch(Exception e){
			logger.error("remove方法运行异常",e);
			re.setMessage("删除失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 更新菜单信息
	 * */
	@RequestMapping("/update")
	@ResponseBody
	public ResponseEnvelope update(Module module){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新资源信息
			if(module.getResource_id() == null) module.setResource_id(0L);
			menuManageService.updateModule(module);
		}catch(Exception e){
			logger.error("更新菜单信息异常",e);
			re.setMessage("修改失败");
			re.setSuccess(false);
		}
		return re;
	}
	
}
