package com.boot.iAdmin.access.controller;

import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.model.role.Role;
import com.boot.iAdmin.access.model.role.RoleVo;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IAuthorityService;
import com.boot.iAdmin.access.service.api.IRoleManageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 角色管理控制器
 * 
 * <AUTHOR>
 * @date 2016年9月11日 10:56:39
 * */
@Controller
@RequestMapping("/roleManageController")
@Api(value="角色管理",tags="角色管理")
public class RoleManageController {
	
	private final static Log logger = LogFactory.getLog(RoleManageController.class);
	
	@Autowired
	private IRoleManageService roleManageService;
	
	@Autowired
	private IAuthorityService authorityService;
	
	
	@RequestMapping("/index")
	public String index(){
		return "/system/role_manage/index.html";
	}
	
	/**
	 * 角色查询方法
	 * <P>兼容分页
	 * @param page 当前页
	 * @param pageNum 每页显示条数
	 * */
	@RequestMapping(path="/load")
	@ResponseBody
	public BootstrapTableModel<RoleVo> load(BootstrapTableModel<RoleVo> model,RoleVo role){
		model.setObj(role);
		return roleManageService.loadRoleByPage(model);
	}
	
	/**
	 * 角色删除方法
	 * <P>逻辑删除
	 * @param roleId 角色主键
	 * */
	@RequestMapping(path="/disable")
	@ResponseBody
	public ResponseEnvelope disable(Long roleId){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			roleManageService.disableRoleById(roleId);
		}catch(Exception e){
			logger.error("角色删除异常",e);
			re.setMessage("删除失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 角色恢复方法
	 * <P>逻辑恢复
	 * @param roleId 角色主键
	 * */
	@RequestMapping(path="/recover")
	@ResponseBody
	public ResponseEnvelope recover(Long roleId){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			roleManageService.recoverRoleById(roleId);
		}catch(Exception e){
			logger.error("角色恢复异常",e);
			re.setMessage("恢复失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 新增角色界面
	 * 
	 * */
	@RequestMapping(path="/toAdd")
	public String toAdd(Model model){
		model.addAttribute("auths", authorityService.getAllAuth());
		return "/system/role_manage/role_add.html";
	}
	
	/**
	 * 用户角色方法
	 * <P>物理删除
	 * @param roleIds 用户主键集合
	 * */
	@RequestMapping(path="/delete")
	@ResponseBody
	public ResponseEnvelope delete(String roleIds){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//判断当前待删除角色是否被用户引用
			List<SysUser> user_lists = roleManageService.judgeDeleteRolesByRolesIds(roleIds);
			if(user_lists != null && user_lists.size() > 0){//如果权限信息被用户引用
				re.setMessage("角色已经被用户信息引用，暂时无法删除，请先删除对应的用户信息");
				re.setSuccess(false);
			}else{
				roleManageService.deleteRoleByIds(roleIds);
			}
			
		}catch(Exception e){
			logger.error("删除角色异常",e);
			re.setMessage("删除失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 新增角色
	 * 
	 * */
	@RequestMapping(path="/add")
	@ResponseBody
	public ResponseEnvelope add(Role role,String[] auths){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			role.setDt_create(new Date());//角色默认创建时间
			roleManageService.insertRole(role,auths);
		}catch(Exception e){
			logger.error("新增角色异常",e);
			re.setMessage("新增失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	@RequestMapping("/validateRoleName")
	public @ResponseBody ResponseEnvelope validateRoleName(Role role,HttpServletRequest request) {
		ResponseEnvelope re = new ResponseEnvelope();
		List<String> results = roleManageService.validateRoleName(role);
		if(results != null && results.size() >0 && results.get(0) != null){//查询数据库存在相同的角色名称
			re.setSuccess(false);
		}else{
			re.setSuccess(true);
		}
		return re;
	}
	
	@RequestMapping("/validateRoleCode")
	public @ResponseBody ResponseEnvelope validateRoleCode(Role role,HttpServletRequest request) {
		ResponseEnvelope re = new ResponseEnvelope();
		List<String> results = roleManageService.validateRoleCode(role);
		if(results != null && results.size() >0 && results.get(0) != null){//查询数据库存在相同的角色编码
			re.setSuccess(false);
		}else{
			re.setSuccess(true);
		}
		return re;
	}
	
	/**
	 * 修改角色
	 * 
	 * @param role 角色
	 * */
	@RequestMapping("/toEdit")
	public String toEdit(Model model,Role role){
		RoleVo roleVo = (RoleVo)roleManageService.getRole(role);
		model.addAttribute("role",roleVo.addAuths(authorityService.getAuthByRoleId(role.getRole_id())));
		model.addAttribute("auths", authorityService.getAllAuth());
		return "/system/role_manage/role_edit.html";
	}
	
	/**
	 * 修改确认
	 * 
	 * @param role 角色信息
	 * @param auths 权限ID集合
	 * */
	@RequestMapping("/edit")
	@ResponseBody
	public ResponseEnvelope edit(Role role,String[] auths){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新角色信息
			roleManageService.updateRole(role,auths);
		}catch(Exception e){
			logger.error("修改角色异常",e);
			re.setMessage("修改失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 获取角色权限
	 * */
	@PostMapping("/getAuthByRole")
	@ApiOperation(value = "获取角色权限")
	@ResponseBody
	public ResponseEnvelope getAuthByRole(Long roleId) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			re.setResult(authorityService.getAuthByRoleId(roleId));
		}catch(Exception e){
			re.setMessage(ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 获取角色列表
	 * */
	@PostMapping("/getRoleList")
	@ApiOperation(value="获取角色列表")
	@ResponseBody
	public ResponseEnvelope getAuthList() {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			re.setResult(roleManageService.selectAll());
		}catch(Exception e){
			re.setMessage(ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
}
