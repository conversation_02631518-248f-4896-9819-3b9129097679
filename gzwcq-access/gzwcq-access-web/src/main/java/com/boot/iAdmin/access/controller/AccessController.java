package com.boot.iAdmin.access.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.WebInvocationPrivilegeEvaluator;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IModuleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@RequestMapping("/access")
@Api(value="鉴权服务",tags="权限控制")
public class AccessController {
	
	private final static Log logger = LogFactory.getLog(AccessController.class);
	
	@Autowired
	private IModuleService moduleService;
	
	@Autowired
	private WebInvocationPrivilegeEvaluator webInvocationPrivilegeEvaluator;
	
	/**
	 * 跳转登录页面
	 * <P>身份未认证，未登录或者session失效
	 * @param errorCode 错误编码 TIMEOUT--session失效 AUTHENTICATION_ERROR--认证失败 LOGOUT--登出
	 * <AUTHOR>
	 * @date 2016年4月25日 19:06:13
	 * */
	@RequestMapping("/login")
	public String login(String errorCode,HttpServletRequest request){
		logger.info(String.format("即将跳转到登录页面[%s]", errorCode));
		return "redirect:/login.html";
	}
	
	/**
	 * 登录成功，跳转到首页
	 * @Title: index
	 * <AUTHOR>
	 * @param model
	 * @param response
	 * @return String
	 */
	@RequestMapping("/index")
	public String index(Model model,HttpServletResponse response,HttpServletRequest request){
		
//		response.getStatus();
		//获取当前用户的信息以及菜单
		SysUser loginUser = (SysUser)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		model.addAttribute("loginUser", loginUser);
//		model.addAttribute("moduleTree", moduleService.loadModuleTreeByUser(loginUser));
		return "/index.html";
	}
	
	
	/**
	 * 获取当前登录用户所有的菜单（平级）
	 * */
	@RequestMapping("/loadModulesByLoginUser")
	@ResponseBody
	@ApiOperation(value="获取当前登录用户所有的菜单（平级）")
	public ResponseEnvelope loadModulesByLoginUser() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			SysUser loginUser = (SysUser)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			re.setResult(moduleService.loadModulesByUser(loginUser));
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 获取当前登录用户菜单树
	 * */
	@RequestMapping("/loadModuleTreeByLoginUser")
	@ResponseBody
	@ApiOperation(value="获取当前登录用户菜单树")
	public ResponseEnvelope loadModuleTreeByLoginUser() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			SysUser loginUser = (SysUser)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			re.setResult(moduleService.loadModuleTreeByUser(loginUser));
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	//判断是否有所传地址的访问权限
	@RequestMapping("/isAllowed")
	@ResponseBody
	@ApiOperation(value="判断是否有所传地址的访问权限")
	public ResponseEnvelope isAllowed(@RequestParam(name="uri",required=true) String uri) {
		ResponseEnvelope re = new ResponseEnvelope();
		re.setSuccess(webInvocationPrivilegeEvaluator.isAllowed(uri, SecurityContextHolder.getContext().getAuthentication()));
		return re;
	}
	

}
