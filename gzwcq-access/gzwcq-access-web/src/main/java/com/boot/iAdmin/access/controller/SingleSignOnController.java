package com.boot.iAdmin.access.controller;

import com.boot.IAdmin.common.code.ErrCode;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.service.api.ISingleSignOnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/15:10:30:55
 **/
@RestController
@Api(value = "/SSO/singleSignOnLogin", description = "单点登录模块")
@RequestMapping("/SSO")
public class SingleSignOnController {
    @Autowired
    private ISingleSignOnService singleSignOnService;

    @ApiResponses({
            @ApiResponse(code = 0, message = "验证成功"),
            @ApiResponse(code = ErrCode.SINGLE_SIGN_ON, message = "非法入侵接口"),
            @ApiResponse(code = ErrCode.SINGLE_SIGN_ON_WARNING, message = "ticket不正确，疑似非法入侵"),
            @ApiResponse(code = ErrCode.SINGLE_ERR, message = "sso系统返回的数据格式已经变革请联系SSO")
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "string", name = "requestTicket", value = "Ticket令牌：放请求头、cookie、请求体都可以", required = true)})
    @ApiOperation(value = "单点登录", notes = "请求头或者请求体或者cookie中包含ticket", httpMethod = "GET")
    @RequestMapping(path = "/singleSignOnLogin", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseEnvelope singleSignOnLogin(HttpServletRequest request, RestTemplate restTemplate, @RequestParam("requestTicket") String requestTicket) {
        return singleSignOnService.singleSignOnLogin(request, restTemplate, requestTicket);
    }

}
