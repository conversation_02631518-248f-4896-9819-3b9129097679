package com.boot.iAdmin.access.controller;

import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.interceptor.MyAuthenticationProvider;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;
import com.boot.iAdmin.access.service.api.IOrganizationService;
import com.boot.iAdmin.access.service.api.IRoleManageService;
import com.boot.iAdmin.access.service.api.ISysUsersService;
import com.boot.iAdmin.access.service.api.IUserManageService;
import com.boot.iAdmin.jwt.api.JWTApi;
import com.boot.iAdmin.jwt.common.Constant;
import com.boot.iAdmin.jwt.interceptor.JWTUsernamePasswordAuthenticationFilter;
import com.boot.iAdmin.jwt.util.MathCaptchaGeneratorUtils;
import com.boot.iAdmin.jwt.util.RSAUtils;
import com.boot.iAdmin.redis.common.RedisUtil;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.userdetails.UserDetailsChecker;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户管理控制器
 * <P>框架级控制器，用于管理系统用户信息
 * @date 2016年8月18日 15:42:29
 * <AUTHOR>
 * */
@Controller
@RequestMapping("/userManageController")
@Api(value="用户管理",tags="用户管理")
public class UserManageController {
	
	private final static Log logger = LogFactory.getLog(UserManageController.class);
	
	@Autowired
	private IUserManageService userManageService;
	
	@Autowired
	private IRoleManageService roleManageService;
	
	@Autowired
	private ISysUsersService sysUsersService;
	
	@Qualifier(value="myAuthenticationProvider")
	@Autowired
	private MyAuthenticationProvider authenticationProvider;
	
	@Autowired
	private IOrganizationService organizationService;
	
	@Qualifier(value="myPreAuthenticationChecks")
	@Autowired
	private UserDetailsChecker userDetailsChecker;
	@Autowired
	private MathCaptchaGeneratorUtils mathCaptchaGeneratorUtils;
	@Autowired
	private MathCaptchaGeneratorUtils generatorUtils;

	@Autowired
	private RedisUtil redisUtil;
	/**
	 * 用户查询方法
	 * <P>兼容分页
	 * @param offset 当前页
	 * @param limit 每页显示条数
	 * */
	@RequestMapping(path="/load",method=RequestMethod.POST)
	@ResponseBody
	public BootstrapTableModel<SysUserVo> load(BootstrapTableModel<SysUser> model,SysUserVo user){
		user.setOrganization_id(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getOrganization_id());
		model.setObj(user);
		return userManageService.loadUserByPage(model);
	}
	
	/**
	 * 用户删除方法
	 * <P>逻辑删除
	 * @param userId 用户主键
	 * */
	@RequestMapping(path="/dff",method=RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope dff(String userId){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			userManageService.deleteUserById(userId);
		}catch(Exception e){
			logger.error("删除用户异常",e);
			re.setMessage("删除失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**validateUserNameAndPassword
	 * 恢复用户方法
	 * @param userId 用户主键
	 * */
	@RequestMapping(path="/recover",method=RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope recover(String userId){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			userManageService.recoverUserById(userId);
		}catch(Exception e){
			logger.error("恢复用户异常",e);
			re.setMessage("删除失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 新增用户
	 * 
	 * */
	@RequestMapping(path="/add",method=RequestMethod.POST)
	@ResponseBody
	@AvoidRepeatableCommit
	public ResponseEnvelope add(SysUser user,String[] roles){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if(user.getOrganization_id() == null) {
				user.setOrganization_id(user.getOrganization_id());
			}
			user.setIsdeleted(Constants.NO_DELETED);
			user.setStatus(1);
			user.setDt_create(new Date());
			user.setFirst_login("Y");
			user.setInitial_pass("Y");
			user.setPassword(authenticationProvider.getPasswordEncoder().encode(user.getPassword()));
			userManageService.accountVerification(user);
			//新增用户
			userManageService.insertUser(user,roles);
		}catch(Exception e){
			logger.error("新增用户异常",e);
			re.setMessage("新增失败:"+e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 用户批量删除方法
	 * <P>逻辑删除
	 * @param userIds 用户主键集合
	 * */
	@RequestMapping(path="/delete",method=RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope delete(String userIds){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			userManageService.deleteUserByIds(userIds);
		}catch(Exception e){
			logger.error("物理删除用户异常",e);
			re.setMessage("删除失败:" + e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 修改确认
	 * 
	 * @param user 用户信息
	 * @param roles 角色ID集合
	 * */
	@RequestMapping(path="/edit",method=RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope edit(SysUser user,String[] roles){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			userManageService.accountVerification(user);
			if(StringUtils.isNotBlank(user.getPassword())) {//非空
				user.setPassword(authenticationProvider.getPasswordEncoder().encode(user.getPassword()));
			}
			//更新用户信息
			userManageService.updateUser(user,roles);
		}catch(Exception e){
			logger.error("修改用户异常",e);
			re.setMessage("修改失败:"+e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 更新基本信息
	 * */
	@RequestMapping(path="/editBaseInfo",method=RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope editBaseInfo(SysUser user){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			userManageService.updateUserInfoIgnoreNull(user);
		}catch(Exception e){
			logger.error("修改用户异常",e);
			re.setMessage("修改失败");
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 验证用户名密码是否正确
	 * @fix: 除了用户名和密码外，还要验证账号状况 2019年5月9日 15:00:12
	 * */
	@RequestMapping(path="/validateUserNameAndPassword",method= {RequestMethod.POST,RequestMethod.GET})
	@ResponseBody
	@SuppressWarnings("all")
	public ResponseEnvelope validateExistence(SysUser user, ServletRequest request, ServletResponse response){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			String captchaCode = request.getParameter(JWTUsernamePasswordAuthenticationFilter.CAPTCHA_CODE);
			String captchaId = null;
			if (request instanceof HttpServletRequest) {
				HttpServletRequest httpRequest = (HttpServletRequest) request;
				captchaId = httpRequest.getHeader(JWTUsernamePasswordAuthenticationFilter.CAPTCHA_ID);
			}
			if (!generatorUtils.verifyCaptcha(captchaCode, captchaId)){
				throw new RuntimeException("验证码错误或验证码过期");
			}
			SysUser userDetail = sysUsersService.getByUsername(user.getUsername());
			String password = user.getPassword();
			String tokenStr = new String(RSAUtils.decryptDataByPrivate(password, JWTApi.PRIVATE_KEY));
			logger.info(String.format("密文为：%s,解码后：%s", password, tokenStr));
			String[] tokenParamArr = tokenStr.split("#");
			if (tokenParamArr.length != 2) {
				//密文格式不正确
				throw new RuntimeException("账号或密码错误");
			}
			String dateTime = tokenParamArr[1];
			// 验证密码正确且未过期，时间和当前时间相差5分钟以内
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
			Date currDate = new Date();
			long timeDif = currDate.getTime() - dateFormat.parse(dateTime).getTime();
			if (!(timeDif > -10 * 60 * 1000 && timeDif < 10 * 60 * 1000)) {
				logger.warn(String.format("加密时间为：%s,服务器当前时间为：%s,时间误差为：%s毫秒",
						dateTime, dateFormat.format(currDate), timeDif));
				throw new RuntimeException("账号或密码错误");
			}
			password = tokenParamArr[0];
			boolean flag = true;
			if(userDetail == null) {//用户不存在
				logger.warn("账号或密码错误");
				re.setState(false);
				re.setMessage("账号或密码错误");
				flag = false;
			}else {
				if (!authenticationProvider.getPasswordEncoder().matches(password,
					userDetail.getPassword())) {
					logger.warn("账号或密码错误");
					re.setState(false);
					re.setMessage("账号或密码错误");
					flag = false;
				}
			}
			if (!flag){
				return re;
			}
			//验证账号状况
			if(userDetail != null) userDetailsChecker.check(userDetail);
			userDetail.setPassword(null);
			SysUser u = new SysUser();
			u.setUser_id(userDetail.getUser_id());
			if ("N".equals(userDetail.getFirst_login())){
				userDetail.setFirstLoginFlag(false);
			}else {
				userDetail.setFirstLoginFlag(true);
				u.setFirst_login("N");
			}
			if ("N".equals(userDetail.getInitial_pass())){
				userDetail.setInitialPassFlag(false);
			}else {
				userDetail.setInitialPassFlag(true);
			}
			userManageService.updateUserInfoIgnoreNull(u);
			if (flag){
				re.setResult(userDetail);
			}
		}catch(Exception e){
			logger.error("验证失败",e);
			re.setMessage(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}

	@RequestMapping(path="/getCodeImage",method= {RequestMethod.POST,RequestMethod.GET})
	@ResponseBody
	@SuppressWarnings("all")
	public void getCodeImage(HttpServletRequest request, HttpServletResponse response){
		try {
			mathCaptchaGeneratorUtils.generateCaptchaImage(request, response);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@RequestMapping(path="/out",method= {RequestMethod.POST,RequestMethod.GET})
	@ResponseBody
	@SuppressWarnings("all")
	public ResponseEnvelope userOut(HttpServletRequest request, HttpServletResponse response){
		ResponseEnvelope responseEnvelope = new ResponseEnvelope();
		try {
			SysUser user = ((SysUser)SpringSecurityUserTools.instance().getUser(null));
			redisUtil.remove(Constant.REDIS_TOKE_KEY_PREFIX+user.getUser_id());
		} catch (Exception e) {
			responseEnvelope.setSuccess(false);
		}
		return responseEnvelope;
	}
	/**
	 * 重置密码
	 * */
	@RequestMapping(path="/resetPassword",method=RequestMethod.POST)
	@ResponseBody
	@AvoidRepeatableCommit
	public ResponseEnvelope resetPassword(@RequestParam(required=true) String new_password,@RequestParam(required=true) String password){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			SysUser userDetail = sysUsersService.getByUsername(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUsername());
			if(!authenticationProvider.getPasswordEncoder().matches(password,userDetail.getPassword())) {
				throw new Exception("原密码不正确");
			}
			if(authenticationProvider.getPasswordEncoder().matches(new_password,userDetail.getPassword())) {
				throw new Exception("新密码不能和原密码一致");
			}
			SysUser user = new SysUser();
			user.setUser_id(userDetail.getUser_id());
			user.setPassword(authenticationProvider.getPasswordEncoder().encode(new_password));
			if (!"N".equals(userDetail.getInitial_pass())){
				user.setInitial_pass("N");
			}
			userManageService.updateUserInfoIgnoreNull(user);
		}catch(Exception e){
			logger.error(ExceptionUtils.getStackTrace(e));
			re.setMessage(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 参数唯一性验证
	 * */
	@RequestMapping(path="/validateUniqueParam",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope validateUniqueParam(SysUser user) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(userManageService.validateUniqueParam(user));
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据用户主键获取用户信息
	 * */
	@RequestMapping(path="/getUserById",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope getUserById(SysUser user) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(userManageService.getUserInfoByUserId(user.getUser_id()));
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据用户信息加载权限信息
	 * */
	@RequestMapping(path="/loadOldAuth",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope loadOldAuth(SysUser user, Long company) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			Map<String,Object> dataMap = new HashMap<String,Object>();
			dataMap.put("roles",roleManageService.selectRolesByUserId(user.getUser_id()));
			re.setResult(dataMap);
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 用户权限设置
	 * <P> 用户
	 * */
	@RequestMapping(path="/authSetting",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope authSetting(SysUserVo user,String[] roleArr,Long company_id) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			userManageService.authSetting(user,roleArr,company_id);
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 获取当前登陆用户信息
	 */
	@RequestMapping(path="/userInfo",method= RequestMethod.POST)
	@ResponseBody
	public ResponseEnvelope getUserInfo() {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			SysUser user = (SysUser)SpringSecurityUserTools.instance().getUser(null);
			user.setOrgName(organizationService.getOrgNameById(user.getOrganization_id()));
			user.setPassword(null);
			re.setResult(user);
		}catch(Exception e) {
			logger.error("获取登陆用户信息出错",e);
			re.setMessage("获取登陆用户信息出错");
			re.setSuccess(false);
		}
		return re;
	}

	@PostMapping(path="/currentUserOrgTree")
	@ResponseBody
	public ResponseEnvelope getCurrentUserOrgTree(@RequestParam(required = false) String id){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			SysUser user = (SysUser)SpringSecurityUserTools.instance().getUser(null);
			re.setResult(userManageService.getCurrentUserOrgTree(user,id));
		}catch(Exception e) {
			logger.error("获取登录用户下级组织树失败",e);
			re.setMessage("获取登录用户下级组织树失败");
			re.setSuccess(false);
		}
		return re;
	}
}
