package com.boot.iAdmin.access.controller;

import com.alibaba.fastjson.JSON;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode;
import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNodeExport;
import com.boot.IAdmin.dict.service.api.IDictionaryService;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.organization.SysOrganizationParm;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IOrganizationService;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/**
 * 组织管理控制器
 * */
@Controller
@RequestMapping("/organizationManageController")
@Api(value="组织管理",tags="组织管理")
public class OrganizationManageController {

	private final static Log logger = LogFactory.getLog(OrganizationManageController.class);

	@Autowired
	private IOrganizationService organizationService;

	@Autowired
	private IDictionaryService dictionaryService;

	@RequestMapping("/index")
	public String index(Model model){
		model.addAttribute("orgTypeLists",dictionaryService.getByTypeCodeCommon("OrgType"));//组织类型下拉列表
		return "/system/organization_manage/index.html";
	}

	/**
	 * 异步加载组织树信息
	 *
	 * @param id 父节点ID
	 * */
	@PostMapping("/load")
	@ResponseBody
	@ApiOperation("异步加载组织树信息")
	public Collection<ZTreeNode> load(@RequestParam(value="id",defaultValue="0") String id){
		return organizationService.loadOrganizationByParentId(id);
	}

	/**
	 * 异步加载组织树信息(包装对象)
	 * */
	@PostMapping("/loadAll")
	@ResponseBody
	@ApiOperation("异步加载组织树信息(包装对象)")
	public ResponseEnvelope loadAll(@RequestParam(value="id",defaultValue="0") String id){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			re.setResult(organizationService.loadOrganizationByParentId(id));
		}catch(Exception e){
			logger.error("查询组织信息异常",e);
			re.setMessage("查询组织信息失败");
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 异步加载组织树信息
	 *
	 * @param id 父节点ID
	 * */
	@PostMapping("/loadTwo")
	@ResponseBody
	public Collection<ZTreeNode> loadTwo(@RequestParam(value="id",defaultValue="0") String id){
		return organizationService.loadOrganizationByParentIdTwo(id);
	}

	/**
	 * 异步加载组织树信息(包装对象)
	 * */
	@PostMapping("/loadThree")
	@ResponseBody
	public ResponseEnvelope load3(@RequestParam(value="id",required = false) String id){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			re.setResult(organizationService.loadLoginUserOrganizations(id));
		}catch(Exception e){
			logger.error("查询组织信息异常",e);
			re.setMessage("查询组织信息失败");
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 异步加载组织树信息
	 *
	 * @param id 父节点ID
	 * */
	@PostMapping("/loadTree")
	@ResponseBody
	@ApiOperation("异步加载组织树信息")
	public Collection<ZTreeNode> loadTree(@RequestParam(value="id",defaultValue="0") String id,String userId){
		return organizationService.loadOrganizationTree(id, userId);
	}

	/**
	 * 获得组织详细信息
	 *
	 * @param id 组织ID
	 * */
	@PostMapping("/detail")
	@ResponseBody
	public ResponseEnvelope detail(String id){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新组织信息
			re.setResult(organizationService.selectOrganization(new SysOrganization(id)));
		}catch(Exception e){
			logger.error("查询组织详细信息异常",e);
			re.setMessage("查询组织详细信息失败");
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 更新组织信息
	 * */
	@PostMapping("/update")
	@ResponseBody
	public ResponseEnvelope update(SysOrganization organization){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新组织信息
			organizationService.updateOrganization(organization);
		}catch(Exception e){
			logger.error("更新组织信息异常",e);
			re.setMessage("修改失败");
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 新增组织
	 * */
	@PostMapping("/add")
	@ResponseBody
	public ResponseEnvelope add(SysOrganization organization){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			organization.setOrganization_name("未命名"+new Random().nextInt(10));//生成的名字随机数(未命名XXX)
			Map<String, Object> map_max_par_sub_code = organizationService.selectMaxOrgCodeByParId(organization.getParent_id());
			if(map_max_par_sub_code == null) {
				SysOrganization org_par = organizationService.selectOrganization(new SysOrganization(organization.getParent_id()));
				organization.setOrganization_code(org_par.getOrganization_code()+"01");
			}else{
				String max_par_sub_code = (String)map_max_par_sub_code.get("ORGANIZATION_CODE");
				if(Strings.isNullOrEmpty(max_par_sub_code)) {
					SysOrganization org_par = organizationService.selectOrganization(new SysOrganization(organization.getParent_id()));
					organization.setOrganization_code(org_par.getOrganization_code()+"01");
				}else {
					String max_par_sub_pre = max_par_sub_code.substring(0, max_par_sub_code.length()-2);
					String max_par_sub_suf = max_par_sub_code.substring(max_par_sub_code.length()-2);
					organization.setOrganization_code(max_par_sub_pre + String.format("%02d",Integer.valueOf(Integer.valueOf(max_par_sub_suf)+1)));
				}
			}
			//更新组织信息
			organizationService.insertOrganization(organization);
		}catch(Exception e){
			logger.error("新增组织异常",e);
			re.setMessage("新增失败");
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 删除组织，包括其下子组织
	 * */
	@PostMapping("/remove")
	@ResponseBody
	public ResponseEnvelope remove(SysOrganization organization){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//判断该组织及其子组织是否被(用户)引用
			if(organizationService.getTotalOrganization(organization) > 0){
				throw new Exception("该组织及其子组织被用户引用，无法删除");
			}
			//删除组织及其子组织
			organizationService.deleteOrganization(organization);
		}catch(Exception e){
			logger.error("删除组织异常",e);
			re.setMessage(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 参数唯一性验证
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	public ResponseEnvelope validateUniqueParam(SysOrganization organization) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(organizationService.validateUniqueParam(organization));
		} catch (Exception e) {
			logger.error(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}

	@PostMapping("/getOrganization")
	@ResponseBody
	public ResponseEnvelope getOrganization(HttpSession session){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			String org_id = ((SysUser)SpringSecurityUserTools.instance().getUser(session)).getOrganization_id();
			SysOrganization org = organizationService.selectOrganization(new SysOrganization(org_id));
			re.setResult(org);
		}catch(Exception e){
			logger.error("获取组织异常",e);
			re.setMessage(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}
	/**
	 * 通过组织编码获取组织名称
	 * @param org_id
	 * @return
	 */
	@PostMapping("/getOrgNameById")
	@ResponseBody
	public ResponseEnvelope getOrgNameById(String org_id) {
		ResponseEnvelope re = new ResponseEnvelope();
		String orgName=organizationService.getOrgNameById(org_id);
		re.setResult(orgName);
		return re;
	}

	/**
	 * 	加载组织tree下拉选项
	 * */
	@PostMapping("/getPageDatas")
	@ResponseBody
	public ResponseEnvelope getPageDatas(SysOrganization organization) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			//获取组织树
			VueAntdTreeSelectNode[] nodes = {organizationService.getOrganizationTressOption(organization)};
			re.setResult(nodes);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 获取当前登录用户的组织列表
	 * */
	@PostMapping("/getOrgsByLoginUser")
	@ResponseBody
	@ApiOperation("获取当前登录用户的组织列表")
	public ResponseEnvelope getOrgsByLoginUser(HttpSession session,SysOrganization org){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			String ordId = ((SysUser)SpringSecurityUserTools.instance().getUser(session)).getCurrOrgId();
			Map<String,Object> queryMap = new HashMap<String,Object>();
			queryMap.put("ordIds", ordId);
			List<SysOrganization> lists = organizationService.getOrgsByIds(queryMap);
			re.setResult(lists);
		}catch(Exception e){
			logger.error("获取组织异常",e);
			re.setMessage(e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}

	@PostMapping("/getChildOrgTree")
	@ResponseBody
	@ApiOperation("获取本级及以下组织")
	public ResponseEnvelope getChildOrgTree(String rootId) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			//获取组织树
			VueAntdTreeSelectNode[] nodes = {organizationService.getChildOrgTree(rootId)};
			re.setResult(nodes);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	
	/**
	 * 根据当前用户组织获取本级及以下组织
	 * */
	@PostMapping("/getUserChildOrgTree")
	@ResponseBody
	@ApiOperation("根据当前用户组织获取本级及以下组织")
	public ResponseEnvelope getUserChildOrgTree() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			//获取组织树
			VueAntdTreeSelectNode[] nodes = {organizationService.getChildOrgTreeByUserId()};
			re.setResult(nodes);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}
	/**
	 * 导出组织获取数据
	 * */
	@PostMapping("/getExportOrgTree")
	@ResponseBody
	@ApiOperation("导出组织获取数据")
	public ResponseEnvelope getExportOrgTree() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			//获取组织树
			VueAntdTreeSelectNodeExport[] nodes = {organizationService.getExportOrgTree()};
			re.setResult(nodes);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	@RequestMapping("/getParentOrgTree")
	@ResponseBody
	public ResponseEnvelope getParentOrgTree(String orgId) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			//获取组织树
			VueAntdTreeSelectNode[] nodes = {organizationService.getParentOrgTree(orgId)};
			re.setResult(nodes);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	@RequestMapping("/getOrgList")
	@ResponseBody
	public ResponseEnvelope getOrgList(SysOrganization org) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getOrgList(org));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 组织上移下移
	 * @param orgId
	 * @param type
	 * @return
	 */
	@RequestMapping("/moveUpMoveDown")
	@ResponseBody
	public ResponseEnvelope moveUpMoveDown(String orgId,String type) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			organizationService.moveUpMoveDown(orgId, type);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setMessage("操作失败：" + e.getMessage());
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 获取根节点列表(超管直接看所有组织，其他人看本组织加可见组织)
	 * @param
	 * @return
	 */
	@RequestMapping("/getRootOrgList")
	@ResponseBody
	public ResponseEnvelope getRootOrgList() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getRootOrgList());
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 模糊搜索当前登录人可见组织
	 */
	@PostMapping("/getOrgsByName")
	@ApiOperation("模糊搜索组织")
	@ResponseBody
	public ResponseEnvelope getOrgsByName(SysOrganization org) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getOrgsByName(org));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 模糊搜索当前登录人子级组织
	 */
	@PostMapping("/getLoginUserChildrenOrgs")
	@ResponseBody
	public ResponseEnvelope getLoginUserChildrenOrgs(SysOrganization org) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getLoginUserChildrenOrgs(org));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}


	/**
	 * 产权树导出
	 */
	@PostMapping("/export")
	@ApiOperation("产权树导出为excel")
	public ResponseEntity<byte[]> treeExport(@RequestBody SysOrganizationParm parm, HttpServletResponse resp) throws IOException {
		try {
			return organizationService.treeExport(parm);
		} catch (Exception e) {
			logger.error("EXPORT ERROR:", e);
			resp.reset();
			resp.setContentType("application/json");
			resp.setCharacterEncoding("utf-8");
			ResponseEnvelope re = new ResponseEnvelope();
			re.setMessage("产权树导出失败："+ ExceptionUtils.getMessage(e));
			re.setSuccess(false);
			resp.getWriter().println(JSON.toJSONString(re));
		}
		return null;
	}

	/**
	 * 异步加载组织树(本级及以下+审批托管)信息(包装对象)
	 * */
	@PostMapping("/loadChildrenAndAudits")
	@ResponseBody
	public ResponseEnvelope loadChildrenAndAudits(@RequestParam(value="id",required = false) String id){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			re.setResult(organizationService.loadChildrenAndAudits(id));
		}catch(Exception e){
			logger.error("查询组织信息异常",e);
			re.setMessage("查询组织信息失败");
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 模糊搜索当前登录人子级组织+审批托管组织(变动/注销第一步)
	 */
	@PostMapping("/getLoginUserChildrenAndAudits")
	@ResponseBody
	public ResponseEnvelope getLoginUserChildrenAndAudits(SysOrganization org) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getLoginUserChildrenAndAudits(org));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 获取当前登录人可见的树
	 */
	@PostMapping("/getLoginUserVisibleTree")
	@ResponseBody
	@ApiOperation("获取当前登录人可见的树")
	public ResponseEnvelope getLoginUserVisibleTree() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getLoginUserVisibleTree());
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}
	/**
	 * 获取当前登录人可见的树
	 */
	@PostMapping("/getLoginUserVisiblePossessDeleteStatusTree")
	@ResponseBody
	@ApiOperation("获取当前登录人可见的树(包括注销的企业)")
	public ResponseEnvelope getLoginUserVisiblePossessDeleteStatusTree() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getLoginUserVisiblePossessDeleteStatusTree());
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 模糊搜索当前登录可见组织
	 */
	@PostMapping("/getLoginUserVisibleOrgs")
	@ResponseBody
	public ResponseEnvelope getLoginUserVisibleOrgs(SysOrganization org) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(organizationService.getLoginUserVisibleOrgs(org));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
		}
		return re;
	}
}
