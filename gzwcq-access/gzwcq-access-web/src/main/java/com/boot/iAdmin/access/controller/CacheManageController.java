package com.boot.iAdmin.access.controller;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.interceptor.AbstractSecurityMetadataSource;
import com.boot.iAdmin.cache.core.CacheBeanFactory;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 缓存控制器
 * <P>刷新系统缓存
 * */
@Controller
@RequestMapping("/cacheManageController")
@Api(value="系统缓存",tags="系统缓存")
public class CacheManageController {
	
	Log logger = LogFactory.getLog(CacheManageController.class);
	
	@Qualifier(value="securityMetadataSource")
	@Autowired
	private AbstractSecurityMetadataSource securityMetadataSource;
	
	@Autowired(required=false)
	private CacheBeanFactory cacheBeanFactory;//缓存工厂
	
	/**
	 * 刷新spring-security安全数据源
	 * */
	@RequestMapping("/refreashMetadataSource")
	@ResponseBody
	@ApiOperation("刷新系统缓存")
	public ResponseEnvelope refreashMetadataSource(){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//刷新
			securityMetadataSource.refreshResuorceMap();
			re.setMessage("刷新成功");
		}catch(Exception e){
			logger.error(e);
			re.setSuccess(false);
			re.setMessage(e.getMessage());
		}
		return re;
	}
	
	/**
	 * 刷新字典缓存
	 * */
	@RequestMapping("/refreashDictCache")
	@ResponseBody
	@ApiOperation("刷新字典缓存")
	public ResponseEnvelope refreashDictCache(){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//刷新
			if(cacheBeanFactory != null) {
				cacheBeanFactory.getDictCacheStrategy().refresh();
				re.setMessage("刷新成功");
			}else {
				throw new RuntimeException("缓存工厂不存在！请确认当前系统是否启动缓存组件");
			}
		}catch(Exception e){
			logger.error(e);
			re.setSuccess(false);
			re.setMessage(e.getMessage());
		}
		return re;
	}
}
