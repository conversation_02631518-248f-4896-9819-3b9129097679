package com.boot.iAdmin.access.model.module;

/**
 * 菜单
 * 
 * <AUTHOR>
 * @date 2016年5月29日 16:47:31
 * */
public class Module {
	
	// 菜单ID
	protected Long module_id;
	// 菜单名称
	protected String module_name;
	// 菜单描述
	protected String module_desc;
	// 菜单类型
	protected String module_type;
	// 菜单访问地址
	protected String module_url;
	// 上级菜单ID
	protected Long parent;
	// 菜单层级
	protected Integer i_level;
	// 是否是叶子
	protected Integer leaf;
	// 是否可用
	protected Integer enable;
	// 优先级
	protected Integer priority;
	// 资源ID
	protected Long resource_id;
	
	public Module() {
	}

	public Module(Long module_id) {
		super();
		this.module_id = module_id;
	}

	public Long getModule_id() {
		return module_id;
	}

	public void setModule_id(Long module_id) {
		this.module_id = module_id;
	}

	public String getModule_name() {
		return module_name;
	}

	public void setModule_name(String module_name) {
		this.module_name = module_name;
	}

	public String getModule_desc() {
		return module_desc;
	}

	public void setModule_desc(String module_desc) {
		this.module_desc = module_desc;
	}

	public String getModule_type() {
		return module_type;
	}

	public void setModule_type(String module_type) {
		this.module_type = module_type;
	}

	public String getModule_url() {
		return module_url;
	}

	public void setModule_url(String module_url) {
		this.module_url = module_url;
	}

	public Long getParent() {
		return parent;
	}

	public void setParent(Long parent) {
		this.parent = parent;
	}

	public Integer getI_level() {
		return i_level;
	}

	public void setI_level(Integer i_level) {
		this.i_level = i_level;
	}

	public Integer getLeaf() {
		return leaf;
	}

	public void setLeaf(Integer leaf) {
		this.leaf = leaf;
	}

	public Integer getEnable() {
		return enable;
	}

	public void setEnable(Integer enable) {
		this.enable = enable;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public Long getResource_id() {
		return resource_id;
	}

	public void setResource_id(Long resource_id) {
		this.resource_id = resource_id;
	}

}
