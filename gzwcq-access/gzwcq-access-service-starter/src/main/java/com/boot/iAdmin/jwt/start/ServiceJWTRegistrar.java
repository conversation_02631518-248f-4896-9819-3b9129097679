package com.boot.iAdmin.jwt.start;

import javax.servlet.Filter;

import com.boot.iAdmin.redis.common.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

import com.boot.iAdmin.jwt.interceptor.JWTServiceAccessFilter;

/**
 * JWT配置
 * <P>后端服务权限
 * */
public class ServiceJWTRegistrar {
	
	
	/**
	 * 用于过滤移动端过来的请求，判断是否有访问权限
	 * <P>/jwt开头的URL不会进入后端权限过滤器security而是通过本过滤器过滤删选
	 * */
	@Bean
	public FilterRegistrationBean<Filter> filterRegistration(@Autowired RedisUtil redisUtil) {
		FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<Filter>();
		registration.setFilter(new JWTServiceAccessFilter(redisUtil));
		registration.addUrlPatterns("/jwt/*");
		registration.addInitParameter("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/gzwcq/jwt/openApi/*");//忽略的地址
		registration.setOrder(Integer.MIN_VALUE+51);
		return registration;
	}
	
}
