package com.boot.iAdmin.access.service.impl;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.boot.IAdmin.common.code.ErrCode;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.HttpClientUtils;
import com.boot.iAdmin.access.model.sso.SingleSignOnVO;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;
import com.boot.iAdmin.access.service.api.ISingleSignOnService;
import com.boot.iAdmin.access.service.api.ISysUsersService;
import com.boot.iAdmin.access.service.api.IUserManageService;
import com.boot.iAdmin.jwt.common.Constant;
import com.boot.iAdmin.jwt.util.JWTUtils;
import com.boot.iAdmin.redis.common.RedisUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsChecker;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/17:16:20:26
 **/
@Service
public class SingleSignOnServiceImpl implements ISingleSignOnService {
    final String HTTP = "http";
    final String URL = "10.10.136.13:8088/ssoserver/caslogin/serviceValidate";
    final String URL_SERVICE = "http://10.10.49.102:9528/SSOLogin";
    final String TICKET = "ticket";
    private final static Log logger = LogFactory.getLog(SingleSignOnServiceImpl.class);
    @Autowired
    private ISysUsersService sysUsersService;
    @Autowired
    private IUserManageService userManageService;
    @Autowired
    private UserDetailsChecker userDetailsChecker;
    @Autowired
    private UserDetailsService userDetailsService;
    @Autowired
    private AccountStatusUserDetailsChecker preAuthenticationChecks;
    @Autowired
    private RedisUtil redisUtil;
    @Override
    public ResponseEnvelope singleSignOnLogin(HttpServletRequest request, RestTemplate restTemplate, String requestTicket) {
        String username;
        String Ticket;
        ResponseEnvelope responseEnvelope = new ResponseEnvelope();
        Map<String, Object> responseMap = new HashMap<>();
        Map<String, String> requestDTO = new HashMap<>();
        try {
            //获取Ticket
            Ticket = this.getTicket(requestTicket, request);
            //ticket不存在
            if (!Objects.nonNull(Ticket)) {
                responseEnvelope.setResultCode(ErrCode.SINGLE_SIGN_ON);
                responseEnvelope.setMessage("非法入侵接口！！");
                return responseEnvelope;
            }
            //请求数据
            requestDTO.put(TICKET, Ticket);
            requestDTO.put("service", URL_SERVICE);
            //拼接url
            String getURL = this.generateRequestParameters(HTTP, URL, requestDTO);
            logger.info("开始请求sso\t" + getURL);
            //开始请求
            String sendGet = sendGet(restTemplate, HTTP, URL, requestDTO);
            logger.info("调用SSO请求返回" + sendGet);
            Object user = null;
            try {
                JSONObject jsonObject = HttpClientUtils.xmltoJson(sendGet);
                logger.info("解析为");
                logger.info(jsonObject);
                //获取第一层数据
                JSONObject serviceResponse = (JSONObject) jsonObject.get("serviceResponse");
                //获取第二层数据
                JSONObject attributes = (JSONObject) serviceResponse.get("authenticationSuccess");
                //获取账号
                user = attributes.get("user");
            } catch (Exception e) {
//                todo 后续SSO 问题解决恢复原样
//                user = "louqiwei";
                    logger.warn("sso系统返回的数据格式已经变革请联系SSO", e);
                    responseEnvelope.setResultCode(ErrCode.SINGLE_ERR);
                    responseEnvelope.setResult(e);
                    return responseEnvelope;
            }
            //账号为空
            if (!Objects.nonNull(user)) {
//                /**
//                 * todo 后续SSO 问题解决恢复原样
//                 */
//                user = "louqiwei";
                responseEnvelope.setResultCode(ErrCode.SINGLE_SIGN_ON_WARNING);
                responseEnvelope.setMessage("非法入侵接口！！");
                logger.error("此ticket为假Ticket，Ticket为\t" + Ticket);
                return responseEnvelope;
            }
            username = user.toString();
            //验证账号并获取用户数据
            SingleSignOnVO singleSignOnVO = this.isUser(username);
            if (!Objects.nonNull(singleSignOnVO)) {
                logger.warn("单点登录 用户不存在");
                logger.warn(" 目标已成功入侵Ticket：考虑可能是因为我们这边数据库没这个Ticket账号");
                logger.error("单点登录 目标已成功入侵Ticket  ticket:\t" + Ticket);
                responseEnvelope.setResultCode(ErrCode.SINGLE_SIGN_ON);
                responseEnvelope.setMessage("非法入侵接口！！");
                return responseEnvelope;
            }
            responseMap.put("singleSignOnVO", singleSignOnVO);
            responseMap.put("token", getToken(username));
            responseEnvelope.setResult(responseMap);
            responseEnvelope.setResultCode(2000);
            return responseEnvelope;
        } catch (Exception e) {
            logger.warn("单点登录 失败", e);
            responseEnvelope.setResultCode(ErrCode.SINGLE_SIGN_ON);
            responseEnvelope.setMessage("非法入侵接口！！");
            responseEnvelope.setResult(e);
            return responseEnvelope;
        }
    }

    /**
     * 获取Ticket
     *
     * @param requestTicket 请求体中的值
     * @param request       HttpServletRequest
     * @return
     */
    private String getTicket(String requestTicket, HttpServletRequest request) {
        String Ticket;
        Map<String, Object> heads;
        Map<String, String> cookie = new HashMap<>();
        //请求体中没有Ticket
//        if (!StringUtils.isNotBlank(requestTicket)) {
//            Cookie[] cookies = request.getCookies();
        //将Cookie转化为map；
//            Arrays.stream(cookies).forEach(iter -> cookie.put(iter.getName(), iter.getValue()));
        //查询cookie中有没有TICKET令牌
//            if (!cookie.containsKey(TICKET)) {
//                //查询请求头中有没有数据
//                //查询请求头中的数据
//                heads = this.getHeads(request);
//                //请求头中也没有TICKET
//                if (!heads.containsKey(TICKET)) return null;
//                //在请求头中取Ticket
//                Ticket = heads.get(TICKET).toString();
//            }
//            //在cookie中取
//            Ticket = cookie.get(TICKET).toString();
//        } else {
        //取前端传入的Ticket（请求体）
        Ticket = requestTicket;
//        }
        return Ticket;
    }

    /**
     * 查询请求头的数据
     *
     * @param request
     * @return
     */
    private Map<String, Object> getHeads(HttpServletRequest request) {
        Map<String, Object> stringObjectHashMap = new HashMap<>();
        Enumeration<String> headers = request.getHeaderNames();
        System.out.println("请求头信息");
        while (headers.hasMoreElements()) {
            String headName = (String) headers.nextElement();
            String headValue = request.getHeader(headName);
            System.out.println(headName + "：" + headValue);
            stringObjectHashMap.put(headName, headValue);
        }
        return stringObjectHashMap;
    }

    /**
     * 生成get参数请求url
     * 示例：https://0.0.0.0:80/api?u=u&o=o
     * 示例：https://0.0.0.0:80/api
     *
     * @param protocol 请求协议 示例: http 或者 https
     * @param uri      请求的uri 示例: 0.0.0.0:80
     * @param params   请求参数
     * @return
     */
    public String generateRequestParameters(String protocol, String uri, Map<String, String> params) {
        StringBuilder sb = new StringBuilder(protocol).append("://").append(uri);
        if (Objects.nonNull(params)) {
            sb.append("?");
            for (Map.Entry map : params.entrySet()) {
                sb.append(map.getKey())
                        .append("=")
                        .append(map.getValue())
                        .append("&");
            }
            uri = sb.substring(0, sb.length() - 1);
            return uri;
        }
        return sb.toString();
    }

    /**
     * get请求、请求参数为?拼接形式的
     * <p>
     * 最终请求的URI如下：
     * <p>
     *
     * @return
     */
    public String sendGet(RestTemplate restTemplate, String HTTP, String URL, Map<String, String> requestMap) {
        //配置xml
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_XML, MediaType.APPLICATION_PROBLEM_XML));
        restTemplate.getMessageConverters().add(mappingJackson2HttpMessageConverter);
        /**
         * 发送get请求
         */
        ResponseEntity<String> responseEntity = restTemplate.getForEntity
                (
                        generateRequestParameters(HTTP, URL, requestMap),
                        String.class
                );
        return responseEntity.getBody();
    }

    /***
     * 查询是账号是否存在  null时为不存在
     * 验证账号并获取用户数据
     * @param username 账号
     * @return
     */
    public SingleSignOnVO isUser(String username) {
        //查询数据库的账号数据
        SysUserVo userDetail = sysUsersService.getByUsername(username);
        //查询数据库是否有这个账号。
        if (!Objects.nonNull(userDetail) || !StringUtils.isNotBlank(userDetail.getUsername())) {
            logger.warn("账号" + username + "不存在");
            return null;
        }
        //更新账号的数据比如是否首次登录
        updateUser(userDetail);
        //将数据转换为VO
        SingleSignOnVO singleSignOnVO = JSON.parseObject(JSON.toJSON(userDetail).toString(), SingleSignOnVO.class);
        return singleSignOnVO;
    }

    /***
     * 更新用户状态数据，如是否第一次登录
     * @param userDetail
     */
    public void updateUser(SysUserVo userDetail) {
        if (userDetail != null) userDetailsChecker.check(userDetail);
        userDetail.setPassword(null);
        SysUser u = new SysUser();
        u.setUser_id(userDetail.getUser_id());
        //查看是否首次登录
        if ("N".equals(userDetail.getFirst_login())) {
            userDetail.setFirstLoginFlag(false);
        } else {
//          是否首次登录
            userDetail.setFirstLoginFlag(true);
            u.setFirst_login("N");
        }
        //是否属于初始密码
        if ("N".equals(userDetail.getInitial_pass())) {
            //不属于初始密码
            userDetail.setInitialPassFlag(false);
        } else {
            //属于初始密码
            userDetail.setInitialPassFlag(true);
        }
        //更新用户数据
        userManageService.updateUserInfoIgnoreNull(u);
    }

    /**
     * 根据用户账号获取token
     *
     * @param userName
     * @return
     */
    public String getToken(String userName) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(userName)) throw new Exception("用户名不能为空！");
            //根据用户名获取用户信息
            UserDetails ud = userDetailsService.loadUserByUsername(userName);
            //验证账号是否有效
            preAuthenticationChecks.check(ud);
//            if (!authenticationProvider.getPasswordEncoder().matches("gzw135246", ud.getPassword()))
//                throw new RuntimeException("密码不正确");
            //生成token
            String token = JWTUtils.buildJwtToken(Constant.TOKEN_CLAIM, ud);
            SysUserVo userDetail = sysUsersService.getByUsername(userName);
            redisUtil.set(Constant.REDIS_TOKE_KEY_PREFIX+((SysUser) userDetail).getUser_id(), SecureUtil.md5(token), (long) (4*(60*60)));
//            String token = JWTUtils.buildJwtToken(Constant.TOKEN_CLAIM, new UsernamePasswordAuthenticationToken(ud, null, ud.getAuthorities()));
            //将token编码放在response header中返回给客户端
            return token;
        } catch (Exception e) {
            logger.error("单点登录JWT身份认证失败：", e);
        }
        return null;
    }
}
