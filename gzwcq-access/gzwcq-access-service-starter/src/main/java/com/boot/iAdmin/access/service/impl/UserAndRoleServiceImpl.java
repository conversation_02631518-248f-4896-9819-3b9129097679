package com.boot.iAdmin.access.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.boot.iAdmin.access.mapper.UserAndRoleMapper;
import com.boot.iAdmin.access.model.roleAndUser.RoleAndUser;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IUserAndRoleService;

@Service("userAndRoleService")
public class UserAndRoleServiceImpl implements IUserAndRoleService{
	
	@Autowired
	private UserAndRoleMapper userAndRoleMapper;
	
	/**
	 * 更新用户角色关联关系
	 * <P>全删全增
	 * @param user 用户
	 * @param roles 角色ID集合
	 * */
	public void updateUserAndRolesWithOneUser(SysUser user, String[] roles) {
		RoleAndUser roleAndUser = new RoleAndUser();
		roleAndUser.setUser_id(user.getUser_id());
		userAndRoleMapper.deleteUserAndRoleByUser(roleAndUser);
		if(roles == null) return;
		for(String role_id : roles){
			roleAndUser.setId(null);
			roleAndUser.setRole_id(Long.parseLong(role_id));
			userAndRoleMapper.insertUserAndRole(roleAndUser);
		}
	}

	/**
	 * 根据用户ID删除用户角色关联关系
	 * @param userIds 用户ID
	 * */
	
	public void deleteUserAndRoleByUserIds(String userIds) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("userIds",userIds.split(","));
		userAndRoleMapper.deleteUserAndRoleByUserIds(map);
	}
	
	/**
	 * 根据角色ID删除用户角色关联关系
	 * @param roleIds 角色ID
	 * */
	
	public void deleteUserAndRoleByRoleIds(String roleIds) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("roleIds",roleIds.split(","));
		userAndRoleMapper.deleteUserAndRoleByRoleIds(map);
	}
	
	@Override
	public void insertUserAndRole(RoleAndUser roleAndUser) {
		userAndRoleMapper.insertUserAndRole(roleAndUser);
	}

	/**
	 * 更新用户公司角色关联关系
	 * <P>全删全增
	 * @param user 用户
	 * @param roles 角色ID集合
	 * */
	@Deprecated
	public void updateUserAndRolesWithOneUserAndCompany(SysUser user, String[] roles,Long company_id) {
		RoleAndUser roleAndUser = new RoleAndUser();
		roleAndUser.setUser_id(user.getUser_id());
		roleAndUser.setCompany_id(company_id);
		userAndRoleMapper.deleteUserAndRoleByUserAndCompany(roleAndUser);
		if(roles != null) {
			for(String role_id : roles){
				roleAndUser.setId(null);
				roleAndUser.setRole_id(Long.parseLong(role_id));
				userAndRoleMapper.insertUserAndRole(roleAndUser);
			}
		}
	}
	

}
