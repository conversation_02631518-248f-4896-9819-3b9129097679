package com.boot.iAdmin.appInfo.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： crm_app_info <br/>
 *         描述：第三方应用表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppInfo implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="id主键")
	protected String id;// id主键
  	@ApiParam(value="第三方应用名称")
	protected String appName;// 第三方应用名称
  	@ApiParam(value="第三方应用编码")
	protected String appId;// 第三方应用编码
  	@ApiParam(value="鉴权token")
	protected String token;// 鉴权token
  	@ApiParam(value="应用描述")
	protected String remark;// 应用描述
  	@ApiParam(value="创建人ID")
	protected String createUser;// 创建人ID
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人ID")
	protected String lastUpdateUser;// 更新人ID
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="最后更新时间")
	protected Date lastUpdateTime;// 最后更新时间
  	@ApiParam(value="删除状态(Y/N)")
	protected String isdeleted;// 删除状态(Y/N)
	@ApiParam("组织id")
	protected String orgId;

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public AppInfo() {
		super();
	}
	
  	public AppInfo(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAppName() {
		return appName;
	}
	public void setAppName(String appName) {
		this.appName = appName;
	}
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	public String getIsdeleted() {
		return isdeleted;
	}
	public void setIsdeleted(String isdeleted) {
		this.isdeleted = isdeleted;
	}
}
