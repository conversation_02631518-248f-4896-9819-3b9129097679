package com.boot.iAdmin.access.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.boot.iAdmin.access.mapper.RoleAndAuthMapper;
import com.boot.iAdmin.access.model.role.Role;
import com.boot.iAdmin.access.model.roleAndAuth.RoleAndAuth;
import com.boot.iAdmin.access.service.api.IRoleAndAuthService;

@Service
public class RoleAndAuthServiceImpl implements IRoleAndAuthService {

	@Autowired
	private RoleAndAuthMapper roleAndAuthMapper;

	/**
	 * 更新角色权限关联关系
	 * <P>
	 * 全删全增
	 * */
	
	public void updateRoleAndAuthsWithOneRole(Role role, String[] auths) {
		RoleAndAuth roleAndAuth = new RoleAndAuth();
		roleAndAuth.setRole_id(role.getRole_id());
		// 删除角色原先权限关联关系
		roleAndAuthMapper.deleteAuthsByOneRole(roleAndAuth);
		// 循环新增
		if (auths == null)
			return;
		for (String auth_id : auths) {
			roleAndAuth.setId(null);
			roleAndAuth.setAuthority_id(Long.parseLong(auth_id));
			roleAndAuthMapper.insertRoleAndAuth(roleAndAuth);
		}
	}

	/**
	 * 根据权限ID删除角色权限关联关系
	 * */
	
	public void deleteRoleAndResourceByAuthIds(String authIds) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("authIds", authIds.split(","));
		roleAndAuthMapper.deleteRoleAndResourceByAuthIds(map);
	}
	
	/**
	 * 根据角色ID删除角色权限关联关系
	 * */
	
	public void deleteRoleAndResourceByRoleIds(String roleIds) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("roleIds", roleIds.split(","));
		roleAndAuthMapper.deleteRoleAndResourceByRoleIds(map);
	}
	
}
