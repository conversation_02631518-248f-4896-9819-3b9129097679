package com.boot.iAdmin.access.service.impl;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.mapper.AuthoritiesMapper;
import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.authAndResource.AuthAndResource;
import com.boot.iAdmin.access.model.authority.AuthVo;
import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IAuthAndResourceService;
import com.boot.iAdmin.access.service.api.IAuthorityService;
import com.boot.iAdmin.access.service.api.IRoleAndAuthService;

@Service
public class AuthorityServiceImpl implements IAuthorityService{
	
	@Autowired
	private AuthoritiesMapper authorityMapper;
	
	@Autowired
	private SysUserInfoMapper sysUserInfoMapper;
	
	@Autowired
	private IAuthAndResourceService authAndResourceService;
	
	@Autowired
	private IRoleAndAuthService roleAndAuthService;
	
	/**
	 * 获取权限集合
	 * */
	public Collection<SysAuthorities> getAllAuth() {
		return this.getAuth(new SysAuthorities());
	}

	
	/**
	 * 根据条件查询权限
	 * */
	public Collection<SysAuthorities> getAuth(SysAuthorities auth) {
		return authorityMapper.getAuth(auth);
	}

	
	/**
	 * 根据角色ID查询权限
	 * */
	public Collection<SysAuthorities> getAuthByRoleId(Long role_id) {
		return authorityMapper.getAuthByRoleId(role_id);
	}

	
	/**
	 * 分页查询权限
	 * */
	public BootstrapTableModel<AuthVo> loadAuthByPage(BootstrapTableModel<AuthVo> model) {
		return model.addRows(authorityMapper.loadAuthByPage(model)).addTotals(authorityMapper.loadTotalAuths(model));
	}

	
	/**失效选中权限*/
	public void disableAuthById(Long authId) {
		SysAuthorities auth = new SysAuthorities(authId);
		auth.setEnable(0);
		authorityMapper.updateIgnoreNull(auth);
	}

	
	/**恢复选中权限*/
	public void recoverAuthById(Long authId) {
		SysAuthorities auth = new SysAuthorities(authId);
		auth.setEnable(1);
		authorityMapper.updateIgnoreNull(auth);
	}

	/**
	 * 批量删除权限
	 * <P>物理删除，不可恢复
	 * <P>同时删除权限角色，权限资源关联关系等冗余数据
	 * 
	 * @param authIds 权限主键集合
	 * */
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void deleteAuthByIds(String authIds) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("authIds", authIds.split(","));
		//TODO 删除角色权限关联表中数据 
		roleAndAuthService.deleteRoleAndResourceByAuthIds(authIds);
		//TODO 删除权限资源关联表中数据
		authAndResourceService.deleteAuthAndResourceByAuthIds(authIds);
		//删除权限
		authorityMapper.deleteAuthByIds(map);
	}

	
	/**
	 * 新增权限
	 * */
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void insertAuth(SysAuthorities auth,String resourceIds) {
		//新增权限
		authorityMapper.insertAuth(auth);
		//新增权限资源关联关系
		if(StringUtils.isNotBlank(resourceIds)){
			for(String id : resourceIds.split(",")){
				authAndResourceService.insertAuthAndResource(new AuthAndResource(auth.getAuthority_id(),Long.parseLong(id)));
			}
		}
	}


	/**
	 * 根据权限ID获取权限
	 * */
	public SysAuthorities selectAuthById(SysAuthorities auth) {
		List<SysAuthorities> auths = (List<SysAuthorities>)authorityMapper.getAuth(auth);
		return auths.get(0);
	}

	
	/**
	 * 修改权限
	 * @param auth 权限信息
	 * @param selResourcesIds 所选中的资源ID
	 * @param allResourcesIds 所涉及到的资源ID
	 * */
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void updateAuth(SysAuthorities auth, String allResourcesIds, String selResourcesIds) {
		//更新权限基本信息
		authorityMapper.updateIgnoreNull(auth);
		//更新权限所关联资源信息
		//删除所涉及到的资源中已存在的关联关系
		authAndResourceService.deleteAuthAndResourceInCurrResourcesIds(auth,allResourcesIds);
		//新增所选中的资源关联关系
		if(StringUtils.isBlank(selResourcesIds)) return;
		for(String resourceId : selResourcesIds.split(",")){
			authAndResourceService.insertAuthAndResource(new AuthAndResource(auth.getAuthority_id(),Long.parseLong(resourceId)));
		}
	}
	
	public List<String> validateAuthorityName(SysAuthorities auth){
		return authorityMapper.validateAuthorityName(auth);
	}
	
	public List<SysUser> judgeDeleteAuthsByAuthIds(String authIds){
		return sysUserInfoMapper.loadUserByAuthorIDs(authIds);
	}
}
