package com.boot.iAdmin.access.model.module;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 自定义菜单树
 * 
 * <AUTHOR>
 * @date 2016年5月29日 17:36:51
 * */
public class CustomModuleTree extends Module {
	
	//系统根目录ID
	private static final long ROOT_ID = 1;
	
	private String path;//菜单路径

	public CustomModuleTree() {
		super();
	}

	public CustomModuleTree(List<CustomModuleTree> modules) {
		super();
		buildMoudleTree(modules);
	}

	// 子菜单集合
	private List<CustomModuleTree> chlidModules = new ArrayList<CustomModuleTree>();

	public List<CustomModuleTree> getChlidModules() {
		return chlidModules;
	}

	public void setChlidModules(List<CustomModuleTree> chlidModules) {
		this.chlidModules = chlidModules;
	}

	/**
	 * 构造菜单树
	 * */
	private void buildMoudleTree(List<CustomModuleTree> modules) {
		// FIXME 如果菜单没有关联资源且不含有子菜单则过滤
		validateModule(modules);
		Iterator<CustomModuleTree> it = modules.iterator();
		while (it.hasNext()) {
			CustomModuleTree module = it.next();
			if (module.parent.longValue() == ROOT_ID) {// ID为1的菜单为系统根目录
				module.setPath(String.valueOf(module.getModule_id()));
				this.chlidModules.add(module);
				it.remove();
			}
		}
		buildMoudleTree(chlidModules, modules);
	}

	// 如果菜单没有关联资源且不含有子菜单则过滤
	private void validateModule(List<CustomModuleTree> modules) {
		Iterator<CustomModuleTree> it = modules.iterator();
		while (it.hasNext()) {
			CustomModuleTree module = it.next();
			if (module.resource_id == null && !haveChild(module, modules)) {// ID为0的菜单为系统根目录
				it.remove();
			}
		}
	}

	// 判断该菜单是否都含有子菜单
	//fix 仅判断是否含有子菜单，防止子菜单下还有有效子菜单时无法展示
	//fix 递归所有子节点直到找到有效子节点才返回true，否则遍历所有子节点都无效后返回false
	private boolean haveChild(CustomModuleTree module, List<CustomModuleTree> modules) {
		for (CustomModuleTree mo : modules) {
			if (module.module_id.longValue() == mo.parent.longValue()) {//获取到子菜单
				if(mo.resource_id != null || haveChild(mo,modules)) {//判断子菜单是否关联资源,是则当前菜单有效，否则继续判断子菜单下是否含有有效子菜单
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 递归构造子菜单
	 * */
	private void buildMoudleTree(List<CustomModuleTree> moduleTrees, List<CustomModuleTree> modules) {
		for (CustomModuleTree moduleTree : moduleTrees) {
			for (CustomModuleTree module : modules) {
				if (moduleTree.module_id.longValue() == module.parent.longValue()) {
					module.setPath(moduleTree.getPath() + "/" + module.getModule_id());
					moduleTree.getChlidModules().add(module);
				}
			}
			buildMoudleTree(moduleTree.getChlidModules(), modules);
		}
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}
	
}
