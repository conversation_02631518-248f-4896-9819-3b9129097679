package com.boot.iAdmin.access.model.sys_logs;

import java.util.Date;
import java.io.Serializable;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： sys_logs <br/>
 *         描述：InnoDB free: 163840 kB <br/>
 */
public class Logs implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 4782362480001796618L;
	// 需要手动添加非默认的serialVersionUID
	protected Long id;// 主键
	protected String userid;// 登录用户
	protected String module;// 模块名
	protected String method;// 方法名
	protected String params;// 参数
	protected String response_date;// 响应时间
	protected String ip;// IP
	@DateTimeFormat(pattern="yyyy-MM-dd")
	protected Date execute_date;// 执行时间
	protected String commite;// 描述
	@DateTimeFormat(pattern="yyyy-MM-dd")
	protected Date create_date;// 记录日期
	protected Integer status;//执行结果 1--成功 2--失败
	
	public Logs() {
		super();
	}
	
	public Logs(long id) {
		super();
		this.id = id;
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getModule() {
		return module;
	}
	public void setModule(String module) {
		this.module = module;
	}
	public String getMethod() {
		return method;
	}
	public void setMethod(String method) {
		this.method = method;
	}
	public String getParams() {
		return params;
	}
	public void setParams(String params) {
		this.params = params;
	}
	public String getResponse_date() {
		return response_date;
	}
	public void setResponse_date(String response_date) {
		this.response_date = response_date;
	}
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}
	public Date getExecute_date() {
		return execute_date;
	}
	public void setExecute_date(Date execute_date) {
		this.execute_date = execute_date;
	}
	public String getCommite() {
		return commite;
	}
	public void setCommite(String commite) {
		this.commite = commite;
	}
	public Date getCreate_date() {
		return create_date;
	}
	public void setCreate_date(Date create_date) {
		this.create_date = create_date;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
	
	
}
