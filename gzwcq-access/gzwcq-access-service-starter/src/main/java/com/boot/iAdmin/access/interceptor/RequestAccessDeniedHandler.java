package com.boot.iAdmin.access.interceptor;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.catalina.connector.Response;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.AccessDeniedHandler;

import com.boot.iAdmin.access.common.ControllerTools;
import com.boot.iAdmin.access.model.common.Constant;

public class RequestAccessDeniedHandler implements AccessDeniedHandler {
	
	private static final Log logger = LogFactory.getLog(RequestAccessDeniedHandler.class);
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.security.web.access.AccessDeniedHandler#handle(javax
	 * .servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse,
	 * org.springframework.security.access.AccessDeniedException)
	 */
	private String errorPage;
	
	
	// ~ Methods
	// ========================================================================================================

	public RequestAccessDeniedHandler(String errorPage) {
		super();
		this.errorPage = errorPage;
	}

	@Override
	public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException,
			ServletException {
		logger.info(String.format("@用户[%s]访问资源URL[%s]被拒绝", SecurityContextHolder.getContext().getAuthentication().getPrincipal(),request.getRequestURI()));
		ControllerTools.print(response, Constant.ACCESSDENIED,Response.SC_FORBIDDEN);//前后端分离都为Ajax
		/*boolean isAjax = ControllerTools.isAjaxRequest(request);
		if (isAjax) {
			ControllerTools.print(response, Constant.ACCESSDENIED,Response.SC_FORBIDDEN);
		} else if (!response.isCommitted()) {
			if (errorPage != null) {
				// Put exception into request scope (perhaps of use to a view)
				request.setAttribute(WebAttributes.ACCESS_DENIED_403, accessDeniedException);

				// Set the 403 status code.
				response.setStatus(HttpServletResponse.SC_FORBIDDEN);

				// forward to error page.
				RequestDispatcher dispatcher = request.getRequestDispatcher(errorPage);
				dispatcher.forward(request, response);
			} else {
				response.sendError(HttpServletResponse.SC_FORBIDDEN, accessDeniedException.getMessage());
			}
		}*/
	}

	/**
	 * The error page to use. Must begin with a "/" and is interpreted relative
	 * to the current context root.
	 * 
	 * @param errorPage
	 *            the dispatcher path to display
	 * 
	 * @throws IllegalArgumentException
	 *             if the argument doesn't comply with the above limitations
	 */
	public void setErrorPage(String errorPage) {
		if ((errorPage != null) && !errorPage.startsWith("/")) {
			throw new IllegalArgumentException("errorPage must begin with '/'");
		}

		this.errorPage = errorPage;
	}

}
