package com.boot.iAdmin.appInfo.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.boot.iAdmin.appInfo.entity.AppInfo;

public interface IAppInfoMapper {
	
	/*保存对象*/
	void insert(AppInfo appInfo);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(Map<String, Object> map);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(AppInfo appInfo);
	
	/**更新*/
	void update(AppInfo appInfo);
	
	/*根据主键查询对象*/
	AppInfo selectAppInfoByPrimaryKey(AppInfo appInfo);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<AppInfo> selectForList(AppInfo appInfo);
	
	/**
	 * 数据唯一性验证
	 * */
	List<AppInfo> selectForUnique(AppInfo appInfo);

	AppInfo selectAppInfoByAppKey(@Param("appId")String appId);
	
}