package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.resource.SysResource;

/**
 * 资源Dao
 * */
public interface ResourceMapper {
	
	/**
	 * 获取该权限下关联的资源集合
	 * @param auth 权限
	 * */
	List<SysResource> getResourcesByAuth(SysAuthorities auth);
	
	/**
	 * 根据父节点ID加载所有子节点
	 * @param id 父节点ID
	 * */
	Collection<ZTreeNode> loadResourceByParentId(String id);
	
	/**
	 * 获取单个资源信息
	 * 
	 * @return
	 * */
	SysResource selectResource(SysResource sysResource);
	
	/**
	 * 更新资源信息
	 * */
	void updateIgnoreNull(SysResource resource);
	
	/**新增资源*/
	void insertResource(SysResource resource);
	
	/**
	 * 删除资源，包括其下子资源
	 * */
	void deleteResourceTree(SysResource resource);
	
	/**
	 * 根据所传资源查询其以及其子资源是否被权限关联
	 * */
	int getTotalAuthAndResource(SysResource resource);
	
	/**
	 * 根据所传资源查询其以及其子资源是否被菜单关联
	 * */
	int getTotalMoudleAndResource(SysResource resource);
	
	/**
	 * 参数唯一性验证
	 * */
	List<Map<String, String>> selectForUnique(SysResource resource);
	
}
