package com.boot.iAdmin.access.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.boot.iAdmin.access.mapper.AuthAndResourceMapper;
import com.boot.iAdmin.access.model.authAndResource.AuthAndResource;
import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.service.api.IAuthAndResourceService;

@Service
public class AuthAndResourceServiceImpl implements IAuthAndResourceService{
	
	@Autowired
	private AuthAndResourceMapper authAndResourceMapper;
	
	/**
	 * 新增权限资源关联关系
	 * 
	 * @param authAndResource 关联关系对象
	 * */
	public void insertAuthAndResource(AuthAndResource authAndResource) {
		authAndResourceMapper.insertAuthAndResource(authAndResource);
	}
	
	/**
	 * 根据权限ID和所涉及到的资源集合，删除在当前资源集合中的当前权限资源关联关系
	 * @param auth 权限
	 * @param allResourcesIds 所涉及到的资源集合
	 * */
	public void deleteAuthAndResourceInCurrResourcesIds(SysAuthorities auth, String allResourcesIds) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("auth_id", auth.getAuthority_id());
		map.put("currResourceIds", allResourcesIds.split(","));
		authAndResourceMapper.deleteAuthAndResourceInCurrResourcesIds(map);
	}
	
	/**
	 * 根据权限ID删除权限资源关联关系
	 * */
	public void deleteAuthAndResourceByAuthIds(String authIds) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("authIds", authIds.split(","));
		authAndResourceMapper.deleteAuthAndResourceByAuthIds(map);
	}
	
}
