package com.boot.iAdmin.access.service.impl;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.role.Role;
import com.boot.iAdmin.access.model.roleAndUser.RoleAndUser;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;
import com.boot.iAdmin.access.service.api.IRoleManageService;
import com.boot.iAdmin.access.service.api.IUserAndRoleService;
import com.boot.iAdmin.access.service.api.IUserManageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserManageServiceImpl implements IUserManageService {

    @Autowired
    private SysUserInfoMapper userMapper;

    @Autowired
    private IUserAndRoleService userAndRoleService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private IRoleManageService roleManageService;

    /**
     * 根据用户ID逻辑删除用户
     */

    public void deleteUserById(String userId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("status", 0);
        userMapper.updateUserStatus(map);
    }

    /**
     * 根据用户ID恢复用户
     */

    public void recoverUserById(String userId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        map.put("status", 1);
        userMapper.updateUserStatus(map);
    }

    /**
     * 根据用户ID获取与用户详细信息
     */
    public SysUserVo getUserInfoByUserId(String userId) {
        SysUserVo sysUserVo = userMapper.getUserInfoByUserId(userId);
        SysOrganization org = organizationService.getOrgByOrgId(sysUserVo.getOrganization_id());
        sysUserVo.setOrgLevel(org.getBusiness_level());
        sysUserVo.setBusinessType(org.getBusinesstype());
        return sysUserVo;
    }

    /**
     * 新增用户
     */
    public void insertUser(SysUser user) {
    	user.setIsdeleted(Constants.NO_DELETED);
        userMapper.insertUser(user);
    }

    /**
     * 根据用户主键集合批量删除用户
     * <p>
     * 逻辑删除
     * <p>
     * 同时删除用户角色关联关系冗余数据
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteUserByIds(String userIds) {
        Map<String, Object> map = new HashMap<String, Object>(1) {
            {
                put("userIds", userIds.split(","));
            }
        };
        // 待删除的用户id中,有在途单的用户
        List<SysUserVo> hasReviewingUsers =  this.selectReviewingJbxxByUserIds(map);
        if (!hasReviewingUsers.isEmpty()) {
            throw new RuntimeException("用户" + hasReviewingUsers.parallelStream()
                    .map(SysUserVo::getName).collect(Collectors.toList()) + "有在途流程,无法删除!");
        }
        // TODO 删除用户角色关联关系
        userAndRoleService.deleteUserAndRoleByUserIds(userIds);
        userMapper.deleteUserByIds(map);
    }

    /**
     * 查找拥有在途登记的用户
     */
    @Override
    public List<SysUserVo> selectReviewingJbxxByUserIds(Map<String, Object> map) {
        return userMapper.selectReviewingJbxxByUserIds(map);
    }

    /**
     * 更新用户信息
     * <p>
     * 包含用户基本信息和用户角色关联关系
     *
     * @param user  用户基本信息
     * @param roles 角色ID数组
     */

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateUser(SysUser user, String[] roles) {
        this.updateUserInfoIgnoreNull(user);
        // 更新用户角色关联关系
//		userAndRoleService.updateUserAndRolesWithOneUser(user, roles);

        //自动关联用户角色权限
        addUserAndRole(user);
    }

    /**
     * 更新角色基本信息
     */
    public void updateUserInfoIgnoreNull(SysUser user) {
        userMapper.updateUserInfoIgnoreNull(user);
    }

    /**
     * 分页查询
     */
    @Override
    public BootstrapTableModel<SysUserVo> loadUserByPage(BootstrapTableModel<SysUser> model) {
        BootstrapTableModel<SysUserVo> result = new BootstrapTableModel<SysUserVo>();
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        Collection<SysUserVo> userList =  userMapper.loadUserByPage(model);
        for(SysUserVo u : userList){
            //查询角色列表
            u.setRoles(roleManageService.selectRolesByUserId(u.getUser_id()));
            if(u.getUser_id().equals(user.getUser_id())){
                u.setIsMySelf(true);
            }
        }
        return result.addRows(userList).addTotals(userMapper.loadTotalUsers(model));
    }

    /**
     * 验证参数唯一性
     */
    @Override
    public boolean validateUniqueParam(SysUser user) {
        return userMapper.selectForUnique(user).size() == 0;
    }

    /**
     * 新增用户
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertUser(SysUser user, String[] roles) {
        //根据页面设置的组织信息--更新用户公司组信息
        userMapper.insertUser(user);

        //自动关联用户角色权限
        addUserAndRole(user);

		/*//为空，直接返回
		if(roles == null) return;
		//新增用户角色关联关系
		RoleAndUser roleAndUser = new RoleAndUser();
		roleAndUser.setUser_id(user.getUser_id());
		for(String role_id : roles){
			roleAndUser.setId(null);
			roleAndUser.setRole_id(Long.parseLong(role_id));
			userAndRoleService.insertUserAndRole(roleAndUser);
		}
		//新增用户用户组关联关系
		UsersGroups group =new UsersGroups();
		group.setUser_id(user.getUser_id());
		group.setGroup_id(user.getGroup_id());
		group.setOrg_id(null);//公司ID，待美工设置
		usersGroupsService.save(group);
		
		//新增用户数据权限关联关系
		UsersDataAuthorities dataAuth=new UsersDataAuthorities();
		dataAuth.setData_id(user.getData_id());
		dataAuth.setUser_id(user.getUser_id());
		dataAuth.setOrg_id(null);//公司ID，待美工设置
*/
    }

    /**
     * 自动关联用户角色权限
     * @param user
     */
    private void addUserAndRole(SysUser user){
        //自动关联账号角色
        //系统管理员：公告、文件、组织机构、用户  SYSTEM_ADMIN
        //国资委管理员（企业为国资委+账号类型为系统管理员） GZW_ADMIN
        //一级企业管理员（企业级次为“1”+账号类型为系统管理员）  ONE_LEVEL_ADMIN
        //业务管理员：
        //省国资委复审（企业为浙江省国资委+账号类型为业务管理员+审核层级复审） SGZW_RECHECK
        //省国资委初审（企业为浙江省国资委+账号类型为业务管理员+审核层级初审） SGZW_FIRST_TRIAL
        //国资委复审（企业为非浙江省国资委+账号类型为业务管理员+审核层级复审） GZW_RECHECK
        //国资委初审（企业为非浙江省国资委+账号类型为业务管理员+审核层级初审） GZW_FIRST_TRIAL
        //一级企业复审（企业级次为“1”+账号类型为业务管理员+审核层级复审）ONE_LEVEL_RECHECK
        //一级企业初审（企业级次为“1”+账号类型为业务管理员+审核层级初审）ONE_LEVEL_FIRST_TRIAL
        //普通审核（企业级次为“2”及以上数字+账号类型为业务管理员） NORMAL_REVIEW

        //查询关联组织数据
        SysOrganization org = organizationMapper.getOrgByOrgId(user.getOrganization_id());

        Role role1 = new Role();

        //先判断账号是系统管理员还是业务管理员
        if(Constants.SYSTEM_ADMIN_TYPE.equals(user.getType())){ //系统管理员
            //查询系统管理员角色
            Role role = new Role();
            role.setRole_code(Constants.SYSTEM_ADMIN);
            role = roleManageService.getRole(role);
            RoleAndUser roleAndUser = new RoleAndUser();
            roleAndUser.setUser_id(user.getUser_id());
            roleAndUser.setId(null);
            roleAndUser.setRole_id(role.getRole_id());
            userAndRoleService.insertUserAndRole(roleAndUser);

            //判断企业是国资委还是1级企业
            if(StringUtils.isNotEmpty(org.getBusinesstype()) && "1".equals(org.getBusinesstype())){ //国资委管理员
                //查询系统管理员角色
                role1.setRole_code(Constants.GZW_ADMIN);

            }
            if(StringUtils.isNotEmpty(org.getBusiness_level()) && Integer.parseInt(org.getBusiness_level()) == 1){ //一级企业管理员
                //查询系统管理员角色
                role1.setRole_code(Constants.SYSTEM_ADMIN);
            }

        }else if(Constants.BUSINESS_ADMIN_TYPE.equals(user.getType())){ //业务管理员
            //判断企业是国资委还是1级企业
            if(StringUtils.isNotEmpty(org.getBusinesstype()) && "1".equals(org.getBusinesstype())){ //国资委
                if(StringUtils.isNotEmpty(user.getAudit_level()) && "2".equals(user.getAudit_level())){ //复审
                    if (Constants.ZJSGZW_ORG_ID.equals(user.getOrganization_id())){
                        //省国资委复审
                        role1.setRole_code(Constants.SGZW_RECHECK);
                    }else {
                        //普通国资委复审角色
                        role1.setRole_code(Constants.GZW_RECHECK);
                    }
                }
                if(StringUtils.isNotEmpty(user.getAudit_level()) && "1".equals(user.getAudit_level())){ //初审
                    if (Constants.ZJSGZW_ORG_ID.equals(user.getOrganization_id())){
                        //省国资委初审
                        role1.setRole_code(Constants.SGZW_FIRST_TRIAL);
                    }else {
                        //普通国资委初审角色
                        role1.setRole_code(Constants.GZW_FIRST_TRIAL);
                    }
                }
            }
            if(StringUtils.isNotEmpty(org.getBusiness_level()) && Integer.parseInt(org.getBusiness_level()) >= 1){ //企业审核
                if(StringUtils.isNotEmpty(user.getAudit_level()) && "2".equals(user.getAudit_level())){ //复审
                    //企业复审角色
                    role1.setRole_code(Constants.RECHECK);
                }
                if(StringUtils.isNotEmpty(user.getAudit_level()) && "1".equals(user.getAudit_level())){ //初审
                    //企业初审角色
                    role1.setRole_code(Constants.FIRST_TRIAL);
                }
            }
/*            if(StringUtils.isNotEmpty(org.getBusiness_level()) && Integer.parseInt(org.getBusiness_level()) >= 2) { //普通审核
                if(StringUtils.isNotEmpty(user.getAudit_level()) && "2".equals(user.getAudit_level())){ //复审
                    //二级企业及以上复审角色
                    role1.setRole_code(Constants.TWO_LEVEL_RECHECK);
                }
                if(StringUtils.isNotEmpty(user.getAudit_level()) && "1".equals(user.getAudit_level())){ //初审
                    //二级企业及以上初审角色
                    role1.setRole_code(Constants.TWO_LEVEL_FIRST_TRIAL);
                }
            }*/
        }

        if(StringUtils.isNotEmpty(role1.getRole_code())){
            //先删除用户原有角色
            userAndRoleService.deleteUserAndRoleByUserIds(user.getUser_id());

            role1 = roleManageService.getRole(role1);
            RoleAndUser roleAndUser1 = new RoleAndUser();
            roleAndUser1.setUser_id(user.getUser_id());
            roleAndUser1.setId(null);
            roleAndUser1.setRole_id(role1.getRole_id());
            userAndRoleService.insertUserAndRole(roleAndUser1);
        }
    }

    /**
     * 权限设置
     * <P> 用户+公司
     */
    @Override
    public void authSetting(SysUserVo user, String[] roles, Long company_id) {
        //更新角色信息
        userAndRoleService.updateUserAndRolesWithOneUser(user, roles);
        //更新数据权限信息
//		usersDataAuthoritiesService.updateUserAndDataAuthWithOneUserAndCompany(user,company_id);
        //更新用户组信息
//		usersGroupsService.updateUserGroupWithOneUserAndCompany(user,company_id);
    }

    /**
     * 获取当前登录用户本级及以下的组织
     */
    @Override
    public Collection<ZTreeNode> getCurrentUserOrgTree(SysUser user,String id) {
        if (StringUtils.isBlank(id)){
            return organizationMapper.getCurrentUserOrganization(user.getOrganization_id());
        }else {
            return organizationService.loadOrganizationByParentIdTwo(id);
        }
    }

    @Override
    public void accountVerification(SysUser user){
        if (user == null){
            throw new RuntimeException("保存的用户不能为空!");
        }
        String organization_id = user.getOrganization_id();
        if (StringUtils.isBlank(organization_id)){
            throw new RuntimeException("保存的用户组织不能为空!");
        }
        SysUser user1 = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        Integer integer = organizationMapper.selectInsetStatus(user1.getOrganization_id(), organization_id);
        if (integer == 0 ){
            throw new RuntimeException("非下级的组织不能设置用户");
        }
        //只有系统管理员才能够管理账号
        SysUser loginUser = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        SysOrganization loginUserOrg = organizationService.selectOrganizationById(new SysOrganization(loginUser.getOrganization_id()));

        if (!Constants.SYSTEM_ADMIN_TYPE.equals(loginUser.getType())){
            throw new RuntimeException("非系统管理员无法新增或编辑账号!");
        }
        SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(user.getOrganization_id()));
        //如果保存的是系统管理员账号
        if ("1".equals(user.getType())){
            //一级企业系统管理员只能开通业务账号
            if (!"1".equals(loginUserOrg.getBusinesstype()) && loginUserOrg.getBusiness_level() != null
                    && Integer.parseInt(loginUserOrg.getBusiness_level()) == 1){
                throw new RuntimeException("一级企业系统管理员只能开通业务账号!");
            }
            //如果不是一级及以上企业或国资委
            if (!"1".equals(sysOrganization.getBusinesstype()) && sysOrganization.getBusiness_level() != null &&
                    Integer.parseInt(sysOrganization.getBusiness_level())>1){
                throw new RuntimeException("非一级及以上企业无系统管理员账号!");
            }/*else {//2022/03/23变更:去除账号数量限制
                SysUser u = new SysUser();
                u.setUser_id(user.getUser_id());
                u.setOrganization_id(user.getOrganization_id());
                u.setType("1");
                u.setIsdeleted(Constants.NO_DELETED);
                List<SysUser> sysUsers = userMapper.selectExistUsers(u);
                if (sysUsers != null && sysUsers.size() > 0){
                    throw new RuntimeException("该企业已存在系统管理员账号!");
                }
            }*/
        //如果保存的是业务管理员账号
        // 2022/03/14变更:账号管理变更,所有企业都需要有初审和复审
        // 2022/03/23变更:去除账号数量限制
        }/*else if ("2".equals(user.getType())){
            //如果不是一级及以上企业或国资委
            if (!"1".equals(sysOrganization.getBusinesstype()) && sysOrganization.getBusiness_level() != null &&
                    Integer.parseInt(sysOrganization.getBusiness_level())>1){
                //初审账号
                if ("1".equals(user.getAudit_level())){
                    SysUser u = new SysUser();
                    u.setUser_id(user.getUser_id());
                    u.setType("2");
                    u.setAudit_level("1");
                    u.setIsdeleted(Constants.NO_DELETED);
                    u.setOrganization_id(user.getOrganization_id());
                    List<SysUser> sysUsers = userMapper.selectExistUsers(u);
                    if (sysUsers != null && sysUsers.size() > 0){
                        throw new RuntimeException("该企业已存在初审管理员账号!");
                    }
                }else if ("2".equals(user.getAudit_level())){
                    SysUser u = new SysUser();
                    u.setUser_id(user.getUser_id());
                    u.setType("2");
                    u.setAudit_level("2");
                    u.setIsdeleted(Constants.NO_DELETED);
                    u.setOrganization_id(user.getOrganization_id());
                    List<SysUser> sysUsers = userMapper.selectExistUsers(u);
                    if (sysUsers != null && sysUsers.size() > 0){
                        throw new RuntimeException("该企业已存在复审管理员账号!");
                    }
                }
            }else {
                //初审账号
                if ("1".equals(user.getAudit_level())){
                    SysUser u = new SysUser();
                    u.setUser_id(user.getUser_id());
                    u.setType("2");
                    u.setAudit_level("1");
                    u.setIsdeleted(Constants.NO_DELETED);
                    u.setOrganization_id(user.getOrganization_id());
                    List<SysUser> sysUsers = userMapper.selectExistUsers(u);
                    if (sysUsers != null && sysUsers.size() > 1){
                        throw new RuntimeException("该企业已存在2个初审管理员账号!");
                    }
                }else if ("2".equals(user.getAudit_level())){
                    SysUser u = new SysUser();
                    u.setUser_id(user.getUser_id());
                    u.setType("2");
                    u.setAudit_level("2");
                    u.setIsdeleted(Constants.NO_DELETED);
                    u.setOrganization_id(user.getOrganization_id());
                    List<SysUser> sysUsers = userMapper.selectExistUsers(u);
                    if (sysUsers != null && sysUsers.size() > 0){
                        throw new RuntimeException("该企业已存在复审管理员账号!");
                    }
                }
            }
        }*/
    }
}
