package com.boot.iAdmin.jwt.util;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.io.ObjectOutputStream;
import java.io.ObjectInputStream;
import java.security.KeyFactory;

public class KeyPairGeneratorExampleUtils {

    private static final String PUBLIC_KEY_FILE = "public.key";
    private static final String PRIVATE_KEY_FILE = "private.key";

    public static void main(String[] args) {
        try {
            // 1. Generate RSA key pair
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(2048); // Specify the key size
            KeyPair keyPair = keyGen.generateKeyPair();

            PublicKey publicKey = keyPair.getPublic();
            PrivateKey privateKey = keyPair.getPrivate();

//            // 2. Save keys to files
//            saveKeyToFile(PUBLIC_KEY_FILE, publicKey);
//            saveKeyToFile(PRIVATE_KEY_FILE, privateKey);

            System.out.println("Keys have been generated and saved.");

            // 3. Load keys from files (for demonstration)
            PublicKey loadedPublicKey = loadPublicKeyFromFile(PUBLIC_KEY_FILE);
            PrivateKey loadedPrivateKey = loadPrivateKeyFromFile(PRIVATE_KEY_FILE);

            System.out.println("Loaded public key: " + Base64.getEncoder().encodeToString(loadedPublicKey.getEncoded()));
            System.out.println("Loaded private key: " + Base64.getEncoder().encodeToString(loadedPrivateKey.getEncoded()));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void saveKeyToFile(String fileName, java.security.Key key) throws Exception {
        try (FileOutputStream fos = new FileOutputStream(fileName);
             ObjectOutputStream oos = new ObjectOutputStream(fos)) {
            oos.writeObject(key);
        }
    }

    private static PublicKey loadPublicKeyFromFile(String fileName) throws Exception {
        try (FileInputStream fis = new FileInputStream(fileName);
             ObjectInputStream ois = new ObjectInputStream(fis)) {

            byte[] keyBytes = ((java.security.Key) ois.readObject()).getEncoded();
            X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(spec);
        }
    }

    private static PrivateKey loadPrivateKeyFromFile(String fileName) throws Exception {
        try (FileInputStream fis = new FileInputStream(fileName);
             ObjectInputStream ois = new ObjectInputStream(fis)) {

            byte[] keyBytes = ((java.security.Key) ois.readObject()).getEncoded();
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(spec);
        }
    }
}
