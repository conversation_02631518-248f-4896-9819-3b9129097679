package com.boot.iAdmin.access.model.dataAuthorities;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： sys_data_authorities <br/>
 *         描述：sys_data_authorities <br/>
 */
public class DataAuthoritiesVo extends DataAuthorities {

	private static final long serialVersionUID = 1L;
	private List<DataAuthoritiesVo> dataAuthoritiesList;
	
	public DataAuthoritiesVo() {
		super();
	}

	public DataAuthoritiesVo(long data_id) {
		super();
		this.data_id = data_id;
	}

	public List<DataAuthoritiesVo> getDataAuthoritiesList() {
		return dataAuthoritiesList;
	}

	public void setDataAuthoritiesList(List<DataAuthoritiesVo> dataAuthoritiesList) {
		this.dataAuthoritiesList = dataAuthoritiesList;
	}

}
