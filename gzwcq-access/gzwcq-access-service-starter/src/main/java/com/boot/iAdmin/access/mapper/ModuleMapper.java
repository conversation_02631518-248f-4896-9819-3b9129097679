package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;

import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.module.CustomModuleTree;
import com.boot.iAdmin.access.model.module.Module;
import com.boot.iAdmin.access.model.user.SysUser;

/**
 * 菜单DAO
 * */
public interface ModuleMapper {

	/**
	 * 根据登录用户获取所有菜单
	 * */
	List<CustomModuleTree> getAllModulesByUser(SysUser loginUser);

	/**
	 * 根据父节点查找子菜单
	 * */
	Collection<ZTreeNode> loadMenuByParentId(String id);

	/**
	 * 查询菜单信息
	 * */
	Module selectModule(Module module);

	/**
	 * 新增菜单
	 * */
	void insertModule(Module module);

	/**
	 * 删除菜单
	 * */
	void deleteModule(Module module);

	/**
	 * 更新菜单
	 * <P>忽略空字段
	 * */
	void updateIgnoreNull(Module module);
	
	/**
	 * 查询所有菜单
	 * */
	List<CustomModuleTree> selectAllModules();

	List<Module> selectForListModule(Module module);

}
