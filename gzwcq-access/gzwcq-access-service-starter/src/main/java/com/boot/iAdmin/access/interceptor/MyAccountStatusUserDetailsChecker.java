package com.boot.iAdmin.access.interceptor;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

/**
 * 自定义帐号状态验证器
 * <P> 默认验证是否锁定，是否有效，是否过期，证书是否有效
 * <AUTHOR>
 * @date 2016年9月9日 10:31:15
 * */
@Service(value="myPreAuthenticationChecks")
public class MyAccountStatusUserDetailsChecker extends AccountStatusUserDetailsChecker{
	
	private static final Log logger = LogFactory.getLog(MyAccountStatusUserDetailsChecker.class);

	@Override
	public void check(UserDetails user) {
		if (!user.isAccountNonLocked()) {
			logger.error("@帐号验证失败：帐号被锁定");
			throw new LockedException(messages.getMessage(
					"AccountStatusUserDetailsChecker.locked", "帐号被锁定"));
		}

		if (!user.isEnabled()) {
			logger.error("@帐号验证失败：帐号失效");
			throw new DisabledException(messages.getMessage(
					"AccountStatusUserDetailsChecker.disabled", "帐号失效"));
		}

		/*if (!user.isAccountNonExpired()) {
			logger.error("@帐号验证失败：帐号已过期");
			throw new AccountExpiredException(
					messages.getMessage("AccountStatusUserDetailsChecker.expired",
							"帐号已过期"));
		}

		if (!user.isCredentialsNonExpired()) {
			logger.error("@帐号验证失败：帐号证书失效");
			throw new CredentialsExpiredException(messages.getMessage(
					"AccountStatusUserDetailsChecker.credentialsExpired",
					"帐号证书失效"));
		}*/
	}

}
