package com.boot.iAdmin.access.interceptor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import com.boot.iAdmin.access.service.api.IResourceService;
import com.boot.iAdmin.redis.common.RedisUtil;
import com.google.common.base.Preconditions;

/**
 * 基于redis缓存的安全数据元
 * */
public class MyFilterInvocationSecurityMetadataSourceForRedis extends AbstractSecurityMetadataSource{
	
	//redis中缓存安全数据库的key值
	@Value("${com.auto.security.redis_metadata_key}")
	private String redis_metadata_key;
	
	private final static String SEPARATOR = ";";//资源匹配规则分隔符

	protected final Log logger = LogFactory.getLog(getClass());

//	private Map<String, Collection<ConfigAttribute>> requestMap;// 权限集合
	
	@Resource
	private IResourceService resourceService;//资源服务
	
	@Autowired  
    RedisUtil redisUtil;

	public MyFilterInvocationSecurityMetadataSourceForRedis() {
	}
	
	/*
	 * 重写，直接调用getRealAttributes获取，不使用二级缓存，避免集群环境缓存不一致
	 * */
	@Override
	public Collection<ConfigAttribute> getAttributes(Object object) {
		return getRealAttributes(object);
	}
	
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.security.access.SecurityMetadataSource#getAttributes
	 * (java.lang.Object)
	 * 
	 * <P> update by ruanhj 修改spring security 权限匹配策略，获取到匹配该资源的所有权限
	 * <P> update by ruanhj 2017年6月30日 16:49:21 扩展资源规则匹配，一个资源可增加多个规则，只要一个规则匹配成功，该资源就匹配成功，用;分隔符隔开
	 */
	public Collection<ConfigAttribute> getRealAttributes(Object object) throws IllegalArgumentException {
		final HttpServletRequest request = ((FilterInvocation) object).getRequest();

		Collection<ConfigAttribute> attrs = new ArrayList<ConfigAttribute>();
//		attrs.clear();
		for (Map.Entry<String, Collection<ConfigAttribute>> entry : getMetaDataTFromRd().entrySet()) {
//			AntPathRequestMatcher matcher = (AntPathRequestMatcher)entry.getKey();
			String[] patterns =  entry.getKey().split(SEPARATOR);
			for(String pattern : patterns){
				if(new AntPathRequestMatcher(pattern).matches(request)){
					for(ConfigAttribute val : entry.getValue()){
						if(!attrs.contains(val)) attrs.add(val);
					}
					break;
				}
			}
			/*if (entry.getKey().matches(request)) {
				for(ConfigAttribute val : entry.getValue()){
					if(!attrs.contains(val)) attrs.add(val);
				}
//				break;
			}*/
		}
		logger.info("@URL资源：" + request.getRequestURI() + " -> " + attrs);
		if (attrs == null || attrs.size() < 1) {
			logger.warn(String.format("@当前资源[%s]没有关联到任何权限", request.getRequestURI()));
		}
		return attrs;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.springframework.security.access.SecurityMetadataSource#
	 * getAllConfigAttributes()
	 */
	public Collection<ConfigAttribute> getAllConfigAttributes() {
		Set<ConfigAttribute> allAttributes = new HashSet<ConfigAttribute>();

		for (Map.Entry<String, Collection<ConfigAttribute>> entry : getMetaDataTFromRd().entrySet()) {
			allAttributes.addAll(entry.getValue());
		}

		return allAttributes;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.security.access.SecurityMetadataSource#supports(java
	 * .lang.Class)
	 */
	public boolean supports(Class<?> clazz) {
		return FilterInvocation.class.isAssignableFrom(clazz);
	}

	/**
	 * 加载资源
	 * */
	private Map<String, String> loadResuorce() {
		Map<String, String> map = new LinkedHashMap<String, String>();

		List<Map<String, String>> list = resourceService.getURLResourceMapping();
		Iterator<Map<String, String>> it = list.iterator();
		while (it.hasNext()) {
			Map<String, String> rs = it.next();
			String resourcePath = rs.get("resourcePattern");
			String authorityMark = rs.get("authorityMark");

			if (map.containsKey(resourcePath)) {
				String mark = map.get(resourcePath);
				map.put(resourcePath, mark + "," + authorityMark);
			} else {
				map.put(resourcePath, authorityMark);
			}
		}
		return map;
	}

	/**
	 * 构造请求资源Map
	 *<P> key--请求资源 value--权限 
	 * */
	protected Map<String, Collection<ConfigAttribute>> bindRequestMap() {
		Map<String, Collection<ConfigAttribute>> map = new LinkedHashMap<String, Collection<ConfigAttribute>>();

		Map<String, String> resMap = this.loadResuorce();
		for (Map.Entry<String, String> entry : resMap.entrySet()) {
			String key = entry.getKey();//resources --资源
			Collection<ConfigAttribute> atts = new ArrayList<ConfigAttribute>();
			atts = SecurityConfig.createListFromCommaDelimitedString(entry.getValue());//权限
			map.put(key, atts);
		}

		return map;
	}
	
	/**
	 * 初始化资源
	 * <P>构造以后执行
	 * */
	public void afterPropertiesSet() throws Exception {
		Preconditions.checkState(StringUtils.isNotBlank(redis_metadata_key),"权限缓存KEY值不能为空，com.auto.security.redis_metadata_key can not be null");
		saveMetaDataToRd();
	}
	
	/**
	 *刷新资源
	 * */
	public void refreshResuorceMap() {
		saveMetaDataToRd();
		super.refreshResuorceMap();
	}
	
	//缓存数据元
	private void saveMetaDataToRd(){
		Map<String, Collection<ConfigAttribute>> requestMap = this.bindRequestMap();
		redisUtil.set(redis_metadata_key, requestMap);
		logger.info("资源加载完毕，并存入redis,资源权限列表：" + requestMap);
	}
	
	
	//获取数据元
	@SuppressWarnings("unchecked")
	private Map<String, Collection<ConfigAttribute>> getMetaDataTFromRd(){
		if(redisUtil.get(redis_metadata_key) == null){
			synchronized(this) {
				if(redisUtil.get(redis_metadata_key) == null){
					//重新加载资源
					refreshResuorceMap();
				}
			}
		}
		return (Map<String, Collection<ConfigAttribute>>)redisUtil.get(redis_metadata_key);
	}

}
