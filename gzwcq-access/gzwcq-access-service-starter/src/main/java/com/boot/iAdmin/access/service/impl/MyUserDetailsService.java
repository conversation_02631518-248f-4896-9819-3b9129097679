package com.boot.iAdmin.access.service.impl;

import java.util.ArrayList;
import java.util.Collection;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.boot.IAdmin.common.utils.JsonUtil;
import com.boot.iAdmin.access.model.authority.CustomGrantedAuthority;
import com.boot.iAdmin.access.model.user.SysUserVo;
import com.boot.iAdmin.access.service.api.IOrganizationService;
import com.boot.iAdmin.access.service.api.ISysUsersService;

@Service("myUserDetailsService")
@SuppressWarnings("unchecked")
public class MyUserDetailsService implements UserDetailsService {
	
//	private final static String FUNCTION_AUTH_ALL = "ALL";//所有公司功能权限
	
	protected final Log logger = LogFactory.getLog(getClass());

	@Autowired
	private ISysUsersService sysUsersService;
	
	@Autowired
	private IOrganizationService organizationService;
	
	/*@Autowired
	private UserCache userCache;*/
	
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		Collection<CustomGrantedAuthority> auths = new ArrayList<CustomGrantedAuthority>();
		
		// 获取用户基本信息
		SysUserVo user = this.sysUsersService.getByUsername(username);

		if (user == null)
//			throw new UsernameNotFoundException(String.format("@未查询到用户[%s]的信息，登录失败", username));
			throw new UsernameNotFoundException("账号或密码错误");

		auths = getAuths(username);
		
		//当前组织id,当前组织名称,做非空判断，防止用户不设置组织信息
		if(StringUtils.isNotBlank(user.getOrganization_id())) {
			user.setCurrOrgId(user.getOrganization_id());
//			user.setCurrOrgName( organizationService.getOrgNameById(user.getOrganization_id()));
			user.setOrganization(organizationService.getOrgByOrgId(user.getOrganization_id()));
		}			
		//如果用户没有组织，或者用户所属的组织已被删除则登录失败
		if(user.getOrganization() == null) {
			throw new RuntimeException(String.format("用户没有组织信息，登录失败", username));
		}
		
		user.setAuthorities(auths);
		
		StringBuilder userInfo = new StringBuilder("*********************" + "user info" + "***********************\n");
		userInfo.append("功能权限：\n");
		userInfo.append(JsonUtil.toJSon(user.getAuthorities()) + "\n");
		userInfo.append("********************************************************");
		logger.info(userInfo.toString());

//		this.userCache.putUserInCache(user);

		return user;
	}
	
	/**
	 * 根据用户名获取用户功能权限
	 * */
	private Collection<CustomGrantedAuthority> getAuths(String username){
		return (Collection<CustomGrantedAuthority>)this.sysUsersService.loadUserAuthorities(username);
	}

}
