package com.boot.iAdmin.access.mapper;

import java.util.Map;

import com.boot.iAdmin.access.model.roleAndAuth.RoleAndAuth;

public interface RoleAndAuthMapper {

	/** 删除角色所关联的权限 */
	void deleteAuthsByOneRole(RoleAndAuth roleAndAuth);

	/** 新增角色权限关联关系 */
	void insertRoleAndAuth(RoleAndAuth roleAndAuth);

	/**
	 * 根据权限ID删除角色权限关联关系
	 * */
	void deleteRoleAndResourceByAuthIds(Map<String, Object> map);

	/**
	 * 根据角色ID删除角色权限关联关系
	 * */
	void deleteRoleAndResourceByRoleIds(Map<String, Object> map);

}
