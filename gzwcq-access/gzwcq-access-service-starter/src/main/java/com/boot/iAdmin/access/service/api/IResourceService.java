package com.boot.iAdmin.access.service.api;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.resource.SysResource;

/**
 * 资源服务
 * */
public interface IResourceService {
	
	/**
	 * 获取资源集合
	 * */
	List<Map<String, String>> getURLResourceMapping();
	
	/**
	 * 根据父节点ID加载所有子节点
	 * @param id 父节点ID
	 * */
	Collection<ZTreeNode> loadResourceByParentId(String id);
	
	/**
	 * 获取当前权限的资源集合
	 * @param authority_id 权限ID
	 * */
	Collection<SysResource> getResourcesByAuthId(Long authority_id);
	
	/**
	 * 异步加载资源树信息
	 * <P>当前权限已关联的资源默认选中
	 * @param nodeId 父节点ID
	 * @param authority_id 权限ID
	 * */
	Collection<ZTreeNode> loadResourceByParentId(String nodeId,Long authority_id);
	
	/**
	 * 获取单个资源
	 * */
	SysResource selectResource(SysResource sysResource);
	
	/**
	 * 更新资源信息
	 * */
	void updateResource(SysResource resource);
	
	/**
	 * 新增资源
	 * */
	void insertResource(SysResource resource);
	
	/**
	 * 删除资源，包括其下子资源
	 * */
	void deleteResource(SysResource resource);
	
	/**
	 * 根据所传资源查询其以及其子资源是否被权限关联
	 * */
	int getTotalAuthAndResource(SysResource resource);
	
	/**
	 * 根据所传资源查询其以及其子资源是否被菜单关联
	 * */
	int getTotalMoudleAndResource(SysResource resource);
	
	/**
	 * 参数唯一性验证
	 * */
	boolean validateUniqueParam(SysResource resource);

}
