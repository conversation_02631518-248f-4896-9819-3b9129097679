package com.boot.iAdmin.jwt.interceptor;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.boot.iAdmin.redis.common.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import com.alibaba.druid.util.PatternMatcher;
import com.alibaba.druid.util.ServletPathMatcher;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.jwt.common.Constant;
import com.boot.iAdmin.jwt.util.JWTUtils;

/**
 * 过滤器
 * <P>用于过滤客户端的服务请求，验证是否有访问权限
 * <AUTHOR>
 * @date 2019年11月26日 14:04:16
 * */
public class JWTServiceAccessFilter implements Filter{
	
	private Log logger = LogFactory.getLog(this.getClass());
	
	private Set<String>        excludesPattern;
	protected PatternMatcher   pathMatcher                       = new ServletPathMatcher();
	public static final String PARAM_NAME_EXCLUSIONS             = "exclusions";
	private RedisUtil redisUtil;

	public JWTServiceAccessFilter(RedisUtil redisUtil) {
		this.redisUtil = redisUtil;
	}

	@Override
	public void init(FilterConfig config) throws ServletException {
		 String exclusions = config.getInitParameter(PARAM_NAME_EXCLUSIONS);
         if (exclusions != null && exclusions.trim().length() != 0) {
             excludesPattern = new HashSet<String>(Arrays.asList(exclusions.split("\\s*,\\s*")));
         }
         logger.info("==============JWT访问过滤器初始化成功=================");
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		response.setContentType("text/html;charset=UTF-8");
		HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String requestURI = httpRequest.getRequestURI();
        //如果不需要过滤，则直接跳过
		if (isExclusion(requestURI)) {
            chain.doFilter(request, response);
            return;
        }
		//需要过滤，则判断当前请求中是否含有Token信息，如果有则通过token获取用户信息，否则直接拦截
		String token = httpRequest.getHeader("Authorization");
//		String token = httpRequest.getParameter("token");
		if(StringUtils.isBlank(token)) {
			httpResponse.setStatus(401);//未登录
			response.setContentType("application/json;charset=UTF-8");
			ResponseEnvelope re = new ResponseEnvelope();
			re.setSuccess(false);
			re.setMessage("没有获取到身份令牌！请登录");
            response.getWriter().print(JsonUtil.toJSon(re));
            return;
		}else {
			//验证JWT令牌是否有效
			String userJson = JWTUtils.verify(token, Constant.TOKEN_CLAIM,redisUtil);
			if(StringUtils.isBlank(userJson)) {
				httpResponse.setStatus(401);//未登录
				response.setContentType("application/json;charset=UTF-8");
				ResponseEnvelope re = new ResponseEnvelope();
				re.setSuccess(false);
				re.setMessage("令牌验证失败！请重新登录");
	            response.getWriter().print(JsonUtil.toJSon(re));
	            return;
			}else {//获取用户信息并缓存
				UserDetails userDetails = JsonUtil.readValue(userJson, SysUser.class);
				UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails,null,userDetails.getAuthorities());
				SecurityContextHolder.getContext().setAuthentication(authentication);//将当前用户身份信息放到Local
			}
		}
		chain.doFilter(request, response);//验证通过
	}
	
	/**
	 * 判断是否忽略过滤
	 * */
	public boolean isExclusion(String requestURI) {
        if (excludesPattern == null || requestURI == null) {
            return false;
        }

        for (String pattern : excludesPattern) {
            if (pathMatcher.matches(pattern, requestURI)) {
                return true;
            }
        }

        return false;
    }

}
