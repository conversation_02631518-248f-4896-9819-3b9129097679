package com.boot.iAdmin.access.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component(value = "mySecurityProperties")
@ConfigurationProperties(prefix = "com.auto.security")
public class MySecurityProperties {

	private List<String> ignores = new ArrayList<String>();// 不验证地址
	
	private List<String> sessionControlIgnores = new ArrayList<String>();// 不控制session并发用户

	// 登出地址
	private String logoutUrl;

	// 登录成功跳转地址
	private String loginSuccessUrl;

	// 登录失败跳转地址
	private String loginFailUrl;

	// 超时登录地址
	private String loginFormUrl;

	// 权限不足跳转地址
	private String errorPage403;

	// 是否集群/是否分布式缓存共享
	@Value("${com.auto.security.isCluster}")
	private boolean isCluster;
	
	private int maximumSessions = -1;//允许最大session并发数，默认为-1，不控制
	
	private String expiredUrl = "/resource/page/error/expired.jsp";//被下线跳转地址
	
	private boolean sessionStoreRedis;// session是否存储在redis中
	
	private String sessionStoreKey;//session中存储的key
	
	private String passwordEncoder;//密码加密策略

	public List<String> getIgnores() {
		return ignores;
	}

	public void setIgnores(List<String> ignores) {
		this.ignores = ignores;
	}

	public String getLogoutUrl() {
		return logoutUrl;
	}

	public void setLogoutUrl(String logoutUrl) {
		this.logoutUrl = logoutUrl;
	}

	public String getLoginSuccessUrl() {
		return loginSuccessUrl;
	}

	public void setLoginSuccessUrl(String loginSuccessUrl) {
		this.loginSuccessUrl = loginSuccessUrl;
	}

	public String getLoginFailUrl() {
		return loginFailUrl;
	}

	public void setLoginFailUrl(String loginFailUrl) {
		this.loginFailUrl = loginFailUrl;
	}

	public String getLoginFormUrl() {
		return loginFormUrl;
	}

	public void setLoginFormUrl(String loginFormUrl) {
		this.loginFormUrl = loginFormUrl;
	}

	public String getErrorPage403() {
		return errorPage403;
	}

	public void setErrorPage403(String errorPage403) {
		this.errorPage403 = errorPage403;
	}

	public boolean isCluster() {
		return isCluster;
	}

	public void setCluster(boolean isCluster) {
		this.isCluster = isCluster;
	}

	public int getMaximumSessions() {
		return maximumSessions;
	}

	public void setMaximumSessions(int maximumSessions) {
		this.maximumSessions = maximumSessions;
	}

	public String getExpiredUrl() {
		return expiredUrl;
	}

	public void setExpiredUrl(String expiredUrl) {
		this.expiredUrl = expiredUrl;
	}

	public boolean isSessionStoreRedis() {
		return sessionStoreRedis;
	}

	public void setSessionStoreRedis(boolean sessionStoreRedis) {
		this.sessionStoreRedis = sessionStoreRedis;
	}

	public String getSessionStoreKey() {
		return sessionStoreKey;
	}

	public void setSessionStoreKey(String sessionStoreKey) {
		this.sessionStoreKey = sessionStoreKey;
	}

	public List<String> getSessionControlIgnores() {
		return sessionControlIgnores;
	}

	public void setSessionControlIgnores(List<String> sessionControlIgnores) {
		this.sessionControlIgnores = sessionControlIgnores;
	}

	public String getPasswordEncoder() {
		return passwordEncoder;
	}

	public void setPasswordEncoder(String passwordEncoder) {
		this.passwordEncoder = passwordEncoder;
	}
	
	
}
