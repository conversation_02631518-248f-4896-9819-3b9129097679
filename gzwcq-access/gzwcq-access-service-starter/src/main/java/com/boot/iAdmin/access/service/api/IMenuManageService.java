package com.boot.iAdmin.access.service.api;

import java.util.Collection;
import java.util.List;

import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.module.Module;
import com.boot.iAdmin.access.model.module.ModuleVo;

/**
 * 菜单接口服务
 * */
public interface IMenuManageService {
	
	/**
	 * 异步加载菜单树信息
	 * 
	 * @param id 父节点ID
	 * */
	Collection<ZTreeNode> loadMenuByParentId(String id);
	
	/**
	 * 查询菜单信息
	 * */
	Module selectModule(Module module);
	
	/**
	 * 新增菜单
	 * */
	void insertModule(Module module);
	
	/**
	 * 删除菜单
	 * */
	void deleteModule(Module module);
	
	/**
	 * 更新菜单
	 * */
	void updateModule(Module module);
	
	
	List<Module> selectForListModule(ModuleVo module);

}
