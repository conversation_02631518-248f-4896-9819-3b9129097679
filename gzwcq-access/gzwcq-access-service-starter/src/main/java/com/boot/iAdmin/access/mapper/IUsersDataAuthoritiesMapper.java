package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.userAndData.UsersDataAuthorities;
import com.boot.iAdmin.access.model.userAndData.UsersDataAuthoritiesVo;

@Repository
public interface IUsersDataAuthoritiesMapper {
	
	/*保存对象*/
	void save(UsersDataAuthorities usersDataAuthorities);
	
	/*根据表主键删除对象,采用foreach遍历*/
	void delete(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(UsersDataAuthorities usersDataAuthorities);
	
	/*分页查询对象*/
	Collection<UsersDataAuthoritiesVo> queryUsersDataAuthoritiesByPage(BootstrapTableModel<UsersDataAuthoritiesVo> bootModel);
	
	/*数据总量查询*/
	long queryTotalUsersDataAuthoritiess(BootstrapTableModel<UsersDataAuthoritiesVo> bootModel);
	
	/*根据主键查询对象*/
	UsersDataAuthorities queryUsersDataAuthoritiesById(UsersDataAuthorities usersDataAuthorities);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<UsersDataAuthorities> selectForList(UsersDataAuthorities usersDataAuthorities);
	
	/**
	 * 数据唯一性验证
	 * */
	List<UsersDataAuthorities> selectForUnique(UsersDataAuthorities usersDataAuthorities);

	void deleteByUserAndCompany(UsersDataAuthorities uda);
	
}