package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.Map;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.sys_logs.Logs;
import com.boot.iAdmin.access.model.sys_logs.LogsVo;

public interface ILogsMapper {
	
	/*保存对象*/
	void save(Logs logs);
	
	/*根据表主键删除对象,采用foreach遍历*/
	void delete(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Logs logs);
	
	/*分页查询对象*/
	Collection<LogsVo> queryLogsByPage(BootstrapTableModel<LogsVo> bootModel);
	
	/*数据总量查询*/
	long queryTotalLogss(BootstrapTableModel<LogsVo> bootModel);
	
	/*根据主键查询对象*/
	LogsVo queryLogsById(Logs logs);
	
	/*根据部分属性对象查询全部结果，不分页*/
	Collection<LogsVo> selectForList(Logs logs);
	
}