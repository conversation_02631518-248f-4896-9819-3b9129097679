package com.boot.iAdmin.jwt.start;

import javax.servlet.Filter;

import com.boot.iAdmin.access.service.api.IUserManageService;
import com.boot.iAdmin.jwt.util.MathCaptchaGeneratorUtils;
import com.boot.iAdmin.redis.common.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.core.userdetails.UserDetailsService;

import com.boot.iAdmin.access.interceptor.MyAuthenticationProvider;
import com.boot.iAdmin.access.service.api.ISysUsersService;
import com.boot.iAdmin.jwt.interceptor.JWTAccessFilter;
import com.boot.iAdmin.jwt.interceptor.JWTUsernamePasswordAuthenticationFilter;

/**
 * JWT配置
 * */
public class JWTRegistrar {
	
	
	/**
	 * JWT身份认证过滤器
	 * */
	@Bean
	public FilterRegistrationBean<Filter> authFilterRegistration(@Autowired UserDetailsService userDetailsService, @Qualifier(value="myAuthenticationProvider") MyAuthenticationProvider authenticationProvider,
																 @Qualifier(value="myPreAuthenticationChecks") AccountStatusUserDetailsChecker preAuthenticationChecks, @Autowired MathCaptchaGeneratorUtils generatorUtils, @Autowired RedisUtil redisUtil,@Autowired IUserManageService userManageService) {
		FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<Filter>();
		registration.setFilter(new JWTUsernamePasswordAuthenticationFilter(userDetailsService,authenticationProvider,preAuthenticationChecks,generatorUtils,redisUtil,userManageService));
		registration.addUrlPatterns("/jwt/authentication");
		registration.setOrder(Integer.MIN_VALUE+50);
		return registration;
	}
	
	/**
	 * 用于过滤移动端过来的请求，判断是否有访问权限
	 * <P>/jwt开头的URL不会进入后端权限过滤器security而是通过本过滤器过滤删选
	 * */
	@Bean
	public FilterRegistrationBean<Filter> filterRegistration(@Autowired ISysUsersService sysUsersService,@Autowired RedisUtil redisUtil) {
		FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<Filter>();
		registration.setFilter(new JWTAccessFilter(sysUsersService,redisUtil));
		registration.addUrlPatterns("/jwt/*");
		registration.addInitParameter("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/gzwcq/jwt/openApi/*,/gzwcq/jwt/SSO/*,/gzwcq/userManageController/getCodeImage");//忽略的地址
		registration.setOrder(Integer.MIN_VALUE+51);
		return registration;
	}
	
}
