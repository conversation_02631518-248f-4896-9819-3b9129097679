package com.boot.iAdmin.access.interceptor;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.catalina.connector.Response;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;

import com.boot.iAdmin.access.common.ControllerTools;
import com.boot.iAdmin.access.model.common.Constant;


/**
 * 自定义未登录切入点
 * <P> 兼容AJAX请求，会话超时
 * 
 * <AUTHOR>
 * @date 2017年3月15日 14:30:50
 * */
public class CustomLoginUrlAuthenticationEntryPoint extends LoginUrlAuthenticationEntryPoint{
	
	
	public CustomLoginUrlAuthenticationEntryPoint(String loginFormUrl) {
		super(loginFormUrl);
	}

	@Override
	public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
		ControllerTools.print(response, Constant.TIMEOUT,Response.SC_FORBIDDEN);//前后端分离都采用Ajax方式，无法重定向
	}


}
