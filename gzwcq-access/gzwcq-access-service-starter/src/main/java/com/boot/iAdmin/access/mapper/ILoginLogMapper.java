package com.boot.iAdmin.access.mapper;

import java.util.List;
import java.util.Map;

import com.boot.iAdmin.access.model.loginLog.LoginLog;
import com.boot.iAdmin.access.model.loginLog.LoginLogParam;
import com.boot.iAdmin.access.model.loginLog.LoginLogVo;

public interface ILoginLogMapper {
	
	/*保存对象*/
	void insert(LoginLog loginLog);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(Map<String, Object> map);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(LoginLog loginLog);
	
	/**更新*/
	void update(LoginLog loginLog);
	
	/*分页查询对象*/
	List<LoginLogVo> queryLoginLogForList(LoginLogParam loginLogParam);
	
	/*数据总量查询*/
	long queryTotalLoginLogs(LoginLogParam loginLogParam);
	
	/*根据主键查询对象*/
	LoginLog selectLoginLogByPrimaryKey(LoginLog loginLog);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<LoginLog> selectForList(LoginLog loginLog);
	
	/**
	 * 数据唯一性验证
	 * */
	List<LoginLog> selectForUnique(LoginLog loginLog);
	
}