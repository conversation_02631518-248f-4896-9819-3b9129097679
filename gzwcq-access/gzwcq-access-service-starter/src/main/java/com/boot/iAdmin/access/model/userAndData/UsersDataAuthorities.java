package com.boot.iAdmin.access.model.userAndData;

import java.io.Serializable;

/**
 * <AUTHOR> <br/>
 *         表名： sys_users_data_authorities <br/>
 *         描述：用户与数据权限关系表 <br/>
 */
public class UsersDataAuthorities implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// 需要手动添加非默认的serialVersionUID
	protected Long user_id;// 用户id
	protected Long data_id;// 数据权限ID
	protected Long org_id;// 组织ID

	public UsersDataAuthorities() {
		super();
	}
	
	public UsersDataAuthorities(long user_id) {
		super();
		this.user_id = user_id;
	}
	
	public Long getUser_id() {
		return user_id;
	}
	public void setUser_id(Long user_id) {
		this.user_id = user_id;
	}
	public Long getData_id() {
		return data_id;
	}
	public void setData_id(Long data_id) {
		this.data_id = data_id;
	}
	public Long getOrg_id() {
		return org_id;
	}
	public void setOrg_id(Long org_id) {
		this.org_id = org_id;
	}
}
