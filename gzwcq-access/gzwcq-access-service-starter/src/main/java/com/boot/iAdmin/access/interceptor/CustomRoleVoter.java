package com.boot.iAdmin.access.interceptor;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

/**
 * 自定义角色权限投票器
 * 
 * <AUTHOR>
 */
public class CustomRoleVoter implements AccessDecisionVoter<Object> {

	private final Log logger = LogFactory.getLog(getClass());
	// ~ Instance fields
	// ================================================================================================

	private String rolePrefix = "ROLE_";

	// ~ Methods
	// ========================================================================================================

	public String getRolePrefix() {
		return rolePrefix;
	}

	/**
	 * Allows the default role prefix of <code>ROLE_</code> to be overridden.
	 * May be set to an empty value, although this is usually not desirable.
	 *
	 * @param rolePrefix
	 *            the new prefix
	 */
	public void setRolePrefix(String rolePrefix) {
		this.rolePrefix = rolePrefix;
	}

	@Override
	public boolean supports(ConfigAttribute attribute) {
		if ((attribute.getAttribute() != null) && attribute.getAttribute().startsWith(getRolePrefix())) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * This implementation supports any type of class, because it does not query
	 * the presented secure object.
	 *
	 * @param clazz
	 *            the secure object
	 *
	 * @return always <code>true</code>
	 */
	@Override
	public boolean supports(Class<?> clazz) {
		return true;
	}

	/**
	 * 投票决定用户是否能访问当前资源
	 * <P>
	 * 用户只要配置了【拥有当前资源的权限集合（attributes）】中任意一个即认为有访问当前资源的权限
	 * 
	 * @param authentication
	 *            身份信息
	 * @param object
	 *            FilterInvocation
	 * @param attributes
	 *            拥有当前资源的权限列表
	 * */
	@Override
	public int vote(Authentication authentication, Object object, Collection<ConfigAttribute> attributes) {
		if (authentication == null) {
			return ACCESS_DENIED;
		}
		int result = ACCESS_ABSTAIN;
		// 用户所拥有的权限集合
		Collection<? extends GrantedAuthority> authorities = extractAuthorities(authentication);

		for (ConfigAttribute attribute : attributes) {
			if (this.supports(attribute)) {
				result = ACCESS_DENIED;

				// Attempt to find a matching granted authority
				// 用户只要有一个拥有该资源的权限即返回成功
				for (GrantedAuthority authority : authorities) {
					if (attribute.getAttribute().equals(authority.getAuthority())) {
						logger.debug(String.format("@用户[%s]，权限[%s]匹配成功", authentication.getPrincipal(), attribute.getAttribute()));
						return ACCESS_GRANTED;
					}
				}
			}
		}
		return result;
	}

	Collection<? extends GrantedAuthority> extractAuthorities(Authentication authentication) {
		return authentication.getAuthorities();
	}
}
