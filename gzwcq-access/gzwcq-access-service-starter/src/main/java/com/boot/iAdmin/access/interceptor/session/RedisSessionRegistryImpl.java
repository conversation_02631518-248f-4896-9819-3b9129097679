package com.boot.iAdmin.access.interceptor.session;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.session.SessionDestroyedEvent;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.Assert;

/**
 * REDIS session注册器
 * FIXME 同一用户并发登录问题
 * <AUTHOR>
 * @date 2018年12月7日 13:03:12
 */
@SuppressWarnings("all")
public class RedisSessionRegistryImpl implements SessionRegistry, ApplicationListener<SessionDestroyedEvent> {

	private static final String DEFAULT_SESSIONS = "sessions";

	private static final String DEFAULT_PRINCIPALS = "principals";

	private String sessions_key = DEFAULT_SESSIONS;

	private String principals_key = DEFAULT_PRINCIPALS;

	@Resource
	private RedisTemplate redisTemplate;

	protected final Log logger = LogFactory.getLog(SessionRegistryImpl.class);

	public RedisSessionRegistryImpl() {
	}

	public RedisSessionRegistryImpl(String key) {
		if (StringUtils.isNotBlank(key)) {
			this.sessions_key = key + "_" + DEFAULT_SESSIONS;
			this.principals_key = key + "_" + DEFAULT_PRINCIPALS;
		}
	}

	public List<Object> getAllPrincipals() {
		return new ArrayList(this.getPrincipalsKeySet());
	}
	
	/**
	 * 获取当前登录用户所有未失效的登录信息
	 * */
	public List<SessionInformation> getAllSessions(Object principal, boolean includeExpiredSessions) {
		final Set<String> sessionsUsedByPrincipal = this.getPrincipals(principal.toString());

		if (sessionsUsedByPrincipal == null) {
			return Collections.emptyList();
		}

		List<SessionInformation> list = new ArrayList<>(
				sessionsUsedByPrincipal.size());
		
		//FIXME 此处可根据当前用户失效的session数，适当清空一下失效的session信息,目前保留5个失效缓存
		List<SessionInformation> expiredInfos = new ArrayList<SessionInformation>();//记录失效缓存
		for (String sessionId : sessionsUsedByPrincipal) {
			SessionInformation sessionInformation = this.getSessionInformation(sessionId);

			if (sessionInformation == null) {
				//删除冗余信息
				this.removePrincipalSessionId(sessionId, principal.toString());
				continue;
			}
			
			if (includeExpiredSessions || !sessionInformation.isExpired()) {
				list.add(sessionInformation);
			}
			//FIXME 此处清理冗余数据
			if(sessionInformation.isExpired() && 
					(new Date().getTime() - sessionInformation.getLastRequest().getTime()) > 60*60*1000) {//已经失效且最后请求时间超过1小时，清除
				this.removeSessionInfo(sessionId);
			}
		}
		return list;
		/*Set<String> sessionsUsedByPrincipal = this.getPrincipals(((UserDetails) principal).getUsername());
		if (sessionsUsedByPrincipal == null) {
			return Collections.emptyList();
		} else {
			List<SessionInformation> list = new ArrayList(sessionsUsedByPrincipal.size());
			Iterator var5 = sessionsUsedByPrincipal.iterator();

			while (true) {
				SessionInformation sessionInformation;
				do {
					do {
						if (!var5.hasNext()) {
							return list;
						}

						String sessionId = (String) var5.next();
						sessionInformation = this.getSessionInformation(sessionId);
					} while (sessionInformation == null);
				} while (!includeExpiredSessions && sessionInformation.isExpired());

				list.add(sessionInformation);
			}
		}*/
	}

	public SessionInformation getSessionInformation(String sessionId) {
		Assert.hasText(sessionId, "SessionId required as per interface contract");
		return (SessionInformation) this.getSessionInfo(sessionId);
	}
	
	/**
	 * session销毁事件监听
	 * */
	public void onApplicationEvent(SessionDestroyedEvent event) {
		String sessionId = event.getId();
		this.removeSessionInformation(sessionId);
	}
	
	/**
	 * 刷新最后登录时间
	 * */
	public void refreshLastRequest(String sessionId) {
		Assert.hasText(sessionId, "SessionId required as per interface contract");
		SessionInformation info = this.getSessionInformation(sessionId);
		if (info != null) {
			this.refreshLastRequest(info);
		}
	}

	public void registerNewSession(String sessionId, Object principal) {
		Assert.hasText(sessionId, "SessionId required as per interface contract");
		Assert.notNull(principal, "Principal required as per interface contract");
		if (this.logger.isDebugEnabled()) {
			this.logger.debug("Registering session " + sessionId + ", for principal " + principal);
		}

		if (this.getSessionInformation(sessionId) != null) {
			this.removeSessionInformation(sessionId);
		}

		this.addSessionInfo(sessionId, new SessionInformation(principal, sessionId, new Date()));

		// this.sessions_key.put(sessionId, new SessionInformation(principal, sessionId,
		// new Date()));
		Set<String> sessionsUsedByPrincipal = (Set) this.getPrincipals(principal.toString());
		if (sessionsUsedByPrincipal == null) {
			sessionsUsedByPrincipal = new CopyOnWriteArraySet();
			Set<String> prevSessionsUsedByPrincipal = (Set) this.putIfAbsentPrincipals(principal.toString(), sessionsUsedByPrincipal);
			if (prevSessionsUsedByPrincipal != null) {
				sessionsUsedByPrincipal = prevSessionsUsedByPrincipal;
			}
		}

		((Set) sessionsUsedByPrincipal).add(sessionId);
		this.putPrincipals(principal.toString(), sessionsUsedByPrincipal);
		if (this.logger.isTraceEnabled()) {
			this.logger.trace("Sessions used by '" + principal + "' : " + sessionsUsedByPrincipal);
		}

	}

	public void removeSessionInformation(String sessionId) {
		Assert.hasText(sessionId, "SessionId required as per interface contract");
		SessionInformation info = this.getSessionInformation(sessionId);
		if (info != null) {
			if (this.logger.isTraceEnabled()) {
				this.logger.debug("Removing session " + sessionId + " from set of registered sessions");
			}

			this.removeSessionInfo(sessionId);
			Set<String> sessionsUsedByPrincipal = (Set) this.getPrincipals(info.getPrincipal().toString());
			if (sessionsUsedByPrincipal != null) {
				if (this.logger.isDebugEnabled()) {
					this.logger.debug("Removing session " + sessionId + " from principal's set of registered sessions");
				}

//				sessionsUsedByPrincipal.remove(sessionId);
				this.removePrincipalSessionId(sessionId, info.getPrincipal().toString());
				sessionsUsedByPrincipal.remove(sessionId);
				if (sessionsUsedByPrincipal.isEmpty()) {
					if (this.logger.isDebugEnabled()) {
						this.logger.debug("Removing principal " + info.getPrincipal() + " from registry");
					}

					this.removePrincipal(((UserDetails) info.getPrincipal()).getUsername());
				}

				if (this.logger.isTraceEnabled()) {
					this.logger.trace("Sessions used by '" + info.getPrincipal() + "' : " + sessionsUsedByPrincipal);
				}

			}
		}
	}

	public void addSessionInfo(final String sessionId, final SessionInformation sessionInformation) {
		BoundHashOperations<String, String, SessionInformation> hashOperations = redisTemplate.boundHashOps(sessions_key);
		hashOperations.put(sessionId, sessionInformation);
	}

	public SessionInformation getSessionInfo(final String sessionId) {
		BoundHashOperations<String, String, SessionInformation> hashOperations = redisTemplate.boundHashOps(sessions_key);
		return hashOperations.get(sessionId);
	}

	public void removeSessionInfo(final String sessionId) {
		BoundHashOperations<String, String, SessionInformation> hashOperations = redisTemplate.boundHashOps(sessions_key);
		hashOperations.delete(sessionId);
	}

	public Set<String> putIfAbsentPrincipals(final String key, final Set<String> set) {
		BoundHashOperations<String, String, Set<String>> hashOperations = redisTemplate.boundHashOps(principals_key);
		hashOperations.putIfAbsent(key, set);
		return hashOperations.get(key);
	}

	public void putPrincipals(final String key, final Set<String> set) {
		BoundHashOperations<String, String, Set<String>> hashOperations = redisTemplate.boundHashOps(principals_key);
		hashOperations.put(key, set);
	}

	public Set<String> getPrincipals(final String key) {
		BoundHashOperations<String, String, Set<String>> hashOperations = redisTemplate.boundHashOps(principals_key);
		return hashOperations.get(key);
	}

	public Set<String> getPrincipalsKeySet() {
		BoundHashOperations<String, String, Set<String>> hashOperations = redisTemplate.boundHashOps(principals_key);
		return hashOperations.keys();
	}

	public void removePrincipal(final String key) {
		BoundHashOperations<String, String, Set<String>> hashOperations = redisTemplate.boundHashOps(principals_key);
		hashOperations.delete(key);
	}
	
	//删除Principal中的一个sessionId
	public void removePrincipalSessionId(final String sessionId,final String key) {
		BoundHashOperations<String, String, Set<String>> hashOperations = redisTemplate.boundHashOps(principals_key);
		Set<String> sessionsUsedByPrincipal = (Set) this.getPrincipals(key);
		sessionsUsedByPrincipal.remove(sessionId);
		this.putPrincipals(key, sessionsUsedByPrincipal);
	}
	
	//刷新最后登录时间
	public void refreshLastRequest(SessionInformation info) {
		info.refreshLastRequest();
		this.addSessionInfo(info.getSessionId(),info);
	}

}