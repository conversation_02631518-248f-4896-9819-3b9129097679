package com.boot.iAdmin.jwt.util;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.boot.iAdmin.redis.common.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;
import java.util.UUID;
/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/16:14:04:03
 **/
@Component
public class MathCaptchaGeneratorUtils {
    private static final int WIDTH = 160;
    private static final int HEIGHT = 47;
    private static final String CAPTCHA_KEY_PREFIX = "mathCaptcha:";
    private static final Long EXPIRE_TIME_SECONDS = 90L; // 验证码过期时间，单位为秒
    private static final Random RANDOM = new Random();
    @Autowired
    private RedisUtil redisUtil; // Redis连接配置，请根据实际情况调整

    // 生成随机的数学问题
    private  String generateMathQuestion() {
        Random random = new Random();
        int num1 = random.nextInt(5) + 1;
        int num2 = random.nextInt(5);
        char operator = "+-".charAt(random.nextInt(2));

        int answer;
        switch (operator) {
            case '+':
                answer = num1 + num2;
                break;
            case '-':
                num2 = RANDOM.nextInt(num1 + 1);
                answer = num1 - num2;
                break;
            case '*':
                answer = num1 * num2;
                break;
            case '/':
                num2 = RANDOM.nextInt(4) + 1; // 除数不能为0
                num1 = num2 * (RANDOM.nextInt(5 / num2) + 1); // 确保结果是整数
                answer = num1 / num2; // 强制转换为浮点数除法
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + operator);
        }
        StringBuilder question = new StringBuilder();
        question.append(num1).append(" ").append(operator).append(" ").append(num2);

//        String formattedAnswer = String.format("%.2f", answer);
        String formattedAnswer = String.valueOf(answer);
        return question.toString() + "=" + formattedAnswer;
    }

    public  void generateCaptchaImage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        ResponseEnvelope re = new ResponseEnvelope();
        // 创建 BufferedImage 对象
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();

        // 设置背景颜色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, WIDTH, HEIGHT);

        // 设置前景颜色
        g2d.setColor(Color.BLACK);
        Font font = new Font("Arial", Font.BOLD, 34);
        g2d.setFont(font);

        // 生成数学问题
        String mathQuestion = generateMathQuestion();
        String uuid = UUID.randomUUID().toString(); // 创建唯一标识符

        // 计算文本宽度并居中绘制
        FontRenderContext frc = g2d.getFontRenderContext();
        Rectangle2D textBounds = font.getStringBounds(mathQuestion.split("=")[0] + " = ", frc);
        double textWidth = textBounds.getWidth();
        double textHeight = textBounds.getHeight();

//        int x = (WIDTH - (int) textBounds.getWidth()) / 2;
//        int y = HEIGHT - ((HEIGHT - (int) textBounds.getHeight()) / 2); // 简化垂直居中计算

        // 水平居中：(image width - text width) / 2
        int x = (int) ((WIDTH - textWidth) / 2);

        // 垂直居中：(image height - text ascent) / 2 + font ascent
        // 注意：font ascent 是从基线到字符顶部的距离
        FontMetrics metrics = g2d.getFontMetrics(font);
        int y = (HEIGHT - metrics.getAscent() + metrics.getDescent()) / 2 + metrics.getAscent();

        // 绘制数学问题文本
        g2d.drawString(mathQuestion.split("=")[0]+ " = ", x, y);

        // 将答案存入Redis，并设置过期时间
        redisUtil.set(CAPTCHA_KEY_PREFIX + uuid, mathQuestion.split("=")[1],EXPIRE_TIME_SECONDS);

        // 添加干扰线和点（代码与之前相同）
        addDistortions(g2d);
        g2d.dispose();
        // 将UUID返回给前端用于后续验证
        response.setHeader("Captcha-Id", uuid);
//        // 输出图像到响应流
//        response.setContentType("image/png");
//        ImageIO.write(image, "png", response.getOutputStream());
        // 将 BufferedImage 转换为字节数组
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(image, "png", os);
        byte[] byteArr = os.toByteArray();
        response.setContentType("text/html;charset=UTF-8");
        // 将字节数组转换为 Base64 编码的字符串
        String base64Image = Base64.getEncoder().encodeToString(byteArr);
        re.setResult("data:image/png;base64,"+base64Image);
        response.getWriter().print(JsonUtil.toJSon(re));
    }
    private void addDistortions(Graphics2D g2d) {
        // 实现添加干扰元素的方法，例如画一些随机线条或点
        for (int i = 0; i < 5; i++) {
            g2d.setColor(new Color(RANDOM.nextInt(255), RANDOM.nextInt(255), RANDOM.nextInt(255)));
            g2d.drawLine(RANDOM.nextInt(WIDTH), RANDOM.nextInt(HEIGHT), RANDOM.nextInt(WIDTH), RANDOM.nextInt(HEIGHT));
        }
    }
    // 验证用户输入的答案是否正确
    public  boolean verifyCaptcha(String userInput, String captchaId) {
        if (captchaId == null || captchaId.isEmpty()) {
            return false;
        }
        Object storedAnswer = redisUtil.get(CAPTCHA_KEY_PREFIX + captchaId);
        if (storedAnswer == null || !userInput.trim().equals(storedAnswer.toString())) {
            return false;
        }
        // 验证成功后删除Redis中的答案
        redisUtil.remove(CAPTCHA_KEY_PREFIX + captchaId);
        return true;
    }

    private  Color getRandomColor() {
        Random random = new Random();
        return new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256));
    }
}
