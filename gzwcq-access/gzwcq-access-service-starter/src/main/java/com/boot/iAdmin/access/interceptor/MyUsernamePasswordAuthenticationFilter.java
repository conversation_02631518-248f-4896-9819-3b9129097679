package com.boot.iAdmin.access.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.boot.iAdmin.access.model.common.Constant;

public class MyUsernamePasswordAuthenticationFilter extends
		UsernamePasswordAuthenticationFilter {
	
	private static final Log logger = LogFactory.getLog(MyUsernamePasswordAuthenticationFilter.class);
	
	public static final String SPRING_SECURITY_FORM_CAPTCHA_KEY = "j_captcha";
	public static final String SESSION_GENERATED_CAPTCHA_KEY = Constant.SESSION_GENERATED_CAPTCHA_KEY;

	private String captchaParameter = SPRING_SECURITY_FORM_CAPTCHA_KEY;

	@Override
	public Authentication attemptAuthentication(HttpServletRequest request,
			HttpServletResponse response) throws AuthenticationException {
		
		logger.info(String.format("@用户[%s]开始登录...", obtainUsername(request)));
		/**
		 * 扩展验证码
		 * */
//		String genCode = this.obtainGeneratedCaptcha(request);
//		String inputCode = this.obtainCaptcha(request);
		/*if (genCode == null)
			throw new CaptchaException(
					this.messages
							.getMessage("LoginAuthentication.captchaInvalid"));
		if (!genCode.equalsIgnoreCase(inputCode)) {
			throw new CaptchaException(
					this.messages
							.getMessage("LoginAuthentication.captchaNotEquals"));
		}*/

		return super.attemptAuthentication(request, response);
	}

	protected String obtainCaptcha(HttpServletRequest request) {
		return request.getParameter(this.captchaParameter);
	}

	protected String obtainGeneratedCaptcha(HttpServletRequest request) {
		return (String) request.getSession().getAttribute(
				SESSION_GENERATED_CAPTCHA_KEY);
	}
	
	/**
	 * 重写支持单点获取
	 * */
	@Override
	protected String obtainUsername(HttpServletRequest request) {
		/*if(StringUtils.isNotBlank(super.obtainUsername(request))){
			return super.obtainUsername(request);
		}else if(StringUtils.isNoneBlank(AssertionHolder.getAssertion().getPrincipal().getName())){
			return AssertionHolder.getAssertion().getPrincipal().getName();
		}else{
			return super.obtainUsername(request);
		}*/
		return super.obtainUsername(request);
	}
	
	
}
