package com.boot.iAdmin.access.model.loginLog;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： sys_login_log <br/>
 *         描述：sys_login_log <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginLog implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="流水号")
	protected Long id;// 流水号
  	@ApiParam(value="组织")
	protected String orgId;// 组织
  	@ApiParam(value="组织名称")
	protected String orgName;// 组织名称
  	@ApiParam(value="用户名")
	protected String username;// 用户名
  	@ApiParam(value="用户姓名")
	protected String name;// 用户姓名
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="登录时间")
	protected Date loginTime;// 登录时间
  	@ApiParam(value="登录IP")
	protected String ip;// 登录IP
  	@ApiParam(value="登录类型")
	protected String type;// 登录类型

	public LoginLog() {
		super();
	}
	
  	public LoginLog(Long id) {
  		super();
  		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Date getLoginTime() {
		return loginTime;
	}
	public void setLoginTime(Date loginTime) {
		this.loginTime = loginTime;
	}
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
}
