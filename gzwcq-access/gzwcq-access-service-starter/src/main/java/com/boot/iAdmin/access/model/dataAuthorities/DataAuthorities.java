package com.boot.iAdmin.access.model.dataAuthorities;

import java.io.Serializable;

/**
 * <AUTHOR> <br/>
 *         表名： sys_data_authorities <br/>
 *         描述：sys_data_authorities <br/>
 */
public class DataAuthorities implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// 需要手动添加非默认的serialVersionUID
	protected Long data_id;// data_id
	protected String data_name;// 权限名称
	protected String data_desc;// 权限描述
	

	public DataAuthorities() {
		super();
	}
	
	public DataAuthorities(long data_id) {
		super();
		this.data_id = data_id;
	}
	
	public Long getData_id() {
		return data_id;
	}
	public void setData_id(Long data_id) {
		this.data_id = data_id;
	}
	public String getData_name() {
		return data_name;
	}
	public void setData_name(String data_name) {
		this.data_name = data_name;
	}
	public String getData_desc() {
		return data_desc;
	}
	public void setData_desc(String data_desc) {
		this.data_desc = data_desc;
	}
}
