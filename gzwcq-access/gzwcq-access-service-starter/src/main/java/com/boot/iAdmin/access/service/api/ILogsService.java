package com.boot.iAdmin.access.service.api;

import java.util.Collection;
import java.util.Map;

import com.boot.IAdmin.common.aop.IgnoreLog;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.sys_logs.Logs;
import com.boot.iAdmin.access.model.sys_logs.LogsVo;
@IgnoreLog
public interface ILogsService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void save(Logs logs);

	/**
	 * 按对象中的主键进行删除，
	 */
	void delete(Map<String, Object> map);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Logs logs);
	/**
	 *根据页面查询条件查询数据并分页
	 */
	BootstrapTableModel<LogsVo> queryLogsByPage(BootstrapTableModel<LogsVo> bootModel);
	/**
	 *分页查询总条数
	 */
	long queryTotalLogss(BootstrapTableModel<LogsVo> bootModel);
	/**
	 *通过ID查询数据
	 */
	LogsVo queryLogsById(Logs logs);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	Collection<LogsVo> selectForList(Logs logs);
	
}