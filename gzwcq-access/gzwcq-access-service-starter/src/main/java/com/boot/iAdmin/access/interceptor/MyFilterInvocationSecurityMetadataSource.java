package com.boot.iAdmin.access.interceptor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;

import com.boot.iAdmin.access.service.api.IResourceService;

/**
 * 自定义spring security 安全数据元
 * 
 * <AUTHOR>
 * @date 2016年5月11日 20:30:39
 * */
public class MyFilterInvocationSecurityMetadataSource  extends AbstractSecurityMetadataSource {
	
	private final static String SEPARATOR = ";";//资源匹配规则分隔符

	protected final Log logger = LogFactory.getLog(getClass());

//	private final static List<ConfigAttribute> NULL_CONFIG_ATTRIBUTE = new ArrayList<ConfigAttribute>();
	
	private Map<RequestMatcher, Collection<ConfigAttribute>> requestMap;// 权限集合
	
	@Resource
	private IResourceService resourceService;//资源服务

	public MyFilterInvocationSecurityMetadataSource() {
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.security.access.SecurityMetadataSource#getAttributes
	 * (java.lang.Object)
	 * 
	 * <P> update by ruanhj 修改spring security 权限匹配策略，获取到匹配该资源的所有权限
	 * <P> update by ruanhj 2017年6月30日 16:49:21 扩展资源规则匹配，一个资源可增加多个规则，只要一个规则匹配成功，该资源就匹配成功，用;分隔符隔开
	 */
	public Collection<ConfigAttribute> getRealAttributes(Object object) throws IllegalArgumentException {
		final HttpServletRequest request = ((FilterInvocation) object).getRequest();
		
		Collection<ConfigAttribute> attrs = new ArrayList<ConfigAttribute>();
//		attrs.clear();
		for (Map.Entry<RequestMatcher, Collection<ConfigAttribute>> entry : requestMap.entrySet()) {
			AntPathRequestMatcher matcher = (AntPathRequestMatcher)entry.getKey();
			String[] patterns =  matcher.getPattern().split(SEPARATOR);
			for(String pattern : patterns){
				if(new AntPathRequestMatcher(pattern).matches(request)){
					for(ConfigAttribute val : entry.getValue()){
						if(!attrs.contains(val)) attrs.add(val);
					}
					break;
				}
			}
			/*if (entry.getKey().matches(request)) {
				for(ConfigAttribute val : entry.getValue()){
					if(!attrs.contains(val)) attrs.add(val);
				}
//				break;
			}*/
		}
		logger.info("@URL资源：" + request.getRequestURI() + " -> " + attrs);
		if (attrs == null || attrs.size() < 1) {
			logger.warn(String.format("@当前资源[%s]没有关联到任何权限", request.getRequestURI()));
		}
		return attrs;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.springframework.security.access.SecurityMetadataSource#
	 * getAllConfigAttributes()
	 */
	public Collection<ConfigAttribute> getAllConfigAttributes() {
		Set<ConfigAttribute> allAttributes = new HashSet<ConfigAttribute>();

		for (Map.Entry<RequestMatcher, Collection<ConfigAttribute>> entry : requestMap.entrySet()) {
			allAttributes.addAll(entry.getValue());
		}

		return allAttributes;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.security.access.SecurityMetadataSource#supports(java
	 * .lang.Class)
	 */
	public boolean supports(Class<?> clazz) {
		return FilterInvocation.class.isAssignableFrom(clazz);
	}

	/**
	 * 加载资源
	 * */
	private Map<String, String> loadResuorce() {
		Map<String, String> map = new LinkedHashMap<String, String>();

		List<Map<String, String>> list = resourceService.getURLResourceMapping();
		Iterator<Map<String, String>> it = list.iterator();
		while (it.hasNext()) {
			Map<String, String> rs = it.next();
			String resourcePath = rs.get("resourcePattern");
			String authorityMark = rs.get("authorityMark");

			if (map.containsKey(resourcePath)) {
				String mark = map.get(resourcePath);
				map.put(resourcePath, mark + "," + authorityMark);
			} else {
				map.put(resourcePath, authorityMark);
			}
		}
		return map;
	}

	/**
	 * 构造请求资源Map
	 *<P> key--请求资源 value--权限 
	 * */
	protected Map<RequestMatcher, Collection<ConfigAttribute>> bindRequestMap() {
		Map<RequestMatcher, Collection<ConfigAttribute>> map = new LinkedHashMap<RequestMatcher, Collection<ConfigAttribute>>();

		Map<String, String> resMap = this.loadResuorce();
		for (Map.Entry<String, String> entry : resMap.entrySet()) {
			String key = entry.getKey();//resources --资源
			Collection<ConfigAttribute> atts = new ArrayList<ConfigAttribute>();
			atts = SecurityConfig.createListFromCommaDelimitedString(entry.getValue());//权限
			map.put(new AntPathRequestMatcher(key), atts);
		}

		return map;
	}
	
	/**
	 * 初始化资源
	 * <P>构造以后执行
	 * */
	public void afterPropertiesSet() throws Exception {
		this.requestMap = this.bindRequestMap();
		logger.info("资源权限列表" + this.requestMap);
	}
	
	/**
	 *刷新资源
	 * */
	public void refreshResuorceMap() {
		this.requestMap = this.bindRequestMap();
		super.refreshResuorceMap();
	}

}
