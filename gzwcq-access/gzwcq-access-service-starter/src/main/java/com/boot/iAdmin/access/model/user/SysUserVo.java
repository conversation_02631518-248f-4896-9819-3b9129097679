package com.boot.iAdmin.access.model.user;

import java.util.Collection;

import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.role.Role;

/**
 * 用户扩展类
 * 
 * <AUTHOR>
 * @date 2016年8月29日 15:52:54
 * */
public class SysUserVo extends SysUser {

	private static final long serialVersionUID = -5682510566295714903L;

	private int limit;
	private int pageNumber;
	private String orgName;
	private String typeStr;//账号类型
	private String auditLevelStr;//审核层级
	private String orgLevel;//企业级次
	private String businessType;
	private Boolean isMySelf; //是否我自己账号

	public Boolean getIsMySelf() {
		return isMySelf;
	}

	public void setIsMySelf(Boolean isMySelf) {
		this.isMySelf = isMySelf;
	}

	private SysOrganization organization;//用户所属组织

	@Override
	public String getOrgName() {
		return orgName;
	}

	@Override
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	private String enabled_txt;// 状态显示值
	
	private Collection<Role> roles;//用户所拥有的角色集合
	
	private String organization_name;// 组织名称


	private String organization_desc;// 组织描述
	
	private Long group_id;//用户组

	public String getOrganization_name() {
		return organization_name;
	}

	public void setOrganization_name(String organization_name) {
		this.organization_name = organization_name;
	}

	public String getEnabled_txt() {
		return enabled_txt;
	}
	
	/*@Deprecated
	@Override
	protected void setEnabled_txt(boolean enabled) {
		this.enabled_txt = enabled ? "有效" : "失效";
	}*/
	
	public void setEnabled_txt(String enabled_txt) {
		this.enabled_txt = enabled_txt;
	}

	public Collection<Role> getRoles() {
		return roles;
	}

	public void setRoles(Collection<Role> roles) {
		this.roles = roles;
	}
	
	/**
	 * 为属性roles赋值，并返回当前对象本身
	 * */
	public SysUser addRoles(Collection<Role> roles){
		this.setRoles(roles);
		return this;
	}

	public String getOrganization_desc() {
		return organization_desc;
	}

	public void setOrganization_desc(String organization_desc) {
		this.organization_desc = organization_desc;
	}

	public Long getGroup_id() {
		return group_id;
	}

	public void setGroup_id(Long group_id) {
		this.group_id = group_id;
	}

	//公共查询条件 工号  姓名  手机
	private String commonQuery;

	public String getCommonQuery() {
		return commonQuery;
	}

	public void setCommonQuery(String commonQuery) {
		this.commonQuery = commonQuery;
	}

	public SysOrganization getOrganization() {
		return organization;
	}

	public void setOrganization(SysOrganization organization) {
		this.organization = organization;
	}

	public String getTypeStr() {
		return typeStr;
	}

	public void setTypeStr(String typeStr) {
		this.typeStr = typeStr;
	}

	public String getAuditLevelStr() {
		return auditLevelStr;
	}

	public void setAuditLevelStr(String auditLevelStr) {
		this.auditLevelStr = auditLevelStr;
	}

	public String getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(String orgLevel) {
		this.orgLevel = orgLevel;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
}
