package com.boot.iAdmin.access.model.roleAndAuth;

/**
 * 角色权限关联关系
 * */
public class RoleAndAuth {
	
	private Long id;
	private Long authority_id;
	private Long role_id;
	
	public RoleAndAuth(){}
	
	public RoleAndAuth(Long id, Long authority_id, Long role_id) {
		super();
		this.id = id;
		this.authority_id = authority_id;
		this.role_id = role_id;
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getAuthority_id() {
		return authority_id;
	}
	public void setAuthority_id(Long authority_id) {
		this.authority_id = authority_id;
	}
	public Long getRole_id() {
		return role_id;
	}
	public void setRole_id(Long role_id) {
		this.role_id = role_id;
	}
	
	
}
