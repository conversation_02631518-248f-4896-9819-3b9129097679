package com.boot.iAdmin.access.service.impl;

import java.util.Collection;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.mapper.ILogsMapper;
import com.boot.iAdmin.access.model.sys_logs.Logs;
import com.boot.iAdmin.access.model.sys_logs.LogsVo;
import com.boot.iAdmin.access.service.api.ILogsService;

@Service
public class LogsServiceImpl implements ILogsService {
	
	@Autowired
	private ILogsMapper logsMapper;

	public void save(Logs logs){
		logsMapper.save(logs);
	}

	public void delete(Map<String,Object> map){
		logsMapper.delete(map);
	}
	
	public void updateIgnoreNull(Logs logs){
		logsMapper.updateIgnoreNull(logs);
	}	
	
	public BootstrapTableModel<LogsVo> queryLogsByPage(BootstrapTableModel<LogsVo> bootModel) {
		return bootModel.addRows(logsMapper.queryLogsByPage(bootModel)).addTotals(logsMapper.queryTotalLogss(bootModel));
	}

	public LogsVo queryLogsById(Logs Logs) {
		return logsMapper.queryLogsById(Logs);
	}
	
	public long queryTotalLogss(BootstrapTableModel<LogsVo> bootModel) {
		return logsMapper.queryTotalLogss(bootModel);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public Collection<LogsVo> selectForList(Logs logs){
		return logsMapper.selectForList(logs);
	}
}
