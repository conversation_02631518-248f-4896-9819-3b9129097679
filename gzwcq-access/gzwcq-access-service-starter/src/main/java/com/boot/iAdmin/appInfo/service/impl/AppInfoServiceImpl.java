package com.boot.iAdmin.appInfo.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.boot.IAdmin.common.utils.Constants;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.appInfo.entity.AppInfo;
import com.boot.iAdmin.appInfo.mapper.IAppInfoMapper;
import com.boot.iAdmin.appInfo.service.api.IAppInfoService;

@Service
public class AppInfoServiceImpl implements IAppInfoService {
	
	@Autowired
	private IAppInfoMapper appInfoMapper;
	
  	@Override
	public void insert(AppInfo appInfo){
		appInfo.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		appInfo.setCreateTime(new Date());//创建时间
		appInfo.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		appInfo.setLastUpdateTime(new Date());//更新时间
		appInfo.setIsdeleted(Constants.NO_DELETED);//设置默认值2，未删除
		appInfoMapper.insert(appInfo);
	}
	
    @Override
	public void deleteByPrimaryKeys(Map<String,Object> map){
		appInfoMapper.logicDeleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
	public void deleteByPrimaryKey(Map<String, Object> map) {
		appInfoMapper.logicDeleteByPrimaryKey(map);
	}
	
  	@Override
	public void updateIgnoreNull(AppInfo appInfo){
		appInfo.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		appInfo.setLastUpdateTime(new Date());//更新时间
		appInfoMapper.updateIgnoreNull(appInfo);
	}
	
	/**
	* 更新
	*/
  	@Override
	public void update(AppInfo appInfo){
		appInfo.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		appInfo.setLastUpdateTime(new Date());//更新时间
		appInfoMapper.update(appInfo);
	}
	
	@Override
	public AppInfo selectAppInfoByPrimaryKey(AppInfo AppInfo) {
		return appInfoMapper.selectAppInfoByPrimaryKey(AppInfo);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@Override
	public List<AppInfo> selectForList(AppInfo appInfo){
		return appInfoMapper.selectForList(appInfo);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(AppInfo appInfo) {
		return appInfoMapper.selectForUnique(appInfo).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
	public void saveOne(AppInfo appInfo) {
		if(appInfo.getId() == null) {
			this.insert(appInfo);
		}else {
			this.updateIgnoreNull(appInfo);
		}
	}
	
	/**
	 * 保存多个用户
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void multipleSaveAndEdit(AppInfo[] objs) {
		for(AppInfo appInfo : objs) {
			this.saveOne(appInfo);
		}
	}

	@Override
	public AppInfo selectAppInfoByAppKey(String appId) {
		return appInfoMapper.selectAppInfoByAppKey(appId);
	}
}
