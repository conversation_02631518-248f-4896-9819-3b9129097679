package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.groups.Groups;
import com.boot.iAdmin.access.model.groups.GroupsVo;

@Repository
public interface IGroupsMapper {
	
	/*保存对象*/
	void save(Groups groups);
	
	/*根据表主键删除对象,采用foreach遍历*/
	void delete(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Groups groups);
	
	/*分页查询对象*/
	Collection<GroupsVo> queryGroupsByPage(BootstrapTableModel<GroupsVo> bootModel);
	
	/*数据总量查询*/
	long queryTotalGroupss(BootstrapTableModel<GroupsVo> bootModel);
	
	/*根据主键查询对象*/
	Groups queryGroupsById(Groups groups);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Groups> selectForList(Groups groups);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Groups> selectForUnique(Groups groups);
	
	int queryByMap(Map<String, Object> map);
	
	/**根据公司获取用户组*/
	List<Groups> loadUserGroupByCompany(Map<String, Object> paramsMap);

	List<Groups> queryUsersGroupByUser(Long technician_id);
}