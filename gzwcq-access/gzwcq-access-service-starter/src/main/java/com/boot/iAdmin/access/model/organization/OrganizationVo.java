package com.boot.iAdmin.access.model.organization;

import java.util.List;

public class OrganizationVo extends SysOrganization {
	
	//父节点名称
	private String parent_node;
	//为了物料分配组织功能
	private Long except_MaterialMainOrg;
	
	private String totalName;//全称

	private String xyCode;//信用代码

	private String code_name;//组织代码+名称

	private Integer existJbxx;//是否登记过.用于子级数量计算 1是0否

	private long childNums;//直接子级数量

	private long allChildNums;//所有子级数量

	private List<OrganizationVo> organizationVoList;//包含的子节点


	public String getXyCode() {
		return organization_code.substring(organization_code.lastIndexOf("_")+1);
	}

	public void setXyCode(String xyCode) {
		this.xyCode = organization_code.substring(organization_code.lastIndexOf("_")+1);
	}

	public String getCode_name() {
		return organization_code+" "+organization_name;
	}

	public void setCode_name(String code_name) {
		this.code_name = organization_code+" "+organization_name;
	}

	public Long getExcept_MaterialMainOrg() {
		return except_MaterialMainOrg;
	}

	public void setExcept_MaterialMainOrg(Long except_MaterialMainOrg) {
		this.except_MaterialMainOrg = except_MaterialMainOrg;
	}

	public String getParent_node() {
		return parent_node;
	}

	public void setParent_node(String parent_node) {
		this.parent_node = parent_node;
	}

	public String getTotalName() {
		return totalName;
	}

	public void setTotalName(String totalName) {
		this.totalName = totalName;
	}

	public List<OrganizationVo> getOrganizationVoList() {
		return organizationVoList;
	}

	public void setOrganizationVoList(List<OrganizationVo> organizationVoList) {
		this.organizationVoList = organizationVoList;
	}

	public Integer getExistJbxx() {
		return existJbxx;
	}

	public void setExistJbxx(Integer existJbxx) {
		this.existJbxx = existJbxx;
	}

	public long getChildNums() {
		return childNums;
	}

	public void setChildNums(long childNums) {
		this.childNums = childNums;
	}

	public long getAllChildNums() {
		return allChildNums;
	}

	public void setAllChildNums(long allChildNums) {
		this.allChildNums = allChildNums;
	}
}
