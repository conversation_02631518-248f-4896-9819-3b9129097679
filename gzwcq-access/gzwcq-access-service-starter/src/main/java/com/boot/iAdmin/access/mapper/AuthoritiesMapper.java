package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.authority.AuthVo;
import com.boot.iAdmin.access.model.authority.CustomAuthorities;
import com.boot.iAdmin.access.model.authority.SysAuthorities;

/**
 * 权限DAO
 * */
public interface AuthoritiesMapper {
	
	/**
	 * 获取权限集合
	 * */
	List<CustomAuthorities> loadAuthorities();
	
	/**
	 * 查询
	 * */
	Collection<SysAuthorities> getAuth(SysAuthorities auth);
	
	/**
	 * 根据角色ID查询关联的权限
	 * */
	Collection<SysAuthorities> getAuthByRoleId(Long role_id);
	
	/**分页查询权限*/
	Collection<AuthVo> loadAuthByPage(BootstrapTableModel<AuthVo> pageModel);
	
	/**查询总条数*/
	long loadTotalAuths(BootstrapTableModel<AuthVo> pageModel);
	
	/**
	 * 更新权限信息
	 * <P>忽略空字段
	 * */
	void updateIgnoreNull(SysAuthorities auth);
	
	/**
	 * 删除权限
	 * */
	void deleteAuthByIds(Map<String,Object> map);
	
	/**新增权限*/
	void insertAuth(SysAuthorities auth);
	
	/**
	 * 校验权限名称
	 * @param auth
	 * @return
	 */
	List<String> validateAuthorityName(SysAuthorities auth);
}
