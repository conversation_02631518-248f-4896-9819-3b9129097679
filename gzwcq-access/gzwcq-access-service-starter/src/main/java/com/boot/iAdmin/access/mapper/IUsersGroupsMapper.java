package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.userAndGroup.UsersGroups;
import com.boot.iAdmin.access.model.userAndGroup.UsersGroupsVo;

@Repository
public interface IUsersGroupsMapper {
	
	/*保存对象*/
	void save(UsersGroups usersGroups);
	
	/*根据表主键删除对象,采用foreach遍历*/
	void delete(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(UsersGroups usersGroups);
	
	/*分页查询对象*/
	Collection<UsersGroupsVo> queryUsersGroupsByPage(BootstrapTableModel<UsersGroupsVo> bootModel);
	
	/*数据总量查询*/
	long queryTotalUsersGroupss(BootstrapTableModel<UsersGroupsVo> bootModel);
	
	/*根据主键查询对象*/
	UsersGroups queryUsersGroupsById(UsersGroups usersGroups);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<UsersGroups> selectForList(UsersGroups usersGroups);
	
	/**
	 * 数据唯一性验证
	 * */
	List<UsersGroups> selectForUnique(UsersGroups usersGroups);

	void deleteUserGroupByUserAndCompany(UsersGroups ug);
	
}