package com.boot.iAdmin.access.interceptor.session;

import java.io.IOException;

import org.apache.catalina.connector.Response;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.util.Assert;

import com.boot.iAdmin.access.common.ControllerTools;
import com.boot.iAdmin.access.model.common.Constant;
import com.boot.iAdmin.access.model.user.SysUser;

/**
 * 自定义session因被挤失效处理器
 * <P>支持Ajax
 * <AUTHOR>
 * @date 2018年12月7日 11:19:16
 * */
public class RedirectWithAjaxSessionInformationExpiredStrategy implements SessionInformationExpiredStrategy{

	private final Log logger = LogFactory.getLog(getClass());
	private final String destinationUrl;
	private final RedirectStrategy redirectStrategy;

	public RedirectWithAjaxSessionInformationExpiredStrategy(String invalidSessionUrl) {
		this(invalidSessionUrl, new DefaultRedirectStrategy());
	}

	public RedirectWithAjaxSessionInformationExpiredStrategy(String invalidSessionUrl, RedirectStrategy redirectStrategy) {
		Assert.isTrue(UrlUtils.isValidRedirectUrl(invalidSessionUrl),
				"url must start with '/' or with 'http(s)'");
		this.destinationUrl=invalidSessionUrl;
		this.redirectStrategy=redirectStrategy;
	}

	public void onExpiredSessionDetected(SessionInformationExpiredEvent event) throws IOException {
		logger.debug("Redirecting to '" + destinationUrl + "'");
		logger.info(String.format("===============用户[%s]认证信息已经过期，将强制登出并清除session[%s]==============", ((SysUser)event.getSessionInformation().getPrincipal()).getUsername(),event.getSessionInformation().getSessionId()));
		boolean isAjax = ControllerTools.isAjaxRequest(event.getRequest());
		if (isAjax) {
			ControllerTools.print(event.getResponse(), Constant.EXPIRED,Response.SC_EXPECTATION_FAILED);
		}else{
			redirectStrategy.sendRedirect(event.getRequest(), event.getResponse(), destinationUrl);
		}
	}

}
