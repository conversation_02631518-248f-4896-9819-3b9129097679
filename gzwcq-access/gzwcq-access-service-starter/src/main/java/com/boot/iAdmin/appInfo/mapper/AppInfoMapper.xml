<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.iAdmin.appInfo.mapper.IAppInfoMapper">

	<resultMap type="com.boot.iAdmin.appInfo.entity.AppInfo" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="app_name" property="appName"/>
		<result column="app_id" property="appId"/>
		<result column="token" property="token"/>
		<result column="remark" property="remark"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="isdeleted" property="isdeleted"/>
		<result column="org_id" property="orgId"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.boot.iAdmin.appInfo.entity.AppInfoVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		app_name, 
		app_id, 
		token, 
		remark, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time, 
		isdeleted,
		org_id
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.app_name, 
		t.app_id, 
		t.token, 
		t.remark, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time, 
		t.isdeleted
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{appName}, 
		#{appId}, 
		#{token}, 
		#{remark}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}, 
		#{isdeleted}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="appName != null and appName != ''">
			and t.app_name = #{appName}
		</if>
		<if test="appId != null and appId != ''">
			and t.app_id = #{appId}
		</if>
		<if test="token != null and token != ''">
			and t.token = #{token}
		</if>
		<if test="remark != null and remark != ''">
			and t.remark = #{remark}
		</if>
		<if test="createUser != null">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="isdeleted != null and isdeleted != ''">
			and t.isdeleted = #{isdeleted}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.boot.iAdmin.appInfo.entity.AppInfo" useGeneratedKeys="true" keyProperty="id">
		<!-- <selectKey keyProperty="id" resultType="Long" order="AFTER">
			select last_insert_id()
		</selectKey> -->
      	<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into sys_app_info (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update sys_app_info set isDeleted = 'Y' where
		id in
		<foreach collection="appInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update sys_app_info set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from sys_app_info  where
		id in
		<foreach collection="appInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.util.Map">
		delete from sys_app_info  where id = #{id}
	</delete>
	
	<select id="selectAppInfoByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from sys_app_info
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update sys_app_info
		<set>
			<if test="appName != null">
				app_name=#{appName},
			</if>
			<if test="appId != null">
				app_id=#{appId},
			</if>
			<if test="token != null">
				token=#{token},
			</if>
			<if test="remark != null">
				remark=#{remark},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime},
			</if>
			<if test="isdeleted != null">
				isdeleted=#{isdeleted}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update sys_app_info
		<set>
			app_name=#{appName},
			app_id=#{appId},
			token=#{token},
			remark=#{remark},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime},
			isdeleted=#{isdeleted}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.boot.iAdmin.appInfo.entity.AppInfo" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			sys_app_info t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.boot.iAdmin.appInfo.entity.AppInfo" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from sys_app_info t
		where t.id != #{id}
			<if test="appName != null and appName != ''">
				and t.app_name = #{appName}
			</if>
			<if test="appId != null and appId != ''">
				and t.app_id = #{appId}
			</if>
			<if test="token != null and token != ''">
				and t.token = #{token}
			</if>
			<if test="remark != null and remark != ''">
				and t.remark = #{remark}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
			<if test="isdeleted != null and isdeleted != ''">
				and t.isdeleted = #{isdeleted}
			</if>
	</select>
	
	<select id="selectAppInfoByAppKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from sys_app_info
		where app_id=#{appId}
		and isDeleted = 'N'
	</select>

</mapper>