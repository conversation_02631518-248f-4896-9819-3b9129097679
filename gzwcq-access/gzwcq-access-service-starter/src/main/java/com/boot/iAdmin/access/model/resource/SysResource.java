package com.boot.iAdmin.access.model.resource;

/**
 * 资源
 * */
public class SysResource {

	protected Long resource_id;// 资源ID
	protected String resource_type;// 资源类型
	protected String resource_name;// 资源名
	protected String resource_desc;// 资源描述
	protected String resource_path;// 资源路径
	protected String resource_pattern;// 资源匹配规则
	protected Integer priority;// 优先级
	protected Integer enable;// 是否有效
	protected Integer issys;// 是否系统资源
	protected Long module_id;// 菜单ID
	protected Long parent_id;//父节点ID
	
	public SysResource(){}

	public SysResource(Long resource_id) {
		super();
		this.resource_id = resource_id;
	}

	public Long getResource_id() {
		return resource_id;
	}

	public void setResource_id(Long resource_id) {
		this.resource_id = resource_id;
	}

	public String getResource_type() {
		return resource_type;
	}

	public void setResource_type(String resource_type) {
		this.resource_type = resource_type;
	}

	public String getResource_name() {
		return resource_name;
	}

	public void setResource_name(String resource_name) {
		this.resource_name = resource_name;
	}

	public String getResource_desc() {
		return resource_desc;
	}

	public void setResource_desc(String resource_desc) {
		this.resource_desc = resource_desc;
	}

	public String getResource_path() {
		return resource_path;
	}

	public void setResource_path(String resource_path) {
		this.resource_path = resource_path;
	}

	public String getResource_pattern() {
		return resource_pattern;
	}

	public void setResource_pattern(String resource_pattern) {
		this.resource_pattern = resource_pattern;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public Integer getEnable() {
		return enable;
	}

	public void setEnable(Integer enable) {
		this.enable = enable;
	}

	public Integer getIssys() {
		return issys;
	}

	public void setIssys(Integer issys) {
		this.issys = issys;
	}

	public Long getModule_id() {
		return module_id;
	}

	public void setModule_id(Long module_id) {
		this.module_id = module_id;
	}

	public Long getParent_id() {
		return parent_id;
	}

	public void setParent_id(Long parent_id) {
		this.parent_id = parent_id;
	}

}
