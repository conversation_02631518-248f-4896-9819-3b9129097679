package com.boot.iAdmin.access.interceptor;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;

/**
 * 抽象安全数据元
 * */
public abstract class AbstractSecurityMetadataSource implements FilterInvocationSecurityMetadataSource,InitializingBean{
	
	protected final Log logger = LogFactory.getLog(getClass());
	//http地址与权限集合缓存
	protected final Map<String,Collection<ConfigAttribute>> uriToAuthsCacheMap = new ConcurrentHashMap<String,Collection<ConfigAttribute>>();
	@Override
	public Collection<ConfigAttribute> getAttributes(Object object) throws IllegalArgumentException {
		final HttpServletRequest request = ((FilterInvocation) object).getRequest();
		//增加缓存，如果该url已经匹配过权限集合则直接返回
		if(uriToAuthsCacheMap.containsKey(request.getRequestURI())) {
			logger.info("从缓存获取资源：@URL资源：" + request.getRequestURI() + " -> " + uriToAuthsCacheMap.get(request.getRequestURI()));
			return uriToAuthsCacheMap.get(request.getRequestURI());
		}
		//真正获取，有子类实现
		Collection<ConfigAttribute> attrs = this.getRealAttributes(object);
		//添加到缓存
		uriToAuthsCacheMap.put(request.getRequestURI(),attrs);
		return attrs;
	}
	
	//真正获取，有子类实现
	protected abstract Collection<ConfigAttribute> getRealAttributes(Object object);

	@Override
	public Collection<ConfigAttribute> getAllConfigAttributes() {
		return null;
	}

	@Override
	public boolean supports(Class<?> clazz) {
		return false;
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		
	}
	
	/**
	 * 抽象方法子类必须实现
	 * */
	public void refreshResuorceMap() {
		//清空http地址到权限集合的缓存
		this.uriToAuthsCacheMap.clear();
	}
}
