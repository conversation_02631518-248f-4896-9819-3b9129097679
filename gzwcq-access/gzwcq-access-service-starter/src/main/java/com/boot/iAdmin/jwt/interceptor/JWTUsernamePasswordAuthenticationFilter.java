package com.boot.iAdmin.jwt.interceptor;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.crypto.SecureUtil;
import com.boot.iAdmin.access.service.api.IUserManageService;
import com.boot.iAdmin.jwt.api.JWTApi;
import com.boot.iAdmin.jwt.util.MathCaptchaGeneratorUtils;
import com.boot.iAdmin.jwt.util.RSAUtils;
import com.boot.iAdmin.redis.common.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.boot.IAdmin.common.utils.SpringContextUtil;
import com.boot.iAdmin.access.interceptor.MyAccountStatusUserDetailsChecker;
import com.boot.iAdmin.access.interceptor.MyAuthenticationProvider;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.ILoginLogService;
import com.boot.iAdmin.jwt.common.Constant;
import com.boot.iAdmin.jwt.util.JWTUtils;

/**
 * JWT身份认证
 * */
public class JWTUsernamePasswordAuthenticationFilter implements Filter{
	
	private Log logger = LogFactory.getLog(this.getClass());

	private final static String USER_NAME = "username";// 用户名

	private final static String PASS_WORD = "password";// 密码

	public final static String CAPTCHA_CODE = "captchaCode"; //验证码

	public final static  String CAPTCHA_ID = "Captcha-Id";//验证码照片标识
	private UserDetailsService userDetailsService;
	private MathCaptchaGeneratorUtils generatorUtils;
	private MyAuthenticationProvider authenticationProvider;
	private RedisUtil redisUtil;
	private AccountStatusUserDetailsChecker preAuthenticationChecks;
	private IUserManageService userManageService;
	
	public JWTUsernamePasswordAuthenticationFilter(UserDetailsService userDetailsService, MyAuthenticationProvider authenticationProvider,AccountStatusUserDetailsChecker preAuthenticationChecks,MathCaptchaGeneratorUtils generatorUtils,RedisUtil redisUtil,IUserManageService userManageService) {
		super();
		this.userDetailsService = userDetailsService;
		this.authenticationProvider = authenticationProvider;
		this.preAuthenticationChecks = preAuthenticationChecks;
		this.generatorUtils = generatorUtils;
		this.redisUtil = redisUtil;
		this.userManageService = userManageService;
	}

	private static final String LOCK_NUMBER = "5";

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			response.setContentType("text/html;charset=UTF-8");
			String userName = request.getParameter(USER_NAME);
			String pwd = request.getParameter(PASS_WORD);
			String captchaCode = request.getParameter(CAPTCHA_CODE);
			String captchaId = null;
			if (request instanceof HttpServletRequest) {
				HttpServletRequest httpRequest = (HttpServletRequest) request;
				captchaId = httpRequest.getHeader(CAPTCHA_ID);
			}
			if(StringUtils.isBlank(userName)) throw new Exception("账号或密码错误！");
			if(StringUtils.isBlank(pwd)) throw new Exception("账号或密码错误！");
			if(StringUtils.isBlank(captchaCode)) throw new Exception("验证码不能为空");
			if (!generatorUtils.verifyCaptcha(captchaCode, captchaId)){
				throw new RuntimeException("验证码错误或验证码过期");
			}
			String tokenStr = new String(RSAUtils.decryptDataByPrivate(pwd, JWTApi.PRIVATE_KEY));
			logger.info(String.format("密文为：%s,解码后：%s", pwd, tokenStr));
			String[] tokenParamArr = tokenStr.split("#@#");
			if (tokenParamArr.length != 2) {
				//密文格式不正确
				throw new RuntimeException("账号或密码错误");
			}
			String dateTime = tokenParamArr[1];
			// 验证密码正确且未过期，时间和当前时间相差5分钟以内
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
			Date currDate = new Date();
			long timeDif = currDate.getTime() - dateFormat.parse(dateTime).getTime();
			if (!(timeDif > -10 * 60 * 1000 && timeDif < 10 * 60 * 1000)) {
				logger.warn(String.format("加密时间为：%s,服务器当前时间为：%s,时间误差为：%s毫秒",
						dateTime, dateFormat.format(currDate), timeDif));
				throw new RuntimeException("账号或密码错误");
			}
			pwd = tokenParamArr[0];
			logger.info(String.format("@用户[%s]开始登录(普通)...", userName));
			//根据用户名获取用户信息
			UserDetails user = userDetailsService.loadUserByUsername(userName);
			//验证账号是否有效
			preAuthenticationChecks.check(user);
			String errKey= Constant.REDIS_ERR_PASSWORD+ user.getUsername();
			String errLock= Constant.REDIS_ERR_PASSWORD_LOCK + user.getUsername();
			Object number =  redisUtil.get(errLock);
			if (Objects.nonNull(number) && number.toString().equals(LOCK_NUMBER)){
				throw new Exception("账号输入错误达到5次，已经锁定请十五分钟后尝试");
			}
			//验证密码是否正确
			if(!authenticationProvider.getPasswordEncoder().matches(pwd,user.getPassword())){
				Long increment = redisUtil.incrementWithExpire(errKey, 15, TimeUnit.MINUTES);
				if (increment >= Long.valueOf(LOCK_NUMBER)) {
					redisUtil.set(errLock, LOCK_NUMBER, (long) (15 * 60));
				}
				throw new Exception("账号或密码错误");
			}
			redisUtil.remove(errLock);
			redisUtil.remove(errKey);
			//生成token
			String token = JWTUtils.buildJwtToken(Constant.TOKEN_CLAIM, user);
			redisUtil.set(Constant.REDIS_TOKE_KEY_PREFIX+((SysUser) user).getUser_id(), SecureUtil.md5(token), (long) (4*(60*60)));
			logger.info(String.format("@用户[%s]登录成功(普通)...，token为%s", userName,token));
			//将token编码放在response header中返回给客户端
			((HttpServletResponse)response).addHeader("token", token);
			//记录登录日志
			ILoginLogService loginLogService = (ILoginLogService)SpringContextUtil.getBean(ILoginLogService.class);
			loginLogService.saveLoginLog((SysUser)user,(HttpServletRequest)request,"normal");
			SysUser sysUser = (SysUser)user;
			//验证账号状况
			sysUser.setPassword(null);
			SysUser u = new SysUser();
			u.setUser_id(sysUser.getUser_id());
			if ("N".equals(sysUser.getFirst_login())){
				sysUser.setFirstLoginFlag(false);
			}else {
				sysUser.setFirstLoginFlag(true);
				u.setFirst_login("N");
			}
			if ("N".equals(sysUser.getInitial_pass())){
				sysUser.setInitialPassFlag(false);
			}else {
				sysUser.setInitialPassFlag(true);
			}
			userManageService.updateUserInfoIgnoreNull(u);
			re.setResult(sysUser);
		}catch(Exception e) {
			logger.error("JWT身份认证失败：",e);
			re.setSuccess(false);
			re.setMessage("JWT身份认证失败："+ExceptionUtils.getMessage(e));
		}
		response.getWriter().print(JsonUtil.toJSon(re));
		return;
	}
	
}
