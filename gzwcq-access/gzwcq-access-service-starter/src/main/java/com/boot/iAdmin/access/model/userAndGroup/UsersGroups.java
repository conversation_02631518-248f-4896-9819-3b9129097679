package com.boot.iAdmin.access.model.userAndGroup;

import java.io.Serializable;

/**
 * <AUTHOR> <br/>
 *         表名： sys_users_groups <br/>
 *         描述：用户与用户组关系表 <br/>
 */
public class UsersGroups implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	protected Long user_id;// 用户ID
	protected Long group_id;// 用户组ID
	protected Long org_id;// 组织ID

	public UsersGroups() {
		super();
	}
	
	public UsersGroups(long user_id) {
		super();
		this.user_id = user_id;
	}
	
	public Long getUser_id() {
		return user_id;
	}
	public void setUser_id(Long user_id) {
		this.user_id = user_id;
	}
	public Long getGroup_id() {
		return group_id;
	}
	public void setGroup_id(Long group_id) {
		this.group_id = group_id;
	}
	public Long getOrg_id() {
		return org_id;
	}
	public void setOrg_id(Long org_id) {
		this.org_id = org_id;
	}
}
