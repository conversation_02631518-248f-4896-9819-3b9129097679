package com.boot.iAdmin.access.common;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class ControllerTools {
	
	/**
	 * 判断该请求是否为AJAX请求
	 * */
	public static boolean isAjaxRequest(HttpServletRequest request) {
		//判断是否ajax
		if("XMLHttpRequest".equalsIgnoreCase(request.getHeader("X-Requested-With"))){
			return true;
		}
		//判断是否axios
		String accept = request.getHeader("accept");
		if (accept == null || accept.contains("application/json")) {
			return true;
		}
		return false;
	}
	
	/**
	 * 输出错误信息
	 * */
	public static void print(HttpServletResponse response, String message,int code) {
		try {
			response.setCharacterEncoding("UTF-8");
			response.setHeader("errorCode", message);
			response.setStatus(code);//设置错误码
			//TODO..统一输出jsonUtil success = false
//			response.getOutputStream().print(message);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
