package com.boot.iAdmin.access.model.role;

import java.util.Collection;

import com.boot.iAdmin.access.model.authority.SysAuthorities;

/**
 * 扩展Role
 * */
public class RoleVo extends Role {

	private String enable_txt;
	
	private Collection<SysAuthorities> auths;//权限集合
	
	public RoleVo(){
		this.enable_txt = "有效";
	}

	public String getEnable_txt() {
		return enable_txt;
	}

	public void setEnable_txt(String enable_txt) {
		this.enable_txt = enable_txt;
	}

	/*@Override
	public void setEnable(Integer enable) {
		super.setEnable(enable);
		this.enable_txt = this.enable.intValue() == 1 ? "有效" : "失效";
	}*/

	public Collection<SysAuthorities> getAuths() {
		return auths;
	}

	public void setAuths(Collection<SysAuthorities> auths) {
		this.auths = auths;
	}

	public Object addAuths(Collection<SysAuthorities> auth) {
		this.setAuths(auth);
		return this;
	}
	
}
