package com.boot.iAdmin.access.service.api;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.security.core.GrantedAuthority;

import com.boot.iAdmin.access.model.user.SysUserVo;

/**
 * 用户信息服务
 * */
public interface ISysUsersService {
	
	/**
	 * 根据用户名获取用户信息
	 * 
	 * @param username 用户名
	 * 
	 * @return Sysuser
	 * */
	public SysUserVo getByUsername(String username);  
    
	/**
	 * 根据用户名获取用户权限集合
	 * 
	 * @param username 用户名
	 * 
	 * @return Collection<GrantedAuthority>
	 * */
    public Collection<? extends GrantedAuthority> loadUserAuthorities(String username);
	
	/**
	 * 模糊查询创建人列表
	 * @param name
	 * @return
	 */
	List<Map> getUserNameByName(String name);

	/**
	 * 根据组织ID查找用户
	 * @param organization_id
	 * @return
	 */
    List<Map> getUserByOrgId(long organization_id);

}
