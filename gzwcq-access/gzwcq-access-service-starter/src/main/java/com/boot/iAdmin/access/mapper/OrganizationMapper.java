package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNodeExport;
import org.apache.ibatis.annotations.Param;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.organization.OrganizationVo;
import com.boot.iAdmin.access.model.organization.SysOrganization;

/**
 * 组织Dao
 */
public interface OrganizationMapper {

    /**
     * 根据父节点ID加载所有子节点
     *
     * @param id 父节点ID
     */
    Collection<ZTreeNode> loadOrganizationByParentId(String id);

    Collection<ZTreeNode> loadOrganizationByParentIdTwo(String id);

    Integer selectInsetStatus(@Param("addUserOrganizationId") String addUserOrganizationId,@Param("userOrganizationId") String userOrganizationId);
    /**
     * 获取单个组织
     */
    SysOrganization selectOrganization(SysOrganization sysOrganization);

    SysOrganization selectOrganizationById(SysOrganization sysOrganization);

    /**
     * 更新资源信息
     */
    void updateIgnoreNull(SysOrganization sysOrganization);

    /**
     * 新增组织
     */
    void insertOrganization(SysOrganization organization);

    /**
     * 新增组织(不自动生成Id)
     */
    void insertOrg(SysOrganization organization);

    /**
     * 删除组织，包括其下子组织
     */
    void deleteOrganization(SysOrganization organization);

    /**
     * 根据所传组织查询其以及其子组织是否被用户关联
     */
    int getTotalOrganization(SysOrganization organization);

    /**
     * 参数唯一性验证
     */
    List<Map<String, String>> selectForUnique(SysOrganization organization);

    /**
     * 根据组织父节点ID，查询出当父节点下面的最大组织编码--可能为空，如果为空，则当前增加的子节点为01节点
     *
     * @param par_id
     * @return
     */
    Map<String, Object> selectMaxOrgCodeByParId(@Param("parent_id") String parent_id);

    /**
     * 根据组织id获取组织名称
     *
     * @param org_id
     * @return
     */
    String getOrgNameById(@Param("org_id") String org_id);


    /**
     * 获取清单分组树
     */
    List<VueAntdTreeSelectNode> getOrganizationList(SysOrganization organization);

    /*分页查询对象*/
    Collection<OrganizationVo> queryOrganizationByPage(BootstrapTableModel<OrganizationVo> bootModel);

    /*数据总量查询*/
    long queryTotalOrganizations(BootstrapTableModel<OrganizationVo> bootModel);

    String selectSubOrgId(Integer orgid);

    SysOrganization loadOneByOrgCode(@Param("org_code") String org_code);

    List<SysOrganization> getOrgsByIds(Map<String, Object> queryMap);

    List<SysOrganization> selectForList(SysOrganization organization);

    List<SysOrganization> selectListForOpenApi(SysOrganization organization);

    SysOrganization getOrgByCode(String code);

    /**
     * 获取所有组织
     *
     * @return
     */
    List<Map> getAllOrg();

    /**
     * 查找本级及下级组织
     *
     * @param organization_id
     * @return
     */
    List<Map<String, Object>> getOrgAndLowerOrg(String organization_id);

    SysOrganization getOrgByOrgId(String organization_id);

    List<Map> getOrgListEdit(String currOrgId);

    /**
     * 获取登录人本级以及下级组织
     *
     * @param paramMap
     * @return
     */
    List<Map<String, Object>> getLoginOrgAndLower(Map<String, Object> paramMap);

    Integer getLoginOrgAndLowerCount(Map<String, Object> paramMap);

    List<Map<String, Object>> getAllOrgList(Map<String, Object> paramMap);

    /**
     * 查找组织id 根据组织名字
     *
     * @param salesOrgId
     */
    String selectOrganizationByName(String salesOrgId);

    /**
     * 组织编码查找组织名字
     *
     * @param str
     * @return
     */
    String getOrgNameByCode(String str);

    /**
     * 查询当前组织及以下的组织列表
     */
    List<VueAntdTreeSelectNode> getCurrAndChildList(@Param("organization_id") String organization_id);

    /**
     * 查询组织树数据
     */
    VueAntdTreeSelectNode getOrganizationTree(SysOrganization organization);

    /**
     * 查询组织下属组织列表
     */
    List<VueAntdTreeSelectNode> getOrganizationListTree(SysOrganization organization);

    /**
     * 查询同级的组织列表并按优先级顺序排序
     *
     * @param organization_id
     * @return
     */
    List<SysOrganization> getChildOrgList(@Param("organization_id") String organization_id);

    Collection<ZTreeNode> getCurrentUserOrganization(@Param("organization_id") String organization_id);

    Collection<ZTreeNode> selectVisiblesOrgs(@Param("visibles") String visibles);

    /**
     * 查询当前组织被托管的组织id列表
     *
     * @param audit_hosting
     * @return
     */
    List<String> selectAuditHosting(@Param("audit_hosting") String audit_hosting);

    /**
     * 查询当前组织及以下的组织id列表
     *
     * @param orgId
     * @return
     */
    List<String> selectAllChildOrgList(@Param("orgId") String orgId);

    /**
     * 查询当前组织以下的组织列表
     *
     * @param orgId
     * @return
     */
    List<SysOrganization> selectAllChildOrgInfoList(@Param("orgId") String orgId);

    /**
     * 查询包括自己及以下的组织数据
     * @param orgId
     * @return
     */
    List<SysOrganization> selectAllInfoList(@Param("orgId") String orgId);

    /**
     * 查询被投资企业组织列表
     *
     * @param FD_CZRZZJGDM
     * @return
     */
    List<SysOrganization> getInvestedOrgList(@Param("FD_CZRZZJGDM") String FD_CZRZZJGDM);

    /**
     * 根据信用代码模糊搜索组织
     */
    List<OrganizationVo> selectByCodeFuzzy(String code);

    /**
     * 查询当前登录人子级组织列表
     */
    List<SysOrganization> getLoginUserChildrenOrgs(SysOrganization org);

    /**
     * 查询同级组织最大的排序数字
     *
     * @param parentId
     * @return
     */
    Double getLastPriority(@Param("parentId") String parentId);

    ZTreeNode getExportData(String id);

    /**
     * 导出组织获取数据
     */
    List<VueAntdTreeSelectNodeExport> getExportOrgTree(String organization_id);

    /**
     * 按企业级次统计某企业下级企业数量
     */
    List<Map<String, Object>> selectChildrenOrgs(@Param("auditHostingList") List<String> auditHostingList);

    /**
     * 根据本级及以下组织id
     */
    Set<String> getLoginUserChildrenIds(@Param("organizationId") String organization_id);

    List<ZTreeNode> selectZTreeNodeByOrgId(@Param("organizationId") String organization_id);

    List<SysOrganization> getChildrenAndAuditsByName(SysOrganization org);

    List<OrganizationVo> selectOrgsByIds(@Param("ids") Collection<String> ids);

    List<OrganizationVo> selectOrgsByIdsPossessDeleteStatus(@Param("ids") Collection<String> ids);

    OrganizationVo selectOrgById(@Param("id") String organization_id);

    /**
     * 获取所有的组织数
     *
     * @param organization_id
     * @return
     */
    OrganizationVo selectOrgByIdPossessDeleteStatus(@Param("id") String organization_id);

    List<OrganizationVo> selectAllVisiblesChildren(@Param("visibleOrgIds") Collection<String> visibleOrgIds);

    List<OrganizationVo> selectAllVisiblesChildrenPossessDeleteStatus(@Param("visibleOrgIds") Collection<String> visibleOrgIds);

    List<SysOrganization> getLoginUserVisibleOrgs(@Param("visibleOrgIds") Collection<String> visibleOrgIds,
                                                  @Param("org") SysOrganization org);

    Long containsKey(@Param("appOrdId")String appOrdId , @Param("ordId") String ordId);
    /**
     * 获取所有虚拟节点
     */
    Set<String> getAllVirtualNode();
}
