package com.boot.iAdmin.access.service.api;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.http.ResponseEntity;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode;
import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNodeExport;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.organization.OrganizationVo;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.organization.SysOrganizationParm;

/**
 * 资源服务
 */
public interface IOrganizationService {

    /**
     * 根据父节点ID加载所有子节点
     *
     * @param id 父节点ID
     */
    Collection<ZTreeNode> loadOrganizationByParentId(String id);

    Collection<ZTreeNode> loadOrganizationByParentIdTwo(String id);

    /**
     * 根据父节点ID加载所有子节点(是否选中)
     *
     * @param id 父节点ID
     */
    Collection<ZTreeNode> loadOrganizationTree(String id, String userId);

    /**
     * 获取单个组织
     */
    SysOrganization selectOrganization(SysOrganization sysOrganization);

    /**
     * 获取单个组织--根据ID查询，为了节省效率，不关联显示父节点名称信息
     */
    SysOrganization selectOrganizationById(SysOrganization sysOrganization);

    /**
     * 更新组织信息
     */
    void updateOrganization(SysOrganization organization);

    /**
     * 新增组织
     */
    void insertOrganization(SysOrganization organization);

    /**
     * 删除组织，包括其下子组织
     */
    void deleteOrganization(SysOrganization organization);

    /**
     * 根据所传组织查询其以及其子组织是否被用户关联
     */
    int getTotalOrganization(SysOrganization organization);

    /**
     * 参数唯一性验证
     */
    boolean validateUniqueParam(SysOrganization organization);

    Map<String, Object> selectMaxOrgCodeByParId(String parent_id);

    String getOrgNameById(String org_id);


    /**
     * 获取组织分组树
     */
    VueAntdTreeSelectNode getOrganizationTressOption(SysOrganization organization);


    /**
     * 根据页面查询条件查询数据并分页
     */
    BootstrapTableModel<OrganizationVo> queryOrganizationByPage(BootstrapTableModel<OrganizationVo> bootModel);

    SysOrganization loadOneByOrgCode(String org_code);

    /**
     * 根据多个组织ID获取组织列表
     */
    List<SysOrganization> getOrgsByIds(Map<String, Object> queryMap);

    VueAntdTreeSelectNode getChildOrgTree(String rootId);

    VueAntdTreeSelectNode getChildOrgTreeByUserId();

    SysOrganization getOrgByCode(String code);

    /**
     * 获取所有组织
     * @return
     */
    List<Map> getAllOrg();

    /**
     * 获取本级及下级所有组织
     * @param organization_id
     * @return
     */
    List<Map<String,Object>> getOrgAndLowerOrg(String organization_id);

    /**
     * 获取登录人的组织信息
     * @param organization_id
     * @return
     */
    SysOrganization getOrgByOrgId(String organization_id);

    /**
     * 获取编辑时登录人的下级组织
     * @return
     */
    List<Map> getOrgListEdit(String currOrgId);

    /**
     * 根据组织编码查找组织名字
     * @param str
     * @return
     */
    String getOrgNameByCode(String str);

    VueAntdTreeSelectNode getParentOrgTree(String orgId);

    List<SysOrganization> getOrgList(SysOrganization org);

    /**
     * 组织上移下移
     * @param orgId
     * @param type
     */
    void moveUpMoveDown(String orgId,String type);

    /**
     * 获取根节点列表
     * @return
     */
    List<ZTreeNode> getRootOrgList();

    /**
     * 查找下一级审批组织节点
     * @param org 审批流当前组织节点
     * @return
     */
    SysOrganization findNextReviewNode(SysOrganization org);

    /**
     * 获取当前登录用户可见组织(异步)
     */
    Collection<ZTreeNode> loadLoginUserOrganizations(String id);

    /**
     * 返回当前登陆人符合列表权限的组织id列表
     * @return
     */
    List<String> dataPermissionsForList();

    /**
     * 返回当前登陆人符合工作流权限的组织id列表
     * @return
     */
    List<String> dataPermissionsForApproval();

    /**
     * 返回当前登录人列表权限的组织id列表(不重复)
     * @return
     */
    Set<String> dataPermissionsForSet();

    /**
     * 根据组织名称模糊匹配组织列表,且为可见组织
     */
    List<SysOrganization> getOrgsByName(SysOrganization org);
    /**
     * 产权树导出
     */
    ResponseEntity<byte[]> treeExport(SysOrganizationParm param);

    /**
     * 根据组织名称模糊匹配当前登录人子级组织列表
     */
    List<SysOrganization> getLoginUserChildrenOrgs(SysOrganization org);

    /**
     * 导出组织获取数据
     * */
    VueAntdTreeSelectNodeExport getExportOrgTree();

    /**
     * 获取指定组织及以下id+托管给他的组织id
     * <P>当前组织及以下+审批托管
     * */
    Set<String> getChildrenAndAuditIdsById(String organizationId);

    /**
     * 异步加载组织树(本级及以下+审批托管)信息(包装对象)
     */
    Collection<ZTreeNode> loadChildrenAndAudits(String id);

    /**
     * 根据组织名称模糊匹配当前登录人子级组织列表+审批托管
     */
    List<SysOrganization> getLoginUserChildrenAndAudits(SysOrganization org);

    /**
     * 当前登录人完整的可见组织树
     */
    List<OrganizationVo> getLoginUserVisibleTree();
    /**
     * 当前登录人完整的可见组织树(删除的组织也展示)
     */
    List<OrganizationVo> getLoginUserVisiblePossessDeleteStatusTree();

    /**
     * 模糊搜索当前登录人可见组织范围内的组织
     */
    List<SysOrganization> getLoginUserVisibleOrgs(SysOrganization org);

    /**
     * 获取所有虚拟节点
     */
    Set<String> getAllVirtualNode();
    
    /**
     * 分页获取产权树列表
     * */
    List<SysOrganization> getOrgListPage(Integer pageNumber,Integer limit ,String appOrgId);
}
