package com.boot.iAdmin.jwt.api;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.crypto.SecureUtil;
import com.boot.iAdmin.jwt.util.MathCaptchaGeneratorUtils;
import com.boot.iAdmin.jwt.util.RSAUtils;
import com.boot.iAdmin.redis.common.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.interceptor.MyAuthenticationProvider;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.jwt.common.Constant;
import com.boot.iAdmin.jwt.util.JWTUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 认证API
 */
@RestController
@RequestMapping("/JWTApi")
public class JWTApi {

	private Log logger = LogFactory.getLog(this.getClass());

	private final static String USER_NAME = "username";// 用户名

	private final static String PASS_WORD = "password";// 密码

	public final static String PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCBBxOd3HogcmI4HeMhmTkbhoNj7SOlV6UZCbOcFiaP7y2ytwpRw4NOG21Sjo8QWGOrJVHw2ZPnoS3hn5D5B+uGLq5btU8ABvCCx02lWWAgvC/3YQWEyLHQQTn0zLhHbADpZPiTHPMv/7sBZBUDkuhED+KrQ7BFA3iJFC5qbDyI2MuXXVPcgwCEUz9dCsfYspUfKYeW66+4WYyALC8Ub8jkA06UcixhDltlMob5OcBDLXBw7SbLqiHXTOp+eLEE2Ls4NKhbl8BcU4gDDRj8npJlud9jNGOFL9Rm7cx0Ec57qp/v+Ngrx9JmZkN97RWPQXzSsNwz4VHPI0LaMGw8STOdAgMBAAECggEAXzdEr6sjaMxgVP8hPQsgjIKAdX5rXrnF+Ssio13pSTB87bSL5fi87ifpThIr2ykPLB5XUMCPmFBmVmj1iADjOts6Ci1LRj7Yv/mqAAprmNynHQw5EVs1dPSde1yT5Qj7x0Ce+5lSkSpL62Pa6g9AXfHHGRHTg6pjCe2dryV+qpEXGwyGL6KdGYTiygE9RH8zTZK7At+4GJlSFFHis4nAr9M2NcE4+91jYnUcuninmX7fUh4el6+41r02miDhnI9f7VjjZRvebdqA02T9jiRmjkdcFEAIytuX5H3Vhkp2AhdavCMOSR4CrsBFZ+rwcz71s9Cumioz21KEY611XOLZVQKBgQDi0fbzTBGIcJJey5OfDkdGJ1Z3e5qAYo0lRIzhyGV24AikNUlGaQVcuidjskH/Zi3f2GhEt2ydKzUO6kkiivCym8kGFrn0JgWX4X+hxiRIBfFywZ89mVUs8+V6rVXcBtMipgiE25Su1+iYfd0M66VMpY9F2K79XX09OcXe71Ci0wKBgQCRoHBRGKrdoOOKmwZnvs74yfv6IC7ftivhWJOqyldmdz8lELtdifFsImu1NId4sY8X/OjWjSCI3g0VhH0Ss1gVwXl7haT5/tCrHLs7WaLUBzc/kkS7Z51irEqmVLNcGj61sRBiflm6Zq982ivdLhtx21ZJBZrcpKRot1pX+V9pzwKBgAipqjvakOTcH8r9DXNKsVAtmnDPyLPUj713o8XPyIhEwC+9VUIaKrlC5tQ476pYdbIR6tGNC9no14rtScPG+LBpoMDZx1paxWXql9xO2/3gNDuEx5crrwQbgI0x55CStiehnbutQz12Q4znabTfnx2RSSqKvwwwMONQvIzqKPM/AoGAWdi0szQW51pGSNPQpd1xcMmdrvqAMQICHKw7nxnrxoo36TRjhRiAXQj3jvrzwiE6ecBOH2E++3KUe/wb9pez9uhBfVY9LQhr0caONdXdwhcZnR0vTrhkv+YwwGJtrpt6qTqvMzVlQr3r84nfmjzCLgL3P2I3432o+fp1wOVYWr0CgYEAxNU23zMLB8qZ3ky7KYTiLYlmK8kxxgM/67k9SZZOz1TsYQ2eIC2LhX0L+HnyEYR9QdNKWIz32VRP3t/49db/EZcC5QFlaw5Pq7mAqEzpR2FCPqGxEEj43SKpnKXUK/Py89tYXj3NGZNF2WaDVagWkMaxepS738uydtRhjrHYaX8=";
	private String PUBLIC_KEY= "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgQcTndx6IHJiOB3jIZk5G4aDY+0jpVelGQmznBYmj+8tsrcKUcODThttUo6PEFhjqyVR8NmT56Et4Z+Q+Qfrhi6uW7VPAAbwgsdNpVlgILwv92EFhMix0EE59My4R2wA6WT4kxzzL/+7AWQVA5LoRA/iq0OwRQN4iRQuamw8iNjLl11T3IMAhFM/XQrH2LKVHymHluuvuFmMgCwvFG/I5ANOlHIsYQ5bZTKG+TnAQy1wcO0my6oh10zqfnixBNi7ODSoW5fAXFOIAw0Y/J6SZbnfYzRjhS/UZu3MdBHOe6qf7/jYK8fSZmZDfe0Vj0F80rDcM+FRzyNC2jBsPEkznQIDAQAB";

	private final static String CAPTCHA_CODE = "captchaCode"; //验证码

	private final static  String CAPTCHA_ID = "Captcha-Id";//验证码照片标识
	@Autowired
	private UserDetailsService userDetailsService;
	@Autowired
	private MathCaptchaGeneratorUtils generatorUtils;
	@Qualifier(value = "myAuthenticationProvider")
	@Autowired
	private MyAuthenticationProvider authenticationProvider;
	@Autowired
	private RedisUtil redisUtil;
	@RequestMapping("/authentication")
	public ResponseEnvelope login(SysUser user, HttpServletResponse response,HttpServletRequest request) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			String userName = request.getParameter(USER_NAME);
			String pwd = request.getParameter(PASS_WORD);
			String captchaCode = request.getParameter(CAPTCHA_CODE);
			String captchaId = null;
			if (request instanceof HttpServletRequest) {
				HttpServletRequest httpRequest = (HttpServletRequest) request;
				captchaId = httpRequest.getHeader(CAPTCHA_ID);
			}
			if(StringUtils.isBlank(userName)) throw new Exception("账号或密码错误！");
			if(StringUtils.isBlank(pwd)) throw new Exception("账号或密码错误！");
			if(StringUtils.isBlank(captchaCode)) throw new Exception("验证码不能为空");
			if (!generatorUtils.verifyCaptcha(captchaCode, captchaId)){
				throw new RuntimeException("验证码错误或验证码过期");
			}

			String tokenStr = new String(RSAUtils.decryptDataByPrivate(pwd, PRIVATE_KEY));
			logger.info(String.format("密文为：%s,解码后：%s", pwd, tokenStr));
			String[] tokenParamArr = tokenStr.split("#");
			if (tokenParamArr.length != 2) {
				//密文格式不正确
				throw new RuntimeException("账号或密码错误");
			}
			String dateTime = tokenParamArr[1];
			// 验证密码正确且未过期，时间和当前时间相差5分钟以内
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
			Date currDate = new Date();
			long timeDif = currDate.getTime() - dateFormat.parse(dateTime).getTime();
			if (!(timeDif > -10 * 60 * 1000 && timeDif < 10 * 60 * 1000)) {
				logger.warn(String.format("加密时间为：%s,服务器当前时间为：%s,时间误差为：%s毫秒",
						dateTime, dateFormat.format(currDate), timeDif));
				throw new RuntimeException("账号或密码错误");
			}
			pwd = tokenParamArr[0];
			logger.info(String.format("@用户[%s]开始登录(普通)...", userName));
			//根据用户名获取用户信息
			UserDetails ud = userDetailsService.loadUserByUsername(userName);
			//验证密码是否正确
			if(!authenticationProvider.getPasswordEncoder().matches(pwd,ud.getPassword())) throw new RuntimeException("用户名或密码错误");
			//生成token
			String token = JWTUtils.buildJwtToken(Constant.TOKEN_CLAIM, new UsernamePasswordAuthenticationToken(ud,null,ud.getAuthorities()));
			//将token编码放在response header中返回给客户端
			response.addHeader("token", token);
			redisUtil.set(Constant.REDIS_TOKE_KEY_PREFIX+((SysUser) user).getUser_id(), SecureUtil.md5(token), (long) (4*(60*60)));
		}catch(Exception e) {
			logger.error("JWT身份认证失败：",e);
			re.setSuccess(false);
			re.setMessage("JWT身份认证失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
}
