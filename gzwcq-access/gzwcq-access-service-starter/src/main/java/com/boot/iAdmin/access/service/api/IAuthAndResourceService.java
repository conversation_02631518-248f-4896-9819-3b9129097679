package com.boot.iAdmin.access.service.api;

import com.boot.iAdmin.access.model.authAndResource.AuthAndResource;
import com.boot.iAdmin.access.model.authority.SysAuthorities;

/**
 * 权限资源关联关系接口
 * */
public interface IAuthAndResourceService {
	
	/**
	 * 新增权限资源关联关系
	 * */
	void insertAuthAndResource(AuthAndResource authAndResource);
	
	/**
	 * 根据权限ID和所涉及到的资源集合，删除在当前资源集合中的当前权限资源关联关系
	 * @param auth 权限
	 * @param allResourcesIds 所涉及到的资源集合
	 * */
	void deleteAuthAndResourceInCurrResourcesIds(SysAuthorities auth, String allResourcesIds);
	
	/**
	 * 根据权限ID删除权限资源关联关系
	 * */
	void deleteAuthAndResourceByAuthIds(String authIds);

}
