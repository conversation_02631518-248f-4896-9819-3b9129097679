package com.boot.iAdmin.access.model.authority;

import java.util.ArrayList;
import java.util.Collection;

import com.boot.iAdmin.access.model.resource.SysResource;

/**
 * 扩展的权限类
 * */
public class CustomAuthorities extends SysAuthorities{
	private static final long serialVersionUID = 1672933688988380051L;
	
	//权限所关联的资源集合
	protected Collection<SysResource> resources = new ArrayList<SysResource>();

	public Collection<SysResource> getResources() {
		return resources;
	}

	public void setResources(Collection<SysResource> resources) {
		this.resources = resources;
	}
	
}
