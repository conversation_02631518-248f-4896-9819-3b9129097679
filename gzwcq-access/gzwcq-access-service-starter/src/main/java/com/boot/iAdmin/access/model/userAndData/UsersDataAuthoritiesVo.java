package com.boot.iAdmin.access.model.userAndData;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： sys_users_data_authorities <br/>
 *         描述：用户与数据权限关系表 <br/>
 */
public class UsersDataAuthoritiesVo extends UsersDataAuthorities {

	private static final long serialVersionUID = 1L;
	private List<UsersDataAuthoritiesVo> usersDataAuthoritiesList;

	public UsersDataAuthoritiesVo() {
		super();
	}

	public UsersDataAuthoritiesVo(long user_id) {
		super();
		this.user_id = user_id;
	}

	public List<UsersDataAuthoritiesVo> getUsersDataAuthoritiesList() {
		return usersDataAuthoritiesList;
	}

	public void setUsersDataAuthoritiesList(List<UsersDataAuthoritiesVo> usersDataAuthoritiesList) {
		this.usersDataAuthoritiesList = usersDataAuthoritiesList;
	}

}
