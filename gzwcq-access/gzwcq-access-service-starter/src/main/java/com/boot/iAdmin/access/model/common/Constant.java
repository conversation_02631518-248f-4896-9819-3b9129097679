package com.boot.iAdmin.access.model.common;

/**
 * 常量
 * 
 * <AUTHOR>
 * @date 2016年5月15日 14:32:30
 * */
public interface Constant {

	// 权限标识
	public static final String AUTH_MARK = "ROLE_";
	// 公共资源
	public static final String COMMON_RES = "/**";
	// 验证码
	static String SESSION_GENERATED_CAPTCHA_KEY = "SESSION_GENERATED_CAPTCHA_KEY";
	// 超时
	public static final String TIMEOUT = "TIMEOUT";
	// 登出
	public static final String LOGOUT = "LOGOUT";
	// 失效,特指被挤下线
	public static final String EXPIRED = "EXPIRED";
	// 超时
	public static final String REPEATSUMIT = "REPEATSUMIT";
	// 认证失败
	public static final String AUTHENTICATION_FAIL = "AUTHENTICATION_FAIL";
	// 没权限，访问拒绝
	public static final String ACCESSDENIED = "ACCESSDENIED";
	//模块前缀
	public static final String MODULE_PREFIX = "MODULE_";
}
