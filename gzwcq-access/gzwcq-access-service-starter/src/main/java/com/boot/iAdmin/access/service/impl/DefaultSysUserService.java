package com.boot.iAdmin.access.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.boot.iAdmin.access.model.user.SysUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;

import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.authority.CustomGrantedAuthority;
import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.model.common.Constant;
import com.boot.iAdmin.access.model.dataAuthorities.DataAuthorities;
import com.boot.iAdmin.access.model.groups.Groups;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.ISysUsersService;

/**
 * 自定义用户信息服务实现类
 * */
@Service("defaultSysUserService")
public class DefaultSysUserService implements ISysUsersService {
	
	@Autowired
	private SysUserInfoMapper userInfoMapper;

	/**
	 * 根据用户名获取用户信息(之前的)
	 * 
	 * @param username
	 *            用户名
	 * 
	 * @return Sysuser
	 * */
	public SysUserVo getByUsername(String username) {
		return userInfoMapper.getUserInfoByUsername(username);
	}
	
	/**
	 * 根据用户名获取用户权限集合
	 * 
	 * @param username
	 *            用户名
	 * 
	 * @return Collection<GrantedAuthority>
	 * */
	public Collection<? extends GrantedAuthority> loadUserAuthorities(String username) {
		
		List<SysAuthorities> list = userInfoMapper.getSysAuthByUserName(username);

		List<CustomGrantedAuthority> auths = new ArrayList<CustomGrantedAuthority>();

		for (SysAuthorities authority : list) {
			CustomGrantedAuthority grantedAuthority = new CustomGrantedAuthority(Constant.AUTH_MARK+authority.getAuthority_id());
			auths.add(grantedAuthority);
		}
		
		return auths;
	}
	
	@Override
	public List<Map> getUserNameByName(String name) {
		return userInfoMapper.getUserNameByName(name);
	}

	/**
	 * 根据组织ID获取用户
	 * @param organization_id
	 * @return
	 */
	@Override
	public List<Map> getUserByOrgId(long organization_id) {
		return userInfoMapper.getUserByOrgId(organization_id);
	}

}
