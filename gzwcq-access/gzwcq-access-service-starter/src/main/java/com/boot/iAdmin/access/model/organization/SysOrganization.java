package com.boot.iAdmin.access.model.organization;

import java.util.Date;

/**
 * 组织机构
 * 
 * <AUTHOR>
 *
 */

public class SysOrganization {

	// 组织ID
	protected String organization_id;
	// 组织名称
	protected String organization_name;
	// 组织说明
	protected String organization_desc;
	// 组织编码
	protected String organization_code;
	// 父节点ID
	protected String parent_id;
	// 优先级
	protected String priority;
	//父组织编码
	protected String parent_code;
	//创建时间
	protected Date create_time;
	//创建人
	protected String create_user;
	//创建单位
	protected String create_unit;
	//最后修改时间
	protected Date last_update_time;
	//最后修改人
	protected String last_update_user;
	//组织类型
	protected String biztypes;
	//同步匹配代码
	protected String syncode;
	//所属国资监管机构/6位区域码
	protected String ssgzjgjg;
	//企业类型（1为行政机构）
	protected String businesstype;
	//企业级次
	protected String business_level;
	//所有父级节点Id，逗号隔开
	protected String parents;
	//所有父级节点Id
	protected String parents_old;
	//可见组织（多选）
	protected String visibles;
	//审核托管组织（单选）
	protected String audit_hosting;
	//企业性质（1国有企业/2合伙企业）
	protected String business_nature;
	//是否删除(Y/N)
	protected String isdeleted;

	public SysOrganization(){}

	public SysOrganization(String organization_id) {
		super();
		this.organization_id = organization_id;
	}
	
	public String getOrganization_id() {
		return organization_id;
	}

	public void setOrganization_id(String organization_id) {
		this.organization_id = organization_id;
	}

	public String getOrganization_name() {
		return organization_name;
	}

	public void setOrganization_name(String organization_name) {
		this.organization_name = organization_name;
	}

	public String getOrganization_desc() {
		return organization_desc;
	}

	public void setOrganization_desc(String organization_desc) {
		this.organization_desc = organization_desc;
	}

	public String getOrganization_code() {
		return organization_code;
	}

	public void setOrganization_code(String organization_code) {
		this.organization_code = organization_code;
	}

	public String getParent_id() {
		return parent_id;
	}

	public void setParent_id(String parent_id) {
		this.parent_id = parent_id;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getParent_code() {
		return parent_code;
	}

	public void setParent_code(String parent_code) {
		this.parent_code = parent_code;
	}

	public Date getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Date create_time) {
		this.create_time = create_time;
	}

	public String getCreate_user() {
		return create_user;
	}

	public void setCreate_user(String create_user) {
		this.create_user = create_user;
	}

	public String getCreate_unit() {
		return create_unit;
	}

	public void setCreate_unit(String create_unit) {
		this.create_unit = create_unit;
	}

	public Date getLast_update_time() {
		return last_update_time;
	}

	public void setLast_update_time(Date last_update_time) {
		this.last_update_time = last_update_time;
	}

	public String getLast_update_user() {
		return last_update_user;
	}

	public void setLast_update_user(String last_update_user) {
		this.last_update_user = last_update_user;
	}

	public String getBiztypes() {
		return biztypes;
	}

	public void setBiztypes(String biztypes) {
		this.biztypes = biztypes;
	}

	public String getSyncode() {
		return syncode;
	}

	public void setSyncode(String syncode) {
		this.syncode = syncode;
	}

	public String getSsgzjgjg() {
		return ssgzjgjg;
	}

	public void setSsgzjgjg(String ssgzjgjg) {
		this.ssgzjgjg = ssgzjgjg;
	}

	public String getBusinesstype() {
		return businesstype;
	}

	public void setBusinesstype(String businesstype) {
		this.businesstype = businesstype;
	}

	public String getBusiness_level() {
		return business_level;
	}

	public void setBusiness_level(String business_level) {
		this.business_level = business_level;
	}

	public String getParents() {
		return parents;
	}

	public void setParents(String parents) {
		this.parents = parents;
	}

	public String getParents_old() {
		return parents_old;
	}

	public void setParents_old(String parents_old) {
		this.parents_old = parents_old;
	}

	public String getVisibles() {
		return visibles;
	}

	public void setVisibles(String visibles) {
		this.visibles = visibles;
	}

	public String getAudit_hosting() {
		return audit_hosting;
	}

	public void setAudit_hosting(String audit_hosting) {
		this.audit_hosting = audit_hosting;
	}

	public String getBusiness_nature() {
		return business_nature;
	}

	public void setBusiness_nature(String business_nature) {
		this.business_nature = business_nature;
	}

	public String getIsdeleted() {
		return isdeleted;
	}

	public void setIsdeleted(String isdeleted) {
		this.isdeleted = isdeleted;
	}
}