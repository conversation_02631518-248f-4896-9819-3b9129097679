package com.boot.iAdmin.access.service.impl;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.boot.iAdmin.access.mapper.ModuleMapper;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.module.Module;
import com.boot.iAdmin.access.model.module.ModuleVo;
import com.boot.iAdmin.access.service.api.IMenuManageService;

@Service
public class MenuManageServiceImpl implements IMenuManageService{
	
	@Autowired
	private ModuleMapper modelMapper;
	
	/**
	 * 异步加载菜单树信息
	 * 
	 * @param id 父节点ID
	 * */
	public Collection<ZTreeNode> loadMenuByParentId(String id) {
		return modelMapper.loadMenuByParentId(id);
	}
	
	/**
	 * 查询菜单信息
	 * */
	public Module selectModule(Module module) {
		return modelMapper.selectModule(module);
	}
	
	/**
	 * 新增菜单
	 * */
	public void insertModule(Module module) {
		modelMapper.insertModule(module);
	}
	
	/**
	 * 删除菜单
	 * */
	public void deleteModule(Module module) {
		modelMapper.deleteModule(module);
	}
	
	/**
	 * 更新菜单
	 * */
	public void updateModule(Module module) {
		modelMapper.updateIgnoreNull(module);
	}

	@Override
	public List<Module> selectForListModule(ModuleVo module) {
		return modelMapper.selectForListModule(module);
	}
	
}
