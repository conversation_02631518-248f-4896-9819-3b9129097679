package com.boot.iAdmin.jwt.util;

import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.hutool.crypto.SecureUtil;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.jwt.common.Constant;
import com.boot.iAdmin.redis.common.RedisUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.boot.IAdmin.common.utils.JsonUtil;
import org.springframework.security.core.userdetails.UserDetails;

import javax.servlet.ServletException;

/**
 * JWT身份认证工具类
 * <P>
 * RSA方式
 * 
 * <AUTHOR>
 * @date 2019年11月26日 13:36:30
 * @dependency JsonUtil
 */
public class JWTUtils {

	private JWTUtils() {}

	private final static Logger logger = LoggerFactory.getLogger(JWTUtils.class);
	// 使用RSA算法
	public static final String KEY_ALGORITHM = "RSA";
	
	// 公钥
	private static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCjO2C0x4RluaA3dPptWtScWaZw" + 
			"MT1NLNVKOIJNK5YGQ0uN/dT/IbJoL9V9xEBr96ULpAYKx6ccnKyhBnoTJCVy9cXJ" + 
			"vdRzedQfsXvkHtZBeFh0ayQhOOTAsvc/pg139vtrZE3DXhvWjSo4de7NYSlrIMT3" + 
			"+BO+MZFGb/w8um8wrQIDAQAB";
	// 私钥
	private static final String PRIVATE_KEY = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKM7YLTHhGW5oDd0" + 
			"+m1a1JxZpnAxPU0s1Uo4gk0rlgZDS4391P8hsmgv1X3EQGv3pQukBgrHpxycrKEG" + 
			"ehMkJXL1xcm91HN51B+xe+Qe1kF4WHRrJCE45MCy9z+mDXf2+2tkTcNeG9aNKjh1" + 
			"7s1hKWsgxPf4E74xkUZv/Dy6bzCtAgMBAAECgYEAgCnB/49cLAb4fjKR4kTcBLr7" + 
			"TMHMkQjvmywNWwwq6lXyP9WaZIwPqXBNX0c1EdDKvP4XZGVol7WIcZJFTTwnTf/n" + 
			"7NbEPIl5OppFK6oo9AzpJI4h6OFUgPevIl025uzN/UMQglrAZywbfoiQLu6hg3Mz" + 
			"DLWmHUGoBtPrOJTvkH0CQQDTwMUWuB2QVKam3gBy7P+LKhJHfJ62Yt7pJ9VWFPRd" + 
			"yWfNn3REyCTHkQ3N4o5bcQoF0sxe8nfO0RB79KfUizznAkEAxVcWZnW3JoSP2DIr" + 
			"nR9PZkhH8cMvkeXu6+TEkpSAGPMUX8NAc37brxaE6wct3AMp3S2zsk85ipblTaeN" + 
			"GKy/SwJAV6r+rhpJ+yBg4sMU0n/2iKpBaSFaIE6s1UDtnTcNuOqStFno7DUkrQ8L" + 
			"9QpwP8F8Ec8lV8Xw/komFfvDpEaFTwJBALcdmZaX0sy2I3g9DcVtwVKq7b+EHAcp" + 
			"MqvICzPtAnSkUxqNZqgFZjqxDcO9VKucb+NiPiXOaBmW7c5CFKZ53cMCQC4ECMDp" + 
			"jEimF6l0FqYUGDiNQjAcJ8EYy+/s0qJaemNXeB+AsPEMdgykps31t6MLoMGDG8mV" + 
			"iylOlmgbV2NO7sg=";
	// 使用RSA算法
	private static Algorithm algorithm = null;
	
	//过期时间
	private final static Integer EXP = 24 * 60 * 60 * 1000; //一天

	// 静态代码块，类加载初始化阶段执行
	static {
		try {
			algorithm = getAlgorithm(PUBLIC_KEY, PRIVATE_KEY);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 加密
	 * @param claim
	 * @param secret
	 * @param data
	 */
	public static String buildJwtToken(String claim, Object data) {
		try {
			// 通过调用 JWT.create()来创建 jwt实例
			JWTCreator.Builder builder = JWT.create()
					.withIssuedAt(new Date(System.currentTimeMillis()-5*60*1000))
					.withExpiresAt(new Date(System.currentTimeMillis() + EXP))//添加过期时间
					.withClaim(claim, JsonUtil.toJSon(data));// 索赔:添加自定义声明值,完成荷载的信息
			// 签署:调用sign()传递算法实例
			return builder.sign(algorithm);
		} catch (JWTCreationException e) {
			logger.error("无效的签名配置！", e.getMessage());
		}
		return null;
	}

	/***
	 * 校验
	 * <P>
	 * 校验token是否有效且是否过期，并获取身份信息
	 */
	public static String verify(String token, String key, RedisUtil redisUtil) {
		String vo = null;
		try {
			// 这将用于验证令牌的签名
			JWTVerifier verifier = JWT.require(algorithm).build();
			// 针对给定令牌执行验证
			DecodedJWT jwt = verifier.verify(token);
			// 获取令牌中定义的声明
			Map<String, Claim> claims = jwt.getClaims();
			// 返回指定键映射到的值
			String userJson = claims.get(key).asString();
			if (StringUtils.isNotBlank(userJson)) {
				SysUser userDetails = (SysUser) JsonUtil.readValue(userJson, SysUser.class);
				String user_id = userDetails.getUser_id();
				Object redisToken = redisUtil.get(Constant.REDIS_TOKE_KEY_PREFIX + user_id);
				if (Objects.isNull(redisToken)){
					throw new JWTVerificationException("令牌已失效或令牌已经过期");
				}
				if (!SecureUtil.md5(token).equals(redisToken.toString())){
					throw new JWTVerificationException("账号已被其他设备登录");
				}
				redisUtil.extendExpiration(Constant.REDIS_TOKE_KEY_PREFIX + user_id,60, TimeUnit.MINUTES);
				vo = userJson;
			}
		} catch (JWTVerificationException e) {
			logger.error(String.format("校验失败或token已过期!%s", token), e);
			vo = e.getMessage();
		}
		return vo;
	}
	
	public static Algorithm getAlgorithm(String publicK,String privateK) throws Exception {
		RSAPublicKey publicKey = (RSAPublicKey) KeyFactory.getInstance(KEY_ALGORITHM).generatePublic(new X509EncodedKeySpec(Base64.decodeBase64(publicK)));
		RSAPrivateKey privateKey = (RSAPrivateKey) KeyFactory.getInstance(KEY_ALGORITHM).generatePrivate(new PKCS8EncodedKeySpec(Base64.decodeBase64(privateK)));
		return Algorithm.RSA256(publicKey, privateKey);
	}
	
	/*public static void main(String[] args) {
		SysUser u = new SysUser();
//		u.setName("123");
//		String token = JWTUtils.buildJwtToken("test", u);
//		System.out.println(token);
		u = JsonUtil.readValue(JWTUtils.verify("eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KTWzgKlmI0kNbcD-bhOdrr577EzsrfIh6UipcotKjJ6S4gZXI6tQ9UrqP86gMFZQC_9BFhB3-Uwmw-BddC-fGgFyRBBh4ICijbzfz-pmmQlP6xI-wtrE-waadrI1k5pxi3VdCFX7t07wJpddC9Ju8eEoLUspKT9UKTyAMRBaKvI", "test"), SysUser.class);
		System.out.println(u.getName());
	}*/
	
}
