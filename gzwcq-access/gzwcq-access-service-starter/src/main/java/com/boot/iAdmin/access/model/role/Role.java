package com.boot.iAdmin.access.model.role;

import java.util.Date;

/**
 * 角色
 * */
public class Role {

	// 主键
	protected Long role_id;
	// 角色名称
	protected String role_name;
	//角色编码
	protected String role_code;
	// 角色描述
	protected String role_desc;
	// 状态
	protected Integer enable;
	@Deprecated
	protected Integer issys;
	// 创建时间
	protected Date dt_create;
	// 更新时间
	private Date dt_update;
	
	public Role() {
		super();
	}

	public Role(Long role_id) {
		super();
		this.role_id = role_id;
	}

	public Long getRole_id() {
		return role_id;
	}

	public void setRole_id(Long role_id) {
		this.role_id = role_id;
	}

	public String getRole_name() {
		return role_name;
	}

	public void setRole_name(String role_name) {
		this.role_name = role_name;
	}

	public String getRole_desc() {
		return role_desc;
	}

	public void setRole_desc(String role_desc) {
		this.role_desc = role_desc;
	}

	public Integer getEnable() {
		return enable;
	}

	public void setEnable(Integer enable) {
		this.enable = enable;
	}

	public Integer getIssys() {
		return issys;
	}

	public void setIssys(Integer issys) {
		this.issys = issys;
	}

	public Date getDt_create() {
		return dt_create;
	}

	public void setDt_create(Date dt_create) {
		this.dt_create = dt_create;
	}

	public Date getDt_update() {
		return dt_update;
	}

	public void setDt_update(Date dt_update) {
		this.dt_update = dt_update;
	}

	public String getRole_code() {
		return role_code;
	}

	public void setRole_code(String role_code) {
		this.role_code = role_code;
	}
	
}
