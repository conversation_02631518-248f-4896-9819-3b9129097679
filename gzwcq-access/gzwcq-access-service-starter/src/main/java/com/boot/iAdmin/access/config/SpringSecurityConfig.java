package com.boot.iAdmin.access.config;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.vote.AffirmativeBased;
import org.springframework.security.access.vote.AuthenticatedVoter;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;

import com.boot.iAdmin.access.interceptor.CustomLoginUrlAuthenticationEntryPoint;
import com.boot.iAdmin.access.interceptor.CustomRoleVoter;
import com.boot.iAdmin.access.interceptor.MyFilterInvocationSecurityMetadataSource;
import com.boot.iAdmin.access.interceptor.MyFilterInvocationSecurityMetadataSourceForRedis;
import com.boot.iAdmin.access.interceptor.RequestAccessDeniedHandler;

/**
 * spring security 配置
 * 
 * <AUTHOR>
 * @date 2018年9月7日 17:45:57
 * 
 */
//@ConfigurationProperties(prefix = "com.auto.security")
public class SpringSecurityConfig {
	
	@Resource(name = "mySecurityProperties")
	private MySecurityProperties securityProperties;

	/**
	 * 登录成功处理器
	 */
	@Bean(name = "authenticationSuccessHandler")
	public AuthenticationSuccessHandler getAuthenticationSuccessHandler() {
		return new SimpleUrlAuthenticationSuccessHandler(securityProperties.getLoginSuccessUrl());
	}

	/**
	 * 登录失败处理器
	 */
	@Bean(name = "authenticationFailureHandler")
	public AuthenticationFailureHandler getAuthenticationFailureHandler() {
		return new SimpleUrlAuthenticationFailureHandler(securityProperties.getLoginFailUrl());
	}

	
	/**
	 * 未登录的切点
	 */
	@Bean(name = "authenticationEntryPoint")
	public AuthenticationEntryPoint getAuthenticationEntryPoint() {
		return new CustomLoginUrlAuthenticationEntryPoint(securityProperties.getLoginFormUrl());
	}
	
	/**
	 * 权限认证失败异常处理器
	 * 权限不足
	 * */
	@Bean(name = "accessDeniedHandler")
	public AccessDeniedHandler getAccessDeniedHandler() {
		return new RequestAccessDeniedHandler(securityProperties.getErrorPage403());
	}
	
	/**
	 * 安全数据源
	 * <P>集群数据源/共享数据源/redis
	 * */
	@Bean(name = "securityMetadataSource")
	@ConditionalOnProperty(value= {"com.auto.security.isCluster"},havingValue = "true")
	@ConditionalOnClass(name= {"com.boot.iAdmin.redis.config.RedisConfig","org.springframework.data.redis.core.RedisOperations"})
	public FilterInvocationSecurityMetadataSource getFilterInvocationSecurityMetadataSourceRedis() {
		return new MyFilterInvocationSecurityMetadataSourceForRedis();
	}
	
	/**
	 * 安全数据源
	 * <P>单机数据源/内存数据源
	 * */
	@Bean(name = "securityMetadataSource")
	@ConditionalOnMissingBean(value=FilterInvocationSecurityMetadataSource.class)
	public FilterInvocationSecurityMetadataSource getFilterInvocationSecurityMetadataSource() {
		return new MyFilterInvocationSecurityMetadataSource();
	}
	
	/**
	 * 访问决策管理器
	 * */
	@Bean(name="accessDecisionManager")
	public AccessDecisionManager accessDecisionManager() {
		List<AccessDecisionVoter<? extends Object>> decisionVoters = new ArrayList<AccessDecisionVoter<? extends Object>>();
		decisionVoters.add(roleVoter());
		decisionVoters.add(authenticatedVoter());
		AccessDecisionManager accessDecisionManager = new AffirmativeBased(decisionVoters);
		return accessDecisionManager;
	}
	
	/**
	 * 角色投票器
	 * */
	private AccessDecisionVoter<?> roleVoter() {
		CustomRoleVoter voter = new CustomRoleVoter();
		voter.setRolePrefix("");
		return voter;
	}
	
	/**
	 * 身份认证投票器
	 * */
	private AuthenticatedVoter authenticatedVoter() {
		return new AuthenticatedVoter();
	}
}
