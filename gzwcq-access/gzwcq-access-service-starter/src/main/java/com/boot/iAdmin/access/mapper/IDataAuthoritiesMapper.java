package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.dataAuthorities.DataAuthorities;
import com.boot.iAdmin.access.model.dataAuthorities.DataAuthoritiesVo;
import com.boot.iAdmin.access.model.dataAuthorities.MenuAuth;

@Repository
public interface IDataAuthoritiesMapper {
	
	/*保存对象*/
	void save(DataAuthorities dataAuthorities);
	
	/*根据表主键删除对象,采用foreach遍历*/
	void delete(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(DataAuthorities dataAuthorities);
	
	/*分页查询对象*/
	Collection<DataAuthoritiesVo> queryDataAuthoritiesByPage(BootstrapTableModel<DataAuthoritiesVo> bootModel);
	
	/*数据总量查询*/
	long queryTotalDataAuthoritiess(BootstrapTableModel<DataAuthoritiesVo> bootModel);
	
	/*根据主键查询对象*/
	DataAuthorities queryDataAuthoritiesById(DataAuthorities dataAuthorities);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<DataAuthorities> selectForList(DataAuthorities dataAuthorities);
	
	/**
	 * 数据唯一性验证
	 * */
	List<DataAuthorities> selectForUnique(DataAuthorities dataAuthorities);
	/**
	 * 菜单权限关联表
	 * @param menuAuth
	 */
	void saveMenuAuth(MenuAuth menuAuth);

	int queryByMap(Map<String, Object> map);
	
	/**根据公司获取数据权限*/
	List<DataAuthorities> loadDataAuthsByCompany(Map<String, Object> paramsMap);
	
	/**
	 * 根据用户以及公司获取菜单权限
	 * */
	List<MenuAuth> queryMenuAuthByUserAndCompany(Map<String, Object> paramsMap);
	
}