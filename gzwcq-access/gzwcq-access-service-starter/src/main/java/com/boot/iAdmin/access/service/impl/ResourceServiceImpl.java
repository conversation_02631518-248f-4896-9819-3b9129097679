package com.boot.iAdmin.access.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.boot.iAdmin.access.mapper.AuthoritiesMapper;
import com.boot.iAdmin.access.mapper.ResourceMapper;
import com.boot.iAdmin.access.model.authority.CustomAuthorities;
import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.model.common.Constant;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.resource.SysResource;
import com.boot.iAdmin.access.service.api.IResourceService;

/**
 * 资源服务实现类
 * */
@Service
public class ResourceServiceImpl implements IResourceService {

	Log logger = LogFactory.getLog(ResourceServiceImpl.class);

	@Autowired
	private ResourceMapper resourceMapper;

	@Autowired
	private AuthoritiesMapper authoritiesMapper;

	/**
	 * 获取资源集合
	 * */
	
	public List<Map<String, String>> getURLResourceMapping() {

		boolean common_res_flg = false;

		List<Map<String, String>> list = new ArrayList<Map<String, String>>();

		List<CustomAuthorities> authLists = this.getAuthorities();
		for (CustomAuthorities auth : authLists) {
			for (SysResource resource : auth.getResources()) {
				if(StringUtils.isBlank(resource.getResource_pattern())) continue;
				if (Constant.COMMON_RES.equals(resource.getResource_pattern()))
					common_res_flg = true;
				Map<String, String> map = new HashMap<String, String>();
				map.put("authorityMark", Constant.AUTH_MARK + auth.getAuthority_id());
				map.put("resourcePattern", resource.getResource_pattern());
				list.add(map);
			}
		}
		// 增加公共权限,保护未配置权限的资源
		if (!common_res_flg) {
			logger.info("所有资源增加公共权限[ROLE_COMMON]");
			Map<String, String> map = new HashMap<String, String>();
			map.put("authorityMark", "ROLE_COMMON");
			map.put("resourcePattern", Constant.COMMON_RES);
			list.add(map);
		}
		return list;
	}

	/**
	 * 获得权限列表
	 * */
	private List<CustomAuthorities> getAuthorities() {
		List<CustomAuthorities> authLists = authoritiesMapper.loadAuthorities();
		for (CustomAuthorities auth : authLists) {
			auth.setResources(this.getResourcesByAuth(auth));
		}
		return authLists;
	}

	/**
	 * 获取该权限下关联的资源集合
	 * */
	private List<SysResource> getResourcesByAuth(SysAuthorities auth) {
		return resourceMapper.getResourcesByAuth(auth);
	}

	/**
	 * 根据父节点ID加载所有子节点
	 * 
	 * @param id
	 *            父节点ID
	 * */
	
	public Collection<ZTreeNode> loadResourceByParentId(String id) {
		Collection<ZTreeNode> nodes = resourceMapper.loadResourceByParentId(id);
		return nodes;
	}
	
	/**
	 * 异步加载资源树信息
	 * <P>当前权限已关联的资源默认选中
	 * @param nodeId 父节点ID
	 * @param authority_id 权限ID
	 * */
	
	public Collection<ZTreeNode> loadResourceByParentId(String nodeId, Long authority_id) {
		//获取子节点集合
		Collection<ZTreeNode> nodes = this.loadResourceByParentId(nodeId);
		//设置是否选中
		Collection<SysResource> resources = this.getResourcesByAuthId(authority_id);
		Collection<String> selIds = new ArrayList<String>();
		for(SysResource resource : resources){
			selIds.add(resource.getResource_id() + "");
		}
		for(ZTreeNode node : nodes){
			if(selIds.contains(node.getId())) node.setChecked(true);
		}
		return nodes;
	}

	/**
	 * 获取当前权限的资源集合
	 * 
	 * @param authority_id
	 *            权限ID
	 * */
	
	public Collection<SysResource> getResourcesByAuthId(Long authority_id) {
		return this.getResourcesByAuth(new SysAuthorities(authority_id));
	}
	
	/**
	 * 获取单个资源信息
	 * 
	 * @return
	 * */
	
	public SysResource selectResource(SysResource sysResource) {
		return resourceMapper.selectResource(sysResource);
	}
	
	/**
	 * 更新资源信息
	 * */
	
	public void updateResource(SysResource resource) {
		resourceMapper.updateIgnoreNull(resource);
	}
	
	/**
	 * 新增资源
	 * */
	
	public void insertResource(SysResource resource) {
		resourceMapper.insertResource(resource);
	}
	
	/**
	 * 删除资源，包括其下子资源
	 * */
	
	public void deleteResource(SysResource resource) {
		resourceMapper.deleteResourceTree(resource);
	}
	
	/**
	 * 根据所传资源查询其以及其子资源是否被权限关联
	 * */
	@Override
	public int getTotalAuthAndResource(SysResource resource) {
		return resourceMapper.getTotalAuthAndResource(resource);
	}
	
	/**
	 * 根据所传资源查询其以及其子资源是否被菜单关联
	 * */
	@Override
	public int getTotalMoudleAndResource(SysResource resource) {
		return resourceMapper.getTotalMoudleAndResource(resource);
	}
	
	/**
	 * 参数唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(SysResource resource) {
		return resourceMapper.selectForUnique(resource).size() == 0;
	}
	
	
}
