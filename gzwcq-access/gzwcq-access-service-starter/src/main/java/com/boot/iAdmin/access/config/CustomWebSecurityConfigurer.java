package com.boot.iAdmin.access.config;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.Filter;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.session.CompositeSessionAuthenticationStrategy;
import org.springframework.security.web.authentication.session.ConcurrentSessionControlAuthenticationStrategy;
import org.springframework.security.web.authentication.session.RegisterSessionAuthenticationStrategy;
import org.springframework.security.web.authentication.session.SessionAuthenticationStrategy;
import org.springframework.security.web.session.ConcurrentSessionFilter;
import org.springframework.security.web.session.HttpSessionEventPublisher;

import com.boot.iAdmin.access.interceptor.MyAuthenticationProvider;
import com.boot.iAdmin.access.interceptor.MyUsernamePasswordAuthenticationFilter;
import com.boot.iAdmin.access.interceptor.session.RedirectWithAjaxSessionInformationExpiredStrategy;
import com.boot.iAdmin.access.interceptor.session.RedisConcurrentSessionControlAuthenticationStrategy;
import com.boot.iAdmin.access.interceptor.session.RedisSessionRegistryImpl;

/**
 * 自定义spring-security
 * <P>
 * FilterSecurityInterceptor无法用spring生成，存在bug
 */
@Configuration
@Import({SpringSecurityConfig.class})
public class CustomWebSecurityConfigurer extends WebSecurityConfigurerAdapter {
	
	private Log logger = LogFactory.getLog(this.getClass());

	@Resource(name = "mySecurityProperties")
	private MySecurityProperties securityProperties;
	// 安全数据元
	@Autowired
	private FilterInvocationSecurityMetadataSource securityMetadataSource;
	// 访问决策管理
	@Autowired
	private AccessDecisionManager accessDecisionManager;
	// 未登录的切点
	@Autowired
	private AuthenticationEntryPoint authenticationEntryPoint;
	// 访问拒绝处理器
	@Autowired
	private AccessDeniedHandler accessDeniedHandler;
	
	@Resource(name="myAuthenticationProvider")
	private AuthenticationProvider authenticationProvider;
	
	@Override
	public void configure(WebSecurity web) throws Exception {
		for (String url : securityProperties.getIgnores()) {
			web.ignoring().antMatchers(url);
		}
		// 自定义页面标签拦截，必须在http之前，注入自定义的权限过滤器
//		web.privilegeEvaluator(getWebInvocationPrivilegeEvaluator());
	}

	@Override
	protected void configure(HttpSecurity http) throws Exception {
		// 解决不允许显示在iframe的问题
		http.headers().frameOptions().disable();
		//增加增加的身份认证提供者
		http.authenticationProvider(authenticationProvider);
		// 自定义登录过滤器
		http.addFilterAt(getUsernamePasswordAuthenticationFilter(null,null), UsernamePasswordAuthenticationFilter.class);
		// 在合适的位置加入权限过滤器
		http.addFilterAt(getFilterSecurityInterceptor(), FilterSecurityInterceptor.class);
		/*
		 * 在http中注册权限过滤器，供其他模块使用，例如@see WebInvocationPrivilegeEvaluator（用于页面权限标签），@see WebSecurity#securityInterceptor，保证全局过滤器唯一性
		 * @see WebSecurityConfigurerAdapter.init
		 * */
		http.setSharedObject(FilterSecurityInterceptor.class, getFilterSecurityInterceptor());
		// 自定义未登录的切点
		http.exceptionHandling().authenticationEntryPoint(authenticationEntryPoint);
		// 自定义权限不足处理器
		http.exceptionHandling().accessDeniedHandler(accessDeniedHandler);
		// 自定义登出
		http.logout().logoutUrl("/logout").logoutSuccessUrl(securityProperties.getLogoutUrl()).deleteCookies();
		// 关闭csrf
		http.csrf().disable();
		// Concurrency Control
		//使用自己的并发控制过滤器
        http.addFilterAt(concurrentSessionFilter(),ConcurrentSessionFilter.class);
		// 只允许一个用户登录,如果同一个账户两次登录,那么第一个账户将被踢下线,跳转到登录页面
        //-1代表不控制
        //FIXME 不设置maximumSessions从而使用自己的并发控制过滤器 参见SessionManagementConfigurer#isConcurrentSessionControlEnabled
		http.sessionManagement().
			sessionAuthenticationStrategy(sessionAuthenticationStrategy())/*.
				invalidSessionUrl(securityProperties.getLoginFormUrl()).
					maximumSessions(1).
						expiredUrl(securityProperties.getLogoutUrl())*/;
	}

	//FIXME 权限过滤器如果注册到IOC容器则会被自动注册到web容器的Filter chain，添加后默认的过滤路径为 /*，而实际只需要注册到security中即可，因此必须取消当前Filter的自动注册 @see CustomWebSecurityConfigurer#cancelSecurityFilterRegistration
	//<P>当该对象交由spring管理，所有我们认为忽略过滤的http请求[web.ignoring()]，还是会进入FilterSecurityInterceptor过滤器，此时由于其他过滤器特别是session过滤器没有执行，从而无法从SecurityContextHolder中获取用户信息，因此会报错：An Authentication object was not found in the SecurityContext
	@Bean(name="filterSecurityInterceptor")
	public FilterSecurityInterceptor getFilterSecurityInterceptor() throws Exception{
		return new MyFilterSecurityInterceptor(securityMetadataSource,accessDecisionManager,authenticationManagerBean());
	}
	
	/**
	 * @fix:自定义Filter通过@Bean注解后，被Spring Boot自动注册到了容器的Filter chain中，这样导致的结果是，所有URL都会被自定义Filter过滤，而不是security中配置的一部分URL，此处取消自动注册
	 * */
	@Bean
    public FilterRegistrationBean<Filter> cancelSecurityFilterRegistration(@Qualifier("filterSecurityInterceptor") FilterSecurityInterceptor filter) {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<Filter>(filter);
        registration.setEnabled(false);
        return registration;
    }
	
	
	/**
	 * 初始化身份认证过滤器并交给spring容器管理
	 * <P>
	 * 调用该方法可以从spring容器中获取相应的bean
	 * @throws Exception 
	 */
	@Bean(name = "myUsernamePasswordAuthenticationFilter")
	public UsernamePasswordAuthenticationFilter getUsernamePasswordAuthenticationFilter(
			@Qualifier(value = "authenticationSuccessHandler") AuthenticationSuccessHandler authenticationSuccessHandler,
			@Qualifier(value = "authenticationFailureHandler") AuthenticationFailureHandler authenticationFailureHandler) throws Exception {
		MyUsernamePasswordAuthenticationFilter usernamePasswordAuthenticationFilter = new MyUsernamePasswordAuthenticationFilter();
		usernamePasswordAuthenticationFilter.setAuthenticationManager(authenticationManagerBean());
		usernamePasswordAuthenticationFilter.setAuthenticationFailureHandler(authenticationFailureHandler);
		usernamePasswordAuthenticationFilter.setAuthenticationSuccessHandler(authenticationSuccessHandler);
		usernamePasswordAuthenticationFilter.setSessionAuthenticationStrategy(sessionAuthenticationStrategy());//用于session控制策略
		usernamePasswordAuthenticationFilter.setFilterProcessesUrl("/j_spring_security_check");
		usernamePasswordAuthenticationFilter.setPostOnly(false);
		return usernamePasswordAuthenticationFilter;
	}
	
	/**
	 * @fix:自定义Filter通过@Bean注解后，被Spring Boot自动注册到了容器的Filter chain中，这样导致的结果是，所有URL都会被自定义Filter过滤，而不是security中配置的一部分URL，此处取消自动注册
	 * */
	@Bean
    public FilterRegistrationBean<Filter> cancelAuthenticationFilterRegistration(@Qualifier("myUsernamePasswordAuthenticationFilter") UsernamePasswordAuthenticationFilter filter) {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<Filter>(filter);
        registration.setEnabled(false);
        return registration;
    }
	
	/**
	 * session注册器
	 * FIXME 考虑集群环境
	 * */
	@Bean(value="mySessionRegistry")
	public SessionRegistry SessionRegistry() {
		if(securityProperties.isSessionStoreRedis()) {
			return new RedisSessionRegistryImpl(securityProperties.getSessionStoreKey());
		}else {
			return new SessionRegistryImpl();
		}
	}
	
	/**
	 * session认证策略
	 * */
	@Bean(value="sas")
	public SessionAuthenticationStrategy sessionAuthenticationStrategy() {
		List<SessionAuthenticationStrategy> delegateStrategies = new ArrayList<SessionAuthenticationStrategy>();
		//创建session会话数策略
		ConcurrentSessionControlAuthenticationStrategy concurrentSessionControlAuthenticationStrategy = bulidConcurrentSessionControlAuthenticationStrategy();
		concurrentSessionControlAuthenticationStrategy.setMaximumSessions(securityProperties.getMaximumSessions());
		delegateStrategies.add(concurrentSessionControlAuthenticationStrategy);//session会话数策略
		//FIXME 该策略会导致sessionId频繁变动，会带来意想不到后果，如：单点登出销毁时无法销毁
//		delegateStrategies.add(new SessionFixationProtectionStrategy());//防止session会话固定攻击策略
		delegateStrategies.add(new RegisterSessionAuthenticationStrategy(SessionRegistry()));//注册session策略
		return new CompositeSessionAuthenticationStrategy(delegateStrategies);
	}
	
	/**
	 * 构造session并发数控制策略
	 * */
	private ConcurrentSessionControlAuthenticationStrategy bulidConcurrentSessionControlAuthenticationStrategy() {
		ConcurrentSessionControlAuthenticationStrategy concurrentSessionControlAuthenticationStrategy = null;
		if(securityProperties.isSessionStoreRedis()) {
			concurrentSessionControlAuthenticationStrategy = new RedisConcurrentSessionControlAuthenticationStrategy(SessionRegistry(),securityProperties.getSessionControlIgnores());
		}else {
			concurrentSessionControlAuthenticationStrategy = new ConcurrentSessionControlAuthenticationStrategy(SessionRegistry());
		}
		return concurrentSessionControlAuthenticationStrategy;
	}
	
	/**
	 * session 并发控制过滤器
	 * <P>如果用户被挤下线而失效，则会触发登出操作并跳转到登录页
	 * FIXME 考虑ajax访问
	 * */
	@Bean
	public ConcurrentSessionFilter concurrentSessionFilter() {
		ConcurrentSessionFilter concurrentSessionFilter = new ConcurrentSessionFilter(SessionRegistry(),new RedirectWithAjaxSessionInformationExpiredStrategy(securityProperties.getExpiredUrl()));
		return concurrentSessionFilter;
	}
	
	/**
	 * @fix:自定义Filter通过@Bean注解后，被Spring Boot自动注册到了容器的Filter chain中，这样导致的结果是，所有URL都会被自定义Filter过滤，而不是security中配置的一部分URL，此处取消自动注册
	 * */
	@Bean
    public FilterRegistrationBean<Filter> cancelSessionFilterRegistration(@Qualifier("concurrentSessionFilter") ConcurrentSessionFilter filter) {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<Filter>(filter);
        registration.setEnabled(false);
        return registration;
    }
	
	/**
	 * session 监听器
	 * <P>用于session失效通知SessionRegistry移除失效的session
	 * */
	@Bean
	public HttpSessionEventPublisher httpSessionEventPublisher() {
	    return new HttpSessionEventPublisher();
	}
	
	/**
	 * 获取身份认证管理者
	 * */
	@Override
    public AuthenticationManager authenticationManagerBean() {
        AuthenticationManager authenticationManager = null;
        try {
            authenticationManager = this.authenticationManager();
        } catch (Exception e) {
        	logger.error( "CustomWebSecurityConfigurer.authenticationManagerBean  error: {}",e);
        }
        return authenticationManager;
    }
	
	/**
	 * 身份认证提供者
	 * */
	@Bean(name="myAuthenticationProvider")
	public AuthenticationProvider authenticationProvider( @Qualifier(value="myUserDetailsService")UserDetailsService userDetailsService
			,@Qualifier(value="myPreAuthenticationChecks")AccountStatusUserDetailsChecker preAuthenticationChecks
			,@Qualifier(value="passwordEncoder")PasswordEncoder passwordEncoder) {
		MyAuthenticationProvider authenticationProvider = new MyAuthenticationProvider();
		authenticationProvider.setUserDetailsService(userDetailsService);
		authenticationProvider.setPreAuthenticationChecks(preAuthenticationChecks);
		authenticationProvider.setPasswordEncoder(passwordEncoder);
		return authenticationProvider;
	}
	
	@Bean(name="passwordEncoder")
	public PasswordEncoder passwordEncoder() throws InstantiationException, IllegalAccessException, ClassNotFoundException {
		return (PasswordEncoder)Class.forName(securityProperties.getPasswordEncoder()).newInstance();
	}
	
	/**
	 * 自定义权限证过滤器
	 * */
	static class MyFilterSecurityInterceptor extends FilterSecurityInterceptor {
		public MyFilterSecurityInterceptor(FilterInvocationSecurityMetadataSource securityMetadataSource, AccessDecisionManager accessDecisionManager, AuthenticationManager authenticationManager){
            this.setSecurityMetadataSource(securityMetadataSource);
            this.setAccessDecisionManager(accessDecisionManager);
            this.setAuthenticationManager(authenticationManager);
        }
	}
	
}
