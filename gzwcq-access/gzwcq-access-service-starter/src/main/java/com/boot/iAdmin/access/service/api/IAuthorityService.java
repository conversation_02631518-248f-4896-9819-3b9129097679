package com.boot.iAdmin.access.service.api;

import java.util.Collection;
import java.util.List;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.authority.AuthVo;
import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.model.user.SysUser;

/**
 * 权限接口
 * */
public interface IAuthorityService {
	
	/**
	 * 获取所有权限
	 * */
	Collection<SysAuthorities> getAllAuth();
	
	/**条件查询*/
	Collection<SysAuthorities> getAuth(SysAuthorities sysAuthorities);
	
	/**
	 * 根据角色ID查询权限
	 * */
	Collection<SysAuthorities> getAuthByRoleId(Long role_id);
	
	/**
	 * 分页查询权限
	 * */
	BootstrapTableModel<AuthVo> loadAuthByPage(BootstrapTableModel<AuthVo> pageModel);
	
	/**逻辑删除权限*/
	void disableAuthById(Long authId);
	
	/**权限恢复*/
	void recoverAuthById(Long authId);
	
	/**权限物理删除*/
	void deleteAuthByIds(String authIds);
	
	/**新增权限
	 * 
	 * @param resourceIds 资源ID
	 * */
	void insertAuth(SysAuthorities auth, String resourceIds);
	
	/**
	 * 根据权限ID获取权限
	 * */
	SysAuthorities selectAuthById(SysAuthorities auth);
	
	/**
	 * 修改权限
	 * @param auth 权限信息
	 * @param selResourcesIds 所选中的资源ID
	 * @param allResourcesIds 所涉及到的资源ID
	 * */
	void updateAuth(SysAuthorities auth, String allResourcesIds, String selResourcesIds);
	
	/**
	 * 校验权限名称是否重复
	 * @param auth
	 * @return
	 */
	abstract List<String> validateAuthorityName(SysAuthorities auth);
	
	/**
	 * 根据权限信息判断被用户的引用
	 * @param authIds
	 * @return
	 */
	abstract List<SysUser> judgeDeleteAuthsByAuthIds(String authIds);

}
