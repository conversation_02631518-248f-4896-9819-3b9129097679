package com.boot.iAdmin.appInfo.service.api;

import java.util.List;
import java.util.Map;

import com.boot.iAdmin.appInfo.entity.AppInfo;

public interface IAppInfoService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(AppInfo appInfo);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(Map<String, Object> map);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(AppInfo appInfo);
	
	/**
	* 更新
	*/
	void update(AppInfo appInfo);
	
	/**
	 *通过ID查询数据
	 */
	AppInfo selectAppInfoByPrimaryKey(AppInfo appInfo);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<AppInfo> selectForList(AppInfo appInfo);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(AppInfo appInfo);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(AppInfo appInfo);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(AppInfo[] objs);
	
	/**
	 * 根据应用编号获取第三方应用信息
	 * */
	AppInfo selectAppInfoByAppKey(String appId);
	
}