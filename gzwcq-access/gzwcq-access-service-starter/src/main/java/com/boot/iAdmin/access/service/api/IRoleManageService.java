package com.boot.iAdmin.access.service.api;

import java.util.Collection;
import java.util.List;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.role.Role;
import com.boot.iAdmin.access.model.role.RoleVo;
import com.boot.iAdmin.access.model.user.SysUser;

public interface IRoleManageService {

	BootstrapTableModel<RoleVo> loadRoleByPage(BootstrapTableModel<RoleVo> model);

	void disableRoleById(Long roleId);

	void recoverRoleById(Long roleId);
	
	/**
	 * 用户角色方法
	 * <P>物理删除
	 * @param roleIds 用户主键集合
	 * */
	void deleteRoleByIds(String roleIds);
	
	/**
	 * 新增角色
	 * */
	void insertRole(Role role, String[] auths);
	
	/**
	 * 查询角色
	 * */
	Role getRole(Role role);
	
	/**
	 * 查询所有角色
	 * */
	Collection<Role> selectAll();
	
	/**
	 * 根据用户ID查询所拥有角色集合
	 * */
	Collection<Role> selectRolesByUserId(String user_id);
	
	/**
	 * 更新角色信息
	 * */
	void updateRole(Role role, String[] auths);
	
	/**
	 * 角色名称新增或者修改的时候--判断是否已经存在相同的名称
	 * @param role	依据角色的名称和ID来进行判断
	 * @return
	 */
	abstract List<String> validateRoleName(Role role);
	
	abstract List<SysUser> judgeDeleteRolesByRolesIds(String roleIds);
	
	@Deprecated
	Object loadRolesByCompany(Long user_id, Long company);

	List<String> validateRoleCode(Role role);
}
