package com.boot.iAdmin.access.service.api;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.boot.iAdmin.access.model.loginLog.LoginLog;
import com.boot.iAdmin.access.model.loginLog.LoginLogParam;
import com.boot.iAdmin.access.model.loginLog.LoginLogVo;
import com.boot.iAdmin.access.model.user.SysUser;

public interface ILoginLogService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(LoginLog loginLog);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(Map<String, Object> map);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(LoginLog loginLog);
	
	/**
	* 更新
	*/
	void update(LoginLog loginLog);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<LoginLogVo> queryLoginLogByPage(LoginLogParam loginLogParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalLoginLogs(LoginLogParam loginLogParam);
  
	
	/**
	 *通过ID查询数据
	 */
	LoginLog selectLoginLogByPrimaryKey(LoginLog loginLog);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<LoginLog> selectForList(LoginLog loginLog);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(LoginLog loginLog);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(LoginLog loginLog);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(LoginLog[] objs);

	void saveLoginLog(SysUser sysUser, HttpServletRequest request, String type);
	
}