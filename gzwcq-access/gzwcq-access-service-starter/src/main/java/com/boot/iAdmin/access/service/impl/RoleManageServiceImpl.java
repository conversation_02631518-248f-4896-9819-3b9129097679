package com.boot.iAdmin.access.service.impl;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.mapper.RoleManageMapper;
import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.role.Role;
import com.boot.iAdmin.access.model.role.RoleVo;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IRoleAndAuthService;
import com.boot.iAdmin.access.service.api.IRoleManageService;
import com.boot.iAdmin.access.service.api.IUserAndRoleService;

@Service
public class RoleManageServiceImpl implements IRoleManageService{
	
	@Autowired
	private RoleManageMapper roleManageMapper;
	
	@Autowired
	private SysUserInfoMapper sysUserInfoMapper;
	
	@Autowired
	private IRoleAndAuthService roleAndAuthService;
	
	@Autowired
	private IUserAndRoleService userAndRoleService;
	
	/**
	 * 分页查询角色信息
	 * */
	public BootstrapTableModel<RoleVo> loadRoleByPage(BootstrapTableModel<RoleVo> model) {
		return model.addRows(roleManageMapper.loadRoleByPage(model)).addTotals(roleManageMapper.loadTotalRoles(model));
	}
	
	/**
	 * 逻辑删除角色
	 * @param roleId 角色ID
	 * */
	
	public void disableRoleById(Long roleId) {
		Role role = new Role(roleId);
		role.setEnable(0);
		roleManageMapper.updateRoleIgnoreNull(role);
	}
	
	/**
	 * 恢复角色
	 * */
	
	public void recoverRoleById(Long roleId) {
		Role role = new Role(roleId);
		role.setEnable(1);
		roleManageMapper.updateRoleIgnoreNull(role);
	}
	
	/**
	 * 用户角色方法
	 * <P>物理删除
	 * <P>同时删除角色与用户关联关系，角色与权限关联关系等冗余数据
	 * @param roleIds 用户主键集合
	 * */
	
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void deleteRoleByIds(String roleIds) {
		//TODO 删除用户角色关联关系
		userAndRoleService.deleteUserAndRoleByRoleIds(roleIds);
		//TODO 删除角色权限关联关系
		roleAndAuthService.deleteRoleAndResourceByRoleIds(roleIds);
		//删除角色
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("roleIds",roleIds.split(","));
		roleManageMapper.deleteRoleByIds(map);
	}
	
	/**
	 * 新增角色
	 * */
	public void insertRole(Role role, String[] auths) {
		roleManageMapper.insertRole(role);
		roleAndAuthService.updateRoleAndAuthsWithOneRole(role,auths);
	}
	
	/**
	 * 查询角色
	 * */
	
	public Role getRole(Role role) {
		return roleManageMapper.getRole(role);
	}
	
	/**
	 * 查询所有角色
	 * */
	
	public Collection<Role> selectAll() {
		return roleManageMapper.selectAll();
	}
	
	/**
	 * 查询用户的角色
	 * */
	
	public Collection<Role> selectRolesByUserId(String user_id) {
		return roleManageMapper.selectRolesByUserId(user_id);
	}
	
	/**
	 * 更新角色信息
	 * 
	 * @param role 角色
	 * @param auths 权限
	 * */
	@Transactional(rollbackFor=Exception.class,propagation=Propagation.REQUIRED)
	
	public void updateRole(Role role, String[] auths) {
		//更新基本信息
		this.updateRoleIgnoreNull(role);
		//更新角色权限关联关系
		roleAndAuthService.updateRoleAndAuthsWithOneRole(role,auths);
	}
	
	/**
	 * 更新角色信息忽略空字段
	 * */
	private void updateRoleIgnoreNull(Role role) {
		roleManageMapper.updateRoleIgnoreNull(role);
	}
	
	
	public List<String> validateRoleName(Role role){
		return roleManageMapper.validateRoleName(role);
	}
	
	public List<String> validateRoleCode(Role role){
		return roleManageMapper.validateRoleCode(role);
	}
	
	public List<SysUser> judgeDeleteRolesByRolesIds(String roleIds){
		return sysUserInfoMapper.loadUserByRoleIDs(roleIds);
	}
	
	/**
	 * 根据公司获取用户角色
	 * */
	@Override
	public List<Role> loadRolesByCompany(Long user_id, Long company) {
		Map<String,Object> paramsMap = new HashMap<String,Object>();
		paramsMap.put("user_id", user_id);
		paramsMap.put("company", company);
		return roleManageMapper.loadRolesByCompany(paramsMap);
	}
	
}
