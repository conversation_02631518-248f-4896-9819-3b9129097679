package com.boot.iAdmin.access.model.common;

import java.util.Collection;
import java.util.List;

/**
 * jquery.zTree 节点对象
 * 
 * <AUTHOR>
 * @date 2016年9月20日 15:00:29
 * */
public class ZTreeNode {
	
	// ID
	protected String id;
	// 节点名称
	protected String name;
	// 节点名称
	protected String title;
	// 子集
	protected Collection<ZTreeNode> children;
	// 直接子节点集合
	protected String childrenIdS;
	// 是否是父节点
	protected boolean isParent;
	//子节点数目
	protected Long childNum;
	//是否选中
	protected boolean checked;
	//描述
	protected String description;
	//编码
	protected String code;
	protected String sjzb; //实缴资本

	protected String czr; //出资人
	protected String zcd; //注册地
	protected String zcrq; //注册日期
	protected String qylb; //企业类别
	protected String parentBl; //父节点注资比例
	protected String zyhy; //主要行业
	
	protected boolean showCheckBox;//自定义属性--是否需要在ztree上面展示chekckbox框(全局设置的会导致页面ztree全部checkbox展示或者消失)

	protected String orgLevel;//企业级次

	protected String parents;//组织所有父节点id，逗号隔开

	protected String visibles;//可见组织

	protected String audit_hosting; //审核托管组织

	protected String xyCode;//信用代码

	protected String code_name;//组织机构代码+名称

	protected String businessType;

	protected String SSGZJGJG; //所属国资监管机构/6位区域码

	protected String createUserName; //创建人名称

	protected List<ZTreeNode> zTreeNodeList;//需要导出的子集

	protected String parentId;//直接父节点

	protected int nodeLevele;//节点等级

	protected Boolean unitJumpMark; //企业跳转标识

	protected String jumpJbxxbId; //跳转企业基本信息id

	public String getSSGZJGJG() {
		return SSGZJGJG;
	}

	public void setSSGZJGJG(String SSGZJGJG) {
		this.SSGZJGJG = SSGZJGJG;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getCode_name() {
		return code+" "+name;
	}

	public void setCode_name(String code_name) {
		this.code_name = code+" "+name;
	}

	public String getXyCode() {
		return code == null? "":code.substring(code.indexOf("_")+1);
	}

	public void setXyCode(String xyCode) {
		this.xyCode = code == null? "":code.substring(code.indexOf("_")+1);
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Collection<ZTreeNode> getChildren() {
		return children;
	}

	public void setChildren(Collection<ZTreeNode> children) {
		this.children = children;
	}

	public boolean getIsParent() {
		return isParent;
	}

	public void setIsParent(boolean isParent) {
		this.isParent = isParent;
	}

	public Long getChildNum() {
		return childNum;
	}

	public void setChildNum(Long childNum) {
		this.childNum = childNum;
		//判断是否为父节点
		this.setIsParent(childNum.longValue()>0);
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public boolean isShowCheckBox() {
		return showCheckBox;
	}

	public void setShowCheckBox(boolean showCheckBox) {
		this.showCheckBox = showCheckBox;
	}

	public String getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(String orgLevel) {
		this.orgLevel = orgLevel;
	}

	public String getParents() {
		return parents;
	}

	public void setParents(String parents) {
		this.parents = parents;
	}

	public String getVisibles() {
		return visibles;
	}

	public void setVisibles(String visibles) {
		this.visibles = visibles;
	}

	public String getAudit_hosting() {
		return audit_hosting;
	}

	public void setAudit_hosting(String audit_hosting) {
		this.audit_hosting = audit_hosting;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public List<ZTreeNode> getzTreeNodeList() {
		return zTreeNodeList;
	}

	public void setzTreeNodeList(List<ZTreeNode> zTreeNodeList) {
		this.zTreeNodeList = zTreeNodeList;
	}

	public int getNodeLevele() {
		return nodeLevele;
	}

	public void setNodeLevele(int nodeLevele) {
		this.nodeLevele = nodeLevele;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getChildrenIdS() {
		return childrenIdS;
	}

	public void setChildrenIdS(String childrenIdS) {
		this.childrenIdS = childrenIdS;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getSjzb() {
		return sjzb;
	}

	public void setSjzb(String sjzb) {
		this.sjzb = sjzb;
	}

	public String getCzr() {
		return czr;
	}

	public void setCzr(String czr) {
		this.czr = czr;
	}

	public String getZcd() {
		return zcd;
	}

	public void setZcd(String zcd) {
		this.zcd = zcd;
	}

	public String getZcrq() {
		return zcrq;
	}

	public void setZcrq(String zcrq) {
		this.zcrq = zcrq;
	}

	public String getQylb() {
		return qylb;
	}

	public void setQylb(String qylb) {
		this.qylb = qylb;
	}

	public String getParentBl() {
		return parentBl;
	}

	public void setParentBl(String parentBl) {
		this.parentBl = parentBl;
	}

	public String getZyhy() {
		return zyhy;
	}

	public void setZyhy(String zyhy) {
		this.zyhy = zyhy;
	}

	public Boolean getUnitJumpMark() {
		return unitJumpMark;
	}

	public void setUnitJumpMark(Boolean unitJumpMark) {
		this.unitJumpMark = unitJumpMark;
	}

	public String getJumpJbxxbId() {
		return jumpJbxxbId;
	}

	public void setJumpJbxxbId(String jumpJbxxbId) {
		this.jumpJbxxbId = jumpJbxxbId;
	}
}
