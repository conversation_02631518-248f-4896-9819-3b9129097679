package com.boot.iAdmin.access.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.boot.IAdmin.common.utils.IPUtil;
import com.boot.iAdmin.access.mapper.ILoginLogMapper;
import com.boot.iAdmin.access.model.loginLog.LoginLog;
import com.boot.iAdmin.access.model.loginLog.LoginLogParam;
import com.boot.iAdmin.access.model.loginLog.LoginLogVo;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.ILoginLogService;
import com.github.pagehelper.PageHelper;

@Service
public class LoginLogServiceImpl implements ILoginLogService {
	
	@Autowired
	private ILoginLogMapper loginLogMapper;
	
  	@Override
	public void insert(LoginLog loginLog){
		loginLogMapper.insert(loginLog);
	}
	
    @Override
	public void deleteByPrimaryKeys(Map<String,Object> map){
		loginLogMapper.logicDeleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
	public void deleteByPrimaryKey(Map<String, Object> map) {
		loginLogMapper.logicDeleteByPrimaryKey(map);
	}
	
  	@Override
	public void updateIgnoreNull(LoginLog loginLog){
		loginLogMapper.updateIgnoreNull(loginLog);
	}
	
	/**
	* 更新
	*/
  	@Override
	public void update(LoginLog loginLog){
		loginLogMapper.update(loginLog);
	}
	
  	@Override
	public List<LoginLogVo> queryLoginLogByPage(LoginLogParam loginLogParam) {
      	//分页
      	PageHelper.startPage(loginLogParam.getPageNumber(),loginLogParam.getLimit());
		return loginLogMapper.queryLoginLogForList(loginLogParam);
	}
	
	@Override
	public LoginLog selectLoginLogByPrimaryKey(LoginLog LoginLog) {
		return loginLogMapper.selectLoginLogByPrimaryKey(LoginLog);
	}
	@Override
	public long queryTotalLoginLogs(LoginLogParam loginLogParam) {
		return loginLogMapper.queryTotalLoginLogs(loginLogParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@Override
	public List<LoginLog> selectForList(LoginLog loginLog){
		return loginLogMapper.selectForList(loginLog);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(LoginLog loginLog) {
		return loginLogMapper.selectForUnique(loginLog).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
	public void saveOne(LoginLog loginLog) {
		if(loginLog.getId() == null) {
			this.insert(loginLog);
		}else {
			this.updateIgnoreNull(loginLog);
		}
	}
	
	/**
	 * 保存多个用户
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void multipleSaveAndEdit(LoginLog[] objs) {
		for(LoginLog loginLog : objs) {
			this.saveOne(loginLog);
		}
	}
	
	/**
	 * 保存登录日志
	 * */
	@Override
	public void saveLoginLog(SysUser loginUser,HttpServletRequest request, String type) {
		LoginLog loginLog = new LoginLog();
		loginLog.setLoginTime(new Date());
		loginLog.setName(loginUser.getName());
		loginLog.setOrgId(loginUser.getCurrOrgId());
		loginLog.setOrgName(loginUser.getCurrOrgName());
		loginLog.setUsername(loginUser.getUsername());
		loginLog.setType(type);//登录类型
		loginLog.setIp(IPUtil.getRemoteHost(request));//IP
		this.insert(loginLog);
	}
	
}
