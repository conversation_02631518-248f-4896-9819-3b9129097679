package com.boot.iAdmin.access.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.role.Role;
import com.boot.iAdmin.access.model.role.RoleVo;

public interface RoleManageMapper {

	void updateRoleById(Role role);

	void updateRoleIgnoreNull(Role role);

	void deleteRoleByIds(Map<String, Object> map);

	void insertRole(Role role);

	Role getRole(Role role);
	
	List<Role> getRoleLists(Role role);

	Collection<Role> selectAll();

	Collection<Role> selectRolesByUserId(String user_id);
	
	Collection<RoleVo> loadRoleByPage(BootstrapTableModel<RoleVo> model);

	long loadTotalRoles(BootstrapTableModel<RoleVo> model);
	
	/**
	 * 校验角色名称的唯一性
	 * @param role
	 * @return
	 */
	List<String> validateRoleName(Role role);
	
	/**
	 * 根据公司获取用户角色
	 * */
	List<Role> loadRolesByCompany(Map<String, Object> paramsMap);

	List<String> validateRoleCode(Role role);

}
