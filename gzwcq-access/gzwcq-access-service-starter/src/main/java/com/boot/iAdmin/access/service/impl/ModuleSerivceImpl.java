package com.boot.iAdmin.access.service.impl;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityMetadataSource;
import org.springframework.security.access.vote.AffirmativeBased;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.FilterInvocation;
import org.springframework.stereotype.Service;

import com.boot.iAdmin.access.mapper.ModuleMapper;
import com.boot.iAdmin.access.model.module.CustomModuleTree;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IModuleService;

/**
 * 菜单服务实现类
 * */
@Service
public class ModuleSerivceImpl implements IModuleService {
	
	Log logger = LogFactory.getLog(ModuleSerivceImpl.class);

	@Autowired
	private ModuleMapper moudleMapper;
	
	@Autowired
	private SecurityMetadataSource securityMetadataSource;
	
	@Resource(name="accessDecisionManager")
	private AffirmativeBased accessDecisionManager;
	
	//超级管理员用户名
	private static final String SUPER_ADMIN = "admin";
	//菜单访问方式
	private static final String MENU_URI_METHOD = "GET";

	/**
	 * 加载当前登录用户所拥有的菜单树
	 * 
	 * @param loginUser
	 *            当前用户
	 * @return
	 * */
	public Object loadModuleTreeByUser(SysUser loginUser) {
		List<CustomModuleTree> modules = moudleMapper.selectAllModules();
		if(SUPER_ADMIN.equals(loginUser.getUsername())){//获取所有菜单
//			modules =  moudleMapper.selectAllModules();
		}else{
			//获取用户菜单
//			modules =  moudleMapper.getAllModulesByUser(loginUser);
			//获取用户菜单,保留用户可访问的菜单，规则同spring security
			Iterator<CustomModuleTree> it = modules.iterator();
			while(it.hasNext()){
				CustomModuleTree menu = it.next();
				if(StringUtils.isBlank(menu.getModule_url())) continue;
				String url = menu.getModule_url();
				if(url.lastIndexOf("?") > -1) url = url.substring(0,url.lastIndexOf("?"));
				Collection<ConfigAttribute> attrs = securityMetadataSource.getAttributes(new FilterInvocation(url,MENU_URI_METHOD));
				try{
					//验证该菜单地址是否有访问权限，如果没有则抛异常
					accessDecisionManager.decide(SecurityContextHolder.getContext().getAuthentication(), null, attrs);
				}catch(Exception e){//该菜单无访问权限，不展示
					it.remove();
				}
			}
		}
		
		return new CustomModuleTree(modules);
	}
	
	/**
	 * 获取用户所有菜单列表
	 * */
	@Override
	public Object loadModulesByUser(SysUser loginUser) {
		List<CustomModuleTree> modules = moudleMapper.selectAllModules();
		if(!SUPER_ADMIN.equals(loginUser.getUsername())){//获取所有菜单
			//获取用户菜单,保留用户可访问的菜单，规则同spring security
			Iterator<CustomModuleTree> it = modules.iterator();
			while(it.hasNext()){
				CustomModuleTree menu = it.next();
				if(StringUtils.isBlank(menu.getModule_url())) continue;
				String url = menu.getModule_url();
				if(url.lastIndexOf("?") > -1) url = url.substring(0,url.lastIndexOf("?"));
				Collection<ConfigAttribute> attrs = securityMetadataSource.getAttributes(new FilterInvocation(url,MENU_URI_METHOD));
				try{
					//验证该菜单地址是否有访问权限，如果没有则抛异常
					accessDecisionManager.decide(SecurityContextHolder.getContext().getAuthentication(), null, attrs);
				}catch(Exception e){//该菜单无访问权限，不展示
					it.remove();
				}
			}
		}
		return modules;
	}
	
}
