package com.boot.iAdmin.access.aop;

import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.catalina.connector.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.IPUtil;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.boot.IAdmin.common.utils.UUIDGenerator;
import com.boot.iAdmin.access.common.ControllerTools;
import com.boot.iAdmin.access.model.common.Constant;

/**
 * 重复提交aop
 * <AUTHOR>
 * @version
 * @since
 */
@Aspect
@Component
@SuppressWarnings("all")
public class AvoidRepeatableCommitAspect {
	
	private Log logger = LogFactory.getLog(this.getClass());

	@Autowired(required= false)
    private RedisTemplate redisTemplate;

    /**
     * @param point
     */
    @Around("@annotation(com.boot.IAdmin.common.aop.AvoidRepeatableCommit)")
    public Object aroundAvoidRepeatableCommit(ProceedingJoinPoint point) throws Throwable {
        HttpServletRequest request  = ((ServletRequestAttributes)RequestContextHolder.currentRequestAttributes()).getRequest();
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        String ip = IPUtil.getIpAddress(request);
        //获取注解
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        //目标类、方法
        String className = method.getDeclaringClass().getName();
        String methodName = method.getName();
        String ipKey = String.format("%s#%s",className,methodName);
        int hashCode = Math.abs(ipKey.hashCode());
        String key = String.format("%s_%d",ip,hashCode);
        AvoidRepeatableCommit avoidRepeatableCommit =  method.getAnnotation(AvoidRepeatableCommit.class);
        long timeout = avoidRepeatableCommit.timeout();
        String value = (String) redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(value)){
        	redisTemplate.opsForValue().set(key, UUIDGenerator.generate(),timeout,TimeUnit.MILLISECONDS);//更新标识，防止连续长时间点击
        	/*if(ControllerTools.isAjaxRequest(request)) {
        		ControllerTools.print(response, Constant.REPEATSUMIT,488);
        	}else {
        		response.setHeader("errorCode", Constant.REPEATSUMIT);
    			response.setStatus(488);//设置错误码
            	PrintWriter out = response.getWriter();
    			out.write("请勿重复提交");
    			out.flush();
        	}*/
            ResponseEnvelope re = new ResponseEnvelope();
            re.setSuccess(false);
            re.setResult(null);
            re.setMessage("请勿重复提交");
        	logger.warn(String.format("============IP为%s的用户请求%s.%s属于表单重复提交，被拦截==============", ip,className,methodName));
        	return re;
        }
        redisTemplate.opsForValue().set(key, UUIDGenerator.generate(),timeout,TimeUnit.MILLISECONDS);//设置标识，防止重复提交
        return point.proceed();
    }

}
