package com.boot.iAdmin.access.common;

import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import com.boot.iAdmin.access.model.user.SysUser;

/**
 * 用户工具
 * <P>
 * 主要用于获取登录用户信息
 * 
 * <AUTHOR>
 * @date 2017年10月27日 10:27:13
 */
public class SpringSecurityUserTools {
	
	//单例
	private static SpringSecurityUserTools springSecurityUserTools;

	private final Log logger = LogFactory.getLog(this.getClass());
	
	//私有构造器
	private SpringSecurityUserTools() {};
	
	//获取实例对象
	public static SpringSecurityUserTools instance() {
		if(springSecurityUserTools == null) {
			synchronized(SpringSecurityUserTools.class) {
				if(springSecurityUserTools == null) {
					springSecurityUserTools = new SpringSecurityUserTools();
				}
			}
		}
		return springSecurityUserTools;
	}

	/**
	 * 获取用户信息
	 */
	public Object getUser(HttpSession session) {
		Object obj = null;
		try {
			if (SecurityContextHolder.getContext() != null && SecurityContextHolder.getContext().getAuthentication() != null &&
					SecurityContextHolder.getContext().getAuthentication().getPrincipal() != null) {
				obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			} else {
				obj = getUserFromSession(session);
			}
		}catch (Exception e){
			logger.warn("获取当前登录用户信息失败",e);
		}
		if(obj == null) {
			obj = new SysUser();
		}
		return obj;
	}

	/**
	 * 从session中获取当前用户登录信息
	 */
	private Object getUserFromSession(HttpSession httpSession) {

		final boolean debug = logger.isDebugEnabled();

		if (httpSession == null) {
			if (debug) {
				logger.debug("No HttpSession currently exists");
			}
			return null;
		}
		// Session exists, so try to obtain a context from it.
		Object contextFromSession = httpSession.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
		if (contextFromSession == null) {
			if (debug) {
				logger.debug("HttpSession returned null object for SPRING_SECURITY_CONTEXT");
			}
			return null;
		}
		// We now have the security context object from the session.
		if (!(contextFromSession instanceof SecurityContext)) {
			if (logger.isWarnEnabled()) {
				logger.warn(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY + " did not contain a SecurityContext but contained: '" + contextFromSession
						+ "'; are you improperly modifying the HttpSession directly " + "(you should always use SecurityContextHolder) or using the HttpSession attribute "
						+ "reserved for this class?");
			}
			return null;
		}
		if (debug) {
			logger.debug("Obtained a valid SecurityContext from " + HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY + ": '" + contextFromSession + "'");
		}
		// Everything OK. The only non-null return from this method.
		return ((SecurityContext) contextFromSession).getAuthentication().getPrincipal();

	}
}
