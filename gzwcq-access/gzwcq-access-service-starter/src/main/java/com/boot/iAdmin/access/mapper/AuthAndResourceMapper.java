package com.boot.iAdmin.access.mapper;

import java.util.Map;

import com.boot.iAdmin.access.model.authAndResource.AuthAndResource;

public interface AuthAndResourceMapper {

	/** 新增权限资源关联关系 */
	void insertAuthAndResource(AuthAndResource authAndResource);

	/**
	 * 根据权限ID和所涉及到的资源集合，删除在当前资源集合中的当前权限资源关联关系
	 * */
	void deleteAuthAndResourceInCurrResourcesIds(Map<String, Object> map);

	/**
	 * 根据权限ID删除权限资源关联关系
	 * */
	void deleteAuthAndResourceByAuthIds(Map<String, Object> map);

}
