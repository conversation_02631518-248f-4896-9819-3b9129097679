package com.boot.iAdmin.access.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode;
import com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNodeExport;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.organization.OrganizationVo;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.organization.SysOrganizationParm;
import com.boot.iAdmin.access.model.role.Role;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IOrganizationService;
import com.boot.iAdmin.access.service.api.IRoleManageService;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrganizationService implements IOrganizationService {

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private SysUserInfoMapper userMapper;

    @Autowired
    private DictCacheStrategy dictCacheStrategy;

    @Autowired
    private IRoleManageService roleManageService;

    /**
     * 根据父节点ID加载所有子节点
     *
     * @param id 父节点ID
     */
    public Collection<ZTreeNode> loadOrganizationByParentId(String id) {
        return organizationMapper.loadOrganizationByParentId(id);
    }

    /**
     * 根据父节点ID加载所有子节点
     *
     * @param id 父节点ID
     */
    @Override
    public Collection<ZTreeNode> loadOrganizationByParentIdTwo(String id) {
        return organizationMapper.loadOrganizationByParentIdTwo(id);
    }

    /**
     * 根据父节点ID加载所有子节点(是否选中)
     *
     * @param id 父节点ID
     */
    public Collection<ZTreeNode> loadOrganizationTree(String id, String userId) {
        Collection<ZTreeNode> nodes = loadOrganizationByParentId(id);
        SysUser user = userMapper.getUserInfoByUserId(userId);
        String organizationId = String.valueOf(user.getOrganization_id());
        for (ZTreeNode zTreeNode : nodes) {
            if (organizationId.equals(zTreeNode.getId())) {
                zTreeNode.setChecked(true);
            }
        }
        return nodes;
    }

    /**
     * 获取单个组织信息
     */
    public SysOrganization selectOrganization(SysOrganization sysOrganization) {
        return organizationMapper.selectOrganization(sysOrganization);
    }

    /**
     * 获取单个组织信息
     */
    public SysOrganization selectOrganizationById(SysOrganization sysOrganization) {
        return organizationMapper.selectOrganizationById(sysOrganization);
    }

    /**
     * 更新组织信息
     */
    public void updateOrganization(SysOrganization organization) {
        organizationMapper.updateIgnoreNull(organization);
    }

    /**
     * 新增组织
     */
    public void insertOrganization(SysOrganization organization) {
        organizationMapper.insertOrganization(organization);
    }

    /**
     * 删除组织，包括其下子组织
     */
    public void deleteOrganization(SysOrganization organization) {
        organizationMapper.deleteOrganization(organization);
    }

    /**
     * 根据所传组织查询其以及其子组织是否被用户关联
     */
    public int getTotalOrganization(SysOrganization organization) {
        return organizationMapper.getTotalOrganization(organization);
    }

    /**
     * 参数唯一性验证
     */
    public boolean validateUniqueParam(SysOrganization organization) {
        return organizationMapper.selectForUnique(organization).size() == 0;
    }

    @Override
    public Map<String, Object> selectMaxOrgCodeByParId(String parent_id) {
        return organizationMapper.selectMaxOrgCodeByParId(parent_id);
    }

    @Override
    public String getOrgNameById(String org_id) {
        return organizationMapper.getOrgNameById(org_id);
    }

    @Override
    public VueAntdTreeSelectNode getOrganizationTressOption(SysOrganization organization) {
        return new VueAntdTreeSelectNode(organizationMapper.getOrganizationList(organization), "0", "组织名称", false);
    }

    @Override
    public BootstrapTableModel<OrganizationVo> queryOrganizationByPage(BootstrapTableModel<OrganizationVo> bootModel) {
        return bootModel.addRows(organizationMapper.queryOrganizationByPage(bootModel)).addTotals(organizationMapper.queryTotalOrganizations(bootModel));
    }

    @Override
    public SysOrganization loadOneByOrgCode(String org_code) {
        return organizationMapper.loadOneByOrgCode(org_code);
    }

    /**
     * 根据多个组织ID获取组织列表
     */
    @Override
    public List<SysOrganization> getOrgsByIds(Map<String, Object> queryMap) {
        return organizationMapper.getOrgsByIds(queryMap);
    }

    @Override
    public VueAntdTreeSelectNode getChildOrgTree(String rootId) {
        //获取根组织
        if (rootId != null) {
            SysOrganization rootOrg = this.selectOrganizationById(new SysOrganization(rootId));
            return new VueAntdTreeSelectNode(organizationMapper.getCurrAndChildList(rootId), String.valueOf(rootId), rootOrg.getOrganization_name(), true);
        } else {
            //根组织ID不存在，则取默认根节点
            return new VueAntdTreeSelectNode(organizationMapper.getCurrAndChildList(Constants.ROOT_ORG), Constants.ROOT_ORG, this.getOrgNameById(Constants.ROOT_ORG), false);
        }
    }

    /**
     * 根据当前用户组织获取本级及以下组织
     */
    @Override
    public VueAntdTreeSelectNode getChildOrgTreeByUserId() {
        //根组织ID不存在，则获取parent为0的组织为根组织
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        SysOrganization org = new SysOrganization();
        //如果拥有所有数据权限则根节点设为1L
        org.setOrganization_id(loginUser.getCurrOrgId());
        List<SysOrganization> orgs = organizationMapper.selectForList(org);
        if (orgs.size() == 1) {
            VueAntdTreeSelectNode vueAntdTreeSelectNode = new VueAntdTreeSelectNode(organizationMapper.getCurrAndChildList(orgs.get(0).getOrganization_id()), String.valueOf(orgs.get(0).getOrganization_id()), orgs.get(0).getOrganization_name(), false);
            //拿到首个节点需要的数据
            ZTreeNode exportData = organizationMapper.getExportData(vueAntdTreeSelectNode.getId());
            vueAntdTreeSelectNode.setSSGZJGJG(exportData.getSSGZJGJG());
            vueAntdTreeSelectNode.setCreateUserName(exportData.getCreateUserName());
            vueAntdTreeSelectNode.setParents(exportData.getParents());
            vueAntdTreeSelectNode.setParentId(exportData.getParentId());
            vueAntdTreeSelectNode.setChildrenIdS(exportData.getChildrenIdS());
            vueAntdTreeSelectNode.setCode(exportData.getCode());
            return vueAntdTreeSelectNode;
        } else {//根组织ID不存在，则取默认根节点
            VueAntdTreeSelectNode vueAntdTreeSelectNode = new VueAntdTreeSelectNode(organizationMapper.getCurrAndChildList(Constants.ROOT_ORG), Constants.ROOT_ORG, this.getOrgNameById(Constants.ROOT_ORG), false);
            ZTreeNode exportData = organizationMapper.getExportData(vueAntdTreeSelectNode.getId());
            vueAntdTreeSelectNode.setSSGZJGJG(exportData.getSSGZJGJG());
            vueAntdTreeSelectNode.setCreateUserName(exportData.getCreateUserName());
            vueAntdTreeSelectNode.setParents(exportData.getParents());
            vueAntdTreeSelectNode.setParentId(exportData.getParentId());
            vueAntdTreeSelectNode.setChildrenIdS(exportData.getChildrenIdS());
            vueAntdTreeSelectNode.setCode(exportData.getCode());
            return vueAntdTreeSelectNode;
        }
    }

    @Override
    public SysOrganization getOrgByCode(String code) {
        return organizationMapper.getOrgByCode(code);
    }

    @Override
    public List<Map> getAllOrg() {
        return organizationMapper.getAllOrg();
    }

    @Override
    public List<Map<String, Object>> getOrgAndLowerOrg(String organization_id) {
        return organizationMapper.getOrgAndLowerOrg(organization_id);
    }

    @Override
    public SysOrganization getOrgByOrgId(String organization_id) {
        return organizationMapper.getOrgByOrgId(organization_id);
    }

    @Override
    public List<Map> getOrgListEdit(String currOrgId) {
        return organizationMapper.getOrgListEdit(currOrgId);
    }

    /**
     * 组织编码查找组织名字
     *
     * @param str
     * @return
     */
    @Override
    public String getOrgNameByCode(String str) {
        return organizationMapper.getOrgNameByCode(str);
    }

    @Override
    public VueAntdTreeSelectNode getParentOrgTree(String orgId) {
        SysOrganization rootOrg = new SysOrganization();
        rootOrg.setOrganization_id(Constants.ROOT_ORG);
        VueAntdTreeSelectNode rootTreeNode = organizationMapper.getOrganizationTree(rootOrg);
        //组织树默认返回两级
        rootOrg.setOrganization_id(rootTreeNode.getId());
        List<VueAntdTreeSelectNode> nextChildTree = organizationMapper.getOrganizationListTree(rootOrg);
        rootTreeNode.setChildren(nextChildTree);
        if (orgId != null) {
            //有查询组织id，则查询到该组织的所有父级组织及同级组织
            SysOrganization org = new SysOrganization();
            org.setOrganization_id(orgId);
            org = organizationMapper.selectOrganizationById(org);
            String[] parents = org.getParents().split(",");
            SysOrganization parOrg = new SysOrganization();
            for (int i = 0; i < parents.length; i++) {
                String parId = parents[i];
                parOrg.setOrganization_id(parId);
                List<VueAntdTreeSelectNode> childTree = organizationMapper.getOrganizationListTree(parOrg);
                //树的最下级找到当前组织，设置子节点列表
                Optional<VueAntdTreeSelectNode> parentOrg = nextChildTree.stream().filter(o -> o.getId().equals(parId)).findFirst();
                parentOrg.get().setChildren(childTree);
                nextChildTree = childTree;
            }
        }
        return rootTreeNode;
    }

    @Override
    public List<SysOrganization> getOrgList(SysOrganization org) {
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        org.setParents(user.getCurrOrgId());
        List<SysOrganization> re = organizationMapper.selectForList(org);
        Optional<SysOrganization> root = re.stream().filter(o -> o.getOrganization_id().equals(Constants.ROOT_ORG)).findFirst();
        if (root.isPresent()) {
            re.remove(root.get());
        }
        for (SysOrganization sys : re) {
            sys.setParents(sys.getParents().substring(sys.getParents().indexOf(user.getCurrOrgId())));
        }
        return re;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveUpMoveDown(String orgId, String type) {
        //查询同级所有组织并排序
        List<SysOrganization> currLevelOrgList = organizationMapper.getChildOrgList(orgId);
        //判断是上移还是下移
        for (int i = 0; i < currLevelOrgList.size(); i++) {
            SysOrganization orgI = currLevelOrgList.get(i);
            if (orgId.equals(orgI.getOrganization_id())) { //是自己组织，进行操作
                if (Constants.MOVE_UP.equals(type)) { //上移
                    if (i == 0) { //自己组织是首个，不能进行移动
                        throw new RuntimeException("本组织已经排在第一个，不能进行上移操作");
                    }
                    SysOrganization lastOrg = currLevelOrgList.get(i - 1); //上一个组织
                    String priorityTmp = orgI.getPriority();
                    orgI.setPriority(lastOrg.getPriority());
                    lastOrg.setPriority(priorityTmp);
                    organizationMapper.updateIgnoreNull(orgI);
                    organizationMapper.updateIgnoreNull(lastOrg);
                } else {
                    if (i == currLevelOrgList.size() - 1) { //自己组织是最后一个，不能进行移动
                        throw new RuntimeException("本组织已经排在最后一个，不能进行下移操作");
                    }
                    SysOrganization nextOrg = currLevelOrgList.get(i + 1); //下一个组织
                    String priorityTmp = orgI.getPriority();
                    orgI.setPriority(nextOrg.getPriority());
                    nextOrg.setPriority(priorityTmp);
                    organizationMapper.updateIgnoreNull(orgI);
                    organizationMapper.updateIgnoreNull(nextOrg);
                }
                break;
            }
        }
    }

    @Override
    public List<ZTreeNode> getRootOrgList() {
        List<ZTreeNode> re = new ArrayList<>();
        //判断当前人是否为超管
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        List<Role> roleList = (List<Role>) roleManageService.selectRolesByUserId(loginUser.getUser_id());
        Optional<Role> op = roleList.stream().filter(r -> r.getRole_code().equals(Constants.SUPER_ADMIN)).findFirst();
        if (op.isPresent()) {
            //超管,看所有组织，根节点取1
            Collection<ZTreeNode> zTreeNodes = organizationMapper.selectVisiblesOrgs("1");
            re.addAll(zTreeNodes);

        } else {
            //不是超管,看自己组织及可见组织
            List<ZTreeNode> myOrg = (List<ZTreeNode>) organizationMapper.selectVisiblesOrgs(loginUser.getOrganization_id());
            re.addAll(myOrg);
            if (StringUtils.isNotEmpty(myOrg.get(0).getVisibles())) { //查询可见组织
                re.addAll(organizationMapper.selectVisiblesOrgs(myOrg.get(0).getVisibles()));
            }
        }
        return re;
    }

    @Override
    public SysOrganization findNextReviewNode(SysOrganization org) {
        String[] parents = org.getParents().split(",");
        SysOrganization nextReviewNode = new SysOrganization();
        for (int i = parents.length - 2; i >= 0; i--) {
            //查找下一级组织企业层级不为空的组织，作为下一个审批节点
            nextReviewNode = organizationMapper.getOrgByOrgId(parents[i]);
            if (StringUtils.isNotEmpty(nextReviewNode.getBusiness_level()) || StringUtils.equals("1", nextReviewNode.getBusinesstype())) {
                return nextReviewNode;
            }
        }
        return null;
    }

    @Override
    public Collection<ZTreeNode> loadLoginUserOrganizations(String id) {
        if (StringUtils.isBlank(id)) {
            return getRootOrgList();
        } else {
            return loadOrganizationByParentIdTwo(id);
        }
    }

    @Override
    public List<String> dataPermissionsForList() {
        List<String> re = new ArrayList<>();
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        //查询自己的组织
        SysOrganization myOrg = organizationMapper.getOrgByOrgId(loginUser.getOrganization_id());
        //自己组织可见的组织id
        String[] visiblesArr = myOrg.getVisibles().split(",");
        //查询被自己组织托管的组织id(列表权限不需要托管的组织)
//        List<String> auditHostingList = organizationMapper.selectAuditHosting(loginUser.getOrganization_id());
        //查询可见组织及其下级组织所有组织id
        List<String> visiblesList = new ArrayList<>();
        for (int i = 0; i < visiblesArr.length; i++) {
            if (StringUtils.isNotEmpty(visiblesArr[i])) {
                visiblesList.addAll(organizationMapper.selectAllChildOrgList(visiblesArr[i]));
            }
        }
        //列表权限组织(自己组织+可见组织)
        re.addAll(visiblesList);
        re.add(myOrg.getOrganization_id());
        return re;
    }

    @Override
    public List<String> dataPermissionsForApproval() {
        List<String> re = new ArrayList<>();
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        //查询自己的组织
        SysOrganization myOrg = organizationMapper.getOrgByOrgId(loginUser.getOrganization_id());
        //自己组织可见的组织id
//        String[] visiblesArr = myOrg.getVisibles().split(",");
        //查询被自己组织托管的组织id(列表权限不需要托管的组织)
        List<String> auditHostingList = organizationMapper.selectAuditHosting(loginUser.getOrganization_id());
        //查询可见组织及其下级组织所有组织id
//        List<String> visiblesList = new ArrayList<>();
//        for(int i = 0; i < visiblesArr.length; i++){
//            if(StringUtils.isNotEmpty(visiblesArr[i])){
//                visiblesList.addAll(organizationMapper.selectAllChildOrgList(visiblesArr[i]));
//            }
//        }
        //列表权限组织(自己组织+可见组织+被托管组织)
//        re.addAll(visiblesList);
        re.add(myOrg.getOrganization_id());
        re.addAll(auditHostingList);
        return re;
    }

    /**
     * 返回当前登录人列表权限的组织id列表(不重复)
     *
     * @return
     */
    @Override
    public Set<String> dataPermissionsForSet() {
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        //查询自己的组织
        SysOrganization myOrg = organizationMapper.getOrgByOrgId(loginUser.getOrganization_id());
        //自己组织可见的组织id(如果有)
        Set<String> visiblesSet = new HashSet<>();
        if (StringUtils.isNotBlank(myOrg.getVisibles())) {
            String[] visiblesArr = myOrg.getVisibles().split(",");
            for (String s : visiblesArr) {
                if (StringUtils.isNotEmpty(s)) {
                    visiblesSet.addAll(organizationMapper.selectAllChildOrgList(s));
                }
            }
        }
        //列表权限组织(自己组织+可见组织)
        Set<String> re = new HashSet<>(visiblesSet);
        re.addAll(organizationMapper.selectAllChildOrgList(myOrg.getOrganization_id()));
        return re;
    }

    public List<OrganizationVo> selectByCodeFuzzy(String code) {
        return organizationMapper.selectByCodeFuzzy(code);
    }

    /**
     * 获取当前用户可见组织
     * <P>当前组织+可见性
     */
    public Set<String> getVisibles() {
        Set<String> visibles = new HashSet<>();
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        SysOrganization myOrg = this.getOrgByOrgId(loginUser.getOrganization_id());
        //查询自己的组织
        visibles.add(myOrg.getOrganization_id());
        if (StringUtils.isNotBlank(myOrg.getVisibles())) {
            visibles.addAll(Arrays.asList(myOrg.getVisibles().split(",")));
        }
        return visibles;
    }

    @Override
    public List<SysOrganization> getOrgsByName(SysOrganization org) {
        // 20220428变更:登记选择上级时,去除权限
        return organizationMapper.selectForList(org);
        /*//自己组织及可见组织
        Set<String> visibles = getVisibles();
        //按组织名称搜索出的组织集合
        List<SysOrganization> list = new ArrayList<>();
        List<SysOrganization> sysOrganizations = organizationMapper.selectForList(org);
        if (CollectionUtils.isNotEmpty(sysOrganizations)){
            for (SysOrganization sysOrganization : sysOrganizations) {
                List<String> parents = Arrays.asList(sysOrganization.getParents().split(","));
                if (!Collections.disjoint(parents, visibles)){
                    list.add(sysOrganization);
                }
            }
        }
        return list;*/
    }

    /**
     * 产权树导出
     */
    @Override
    public ResponseEntity<byte[]> treeExport(SysOrganizationParm parm) {
        String fileName = "组织机构表树.xls";
        //拿到需要导出的数据
        List<ZTreeNode> exportOrg = parm.getExportOrg();
        List<ZTreeNode> zTreeNodes = new ArrayList<>();
        ZTreeNode zTreeNode = new ZTreeNode();
        int maxNodeLevel = 0;
        int minNodeLevel = 0;

        //找到一级组织机构 并处理每个节点数据 并记录最高有多少个节点
        if (CollectionUtils.isNotEmpty(exportOrg)) {
            zTreeNode = exportOrg.get(0);//根节点
            zTreeNode.setNodeLevele(0);
            /*//找出根节点的 code parents ssg username
            zTreeNode = organizationMapper.getExportData(zTreeNode.getId());*/
            if (StringUtils.isNotBlank(zTreeNode.getParents())) { //根节点
                minNodeLevel = zTreeNode.getParents().split(",").length;
            }
            for (ZTreeNode node : exportOrg) {
                if (StringUtils.isBlank(node.getParents())) { //根节点
                    node.setNodeLevele(0);
                } else {
                    //设置每个数据的节点等级
                    int length = node.getParents().split(",").length;
                    node.setNodeLevele(length - minNodeLevel);
                    if (length > maxNodeLevel) {
                        maxNodeLevel = length;
                    }
                }
            }
            /*//按节点等级降序对数据进行处理
            exportOrg.sort(Comparator.comparing(ZTreeNode::getNodeLevele).reversed());*/
            //数据处理 先添加根节点数据
            zTreeNodes.add(zTreeNode);
            int level = maxNodeLevel - minNodeLevel;
            zTreeNodes = this.dataManage(exportOrg, zTreeNodes, zTreeNode, level);

        }
        //表头
        List<List<String>> header = new ArrayList<>();
        //判断有多少层级,就动态添加多少列
        int level = maxNodeLevel - minNodeLevel;
        for (int i = 0; i <= level + 1; i++) {
            List<String> cellContain1 = new ArrayList<>();
            cellContain1.add("企业名称");
            header.add(cellContain1);
        }
        List<String> cellContain3 = new ArrayList<>();
        cellContain3.add("实缴资本/实缴出资额（万元）");
        header.add(cellContain3);
        List<String> cellContain4 = new ArrayList<>();
        cellContain4.add("注册地/主要经营场所");
        header.add(cellContain4);
        List<String> cellContain5 = new ArrayList<>();
        cellContain5.add("注册/成立日期");
        header.add(cellContain5);
        List<String> cellContain6 = new ArrayList<>();
        cellContain6.add("所属行业/经营范围");
        header.add(cellContain6);
        List<String> cellContain7 = new ArrayList<>();
        cellContain7.add("企业类别");
        header.add(cellContain7);
        List<String> cellContain8 = new ArrayList<>();
        cellContain8.add("前五大出资人名称与持股比例");
        header.add(cellContain8);

        //数据
        List<List<Object>> dataList = new ArrayList<>();
        zTreeNodes.stream().forEach(attr -> {
            List<Object> list = new ArrayList<>();
            //判断有多少层级,就动态添加多少空格列
            for (int i = 1; i <= attr.getNodeLevele(); i++) {
                if (i == attr.getNodeLevele()) {
                    list.add(StringUtils.isBlank(attr.getParentBl()) ? "0%" : attr.getParentBl() + "%");
                } else {
                    list.add("");
                }
            }
            list.add(attr.getTitle());
            //中间补充空格
            int j = level - attr.getNodeLevele();
            for (int i = 1; i <= j; i++) {
                list.add("");
            }
            list.add(attr.getTitle());
            list.add(attr.getSjzb());
            list.add(attr.getZcd());
            list.add(attr.getZcrq());
            list.add(attr.getZyhy());
            list.add(attr.getQylb());
            list.add(attr.getCzr());
            dataList.add(list);
        });
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 自动居中
        headWriteCellStyle.setWrapped(true);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        //设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //设置 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        //创建流
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData("attachment",
                new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        //生成easyexcel的流
        EasyExcel.write(out).head(header).sheet("sheet1")
                .registerWriteHandler(horizontalCellStyleStrategy)
                .doWrite(dataList);
        return new ResponseEntity<>(out.toByteArray(), headers, HttpStatus.CREATED);
    }

    /**
     * @param node       节点数据
     * @param zTreeNodes 返回处理后的数据
     * @param exportOrg  数据集合
     * @param level      最大等级
     * @return
     */
    private List<ZTreeNode> dataManage(List<ZTreeNode> exportOrg, List<ZTreeNode> zTreeNodes, ZTreeNode node, int level) {

        //找出所有直接子级
        List<ZTreeNode> collect = exportOrg.stream().filter(attr -> node.getId().equals(attr.getParentId())).collect(Collectors.toList());
        CollectionUtil.reverse(collect);
        if (CollectionUtils.isNotEmpty(collect)) {
            for (ZTreeNode vo : collect) {
                zTreeNodes.add(zTreeNodes.indexOf(node) + 1, vo);
                if (vo.getNodeLevele() != level) {
                    this.dataManage(exportOrg, zTreeNodes, vo, level);
                }
            }
        }
        return zTreeNodes;

    }

    @Override
    public List<SysOrganization> getLoginUserChildrenOrgs(SysOrganization org) {
        SysUser loginUser = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        org.setOrganization_id(loginUser.getOrganization_id());
        return organizationMapper.getLoginUserChildrenOrgs(org);
    }

    /**
     * 导出组织获取数据
     */
    @Override
    public VueAntdTreeSelectNodeExport getExportOrgTree() {
        //根组织ID不存在，则获取parent为0的组织为根组织
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        SysOrganization org = new SysOrganization();
        //如果拥有所有数据权限则根节点设为1L
        org.setOrganization_id(loginUser.getCurrOrgId());
        List<SysOrganization> orgs = organizationMapper.selectForList(org);
        if (orgs.size() == 1) {
            VueAntdTreeSelectNodeExport vueAntdTreeSelectNode = new VueAntdTreeSelectNodeExport(organizationMapper.getExportOrgTree(orgs.get(0).getOrganization_id()), String.valueOf(orgs.get(0).getOrganization_id()), orgs.get(0).getOrganization_name(), false);
            //拿到首个节点需要的数据
            ZTreeNode exportData = organizationMapper.getExportData(vueAntdTreeSelectNode.getId());
            vueAntdTreeSelectNode.setSSGZJGJG(exportData.getSSGZJGJG());
            vueAntdTreeSelectNode.setCreateUserName(exportData.getCreateUserName());
            vueAntdTreeSelectNode.setParents(exportData.getParents());
            vueAntdTreeSelectNode.setParentId(exportData.getParentId());
            vueAntdTreeSelectNode.setChildrenIdS(exportData.getChildrenIdS());
            vueAntdTreeSelectNode.setCode(exportData.getCode());
            return vueAntdTreeSelectNode;
        } else {//根组织ID不存在，则取默认根节点
            VueAntdTreeSelectNodeExport vueAntdTreeSelectNode = new VueAntdTreeSelectNodeExport(organizationMapper.getExportOrgTree(Constants.ROOT_ORG), Constants.ROOT_ORG, this.getOrgNameById(Constants.ROOT_ORG), false);
            ZTreeNode exportData = organizationMapper.getExportData(vueAntdTreeSelectNode.getId());
            vueAntdTreeSelectNode.setSSGZJGJG(exportData.getSSGZJGJG());
            vueAntdTreeSelectNode.setCreateUserName(exportData.getCreateUserName());
            vueAntdTreeSelectNode.setParents(exportData.getParents());
            vueAntdTreeSelectNode.setParentId(exportData.getParentId());
            vueAntdTreeSelectNode.setChildrenIdS(exportData.getChildrenIdS());
            vueAntdTreeSelectNode.setCode(exportData.getCode());
            return vueAntdTreeSelectNode;
        }
    }

    /**
     * 获取指定组织及以下id+托管给他的组织id-所有虚拟节点
     * <P>当前组织及以下+审批托管-虚拟节点
     */
    @Override
    public Set<String> getChildrenAndAuditIdsById(String organizationId) {
        //组织及以下id
        Set<String> childrenIds = organizationMapper.getLoginUserChildrenIds(organizationId);
        //审批托管组织id
        List<String> auditHosting = organizationMapper.selectAuditHosting(organizationId);
        // //所有虚拟节点
        // Set<String> allVirtualNode = this.getAllVirtualNode();
        childrenIds.addAll(auditHosting);
        // childrenIds.removeAll(allVirtualNode);
        return childrenIds;
    }

    /**
     * 获取所有虚拟节点
     */
    @Override
    public Set<String> getAllVirtualNode() {
        return organizationMapper.getAllVirtualNode();
    }


    /**
     * 异步加载组织树(本级及以下+审批托管)信息(包装对象)
     */
    @Override
    public Collection<ZTreeNode> loadChildrenAndAudits(String id) {
        if (StringUtils.isBlank(id)) {
            return this.getRootsList();
        } else {
            return this.loadOrganizationByParentIdTwo(id);
        }
    }

    /**
     * 获取自己组织 + 自己组织有审批托管权限的组织
     */
    public List<ZTreeNode> getRootsList() {
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        //自己组织 + 自己组织有审批托管权限的组织
        return organizationMapper.selectZTreeNodeByOrgId(loginUser.getOrganization_id());
    }

    @Override
    public List<SysOrganization> getLoginUserChildrenAndAudits(SysOrganization org) {
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        org.setOrganization_id(loginUser.getOrganization_id());
        return organizationMapper.getChildrenAndAuditsByName(org);
    }

    /**
     * 获取完整的可见组织树
     */
    @Override
    public List<OrganizationVo> getLoginUserVisibleTree() {
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        OrganizationVo myOrg = organizationMapper.selectOrgById(loginUser.getOrganization_id());
        //自己+可见的组织id
        Set<String> rootOrgIds;
        if (StrUtil.isNotBlank(myOrg.getVisibles())) {
            //仅可见组织id
            HashSet<String> visibles = CollUtil.newHashSet(myOrg.getVisibles().split(","));
            visibles.remove(myOrg.getOrganization_id());
            rootOrgIds = new HashSet<>(1 + visibles.size());
            rootOrgIds.addAll(visibles);
        } else {
            rootOrgIds = new HashSet<>(1);
        }
        rootOrgIds.add(myOrg.getOrganization_id());
        //自己+可见组织第一层,根节点组织
        List<OrganizationVo> rootOrgs = organizationMapper.selectOrgsByIds(rootOrgIds);
        //不包括可见组织根节点在内的所有可见组织
        List<OrganizationVo> allChildren = organizationMapper.selectAllVisiblesChildren(rootOrgIds);
        //填充树形结构
        //把所有组织都存到Map中去
        Map<String, OrganizationVo> parentOrgs = new HashMap<>(rootOrgs.size() + allChildren.size());
        for (OrganizationVo rootOrg : rootOrgs) {
            rootOrg.setOrganizationVoList(new LinkedList<>());
            parentOrgs.put(rootOrg.getOrganization_id(), rootOrg);
        }
        for (OrganizationVo child : allChildren) {
            child.setOrganizationVoList(new LinkedList<>());
            parentOrgs.put(child.getOrganization_id(), child);
        }
        for (OrganizationVo child : allChildren) {
            //每个组织找到自己的直接父级,并加入到父级的子级中
            if (parentOrgs.get(child.getParent_id()) != null) {
                parentOrgs.get(child.getParent_id()).getOrganizationVoList().add(child);
                // 直接子级数量+1
                if (child.getExistJbxx() != null && child.getExistJbxx() == 1) {
                    parentOrgs.get(child.getParent_id()).setChildNums(parentOrgs.get(child.getParent_id()).getChildNums() + 1L);
                }
            }

            if (child.getExistJbxx() != null && child.getExistJbxx() == 1) {
                HashSet<String> parents = CollUtil.newHashSet(child.getParents().split(","));
                parents.remove(child.getOrganization_id());
                for (String parentId : parents) {
                    //所有子级数量计算
                    if (parentOrgs.get(parentId) != null) {
                        parentOrgs.get(parentId).setAllChildNums(parentOrgs.get(parentId).getAllChildNums() + 1L);
                    }
                }
            }
        }
        return rootOrgs;
    }

    @Override
    public List<OrganizationVo> getLoginUserVisiblePossessDeleteStatusTree() {
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        OrganizationVo myOrg = organizationMapper.selectOrgByIdPossessDeleteStatus(loginUser.getOrganization_id());
        //自己+可见的组织id
        Set<String> rootOrgIds;
        if (StrUtil.isNotBlank(myOrg.getVisibles())) {
            //仅可见组织id
            HashSet<String> visibles = CollUtil.newHashSet(myOrg.getVisibles().split(","));
            visibles.remove(myOrg.getOrganization_id());
            rootOrgIds = new HashSet<>(1 + visibles.size());
            rootOrgIds.addAll(visibles);
        } else {
            rootOrgIds = new HashSet<>(1);
        }
        rootOrgIds.add(myOrg.getOrganization_id());
        //自己+可见组织第一层,根节点组织
        List<OrganizationVo> rootOrgs = organizationMapper.selectOrgsByIdsPossessDeleteStatus(rootOrgIds);
        //不包括可见组织根节点在内的所有可见组织
        List<OrganizationVo> allChildren = organizationMapper.selectAllVisiblesChildrenPossessDeleteStatus(rootOrgIds);
        //填充树形结构
        //把所有组织都存到Map中去
        Map<String, OrganizationVo> parentOrgs = new HashMap<>(rootOrgs.size() + allChildren.size());
        for (OrganizationVo rootOrg : rootOrgs) {
            rootOrg.setOrganizationVoList(new LinkedList<>());
            parentOrgs.put(rootOrg.getOrganization_id(), rootOrg);
        }
        for (OrganizationVo child : allChildren) {
            child.setOrganizationVoList(new LinkedList<>());
            parentOrgs.put(child.getOrganization_id(), child);
        }
        for (OrganizationVo child : allChildren) {
            //每个组织找到自己的直接父级,并加入到父级的子级中
            if (parentOrgs.get(child.getParent_id()) != null) {
                parentOrgs.get(child.getParent_id()).getOrganizationVoList().add(child);
                // 直接子级数量+1
                if (child.getExistJbxx() != null && child.getExistJbxx() == 1) {
                    parentOrgs.get(child.getParent_id()).setChildNums(parentOrgs.get(child.getParent_id()).getChildNums() + 1L);
                }
            }

            if (child.getExistJbxx() != null && child.getExistJbxx() == 1) {
                HashSet<String> parents = CollUtil.newHashSet(child.getParents().split(","));
                parents.remove(child.getOrganization_id());
                for (String parentId : parents) {
                    //所有子级数量计算
                    if (parentOrgs.get(parentId) != null) {
                        parentOrgs.get(parentId).setAllChildNums(parentOrgs.get(parentId).getAllChildNums() + 1L);
                    }
                }
            }
        }
        return rootOrgs;
    }

    @Override
    public List<SysOrganization> getLoginUserVisibleOrgs(SysOrganization org) {
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        OrganizationVo myOrg = organizationMapper.selectOrgByIdPossessDeleteStatus(loginUser.getOrganization_id());
        Set<String> visibleOrgIds = new HashSet<>();
        visibleOrgIds.add(myOrg.getOrganization_id());
        if (StringUtils.isNotEmpty(myOrg.getVisibles())) {
            Set<String> visibles = Arrays.stream(myOrg.getVisibles().split(",")).collect(Collectors.toSet());
            visibles.remove(myOrg.getOrganization_id());
            visibleOrgIds.addAll(visibles);
        }
        return organizationMapper.getLoginUserVisibleOrgs(visibleOrgIds, org);
    }

    @Override
    public List<SysOrganization> getOrgListPage(Integer pageNumber, Integer limit, String appOrgId) {
        //分页
        PageHelper.startPage(pageNumber, limit, false);
        List<SysOrganization> re = organizationMapper.selectAllInfoList(appOrgId);
        return re;
    }
}
