package com.boot.iAdmin.access.service.api;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/17:16:18:25
 **/
public interface ISingleSignOnService {
    /**
     * 单点登录
     * @param request
     * @param restTemplate
     * @param requestTicket
     * @return
     */
    ResponseEnvelope singleSignOnLogin(HttpServletRequest request, RestTemplate restTemplate, String requestTicket);
}
