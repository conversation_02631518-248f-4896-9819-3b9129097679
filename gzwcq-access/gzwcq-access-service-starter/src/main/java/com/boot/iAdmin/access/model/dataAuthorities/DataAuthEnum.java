package com.boot.iAdmin.access.model.dataAuthorities;

/**
 * 数据权限枚举
 * */
public enum DataAuthEnum {
	
	MY("my"), CURR_ORG("currOrg"), ORG_PLUS("orgPlus"), COMPANY("company"), USER_GROUP("userGroup"), ALL("All");
	
	private String value;

	private DataAuthEnum(String value) {
		this.value = value;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
	public static DataAuthEnum getEnumByValue(String value) {
		for(DataAuthEnum dEnum : DataAuthEnum.values()){
            if(value.equals(dEnum.getValue())){
                return dEnum;
            }
        }
        return  null;
	}
	
}
