package com.boot.iAdmin.access.model.sys_logs;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： sys_logs <br/>
 *         描述：InnoDB free: 163840 kB <br/>
 */
public class LogsVo extends Logs {

	/**
	 * 
	 */
	private static final long serialVersionUID = -608137695819085736L;
	private List<LogsVo> logsList;
	
	private String userName;//用户名
	
	public LogsVo() {
		super();
	}

	public LogsVo(long id) {
		super();
		this.id = id;
	}

	public List<LogsVo> getLogsList() {
		return logsList;
	}

	public void setLogsList(List<LogsVo> logsList) {
		this.logsList = logsList;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
	
	

}
