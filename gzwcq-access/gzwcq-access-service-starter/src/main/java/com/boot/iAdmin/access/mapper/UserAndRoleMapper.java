package com.boot.iAdmin.access.mapper;

import java.util.Map;

import com.boot.iAdmin.access.model.roleAndUser.RoleAndUser;


public interface UserAndRoleMapper {

	void deleteUserAndRoleByUser(RoleAndUser roleAndUser);

	void insertUserAndRole(RoleAndUser roleAndUser);
	
	/**
	 * 根据用户ID删除用户角色关联关系
	 * @param userIds 用户ID
	 * */
	void deleteUserAndRoleByUserIds(Map<String, Object> map);
	
	/**
	 * 根据角色ID删除用户角色关联关系
	 * @param roleIds 角色ID
	 * */
	void deleteUserAndRoleByRoleIds(Map<String, Object> map);
	
	
	void deleteUserAndRoleByUserAndCompany(RoleAndUser roleAndUser);


}
