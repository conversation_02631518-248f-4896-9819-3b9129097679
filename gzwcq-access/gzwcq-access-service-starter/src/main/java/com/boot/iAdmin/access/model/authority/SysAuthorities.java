package com.boot.iAdmin.access.model.authority;

import java.io.Serializable;

/**
 * 权限
 * */
public class SysAuthorities implements Serializable {

	private static final long serialVersionUID = 1913618103881738569L;

	protected Long authority_id;// 权限编码

	protected String authority_mark;// 权限标识

	protected String authority_name;// 权限名

	protected String authority_desc;// 权限说明

	protected String message;// 提示信息

	protected Integer enable;// 是否可用

	protected Integer issys;// 是否系统权限
	
	
	public SysAuthorities(Long authority_id) {
		super();
		this.authority_id = authority_id;
	}
	
	public SysAuthorities(){}

	public String getAuthority_mark() {
		return authority_mark;
	}

	public void setAuthority_mark(String authority_mark) {
		this.authority_mark = authority_mark;
	}

	public String getAuthority_name() {
		return authority_name;
	}

	public void setAuthority_name(String authority_name) {
		this.authority_name = authority_name;
	}

	public String getAuthority_desc() {
		return authority_desc;
	}

	public void setAuthority_desc(String authority_desc) {
		this.authority_desc = authority_desc;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Long getAuthority_id() {
		return authority_id;
	}

	public void setAuthority_id(Long authority_id) {
		this.authority_id = authority_id;
	}

	public Integer getEnable() {
		return enable;
	}

	public void setEnable(Integer enable) {
		this.enable = enable;
	}

	public Integer getIssys() {
		return issys;
	}

	public void setIssys(Integer issys) {
		this.issys = issys;
	}


}
