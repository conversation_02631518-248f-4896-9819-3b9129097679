package com.boot.iAdmin.access.mapper;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.authority.SysAuthorities;
import com.boot.iAdmin.access.model.organization.OrganizationVo;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户信息Dao
 * */
public interface SysUserInfoMapper {
	
	/**
	 * 根据用户名获取用户权限集合
	 * */
	List<SysAuthorities> getSysAuthByUserName(String username);
	
	/**
	 * 根据用户名获取用户基本信息
	 * */
	SysUserVo getUserInfoByUsername(String username);
	
	/**
	 *更新用户状态 
	 * */
	void updateUserStatus(Map<String, Object> map);
	
	/**
	 * 根据用户ID获取用户信息
	 * */
	SysUserVo getUserInfoByUserId(String userId);
	
	/**新增用户
	 * @param user */
	void insertUser(SysUser user);
	
	/**
	 * 批量删除用户
	 * */
	void deleteUserByIds(Map<String, Object> map);
	
	/**
	 * 更新用户信息
	 * <P>忽略空字段
	 * */
	void updateUserInfoIgnoreNull(SysUser user);

	Collection<SysUserVo> loadUserByPage(BootstrapTableModel<SysUser> model);
	
	long loadTotalUsers(BootstrapTableModel<SysUser> model);
	
	List<SysUser> loadUserByAuthorIDs(@Param("authIds")String authIds);

	List<SysUser> loadUserByRoleIDs(@Param("roleIds")String roleIds);

	List<SysUser> selectForUnique(SysUser user);

	List<SysUser> selectForList(SysUser user);

	/**
	 * 根据角色code,查询用户列表信息
	 * @param roleCode 角色ID
	 * @return
	 */
	List<SysUser> getUserListByRoleCode(String roleCode);

	/**
	 * 模糊查询创建人列表
	 * @param name
	 * @return
	 */
	List<Map> getUserNameByName(@Param("name")String name);

	/**
	 * 根据组织ID查找用户
	 * @param organization_id
	 * @return
	 */
    List<Map> getUserByOrgId(long organization_id);

	/**
	 * 获取登录人本级以及下级组织的所有员工列表
	 * @param paramMap
	 * @return
	 */
    List<Map<String, Object>> getLoginOrgAllStaff(Map<String, Object> paramMap);

	List<OrganizationVo> getCurrentUserOrgTree(String id);

	/**
	 * 添加/编辑账号时,查询该组织已存在的用户,当前账号除外
	 * @param u
	 * @return
	 */
    List<SysUser> selectExistUsers(SysUser u);

    List<SysUserVo> selectReviewingJbxxByUserIds(Map<String, Object> map);
}
