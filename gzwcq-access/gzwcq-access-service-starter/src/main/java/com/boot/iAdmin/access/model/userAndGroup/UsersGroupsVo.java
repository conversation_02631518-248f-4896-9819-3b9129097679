package com.boot.iAdmin.access.model.userAndGroup;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： sys_users_groups <br/>
 *         描述：用户与用户组关系表 <br/>
 */
public class UsersGroupsVo extends UsersGroups {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private List<UsersGroupsVo> usersGroupsList;

	public UsersGroupsVo() {
		super();
	}

	public UsersGroupsVo(long user_id) {
		super();
		this.user_id = user_id;
	}

	public List<UsersGroupsVo> getUsersGroupsList() {
		return usersGroupsList;
	}

	public void setUsersGroupsList(List<UsersGroupsVo> usersGroupsList) {
		this.usersGroupsList = usersGroupsList;
	}

}
