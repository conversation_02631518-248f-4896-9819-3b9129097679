package com.boot.iAdmin.access.aop;

import com.boot.IAdmin.common.aop.AutoSystemLog;
import com.boot.IAdmin.common.aop.IgnoreLog;
import com.boot.IAdmin.common.domain.Contants;
import com.boot.IAdmin.common.utils.IPUtil;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.boot.iAdmin.access.model.sys_logs.Logs;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.ILogsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 通用日志记录控制器
 * <P>AOP
 * <AUTHOR>
 * @date 2017年9月27日 15:34:49
 */
@Aspect
@Order(-1)
@Component
public class LogAopConfig implements ApplicationListener<ContextRefreshedEvent> {

	private Log logger = LogFactory.getLog(this.getClass());

	@Resource
	private ILogsService logsService;
	
	//日志阻塞队列
	LinkedBlockingQueue<Logs> logQueue = new LinkedBlockingQueue<>(10000);


	// 配置接入点,默认配置拦截所有的controller中public方法
	@Pointcut("execution(public * com..*.controller..*.*(..))")
	private void logControllerAspect() {
	}

	/**
	 * 是否记录所有日志
	 * <P>
	 * false 为只记录有注解@AutoSystemLog的方法日志,默认为false
	 */
	@Value("${com.auto.aop.log.saveAll}")
	private boolean saveAll = false;

	@Around("logControllerAspect()")
	public Object aroundController(ProceedingJoinPoint pjp) throws Throwable {
		return doLog(pjp);
	}

	/**
	 * 日志处理
	 */
	private Object doLog(ProceedingJoinPoint pjp) throws Throwable {
		Object object = null;
		// 创建日志实体对象
		Logs log = new Logs();
		// 方法通知前获取时间,用来计算模块执行时间的
		long start = System.currentTimeMillis();
		// 判断是否需要保存
		boolean needSave = validateIfNeedSave(pjp, log);
		if (needSave) {
			try {
				object = pjp.proceed();
				log.setCommite("执行成功！");
				log.setStatus(Contants.STATE_Y);
			} catch (Throwable e) {
				log.setCommite("执行失败：" + e.getMessage());
				log.setStatus(Contants.STATE_N);
				throw e;
			} finally {
				long end = System.currentTimeMillis();
				// 将计算好的时间保存在实体中
				log.setResponse_date("" + (end - start));
				log.setCreate_date(new Date());
				// 异步保存日志
				try {
					log.setParams(getParams(pjp.getArgs()));
					logQueue.add(log);
				}catch(Exception e) {
					logger.error("记录操作日志失败：",e);
				}
			}
		} else {
			object = pjp.proceed();
		}
		return object;
	}

	/**
	 * 判断是否需要记录当前操作日志
	 * 
	 * @param log
	 * 
	 */
	private boolean validateIfNeedSave(ProceedingJoinPoint pjp, Logs log) {
		// 默认不需要记录
		boolean needSave = false;
		try {
			// 当前拦截对象
			Object target = pjp.getTarget();
			// 拦截的方法名称。当前正在执行的方法
			String methodName = pjp.getSignature().getName();
			// 拦截的放参数类型
			// Signature sig = pjp.getSignature();
			MethodSignature msig = (MethodSignature) pjp.getSignature();
			;
			// if (!(sig instanceof MethodSignature)) {
			// throw new IllegalArgumentException("该注解只能用于方法");
			// }
			// msig = (MethodSignature) sig;
			Class<?>[] parameterTypes = msig.getMethod().getParameterTypes();
			// 获得被拦截的方法
			Method method = null;
			try {
				method = target.getClass().getMethod(methodName, parameterTypes);
			} catch (NoSuchMethodException e1) {
				e1.printStackTrace();
			} catch (SecurityException e1) {
				e1.printStackTrace();
			}
			if (null != method) {

				// 判断对象类型是否忽略日志记录@IgnoreLog
				if (method.isAnnotationPresent(IgnoreLog.class) || target.getClass().isAnnotationPresent(IgnoreLog.class)) {
					return false;
				}

				// 判断当前所代理的对象实现的接口是否存在@IgnoreLog注解
				Class<?>[] interfacesClazz = target.getClass().getInterfaces();
				for (Class<?> interfaceClazz : interfacesClazz) {
					if (interfaceClazz.isAnnotationPresent(IgnoreLog.class)) {
						return false;
					}
				}

				// 判断接口上面当前方法是否存在@IgnoreLog注解
				for (Class<?> interfaceClazz : interfacesClazz) {
					Method currMethod = null;
					try {
						currMethod = interfaceClazz.getMethod(methodName, parameterTypes);
					} catch (Exception e) {
					}
					if (currMethod != null && currMethod.isAnnotationPresent(IgnoreLog.class)) {
						return false;
					}
				}

				// 获取存在@AutoSystemLog注解的抽象方法
				Method upMethod = null;
				for (Class<?> interfaceClazz : interfacesClazz) {
					Method currMethod = null;
					try {
						currMethod = interfaceClazz.getMethod(methodName, parameterTypes);
					} catch (Exception e) {
					}
					if (currMethod != null && currMethod.isAnnotationPresent(AutoSystemLog.class)) {
						upMethod = currMethod;
						break;
					}
				}
				// 在不存在@IgnoreLog注解的前提下，当前仅当saveAll==true或者当前方法上存在@AutoSystemLog注解或者抽象方法上存在@AutoSystemLog注解时需要记录日志
				if (saveAll || method.isAnnotationPresent(AutoSystemLog.class) || upMethod != null) {// 判断是否包含自定义的注解，说明一下这里的SystemLog就是我自己自定义的注解
					needSave = true;// 标记需要记录
					// 获取登录用户账户
					try {
						HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
						// 获取系统ip
						log.setIp(IPUtil.getIpAddress(request));
					} catch (Exception e) {
					}
					try {
						SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
						log.setUserid(loginUser.getUser_id());
					} catch (Exception e) {
					}
					// 调用时间
					log.setExecute_date(new Date());
					// 拦截的方法参数
//					log.setParams(getParams(pjp.getArgs()));
					if (method.isAnnotationPresent(AutoSystemLog.class)) {// 获取实现类方法上注解
						AutoSystemLog systemlog = method.getAnnotation(AutoSystemLog.class);
						log.setModule(StringUtils.isBlank(systemlog.moduleName()) ? target.toString() : systemlog.moduleName());
						log.setMethod(StringUtils.isBlank(systemlog.methodName()) ? methodName : systemlog.methodName());
					} else if (upMethod != null) {// 获取接口方法注解
						AutoSystemLog systemlog = upMethod.getAnnotation(AutoSystemLog.class);
						log.setModule(StringUtils.isBlank(systemlog.moduleName()) ? target.toString() : systemlog.moduleName());
						log.setMethod(StringUtils.isBlank(systemlog.methodName()) ? methodName : systemlog.methodName());
					} else {
						log.setModule(target.toString());
						log.setMethod(methodName);
					}
				}
			}
		} catch (Exception e) {
			logger.error(e);
		}
		return needSave;
	}

	public boolean isSaveAll() {
		return saveAll;
	}

	public void setSaveAll(boolean saveAll) {
		this.saveAll = saveAll;
	}

	/**
	 * 返回参数
	 */
	private String getParams(Object[] args) {
		List<String> params = new ArrayList<String>();
		for (Object arg : args) {
			if(arg instanceof ServletRequest || arg instanceof ServletResponse || arg instanceof HttpSession) {
				continue;
			}
			try {
				params.add(JsonUtil.toJSon(arg));
			}catch(Exception e) {
				logger.error(String.format("日志参数转换失败：[%s]", e.getMessage()));
			}
		}
		//如果参数长度大于1000则只保留前1000个字符
		String paramsStr = JsonUtil.toJSon(params);
		if(StringUtils.isNotBlank(paramsStr) && paramsStr.length()> 1000) {
			paramsStr = paramsStr.substring(0,999);
		}
		return paramsStr;
	}
	
	//日志消费者
	class  LogCustomer extends Thread {

		@Override
		public void run() {
			logger.info("==========系统日志异步线程启动"+Thread.currentThread().getId()+"===========");
			while(true) {
				try {
					Logs log = logQueue.take();//从队列获取日志
					logsService.save(log);
				} catch (Exception e) {
					logger.error("记录操作日志失败：",e);
				}finally {
					logger.info("消费后日志队列数为："+logQueue.size());
				}
			}
		}
		
	}
	
	/**
	 * 系统日志线程启动
	 * */
	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
		new LogCustomer().start();
	}

}
