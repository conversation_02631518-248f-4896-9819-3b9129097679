package com.boot.iAdmin.access.model.common;

/**
 * 模块菜单相对固定
 * <P>已升级为按照名称匹配
 * */
@Deprecated
public enum ModuleMenu {
	
	SYSTEM(2L,"system","系统管理"),
	TASK(12L,"task","定时任务"),
	BUSINESS(17L,"business","业务管理"),
	CHART(27L,"chart","报表管理"),
	SERVICE(24L,"service","服务管理"),
	DATA_MANAGER(21L,"data_manager","数据管理"),
	MONITOR(42L,"monitor","平台监控"),
	DATA_EXCHANGE(47L,"data_exchange","数据交换");
	
	private Long id;
	private String code;
	private String name;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	private ModuleMenu(Long id, String code, String name) {
		this.id = id;
		this.code = code;
		this.name = name;
	}
	
	public static ModuleMenu getByCode(String code){
		ModuleMenu[] values = ModuleMenu.values();
		for (ModuleMenu value :  values ) {
			if(value.getCode().equals(code))
				return value;
		}
		return null;
	}

}
