package com.boot.iAdmin.access.service.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.access.model.common.ZTreeNode;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface IUserManageService {
	
//	PageModel<SysUser> queryUserByPage(PageModel<SysUser> pageModel);

	void deleteUserById(String userId);

	void recoverUserById(String userId);

	SysUser getUserInfoByUserId(String userId);

	void insertUser(SysUser user);

	void deleteUserByIds(String userIds);

	void updateUser(SysUser user, String[] roles);

	BootstrapTableModel<SysUserVo> loadUserByPage(BootstrapTableModel<SysUser> model);
	
	void updateUserInfoIgnoreNull(SysUser user);

	boolean validateUniqueParam(SysUser user);

	void insertUser(SysUser user, String[] roles);

	/**
	 * 权限设置
	 * <P> 用户+公司
	 * */
	void authSetting(SysUserVo user, String[] roles, Long company_id);

	Collection<ZTreeNode> getCurrentUserOrgTree(SysUser user, String id);

    void accountVerification(SysUser user);

    List<SysUserVo> selectReviewingJbxxByUserIds(Map<String, Object> map);
}
