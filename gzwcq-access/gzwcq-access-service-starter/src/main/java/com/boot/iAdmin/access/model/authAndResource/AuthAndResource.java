package com.boot.iAdmin.access.model.authAndResource;

/**
 * 权限资源关联关系
 * 
 * <AUTHOR>
 * @date 2016年9月21日 10:37:22
 * */
public class AuthAndResource {

	// 主键
	private Long id;
	// 资源ID
	private Long resource_id;
	// 权限ID
	private Long authority_id;
	
	public AuthAndResource(){}

	public AuthAndResource(Long authority_id,Long resource_id) {
		super();
		this.resource_id = resource_id;
		this.authority_id = authority_id;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getResource_id() {
		return resource_id;
	}

	public void setResource_id(Long resource_id) {
		this.resource_id = resource_id;
	}

	public Long getAuthority_id() {
		return authority_id;
	}

	public void setAuthority_id(Long authority_id) {
		this.authority_id = authority_id;
	}

}
