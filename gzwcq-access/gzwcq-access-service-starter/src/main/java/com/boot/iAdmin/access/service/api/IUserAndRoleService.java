package com.boot.iAdmin.access.service.api;

import com.boot.iAdmin.access.model.roleAndUser.RoleAndUser;
import com.boot.iAdmin.access.model.user.SysUser;


/**
 * 用户角色
 * */
public interface IUserAndRoleService {
	
	void updateUserAndRolesWithOneUser(SysUser user, String[] roles);
	
	/**
	 * 根据用户ID删除用户角色关联关系
	 * @param userIds 用户ID
	 * */
	void deleteUserAndRoleByUserIds(String userIds);
	
	/**
	 * 根据角色ID删除用户角色关联关系
	 * @param roleIds 角色ID
	 * */
	void deleteUserAndRoleByRoleIds(String roleIds);

	void insertUserAndRole(RoleAndUser roleAndUser);
	
	/**
	 * 更新公司用户角色信息
	 * */
	@Deprecated
	void updateUserAndRolesWithOneUserAndCompany(SysUser user, String[] roles,Long company_id);
	
}
