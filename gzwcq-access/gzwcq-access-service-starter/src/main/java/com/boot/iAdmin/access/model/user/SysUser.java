package com.boot.iAdmin.access.model.user;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

import org.springframework.security.core.userdetails.UserDetails;

import com.boot.iAdmin.access.model.authority.CustomGrantedAuthority;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 系统用户
 * */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SysUser implements UserDetails, Serializable {

	private static final long serialVersionUID = 4947153567918475845L;

	// 编号
	protected String user_id;
	// 账户名
	protected String username;
	// 用户姓名
	protected String name;
	// 密码
	protected String password;
	// 创建日期
	protected Date dt_create;
	// 最后登录日期
	protected Date last_login;
	// 截止日期
	protected Date deadline;
	// 登录IP
	protected String login_ip;
	// 账户是否过期 1--过期 0--有效
	protected Integer account_non_expired;
	// 是否被锁定 1--锁定 0--未锁定
	protected Integer account_non_locked;
	// 证书是否过期 1--过期 0--有效
	protected Integer credentials_non_expired;

	protected Integer status; // 账户是否有效 1--有效 0--失效
	
	protected String phone;//手机
	protected String email;//邮箱
	
	protected String organization_id;//所属组织ID
	
	protected String remarks;//备注
	
	protected Collection<CustomGrantedAuthority> authorities; // 权限集合
	
	protected String currOrgName;//当前组织名称
	
	protected String currOrgId;//当前组织Id

	protected String isdeleted; //删除状态

	protected String orgName;
	
	protected String type; //账号类型
	
	protected String audit_level; //审核层级

	protected String initial_pass;//是否是初始密码(Y/N)

	protected String first_login;//是否是首次登录(Y/N)

	protected Boolean initialPassFlag;
	protected Boolean firstLoginFlag;

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public SysUser(){
		//默认有效
		this.status = 1;
	}

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String user_id) {
		this.user_id = user_id;
	}
	
	//子类继承
	@Deprecated
	protected void setEnabled_txt(boolean enabled) {
		
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getName() {
		return name;
	}

	public String getOrganization_id() {
		return organization_id;
	}

	public void setOrganization_id(String organization_id) {
		this.organization_id = organization_id;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}


	public Date getDeadline() {
		return deadline;
	}

	public void setDeadline(Date deadline) {
		this.deadline = deadline;
	}

	public Date getDt_create() {
		return dt_create;
	}

	public void setDt_create(Date dt_create) {
		this.dt_create = dt_create;
	}

	public Date getLast_login() {
		return last_login;
	}

	public void setLast_login(Date last_login) {
		this.last_login = last_login;
	}

	public String getLogin_ip() {
		return login_ip;
	}

	public void setLogin_ip(String login_ip) {
		this.login_ip = login_ip;
	}

	public Integer getAccount_non_expired() {
		return account_non_expired;
	}

	public void setAccount_non_expired(Integer account_non_expired) {
		this.account_non_expired = account_non_expired;
	}

	public Integer getAccount_non_locked() {
		return account_non_locked;
	}

	public void setAccount_non_locked(Integer account_non_locked) {
		this.account_non_locked = account_non_locked;
	}

	public Integer getCredentials_non_expired() {
		return credentials_non_expired;
	}

	public void setCredentials_non_expired(Integer credentials_non_expired) {
		this.credentials_non_expired = credentials_non_expired;
	}

//	public void setEnabled(Integer enabled) {
//		this.enabled = enabled;
////		this.setEnabled_txt(this.enabled.intValue()==1);
//	}
	
	
	
	
	//############必须实现方法，用于spring security判断帐号状态###########
	//preAuthenticationChecks.check()
	public boolean isEnabled() {
		return this.status.intValue() == 1;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public boolean isAccountNonExpired() {
		return true;
	}

	public boolean isAccountNonLocked() {
		return true;
	}

	public boolean isCredentialsNonExpired() {
		return true;
	}
	//############必须实现方法，用于spring security判断帐号状态###########

	public Collection<CustomGrantedAuthority> getAuthorities() {
		return authorities;
	}

	public void setAuthorities(Collection<CustomGrantedAuthority> authorities) {
		this.authorities = authorities;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	@Override
	public String toString() {
		return this.username;
	}
	
	@Override
	public int hashCode() {
		return username.hashCode();
	}
	
	@Override
	public boolean equals(Object obj) {
		return this.toString().equals(obj.toString());
	}
	
	public String getCurrOrgName() {
		return currOrgName;
	}

	public void setCurrOrgName(String currOrgName) {
		this.currOrgName = currOrgName;
	}

	public String getCurrOrgId() {
		return currOrgId;
	}

	public void setCurrOrgId(String currOrgId) {
		this.currOrgId = currOrgId;
	}

	public String getIsdeleted() {
		return isdeleted;
	}

	public void setIsdeleted(String isdeleted) {
		this.isdeleted = isdeleted;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getAudit_level() {
		return audit_level;
	}

	public void setAudit_level(String audit_level) {
		this.audit_level = audit_level;
	}

	public String getInitial_pass() {
		return initial_pass;
	}

	public void setInitial_pass(String initial_pass) {
		this.initial_pass = initial_pass;
	}

	public String getFirst_login() {
		return first_login;
	}

	public void setFirst_login(String first_login) {
		this.first_login = first_login;
	}

	public Boolean getInitialPassFlag() {
		return initialPassFlag;
	}

	public void setInitialPassFlag(Boolean initialPassFlag) {
		this.initialPassFlag = initialPassFlag;
	}

	public Boolean getFirstLoginFlag() {
		return firstLoginFlag;
	}

	public void setFirstLoginFlag(Boolean firstLoginFlag) {
		this.firstLoginFlag = firstLoginFlag;
	}
}
