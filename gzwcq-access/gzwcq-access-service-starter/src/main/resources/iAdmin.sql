/*
 Navicat Premium Data Transfer

 Source Server         : 鸿程内网数据库
 Source Server Type    : MySQL
 Source Server Version : 50717
 Source Host           : ***********:3306
 Source Schema         : clc

 Target Server Type    : MySQL
 Target Server Version : 50717
 File Encoding         : 65001

 Date: 28/05/2020 10:32:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_authorities
-- ----------------------------
DROP TABLE IF EXISTS `sys_authorities`;
CREATE TABLE `sys_authorities`  (
  `AUTHORITY_ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '权限主键',
  `AUTHORITY_MARK` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `AUTHORITY_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限名称',
  `AUTHORITY_DESC` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限说明',
  `MESSAGE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '提示信息',
  `ENABLE` int(11) NULL DEFAULT 1 COMMENT '是否可用',
  `ISSYS` int(11) NULL DEFAULT NULL COMMENT '是否系统权限',
  PRIMARY KEY (`AUTHORITY_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_authorities
-- ----------------------------
INSERT INTO `sys_authorities` VALUES (1, '公共权限', '公共权限', NULL, NULL, 1, NULL);
INSERT INTO `sys_authorities` VALUES (2, '超级管理员', '超级管理员', '超级管理员', NULL, 1, NULL);
INSERT INTO `sys_authorities` VALUES (3, 'C_1', '开发者服务管理', '开发者服务管理', NULL, 1, NULL);

-- ----------------------------
-- Table structure for sys_authorities_resources
-- ----------------------------
DROP TABLE IF EXISTS `sys_authorities_resources`;
CREATE TABLE `sys_authorities_resources`  (
  `ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `RESOURCE_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '资源编码',
  `AUTHORITY_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限编码',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限资源关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_authorities_resources
-- ----------------------------
INSERT INTO `sys_authorities_resources` VALUES (10, '1', '2');
INSERT INTO `sys_authorities_resources` VALUES (15, '25', '1');
INSERT INTO `sys_authorities_resources` VALUES (16, '40', '1');
INSERT INTO `sys_authorities_resources` VALUES (17, '41', '1');
INSERT INTO `sys_authorities_resources` VALUES (18, '177', '3');

-- ----------------------------
-- Table structure for sys_auto_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_auto_job`;
CREATE TABLE `sys_auto_job`  (
  `JOB_ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '作业编码',
  `JOB_TYPE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业类型',
  `JOB_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业名称',
  `CRON_EXPR` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '触发规则cron',
  `RUNTIME_LAST` datetime(0) NULL DEFAULT NULL COMMENT '上一次执行时间',
  `RUNTIME_NEXT` datetime(0) NULL DEFAULT NULL COMMENT '下一次执行时间',
  `JOB_STATUS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `RUN_TIMES` bigint(10) NULL DEFAULT NULL COMMENT '执行次数',
  `RUN_DUARTION` bigint(10) NULL DEFAULT NULL COMMENT '持续时间',
  `SYNC_BEGIN_TIME` datetime(0) NULL DEFAULT NULL COMMENT '触发器开始时间',
  `SYNC_END_TIME` datetime(0) NULL DEFAULT NULL COMMENT '触发器结束时间',
  `JOB_MEMO` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `JOB_CLASS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作业类',
  `JOB_METHOD` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务方法',
  `JOB_OBJECT` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务名称',
  PRIMARY KEY (`JOB_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_auto_job
-- ----------------------------

-- ----------------------------
-- Table structure for sys_data_authorities
-- ----------------------------
DROP TABLE IF EXISTS `sys_data_authorities`;
CREATE TABLE `sys_data_authorities`  (
  `data_id` bigint(11) NOT NULL AUTO_INCREMENT,
  `data_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限名称',
  `data_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限描述',
  PRIMARY KEY (`data_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_data_authorities
-- ----------------------------
INSERT INTO `sys_data_authorities` VALUES (5, 'test', 'test');
INSERT INTO `sys_data_authorities` VALUES (7, '七彩安科故障工单', '七彩安科故障工单');
INSERT INTO `sys_data_authorities` VALUES (8, '资产调拨发起人数据权限', '资产调拨发起人数据权限');

-- ----------------------------
-- Table structure for sys_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `sys_dictionary`;
CREATE TABLE `sys_dictionary`  (
  `id` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '流水',
  `type` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `type_code` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型编码',
  `val` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '值',
  `text` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文本',
  `seq` int(11) NULL DEFAULT NULL COMMENT '序号',
  `active` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否可用',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `parent` bigint(10) NULL DEFAULT NULL,
  `type_id` bigint(10) NULL DEFAULT NULL COMMENT '关联类型主键（保存类型ID而不保存类型值是为了防止类型值修改，子节点类型值同步问题）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `INDEX_P`(`parent`) USING BTREE,
  INDEX `INDEX_T`(`type_id`) USING BTREE,
  INDEX `INDEX_V`(`type_code`, `val`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 711 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dictionary
-- ----------------------------
INSERT INTO `sys_dictionary` VALUES (2, '', NULL, '1', '我的', 1, '1', '我创建的', NULL, NULL);
INSERT INTO `sys_dictionary` VALUES (3, '', NULL, '2', '当前组织', 2, '1', '我当前组织的数据不包含下级', NULL, NULL);
INSERT INTO `sys_dictionary` VALUES (4, '', NULL, '3', '当前组织及以下', 3, '1', '当前组织及组织以下的所有数据', NULL, NULL);
INSERT INTO `sys_dictionary` VALUES (5, '', NULL, '4', '所有公司', 4, '1', '所有公司的数据都可以看', NULL, NULL);
INSERT INTO `sys_dictionary` VALUES (6, '', NULL, '5', '用户组', 5, '1', '用户组内的所有人的数据都可以查看', NULL, NULL);
INSERT INTO `sys_dictionary` VALUES (30, '数据权限', 'DataAuth', NULL, NULL, NULL, NULL, '数据权限', -1, NULL);
INSERT INTO `sys_dictionary` VALUES (37, NULL, NULL, 'my', '我的', 1, NULL, '我创建的', 30, 30);
INSERT INTO `sys_dictionary` VALUES (38, NULL, NULL, 'currOrg', '当前组织', 2, NULL, '我当前组织的数据不包含下级', 30, 30);
INSERT INTO `sys_dictionary` VALUES (39, NULL, NULL, 'orgPlus', '当前组织及以下', 3, NULL, '', 30, 30);
INSERT INTO `sys_dictionary` VALUES (40, NULL, NULL, 'company', '公司', 4, NULL, '所有公司的数据都可以看', 30, 30);
INSERT INTO `sys_dictionary` VALUES (41, NULL, NULL, 'userGroup', '用户组', 5, NULL, '用户组内的所有人的数据都可以查看', 30, 30);
INSERT INTO `sys_dictionary` VALUES (396, '是否', 'YesNo', NULL, NULL, 14, NULL, '', -1, NULL);
INSERT INTO `sys_dictionary` VALUES (397, NULL, NULL, 'Y', '是', NULL, NULL, '', 396, 396);
INSERT INTO `sys_dictionary` VALUES (398, NULL, NULL, 'N', '否', NULL, NULL, '', 396, 396);
INSERT INTO `sys_dictionary` VALUES (587, '状态', 'DIC_STATUS', NULL, NULL, NULL, NULL, '基础数据的状态', -1, NULL);
INSERT INTO `sys_dictionary` VALUES (588, NULL, NULL, '2', '无效', 2, NULL, '', 587, 587);
INSERT INTO `sys_dictionary` VALUES (590, NULL, NULL, '1', '有效', 1, NULL, '', 587, 587);
INSERT INTO `sys_dictionary` VALUES (672, NULL, NULL, 'All', '所有', 6, NULL, '', 30, 30);
INSERT INTO `sys_dictionary` VALUES (708, '协议类型', 'DIC_SSO_TYPE', NULL, NULL, 1, NULL, '单点协议类型', -1, NULL);
INSERT INTO `sys_dictionary` VALUES (709, NULL, NULL, '1', 'CAS', 1, NULL, '', 708, 708);
INSERT INTO `sys_dictionary` VALUES (710, NULL, NULL, '2', 'OAUTH2', 2, NULL, '', 708, 708);

-- ----------------------------
-- Table structure for sys_groups
-- ----------------------------
DROP TABLE IF EXISTS `sys_groups`;
CREATE TABLE `sys_groups`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户组名',
  `group_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户组描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_groups
-- ----------------------------
INSERT INTO `sys_groups` VALUES (1, '1', '修改');
INSERT INTO `sys_groups` VALUES (9, '消防部门', '消防工程师');
INSERT INTO `sys_groups` VALUES (10, '工程部', '技术员维修');
INSERT INTO `sys_groups` VALUES (11, '维修部', '维修部');

-- ----------------------------
-- Table structure for sys_logs
-- ----------------------------
DROP TABLE IF EXISTS `sys_logs`;
CREATE TABLE `sys_logs`  (
  `ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `USERID` bigint(10) NULL DEFAULT NULL COMMENT '登录用户',
  `MODULE` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模块名',
  `METHOD` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '方法名',
  `PARAMS` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '参数',
  `RESPONSE_DATE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '响应时间',
  `IP` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'IP',
  `EXECUTE_DATE` datetime(0) NULL DEFAULT NULL COMMENT '执行时间',
  `COMMITE` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `CREATE_DATE` datetime(0) NULL DEFAULT NULL COMMENT '记录日期',
  `status` int(2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logs
-- ----------------------------

-- ----------------------------
-- Table structure for sys_module_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_module_data`;
CREATE TABLE `sys_module_data`  (
  `data_id` bigint(11) NOT NULL COMMENT '数据权限表id',
  `module_id` bigint(11) NOT NULL COMMENT '菜单id',
  `auth` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限信息',
  PRIMARY KEY (`data_id`, `module_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '菜单数据权限管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_module_data
-- ----------------------------
INSERT INTO `sys_module_data` VALUES (4, 1, '2');
INSERT INTO `sys_module_data` VALUES (4, 2, '3');
INSERT INTO `sys_module_data` VALUES (5, 12, '2');
INSERT INTO `sys_module_data` VALUES (8, 110, 'my');
INSERT INTO `sys_module_data` VALUES (8, 111, 'orgPlus');
INSERT INTO `sys_module_data` VALUES (8, 112, 'my');

-- ----------------------------
-- Table structure for sys_modules
-- ----------------------------
DROP TABLE IF EXISTS `sys_modules`;
CREATE TABLE `sys_modules`  (
  `MODULE_ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '模块编码',
  `MODULE_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模块名称',
  `MODULE_DESC` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模块说明',
  `MODULE_TYPE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模块类型',
  `PARENT` bigint(100) NULL DEFAULT NULL COMMENT '模块上级',
  `I_LEVEL` int(11) NULL DEFAULT 1 COMMENT '菜单级别',
  `LEAF` int(11) NULL DEFAULT 0 COMMENT '最下级',
  `APPLICATION` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应用名称',
  `CONTROLLER` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '控制器名称',
  `ENABLE` int(1) NULL DEFAULT 1 COMMENT '是否可用',
  `PRIORITY` int(11) NULL DEFAULT NULL COMMENT '优先级',
  `RESOURCE_ID` bigint(10) NULL DEFAULT NULL COMMENT '菜单关联资源ID',
  PRIMARY KEY (`MODULE_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 129 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_modules
-- ----------------------------
INSERT INTO `sys_modules` VALUES (1, '菜单根目录', '菜单根目录', NULL, 0, 0, 0, NULL, NULL, 1, NULL, NULL);
INSERT INTO `sys_modules` VALUES (2, '系统管理', '系统管理', 'fa-cogs icon-top', 1, 1, NULL, NULL, NULL, 1, 99, NULL);
INSERT INTO `sys_modules` VALUES (3, '用户管理', '用户管理', 'el-icon-user-solid', 2, 2, NULL, '', '', 1, 1, 4);
INSERT INTO `sys_modules` VALUES (4, '角色管理', '角色管理', 'fa-user-o', 2, 2, NULL, NULL, NULL, 1, 2, 5);
INSERT INTO `sys_modules` VALUES (5, '权限管理', '权限管理', 'fa-users', 2, 2, 0, NULL, NULL, 1, 3, 6);
INSERT INTO `sys_modules` VALUES (6, '菜单管理', '菜单管理', 'el-icon-menu', 2, 2, 0, NULL, NULL, 1, 5, 7);
INSERT INTO `sys_modules` VALUES (9, '资源管理', '资源管理', 'fa-tree', 2, 2, NULL, NULL, NULL, 1, 4, 3);
INSERT INTO `sys_modules` VALUES (14, '日志管理', '日志管理', 'fa-bug', 2, NULL, NULL, NULL, NULL, NULL, 6, NULL);
INSERT INTO `sys_modules` VALUES (15, '缓存刷新', '缓存刷新', 'fa-repeat', 2, NULL, NULL, NULL, NULL, NULL, 7, NULL);
INSERT INTO `sys_modules` VALUES (21, '组织管理', '组织管理', 'fa-cog', 2, NULL, NULL, NULL, NULL, NULL, 8, 19);
INSERT INTO `sys_modules` VALUES (22, '数据权限', '用于配置用户的数据权限，控制数据访问', 'fa-anchor', 2, NULL, NULL, NULL, NULL, NULL, 9, NULL);
INSERT INTO `sys_modules` VALUES (23, '数据字典', '数据字典', 'fa-list-alt', 2, NULL, NULL, NULL, NULL, NULL, 9, 21);
INSERT INTO `sys_modules` VALUES (27, '用户组管理', '用户设置用户组信息', 'fa-users', 2, NULL, NULL, NULL, NULL, NULL, 10, NULL);
INSERT INTO `sys_modules` VALUES (42, '定时任务', NULL, 'fa-clock-o', 2, NULL, NULL, NULL, NULL, NULL, 12, NULL);
INSERT INTO `sys_modules` VALUES (80, '流程引擎', NULL, 'fa-random', 2, NULL, NULL, NULL, NULL, NULL, 7, NULL);
INSERT INTO `sys_modules` VALUES (81, '流程模型管理', '流程模型管理', 'el-icon-printer', 80, NULL, NULL, NULL, NULL, NULL, 1, NULL);
INSERT INTO `sys_modules` VALUES (88, '系统参数', '系统参数', 'el-icon-setting', 2, NULL, NULL, NULL, NULL, NULL, 13, NULL);
INSERT INTO `sys_modules` VALUES (113, '流程定义管理', '流程定义管理', 'fa-deaf', 80, NULL, NULL, NULL, NULL, NULL, 2, NULL);
INSERT INTO `sys_modules` VALUES (114, '流程实例管理', '流程实例管理', 'fa-hourglass-half', 80, NULL, NULL, NULL, NULL, NULL, 3, NULL);
INSERT INTO `sys_modules` VALUES (115, '历史流程管理', '历史流程管理', 'fa-history', 80, NULL, NULL, NULL, NULL, NULL, 4, NULL);
INSERT INTO `sys_modules` VALUES (116, '我审批的流程', '我审批的流程', 'el-icon-chat-line-square', 80, NULL, NULL, NULL, NULL, NULL, 6, NULL);
INSERT INTO `sys_modules` VALUES (117, '我的待办事项', '我的待办事项', 'el-icon-alarm-clock', 80, NULL, NULL, NULL, NULL, NULL, 5, NULL);
INSERT INTO `sys_modules` VALUES (119, '我发起的流程', NULL, 'el-icon-circle-plus-outline', 80, NULL, NULL, NULL, NULL, NULL, 6, NULL);
INSERT INTO `sys_modules` VALUES (123, '元数据缓存', '元数据缓存（权限）', 'el-icon-s-order', 15, NULL, NULL, NULL, NULL, NULL, 1, 172);
INSERT INTO `sys_modules` VALUES (124, '字典缓存', '字典缓存', 'el-icon-s-order', 15, NULL, NULL, NULL, NULL, NULL, 2, 173);
INSERT INTO `sys_modules` VALUES (125, '数据源监控', '数据源监控', NULL, 2, NULL, NULL, NULL, NULL, NULL, 100, 174);
INSERT INTO `sys_modules` VALUES (126, '配置管理', '配置管理', 'fa-cog', 1, NULL, NULL, NULL, NULL, NULL, 1, NULL);
INSERT INTO `sys_modules` VALUES (127, '应用管理', '应用管理', NULL, 126, NULL, NULL, NULL, NULL, NULL, 1, 176);
INSERT INTO `sys_modules` VALUES (128, '开发者服务管理', '开发者服务管理', NULL, 126, NULL, NULL, NULL, NULL, NULL, 2, 177);

-- ----------------------------
-- Table structure for sys_organization
-- ----------------------------
DROP TABLE IF EXISTS `sys_organization`;
CREATE TABLE `sys_organization`  (
  `ORGANIZATION_ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '组织主键',
  `ORGANIZATION_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组织名称',
  `ORGANIZATION_DESC` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组织说明',
  `ORGANIZATION_CODE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组织编码',
  `OA_ORGANIZATION_CODE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'OA内部组织编码',
  `PARENT_ID` bigint(10) NULL DEFAULT NULL COMMENT '父节点ID',
  `PARENT_CODE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父节点编码',
  `PRIORITY` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '优先级',
  `ENABLE` int(11) NULL DEFAULT NULL COMMENT '是否有效',
  `status` int(2) NULL DEFAULT NULL,
  `IS_COMP` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否公司',
  `ORGANIZATION_TYPE` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组织属性',
  `IS_LEAF` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否叶子节点',
  PRIMARY KEY (`ORGANIZATION_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 93 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '组织机构表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_organization
-- ----------------------------
INSERT INTO `sys_organization` VALUES (1, '组织', '组织', '0105', NULL, 0, NULL, NULL, NULL, 'Y', NULL, NULL);
INSERT INTO `sys_organization` VALUES (73, '浙江鸿程计算机', '浙江鸿程计算机', '010504', NULL, 1, NULL, NULL, NULL, 'Y', NULL, 'Y');
INSERT INTO `sys_organization` VALUES (79, '电信支撑部', '电信支撑部', '01050401', NULL, 73, NULL, NULL, NULL, 'Y', 'College', 'Y');
INSERT INTO `sys_organization` VALUES (91, '研发部', '研发部', '0105040103', NULL, 79, NULL, NULL, NULL, 'N', 'Department', NULL);
INSERT INTO `sys_organization` VALUES (92, '高速信息', '高速信息', '010505', '11', 1, '1', NULL, NULL, NULL, NULL, 'Y');

-- ----------------------------
-- Table structure for sys_resources
-- ----------------------------
DROP TABLE IF EXISTS `sys_resources`;
CREATE TABLE `sys_resources`  (
  `RESOURCE_ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '资源编码',
  `RESOURCE_TYPE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'URL' COMMENT '资源类型',
  `RESOURCE_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资源名称',
  `RESOURCE_DESC` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资源说明',
  `RESOURCE_PATH` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资源访问地址',
  `RESOURCE_PATTERN` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资源匹配规则',
  `PRIORITY` bigint(10) NULL DEFAULT NULL COMMENT '优先级',
  `ENABLE` int(11) NULL DEFAULT NULL COMMENT '是否有效',
  `ISSYS` int(11) NULL DEFAULT NULL COMMENT '是否系统资源',
  `MODULE_ID` bigint(10) NULL DEFAULT NULL COMMENT '模块编码',
  `parent_id` bigint(10) NULL DEFAULT NULL COMMENT '父节点ID',
  PRIMARY KEY (`RESOURCE_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 178 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '资源表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_resources
-- ----------------------------
INSERT INTO `sys_resources` VALUES (1, 'URL', '系统', '拥有所有资源访问权限', NULL, '/**', NULL, NULL, NULL, NULL, 0);
INSERT INTO `sys_resources` VALUES (2, 'URL', '系统管理', '系统管理', NULL, '/', 99, NULL, NULL, NULL, 1);
INSERT INTO `sys_resources` VALUES (3, 'URL', '资源管理', '资源管理', '/access/resourceManageController/index', '/access/resourceManageController/**', 4, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (4, 'URL', '用户管理', '用户管理', '/access/userManageController/index', '/access/userManageController/**', 1, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (5, 'URL', '角色管理', '角色管理', '/access/roleManageController/index', '/access/roleManageController/**', 2, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (6, 'URL', '权限管理', '权限管理', '/access/authManageController/index', '/access/authManageController/**', 3, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (7, 'URL', '菜单管理', '菜单管理', '/access/menuManageController/index', '/access/menuManageController/**', 5, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (10, NULL, '定时任务管理', '定时任务管理', NULL, '/', 98, NULL, NULL, NULL, 1);
INSERT INTO `sys_resources` VALUES (11, NULL, '定时任务', '定时任务', '/auto/scheduleController/index', '/auto/scheduleController/**', 1, NULL, NULL, NULL, 10);
INSERT INTO `sys_resources` VALUES (12, NULL, '日志管理', '日志管理', '/access/sys_logsController/index', '/access/sys_logsController/**', 6, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (13, NULL, '缓存刷新', '缓存刷新', ' ', '/', 7, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (14, NULL, '流程引擎', '流程引擎', ' ', '/', 97, NULL, NULL, NULL, 1);
INSERT INTO `sys_resources` VALUES (15, NULL, '流程模型管理', '流程模型管理', '/service/workflowModelController/index', '/service/workflowModelController/**', 1, NULL, NULL, NULL, 14);
INSERT INTO `sys_resources` VALUES (16, NULL, '流程定义管理', '流程定义管理', '/service/processDefinitionController/index', '/service/processDefinitionController/**', 2, NULL, NULL, NULL, 14);
INSERT INTO `sys_resources` VALUES (17, NULL, '流程实例管理', '流程实例管理', '/service/processInstanceController/index', '/service/processInstanceController/**', 3, NULL, NULL, NULL, 14);
INSERT INTO `sys_resources` VALUES (18, NULL, '历史流程管理', '历史流程管理', '/service/hisProcessInstanceController/index', '/service/hisProcessInstanceController/**', 4, NULL, NULL, NULL, 14);
INSERT INTO `sys_resources` VALUES (19, NULL, '组织管理', '组织管理', '/access/organizationManageController/index', '/access/organizationManageController/**', 17, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (20, NULL, '数据权限', NULL, '/access/dataAuthoritiesController/index', '/access/dataAuthoritiesController/**', 9, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (21, NULL, '字典管理', '字典管理', '/access/dictionaryController/index', '/access/dictionaryController/**', 9, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (22, NULL, '我的待办事项', '我的待办事项', '/service/ruTaskController/index', '/service/ruTaskController/**', 5, NULL, NULL, NULL, 14);
INSERT INTO `sys_resources` VALUES (23, NULL, '用户组管理', NULL, '/access/groupsController/index', '/access/groupsController/**', 2, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (24, NULL, '流程申请', '流程申请', NULL, '/', 97, NULL, NULL, NULL, 1);
INSERT INTO `sys_resources` VALUES (25, NULL, '通用流程申请', '通用流程申请', '/auto/flowController/index', '/auto/flowController/**', 1, NULL, NULL, NULL, 24);
INSERT INTO `sys_resources` VALUES (26, NULL, '我审批的流程', '我审批的流程', '/service/hiTaskinstController/index', '/service/hiTaskinstController/**', 6, NULL, NULL, NULL, 14);
INSERT INTO `sys_resources` VALUES (39, NULL, '公共资源', NULL, NULL, '/', NULL, NULL, NULL, NULL, 1);
INSERT INTO `sys_resources` VALUES (40, NULL, '系统index页', NULL, '/auto/access/index', '/auto/access/**', NULL, NULL, NULL, NULL, 39);
INSERT INTO `sys_resources` VALUES (41, NULL, '系统home页', '服务代码表', '/homeController/index', '/homeController/**', NULL, NULL, NULL, NULL, 39);
INSERT INTO `sys_resources` VALUES (43, NULL, '登陆用户信息', NULL, '/auto/userManageController/userInfo', '/auto/userManageController/userInfo', NULL, NULL, NULL, NULL, 39);
INSERT INTO `sys_resources` VALUES (45, NULL, '数据字典公共方法', NULL, '/auto/dictionaryController/getDicsByTypeCommon', '/auto/dictionaryController/getDicsByTypeCommon', NULL, NULL, NULL, NULL, 39);
INSERT INTO `sys_resources` VALUES (46, NULL, '技术员查询', '技术员查询', '/auto/userManageController/getTechnician', '/auto/userManageController/getTechnician', NULL, NULL, NULL, NULL, 4);
INSERT INTO `sys_resources` VALUES (58, NULL, '字典查询text内容', NULL, '/auto/dictionaryController/getTextByVal', '/auto/dictionaryController/getTextByVal', NULL, NULL, NULL, NULL, 21);
INSERT INTO `sys_resources` VALUES (94, NULL, '获取页面初始化数据', 'getPageDatas', NULL, '/**/*Controller/getPageDatas', 5, NULL, NULL, NULL, 39);
INSERT INTO `sys_resources` VALUES (95, NULL, '根据父节点获取子节点列表', 'getByParent', NULL, '/auto/dictionaryController/getByParent', 2, NULL, NULL, NULL, 21);
INSERT INTO `sys_resources` VALUES (97, NULL, '备注', '备注', NULL, '/auto/remarkController/**', 6, NULL, NULL, NULL, 39);
INSERT INTO `sys_resources` VALUES (139, NULL, '系统参数', '系统参数', '/access/systemParametersController/index', '/access/systemParametersController/**', 18, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (147, NULL, '修改历史', '修改历史', NULL, '/auto/recordController/**', 7, NULL, NULL, NULL, 39);
INSERT INTO `sys_resources` VALUES (168, NULL, '我发起的流程', NULL, '/service/hisProcessInstanceController/startedByIndex', '/service/hisProcessInstanceController/**', 7, NULL, NULL, NULL, 14);
INSERT INTO `sys_resources` VALUES (172, NULL, '元数据缓存', '元数据缓存', '/access/cacheManageController/refreashMetadataSource', '/access/cacheManageController/refreashMetadataSource', 1, NULL, NULL, NULL, 13);
INSERT INTO `sys_resources` VALUES (173, NULL, '字典缓存', '字典缓存', '/access/cacheManageController/refreashDictCache', '/access/cacheManageController/refreashDictCache', 2, NULL, NULL, NULL, 13);
INSERT INTO `sys_resources` VALUES (174, NULL, '数据源监控', NULL, '/druid', '/druid/**', 100, NULL, NULL, NULL, 2);
INSERT INTO `sys_resources` VALUES (175, NULL, '配置管理', NULL, NULL, '/', 1, NULL, NULL, NULL, 1);
INSERT INTO `sys_resources` VALUES (176, NULL, '应用管理', '应用管理', '/sso/servicesController/index', '/sso/servicesController/**', 1, NULL, NULL, NULL, 175);
INSERT INTO `sys_resources` VALUES (177, NULL, '开发者服务管理', '开发者服务管理', '/sso/servicesController/client/index', '/sso/servicesController/client/**;/sso/servicesController/getPageDatas;/sso/servicesController/loadOne;/sso/servicesController/delete', NULL, NULL, NULL, NULL, 175);

-- ----------------------------
-- Table structure for sys_roles
-- ----------------------------
DROP TABLE IF EXISTS `sys_roles`;
CREATE TABLE `sys_roles`  (
  `ROLE_ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '角色主键',
  `ROLE_CODE` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
  `ROLE_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色名称',
  `ROLE_DESC` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色说明',
  `ENABLE` int(11) NULL DEFAULT 1 COMMENT '是否可用',
  `ISSYS` int(11) NULL DEFAULT NULL COMMENT '是否系统权限',
  `DT_CREATE` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `DT_UPDATE` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`ROLE_ID`) USING BTREE,
  UNIQUE INDEX `un_code`(`ROLE_CODE`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_roles
-- ----------------------------
INSERT INTO `sys_roles` VALUES (1, 'SUPER_ADMIN', '超级管理员', '超级管理员', 1, 0, '2016-09-20 11:09:58', NULL);
INSERT INTO `sys_roles` VALUES (2, 'C001', '客户端用户', '普通用户', 1, NULL, '2019-02-19 11:55:43', NULL);

-- ----------------------------
-- Table structure for sys_roles_authorities
-- ----------------------------
DROP TABLE IF EXISTS `sys_roles_authorities`;
CREATE TABLE `sys_roles_authorities`  (
  `ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `AUTHORITY_ID` bigint(10) NOT NULL COMMENT '权限编码',
  `ROLE_ID` bigint(10) NOT NULL COMMENT '角色编码',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色权限关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_roles_authorities
-- ----------------------------
INSERT INTO `sys_roles_authorities` VALUES (6, 2, 1);
INSERT INTO `sys_roles_authorities` VALUES (27, 1, 2);
INSERT INTO `sys_roles_authorities` VALUES (28, 3, 2);

-- ----------------------------
-- Table structure for sys_roles_moudles
-- ----------------------------
DROP TABLE IF EXISTS `sys_roles_moudles`;
CREATE TABLE `sys_roles_moudles`  (
  `ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `MODULE_ID` bigint(10) NOT NULL COMMENT '菜单编码',
  `ROLE_ID` bigint(10) NOT NULL COMMENT '角色编码',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '控制角色对模块的访问权，主要用于生成菜单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_roles_moudles
-- ----------------------------

-- ----------------------------
-- Table structure for sys_users
-- ----------------------------
DROP TABLE IF EXISTS `sys_users`;
CREATE TABLE `sys_users`  (
  `USER_ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '用户编码',
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户登录名',
  `NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `PASSWORD` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `DT_CREATE` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `LAST_LOGIN` datetime(0) NULL DEFAULT NULL COMMENT '最后登录日期',
  `DEADLINE` datetime(0) NULL DEFAULT NULL COMMENT '截止日期',
  `LOGIN_IP` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最后登录IP地址',
  `STATUS` int(1) NULL DEFAULT NULL COMMENT '是否可用(新版本使用)',
  `ENABLED` int(11) NULL DEFAULT NULL COMMENT '是否可用(老版本使用)',
  `ACCOUNT_NON_EXPIRED` int(11) NULL DEFAULT NULL COMMENT '用户是否过期',
  `ACCOUNT_NON_LOCKED` int(11) NULL DEFAULT NULL COMMENT '用户是否锁定',
  `CREDENTIALS_NON_EXPIRED` int(11) NULL DEFAULT NULL COMMENT '用户证书是否有效',
  `PHONE` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机',
  `EMAIL` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `ORGANIZATION_ID` bigint(10) NULL DEFAULT NULL COMMENT '所属组织ID',
  `ORGANIZATION_CODE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属组织编码',
  `USER_ORG_IDS` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户全部的组织ID集合',
  `USER_COMP_IDS` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户所属的全部公司ID集合',
  `PRIMARY_COMP_ID` bigint(10) NULL DEFAULT NULL COMMENT '主公司ID',
  `REMARKS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `DATA_ID` bigint(20) NULL DEFAULT NULL COMMENT '数据权限id',
  PRIMARY KEY (`USER_ID`) USING BTREE,
  UNIQUE INDEX `PK_U`(`USERNAME`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

ALTER TABLE sys_users`
ADD COLUMN `ID0001` varchar(100) NULL COMMENT '主数据员工编码' AFTER `group_id`,
ADD COLUMN `DES0007` varchar(255) NULL COMMENT '职位名称' AFTER `ID0001`,
ADD COLUMN `ID0003` varchar(255) NULL COMMENT '职位编号' AFTER `DES0007`;
-- ----------------------------
-- Records of sys_users
-- ----------------------------
INSERT INTO `sys_users` VALUES (1, 'admin', '超级管理员', '{SSHA}7o2APaSyFpzq0vtkg2nZuh1FrbAL8bIwrwtgzA==', '2016-09-11 11:21:08', NULL, NULL, '直管', 1, 1, 0, 0, 0, '18158432342', '<EMAIL>', 91, '0', '91,79', 'ALL', -1, NULL, NULL);
INSERT INTO `sys_users` VALUES (2, 'zhangsan123', '张三', '{SSHA}amM0JxQc/8fD5NRP8A4vgw97RaQjvO5Ju89g1A==', NULL, NULL, NULL, NULL, 1, 1, 0, NULL, NULL, '18158432343', '', NULL, '', '79', '1,73', 79, NULL, NULL);
INSERT INTO `sys_users` VALUES (4, 'casuser1', '马云', '{SSHA}e+xm6Cty/WZC10ubOSKb/DYe6pNvpUbeEq3ttg==', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, '18158432342', '', NULL, NULL, '92', '1', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_users_data_authorities
-- ----------------------------
DROP TABLE IF EXISTS `sys_users_data_authorities`;
CREATE TABLE `sys_users_data_authorities`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `data_id` bigint(20) NOT NULL COMMENT '数据权限ID',
  `org_id` bigint(20) NULL DEFAULT NULL COMMENT '组织ID',
  PRIMARY KEY (`user_id`, `data_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与数据权限关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_users_data_authorities
-- ----------------------------

-- ----------------------------
-- Table structure for sys_users_groups
-- ----------------------------
DROP TABLE IF EXISTS `sys_users_groups`;
CREATE TABLE `sys_users_groups`  (
  `user_id` bigint(11) NOT NULL COMMENT '用户ID',
  `group_id` bigint(11) NOT NULL COMMENT '用户组ID',
  `org_id` bigint(11) NULL DEFAULT NULL COMMENT '组织ID',
  PRIMARY KEY (`user_id`, `group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与用户组关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_users_groups
-- ----------------------------

-- ----------------------------
-- Table structure for sys_users_roles
-- ----------------------------
DROP TABLE IF EXISTS `sys_users_roles`;
CREATE TABLE `sys_users_roles`  (
  `ID` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `ROLE_ID` bigint(10) NOT NULL COMMENT '角色编码',
  `USER_ID` bigint(10) NOT NULL COMMENT '用户编码',
  `company_id` bigint(10) NULL DEFAULT NULL COMMENT '公司/域',
  `create_dt` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `creater` bigint(10) NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 261 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_users_roles
-- ----------------------------
INSERT INTO `sys_users_roles` VALUES (1, 1, 1, NULL, NULL, NULL);
INSERT INTO `sys_users_roles` VALUES (251, 1, 2, NULL, NULL, NULL);
INSERT INTO `sys_users_roles` VALUES (252, 1, 2, NULL, NULL, NULL);
INSERT INTO `sys_users_roles` VALUES (253, 1, 2, NULL, NULL, NULL);
INSERT INTO `sys_users_roles` VALUES (260, 2, 2, -1, NULL, NULL);

-- ----------------------------
-- Function structure for getDicChildList
-- ----------------------------
DROP FUNCTION IF EXISTS `getDicChildList`;
delimiter ;;
CREATE FUNCTION `getDicChildList`(dicId INT)
 RETURNS varchar(4000) CHARSET utf8mb4
BEGIN  
DECLARE sTemp VARCHAR(4000);  
DECLARE sTempChd VARCHAR(4000);  
  
SET sTemp = '$';  
SET sTempChd = cast(dicId as char);  
  
WHILE sTempChd is not NULL DO  
SET sTemp = CONCAT(sTemp,',',sTempChd);  
SELECT group_concat(id) INTO sTempChd FROM sys_dictionary where FIND_IN_SET(parent,sTempChd)>0;  
END WHILE;  
return sTemp;  
END
;;
delimiter ;

-- ----------------------------
-- Function structure for getModuleChildList
-- ----------------------------
DROP FUNCTION IF EXISTS `getModuleChildList`;
delimiter ;;
CREATE FUNCTION `getModuleChildList`(moduleId INT)
 RETURNS varchar(4000) CHARSET utf8
BEGIN  
DECLARE sTemp VARCHAR(4000);  
DECLARE sTempChd VARCHAR(4000);  
  
SET sTemp = '$';  
SET sTempChd = cast(moduleId as char);  
  
WHILE sTempChd is not NULL DO  
SET sTemp = CONCAT(sTemp,',',sTempChd);  
SELECT group_concat(module_id) INTO sTempChd FROM sys_modules where FIND_IN_SET(parent,sTempChd)>0;  
END WHILE;  
return sTemp;  
END
;;
delimiter ;

-- ----------------------------
-- Function structure for getOrganizationChildList
-- ----------------------------
DROP FUNCTION IF EXISTS `getOrganizationChildList`;
delimiter ;;
CREATE FUNCTION `getOrganizationChildList`(orgId INT)
 RETURNS varchar(4000) CHARSET utf8
BEGIN  
DECLARE sTemp VARCHAR(4000);  
DECLARE sTempChd VARCHAR(4000);  
  
SET sTemp = '$';  
SET sTempChd = cast(orgId as char);  
  
WHILE sTempChd is not NULL DO  
SET sTemp = CONCAT(sTemp,',',sTempChd);  
SELECT group_concat(ORGANIZATION_ID) INTO sTempChd FROM sys_organization where FIND_IN_SET(parent_id,sTempChd)>0;  
END WHILE;  
return sTemp;  
END
;;
delimiter ;

-- ----------------------------
-- Function structure for getOrgTotalName
-- ----------------------------
DROP FUNCTION IF EXISTS `getOrgTotalName`;
delimiter ;;
CREATE FUNCTION `getOrgTotalName`(`rid` varchar(11))
 RETURNS varchar(300) CHARSET utf8mb4
  COMMENT '根据组织ID获取组织全称'
BEGIN 
 DECLARE TOTAL_NAME VARCHAR(1000); -- 返回全称
 DECLARE CURR_ID VARCHAR(100); -- 当前节点ID
 DECLARE V_COUNT INT(5);
 
 -- 初始化
 SET CURR_ID = cast(rid as CHAR);
 SET TOTAL_NAME = '';
 -- 判断是否存在
 SELECT COUNT(1) INTO V_COUNT FROM sys_organization WHERE organization_id = CURR_ID;
 IF V_COUNT > 0 THEN
	 SELECT organization_name INTO TOTAL_NAME FROM sys_organization where organization_id = CURR_ID; 
	 WHILE CURR_ID is not null and V_COUNT > 0 DO
		 SELECT parent_id INTO CURR_ID FROM sys_organization where organization_id = CURR_ID; -- 查找父节点为当前节点
		 
		 SELECT COUNT(1) INTO V_COUNT FROM sys_organization WHERE organization_id = CURR_ID;
     IF CURR_ID is not null and V_COUNT > 0 THEN -- 如果父节点存在则拼接字符串
			 SELECT concat(organization_name,'>',TOTAL_NAME) INTO TOTAL_NAME FROM sys_organization where organization_id = CURR_ID; -- 查询出父节点
		 END IF;
	 END WHILE; 
 END IF;
 RETURN TOTAL_NAME; 
 END
;;
delimiter ;

-- ----------------------------
-- Function structure for getResourceChildList
-- ----------------------------
DROP FUNCTION IF EXISTS `getResourceChildList`;
delimiter ;;
CREATE FUNCTION `getResourceChildList`(resourceId INT)
 RETURNS varchar(4000) CHARSET utf8
BEGIN  
DECLARE sTemp VARCHAR(4000);  
DECLARE sTempChd VARCHAR(4000);  
  
SET sTemp = '$';  
SET sTempChd = cast(resourceId as char);  
  
WHILE sTempChd is not NULL DO  
SET sTemp = CONCAT(sTemp,',',sTempChd);  
SELECT group_concat(resource_id) INTO sTempChd FROM sys_resources where FIND_IN_SET(parent_id,sTempChd)>0;  
END WHILE;  
return sTemp;  
END
;;
delimiter ;

-- ----------------------------
-- Function structure for INTE_ARRAY
-- ----------------------------
DROP FUNCTION IF EXISTS `INTE_ARRAY`;
delimiter ;;
CREATE FUNCTION `INTE_ARRAY`(setA varchar(255),setB varchar(255))
 RETURNS int(1)
BEGIN
    DECLARE idx INT DEFAULT 0 ; -- B 集合单元索引 
    DECLARE len INT DEFAULT 0;-- B 集合表达式长度
    DECLARE llen INT DEFAULT 0;-- 最后检查位置
    DECLARE clen INT DEFAULT 0;-- 当前检查位置
    DECLARE tmpStr varchar(255);-- 临时检查数据集
    DECLARE curt varchar(255);-- B 当前检查的单元
    SET len = LENGTH(setB);
    WHILE idx < len DO
        SET idx = idx + 1;
        SET tmpStr = SUBSTRING_INDEX(setB,",",idx);
        SET clen = LENGTH(tmpStr);
-- 获取当前 setB 中的单元
        IF idx = 1 THEN SET curt = tmpStr;
        ELSE SET curt = SUBSTRING(setB,llen+2,clen-llen-1);
        END IF;
-- 检查是否存在于 setA 中
        IF FIND_IN_SET(curt,setA) > 0 THEN RETURN 1;
        END IF;
-- 当前检查终点与上次检查终点相同则跳出
        IF clen <= llen THEN RETURN 0;
        END IF;
 
        SET llen = clen;
    END WHILE;
    RETURN 0;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;


