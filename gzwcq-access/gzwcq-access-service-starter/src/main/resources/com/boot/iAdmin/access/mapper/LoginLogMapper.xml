<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.iAdmin.access.mapper.ILoginLogMapper">

	<resultMap type="com.boot.iAdmin.access.model.loginLog.LoginLog" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="org_id" property="orgId"/>
		<result column="org_name" property="orgName"/>
		<result column="username" property="username"/>
		<result column="name" property="name"/>
		<result column="login_time" property="loginTime"/>
		<result column="ip" property="ip"/>
		<result column="type" property="type"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.boot.iAdmin.access.model.loginLog.LoginLogVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		org_id, 
		org_name, 
		username, 
		name, 
		login_time, 
		ip, 
		type
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.org_id, 
		t.org_name, 
		t.username, 
		t.name, 
		t.login_time, 
		t.ip, 
		t.type
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{orgId}, 
		#{orgName}, 
		#{username}, 
		#{name}, 
		#{loginTime}, 
		#{ip}, 
		#{type}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null">
			and t.id = #{id}
		</if>
		<if test="orgId != null">
			and t.org_id = #{orgId}
		</if>
		<if test="orgName != null and orgName != ''">
			and t.org_name = #{orgName}
		</if>
		<if test="username != null and username != ''">
			and t.username = #{username}
		</if>
		<if test="name != null and name != ''">
			and t.name = #{name}
		</if>
		<if test="loginTime != null">
			and t.login_time = #{loginTime}
		</if>
		<if test="ip != null and ip != ''">
			and t.ip = #{ip}
		</if>
		<if test="type != null and type != ''">
			and t.type = #{type}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.boot.iAdmin.access.model.loginLog.LoginLog" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" resultType="Long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_login_log (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update sys_login_log set isDeleted = 'Y' where
		id in
		<foreach collection="loginLogs" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update sys_login_log set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from sys_login_log  where
		id in
		<foreach collection="loginLogs" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.util.Map">
		delete from sys_login_log  where id = #{id}
	</delete>
	
	<select id="selectLoginLogByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from sys_login_log
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update sys_login_log
		<set>
			<if test="orgId != null">
				org_id=#{orgId},
			</if>
			<if test="orgName != null">
				org_name=#{orgName},
			</if>
			<if test="username != null">
				username=#{username},
			</if>
			<if test="name != null">
				name=#{name},
			</if>
			<if test="loginTime != null">
				login_time=#{loginTime},
			</if>
			<if test="ip != null">
				ip=#{ip},
			</if>
			<if test="type != null">
				type=#{type}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update sys_login_log
		<set>
			org_id=#{orgId},
			org_name=#{orgName},
			username=#{username},
			name=#{name},
			login_time=#{loginTime},
			ip=#{ip},
			type=#{type}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.boot.iAdmin.access.model.loginLog.LoginLog" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			sys_login_log t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalLoginLogs" parameterType="com.boot.iAdmin.access.model.loginLog.LoginLogParam" resultType="java.lang.Long">
		select
			count(id)
		from sys_login_log t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryLoginLogForList" parameterType="com.boot.iAdmin.access.model.loginLog.LoginLogParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			sys_login_log t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.boot.iAdmin.access.model.loginLog.LoginLog" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from sys_login_log t
		where t.id != #{id}
			<if test="orgId != null and orgId != ''">
				and t.org_id = #{orgId}
			</if>
			<if test="orgName != null and orgName != ''">
				and t.org_name = #{orgName}
			</if>
			<if test="username != null and username != ''">
				and t.username = #{username}
			</if>
			<if test="name != null and name != ''">
				and t.name = #{name}
			</if>
			<if test="loginTime != null">
				and t.login_time = #{loginTime}
			</if>
			<if test="ip != null and ip != ''">
				and t.ip = #{ip}
			</if>
			<if test="type != null and type != ''">
				and t.type = #{type}
			</if>
	</select>

</mapper>