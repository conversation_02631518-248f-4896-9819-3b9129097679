<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.OrganizationMapper">

    <sql id="columns">
        organization_id,
        organization_name,
        organization_desc,
        organization_code,
        parent_id,
        parent_code,
        priority,
        create_time,
        create_user,
        create_unit,
        last_update_time,
        last_update_user,
        biztypes,
        syncode,
        businesstype,
        business_level,
        parents,
        parents_old,
        visibles,
        audit_hosting,
        business_nature,
        SSGZJGJG,
        isdeleted
    </sql>

    <sql id="vals">
        #{organization_id},
        #{organization_name},
        #{organization_desc},
        #{organization_code},
        #{parent_id},
        #{parent_code},
        #{priority},
        #{create_time},
        #{create_user},
        #{create_unit},
        #{last_update_time},
        #{last_update_user},
        #{biztypes},
        #{syncode},
        #{businesstype},
        #{business_level},
        #{parents},
        #{parents_old},
        #{visibles},
        #{audit_hosting},
        #{business_nature},
        #{ssgzjgjg},
        #{isdeleted}
    </sql>

    <sql id="cols">
        a.organization_id,
        a.organization_name,
        a.organization_desc,
        a.organization_code,
        a.parent_id,
        a.parent_code,
        a.priority,
        a.create_time,
        a.create_user,
        a.create_unit,
        a.last_update_time,
        a.last_update_user,
        a.biztypes,
        a.syncode,
        a.businesstype,
        a.business_level,
        a.parents,
        a.parents_old,
        a.visibles,
        a.audit_hosting,
        a.business_nature,
        a.SSGZJGJG,
        a.isdeleted
    </sql>

    <!--带别名的列-->
    <sql id="columnsAlias">
        t.organization_id,
        t.organization_name,
        t.organization_desc,
        t.organization_code,
        t.parent_id,
        t.parent_code,
        t.priority,
        t.create_time,
        t.create_user,
        t.create_unit,
        t.last_update_time,
        t.last_update_user,
        t.biztypes,
        t.syncode,
        t.businesstype,
        t.business_level,
        t.parents,
        t.parents_old,
        t.visibles,
        t.audit_hosting,
        t.business_nature,
        t.SSGZJGJG,
        t.isdeleted
    </sql>

    <!-- 给where查询的表起别名t,方便多表关联查询 -->
    <sql id="whereSql">
        <where>
            <if test="obj !=null">
                <if test="obj.organization_id != null and obj.organization_id != ''">
                    and t.organization_id = #{obj.organization_id}
                </if>
                <if test="obj.organization_name != null and obj.organization_name != ''">
                    and t.organization_name LIKE CONCAT(CONCAT('%', #{obj.organization_name}),'%')
                </if>
                <if test="obj.organization_desc != null and obj.organization_desc != ''">
                    and t.organization_desc = #{obj.organization_desc}
                </if>
                <if test="obj.organization_code != null and obj.organization_code != ''">
                    and t.organization_code = #{obj.organization_code}
                </if>
                <if test="obj.parent_id != null and obj.parent_id != ''">
                    and t.parent_id = #{obj.parent_id}
                </if>
                <if test="obj.parent_code != null and obj.parent_code != ''">
                    and t.parent_code = #{obj.parent_code}
                </if>
                <if test="obj.priority != null">
                    and t.priority = #{obj.priority}
                </if>
                <if test="obj.create_time != null">
                    and t.create_time = #{obj.create_time}
                </if>
                <if test="obj.create_user != null and obj.create_user != ''">
                    and t.create_user = #{obj.create_user}
                </if>
                <if test="obj.create_unit != null and obj.create_unit != ''">
                    and t.create_unit = #{obj.create_unit}
                </if>
                <if test="obj.last_update_time != null">
                    and t.last_update_time = #{obj.last_update_time}
                </if>
                <if test="obj.last_update_user != null and obj.last_update_user != ''">
                    and t.last_update_user = #{obj.last_update_user}
                </if>
                <if test="obj.biztypes != null and obj.biztypes != ''">
                    and t.biztypes = #{obj.biztypes}
                </if>
                <if test="obj.syncode != null and obj.syncode != ''">
                    and t.syncode = #{obj.syncode}
                </if>
                <if test="obj.businesstype != null and obj.businesstype != ''">
                    and t.businesstype = #{obj.businesstype}
                </if>
                <if test="obj.business_level != null and obj.business_level != ''">
                    and t.business_level = #{obj.business_level}
                </if>
                <if test="obj.business_nature != null and obj.business_nature != ''">
                    and t.business_nature = #{business_nature}
                </if>
                <if test="obj.isdeleted != null and obj.isdeleted != ''">
                    and t.isdeleted = #{isdeleted}
                </if>
            </if>
        </where>
    </sql>

    <sql id="query">
        <if test="organization_id != null">
            and a.organization_id = #{organization_id}
        </if>
        <if test="organization_name != null and organization_name != ''">
            and a.organization_name = #{organization_name}
        </if>
        <if test="organization_code != null and organization_code != ''">
            and a.organization_code = #{organization_code}
        </if>
        <if test="organization_desc != null and organization_desc != ''">
            and a.organization_desc = #{organization_desc}
        </if>
    </sql>

    <!-- 根据父节点ID查找所有子节点 -->
    <select id="loadOrganizationByParentId" parameterType="string"
            resultType="com.boot.iAdmin.access.model.common.ZTreeNode">
        select a.organization_id id,
        a.organization_name name,
        a.organization_desc description,
        a.organization_code code,
        (select count(1) from sys_organization t where t.parent_id = a.organization_id and t.isdeleted = 'N') childNum
        from sys_organization a
        where a.parent_id = #{id} and a.isdeleted = 'N'
        order by priority
    </select>

    <!-- 获取单个组织信息 -->
    <select id="selectOrganization" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization"
            resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select
        <include refid="cols"/>
        ,b.organization_name parent_node
        from sys_organization a left join sys_organization b on a.parent_id = b.organization_id
        where 1 = 1 and a.isdeleted = 'N'
        <include refid="query"/>
    </select>

    <!-- 根据ID加载对应的树信息，完全忽略其它属性 -->
    <select id="selectOrganizationById" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization"
            resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select
        <include refid="cols"/>
        from sys_organization a
        where organization_id = #{organization_id} and isdeleted = 'N'
    </select>

    <!-- 更新组织信息 -->
    <update id="updateIgnoreNull" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        update sys_organization
        <set>
            <if test="organization_id != null">
                organization_id = #{organization_id},
            </if>
            <if test="organization_name != null">
                organization_name = #{organization_name},
            </if>
            <if test="organization_desc != null">
                organization_desc = #{organization_desc},
            </if>
            <if test="organization_code != null">
                organization_code = #{organization_code},
            </if>
            <if test="parent_id != null">
                parent_id = #{parent_id},
            </if>
            <if test="parents != null">
                PARENTS = #{parents},
            </if>
            <if test="parent_code != null">
                parent_code = #{parent_code},
            </if>
            <if test="priority != null">
                priority = #{priority},
            </if>
            <if test="create_time != null">
                create_time = #{create_time},
            </if>
            <if test="create_user != null">
                create_user = #{create_user},
            </if>
            <if test="create_unit != null">
                create_unit = #{create_unit},
            </if>
            <if test="last_update_time != null">
                last_update_time = #{last_update_time},
            </if>
            <if test="last_update_user != null">
                last_update_user = #{last_update_user},
            </if>
            <if test="biztypes != null">
                biztypes = #{biztypes},
            </if>
            <if test="syncode != null">
                syncode = #{syncode},
            </if>
            <if test="businesstype != null">
                businesstype = #{businesstype},
            </if>
            <if test="business_level != null">
                business_level = #{business_level},
            </if>
            <if test="visibles != null">
                visibles = #{visibles},
            </if>
            <if test="audit_hosting != null">
                audit_hosting = #{audit_hosting},
            </if>
            <if test="business_nature != null">
                business_nature = #{business_nature},
            </if>
            <if test="ssgzjgjg != null">
                SSGZJGJG = #{ssgzjgjg},
            </if>
            <if test="isdeleted != null">
                isdeleted = #{isdeleted}
            </if>
        </set>
        where organization_id = #{organization_id}
    </update>

    <!-- 新增组织 -->
    <insert id="insertOrganization" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        <selectKey keyProperty="organization_id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
        insert into sys_organization (<include refid="columns"/>)
        values (<include refid="vals"/>)
    </insert>

    <!-- 新增组织 -->
    <insert id="insertOrg" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        insert into sys_organization (<include refid="columns"/>)
        values (<include refid="vals"/>)
    </insert>

    <!-- 删除组织及其下子组织 -->
    <update id="deleteOrganization" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        update
        sys_organization set isdeleted = 'Y'
        WHERE
        organization_id IN (
        SELECT
        *
        FROM
        (
        SELECT
        organization_id
        FROM
        sys_organization,
        (
        SELECT
        getOrganizationChildList (#{organization_id}) AS childOrgs
        ) t
        WHERE
        FIND_IN_SET(
        organization_id,
        t.childOrgs
        )
        ) AS TEMP
        )
    </update>

    <select id="getTotalOrganization" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization"
            resultType="int">
        SELECT
        count(0)
        FROM
        sys_users,
        (
        SELECT getOrganizationChildList (#{organization_id}) AS childOrgs
        ) t
        WHERE
        FIND_IN_SET(
        organization_id,
        t.childOrgs
        )
    </select>

    <!-- 根据唯一性参数查询数据 -->
    <select id="selectForUnique" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization"
            resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select t.organization_name from sys_organization t
        where t.organization_id != #{organization_id} and t.isdeleted = 'N'
        <if test="organization_name != null and organization_name != ''">
            and t.organization_name = #{organization_name}
        </if>
        <if test="parent_id != null and parent_id != ''">
            and t.parent_id = #{parent_id}
        </if>
        <if test="organization_code != null and organization_code != ''">
            and t.organization_code = #{organization_code}
        </if>
    </select>

    <!-- 根据组织父节点ID，查询出当父节点下面的最大组织编码,结果可能为空，如果为空，则当前增加的子节点为01节点 -->
    <select id="selectMaxOrgCodeByParId" resultType="java.util.Map">
        SELECT
        MAX(ORGANIZATION_CODE) AS ORGANIZATION_CODE
        FROM
        sys_organization
        WHERE
        PARENT_ID = #{parent_id}
        AND ORGANIZATION_CODE IS NOT NULL
        AND ORGANIZATION_CODE != ''
        and isdeleted = 'N'
    </select>

    <select id="getOrgNameById" resultType="java.lang.String">
        select organization_desc from sys_organization where organization_id=#{org_id}
    </select>

    <!-- 查询组织名称数据集合 -->
    <select id="getOrganizationList" resultType="com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode"
            parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select o.organization_id as id,o.organization_name title,o.parent_id as parent from sys_organization o
        where 1=1 and o.isdeleted = 'N'
        <if test="organization_id != null">
            and o.organization_id !=#{organization_id}
        </if>
    </select>

    <!-- 查询组织名称数据集合 -->
    <select id="getCurrAndChildList" resultType="com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode"
            parameterType="string">
        select o.organization_id as id,o.organization_name title,o.parent_id as parent,o.parent_id parentId,o.organization_code code,
        o.parents,o.SSGZJGJG, su.NAME as createUserName,
        (select group_concat(t.organization_id) from sys_organization t where t.parent_id = o.organization_id) childrenIdS
        from sys_organization o
        left join sys_users su on o.CREATE_USER = su.USER_ID
        where FIND_IN_SET(#{organization_id},o.PARENTS) and o.isdeleted = 'N' order by priority
    </select>

    <!-- 分页查询对象和数据总量查询 -->
    <select id="queryOrganizationByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel"
            resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select
        <include refid="columnsAlias"/>
        from
        sys_organization t
        where t.isdeleted = 'N'
        <include refid="whereSql"/>
        order by organization_id desc
        limit #{offset}, #{limit}
    </select>

    <select id="queryTotalOrganizations" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel"
            resultType="java.lang.Long">
        select
        count(0)
        from sys_organization t
        where t.isdeleted = 'N'
        <include refid="whereSql"/>
    </select>


    <select id="selectSubOrgId" parameterType="Integer" resultType="java.lang.String">
        SELECT getOrganizationChildList(#{orgid})
    </select>

    <select id="loadOneByOrgCode" parameterType="java.lang.String"
            resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select
        <include refid="columnsAlias"/>
        from
        sys_organization t
        where t.organization_code=#{org_code}
    </select>

    <select id="getOrgsByIds" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo"
            parameterType="java.util.Map">
        select
        <include refid="columnsAlias"/>
        ,getOrgTotalName(t.organization_id) totalName
        from
        sys_organization t
        where
        FIND_IN_SET(t.organization_id,#{ordIds})
    </select>

    <select id="selectForList" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo"
            parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select
        <include refid="columnsAlias"/>
        from
        sys_organization t
        <where>
            and t.isdeleted = 'N'
            <if test="organization_id != null and organization_id != ''">
                and t.organization_id = #{organization_id}
            </if>
            <if test="organization_name != null and organization_name != ''">
                and (t.organization_name like concat(concat('%',#{organization_name}),'%')
                or t.organization_code = #{organization_name})
            </if>
            <if test="organization_desc != null and organization_desc != ''">
                and t.organization_desc = #{organization_desc}
            </if>
            <if test="organization_code != null and organization_code != ''">
                and t.organization_code = #{organization_code}
            </if>
            <if test="parent_id != null and parent_id != ''">
                and t.parent_id = #{parent_id}
            </if>
            <if test="parent_code != null and parent_code != ''">
                and t.parent_code = #{parent_code}
            </if>
            <if test="priority != null">
                and t.priority = #{priority}
            </if>
            <if test="create_time != null">
                and t.create_time = #{create_time}
            </if>
            <if test="create_user != null and create_user != ''">
                and t.create_user = #{create_user}
            </if>
            <if test="create_unit != null and create_unit != ''">
                and t.create_unit = #{create_unit}
            </if>
            <if test="last_update_time != null">
                and t.last_update_time = #{last_update_time}
            </if>
            <if test="last_update_user != null and last_update_user != ''">
                and t.last_update_user = #{last_update_user}
            </if>
            <if test="biztypes != null and biztypes != ''">
                and t.biztypes = #{biztypes}
            </if>
            <if test="syncode != null and syncode != ''">
                and t.syncode = #{syncode}
            </if>
            <if test="businesstype != null and businesstype != ''">
                and t.businesstype = #{businesstype}
            </if>
            <if test="business_level != null and business_level != ''">
                and t.business_level = #{business_level}
            </if>
            <if test="parents != null and parents != ''">
                and t.parents like concat(concat('%',#{parents}),'%')
            </if>
        </where>
    </select>

    <select id="getOrgByCode"
            resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select
        <include refid="columnsAlias"/>
        from
        sys_organization t
        where t.ORGANIZATION_CODE=#{code} and t.isdeleted = 'N'
        limit 1
    </select>

    <select id="getAllOrg" resultType="java.util.Map">
        select
        <include refid="columns"/>
        from
        sys_organization
        where isdeleted = 'N'
    </select>

    <select id="getOrgAndLowerOrg" resultType="java.util.Map">
        SELECT
        <include refid="columns"/>
        from sys_organization
        where ORGANIZATION_ID = #{organization_id}
        UNION
        SELECT
        <include refid="columns"/>
        from sys_organization
        where PARENT_ID = #{organization_id}
        and isdeleted = 'N'
    </select>

    <select id="getOrgByOrgId" resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        SELECT
        <include refid="columns"/>
        from sys_organization
        where ORGANIZATION_ID = #{organization_id} and isdeleted = 'N'
    </select>
    <select id="getOrgListEdit" resultType="java.util.Map">
        SELECT
        <include refid="columns"/>
        from sys_organization
        where PARENT_ID = #{organization_id}
        and isdeleted = 'N'
    </select>

    <!--获取本级以及下级组织-->
    <select id="getLoginOrgAndLower" resultType="java.util.Map">
        SELECT
        ORGANIZATION_ID,
        ORGANIZATION_DESC
        FROM
        sys_organization
        <if test="orgId!=null and orgId!=''">
            ,(SELECT getOrganizationChildList (#{orgId}) AS childOrgs ) t
            WHERE
            FIND_IN_SET(ORGANIZATION_ID,t.childOrgs)

        </if>
        <if test="orgIds!=null and orgIds.size>0">
            where ORGANIZATION_ID in
            <foreach collection="orgIds" open="(" close=")" separator="," item="orgId">
                #{orgId}
            </foreach>
        </if>
        limit #{pageNumber},#{limit}
    </select>

    <select id="getAllOrgList" resultType="java.util.Map">
        SELECT
        ORGANIZATION_ID,
        ORGANIZATION_NAME
        FROM
        sys_organization,(SELECT getOrganizationChildList (#{orgId}) AS childOrgs ) t
        WHERE
        FIND_IN_SET(ORGANIZATION_ID,t.childOrgs)
    </select>

    <!--获取本级以及下级组织总数-->
    <select id="getLoginOrgAndLowerCount" resultType="java.lang.Integer">
        SELECT
        COUNT(ORGANIZATION_ID)
        FROM
        sys_organization
        <if test="orgId!=null and orgId!=''">
            ,(SELECT getOrganizationChildList (#{orgId}) AS childOrgs ) t
            WHERE
            FIND_IN_SET(ORGANIZATION_ID,t.childOrgs)
        </if>
        <if test="orgIds!=null and orgIds.size>0">
            where ORGANIZATION_ID in
            <foreach collection="orgIds" open="(" close=")" separator="," item="orgId">
                #{orgId}
            </foreach>
        </if>
    </select>

    <!--查找组织id 根据组织名字-->
    <select id="selectOrganizationByName" resultType="java.lang.String">
        select ORGANIZATION_ID from sys_organization where ORGANIZATION_NAME = #{salesOrgId}
    </select>

    <!--组织编码查找组织名字-->
    <select id="getOrgNameByCode" resultType="java.lang.String">
        select ORGANIZATION_NAME from sys_organization where ORGANIZATION_CODE = #{str}
    </select>

    <!-- 根据父节点ID查找所有子节点,和上面一点点的区别就是在checkbox设置全局性质的值后，还可以针对后端返回的不同值，再次调整checkbox的显示与否,对于组织来说，如果是公司，就不允许直接把组织选择为公司，需要选择到公司下面的具体组织 -->
    <select id="loadOrganizationByParentIdTwo" parameterType="string"
            resultType="com.boot.iAdmin.access.model.common.ZTreeNode">
        select a.organization_id id,
        a.organization_name name,
        a.organization_desc description,
        a.organization_code code,
        a.parents,
        a.PARENT_ID parentId,
        a.business_level orgLevel,
        a.visibles,
        a.audit_hosting,
        a.BUSINESSTYPE businessType,
        a.SSGZJGJG,
        su.NAME as createUserName,
        (select count(1) from sys_organization t where t.parent_id = a.organization_id and t.isdeleted = 'N') childNum,
        (select group_concat(t.organization_id) from sys_organization t where t.parent_id = a.organization_id) childrenIdS,
        case when a.BUSINESSTYPE = '1' then false
        when (select count(1) from view_cq_jbxxb_noDelete cj where cj.UNITID = a.ORGANIZATION_ID) &lt;= 0 then false
        else true end as unitJumpMark,
        (select id from view_cq_jbxxb_noDelete cj where cj.UNITID = a.ORGANIZATION_ID order by cj.CREATE_TIME desc limit 1) as jumpJbxxbId
        from sys_organization a
        left join sys_users su on a.CREATE_USER = su.USER_ID
        where a.parent_id = #{id} and a.isdeleted = 'N'
        order by priority
    </select>
    <select id="selectInsetStatus" resultType="integer">
        select
            count(1)
        from
            sys_organization o
        where
            o.organization_id = #{userOrganizationId}
          aND
            FIND_IN_SET(#{addUserOrganizationId},o.PARENTS)
    </select>
    <!-- 查询组织下属组织列表 -->
    <select id="getOrganizationTree" resultType="com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode"
            parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select o.organization_id as id,o.organization_name title,o.parent_id as parent from sys_organization o
        where 1=1
        and o.organization_id =#{organization_id}
        order by priority
    </select>

    <!-- 查询组织下属组织列表 -->
    <select id="getOrganizationListTree" resultType="com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNode"
            parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select o.organization_id as id,o.organization_name title,o.parent_id as parent,
        (select count(1) from sys_organization t where t.parent_id = o.organization_id and t.isdeleted = 'N') childNum
        from sys_organization o
        where 1=1 and o.isdeleted = 'N'
        and o.parent_id = #{organization_id}
        order by priority
    </select>
    <select id="getChildOrgList" resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        SELECT
        <include refid="columns"/>
        from sys_organization
        where parent_id = (
        select PARENT_ID from sys_organization where ORGANIZATION_ID = #{organization_id})
        and isdeleted = 'N'
        order by priority
    </select>

    <select id="getCurrentUserOrganization" resultType="com.boot.iAdmin.access.model.common.ZTreeNode"
            parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        SELECT
        a.organization_id id,
        a.organization_name NAME,
        a.organization_desc description,
        a.organization_code CODE,
        a.business_level orgLevel,
        ( SELECT count( 1 ) FROM sys_organization t WHERE t.parent_id = a.organization_id and t.isdeleted = 'N') childNum
        FROM
        sys_organization a
        WHERE
        a.ORGANIZATION_ID = #{organization_id}
    </select>

    <select id="selectVisiblesOrgs" parameterType="String"
            resultType="com.boot.iAdmin.access.model.common.ZTreeNode">
        select a.organization_id id,
        a.organization_name name,
        a.organization_desc description,
        a.organization_code code,
        a.parents,
        a.PARENT_ID parentId,
        a.visibles,
        a.audit_hosting,
        (select count(1) from sys_organization t where t.parent_id = a.organization_id and t.isdeleted = 'N') childNum,
        (select group_concat(t.organization_id) from sys_organization t where t.parent_id = a.organization_id) childrenIdS
        from sys_organization a
        where FIND_IN_SET(a.ORGANIZATION_ID,#{visibles})
        and a.isdeleted = 'N'
        order by priority
    </select>

    <select id="selectAuditHosting" resultType="String">
        select ORGANIZATION_ID from sys_organization
        where AUDIT_HOSTING = #{audit_hosting}
        and isdeleted = 'N'
    </select>

    <select id="selectAllChildOrgList" resultType="String">
        select ORGANIZATION_ID from sys_organization
        where FIND_IN_SET(#{orgId},PARENTS)
        and isdeleted = 'N'
        order by priority
    </select>

    <select id="selectAllChildOrgInfoList"
            resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select * from sys_organization
        where FIND_IN_SET(#{orgId},PARENTS)
        and ORGANIZATION_ID != #{orgId}
        and isdeleted = 'N'
        order by priority
    </select>

    <select id="selectAllInfoList"
            resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select * from sys_organization
        where FIND_IN_SET(#{orgId},PARENTS)
          and isdeleted = 'N'
        order by priority
    </select>

    <select id="getInvestedOrgList" parameterType="java.lang.String"
            resultType="com.boot.iAdmin.access.model.organization.SysOrganization">
        SELECT
            *
        FROM
            sys_organization o
        WHERE
                o.ORGANIZATION_ID in (
                SELECT
                    jbxxbs.ORGANIZATION_ID
                FROM
                    (
                        SELECT
                            ranks.ID,
                            ranks.UNITID as ORGANIZATION_ID
                        FROM
                            (
                                SELECT
                                    cjb.ID,
                                    cjb.UNITID,
                                    ROW_NUMBER()over(partition by cjb.UNITID ORDER BY  rbi.RG_TIMEMARK desc) as ran
                                FROM
                                    (select DISTINCT cc.UNITID,cc.JBXX_ID
                                     from cq_czfd cc
                                     where cc.FD_CZRZZJGDM = #{FD_CZRZZJGDM}) cc
                                        JOIN
                                    `cq_jbxxb`  cjb  on cjb.UNITID = cc.UNITID
                                        JOIN
                                    `rg_business_info` rbi on rbi.JBXX_ID = cjb.ID

                                WHERE 1=1

                                  AND
                                    rbi.RG_UNITSTATE = '2'
                                  AND
                                    cjb.JB_DELETED = 'N'
                                  AND
                                    cjb.JB_SHZT = '4'
                            )ranks
                        WHERE ranks.ran = 1
                    ) jbxxbs
                WHERE
                        jbxxbs.id IN(select DISTINCT cc.JBXX_ID
                                     from cq_czfd cc
                                     where cc.FD_CZRZZJGDM = #{FD_CZRZZJGDM})
            )
    </select>

    <select id="selectByCodeFuzzy" parameterType="java.lang.String" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        where organization_code like concat('%',#{code})
        and isdeleted = 'N'
    </select>

    <select id="getLoginUserChildrenOrgs" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        <where>
            find_in_set(#{organization_id},parents)
            <if test="organization_name != null and organization_name != ''">
                and organization_name like concat('%',#{organization_name},'%')
            </if>
            and isdeleted = 'N'
        </where>
    </select>

    <select id="getLastPriority" resultType="java.lang.Double">
        SELECT
        ifnull(CAST(priority as DECIMAL(15,2)),0) as priorityInt
        from sys_organization
        where parent_id = #{parentId}
        order by priorityInt desc
        limit 1
    </select>

    <select id="getExportData" resultType="com.boot.iAdmin.access.model.common.ZTreeNode">
        select a.organization_id id,
        a.organization_name name,
        a.organization_code code,
        a.parents,
        a.SSGZJGJG,
        su.NAME as createUserName,
        (select group_concat(t.organization_id) from sys_organization t where t.parent_id = a.organization_id) childrenIdS
        from sys_organization a
        left join sys_users su on a.CREATE_USER = su.USER_ID
        where a.ORGANIZATION_ID = #{id} and a.isdeleted = 'N'
    </select>


    <select id="getExportOrgTree" resultType="com.boot.IAdmin.common.domain.vueTree.VueAntdTreeSelectNodeExport">
        SELECT
            ttt.*,
            ifnull( group_concat( sd13.text ), ttt.zyhy1 ) zyhy
        FROM
            (
                SELECT
                    o.organization_id AS id,
                    o.organization_name title,
                    o.parent_id AS parent,
                    o.parent_id parentId,
                    o.parents,
                    cqj.jb_zyhy,
                    (
                        SELECT
                            group_concat( sd.text ) zyhy1
                        FROM
                            cq_dwtzqkfd
                                LEFT JOIN (
                                SELECT
                                    val,
                                    text
                                FROM
                                    sys_dictionary
                                WHERE
                                        type_id = ( SELECT id FROM sys_dictionary WHERE type_code = 'INDUSTRY_CLASSIFICATION_TREE' )) sd ON cq_dwtzqkfd.SSHY = sd.val
                        WHERE
                            cq_dwtzqkfd.JBXX_ID = cqj.ID
                    ) zyhy1,
--
                    IFNULL((
                               SELECT
                                   sum( FD_SJZCJ ) sjzb1
                               FROM
                                   cq_czfd
                               WHERE
                                   JBXX_ID = cqj.ID
                           ),
                           IFNULL( ( SELECT sum( SJCZE ) sjzb2 FROM cq_hhrqkfd WHERE JBXX_ID = cqj.ID ), '' )) sjzb,
                    IFNULL((
                               SELECT
                                   GROUP_CONCAT( CONCAT( NAME, RJCZBL, '%' ) ORDER BY RJCZBL DESC ) czr
                               FROM
                                   cq_hhrqkfd
                               WHERE
                                   JBXX_ID = cqj.ID
                               ORDER BY
                                   RJCZBL DESC
                               LIMIT 5
                           ),
                           IFNULL(
                                   (
                                       SELECT
                                           GROUP_CONCAT( CONCAT( FD_CZRMC, FD_GQBL, '%' ) ORDER BY FD_GQBL DESC ) czr
                                       FROM
                                           cq_czfd
                                       WHERE
                                           JBXX_ID = cqj.ID
                                       ORDER BY
                                           FD_GQBL DESC
                                       LIMIT 5
                                   ),
                                   ''
                               )) czr,
                    ifnull(
                            hhqy.HH_ZYJYCS,
                            ifnull( sd8.text, '' )) zcd,
                    ifnull( cqj.JB_ZCRQ, '' ) zcrq,
                    ifnull( sd4.text, '' ) qylb,
                    IFNULL(
                            t4.RJCZBL,
                            IFNULL( t3.FD_GQBL, '' )) parentBl,
                    group_concat( cho.organization_id ) childrenIdS

                FROM
                    sys_organization o
                        LEFT JOIN sys_users su ON o.CREATE_USER = su.USER_ID
                        LEFT JOIN (
                            SELECT
                                *
                            FROM
                                (
                                    SELECT
                                        RANK() over ( PARTITION BY UNITID ORDER BY rg.RG_TIMEMARK DESC ) `rank`,
                                        jb.id,
                                        jb.JB_ZCRQ,
                                        jb.JB_ZCD,
                                        jb.JB_ZYHY,
                                        jb.UNITID,
                                        jb.jb_qylb,
                                        jb.JB_ZCZB,
                                        jb.JB_CZRZZJGDM
                                    FROM
                                        view_cq_jbxxb_noDelete as jb
                                            JOIN
                                        rg_business_info rg  on rg.JBXX_ID = jb.id
                                    WHERE
                                        jb.JB_SHZT = '4'
                                )`rank`
                            WHERE
                                rank.rank =1
                    ) cqj ON cqj.UNITID = o.ORGANIZATION_ID
                        LEFT JOIN sys_organization sog ON sog.ORGANIZATION_ID = o.PARENT_ID
                        AND sog.isdeleted = 'N'
                        LEFT JOIN cq_czfd t3 ON ( t3.JBXX_ID = cqj.ID AND t3.UNITID = o.ORGANIZATION_ID AND t3.FD_CZRZZJGDM = cqj.JB_CZRZZJGDM )
                        LEFT JOIN cq_hhrqkfd t4 ON ( t4.JBXX_ID = cqj.ID AND t4.UNITID = o.ORGANIZATION_ID AND t4.HHR_CODE = cqj.JB_CZRZZJGDM )
                        LEFT JOIN sys_dictionary sd4 ON sd4.val = cqj.jb_qylb
                        AND sd4.type_id =(
                            SELECT
                                id
                            FROM
                                sys_dictionary
                            WHERE
                                type_code = 'QYLB'
                        )
                        LEFT JOIN cq_hhqy hhqy ON hhqy.JBXX_ID = cqj.ID
                        LEFT JOIN sys_dictionary sd8 ON sd8.val = cqj.jb_zcd
                        AND sd8.type_id =(
                            SELECT
                                id
                            FROM
                                sys_dictionary
                            WHERE
                                type_code = 'ZCD'
                        )
                        LEFT JOIN sys_organization cho ON cho.PARENT_ID = o.ORGANIZATION_ID
                WHERE
                    FIND_IN_SET(#{organization_id},o.PARENTS) and o.isdeleted = 'N' GROUP BY o.ORGANIZATION_ID order by o.priority) ttt
                LEFT JOIN (
                SELECT
                    val,
                    text
                FROM
                    sys_dictionary
                WHERE
                        type_id = ( SELECT id FROM sys_dictionary WHERE type_code = 'INDUSTRY_CLASSIFICATION_TREE' )) sd13 ON FIND_IN_SET( sd13.val, ttt.jb_zyhy )
        GROUP BY
            ttt.id
    </select>

    <select id="selectChildrenOrgs" resultType="java.util.Map">
        select t.BUSINESS_LEVEL level,concat(sd1.text,'企业') text,count(t.ORGANIZATION_ID) num,0 rate
        from sys_organization t
        join sys_dictionary sd1 on sd1.val = t.BUSINESS_LEVEL
        join sys_dictionary sd2 on sd1.type_id = sd2.id
        WHERE
        t.isdeleted = 'N' and sd2.type_code='QYJC'
        AND business_level IS NOT NULL
        AND t.ORGANIZATION_ID in
            (select t1.ORGANIZATION_ID from (
            <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
            </foreach>)as t1)
        GROUP BY
        t.business_level,sd1.text
    </select>

    <select id="getLoginUserChildrenIds" parameterType="java.lang.String" resultType="java.lang.String">
        select organization_id from sys_organization
        where find_in_set(#{organizationId},parents) and isdeleted = 'N'
    </select>

    <select id="selectZTreeNodeByOrgId" parameterType="java.lang.String" resultType="com.boot.iAdmin.access.model.common.ZTreeNode">
        select a.organization_id id,
               a.organization_name name,
               a.organization_desc description,
               a.organization_code code,
               a.parents,
               a.PARENT_ID parentId,
               a.visibles,
               a.audit_hosting,
               (select count(1) from sys_organization t where t.parent_id = a.organization_id and t.isdeleted = 'N') childNum,
               (select group_concat(t.organization_id) from sys_organization t where t.parent_id = a.organization_id) childrenIdS
        from sys_organization a
        where a.organization_id = #{organizationId} and a.isdeleted = 'N'
        union
        select ORGANIZATION_ID id,
               ORGANIZATION_NAME name,
               ORGANIZATION_DESC description,
               ORGANIZATION_CODE code,
               parents,
               parent_id parentId,
               VISIBLES,
               AUDIT_HOSTING,
               0 childNum,
               '' childrenIdS
        from sys_organization
        where audit_hosting = #{organizationId} and isdeleted = 'N'
    </select>

    <select id="getChildrenAndAuditsByName" parameterType="com.boot.iAdmin.access.model.organization.SysOrganization" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        where
            (find_in_set(#{organization_id},parents) or audit_hosting = #{organization_id})
            and isdeleted = 'N'
            <if test="organization_name != null and organization_name != ''">
                and organization_name like concat('%',#{organization_name},'%')
            </if>
        order by business_level
    </select>

    <select id="selectOrgsByIds" parameterType="java.util.Collection" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        where isdeleted = 'N' and organization_id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        order by priority
    </select>
    <select id="selectOrgsByIdsPossessDeleteStatus" parameterType="java.util.Collection" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        where organization_id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        order by priority
    </select>

    <select id="selectOrgById" parameterType="java.lang.String" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        where isdeleted = 'N' and organization_id = #{id}
    </select>
    <select id="selectOrgByIdPossessDeleteStatus" parameterType="java.lang.String" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        where  organization_id = #{id}
    </select>
    <select id="selectAllVisiblesChildren" parameterType="java.util.Collection" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
       select t.*,if(t1.UNITID is null,0,1) existJbxx from (select organization_id,organization_name,organization_code,parent_id,PARENTS from sys_organization
        where isdeleted = 'N'
        and
            <foreach collection="visibleOrgIds" open="(" close=")" separator="or" item="vid">
                find_in_set(#{vid},parents)
            </foreach>
        and organization_id not in
            <foreach collection="visibleOrgIds" open="(" close=")" separator="," item="vid">
                #{vid}
            </foreach>) t left join (select distinct UNITID from view_cq_jbxxb_noDelete where JB_SHZT=4) t1 on t1.UNITID = t.ORGANIZATION_ID
    </select>
    <select id="selectAllVisiblesChildrenPossessDeleteStatus" parameterType="java.util.Collection" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select t.*,if(t1.UNITID is null,0,1) existJbxx from (select organization_id,organization_name,organization_code,parent_id,PARENTS from sys_organization
        where 1=1
        and
        <foreach collection="visibleOrgIds" open="(" close=")" separator="or" item="vid">
            find_in_set(#{vid},parents)
        </foreach>
        and organization_id not in
        <foreach collection="visibleOrgIds" open="(" close=")" separator="," item="vid">
            #{vid}
        </foreach>) t left join (select distinct UNITID from view_cq_jbxxb_noDelete where JB_SHZT=4) t1 on t1.UNITID = t.ORGANIZATION_ID
    </select>
    <select id="getLoginUserVisibleOrgs" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo">
        select <include refid="columns"/> from sys_organization
        where 1=1
        and
            <foreach collection="visibleOrgIds" open="(" close=")" separator="or" item="vid">
                find_in_set(#{vid},parents)
            </foreach>
            <if test="org.organization_name != null and org.organization_name != ''">
                and organization_name like concat('%',#{org.organization_name },'%')
            </if>
        order by priority
    </select>
    <select id="containsKey" resultType="long">
        select
               count(1)
        from
            (
                select * from sys_organization
                where FIND_IN_SET(#{appOrdId},PARENTS)
                  and isdeleted = 'N'
                order by priority
                )ss
            where
                ss.ORGANIZATION_ID = #{ordId}
    </select>
    <select id="getAllVirtualNode" resultType="java.lang.String">
        select organization_id
        from sys_organization
        where isdeleted = 'N'
          and BUSINESSTYPE is null
          and BUSINESS_LEVEL is null
    </select>


    <select id="selectListForOpenApi" resultType="com.boot.iAdmin.access.model.organization.OrganizationVo"
            parameterType="com.boot.iAdmin.access.model.organization.SysOrganization">
        select
        t.ORGANIZATION_ID,
        t.ORGANIZATION_NAME,
        t.ORGANIZATION_CODE,
        t.PARENT_ID,
        t.PRIORITY,
        t.PARENT_CODE,
        t.CREATE_TIME,
        t.CREATE_UNIT,
        t.LAST_UPDATE_TIME,
        t.SSGZJGJG,
        t.BUSINESSTYPE,
        t.BUSINESS_LEVEL,
        t.BUSINESS_NATURE
        from
        sys_organization t
        <where>
            and t.isdeleted = 'N'
            <if test="organization_id != null and organization_id != ''">
                and t.organization_id = #{organization_id}
            </if>
            <if test="organization_name != null and organization_name != ''">
                and (t.organization_name like concat(concat('%',#{organization_name}),'%')
                or t.organization_code = #{organization_name})
            </if>
            <if test="organization_desc != null and organization_desc != ''">
                and t.organization_desc = #{organization_desc}
            </if>
            <if test="organization_code != null and organization_code != ''">
                and t.organization_code = #{organization_code}
            </if>
            <if test="parent_id != null and parent_id != ''">
                and t.parent_id = #{parent_id}
            </if>
            <if test="parent_code != null and parent_code != ''">
                and t.parent_code = #{parent_code}
            </if>
            <if test="priority != null">
                and t.priority = #{priority}
            </if>
            <if test="create_time != null">
                and t.create_time = #{create_time}
            </if>
            <if test="create_user != null and create_user != ''">
                and t.create_user = #{create_user}
            </if>
            <if test="create_unit != null and create_unit != ''">
                and t.create_unit = #{create_unit}
            </if>
            <if test="last_update_time != null">
                and t.last_update_time = #{last_update_time}
            </if>
            <if test="last_update_user != null and last_update_user != ''">
                and t.last_update_user = #{last_update_user}
            </if>
            <if test="biztypes != null and biztypes != ''">
                and t.biztypes = #{biztypes}
            </if>
            <if test="syncode != null and syncode != ''">
                and t.syncode = #{syncode}
            </if>
            <if test="businesstype != null and businesstype != ''">
                and t.businesstype = #{businesstype}
            </if>
            <if test="business_level != null and business_level != ''">
                and t.business_level = #{business_level}
            </if>
            <if test="parents != null and parents != ''">
                and t.parents like concat(concat('%',#{parents}),'%')
            </if>
        </where>
    </select>
</mapper>