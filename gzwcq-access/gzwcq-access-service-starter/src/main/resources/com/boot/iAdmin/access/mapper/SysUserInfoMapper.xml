<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.SysUserInfoMapper">

    <!-- 用户表字段 -->
    <sql id="userProperty">
        USER_ID,
        USERNAME,
        NAME,
        PASSWORD,
        DT_CREATE,
        LAST_LOGIN,
        DEADLINE,
        LOGIN_IP,
        STATUS,
        ACCOUNT_NON_EXPIRED,
        ACCOUNT_NON_LOCKED,
        CREDENTIALS_NON_EXPIRED,
        PHONE,
        EMAIL,
        ORGANIZATION_ID,
        REMARKS,
        ISDELETED,
        TYPE,
        AUDIT_LEVEL,
        initial_pass,
        first_login
    </sql>

    <sql id="userPropertyAlias">
        t.USER_ID,
        t.USERNAME,
        t.NAME,
       	t.PASSWORD,
        t.DT_CREATE,
        t.LAST_LOGIN,
        t.DEADLINE,
        t.LOGIN_IP,
        t.status,
        t.ACCOUNT_NON_EXPIRED,
        t.ACCOUNT_NON_LOCKED,
        t.CREDENTIALS_NON_EXPIRED,
        t.PHONE,
        t.EMAIL,
        t.ORGANIZATION_ID,
        t.REMARKS,
        t.ISDELETED,
        t.TYPE,
        t.AUDIT_LEVEL,
        t.initial_pass,
        t.first_login
    </sql>

    <!--去掉了密码-->
    <sql id="userPropertyAlias2">
        t.USER_ID,
        t.USERNAME,
        t.NAME,
        t.DT_CREATE,
        t.LAST_LOGIN,
        t.DEADLINE,
        t.LOGIN_IP,
        t.status,
        t.ACCOUNT_NON_EXPIRED,
        t.ACCOUNT_NON_LOCKED,
        t.CREDENTIALS_NON_EXPIRED,
        t.PHONE,
        t.EMAIL,
        t.ORGANIZATION_ID,
        t.REMARKS,
        t.ISDELETED,
        t.TYPE,
        t.AUDIT_LEVEL,
        t.initial_pass,
        t.first_login
    </sql>

    <!-- 用户表字段值 -->
    <sql id="userVals">
        #{user_id},
        #{username},
        #{name},
        #{password},
        #{dt_create},
        #{last_login},
        #{deadline},
        #{login_ip},
        #{status},
        #{account_non_expired},
        #{account_non_locked},
        #{credentials_non_expired},
        #{phone},
        #{email},
        #{organization_id},
        #{remarks},
        #{isdeleted},
        #{type},
        #{audit_level},
        #{initial_pass},
        #{first_login}
    </sql>

    <!-- 根据用户名获取用户信息 -->
    <select id="getUserInfoByUsername" parameterType="string" resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        SELECT
        <include refid="userProperty"/>
        FROM
        SYS_USERS
        WHERE
        USERNAME = #{username} and isdeleted = 'N'
    </select>

    <!-- 根据用户ID获取用户信息 -->
    <select id="getUserInfoByUserId" parameterType="string" resultType="com.boot.iAdmin.access.model.user.SysUserVo">
		SELECT
			<include refid="userPropertyAlias2"/> ,
			o.ORGANIZATION_NAME,
			o.ORGANIZATION_DESC,
            sd1.text typeStr,
		    sd2.text auditLevelStr
		FROM
			SYS_USERS t
		left join SYS_ORGANIZATION o on t.ORGANIZATION_ID = o.ORGANIZATION_ID
        LEFT JOIN sys_dictionary sd1 ON sd1.val = t.type and sd1.parent = 10651
        LEFT JOIN sys_dictionary sd2 ON sd2.val = t.audit_level and sd2.parent = 10654
		WHERE
			t.USER_ID = #{userId}
    </select>

    <!-- 根据用户名获取权限  -->
    <select id="getSysAuthByUserName" parameterType="string"
            resultType="com.boot.iAdmin.access.model.authority.SysAuthorities">
		SELECT
			 auth.*
		FROM
			SYS_USERS u,
			sys_authorities auth,
			sys_roles role,
			sys_roles_authorities ra,
			sys_users_roles ur
		WHERE
			u.user_id = ur.user_id
			and ur.role_id = role.role_id
			and role.role_id = ra.role_id
			and ra.authority_id = auth.authority_id
			and USERNAME = #{username}
    </select>

    <!-- 查询用户列表（分页） -->
    <select id="loadUserByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel"
            resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        SELECT
        <include refid="userPropertyAlias2"/>,
        case when t.status = 1 then '正常' else '停用' end as enabled_txt,
        so.ORGANIZATION_NAME,sd1.text typeStr,sd2.text auditLevelStr
        FROM
        sys_users t
        left join sys_organization so on t.organization_id = so.organization_id
        LEFT JOIN sys_dictionary sd1 ON sd1.val = t.type and sd1.parent = (select id from sys_dictionary where type_code='ACCOUNT_TYPE')
        LEFT JOIN sys_dictionary sd2 ON sd2.val = t.audit_level and sd2.parent = (select id from sys_dictionary where type_code='AUDIT_LEVEL')
        WHERE
        FIND_IN_SET(#{obj.organization_id},so.parents) and t.isdeleted = 'N'
        <if test="obj.username != '' and obj.username != null">
            AND t.USERNAME LIKE CONCAT('%',#{obj.username},'%')
        </if>
        <if test="obj.name != '' and obj.name != null">
            AND t.NAME LIKE CONCAT('%',#{obj.name},'%')
        </if>
        <if test="obj.organization_name != '' and obj.organization_name != null">
            AND so.ORGANIZATION_NAME LIKE CONCAT('%',#{obj.organization_name},'%')
        </if>
        order by t.DT_CREATE desc
        LIMIT #{offset},#{limit}
    </select>

    <!-- 查询用户总数 -->
    <select id="loadTotalUsers" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="long">
        SELECT
            count(t.user_id)
        FROM
        sys_users t
        left join sys_organization so on t.organization_id = so.organization_id
        LEFT JOIN sys_dictionary sd1 ON sd1.val = t.type and sd1.parent = (select id from sys_dictionary where type_code='ACCOUNT_TYPE')
        LEFT JOIN sys_dictionary sd2 ON sd2.val = t.audit_level and sd2.parent = (select id from sys_dictionary where type_code='AUDIT_LEVEL')
        WHERE
        FIND_IN_SET(#{obj.organization_id},so.parents) and t.isdeleted = 'N'
        <if test="obj.username != '' and obj.username != null">
            AND t.USERNAME LIKE CONCAT('%',#{obj.username},'%')
        </if>
        <if test="obj.name != '' and obj.name != null">
            AND t.NAME LIKE CONCAT('%',#{obj.name},'%')
        </if>
        <if test="obj.organization_name != '' and obj.organization_name != null">
            AND so.ORGANIZATION_NAME LIKE CONCAT('%',#{obj.organization_name},'%')
        </if>
    </select>

    <!-- 更新用户状态 -->
    <update id="updateUserStatus" parameterType="java.util.Map">
		UPDATE SYS_USERS SET status = #{status} WHERE USER_ID = #{userId}
    </update>

    <!-- 新增用户 -->
    <insert id="insertUser" parameterType="com.boot.iAdmin.access.model.user.SysUser" useGeneratedKeys="true" keyProperty="user_id">
        <selectKey keyProperty="user_id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
        INSERT INTO SYS_USERS (<include refid="userProperty"/>)
        values (<include refid="userVals"/>)
    </insert>

    <!-- 批量删除用户 -->
    <update id="deleteUserByIds" parameterType="java.util.Map">
        update SYS_USERS
            set isdeleted = 'Y'
        WHERE USER_ID IN
        <foreach collection="userIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <!-- 更新用户信息 -->
    <update id="updateUserInfoIgnoreNull" parameterType="com.boot.iAdmin.access.model.user.SysUser">
        UPDATE SYS_USERS
        <set>
            <if test="user_id != null and user_id != ''">
                user_id = #{user_id},
            </if>
            <if test="username != null and username != ''">
                username = #{username},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="dt_create != null">
                dt_create = #{dt_create},
            </if>
            <if test="last_login != null">
                last_login = #{last_login},
            </if>
            <if test="deadline != null">
                deadline = #{deadline},
            </if>
            <if test="login_ip != null and login_ip != ''">
                login_ip = #{login_ip},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="organization_id != null and organization_id != ''">
                organization_id = #{organization_id},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="audit_level != null and audit_level != ''">
                audit_level = #{audit_level},
            </if>
            <if test="initial_pass != null and initial_pass != ''">
                initial_pass = #{initial_pass},
            </if>
            <if test="first_login != null and first_login != ''">
                first_login = #{first_login}
            </if>
        </set>
        WHERE USER_ID = #{user_id}
    </update>

    <!-- 查询用户信息，从sys_roles_authorities反向推导关联的全部用户信息,权限删除的时候判断使用 -->
    <select id="loadUserByAuthorIDs" parameterType="java.lang.String"
            resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        SELECT
        <include refid="userProperty"/>
        FROM SYS_USERS t
        WHERE
        t.status = 1
        AND t.USER_ID IN (
        SELECT DISTINCT
        USER_ID
        FROM
        sys_users_roles
        WHERE
        ROLE_ID IN (
        SELECT DISTINCT
        ROLE_ID
        FROM
        sys_roles_authorities
        WHERE
        AUTHORITY_ID IN (${authIds})
        )
        )
    </select>

    <!-- 查询用户信息，从sys_users_roles反向推导关联的全部用户信息,角色删除的时候判断使用 -->
    <select id="loadUserByRoleIDs" parameterType="java.lang.String"
            resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        SELECT
        <include refid="userProperty"/>
        FROM SYS_USERS t
        WHERE
        t.status = 1
        AND t.USER_ID IN (
        SELECT DISTINCT
        USER_ID
        FROM
        sys_users_roles
        WHERE
        ROLE_ID IN (${roleIds})
        )
    </select>

    <!-- 参数唯一性 -->
    <select id="selectForUnique" parameterType="com.boot.iAdmin.access.model.user.SysUser"
            resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        SELECT USER_ID,USERNAME
        FROM SYS_USERS
        <where>
			isdeleted = 'N'
            <if test="user_id != null and user_id != ''">
                and USER_ID != #{user_id}
            </if>
            <if test="username != null and username != ''">
                and username = #{username}
            </if>
        </where>
    </select>

    <select id="selectForList" parameterType="com.boot.iAdmin.access.model.user.SysUser"
            resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        select
        <include refid="userPropertyAlias"/>
        from
        sys_users t
        where 1=1
        <if test="user_id != null">
            and t.user_id = #{user_id}
        </if>
        <if test="username != null and username != ''">
            and t.username = #{username}
        </if>
        <if test="name != null and name != ''">
            and t.name like concat('%',#{name},'%')
        </if>
        <if test="organization_id!= null and organization_id !=''">
            and t.organization_id = #{organization_id}
        </if>
        <if test="type!= null and type !=''">
            and t.type = #{type}
        </if>
        <if test="audit_level!= null and audit_level !=''">
            and t.audit_level = #{audit_level}
        </if>
        <if test="isdeleted != null and isdeleted !=''">
            and t.isdeleted = #{isdeleted}
        </if>
        limit 10
    </select>

    <!-- 查询用户信息，根据角色code查询用户信息 -->
    <select id="getUserListByRoleCode" parameterType="java.lang.String"
            resultType="com.boot.iAdmin.access.model.user.SysUser">
        SELECT DISTINCT
        <include refid="userPropertyAlias"/>
        FROM
        sys_users t,
        sys_roles r,
        sys_users_roles ur
        WHERE
        t.status = 1
        AND
        r.role_id = ur.role_id
        AND
        t.user_id = ur.user_id
        AND
        r.role_code = #{roleCode}
    </select>

    <!--模糊查询用户列表-->
    <select id="getUserNameByName" resultType="map">
        SELECT
        USER_ID as userid,
        USERNAME as username
        FROM
        sys_users
        <where>
            USERNAME like concat('%',#{name},'%')
        </where>
    </select>

    <select id="getUserByOrgId" resultType="java.util.Map">
        select
        <include refid="userProperty"/>
        from
        sys_users
        where ORGANIZATION_ID =#{organization_id}
    </select>

    <!--获取登录人本级以及下级组织的所有员工列表-->
    <select id="getLoginOrgAllStaff" resultType="java.util.Map">
        SELECT u.USER_ID,u.NAME,o.ORGANIZATION_ID,o.ORGANIZATION_NAME FROM sys_users u
        INNER JOIN
        sys_organization o
        ON u.ORGANIZATION_ID = o.ORGANIZATION_ID
        AND FIND_IN_SET(u.ORGANIZATION_ID,(SELECT getOrganizationChildList (#{orgId})))
        <if test="staff !=null and staff !=''">
            and u.USER_ID = #{staff}
        </if>
        and o.isdeleted = 'N'
        limit #{pageNumber},#{limit}
    </select>

    <resultMap type="com.boot.iAdmin.access.model.organization.OrganizationVo" id="BaseResultMap">
        <id column="organization_id" property="organization_id"/>
        <result column="organization_name" property="organization_name"/>
        <result column="organization_desc" property="organization_desc"/>
        <result column="organization_code" property="organization_code"/>
    </resultMap>
    <!--递归查询部门及子部门-->
    <resultMap id="organizationsWithChildren" type="com.boot.iAdmin.access.model.organization.OrganizationVo" extends="BaseResultMap">
        <collection property="children" ofType="com.boot.iAdmin.access.model.organization.OrganizationVo"
            select="com.boot.iAdmin.access.mapper.SysUserInfoMapper.getCurrentUserOrgTree" column="organization_id">
        </collection>
    </resultMap>
    <select id="getCurrentUserOrgTree" parameterType="java.lang.String" resultMap="organizationsWithChildren">
        select
            organization_id,
            organization_name,
            organization_desc,
            organization_code
        from sys_organization
        where parent_id = #{id}
        and isdeleted = 'N'
        order by priority
    </select>

    <select id="selectExistUsers" parameterType="com.boot.iAdmin.access.model.user.SysUser" resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        select <include refid="userPropertyAlias"/> from sys_users t
        <where>
            <if test="user_id != null">
                and t.user_id != #{user_id}
            </if>
            <if test="organization_id!= null and organization_id !=''">
                and t.organization_id = #{organization_id}
            </if>
            <if test="type!= null and type !=''">
                and t.type = #{type}
            </if>
            <if test="audit_level!= null and audit_level !=''">
                and t.audit_level = #{audit_level}
            </if>
            <if test="isdeleted != null and isdeleted !=''">
                and t.isdeleted = #{isdeleted}
            </if>
        </where>
    </select>

    <select id="selectReviewingJbxxByUserIds" parameterType="java.util.Map" resultType="com.boot.iAdmin.access.model.user.SysUserVo">
        select distinct t.USER_ID,t.`NAME` from sys_users t
        join view_cq_jbxxb_noDelete cj on t.user_id = cj.CREATE_USER
        where
          <!--3:审核中-->
          cj.JB_SHZT = '3'
          and cj.CREATE_USER in
          <foreach collection="userIds" open="(" close=")" separator="," item="id">
              #{id}
          </foreach>
          and t.user_id in
          <foreach collection="userIds" open="(" close=")" separator="," item="id">
              #{id}
          </foreach>
    </select>
</mapper>