<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.RoleManageMapper">
    <sql id="columns">
		ROLE_ID,
		ROLE_NAME,
		ROLE_DESC,
		ENABLE,
		ISSYS,
		DT_CREATE,
		DT_UPDATE,
		role_code        
    </sql>
    
    <sql id="vals">
        #{role_id},
        #{role_name},
        #{role_desc},
        #{enable},
        #{issys},
        #{dt_create},
        #{dt_update},
        #{role_code}
    </sql>
    
    <!-- BootstrapTable方式的分页查询 -->
    <!-- 分页查询角色 -->
    <select id="loadRoleByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="com.boot.iAdmin.access.model.role.RoleVo">
		SELECT 
			<include refid="columns"/> 
			,
			case when enable = 1 then '有效' else '失效' end as enable_txt
		FROM 
			SYS_ROLES
		<where>
		   <if test="obj !=null">
		      <if test="obj.role_name != '' and obj.role_name != null">
				AND ROLE_NAME like CONCAT('%',#{obj.role_name},'%')		    
			  </if>
			  <if test="obj.role_desc != '' and obj.role_desc != null">
				AND ROLE_DESC like CONCAT('%',#{obj.role_desc},'%')		    
			  </if>
			  <if test="obj.role_code != '' and obj.role_code != null">
				AND ROLE_CODE like CONCAT('%',#{obj.role_code},'%')		    
			  </if>
		   </if>
		</where>
		LIMIT #{offset},#{limit}     
    </select>
    
    <!-- 查询总数 -->
    <select id="loadTotalRoles" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="long">
		SELECT COUNT(1) 
			FROM 
			SYS_ROLES
		<where>
		   <if test="obj !=null">
		      <if test="obj.role_name != '' and obj.role_name != null">
				AND ROLE_NAME like CONCAT('%',#{obj.role_name},'%')		    
			  </if>
			  <if test="obj.role_desc != '' and obj.role_desc != null">
				AND ROLE_DESC like CONCAT('%',#{obj.role_desc},'%')		    
			  </if>
			  <if test="obj.role_code != '' and obj.role_code != null">
				AND ROLE_CODE like CONCAT('%',#{obj.role_code},'%')		    
			  </if>
		   </if>
		</where>
    </select>    
    
    <!-- 修改角色 -->
    <update id="updateRoleById" parameterType="com.boot.iAdmin.access.model.role.Role">
		UPDATE SYS_ROLES
		<set>
	        role_name = #{role_name},
	        role_code = #{role_code},
	        role_desc = #{role_desc},
	        enable = #{enable},
	        issys = #{issys},
	        dt_create = #{dt_create},
	        dt_update = #{dt_update}
		</set>
		WHERE ROLE_ID = #{role_id}
    </update>
    <!-- 修改角色，忽略空字段 -->
    <update id="updateRoleIgnoreNull" parameterType="com.boot.iAdmin.access.model.role.Role">
		UPDATE SYS_ROLES
		<set>
		    <if test="role_name != null and role_name != ''">
				role_name = #{role_name},       
		    </if>
		    <if test="role_desc != null and role_desc != ''">
				role_desc = #{role_desc},       
		    </if>
		    <if test="enable != null">
				enable = #{enable},       
		    </if>
		    <if test="issys != null">
				issys = #{issys},       
		    </if>
		    <if test="dt_create != null and dt_create != ''">
				dt_create = #{dt_create},       
		    </if>
		    <if test="dt_update != null and dt_update != ''">
				dt_update = #{dt_update},
		    </if>
		    <if test="role_code != null and role_code != ''">
				role_code = #{role_code}
		    </if>
		</set>
		WHERE ROLE_ID = #{role_id}
    </update>
    
    <!-- 批量删除角色 -->
	<delete id="deleteRoleByIds" parameterType="java.util.Map">
		DELETE FROM SYS_ROLES
			WHERE ROLE_ID IN
			<foreach collection="roleIds" open="(" close=")" separator="," item="id">
				#{id} 
			</foreach>
	</delete>
	
	<!-- 新增角色 -->
	<insert id="insertRole">
		<selectKey keyProperty="role_id" resultType="long" order="AFTER">
			select last_insert_id()
		</selectKey>
		INSERT INTO SYS_ROLES (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<!-- 获取角色 -->
	<select id="getRole" parameterType="com.boot.iAdmin.access.model.role.Role" resultType="com.boot.iAdmin.access.model.role.RoleVo">
		SELECT 
			<include refid="columns" />
		FROM SYS_ROLES
		WHERE 1 = 1
			<if test="role_id != null">
				and role_id = #{role_id}    
		    </if>
			<if test="role_name != null and role_name != ''">
				and role_name = #{role_name}       
		    </if>
		    <if test="role_desc != null and role_desc != ''">
				and role_desc = #{role_desc}       
		    </if>
		    <if test="enable != null">
				and enable = #{enable}       
		    </if>
		    <if test="issys != null">
				and issys = #{issys}       
		    </if>
		    <if test="dt_create != null">
				and dt_create = #{dt_create}       
		    </if>
		    <if test="dt_update != null">
				and dt_update = #{dt_update}     
		    </if>
		    <if test="role_code != null and role_code != ''">
				and role_code = #{role_code}       
		    </if>
	</select>
	
		<!-- 获取角色 -->
	<select id="getRoleLists" parameterType="com.boot.iAdmin.access.model.role.Role" resultType="com.boot.iAdmin.access.model.role.RoleVo">
		SELECT 
			<include refid="columns" />
		FROM SYS_ROLES
		WHERE 1 = 1
			<if test="role_id != null">
				and role_id = #{role_id}    
		    </if>
			<if test="role_name != null and role_name != ''">
				and role_name = #{role_name}       
		    </if>
		    <if test="role_desc != null and role_desc != ''">
				and role_desc = #{role_desc}       
		    </if>
		    <if test="enable != null">
				and enable = #{enable}       
		    </if>
		    <if test="issys != null">
				and issys = #{issys}       
		    </if>
		    <if test="dt_create != null">
				and dt_create = #{dt_create}       
		    </if>
		    <if test="dt_update != null">
				and dt_update = #{dt_update}     
		    </if>
		    <if test="role_code != null and role_code != ''">
				and role_code = #{role_code}       
		    </if>
	</select>
	
	<!-- 查询所有角色 -->
	<select id="selectAll" resultType="com.boot.iAdmin.access.model.role.Role">
		SELECT  <include refid="columns" /> FROM SYS_ROLES  
	</select>
	
	<!-- 根据用户ID查询所拥有角色集合 -->
	<select id="selectRolesByUserId" parameterType="java.lang.String" resultType="com.boot.iAdmin.access.model.role.Role">
		SELECT <include refid="columns" /> FROM SYS_ROLES
			WHERE ROLE_ID IN (SELECT ROLE_ID FROM SYS_USERS_ROLES WHERE USER_ID = #{user_id})
	</select>
	
	<!-- 校验角色名称的唯一性 -->
	<select id="validateRoleName" resultType="java.lang.String">
		SELECT
			t.ROLE_NAME as NAME
		FROM
			sys_roles t
		WHERE
			t.ROLE_NAME = #{role_name}
		AND (t.ROLE_ID IS NOT NULL AND t.ROLE_ID != ${role_id})
	</select>
	
	<select id="validateRoleCode" resultType="java.lang.String">
		SELECT
			t.ROLE_NAME as NAME
		FROM
			sys_roles t
		WHERE
			t.ROLE_CODE = #{role_code}
		AND (t.ROLE_ID IS NOT NULL AND t.ROLE_ID != ${role_id})
	</select>
	
	<!-- 根据用户和公司查询所拥有角色集合 -->
	<select id="loadRolesByCompany" parameterType="java.util.Map" resultType="com.boot.iAdmin.access.model.role.Role">
		SELECT <include refid="columns" /> FROM SYS_ROLES
			WHERE ROLE_ID IN (SELECT ROLE_ID FROM SYS_USERS_ROLES WHERE USER_ID = #{user_id} and company_id = #{company})
	</select>
</mapper>