<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.UserAndRoleMapper" >
    
    <sql id="columns">
		id,
		role_id,
		user_id,
		company_id      
    </sql>
    
    <sql id="vals">
    	#{id},
    	#{role_id},
    	#{user_id},
    	#{company_id}
    </sql>
    
    <delete id="deleteUserAndRoleByUser" parameterType="com.boot.iAdmin.access.model.roleAndUser.RoleAndUser">
        DELETE FROM sys_users_roles WHERE USER_ID = #{user_id}
    </delete>
    
    <!-- 新增关联关系 -->
    <!-- 自增长ID-->
	<insert id="insertUserAndRole" parameterType="com.boot.iAdmin.access.model.roleAndUser.RoleAndUser" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			select last_insert_id()
		</selectKey>
		INSERT INTO sys_users_roles (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert> 
	
	<!-- 根据用户ID删除用户角色关联关系 -->
	<delete id="deleteUserAndRoleByUserIds" parameterType="java.util.Map">
	    DELETE
		FROM
			sys_users_roles
		WHERE
			user_id in 
		<foreach collection="userIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</delete>
	
	<!-- 根据角色ID删除用户角色关联关系 -->
	<delete id="deleteUserAndRoleByRoleIds" parameterType="java.util.Map">
	    DELETE
		FROM
			sys_users_roles
		WHERE
			role_id in 
		<foreach collection="roleIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</delete>
	
	<delete id="deleteUserAndRoleByUserAndCompany" parameterType="com.boot.iAdmin.access.model.roleAndUser.RoleAndUser">
        DELETE FROM sys_users_roles WHERE USER_ID = #{user_id} and company_id = #{company_id}
    </delete>
</mapper>