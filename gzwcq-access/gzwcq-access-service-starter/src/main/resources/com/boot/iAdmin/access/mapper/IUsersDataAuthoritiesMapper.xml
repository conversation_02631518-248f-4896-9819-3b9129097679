<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.iAdmin.access.mapper.IUsersDataAuthoritiesMapper">

	<sql id="columns">
		user_id, 
		data_id, 
		org_id
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.user_id, 
		t.data_id, 
		t.org_id
	</sql>
	
	<sql id="vals">
		#{user_id}, 
		#{data_id}, 
		#{org_id}
	</sql>
	
	<sql id="conds">
		<if test="user_id != null and user_id != ''">
			and user_id = #{user_id}
		</if>
		<if test="data_id != null and data_id != ''">
			and data_id = #{data_id}
		</if>
		<if test="org_id != null and org_id != ''">
			and org_id = #{org_id}
		</if>
	</sql>
	
	<sql id="bootConds">
		<if test="obj.user_id != null and obj.user_id != ''">
			and user_id = #{obj.user_id}
		</if>
		<if test="obj.data_id != null and obj.data_id != ''">
			and data_id = #{obj.data_id}
		</if>
		<if test="obj.org_id != null and obj.org_id != ''">
			and org_id = #{obj.org_id}
		</if>
	</sql>
	
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<where>
			<if test="obj !=null">
					<if test="obj.user_id != null and obj.user_id != ''">
						and t.user_id = #{obj.user_id}
					</if>
					<if test="obj.data_id != null and obj.data_id != ''">
						and t.data_id = #{obj.data_id}
					</if>
					<if test="obj.org_id != null and obj.org_id != ''">
						and t.org_id = #{obj.org_id}
					</if>
			</if>
		</where>
	</sql>
	
	
	<sql id="whereSqlForList">
		<where>
					<if test="user_id != null and user_id != ''">
						and user_id = #{user_id}
					</if>
					<if test="data_id != null and data_id != ''">
						and data_id = #{data_id}
					</if>
					<if test="org_id != null and org_id != ''">
						and org_id = #{org_id}
					</if>
		</where>
	</sql>
	
	

	<insert id="save" parameterType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthorities" useGeneratedKeys="true" keyProperty="user_id">
		<selectKey keyProperty="user_id" resultType="Long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_users_data_authorities (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<delete id="delete" parameterType="java.util.Map">
		delete from sys_users_data_authorities  where
		user_id in
		<foreach collection="usersDataAuthoritiess" open="(" close=")" separator="," item="user_id">
		    #{user_id} 
		</foreach>
	</delete>  
	
	<select id="queryUsersDataAuthoritiesById" resultType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthoritiesVo">
		select 
		<include refid="columns"/>
		from sys_users_data_authorities
		where user_id=#{user_id} 
	</select>

	<update id="updateIgnoreNull">
		update sys_users_data_authorities
		<set>
			<if test="user_id != null">
				user_id=#{user_id}, 
			</if>
			<if test="data_id != null">
				data_id=#{data_id}, 
			</if>
			<if test="org_id != null">
				org_id=#{org_id}
			</if>
		</set>
		where user_id=#{user_id} 
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthorities" resultType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthoritiesVo">
		select
		<include refid="columns"/>
		from
			sys_users_data_authorities t
		<include refid="whereSqlForList" />
	</select>	


	<!-- 分页查询对象和数据总量查询 -->
	<select id="queryUsersDataAuthoritiesByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthoritiesVo">
		select
		<include refid="columnsAlias"/>
		from
			sys_users_data_authorities t
		<include refid="whereSql" />
		order by user_id desc
		limit #{offset}, #{limit}
	</select>
	
	<select id="queryTotalUsersDataAuthoritiess" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="java.lang.Long">
		select
		count(user_id)
		from sys_users_data_authorities t
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthorities" resultType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthoritiesVo">
		select <include refid="columnsAlias"/> from sys_users_data_authorities t
		where t.user_id != #{user_id}
					<if test="data_id != null and data_id != ''">
						and t.data_id = #{data_id}
					</if>
					<if test="org_id != null and org_id != ''">
						and t.org_id = #{org_id}
					</if>
	</select>
	 
	<delete id="deleteByUserAndCompany" parameterType="com.boot.iAdmin.access.model.userAndData.UsersDataAuthorities">
		DELETE FROM sys_users_data_authorities WHERE USER_ID = #{user_id} AND ORG_ID = #{org_id}
	</delete>

</mapper>