<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.RoleAndAuthMapper">
    
    <sql id="columns">
		ID,
		AUTHORITY_ID,
		ROLE_ID        
    </sql>
    
    <sql id="vals">
        #{id},
        #{authority_id},
        #{role_id}
    </sql>
    
    <!-- 删除当前角色的所有权限关联关系 -->
    <delete id="deleteAuthsByOneRole" parameterType="com.boot.iAdmin.access.model.roleAndAuth.RoleAndAuth">
        delete from sys_roles_authorities where role_id = #{role_id}
    </delete>
    
    <!-- 新增角色权限关联关系 -->
    <!-- 自增长ID-->
	<insert id="insertRoleAndAuth" parameterType="com.boot.iAdmin.access.model.roleAndAuth.RoleAndAuth" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			select last_insert_id()
		</selectKey>
		INSERT INTO sys_roles_authorities (<include refid="columns"/>) 
		values (<include refid="vals"/>)
	</insert>
	
	<!-- 根据权限ID删除角色权限关联关系 -->
	<delete id="deleteRoleAndResourceByAuthIds" parameterType="java.util.Map">
	    DELETE
		FROM
			sys_roles_authorities
		WHERE
			authority_id in 
		<foreach collection="authIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</delete>
	<!-- 根据角色ID删除角色权限关联关系 -->
	<delete id="deleteRoleAndResourceByRoleIds" parameterType="java.util.Map">
	    DELETE
		FROM
			sys_roles_authorities
		WHERE
			role_id in 
		<foreach collection="roleIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</delete>
	
</mapper>