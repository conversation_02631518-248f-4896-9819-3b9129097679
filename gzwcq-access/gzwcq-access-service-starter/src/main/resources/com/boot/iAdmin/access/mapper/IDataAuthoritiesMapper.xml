<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.iAdmin.access.mapper.IDataAuthoritiesMapper">

	<sql id="columns">
		data_id, 
		data_name, 
		data_desc
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.data_id, 
		t.data_name, 
		t.data_desc
	</sql>
	
	<sql id="vals">
		#{data_id}, 
		#{data_name}, 
		#{data_desc}
	</sql>
	
	<sql id="conds">
		<if test="data_id != null and data_id != ''">
			and data_id = #{data_id}
		</if>
		<if test="data_name != null and data_name != ''">
			and data_name = #{data_name}
		</if>
		<if test="data_desc != null and data_desc != ''">
			and data_desc = #{data_desc}
		</if>
	</sql>
	
	<sql id="bootConds">
		<if test="obj.data_id != null and obj.data_id != ''">
			and data_id = #{obj.data_id}
		</if>
		<if test="obj.data_name != null and obj.data_name != ''">
			and data_name = #{obj.data_name}
		</if>
		<if test="obj.data_desc != null and obj.data_desc != ''">
			and data_desc = #{obj.data_desc}
		</if>
	</sql>
	
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<where>
			<if test="obj !=null">
					<if test="obj.data_id != null and obj.data_id != ''">
						and t.data_id = #{obj.data_id}
					</if>
					<if test="obj.data_name != null and obj.data_name != ''">
						and t.data_name = #{obj.data_name}
					</if>
					<if test="obj.data_desc != null and obj.data_desc != ''">
						and t.data_desc = #{obj.data_desc}
					</if>
			</if>
		</where>
	</sql>
	
	
	<sql id="whereSqlForList">
		<where>
					<if test="data_id != null and data_id != ''">
						and data_id = #{data_id}
					</if>
					<if test="data_name != null and data_name != ''">
						and data_name = #{data_name}
					</if>
					<if test="data_desc != null and data_desc != ''">
						and data_desc = #{data_desc}
					</if>
		</where>
	</sql>
	
	

	<insert id="save" parameterType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthorities" useGeneratedKeys="true" keyProperty="data_id">
		<selectKey keyProperty="data_id" resultType="Long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_data_authorities (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<delete id="delete" parameterType="java.util.Map">
		delete from sys_data_authorities  where
		data_id in
		<foreach collection="dataAuthoritiess" open="(" close=")" separator="," item="data_id">
		    #{data_id} 
		</foreach>
	</delete>  
	<select id="queryByMap" parameterType="java.util.Map"  resultType="java.lang.Integer">
		select count(1) from sys_users  where
		data_id in
		<foreach collection="dataAuthoritiess" open="(" close=")" separator="," item="data_id">
		    #{data_id} 
		</foreach>
	</select>
	<select id="queryDataAuthoritiesById" resultType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthoritiesVo">
		select 
		<include refid="columns"/>
		from sys_data_authorities
		where data_id=#{data_id} 
	</select>

	<update id="updateIgnoreNull">
		update sys_data_authorities
		<set>
			<if test="data_id != null">
				data_id=#{data_id}, 
			</if>
			<if test="data_name != null">
				data_name=#{data_name}, 
			</if>
			<if test="data_desc != null">
				data_desc=#{data_desc}, 
			</if>
		</set>
		where data_id=#{data_id} 
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthorities" resultType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthoritiesVo">
		select
		<include refid="columns"/>
		from
			sys_data_authorities t
		<include refid="whereSqlForList" />
	</select>	


	<!-- 分页查询对象和数据总量查询 -->
	<select id="queryDataAuthoritiesByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthoritiesVo">
		select
		<include refid="columnsAlias"/>
		from
			sys_data_authorities t
		<include refid="whereSql" />
		order by data_id desc
		limit #{offset}, #{limit}
	</select>
	
	<select id="queryTotalDataAuthoritiess" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="java.lang.Long">
		select
		count(data_id)
		from sys_data_authorities t
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthorities" resultType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthoritiesVo">
		select <include refid="columnsAlias"/> from sys_data_authorities t
		where t.data_id != #{data_id}
					<if test="data_name != null and data_name != ''">
						and t.data_name = #{data_name}
					</if>
					<if test="data_desc != null and data_desc != ''">
						and t.data_desc = #{data_desc}
					</if>
	</select>
	<insert id="saveMenuAuth" parameterType="com.boot.iAdmin.access.model.dataAuthorities.MenuAuth">
		insert into sys_module_data (data_id,module_id,auth) values(#{data_id},#{module_id},#{auth}) ON DUPLICATE KEY UPDATE auth= #{auth}
	</insert>
	
	<!-- 根据公司获取数据权限 -->
	<select id="loadDataAuthsByCompany" parameterType="java.util.Map" resultType="com.boot.iAdmin.access.model.dataAuthorities.DataAuthoritiesVo">
		select
		<include refid="columns"/>
		from
			sys_data_authorities t
		where data_id in(SELECT data_id FROM sys_users_data_authorities WHERE USER_ID = #{user_id}  and org_id = #{company})
	</select>
	
	<select id="queryMenuAuthByUserAndCompany" parameterType="java.util.Map" resultType="com.boot.iAdmin.access.model.dataAuthorities.MenuAuth">
		select
			t.*
		from
			sys_module_data t,
			sys_users_data_authorities uda
		where 
			t.data_id = uda.data_id and
			uda.user_id = #{user_id} and
			uda.org_id = #{comp}
	</select>
</mapper>