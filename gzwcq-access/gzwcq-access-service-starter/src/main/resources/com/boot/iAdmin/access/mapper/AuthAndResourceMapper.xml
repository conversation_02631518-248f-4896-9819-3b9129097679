<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.AuthAndResourceMapper">
    <sql id="columns">
        id,
        resource_id,
        authority_id
    </sql>
    <sql id="vals">
        #{id},
        #{resource_id},
        #{authority_id}
    </sql>
    
    <!-- 新增权限资源关联关系-->
	<insert id="insertAuthAndResource">
		<selectKey keyProperty="id" resultType="long" order="AFTER">
			select last_insert_id()
		</selectKey>
		INSERT INTO sys_authorities_resources (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<!-- 根据权限ID和所涉及到的资源集合，删除在当前资源集合中的当前权限资源关联关系 -->
	<delete id="deleteAuthAndResourceInCurrResourcesIds" parameterType="java.util.Map">
		DELETE
		FROM
			sys_authorities_resources
		WHERE
			authority_id = #{auth_id}
		AND resource_id IN
		<foreach collection="currResourceIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</delete>
	
	<!-- 根据权限ID删除权限资源关联关系 -->
	<delete id="deleteAuthAndResourceByAuthIds" parameterType="java.util.Map">
	    DELETE
		FROM
			sys_authorities_resources
		WHERE
			authority_id in 
		<foreach collection="authIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</delete>
</mapper>