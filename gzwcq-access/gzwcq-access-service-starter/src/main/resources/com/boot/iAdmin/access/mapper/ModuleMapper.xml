<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.ModuleMapper">
    <sql id="sql">
		T.MODULE_ID,
		T.MODULE_NAME,
		T.MODULE_DESC,
		T.MODULE_TYPE,
		T.PARENT,
		T.I_LEVEL,
		T.LEAF,
		T.ENABLE,
		T.PRIORITY,
		T.RESOURCE_ID
    </sql>
    
    <sql id="columns">
		MODULE_ID,
		MODULE_NAME,
		MODULE_DESC,
		MODULE_TYPE,
		PARENT,
		I_LEVEL,
		LEAF,
		ENABLE,
		PRIORITY,
		RESOURCE_ID
    </sql>
    
    <sql id="vals">
        #{module_id},
		#{module_name},
		#{module_desc},
		#{module_type},
		#{parent},
		#{i_level},
		#{leaf},
		#{enable},
		#{priority},
		#{resource_id}
    </sql>
    
     <sql id="query">
        <if test="module_id != null">
           and t.module_id = #{module_id}
        </if>
        <if test="module_name != null and module_name !=''">
           and t.module_name = #{module_name}
        </if>
        <if test="module_desc != null and module_desc !=''">
           and t.module_desc = #{module_desc}
        </if>
        <if test="module_type != null and module_type !=''">
           and t.module_type = #{module_type}
        </if>
        <if test="parent != null">
           and t.parent = #{parent}
        </if>
        <if test="i_level != null">
           and t.i_level = #{i_level}
        </if>
        <if test="leaf != null">
           and t.leaf = #{leaf}
        </if>
        <if test="enable != null">
           and t.enable = #{enable}
        </if>
        <if test="priority != null">
           and t.priority = #{priority}
        </if>
        <if test="resource_id != null">
           and t.resource_id = #{resource_id}
        </if>
    </sql>
    
    <select id="getAllModulesByUser" parameterType="com.boot.iAdmin.access.model.user.SysUser" resultType="com.boot.iAdmin.access.model.module.CustomModuleTree">
		select * from (SELECT
			<include refid = "sql" />, 
			RE.RESOURCE_PATH MODULE_URL
		FROM
			SYS_AUTHORITIES_RESOURCES AR,
			SYS_ROLES_AUTHORITIES RA,
			SYS_USERS_ROLES UR,
			SYS_MODULES T,
			SYS_RESOURCES RE
		WHERE
			UR.USER_ID = #{user_id}
		AND RA.ROLE_ID = UR.ROLE_ID
		AND RA.AUTHORITY_ID = AR.AUTHORITY_ID
		AND AR.RESOURCE_ID = T.RESOURCE_ID
		AND T.RESOURCE_ID = RE.RESOURCE_ID
		UNION
		SELECT
			<include refid = "sql" />,
			'' MODULE_URL
			FROM SYS_MODULES T
			WHERE T.RESOURCE_ID IS NULL) a
		ORDER BY a.PRIORITY
    </select>
    
    <!-- 查询所有菜单 -->
    <select id="selectAllModules" resultType="com.boot.iAdmin.access.model.module.CustomModuleTree">
        SELECT
			<include refid = "sql" />, 
			RE.RESOURCE_PATH MODULE_URL
		FROM SYS_MODULES T LEFT JOIN SYS_RESOURCES RE ON T.RESOURCE_ID = RE.RESOURCE_ID
		ORDER BY T.PRIORITY
    </select>
    
    <!-- 根据父节点ID查找所有子节点 -->
    <select id="loadMenuByParentId" parameterType="string" resultType="com.boot.iAdmin.access.model.common.ZTreeNode">
		select a.module_id id,
			   a.module_name name,
			   (select count(1) from sys_modules t where t.parent = a.module_id) childNum
			from sys_modules a
			where a.parent = #{id}
			order by priority
    </select>
    
    <!-- 查询菜单信息 -->
    <select id="selectModule" parameterType="com.boot.iAdmin.access.model.module.Module" resultType="com.boot.iAdmin.access.model.module.ModuleVo">
		select 
			<include refid="sql"/>,
			a.resource_name
		from sys_modules t left join sys_resources a on t.resource_id = a.resource_id
		where 1 = 1
			<include refid="query"/>
    </select>
    
    <!-- 新增菜单 -->
	<insert id="insertModule" parameterType="com.boot.iAdmin.access.model.module.Module">
		<selectKey keyProperty="module_id" resultType="long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_modules (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<!-- 删除菜单及其下子菜单 -->
	<delete id="deleteModule" parameterType="com.boot.iAdmin.access.model.module.Module">
		DELETE
			FROM
				sys_modules
			WHERE
				module_id IN (
					SELECT
						*
					FROM
						(
							SELECT
								module_id
							FROM
								sys_modules
							WHERE
								FIND_IN_SET(
									module_id,
									getModuleChildList (#{module_id})
								)
						) AS TEMP
				)
	</delete>
	
	<!-- 更新菜单信息 -->
    <update id="updateIgnoreNull" parameterType="com.boot.iAdmin.access.model.module.Module">
        update sys_modules
        <set>
            <if test="module_id != null">
	           module_id = #{module_id},
	        </if>
	        <if test="module_name != null and module_name !=''">
	           module_name = #{module_name},
	        </if>
	        <if test="module_desc != null and module_desc !=''">
	           module_desc = #{module_desc},
	        </if>
	        <if test="module_type != null and module_type !=''">
	           module_type = #{module_type},
	        </if>
	        <if test="parent != null">
	           parent = #{parent},
	        </if>
	        <if test="i_level != null">
	           i_level = #{i_level},
	        </if>
	        <if test="leaf != null">
	           leaf = #{leaf},
	        </if>
	        <if test="enable != null">
	           enable = #{enable},
	        </if>
	        <if test="priority != null">
	           priority = #{priority},
	        </if>
	        <if test="resource_id != null and resource_id != ''">
	           resource_id = #{resource_id},
	        </if>
	        <if test="resource_id == ''">
	           resource_id = null
	        </if>
        </set>
        where module_id = #{module_id}
    </update>
    
    <select id="selectForListModule" parameterType="com.boot.iAdmin.access.model.module.ModuleVo" resultType="com.boot.iAdmin.access.model.module.ModuleVo">
    	select t.module_id,t.module_name,t.parent, (select auth from  sys_module_data a where t.module_id = a.module_id and a.data_id=#{data_id}) auth from sys_modules t where 1=1
		 <if test="module_name != null and module_name !=''">
           and t.module_name = #{module_name}
        </if>
    </select>
</mapper>