<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.ResourceMapper">
    
     <sql id="columns">
		RESOURCE_ID,
		RESOURCE_TYPE,
		RESOURCE_NAME,
		RESOURCE_DESC,
		RESOURCE_PATH,
		RESOURCE_PATTERN,
		PRIORITY,
		ENABLE,
		ISSYS,
		MODULE_ID,
		PARENT_ID
    </sql>
    
     <sql id="vals">
        #{resource_id},
        #{resource_type},
		#{resource_name},
		#{resource_desc},
		#{resource_path},
		#{resource_pattern},
		#{priority},
		#{enable},
		#{issys},
		#{module_id},
		#{parent_id}
     </sql>
    
    <sql id="cols">
		A.RESOURCE_ID,
		A<PERSON>RESOURCE_TYPE,
		A<PERSON>URCE_NAME,
		A.<PERSON>_DESC,
		A.<PERSON>URCE_PATH,
		A.<PERSON>_PATTERN,
		A.PRIORITY,
		A.ENABLE,
		A.ISSYS,
		A.MODULE_ID,
		A.PARENT_ID
    </sql>
    
    <sql id="query">
        <if test="resource_id != null">
           and a.resource_id = #{resource_id}
        </if>
        <if test="resource_type != null and resource_type != ''">
           and a.resource_type = #{resource_type}
        </if>
        <if test="resource_name != null and resource_name != ''">
           and a.resource_name = #{resource_name}
        </if>
        <if test="resource_desc != null and resource_desc != ''">
           and a.resource_desc = #{resource_desc}
        </if>
        <if test="resource_path != null and resource_path != ''">
           and a.resource_path = #{resource_path}
        </if>
        <if test="resource_pattern != null and resource_pattern != ''">
           and a.resource_pattern = #{resource_pattern}
        </if>
    </sql>
    
    <select id="getResourcesByAuth" parameterType="com.boot.iAdmin.access.model.authority.CustomAuthorities" resultType="com.boot.iAdmin.access.model.resource.SysResource">
        SELECT  <include refid="cols"/>
        	FROM SYS_RESOURCES A,
        		 SYS_AUTHORITIES_RESOURCES B
        	WHERE 
	        	A.RESOURCE_ID = B.RESOURCE_ID 
	        	AND B.AUTHORITY_ID = #{authority_id}
    </select>
    
    <!-- 根据父节点ID查找所有子节点 -->
    <select id="loadResourceByParentId" parameterType="string" resultType="com.boot.iAdmin.access.model.common.ZTreeNode">
		select a.resource_id id,
			   a.resource_name name,
			   (select count(1) from SYS_RESOURCES t where t.parent_id = a.resource_id) childNum
			from SYS_RESOURCES a
			where a.parent_id = #{id}
			order by priority
    </select>
    
    <!-- 获取单个资源信息 -->
    <select id="selectResource" parameterType="com.boot.iAdmin.access.model.resource.SysResource" resultType="com.boot.iAdmin.access.model.resource.ResourceVo">
		select  <include refid="cols"/> ,b.resource_name parent_node     
			from sys_resources a left join sys_resources b on a.parent_id = b.resource_id
			where 1 = 1
			<include refid="query"/> 
    </select>
    
    <!-- 更新资源信息 -->
    <update id="updateIgnoreNull" parameterType="com.boot.iAdmin.access.model.resource.SysResource">
        update sys_resources
        <set>
            <if test="resource_id != null">
		        resource_id = #{resource_id},
		    </if>
		    <if test="resource_name != null and resource_name !=''">
		        resource_name = #{resource_name},
		    </if>
		    <if test="resource_type != null and resource_type !=''">
		        resource_type = #{resource_type},
		    </if>
		    <if test="resource_desc != null and resource_desc !=''">
		        resource_desc = #{resource_desc},
		    </if>
		    <if test="resource_path != null and resource_path !=''">
		        resource_path = #{resource_path},
		    </if>
		    <if test="resource_pattern != null and resource_pattern !=''">
		        resource_pattern = #{resource_pattern},
		    </if>
		    <if test="priority != null">
		        priority = #{priority},
		    </if>
		     <if test="module_id != null">
		        module_id = #{module_id},
		    </if>
		     <if test="parent_id != null">
		        parent_id = #{parent_id},
		    </if>
        </set>
        where resource_id = #{resource_id}
    </update>
    
    <!-- 新增资源 -->
	<insert id="insertResource" parameterType="com.boot.iAdmin.access.model.resource.SysResource">
		<selectKey keyProperty="resource_id" resultType="long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_resources (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<!-- 删除资源及其下子资源 -->
	<delete id="deleteResourceTree" parameterType="com.boot.iAdmin.access.model.resource.SysResource">
		DELETE
			FROM
				sys_resources
			WHERE
				resource_id IN (
					SELECT
						*
					FROM
						(
							SELECT
								resource_id
							FROM
								sys_resources
							WHERE
								FIND_IN_SET(
									resource_id,
									getResourceChildList (#{resource_id})
								)
						) AS TEMP
				)
	</delete>
	
	<select id="getTotalAuthAndResource" parameterType="com.boot.iAdmin.access.model.resource.SysResource" resultType="int">
	    SELECT
			count(1)
		FROM
			sys_authorities_resources
		WHERE
			FIND_IN_SET(
				resource_id,
				getResourceChildList (#{resource_id})
			)
	</select>
	
	<select id="getTotalMoudleAndResource" parameterType="com.boot.iAdmin.access.model.resource.SysResource" resultType="int">
	    SELECT
			count(1)
		FROM
			sys_modules
		WHERE
			FIND_IN_SET(
				resource_id,
				getResourceChildList (#{resource_id})
			)
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.boot.iAdmin.access.model.resource.SysResource" resultType="com.boot.iAdmin.access.model.resource.SysResource">
		select t.resource_name from sys_resources t
		where t.resource_id != #{resource_id}
		<if test="resource_name != null and resource_name != ''">
				and t.resource_name = #{resource_name}
		</if>
	</select>
</mapper>