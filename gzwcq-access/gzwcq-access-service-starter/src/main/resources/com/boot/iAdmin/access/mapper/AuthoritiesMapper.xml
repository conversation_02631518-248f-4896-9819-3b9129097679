<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.boot.iAdmin.access.mapper.AuthoritiesMapper">
    
    <sql id="columns">
        AUTHORITY_ID,
        AUTHORITY_MARK,
        AUTHORITY_NAME,
        AUTHORITY_DESC,
        MESSAGE,
        ENABLE,
        ISSYS
    </sql>
    
    <sql id="vals">
        #{authority_id},
        #{authority_mark},
        #{authority_name},
        #{authority_desc},
        #{message},
        #{enable},
        #{issys}
    </sql>
    
    <sql id="queryVals">
        <if test="authority_id != null">
            and authority_id = #{authority_id}
        </if>
        <if test="authority_mark != null and authority_mark != ''">
            and authority_mark = #{authority_mark}
        </if>
        <if test="authority_name != null and authority_name != ''">
            and authority_name = #{authority_name}
        </if>
        <if test="authority_desc != null and authority_desc != ''">
            and authority_desc = #{authority_desc}
        </if>
        <if test="message != null and message != ''">
            and message = #{message}
        </if>
        <if test="enable != null">
            and enable = #{enable}
        </if>
        <if test="issys != null">
            and issys = #{issys}
        </if>
    </sql>
    
    
    <!-- 新增权限 -->
	<insert id="insertAuth">
		<selectKey keyProperty="authority_id" resultType="long" order="AFTER">
			select last_insert_id()
		</selectKey>
		INSERT INTO SYS_AUTHORITIES (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
    
    <!-- BootstrapTable方式的分页查询 -->
    <!-- 分页查询权限 -->
    <select id="loadAuthByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="com.boot.iAdmin.access.model.authority.AuthVo">
		SELECT 
			<include refid="columns"/> 
			,
			case when enable = 1 then '有效' else '失效' end as enable_txt
		FROM 
			SYS_AUTHORITIES
		WHERE 1 = 1
		<if test="obj.authority_mark != '' and obj.authority_mark != null">
			AND authority_mark like concat('%',#{obj.authority_mark},'%')		    
		</if>
		<if test="obj.authority_name != '' and obj.authority_name != null">
			AND authority_name like concat('%',#{obj.authority_name},'%')		    
		</if>
		LIMIT #{offset},#{limit}      
    </select>
    
     <!-- 查询总数 -->
    <select id="loadTotalAuths" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="long">
		SELECT COUNT(1) 
			FROM 
			SYS_AUTHORITIES
		WHERE 1 = 1
		<if test="obj.authority_mark != '' and obj.authority_mark != null">
			AND authority_mark like concat('%',#{obj.authority_mark},'%')		    
		</if>
		<if test="obj.authority_name != '' and obj.authority_name != null">
			AND authority_name like concat('%',#{obj.authority_name},'%')		    
		</if>
    </select>
    
    
	<!-- 加载权限 -->
	<select id="loadAuthorities" resultType="com.boot.iAdmin.access.model.authority.AuthVo">
		SELECT <include refid="columns"/> FROM SYS_AUTHORITIES
	</select>
	
	<!-- 查询权限 -->
	<select id="getAuth" parameterType="com.boot.iAdmin.access.model.authority.SysAuthorities" resultType="com.boot.iAdmin.access.model.authority.AuthVo">
		select <include refid="columns"/>
		from SYS_AUTHORITIES
		where 1 = 1
		<include refid="queryVals"/>
	</select>
	
	<!-- 根据角色ID查询权限 -->
	<select id="getAuthByRoleId" parameterType="long" resultType="com.boot.iAdmin.access.model.authority.SysAuthorities">
		select <include refid="columns"/>
		from SYS_AUTHORITIES
		where authority_id in (
			select authority_id from sys_roles_authorities where role_id = #{role_id}
		)
	</select>
	
	<!-- 更新权限信息，忽略空字段 -->
	<update id="updateIgnoreNull" parameterType="com.boot.iAdmin.access.model.authority.SysAuthorities">
		update SYS_AUTHORITIES
		<set>
		    <if test="authority_id != null">
		        authority_id = #{authority_id},
		    </if>
		    <if test="authority_mark != null and authority_mark !=''">
		        authority_mark = #{authority_mark},
		    </if>
		    <if test="authority_name != null and authority_name !=''">
		        authority_name = #{authority_name},
		    </if>
		    <if test="authority_desc != null and authority_desc !=''">
		        authority_desc = #{authority_desc},
		    </if>
		    <if test="message != null and message !=''">
		        message = #{message},
		    </if>
		    <if test="authority_id != null">
		        authority_id = #{authority_id},
		    </if>
		    <if test="enable != null">
		        enable = #{enable},
		    </if>
		    <if test="issys != null">
		        issys = #{issys}
		    </if>
		</set>
		where authority_id = #{authority_id}
	</update>
	
	<!-- 批量删除权限 -->
	<delete id="deleteAuthByIds" parameterType="java.util.Map">
	    delete from sys_authorities
		    where authority_id in
		    <foreach collection="authIds" open="(" close=")" separator="," item="id">
					#{id} 
			</foreach>
	</delete>
	
	<!-- 校验权限名称的唯一性 -->
	<select id="validateAuthorityName" resultType="java.lang.String">
		SELECT
			t.AUTHORITY_NAME AS NAME
		FROM
			sys_authorities t
		where t.AUTHORITY_NAME = #{authority_name}
		and (t.AUTHORITY_ID IS NOT NULL AND t.AUTHORITY_ID != ${authority_id}) 
	</select>
	
</mapper>