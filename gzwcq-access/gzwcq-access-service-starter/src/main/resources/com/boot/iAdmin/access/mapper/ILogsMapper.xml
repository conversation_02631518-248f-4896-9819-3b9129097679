<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.iAdmin.access.mapper.ILogsMapper">

	<sql id="columns">
		id, userid, module, method, params, response_date, ip, execute_date, commite, create_date,status
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, t.userid, t.module, t.method, t.params, t.response_date, t.ip, t.execute_date, t.commite, t.create_date,t.status
	</sql>
	
	<sql id="vals">
		#{id}, #{userid}, #{module}, #{method}, #{params}, #{response_date}, #{ip}, #{execute_date}, #{commite}, #{create_date},#{status}
	</sql>
	
	<sql id="conds">
		<if test="id != null and id != ''">
			and id = #{id}
		</if>
		<if test="userid != null and userid != ''">
			and userid = #{userid}
		</if>
		<if test="module != null and module != ''">
			and module = #{module}
		</if>
		<if test="method != null and method != ''">
			and method = #{method}
		</if>
		<if test="params != null and params != ''">
			and params = #{params}
		</if>
		<if test="response_date != null and response_date != ''">
			and response_date = #{response_date}
		</if>
		<if test="ip != null and ip != ''">
			and ip = #{ip}
		</if>
		<if test="execute_date != null">
			and execute_date = #{execute_date}
		</if>
		<if test="commite != null and commite != ''">
			and commite = #{commite}
		</if>
		<if test="create_date != null">
			and create_date = #{create_date}
		</if>
		<if test="status != null">
			and status = #{status}
		</if>
	</sql>
	
	<sql id="bootConds">
		<if test="obj.id != null and obj.id != ''">
			and id = #{obj.id}
		</if>
		<if test="obj.userid != null and obj.userid != ''">
			and userid = #{obj.userid}
		</if>
		<if test="obj.module != null and obj.module != ''">
			and module = #{obj.module}
		</if>
		<if test="obj.method != null and obj.method != ''">
			and method = #{obj.method}
		</if>
		<if test="obj.params != null and obj.params != ''">
			and params = #{obj.params}
		</if>
		<if test="obj.response_date != null and obj.response_date != ''">
			and response_date = #{obj.response_date}
		</if>
		<if test="obj.ip != null and obj.ip != ''">
			and ip = #{obj.ip}
		</if>
		<if test="obj.execute_date != null">
			and execute_date = #{obj.execute_date}
		</if>
		<if test="obj.commite != null and obj.commite != ''">
			and commite = #{obj.commite}
		</if>
		<if test="obj.create_date != null">
			and create_date = #{obj.create_date}
		</if>
		<if test="obj.status != null">
			and status = #{obj.status}
		</if>
	</sql>
	
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<where>
			<if test="obj !=null">
					<if test="obj.id != null and obj.id != ''">
						and t.id = #{obj.id}
					</if>
					<if test="obj.userid != null and obj.userid != ''">
						and t.userid = #{obj.userid}
					</if>
					<if test="obj.module != null and obj.module != ''">
						and t.module = #{obj.module}
					</if>
					<if test="obj.method != null and obj.method != ''">
						and t.method = #{obj.method}
					</if>
					<if test="obj.params != null and obj.params != ''">
						and t.params = #{obj.params}
					</if>
					<if test="obj.response_date != null and obj.response_date != ''">
						and t.response_date = #{obj.response_date}
					</if>
					<if test="obj.ip != null and obj.ip != ''">
						and t.ip = #{obj.ip}
					</if>
					<if test="obj.execute_date != null">
						and t.execute_date = #{obj.execute_date}
					</if>
					<if test="obj.commite != null and obj.commite != ''">
						and t.commite = #{obj.commite}
					</if>
					<if test="obj.create_date != null">
						and date_format(t.create_date,'%Y-%m-%d') = date_format(#{obj.create_date},'%Y-%m-%d')
					</if>
					<if test="obj.status != null">
						and t.status = #{obj.status}
					</if>
					<!-- 用户名 -->
					<if test="obj.userName != null and obj.userName != ''">
						and u.username = #{obj.userName}
					</if>
			</if>
		</where>
	</sql>
	
	
	<sql id="whereSqlForList">
		<where>
					<if test="id != null and id != ''">
						and id = #{id}
					</if>
					<if test="userid != null and userid != ''">
						and userid = #{userid}
					</if>
					<if test="module != null and module != ''">
						and module = #{module}
					</if>
					<if test="method != null and method != ''">
						and method = #{method}
					</if>
					<if test="params != null and params != ''">
						and params = #{params}
					</if>
					<if test="response_date != null and response_date != ''">
						and response_date = #{response_date}
					</if>
					<if test="ip != null and ip != ''">
						and ip = #{ip}
					</if>
					<if test="execute_date != null">
						and execute_date = #{execute_date}
					</if>
					<if test="commite != null and commite != ''">
						and commite = #{commite}
					</if>
					<if test="create_date != null">
						and create_date = #{create_date}
					</if>
					<if test="status != null">
						and status = #{status}
					</if>
		</where>
	</sql>
	
	

	<insert id="save" parameterType="com.boot.iAdmin.access.model.sys_logs.Logs" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" resultType="Long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_logs (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<delete id="delete" parameterType="java.util.Map">
		delete from sys_logs  where
		id in
		<foreach collection="logss" open="(" close=")" separator="," item="id">
		    #{id} 
		</foreach>
	</delete>  
	
	<select id="queryLogsById" resultType="com.boot.iAdmin.access.model.sys_logs.LogsVo">
		select 
		<include refid="columnsAlias"/>,u.username
		from sys_logs t left join sys_users u on t.userid = u.user_id
		where id=#{id} 
	</select>

	<update id="updateIgnoreNull">
		update sys_logs
		<set>
			<if test="id != null and id != ''">
				id=#{id}, 
			</if>
			<if test="userid != null and userid != ''">
				userid=#{userid}, 
			</if>
			<if test="module != null and module != ''">
				module=#{module}, 
			</if>
			<if test="method != null and method != ''">
				method=#{method}, 
			</if>
			<if test="params != null and params != ''">
				params=#{params}, 
			</if>
			<if test="response_date != null and response_date != ''">
				response_date=#{response_date}, 
			</if>
			<if test="ip != null and ip != ''">
				ip=#{ip}, 
			</if>
			<if test="execute_date != null">
				execute_date=#{execute_date}, 
			</if>
			<if test="commite != null and commite != ''">
				commite=#{commite}, 
			</if>
			<if test="create_date != null">
				create_date=#{create_date},
			</if>
			<if test="status != null">
				status=#{status}
			</if>
		</set>
		where id=#{id} 
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.boot.iAdmin.access.model.sys_logs.Logs" resultType="com.boot.iAdmin.access.model.sys_logs.LogsVo">
		select
		<include refid="columns"/>
		from
			sys_logs t join sys_users u on t.userid = u.user_id
		<include refid="whereSqlForList" />
	</select>	


	<!-- 分页查询对象和数据总量查询 -->
	<select id="queryLogsByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="com.boot.iAdmin.access.model.sys_logs.LogsVo">
		select
		<include refid="columnsAlias"/>,u.username
		from
			sys_logs t left join sys_users u on t.userid = u.user_id
			<include refid="whereSql" />
		order by id desc
		limit #{offset}, #{limit}
	</select>
	
	<select id="queryTotalLogss" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="java.lang.Long">
		select
		count(ID)
		from sys_logs t left join sys_users u on t.userid = u.user_id
		<include refid="whereSql" />
	</select>

</mapper>