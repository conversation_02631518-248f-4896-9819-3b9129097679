<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.iAdmin.access.mapper.IGroupsMapper">

	<sql id="columns">
		id, 
		group_name, 
		group_desc
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.group_name, 
		t.group_desc
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{group_name}, 
		#{group_desc}
	</sql>
	
	<sql id="conds">
		<if test="id != null and id != ''">
			and id = #{id}
		</if>
		<if test="group_name != null and group_name != ''">
			and group_name = #{group_name}
		</if>
		<if test="group_desc != null and group_desc != ''">
			and group_desc = #{group_desc}
		</if>
	</sql>
	
	<sql id="bootConds">
		<if test="obj.id != null and obj.id != ''">
			and id = #{obj.id}
		</if>
		<if test="obj.group_name != null and obj.group_name != ''">
			and group_name = #{obj.group_name}
		</if>
		<if test="obj.group_desc != null and obj.group_desc != ''">
			and group_desc = #{obj.group_desc}
		</if>
	</sql>
	
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<where>
			<if test="obj !=null">
					<if test="obj.id != null and obj.id != ''">
						and t.id = #{obj.id}
					</if>
					<if test="obj.group_name != null and obj.group_name != ''">
						and t.group_name = #{obj.group_name}
					</if>
					<if test="obj.group_desc != null and obj.group_desc != ''">
						and t.group_desc = #{obj.group_desc}
					</if>
			</if>
		</where>
	</sql>
	
	
	<sql id="whereSqlForList">
		<where>
					<if test="id != null and id != ''">
						and id = #{id}
					</if>
					<if test="group_name != null and group_name != ''">
						and group_name = #{group_name}
					</if>
					<if test="group_desc != null and group_desc != ''">
						and group_desc = #{group_desc}
					</if>
		</where>
	</sql>
	
	

	<insert id="save" parameterType="com.boot.iAdmin.access.model.groups.Groups" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" resultType="Long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_groups (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
	<delete id="delete" parameterType="java.util.Map">
		delete from sys_groups  where
		id in
		<foreach collection="groupss" open="(" close=")" separator="," item="id">
		    #{id} 
		</foreach>
	</delete>  
	
	<select id="queryGroupsById" resultType="com.boot.iAdmin.access.model.groups.GroupsVo">
		select 
		<include refid="columns"/>
		from sys_groups
		where id=#{id} 
	</select>

	<update id="updateIgnoreNull">
		update sys_groups
		<set>
			<if test="id != null">
				id=#{id}, 
			</if>
			<if test="group_name != null">
				group_name=#{group_name}, 
			</if>
			<if test="group_desc != null">
				group_desc=#{group_desc}
			</if>
		</set>
		where id=#{id} 
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.boot.iAdmin.access.model.groups.Groups" resultType="com.boot.iAdmin.access.model.groups.GroupsVo">
		select
		<include refid="columns"/>
		from
			sys_groups t
		<include refid="whereSqlForList" />
	</select>	


	<!-- 分页查询对象和数据总量查询 -->
	<select id="queryGroupsByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="com.boot.iAdmin.access.model.groups.GroupsVo">
		select
		<include refid="columnsAlias"/>
		from
			sys_groups t
		<include refid="whereSql" />
		order by id desc
		limit #{offset}, #{limit}
	</select>
	
	<select id="queryTotalGroupss" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="java.lang.Long">
		select
		count(id)
		from sys_groups t
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.boot.iAdmin.access.model.groups.Groups" resultType="com.boot.iAdmin.access.model.groups.GroupsVo">
		select <include refid="columnsAlias"/> from sys_groups t
		where t.id != #{id}
					<if test="group_name != null and group_name != ''">
						and t.group_name = #{group_name}
					</if>
					<if test="group_desc != null and group_desc != ''">
						and t.group_desc = #{group_desc}
					</if>
	</select>
	
	<select id="queryByMap" parameterType="java.util.Map" resultType="java.lang.Integer">
		select count(1) from sys_users  where
			group_id in
		<foreach collection="groupss" open="(" close=")" separator="," item="id">
		    #{id} 
		</foreach>
	</select>
	
	<!-- 根据公司获取用户组 -->
	<select id="loadUserGroupByCompany" parameterType="java.util.Map" resultType="com.boot.iAdmin.access.model.groups.GroupsVo">
		select
		<include refid="columns"/>
		from
			sys_groups t
		where id in (SELECT GROUP_ID FROM sys_users_groups WHERE USER_ID = #{user_id} and ORG_ID = #{company})
	</select>
	
	<select id="queryUsersGroupByUser" resultType="com.boot.iAdmin.access.model.groups.GroupsVo">
		select
			<include refid="columns"/>
		from
			sys_groups t,
			sys_users_groups ug
		where ug.group_id = t.id
		and ug.user_id = #{technician_id}
	</select>
</mapper>