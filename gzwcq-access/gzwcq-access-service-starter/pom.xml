<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.zjhc.gzw.gzwcq-parent</groupId>
    <artifactId>gzwcq-access</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-access</groupId>
  <artifactId>gzwcq-access-service-starter</artifactId>
  
  <dependencies>
		<!-- spring security -->
		<!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-security -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-taglibs</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
			<artifactId>gzwcq-redis-starter</artifactId>
			<version>${wfw.version}</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.10.3</version>
		</dependency>
		
		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
			<artifactId>gzwcq-common</artifactId>
			<version>${wfw.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
			<artifactId>gzwcq-cache-starter</artifactId>
			<version>${wfw.version}</version>
		</dependency>
		
		<!-- 阿里druid数据库连接池依赖 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.22</version>
		</dependency>
		
		<!-- swagger -->
		<dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
      <dependency>
          <groupId>commons-net</groupId>
          <artifactId>commons-net</artifactId>
          <version>3.1</version>
          <scope>compile</scope>
      </dependency>

  </dependencies>

	<build>
		<!-- mvn clean install -->
		<finalName>${artifactId}</finalName>
		<resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
	</build>
</project>