package com.zjhc.gzwcq.extProjectComplete.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Date;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectComplete;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteVo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteParam;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import com.zjhc.gzwcq.extProjectComplete.client.ExtProjectCompleteFeignClient;

@Controller
@RequestMapping("/extProjectCompleteController")
@Api(value = "extProjectComplete接口文档", tags = "项目成交")
public class ExtProjectCompleteController {

    private final Logger logger = LoggerFactory.getLogger(ExtProjectCompleteController.class);

    //feign客户端
    @Autowired
    private ExtProjectCompleteFeignClient extProjectCompleteFeignClient;


    /**
     * 分页查询列表
     *
     * @param BootstrapTableModel
     * @param ExtProjectComplete
     * @return String
     * @Title: load
     * <AUTHOR>
    @PostMapping("/load")
    @ResponseBody
    @ApiOperation(value = "分页查询列表")
    public BootstrapTableModel<ExtProjectCompleteVo> list(ExtProjectCompleteParam extProjectCompleteParam) {
        Integer pageNumber = extProjectCompleteParam.getPageNumber();
        Integer limit = extProjectCompleteParam.getLimit();
        extProjectCompleteParam.setPageNumber(pageNumber == 0 || Objects.isNull(pageNumber) ? 1 : pageNumber);
        extProjectCompleteParam.setLimit(limit == 0 || Objects.isNull(limit) ? 10 : limit);
        return extProjectCompleteFeignClient.queryByPage(extProjectCompleteParam);
    }

    /**
     * 新建方法
     *
     * @param ExtProjectComplete
     * @return String
     * @Title: create
     * <AUTHOR>
    @PostMapping("/add")
    @AvoidRepeatableCommit
    @ApiOperation(value = "新增")
    public @ResponseBody
    ResponseEnvelope create(ExtProjectComplete extProjectComplete) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            if (extProjectComplete != null) {
                extProjectCompleteFeignClient.insert(extProjectComplete);
                re.setMessage("新增成功");
            } else {
                re.setMessage("新增失败");
            }
        } catch (Exception e) {
            logger.error("新增失败：", e);
            re.setSuccess(false);
            re.setMessage("新增失败：" + ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 页面修改接口
     *
     * @param ExtProjectComplete
     * @return
     * @Title: update
     * <AUTHOR>
    @PostMapping("/edit")
    @ApiOperation(value = "编辑")
    public @ResponseBody
    ResponseEnvelope update(ExtProjectComplete extProjectComplete) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            if (extProjectComplete != null) {
                extProjectCompleteFeignClient.updateIgnoreNull(extProjectComplete);
                re.setMessage("修改成功");
            } else {
                re.setMessage("修改失败");
            }
        } catch (Exception e) {
            logger.error("修改失败：", e);
            re.setSuccess(false);
            re.setMessage("修改失败：" + ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 删除方法
     *
     * @param ExtProjectComplete
     * @return String
     * @Title: delete
     * <AUTHOR>
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除")
    public @ResponseBody
    ResponseEnvelope delete(String extProjectCompleteIds) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("extProjectCompletes", extProjectCompleteIds.split(","));
            extProjectCompleteFeignClient.deleteByPrimaryKeys(map);
            re.setMessage("删除成功");
        } catch (Exception e) {
            logger.error("删除失败：", e);
            re.setSuccess(false);
            re.setMessage("删除失败：" + ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 保存单个对象
     *
     * @param ExtProjectComplete 对象
     */
    @PostMapping("/saveOne")
    @ResponseBody
    @AvoidRepeatableCommit
    @ApiOperation(value = "保存（新增或修改）")
    public ResponseEnvelope saveOne(ExtProjectComplete extProjectComplete) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            //保存对象信息
            extProjectCompleteFeignClient.saveOne(extProjectComplete);
            re.setResult(extProjectComplete);
        } catch (Exception e) {
            logger.error("保存异常", e);
            re.setMessage("保存失败：" + ExceptionUtils.getMessage(e));
            re.setSuccess(false);
        }
        return re;
    }

    /**
     * 保存多个对象
     *
     * @param objs 用户信息
     */
    @PostMapping("/multipleSaveAndEdit")
    @ResponseBody
    @ApiOperation(value = "批量保存（新增或修改）")
    public ResponseEnvelope multipleSaveAndEdit(String objs) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            //更新对象信息
            ExtProjectComplete[] objArr = JsonUtil.readValue(objs, ExtProjectComplete[].class);
            extProjectCompleteFeignClient.multipleSaveAndEdit(objArr);
            re.setResult(objArr);
        } catch (Exception e) {
            logger.error("保存异常", e);
            re.setMessage("保存失败：" + ExceptionUtils.getMessage(e));
            re.setSuccess(false);
        }
        return re;
    }

    /**
     * 根据主键获取对象
     */
    @PostMapping("/loadOne")
    @ResponseBody
    @ApiOperation(value = "根据主键获取对象")
    public ResponseEnvelope loadOne(ExtProjectComplete extProjectComplete) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(extProjectCompleteFeignClient.selectExtProjectCompleteByPrimaryKey(extProjectComplete));
        } catch (Exception e) {
            logger.error("QUERY ERROR:", e);
            re.setSuccess(false);
            re.setMessage("查询失败：" + ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 唯一性校验
     */
    @PostMapping("/validateUniqueParam")
    @ResponseBody
    @ApiOperation(value = "唯一性校验")
    public ResponseEnvelope validateUniqueParam(ExtProjectComplete extProjectComplete) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setState(extProjectCompleteFeignClient.validateUniqueParam(extProjectComplete));
        } catch (Exception e) {
            logger.error("QUERY ERROR:", e);
            re.setSuccess(false);
            re.setMessage(ExceptionUtils.getMessage(e));
        }
        return re;
    }


    @PostMapping("/updateStatus")
    @ResponseBody
    @ApiOperation(value = "批量更改已读")
    public ResponseEnvelope updateStatus(@RequestParam("ids") String ids) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re = extProjectCompleteFeignClient.updateStatus(ids);
        } catch (Exception e) {
            logger.error("QUERY ERROR:", e);
            re.setSuccess(false);
            re.setMessage(ExceptionUtils.getMessage(e));
        }
        return re;
    }

    @PostMapping("/unreadNumber")
    @ResponseBody
    @ApiOperation(value = "查询未读数量")
    public ResponseEnvelope unreadNumber() {
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re = extProjectCompleteFeignClient.unreadNumber();
        } catch (Exception e) {
            logger.error("QUERY ERROR:", e);
            re.setSuccess(false);
            re.setMessage(ExceptionUtils.getMessage(e));
        }
        return re;
    }

}
