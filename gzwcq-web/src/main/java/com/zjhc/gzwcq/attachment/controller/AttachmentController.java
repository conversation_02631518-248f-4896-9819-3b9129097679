package com.zjhc.gzwcq.attachment.controller;

import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.itextpdf.text.pdf.PdfReader;
import com.zjhc.gzwcq.attachment.client.AttachmentFeignClient;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.entity.AttachmentParam;
import com.zjhc.gzwcq.attachment.entity.AttachmentVo;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("attachmentController")
@Api(value="attachment接口文档",tags="附件表")
public class AttachmentController{

	private final Logger logger = LoggerFactory.getLogger(AttachmentController.class);
	
  	//feign客户端
  	@Autowired
	private AttachmentFeignClient attachmentFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Attachment
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<AttachmentVo> list(AttachmentParam attachmentParam) {
        return attachmentFeignClient.queryByPage(attachmentParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Attachment
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Attachment attachment) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (attachment != null) {
				attachmentFeignClient.insert(attachment);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Attachment
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Attachment attachment) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (attachment != null) {
				attachmentFeignClient.updateIgnoreNull(attachment);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param Attachment
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String attachmentIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("attachments",attachmentIds.split(","));
			attachmentFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Attachment 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(Attachment attachment){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			attachmentFeignClient.saveOne(attachment);
			re.setResult(attachment);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Attachment[] objArr = JsonUtil.readValue(objs, Attachment[].class);
			attachmentFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Attachment attachment) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(attachmentFeignClient.selectAttachmentByPrimaryKey(attachment));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Attachment attachment) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(attachmentFeignClient.validateUniqueParam(attachment));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 上传附件
	 * @param files
	 * @param response
	 */
	@RequestMapping("uploadFile")
	@ResponseBody
	@ApiOperation(value="上传文件")
	public ResponseEnvelope uploadFile(MultipartFile file){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			Attachment attachment = attachmentFeignClient.uploadFile(file);
			re.setResult(attachment);
		}catch(Exception e) {
			logger.error("附件上传失败：",e);
			re.setSuccess(false);
		}
		return re;
	}


	/**
	 * 下载文件
	 */
	@RequestMapping("/download")
	@ApiOperation(value="下载文件")
	public void download(HttpServletRequest request, HttpServletResponse response, @RequestParam(required = true) String ftpPath, @RequestParam(required = true) String file_name) {
		java.io.BufferedInputStream bis = null;
		java.io.BufferedOutputStream bos = null;
		try {
			response.setContentType("text/html;charset=utf-8");
			request.setCharacterEncoding("UTF-8");
			response.setContentType("application/x-msdownload;");
			// 从Ftp获取文件
			Response re = attachmentFeignClient.download(ftpPath);
			Response.Body body = re.body();
			InputStream input = body.asInputStream();
			response.setHeader("Content-disposition", "attachment; filename=" + new String(file_name.getBytes("utf-8"), "ISO8859-1"));
			if(input == null) {//未获取到文件
				response.setHeader("Content-disposition", "attachment; filename=" + new String("FileNotFound.txt".getBytes("utf-8"), "ISO8859-1"));
			}else {
				bis = new BufferedInputStream(input);
				bos = new BufferedOutputStream(response.getOutputStream());
				byte[] buff = new byte[2048];
				int bytesRead;
				while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
					bos.write(buff, 0, bytesRead);
				}
			}
		} catch (Exception e) {
			logger.error("下载失败：",e);
		} finally {
			IOUtils.closeQuietly(bis);
			IOUtils.closeQuietly(bos);
		}
	}

	/**
	 * 查看pdf页码
	 * @param
	 * @param
	 */
	@RequestMapping("getPDfPage")
	@ResponseBody
	@ApiOperation(value="查看pdf页码")
	public ResponseEnvelope getPDfPage(@RequestParam(required = true) String ftpPath ){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			// 从Ftp获取文件
			Response response = attachmentFeignClient.download(ftpPath);
			Response.Body body = response.body();
			InputStream input = body.asInputStream();
			PdfReader.debugmode = true;
			PdfReader pdfReader = new PdfReader(input);
			int pages = pdfReader.getNumberOfPages();
			re.setResult(pages);
		}catch (ClassCastException e){
			re.setMessage("加密的PDF文件，不能在线预览，请下载后再进行预览");
			re.setSuccess(false);
		}
		catch(Exception e) {
			logger.error("附件上传失败：",e);
			re.setSuccess(false);
		}finally {
			PdfReader.debugmode = false;
		}
		return re;
	}

}
