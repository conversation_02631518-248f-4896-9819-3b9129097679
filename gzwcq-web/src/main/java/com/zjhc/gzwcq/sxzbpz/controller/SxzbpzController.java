package com.zjhc.gzwcq.sxzbpz.controller;

import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.sxzbpz.client.SxzbpzFeignClient;
import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("sxzbpzController")
@Api(value="sxzbpz接口文档",tags="筛选指标配置")
public class SxzbpzController{

	private final Logger logger = LoggerFactory.getLogger(SxzbpzController.class);
	
  	//feign客户端
  	@Autowired
	private SxzbpzFeignClient sxzbpzFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Sxzbpz
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<SxzbpzVo> list(SxzbpzParam sxzbpzParam) {
        return sxzbpzFeignClient.queryByPage(sxzbpzParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Sxzbpz
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Sxzbpz sxzbpz) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (sxzbpz != null) {
				sxzbpzFeignClient.insert(sxzbpz);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Sxzbpz
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Sxzbpz sxzbpz) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (sxzbpz != null) {
				sxzbpzFeignClient.updateIgnoreNull(sxzbpz);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param Sxzbpz
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String sxzbpzIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("sxzbpzs",sxzbpzIds.split(","));
			sxzbpzFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Sxzbpz 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(Sxzbpz sxzbpz){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			sxzbpzFeignClient.saveOne(sxzbpz);
			re.setResult(sxzbpz);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Sxzbpz[] objArr = JsonUtil.readValue(objs, Sxzbpz[].class);
			sxzbpzFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Sxzbpz sxzbpz) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(sxzbpzFeignClient.selectSxzbpzByPrimaryKey(sxzbpz));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Sxzbpz sxzbpz) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(sxzbpzFeignClient.validateUniqueParam(sxzbpz));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	@PostMapping("/loadFields")
	@ResponseBody
	@ApiOperation(value="按条件获取业务相关表的所有字段,按表名分组展示")
	public ResponseEnvelope loadFields(SxzbpzParam param){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(sxzbpzFeignClient.loadFields(param));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	@PostMapping("/saveBatch")
	@ApiOperation("批量保存")
	@ResponseBody
	public ResponseEnvelope saveBatch(@RequestParam("ids") String ids, Sxzbpz sxzbpz){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			sxzbpzFeignClient.saveBatch(ids,sxzbpz);
		} catch (Exception e) {
			logger.error("SAVE ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 获取经济行为分析指标,按指标类型分组
	 */
	@PostMapping("/loadByIndexType")
	@ApiOperation("获取经济行为分析指标,按指标类型分组")
	@ResponseBody
	public ResponseEnvelope loadByIndexType(SxzbpzParam param){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(sxzbpzFeignClient.loadByIndexType(param));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 按字段中文名搜索
	 */
	@PostMapping("/loadByFieldNameCn")
	@ApiOperation("按字段中文名搜索")
	@ResponseBody
	public ResponseEnvelope loadByFieldNameCn(Sxzbpz sxzbpz){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(sxzbpzFeignClient.loadByFieldNameCn(sxzbpz));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}
}
