package com.zjhc.gzwcq.newHome;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.monitorwarn.controller.MonitorwarnController;
import com.zjhc.gzwcq.newHome.client.NewHomeFeignClient;
import com.zjhc.gzwcq.newHome.dto.BusinessAssessmentDTO;
import com.zjhc.gzwcq.newHome.vo.BusinessTransactionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:30:44
 **/
@RestController
@RequestMapping("newHomeController")
@Api(tags = "新首页接口")
public class NewHomeController {
    private final Logger logger = LoggerFactory.getLogger(NewHomeController.class);
    @Autowired
    private NewHomeFeignClient client;

    @PostMapping("/selectUserStatus")
    @ApiOperation(value = "查询用户状态")
    public ResponseEnvelope selectUserStatus() {
        return client.selectUserStatus();
    }

    @PostMapping("/selectBusinessAssessment")
    @ApiOperation(value = "查询业务考核数据")
    public ResponseEnvelope selectBusinessAssessment(BusinessAssessmentDTO dto) {
        return client.selectBusinessAssessment(dto);
    }

    @PostMapping("/selectBusinessAssessmentInfo")
    @ApiOperation(value = "查询一行数据块")
    public ResponseEnvelope selectBusinessAssessmentInfo(BusinessAssessmentDTO dto) {
        return client.selectBusinessAssessmentInfo(dto);
    }

    @PostMapping("/selectHandleMattersInfo")
    @ApiOperation(value = "一级企业办理事项情况")
    public BootstrapTableModel<BusinessTransactionVO> selectHandleMattersInfo(BusinessAssessmentDTO dto) {
        return client.selectHandleMattersInfo(dto);
    }
}
