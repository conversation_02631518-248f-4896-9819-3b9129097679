package com.zjhc.gzwcq.jbxxb.controller;

import com.alibaba.fastjson.JSON;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.jbxxb.client.JbxxbFeignClient;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbParam;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("jbxxbController")
@Api(value="jbxxb接口文档",tags="产权基本信息表")
public class JbxxbController{

	private final Logger logger = LoggerFactory.getLogger(JbxxbController.class);
	
  	//feign客户端
  	@Autowired
	private JbxxbFeignClient jbxxbFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Jbxxb
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<JbxxbVo> list(@RequestBody JbxxbParam jbxxbParam) {
        return jbxxbFeignClient.queryByPage(jbxxbParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Jbxxb
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (jbxxb != null) {
				jbxxbFeignClient.insert(jbxxb);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Jbxxb
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (jbxxb != null) {
				jbxxbFeignClient.updateIgnoreNull(jbxxb);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param jbxxbIds
	 * @param realDel
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String jbxxbIds,Boolean realDel) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("jbxxbs",jbxxbIds.split(","));
			map.put("realDel",realDel);
			jbxxbFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Jbxxb 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(@RequestBody Jbxxb jbxxb){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			jbxxbFeignClient.saveOne(jbxxb);
			re.setResult(jbxxb);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Jbxxb[] objArr = JsonUtil.readValue(objs, Jbxxb[].class);
			jbxxbFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(jbxxbFeignClient.selectJbxxbByPrimaryKey(jbxxb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(jbxxbFeignClient.validateUniqueParam(jbxxb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 分页查询未办工商列表
	 */
	@PostMapping("/loadWbgs")
	@ResponseBody
	@ApiOperation(value = "分页查询未办工商列表")
	public BootstrapTableModel<JbxxbVo> loadWbgs(JbxxbParam jbxxbParam){
		return jbxxbFeignClient.loadWbgsByPage(jbxxbParam);
	}

	/**
	 * 合伙企业保存
	 * */
	@PostMapping("/savePartnership")
	@ResponseBody
	@AvoidRepeatableCommit
	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope savePartnership(@RequestBody FormVo vo){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			re.setResult(jbxxbFeignClient.savePartnership(vo));
			re.setSuccess(true);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 国有资本保存
	 * */
	@PostMapping("/saveGovernmentCapital")
	@ResponseBody
	@AvoidRepeatableCommit
	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveGovernmentCapital(@RequestBody FormVo vo){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//自己的组织id
			String orgId = vo.getUnitid();
			//占有类似的时候 主要出资人 代码
			String jbCzrzzjgid = vo.getJbCzrzzjgid();
			if (StringUtils.equals(jbCzrzzjgid,orgId)){
				re.setSuccess(false);
				re.setMessage("请重新选择主要出资人，不能选择企业本身作为出资人选项。");
				return re;
			}
			//保存对象信息
			re.setResult(jbxxbFeignClient.saveGovernmentCapital(vo));
			re.setSuccess(true);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}

	/**
	 * 判断企业是否存在办理中业务
	 */
	@PostMapping("/hasProcessing")
	@ResponseBody
	@ApiOperation(value="是否存在办理中业务")
	public ResponseEnvelope hasProcessing(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(jbxxbFeignClient.hasProcessing(jbxxb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}
	/**
	 * 判断企业是否存在下级
	 */
	@PostMapping("/hasSubordinate")
	@ResponseBody
	@ApiOperation(value="判断企业是否存在下级")
	public ResponseEnvelope hasSubordinate(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(jbxxbFeignClient.hasSubordinate(jbxxb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 根据组织id获取其最新的状态数据
	 */
	@PostMapping("/loadRecent")
	@ResponseBody
	@ApiOperation(value="根据组织id获取其最新的状态数据(含信息采集和合规资料)")
	public ResponseEnvelope loadRecentApprovedByUnitId(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(jbxxbFeignClient.loadRecentApprovedByUnitId(jbxxb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

    /**
     * 根据组织id获取其最新的基本信息数据(变动/注销登记时调用)
     */
    @PostMapping("/loadRecentJbxxOnly")
    @ResponseBody
    @ApiOperation(value="根据组织id获取其最新的状态数据(不含信息采集和合规资料)")
    public ResponseEnvelope loadRecentJbxxOnly(Jbxxb jbxxb) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(jbxxbFeignClient.loadRecentJbxxOnly(jbxxb));
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage(ExceptionUtils.getMessage(e));
        }
        return re;
    }

	@PostMapping("/isMainBusiness")
	@ResponseBody
	@ApiOperation(value="是否国家出资企业主业")
	public ResponseEnvelope isMainBusiness(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(jbxxbFeignClient.isMainBusiness(jbxxb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	@PostMapping("/jbxxCompare")
	@ResponseBody
	@ApiOperation(value="比较当前填报中的企业的基本信息与其最新审核通过的基本信息的不同")
	public ResponseEnvelope jbxxCompare(Jbxxb jbxxb){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(jbxxbFeignClient.jbxxCompare(jbxxb));
		}catch (Exception e){
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	@PostMapping("/isJwToJn")
	@ResponseBody
	@ApiOperation(value="是否境外转投境内企业")
	public ResponseEnvelope isJwToJn(Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(jbxxbFeignClient.isJwToJn(jbxxb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 导出为excel表格
	 */
	@PostMapping("/export")
	@ApiOperation("基本信息表导出为excel")
	public ResponseEntity<byte[]> export(Jbxxb jbxxb, HttpServletResponse resp) throws IOException {
		try {
			return jbxxbFeignClient.export(jbxxb);
		} catch (Exception e) {
			logger.error("EXPORT ERROR:", e);
			resp.reset();
			resp.setContentType("application/json");
			resp.setCharacterEncoding("utf-8");
			ResponseEnvelope re = new ResponseEnvelope();
			re.setMessage("基本信息表导出失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
			resp.getWriter().println(JSON.toJSONString(re));
		}
		return null;
	}

	/**
	 * 分页查询列表(每个企业分组，只展示一条数据)
	 * @Title: load
	 * <AUTHOR> @param jbxxbParam
	 * @return String
	 */
	@PostMapping("/loadJbxxbByGroup")
	@ResponseBody
	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<JbxxbVo> loadJbxxbByGroup(JbxxbParam jbxxbParam) {
		//将根据企业进行分组查询
		jbxxbParam.setIsGroup(Constants.YES);
		//登记证只查国有企业
//		if("2".equals(jbxxbParam.getDjType())){
//			jbxxbParam.setBusinessNature(Constants.QYLX_GYQY);
//		}
		return jbxxbFeignClient.queryByDjPage(jbxxbParam);
	}

	/**
	 * 根据主键获取对象,转为pdf
	 * */
	@PostMapping("/loadPdf")
	@ResponseBody
	@ApiOperation(value="根据主键获取对象,转为pdf-登记表")
	public ResponseEntity<byte[]> loadPdf(Jbxxb jbxxb, HttpServletResponse resp) throws IOException {
		try {
			return jbxxbFeignClient.loadPdf(jbxxb);
		} catch (Exception e) {
			logger.error("EXPORT ERROR:", e);
			resp.reset();
			resp.setContentType("application/json");
			resp.setCharacterEncoding("utf-8");
			ResponseEnvelope re = new ResponseEnvelope();
			re.setMessage("操作失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
			resp.getWriter().println(JSON.toJSONString(re));
		}
		return null;
	}

	@PostMapping("/loadAllSituations")
	@ApiOperation("获取所有登记情形")
	@ResponseBody
	public ResponseEnvelope loadAllSituations(){
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(jbxxbFeignClient.loadAllSituations());
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 根据主键获取对象,转为pdf
	 * */
	@PostMapping("/loadDJZPdf")
	@ResponseBody
	@ApiOperation(value="根据主键获取对象,转为pdf-登记证")
	public ResponseEntity<byte[]> loadDJZPdf(Jbxxb jbxxb, HttpServletResponse resp) throws IOException {
		try {
			ResponseEntity<byte[]> re = jbxxbFeignClient.loadDJZPdf(jbxxb);
			if("false".equals(re.getHeaders().get("isgzw").get(0))){
				throw new RuntimeException("非国资委账号不可以打印登记证.");
			}
			return re;
		} catch (Exception e) {
			logger.error("EXPORT ERROR:", e);
			resp.reset();
			resp.setContentType("application/json");
			resp.setCharacterEncoding("utf-8");
			ResponseEnvelope re = new ResponseEnvelope();
			re.setMessage("操作失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
			resp.getWriter().println(JSON.toJSONString(re));
		}
		return null;
	}
}
