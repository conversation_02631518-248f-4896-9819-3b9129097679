package com.zjhc.gzwcq.files.controller;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Date;

import feign.Response;
import org.springframework.beans.factory.annotation.Autowired;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.files.entity.Files;
import com.zjhc.gzwcq.files.entity.FilesVo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.files.entity.FilesParam;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import com.zjhc.gzwcq.files.client.FilesFeignClient;

import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("filesController")
@Api(value="files接口文档",tags="cq_files")
public class FilesController{

	private final Logger logger = LoggerFactory.getLogger(FilesController.class);
	
  	//feign客户端
  	@Autowired
	private FilesFeignClient filesFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Files
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<FilesVo> list(FilesParam filesParam) {
        return filesFeignClient.queryByPage(filesParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Files
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Files files) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (files != null) {
				filesFeignClient.insert(files);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Files
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Files files) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (files != null) {
				filesFeignClient.updateIgnoreNull(files);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param Files
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String filesIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("filess",filesIds.split(","));
			filesFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Files 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(Files files){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			filesFeignClient.saveOne(files);
			re.setResult(files);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Files[] objArr = JsonUtil.readValue(objs, Files[].class);
			filesFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Files files) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(filesFeignClient.selectFilesByPrimaryKey(files));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	/**
	 * 查看次数加一
	 * */
	@PostMapping("/lookTime")
	@ResponseBody
	@ApiOperation(value="根据主键获取对象")
	public void lookTime(String id) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			filesFeignClient.lookTime(id);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
		}

	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Files files) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(filesFeignClient.validateUniqueParam(files));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}


}
