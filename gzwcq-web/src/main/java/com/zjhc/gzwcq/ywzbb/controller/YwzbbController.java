package com.zjhc.gzwcq.ywzbb.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbParam;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import com.zjhc.gzwcq.ywzbb.client.YwzbbFeignClient;

@Controller
@RequestMapping("ywzbbController")
@Api(value="ywzbb接口文档",tags="产权业务指标表")
public class YwzbbController{

	private final Logger logger = LoggerFactory.getLogger(YwzbbController.class);
	
  	//feign客户端
  	@Autowired
	private YwzbbFeignClient ywzbbFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Ywzbb
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<YwzbbVo> list(YwzbbParam ywzbbParam) {
        return ywzbbFeignClient.queryByPage(ywzbbParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Ywzbb
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Ywzbb ywzbb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (ywzbb != null) {
				ywzbbFeignClient.insert(ywzbb);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Ywzbb
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Ywzbb ywzbb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (ywzbb != null) {
				ywzbbFeignClient.updateIgnoreNull(ywzbb);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param Ywzbb
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String ywzbbIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("ywzbbs",ywzbbIds.split(","));
			ywzbbFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Ywzbb 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(Ywzbb ywzbb){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			ywzbbFeignClient.saveOne(ywzbb);
			re.setResult(ywzbb);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Ywzbb[] objArr = JsonUtil.readValue(objs, Ywzbb[].class);
			ywzbbFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Ywzbb ywzbb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(ywzbbFeignClient.selectYwzbbByPrimaryKey(ywzbb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Ywzbb ywzbb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(ywzbbFeignClient.validateUniqueParam(ywzbb));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

}
