package com.zjhc.gzwcq.businessInfo.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.DateUtils;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.businessInfo.client.BusinessInfoFeignClient;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Controller
@RequestMapping("businessInfoController")
@Api(value="businessInfo接口文档",tags="登记状态表/流程实例表")
public class BusinessInfoController{

	private final Logger logger = LoggerFactory.getLogger(BusinessInfoController.class);
	
  	//feign客户端
  	@Autowired
	private BusinessInfoFeignClient businessInfoFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param BusinessInfo
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<BusinessInfoVo> list(BusinessInfoParam businessInfoParam) {
        return businessInfoFeignClient.queryByPage(businessInfoParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param BusinessInfo
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(BusinessInfo businessInfo) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (businessInfo != null) {
				businessInfoFeignClient.insert(businessInfo);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param BusinessInfo
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(BusinessInfo businessInfo) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (businessInfo != null) {
				businessInfoFeignClient.updateIgnoreNull(businessInfo);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param BusinessInfo
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String businessInfoIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("businessInfos",businessInfoIds.split(","));
			businessInfoFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(BusinessInfo businessInfo){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			businessInfoFeignClient.saveOne(businessInfo);
			re.setResult(businessInfo);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			BusinessInfo[] objArr = JsonUtil.readValue(objs, BusinessInfo[].class);
			businessInfoFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(BusinessInfo businessInfo) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(businessInfoFeignClient.selectBusinessInfoByPrimaryKey(businessInfo));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(BusinessInfo businessInfo) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(businessInfoFeignClient.validateUniqueParam(businessInfo));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 获取企业登记历史记录列表
	 * */
	@PostMapping("/loadHistoryList")
	@ResponseBody
	@ApiOperation(value="获取企业等级历史记录列表")
	public ResponseEnvelope loadHistoryList(BusinessInfoParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(businessInfoFeignClient.loadHistoryList(param));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 提交审核
	 * */
	@PostMapping("/submitReview")
	@ResponseBody
	@AvoidRepeatableCommit
	@ApiOperation(value="提交审核")
	public ResponseEnvelope submitReview(@RequestBody FormVo formVo) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			//自己的组织id
			String orgId = formVo.getUnitid();
			String jbCzrzzjgid = "";
			String businessNature = formVo.getBusinessNature();
			if (StringUtils.equals(Constants.QYLX_HHQY,businessNature)){
					jbCzrzzjgid = formVo.getHhCzqyId();
			}else {
				//占有类似的时候 主要出资人 代码
				jbCzrzzjgid = formVo.getJbCzrzzjgid();
			}


			if (StringUtils.equals(jbCzrzzjgid,orgId)){
				re.setSuccess(false);
				re.setMessage("请重新选择主要出资人，不能选择企业本身作为出资人选项。");
				return re;
			}
			String result = businessInfoFeignClient.submitReview(formVo);
			if(StringUtils.isNotEmpty(result)){
				throw new RuntimeException(result);
			}
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("提交失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 工商登记资料补录提交审核
	 * */
	@PostMapping("/suppleSubmitReview")
	@ResponseBody
	@AvoidRepeatableCommit
	@ApiOperation(value="工商登记资料补录提交审核")
	public ResponseEnvelope suppleSubmitReview(@RequestBody Jbxxb jbxxb) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			String result = businessInfoFeignClient.suppleSubmitReview(jbxxb);
			if(StringUtils.isNotEmpty(result)){
				throw new RuntimeException(result);
			}
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("提交失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 审核
	 * */
	@PostMapping("/review")
	@ResponseBody
	@ApiOperation(value="提交审核")
	@AvoidRepeatableCommit
	public ResponseEnvelope review(BusinessInfoParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			businessInfoFeignClient.review(param);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("提交失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	@PostMapping("/recallReview")
	@ResponseBody
	@ApiOperation(value="撤回审核")
	@AvoidRepeatableCommit
	public ResponseEnvelope recallReview(BusinessInfoParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
		return 	businessInfoFeignClient.recallReview(param);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("提交失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	@PostMapping("/selectRecallReview")
	@ResponseBody
	@ApiOperation(value="撤回审核")
	@AvoidRepeatableCommit
	public ResponseEnvelope selectRecallReview(BusinessInfoParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			return businessInfoFeignClient.selectRecallReview(param);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("提交失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 审核轨迹列表查询
	 * */
	@PostMapping("/reviewHistoryList")
	@ResponseBody
	@ApiOperation(value="审核轨迹列表查询")
	public ResponseEnvelope reviewHistoryList(BusinessInfoParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(businessInfoFeignClient.reviewHistoryList(param));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 待办（待审核/退回）列表查询
	 * */
	@PostMapping("/todoList")
	@ResponseBody
	@ApiOperation(value="待办（待审核/退回）列表查询")
	public ResponseEnvelope todoList(BusinessInfoParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(businessInfoFeignClient.todoList(param));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 待办（待审核/退回）列表查询(分页+筛选条件)
	 * */
	@PostMapping("/todoOrReturnList")
	@ResponseBody
	@ApiOperation(value="待办（待审核/退回）列表查询(分页+筛选条件)")
	public BootstrapTableModel<BusinessInfoVo> todoOrReturnList(@RequestBody BusinessInfoParam param) {
		BootstrapTableModel<BusinessInfoVo> model;
		try {
			model = businessInfoFeignClient.todoOrReturnList(param);
		} catch (Exception e) {
			model = new BootstrapTableModel<>();
			logger.error("QUERY ERROR:",e);
			model.setSuccess(false);
			model.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return model;
	}
	/**
	 * 业务审核 已审核 待列表查询(分页+筛选条件)
	 * */
	@PostMapping("/getPassData")
	@ResponseBody
	@ApiOperation(value="已审核列表查询(分页+筛选条件)")
	public BootstrapTableModel<BusinessInfoVo> getPassData(@RequestBody BusinessInfoParam param) {
		BootstrapTableModel<BusinessInfoVo> model;
		try {
			model = businessInfoFeignClient.getPassData(param);
		} catch (Exception e) {
			model = new BootstrapTableModel<>();
			logger.error("QUERY ERROR:",e);
			model.setSuccess(false);
			model.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return model;
	}
	/**
	 * 导出为excel表格
	 */
	@PostMapping("/exportPassData")
	@ApiOperation("导出数据为excel")
	public void exportPassData(BusinessInfoParam param , HttpServletResponse resp) throws IOException {
		try {
			resp.setContentType("application/vnd.ms-excel");
			resp.setCharacterEncoding("utf-8");
			String fileName ="已审核列表";
			resp.addHeader("Content-Disposition", "attachment; filename="+fileName+".xlsx");
			param.setPageNumber(1);
			param.setLimit(Integer.MAX_VALUE);
			param.setOffest(0);
			EasyExcel.write(resp.getOutputStream(), BusinessInfoVo.class).
					registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet(fileName).
					doWrite((List<BusinessInfoVo>) businessInfoFeignClient.getPassData(param).getRows());
		} catch (Exception e) {
			logger.error("EXPORT ERROR:", e);
			resp.reset();
			resp.setContentType("application/json");
			resp.setCharacterEncoding("utf-8");
			ResponseEnvelope re = new ResponseEnvelope();
			re.setMessage("导出Excel失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
			resp.getWriter().println(JSON.toJSONString(re));
		}
	}
	/**
	 * 导出为excel表格
	 */
	@PostMapping("/export")
	@ApiOperation("导出数据为excel")
	public ResponseEntity<byte[]> export(BusinessInfoParam param , HttpServletResponse resp) throws IOException {
		try {
			String fileName;
			if ("1".equals(param.getTodoType())){
				fileName = "待审核列表";
			}else {
				fileName = "上级退回列表";
			}
			//表头
			String str = fileName;
			//正常合同头
			String[] title1 = {"企业名称", "企业代码", "等级类型", "业务状态", "当前审核节点"};
			String[] title2 = {"企业名称", "企业代码", "等级类型", "业务状态", "国家出资企业"};
			SysUser submitUser = (SysUser) SpringSecurityUserTools.instance().getUser(null);
			List<List<String>> header1 = new ArrayList<>();
			if (!submitUser.getOrganization_id().equals("39DC82B5C0000021A568D4D612672F5A")) {
				for (String s : title1) {
					List<String> cellContain = new ArrayList<>();
					cellContain.add(str);
					cellContain.add(s);
					header1.add(cellContain);
				}
			}else {
				for (String s : title2) {
					List<String> cellContain = new ArrayList<>();
					cellContain.add(str);
					cellContain.add(s);
					header1.add(cellContain);
				}
			}
			param.setPageNumber(Objects.isNull(param.getPageNumber()) || param.getPageNumber() <= 0 ? 1 : param.getPageNumber());
			param.setLimit(Objects.isNull(param.getLimit()) || param.getLimit() <= 0 ? Integer.MAX_VALUE : param.getLimit());
			//数据集合
			List<BusinessInfoVo> list = (List<BusinessInfoVo>) businessInfoFeignClient.todoOrReturnList(param).getRows();
			List<List<Object>> dataList = new ArrayList<>();
			list.forEach(attr -> {
				List<Object> data = new ArrayList<>();
				data.add(attr.getOrgName());
				data.add(attr.getOrgCode());
				data.add(attr.getRgTypeName());
				data.add(attr.getBusStatus());
				if (!submitUser.getOrganization_id().equals("39DC82B5C0000021A568D4D612672F5A")){
					data.add(attr.getAfCurrentNode());
				}else {
					data.add(attr.getJbGjczqyStr());
				}
				dataList.add(data);
			});
			//创建流
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			HttpHeaders headers = new HttpHeaders();
			headers.setContentDispositionFormData("attachment",
					new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

			// 头的策略
			WriteCellStyle headWriteCellStyle = new WriteCellStyle();
			// 自动居中
			headWriteCellStyle.setWrapped(true);
			headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
			headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			// 内容的策略
			WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
			//设置 自动换行
			contentWriteCellStyle.setWrapped(true);
			//设置 水平居中
			contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
			//设置 垂直居中
			contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			// 设置边框
			contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
			contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
			contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
			contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
			// 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
			HorizontalCellStyleStrategy horizontalCellStyleStrategy =
					new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
			ExcelWriter excelWriter = EasyExcel.write(out).registerWriteHandler(horizontalCellStyleStrategy).build();
			try {
				//生成easyexcel的流
				WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "商家报名管理").head(header1).build();
				excelWriter.write(dataList, writeSheet1);
			} finally {
				// 千万别忘记finish 会帮忙关闭流
				if (excelWriter != null) {
					excelWriter.finish();
				}
			}
			return new ResponseEntity<>(out.toByteArray(), headers, HttpStatus.CREATED);
		} catch (Exception e) {
			logger.error("EXPORT ERROR:", e);
		}
		return null;
	}

	@PostMapping("/allApprovedAndNowList")
	@ResponseBody
	@ApiOperation(value="查询当前企业所有审核通过的历史记录和当前这条记录")
	public BootstrapTableModel<BusinessInfoVo> allApprovedAndNowList(BusinessInfo param) {
		BootstrapTableModel<BusinessInfoVo> model;
		try {
			param.setRgUnitstate(Constants.REVIEW_STATUS_3);
			model = businessInfoFeignClient.allApprovedAndNowList(param);
		} catch (Exception e) {
			model = new BootstrapTableModel<>();
			logger.error("QUERY ERROR:",e);
			model.setSuccess(false);
			model.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return model;
	}
}
