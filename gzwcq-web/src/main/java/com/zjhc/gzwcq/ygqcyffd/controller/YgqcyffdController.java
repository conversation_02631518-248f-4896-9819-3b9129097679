package com.zjhc.gzwcq.ygqcyffd.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdVo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdParam;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import com.zjhc.gzwcq.ygqcyffd.client.YgqcyffdFeignClient;

@Controller
@RequestMapping("ygqcyffdController")
@Api(value="ygqcyffd接口文档",tags="原股权持有方浮动")
public class YgqcyffdController{

	private final Logger logger = LoggerFactory.getLogger(YgqcyffdController.class);
	
  	//feign客户端
  	@Autowired
	private YgqcyffdFeignClient ygqcyffdFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Ygqcyffd
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<YgqcyffdVo> list(YgqcyffdParam ygqcyffdParam) {
        return ygqcyffdFeignClient.queryByPage(ygqcyffdParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Ygqcyffd
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Ygqcyffd ygqcyffd) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (ygqcyffd != null) {
				ygqcyffdFeignClient.insert(ygqcyffd);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Ygqcyffd
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Ygqcyffd ygqcyffd) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (ygqcyffd != null) {
				ygqcyffdFeignClient.updateIgnoreNull(ygqcyffd);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param Ygqcyffd
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String ygqcyffdIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("ygqcyffds",ygqcyffdIds.split(","));
			ygqcyffdFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Ygqcyffd 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(Ygqcyffd ygqcyffd){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			ygqcyffdFeignClient.saveOne(ygqcyffd);
			re.setResult(ygqcyffd);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Ygqcyffd[] objArr = JsonUtil.readValue(objs, Ygqcyffd[].class);
			ygqcyffdFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Ygqcyffd ygqcyffd) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(ygqcyffdFeignClient.selectYgqcyffdByPrimaryKey(ygqcyffd));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Ygqcyffd ygqcyffd) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(ygqcyffdFeignClient.validateUniqueParam(ygqcyffd));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

}
