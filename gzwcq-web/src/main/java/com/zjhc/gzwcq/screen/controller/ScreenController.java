package com.zjhc.gzwcq.screen.controller;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.screen.client.ScreenFeignClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("screenController")
@Api(value = "screen接口文档", tags = "首页大屏接口文档")
public class ScreenController {

    private final Logger logger = LoggerFactory.getLogger(ScreenController.class);

    @Autowired
    private ScreenFeignClient screenFeignClient;

    @PostMapping("/todoList")
    @ApiOperation(value = "待办事项")
    public ResponseEnvelope todoList() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(screenFeignClient.todoList());
        } catch (Exception e) {
            logger.error("待办事项查询失败:" + e.getMessage());
            re.setSuccess(false);
            re.setMessage("待办事项查询失败:" + e.getMessage());
        }
        return re;
    }

    @PostMapping("/companyNumBySeason")
    @ApiOperation(value = "企业户数统计(按季度)")
    public ResponseEnvelope companyNumBySeason(@RequestParam(value = "unitId",required = false) String unitId,
                                               @RequestParam(value = "year",required = false) String year) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(screenFeignClient.companyNumBySeason(unitId,year));
        } catch (Exception e) {
            logger.error("企业户数(按季度)统计失败:" + e.getMessage());
            re.setSuccess(false);
            re.setMessage("企业户数(按季度)统计失败:" + e.getMessage());
        }
        return re;
    }

    @PostMapping("/companyNumByZzxs")
    @ApiOperation(value = "企业户数统计(按组织形式)")
    public ResponseEnvelope companyNumByZzxs(@RequestParam(value = "unitId",required = false) String unitId) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(screenFeignClient.companyNumByZzxs(unitId));
        } catch (Exception e) {
            logger.error("企业户数(按组织形式)统计失败:" + e.getMessage());
            re.setSuccess(false);
            re.setMessage("企业户数(按组织形式)统计失败:" + e.getMessage());
        }
        return re;
    }

    /**
     * 企业户数统计(按组织形式)V2 - 直接从字典获取组织形式名称
     * <AUTHOR>
     */
    @PostMapping("/companyNumByZzxsV2")
    @ApiOperation(value = "企业户数统计(按组织形式)V2")
    public ResponseEnvelope companyNumByZzxsV2(@RequestParam(value = "unitId",required = false) String unitId) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(screenFeignClient.companyNumByZzxsV2(unitId));
        } catch (Exception e) {
            logger.error("企业户数(按组织形式)V2统计失败:" + e.getMessage());
            re.setSuccess(false);
            re.setMessage("企业户数(按组织形式)V2统计失败:" + e.getMessage());
        }
        return re;
    }

    @PostMapping("/companyNumByLevel")
    @ApiOperation(value = "企业户数统计(按企业级次)")
    public ResponseEnvelope companyNumByLevel(@RequestParam(value = "unitId",required = false) String unitId) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(screenFeignClient.companyNumByLevel(unitId));
        } catch (Exception e) {
            logger.error("企业户数(按企业级次)统计失败:" + e.getMessage());
            re.setSuccess(false);
            re.setMessage("企业户数(按企业级次)统计失败:" + e.getMessage());
        }
        return re;
    }

    @PostMapping("/loadOrgs")
    @ApiOperation(value = "加载组织下拉树")
    public ResponseEnvelope loadOrgs() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(screenFeignClient.loadOrgs());
        } catch (Exception e) {
            logger.error("加载组织下拉树失败:" + e.getMessage());
            re.setSuccess(false);
            re.setMessage("加载组织下拉树失败:" + e.getMessage());
        }
        return re;
    }

    @PostMapping("/businessAssessment")
    @ApiOperation(value = "业务考核")
    public ResponseEnvelope businessAssessment(String type,String year) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(screenFeignClient.businessAssessment(type,year));
        } catch (Exception e) {
            logger.error("查询失败:" + e.getMessage());
            re.setSuccess(false);
            re.setMessage("查询失败:" + e.getMessage());
        }
        return re;
    }
}
