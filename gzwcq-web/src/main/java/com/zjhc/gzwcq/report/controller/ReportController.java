package com.zjhc.gzwcq.report.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.Constants;
import com.zjhc.gzwcq.dataQueryModel.client.DataQueryModelClient;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModel;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("reportController")
public class ReportController {

    private final Logger logger = LoggerFactory.getLogger(ReportController.class);

    @Autowired
    private DataQueryModelClient dataQueryModelClient;

    /**
     * 数据查询列表页
     */
    @PostMapping("/load")
    @ResponseBody
    public BootstrapTableModel<DataQueryModelVo> queryByPage(@RequestBody DataQueryModelParam param) {
        BootstrapTableModel<DataQueryModelVo> model;
        try {
            model = dataQueryModelClient.queryByPage(param);
        } catch (Exception e) {
            model = new BootstrapTableModel<>();
            logger.error("QUERY ERROR：",e);
            model.setSuccess(false);
            model.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return model;
    }

    /**
     * 数据查询页导出excel
     */
    @PostMapping("/export")
    @AvoidRepeatableCommit(timeout = 2000)
    public void export(@RequestBody DataQueryModelParam param, HttpServletResponse response) throws IOException {
        try {
            List<SxzbpzVo> select = param.getSelect();
            select = (select == null? new ArrayList<>(0):select);
            List<DataQueryModelVo> rows = dataQueryModelClient.selectForList(param);
            //不变的显示列
            String[] heads = {"企业名称","企业代码","国家出资企业","产权登记情形","注册资本(万元)","设立注册日期","企业类别","企业级次","登记类型"};
            //标题行
            List<List<String>> headList = new ArrayList<>(heads.length+select.size());
            for (String s : heads) {
                List<String> head = new ArrayList<>(1);
                head.add(s);
                headList.add(head);
            }
            for (SxzbpzVo sxzbpzVo : select) {
                List<String> l = new ArrayList<>(1);
                l.add(sxzbpzVo.getFieldNameCn());
                headList.add(l);
            }
            //
            Field[] fields = DataQueryModel.class.getDeclaredFields();
            List<List<String>> lists = new ArrayList<>();
            for (DataQueryModelVo row : rows) {
                List<String> l = new ArrayList<>();
                l.add(row.getJB_QYMC());
                l.add(row.getJB_ZZJGDM());
                l.add(row.getJB_GJCZQY());
                if(StringUtils.isNotBlank(row.getSituation())){
                    l.add(row.getSituation().split("-")[1]);
                }else {
                    l.add("");
                }
                l.add(row.getJB_ZCZB());
                l.add(row.getJB_ZCRQ());
                l.add(row.getJB_QYLB());
                l.add(row.getJB_QYJC());
                if (StringUtils.isNotBlank(row.getSituation())){
                    l.add(row.getSituation().split("-")[0]);
                }else {
                    l.add("");
                }
                for (SxzbpzVo vo : select) {
                    for (Field field : fields) {
                        if (field.getName().equals(vo.getFieldName())){
                            field.setAccessible(true);
                            l.add((String) field.get(row));
                            break;
                        }
                    }
                }
                lists.add(l);
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("数据查询表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream()).head(headList).autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("数据查询").doWrite(lists);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            logger.error("EXPORT ERROR:", e);
            ResponseEnvelope re = new ResponseEnvelope();
            re.setMessage("导出Excel失败："+ExceptionUtils.getMessage(e));
            re.setSuccess(false);
            response.getWriter().println(JSON.toJSONString(re));
        }
    }

    /**
     * 经济行为分析统计列表页
     */
    @PostMapping("/economicAnalysis")
    @ResponseBody
    public ResponseEnvelope economicAnalysis(@RequestBody DataQueryModelParam param){
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(dataQueryModelClient.economicAnalysis(param));
        } catch (Exception e) {
            logger.error("QUERY ERROR：",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 经济行为分析统计页导出excel
     */
    @PostMapping("/statisticsExport")
    @AvoidRepeatableCommit(timeout = 2000)
    public void statisticsExport(@RequestBody DataQueryModelParam param, HttpServletResponse response) throws IOException {
        try {
            List<DataQueryModelVo> rows = dataQueryModelClient.economicAnalysis(param);
            List<SxzbpzVo> select = param.getSelect();
            if (CollectionUtils.isNotEmpty(select)){
                //只有是经济行为分析指标且为数字类型并且为统计页指标的统计才有意义
                select = select.stream().filter(sxzbpzVo -> sxzbpzVo.getEconomicBehaviorAnalysis() != null &&
                        sxzbpzVo.getEconomicBehaviorAnalysis() == 1 && sxzbpzVo.getStatistics() != null &&
                        sxzbpzVo.getStatistics() == 1 && Constants.FIELD_TYPE_NUM.equals(sxzbpzVo.getType()))
                        .collect(Collectors.toList());
            }
            //不变的显示列
            String[] heads = {"序号","经济行为","办理次数"};
            //标题行
            List<List<String>> headList = new ArrayList<>(heads.length+select.size());
            for (String s : heads) {
                List<String> head = new ArrayList<>(1);
                head.add(s);
                headList.add(head);
            }
            for (SxzbpzVo sxzbpzVo : select) {
                List<String> l = new ArrayList<>(1);
                l.add(sxzbpzVo.getFieldNameCn());
                headList.add(l);
            }
            Field[] fields = DataQueryModel.class.getDeclaredFields();
            List<List<String>> lists = new ArrayList<>();
            Long sumNums = 0L;
            //合计行(比展示的数字合计列多3列固定列)
            List<String> sumList = new ArrayList<>(select.size()+3);
            //给初始值0
            for (int i = 0; i < select.size()+3; i++) {
                sumList.add("0");
            }
            for (DataQueryModelVo row : rows) {
                sumNums += row.getNums();//次数合计
                List<String> l = new ArrayList<>();
                l.add(String.valueOf(row.getNo()));
                l.add(row.getSituation());
                l.add(String.valueOf(row.getNums()));
                for (int j = 0; j < select.size(); j++) {
                    for (Field field : fields) {
                        if (field.getName().equals(select.get(j).getFieldName())) {
                            field.setAccessible(true);
                            String fieldValue = (String) field.get(row);
                            sumList.set(j+3, String.valueOf(new BigDecimal(sumList.get(j+3)).add(new BigDecimal(fieldValue))));
                            l.add(fieldValue);
                            break;
                        }
                    }
                }
                lists.add(l);
            }
            sumList.set(0,"合计");
            sumList.set(1,"");
            sumList.set(2, String.valueOf(sumNums));
            //合计行
            lists.add(sumList);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("经济行为分析统计表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream()).head(headList).autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("经济行为分析-结果列表").doWrite(lists);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            logger.error("EXPORT ERROR:", e);
            ResponseEnvelope re = new ResponseEnvelope();
            re.setMessage("导出Excel失败："+ExceptionUtils.getMessage(e));
            re.setSuccess(false);
            response.getWriter().println(JSON.toJSONString(re));
        }
    }

    /**
     * 经济行为分析明细列表页
     */
    @PostMapping("/economicDetail")
    @ResponseBody
    public BootstrapTableModel<DataQueryModelVo> economicDetail(@RequestBody DataQueryModelParam param){
        BootstrapTableModel<DataQueryModelVo> model;
        try {
            model = dataQueryModelClient.economicDetail(param);
        } catch (Exception e) {
            model = new BootstrapTableModel<>();
            logger.error("QUERY ERROR：",e);
            model.setSuccess(false);
            model.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return model;
    }

    /**
     * 经济行为分析明细页导出excel
     */
    @PostMapping("/detailsExport")
    @AvoidRepeatableCommit(timeout = 2000)
    public void detailsExport(@RequestBody DataQueryModelParam param, HttpServletResponse response) throws IOException {
        try {
            List<DataQueryModelVo> rows = dataQueryModelClient.selectForDetailList(param);
            List<SxzbpzVo> select = param.getSelect();
            if (CollectionUtils.isNotEmpty(select)){
                //指标是经济行为分析并且为明细页指标才导出列
                select = select.stream().filter(sxzbpzVo -> sxzbpzVo.getEconomicBehaviorAnalysis() != null &&
                        sxzbpzVo.getEconomicBehaviorAnalysis() == 1 && sxzbpzVo.getDetail() != null
                        && sxzbpzVo.getDetail() == 1).collect(Collectors.toList());
            }
            //不变的显示列
            String[] heads = {"序号","企业名称","统一社会信用代码"};
            //标题行
            List<List<String>> headList = new ArrayList<>(heads.length+select.size());
            for (String s : heads) {
                List<String> head = new ArrayList<>(1);
                head.add(s);
                headList.add(head);
            }
            for (SxzbpzVo sxzbpzVo : select) {
                List<String> l = new ArrayList<>(1);
                l.add(sxzbpzVo.getFieldNameCn());
                headList.add(l);
            }
            Field[] fields = DataQueryModel.class.getDeclaredFields();
            List<List<String>> lists = new ArrayList<>();
            for (DataQueryModelVo row : rows) {
                List<String> l = new ArrayList<>();
                l.add(String.valueOf(row.getNo()));
                l.add(row.getJB_QYMC());
                l.add(row.getJB_ZZJGDM());
                for (SxzbpzVo sxzbpzVo : select) {
                    for (Field field : fields) {
                        if (field.getName().equals(sxzbpzVo.getFieldName())) {
                            field.setAccessible(true);
                            String fieldValue = (String) field.get(row);
                            l.add(fieldValue);
                            break;
                        }
                    }
                }
                lists.add(l);
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("经济行为分析明细表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream()).head(headList).autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("经济行为分析-明细列表").doWrite(lists);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            logger.error("EXPORT ERROR:", e);
            ResponseEnvelope re = new ResponseEnvelope();
            re.setMessage("导出Excel失败："+ExceptionUtils.getMessage(e));
            re.setSuccess(false);
            response.getWriter().println(JSON.toJSONString(re));
        }
    }

    /**
     * 产权树导出
     */
    @PostMapping("/treeExport")
    @ApiOperation("产权树导出为excel")
    public ResponseEntity<byte[]> treeExport(DataQueryModelParam param,HttpServletResponse resp) throws IOException {
        try {
            return dataQueryModelClient.treeExport(param);
        } catch (Exception e) {
            logger.error("EXPORT ERROR:", e);
            resp.reset();
            resp.setContentType("application/json");
            resp.setCharacterEncoding("utf-8");
            ResponseEnvelope re = new ResponseEnvelope();
            re.setMessage("产权树导出失败："+ExceptionUtils.getMessage(e));
            re.setSuccess(false);
            resp.getWriter().println(JSON.toJSONString(re));
        }
        return null;
    }
}
