package com.zjhc.gzwcq.extProjectTransferee.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Date;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeVo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeParam;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import com.zjhc.gzwcq.extProjectTransferee.client.ExtProjectTransfereeFeignClient;

@Controller
@RequestMapping("extProjectTransfereeController")
@Api(value="extProjectTransferee接口文档",tags="项目受让方")
public class ExtProjectTransfereeController{

	private final Logger logger = LoggerFactory.getLogger(ExtProjectTransfereeController.class);
	
  	//feign客户端
  	@Autowired
	private ExtProjectTransfereeFeignClient extProjectTransfereeFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param ExtProjectTransferee
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<ExtProjectTransfereeVo> list(ExtProjectTransfereeParam extProjectTransfereeParam) {
		Integer pageNumber = extProjectTransfereeParam.getPageNumber();
		Integer limit = extProjectTransfereeParam.getLimit();
		extProjectTransfereeParam.setPageNumber(pageNumber == 0 || Objects.isNull(pageNumber) ? 1 : pageNumber);
		extProjectTransfereeParam.setLimit(limit == 0 || Objects.isNull(limit) ? 10 : limit);
        return extProjectTransfereeFeignClient.queryByPage(extProjectTransfereeParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param ExtProjectTransferee
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(ExtProjectTransferee extProjectTransferee) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (extProjectTransferee != null) {
				extProjectTransfereeFeignClient.insert(extProjectTransferee);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param ExtProjectTransferee
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(ExtProjectTransferee extProjectTransferee) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (extProjectTransferee != null) {
				extProjectTransfereeFeignClient.updateIgnoreNull(extProjectTransferee);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param ExtProjectTransferee
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String extProjectTransfereeIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("extProjectTransferees",extProjectTransfereeIds.split(","));
			extProjectTransfereeFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param ExtProjectTransferee 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(ExtProjectTransferee extProjectTransferee){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			extProjectTransfereeFeignClient.saveOne(extProjectTransferee);
			re.setResult(extProjectTransferee);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			ExtProjectTransferee[] objArr = JsonUtil.readValue(objs, ExtProjectTransferee[].class);
			extProjectTransfereeFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(ExtProjectTransferee extProjectTransferee) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(extProjectTransfereeFeignClient.selectExtProjectTransfereeByPrimaryKey(extProjectTransferee));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(ExtProjectTransferee extProjectTransferee) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(extProjectTransfereeFeignClient.validateUniqueParam(extProjectTransferee));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

}
