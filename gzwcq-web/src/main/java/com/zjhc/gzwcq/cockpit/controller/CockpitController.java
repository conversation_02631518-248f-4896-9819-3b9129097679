package com.zjhc.gzwcq.cockpit.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.zjhc.gzwcq.cockpit.client.CockpitClient;
import com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 驾驶舱统计
 */
@Controller
@RequestMapping("cockpitController")
@Api(value="cockpit接口文档",tags="驾驶舱统计")
public class CockpitController {

    private final static Log logger = LogFactory.getLog(CockpitController.class);

    @Resource
    private CockpitClient cockpitClient;

    /**
     * 省属企业考核数量
     */
    @PostMapping("/getProvince")
    @AvoidRepeatableCommit
    @ApiOperation(value="查询省属企业考核数量")
    @ResponseBody
    public ResponseEnvelope getSSQYKHSL(String year) {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(cockpitClient.getSSQYKHSL(year));
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 省属企业考核数量导出为excel表格
     */
    @PostMapping("/getProvinceExport")
    @ApiOperation("导出数据为excel")
    public void getProvinceExport(HttpServletResponse resp,@RequestBody Map<String,String> map) throws IOException {
        try {
            resp.setContentType("application/vnd.ms-excel");
            resp.setCharacterEncoding("utf-8");
            String fileName = "省属企业考核数据";
            resp.addHeader("Content-Disposition", "attachment; filename="+fileName+".xlsx");
            EasyExcel.write(resp.getOutputStream(), CockpitSSQYKH.class).
                    registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet(fileName).
                    doWrite(cockpitClient.getSSQYKHSL(map.get("year")));
        } catch (Exception e) {
            logger.error("EXPORT ERROR:", e);
            resp.reset();
            resp.setContentType("application/json");
            resp.setCharacterEncoding("utf-8");
            ResponseEnvelope re = new ResponseEnvelope();
            re.setMessage("导出Excel失败："+ExceptionUtils.getMessage(e));
            re.setSuccess(false);
            resp.getWriter().println(JSON.toJSONString(re));
        }
    }

    /**
     * 省地市企业户数统计-企业户数图-按企业类别
     * */
    @PostMapping("/qylbPrefectureAndCity")
    @ResponseBody
    @ApiOperation(value="省地市企业户数统计-企业户数图-按企业类别")
    public ResponseEnvelope qylbPrefectureAndCity() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(cockpitClient.qylbPrefectureAndCity());
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 省地市企业户数统计-企业户数图-按组织形式
     * */
    @PostMapping("/zzxsPrefectureAndCity")
    @ResponseBody
    @ApiOperation(value="省地市企业户数统计-企业户数图-按组织形式")
    public ResponseEnvelope zzxsPrefectureAndCity() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(cockpitClient.zzxsPrefectureAndCity());
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 省地市企业户数统计-企业户数图-按企业级次
     * */
    @PostMapping("/qyjcPrefectureAndCity")
    @ResponseBody
    @ApiOperation(value="省地市企业户数统计-企业户数图-按企业级次")
    public ResponseEnvelope qyjcPrefectureAndCity() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(cockpitClient.qyjcPrefectureAndCity());
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 省属企业户数统计-企业户数图-按企业类别
     * */
    @PostMapping("/qylbByOneLevel")
    @ResponseBody
    @ApiOperation(value="省属企业户数统计-企业户数图-按组织形式")
    public ResponseEnvelope qylbByOneLevel() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(cockpitClient.qylbByOneLevel());
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 省属企业户数统计-企业户数图-按企业类别
     * */
    @PostMapping("/zzxsByOneLevel")
    @ResponseBody
    @ApiOperation(value="省属企业户数统计-企业户数图-按组织形式")
    public ResponseEnvelope zzxsByOneLevel() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(cockpitClient.zzxsByOneLevel());
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }

    /**
     * 省属企业户数统计-企业户数图-按企业级次
     * */
    @PostMapping("/qyjcByOneLevel")
    @ResponseBody
    @ApiOperation(value="省属企业户数统计-企业户数图-按企业级次")
    public ResponseEnvelope qyjcByOneLevel() {
        ResponseEnvelope re = new ResponseEnvelope();
        try {
            re.setResult(cockpitClient.qyjcByOneLevel());
        } catch (Exception e) {
            logger.error("QUERY ERROR:",e);
            re.setSuccess(false);
            re.setMessage("查询失败："+ ExceptionUtils.getMessage(e));
        }
        return re;
    }
}
