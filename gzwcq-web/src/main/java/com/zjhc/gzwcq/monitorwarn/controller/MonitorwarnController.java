package com.zjhc.gzwcq.monitorwarn.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Date;

import com.boot.IAdmin.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import com.zjhc.gzwcq.monitorwarn.client.MonitorwarnFeignClient;

@Controller
@RequestMapping("monitorwarnController")
@Api(value="monitorwarn接口文档",tags="自动预警表")
public class MonitorwarnController{

	private final Logger logger = LoggerFactory.getLogger(MonitorwarnController.class);
	
  	//feign客户端
  	@Autowired
	private MonitorwarnFeignClient monitorwarnFeignClient;
	
	
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Monitorwarn
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<MonitorwarnVo> list(MonitorwarnParam monitorwarnParam) {
        return monitorwarnFeignClient.queryByPage(monitorwarnParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Monitorwarn
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Monitorwarn monitorwarn) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (monitorwarn != null) {
				monitorwarnFeignClient.insert(monitorwarn);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Monitorwarn
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Monitorwarn monitorwarn) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (monitorwarn != null) {
				monitorwarnFeignClient.updateIgnoreNull(monitorwarn);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param Monitorwarn
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String monitorwarnIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("monitorwarns",monitorwarnIds.split(","));
			monitorwarnFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Monitorwarn 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(Monitorwarn monitorwarn){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			monitorwarnFeignClient.saveOne(monitorwarn);
			re.setResult(monitorwarn);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Monitorwarn[] objArr = JsonUtil.readValue(objs, Monitorwarn[].class);
			monitorwarnFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Monitorwarn monitorwarn) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(monitorwarnFeignClient.selectMonitorwarnByPrimaryKey(monitorwarn));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Monitorwarn monitorwarn) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(monitorwarnFeignClient.validateUniqueParam(monitorwarn));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 全自动预警处理 (发起审核)
	 * */
	@PostMapping("/submitWarnReview")
	@ResponseBody
	@ApiOperation(value="全自动预警处理 (发起审核)")
	public ResponseEnvelope submitWarnReview(Monitorwarn monitorwarn) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			String result = monitorwarnFeignClient.submitWarnReview(monitorwarn);
			if(StringUtils.isNotEmpty(result)){
				throw new RuntimeException(result);
			}
		} catch (Exception e) {
			logger.error("ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 查询预警审核列表数据
	 * */
	@PostMapping("/selectWarnReviewList")
	@ResponseBody
	@ApiOperation(value="查询预警审核列表数据")
	public BootstrapTableModel<MonitorwarnVo> selectWarnReviewList(MonitorwarnParam monitorwarn) {
		return monitorwarnFeignClient.selectWarnReviewList(monitorwarn);
	}

	/**
	 * 预警读消息
	 * */
	@PostMapping("/readMessage")
	@ResponseBody
	@ApiOperation(value="预警读消息")
	public ResponseEnvelope readMessage(Monitorwarn monitorwarn) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			monitorwarnFeignClient.readMessage(monitorwarn);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
}
