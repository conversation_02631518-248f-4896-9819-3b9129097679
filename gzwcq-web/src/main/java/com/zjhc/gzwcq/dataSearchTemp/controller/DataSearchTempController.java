package com.zjhc.gzwcq.dataSearchTemp.controller;

import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.dataSearchTemp.client.DataSearchTempFeignClient;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("dataSearchTempController")
@Api(value="dataSearchTemp接口文档",tags="数据查询模板")
public class DataSearchTempController{

	private final Logger logger = LoggerFactory.getLogger(DataSearchTempController.class);
	
  	//feign客户端
  	@Resource
	private DataSearchTempFeignClient dataSearchTempFeignClient;
	

	/**
	 * 分页查询列表
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<DataSearchTempVo> list(DataSearchTempParam dataSearchTempParam) {
        return dataSearchTempFeignClient.queryByPage(dataSearchTempParam);
	}
	
	/**
	 * 新建方法
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(DataSearchTemp dataSearchTemp) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (dataSearchTemp != null) {
				dataSearchTempFeignClient.insert(dataSearchTemp);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(DataSearchTemp dataSearchTemp) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (dataSearchTemp != null) {
				dataSearchTempFeignClient.updateIgnoreNull(dataSearchTemp);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String ids) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<>(1);
			map.put("dataSearchTemps",ids.split(","));
			dataSearchTempFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(@RequestBody DataSearchTemp dataSearchTemp){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			dataSearchTempFeignClient.saveOne(dataSearchTemp);
			// re.setResult(dataSearchTemp);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			DataSearchTemp[] objArr = JsonUtil.readValue(objs, DataSearchTemp[].class);
			dataSearchTempFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(DataSearchTemp dataSearchTemp) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dataSearchTempFeignClient.selectDataSearchTempByPrimaryKey(dataSearchTemp));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(DataSearchTemp dataSearchTemp) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(dataSearchTempFeignClient.validateUniqueParam(dataSearchTemp));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 查询我的模板
	 * */
	@PostMapping("/selectMyTemps")
	@ResponseBody
	@ApiOperation(value="查询我的模板")
	public ResponseEnvelope selectMyTemps(DataSearchTempParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dataSearchTempFeignClient.selectMyTemps(param));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 查询所有公开模板(不包括自己的)
	 * */
	@PostMapping("/selectSharedTemps")
	@ResponseBody
	@ApiOperation(value="所有公开模板(不包括自己的)")
	public ResponseEnvelope selectSharedTemps(DataSearchTempParam param) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(dataSearchTempFeignClient.selectSharedTemps(param));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
}
