package com.zjhc.gzwcq.placard.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Date;

import com.zjhc.gzwcq.attachment.client.AttachmentFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.JsonUtil;
import com.zjhc.gzwcq.placard.entity.Placard;
import com.zjhc.gzwcq.placard.entity.PlacardVo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.boot.IAdmin.common.aop.AvoidRepeatableCommit;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.placard.entity.PlacardParam;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.exception.ExceptionUtils;
import com.zjhc.gzwcq.placard.client.PlacardFeignClient;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("placardController")
@Api(value="placard接口文档",tags="cq_placard")
public class PlacardController{

	private final Logger logger = LoggerFactory.getLogger(PlacardController.class);
	
  	//feign客户端
  	@Autowired
	private PlacardFeignClient placardFeignClient;
	
	@Autowired
	private AttachmentFeignClient attachmentFeignClient;
	
	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Placard
	 * @return String
	 */
	@PostMapping("/load")
	@ResponseBody
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<PlacardVo> list(PlacardParam placardParam) {
        return placardFeignClient.queryByPage(placardParam);
	}
	
	/**
	 * 新建方法
	 * @Title: create
	 * <AUTHOR> @param Placard
	 * @return String
	 */
	@PostMapping("/add")
	@AvoidRepeatableCommit
  	@ApiOperation(value="新增")
	public @ResponseBody ResponseEnvelope create(Placard placard) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (placard != null) {
				placardFeignClient.insert(placard);
				re.setMessage("新增成功");
			} else {
				re.setMessage("新增失败");
			}
		}catch(Exception e){
			logger.error("新增失败：",e);
			re.setSuccess(false);
			re.setMessage("新增失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 页面修改接口
	 * @Title: update
	 * <AUTHOR> @param Placard
	 * @return
	 */
	@PostMapping("/edit")
  	@ApiOperation(value="编辑")
	public @ResponseBody ResponseEnvelope update(Placard placard) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			if (placard != null) {
				placardFeignClient.updateIgnoreNull(placard);
				re.setMessage("修改成功");
			} else {
				re.setMessage("修改失败");
			}
		}catch(Exception e){
			logger.error("修改失败：",e);
			re.setSuccess(false);
			re.setMessage("修改失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 删除方法
	 * @Title: delete
	 * <AUTHOR> @param Placard
	 * @return String
	 */
	@PostMapping("/delete")
  	@ApiOperation(value="批量删除")
	public @ResponseBody ResponseEnvelope delete(String placardIds) {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("placards",placardIds.split(","));
			placardFeignClient.deleteByPrimaryKeys(map);
			re.setMessage("删除成功");
		}catch(Exception e){
			logger.error("删除失败：",e);
			re.setSuccess(false);
			re.setMessage("删除失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	
	/**
	 * 保存单个对象
	 * @param Placard 对象
	 * */
	@PostMapping("/saveOne")
	@ResponseBody
	@AvoidRepeatableCommit
  	@ApiOperation(value="保存（新增或修改）")
	public ResponseEnvelope saveOne(Placard placard){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//保存对象信息
			placardFeignClient.saveOne(placard);
			re.setResult(placard);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 保存多个对象
	 * @param objs 用户信息
	 * */
	@PostMapping("/multipleSaveAndEdit")
	@ResponseBody
  	@ApiOperation(value="批量保存（新增或修改）")
	public ResponseEnvelope multipleSaveAndEdit(String objs){
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			//更新对象信息
			Placard[] objArr = JsonUtil.readValue(objs, Placard[].class);
			placardFeignClient.multipleSaveAndEdit(objArr);
			re.setResult(objArr);
		}catch(Exception e){
			logger.error("保存异常",e);
			re.setMessage("保存失败："+ExceptionUtils.getMessage(e));
			re.setSuccess(false);
		}
		return re;
	}
	
	/**
	 * 根据主键获取对象
	 * */
	@PostMapping("/loadOne")
	@ResponseBody
  	@ApiOperation(value="根据主键获取对象")
	public ResponseEnvelope loadOne(Placard placard) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(placardFeignClient.selectPlacardByPrimaryKey(placard));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
	  
  	/**
	 * 唯一性校验
	 * */
	@PostMapping("/validateUniqueParam")
	@ResponseBody
	@ApiOperation(value="唯一性校验")
	public ResponseEnvelope validateUniqueParam(Placard placard) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setState(placardFeignClient.validateUniqueParam(placard));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
          	re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	@PostMapping("/uploadPic")
	@ResponseBody
	@ApiOperation(value="上传图片返回图片路径")
	public ResponseEnvelope uploadPic(MultipartFile file) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			String url = attachmentFeignClient.uploadFile(file).getFtpFilePath();
			re.setResult(url);
		} catch (Exception e) {
			logger.error("UPLOAD FILE ERROR:",e);
			re.setSuccess(false);
			re.setMessage(ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 在小铃铛中查看公告内容
	 * */
	@PostMapping("/loadOneInBell")
	@ResponseBody
	@ApiOperation(value="在小铃铛中查看公告内容")
	public ResponseEnvelope loadOneInBell(Placard placard) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			PlacardVo placardVo = placardFeignClient.selectPlacardByPrimaryKey(placard);
			//查阅数+1 并判断已读未读并更新此条数据
			placardFeignClient.updateLookTimeAndRead(placardVo);
			re.setResult(placardVo);
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}

	/**
	 * 消息数量
	 * */
	@PostMapping("/messageTotal")
	@ResponseBody
	@ApiOperation(value="消息数量")
	public ResponseEnvelope messageTotal(PlacardVo placardVo) {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			re.setResult(placardFeignClient.messageTotal(placardVo));
		} catch (Exception e) {
			logger.error("QUERY ERROR:",e);
			re.setSuccess(false);
			re.setMessage("查询失败："+ExceptionUtils.getMessage(e));
		}
		return re;
	}
}
