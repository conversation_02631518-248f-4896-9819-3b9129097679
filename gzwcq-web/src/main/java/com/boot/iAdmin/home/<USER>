package com.boot.iAdmin.home;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.boot.IAdmin.common.domain.ResponseEnvelope;

@Controller
@RequestMapping("/homeController")
public class HomeController {
	
	Log logger = LogFactory.getLog(this.getClass());
	
	
	
	/**
	 * 登录成功，跳转到home page
	 * <AUTHOR>
	 * @param model
	 * @param response
	 * @return String
	 */
	@RequestMapping("/index")
	public String home(Model model,HttpServletResponse response,HttpServletRequest request){
		return "/system/home.html";
	}
	
	/**
	 *加载统计数据
	 * */
	@RequestMapping("/loadStatisticalData")
	@ResponseBody
	public ResponseEnvelope loadStatisticalData() {
		ResponseEnvelope re = new ResponseEnvelope();
		try{
			Map<String,Object> map = new HashMap<String,Object>();
			
			re.setResult(map);
		}catch(Exception e){
			logger.error("获取首页统计数据出错:", e);
			re.setSuccess(false);
		}
		return re;
	}
	
	
}
