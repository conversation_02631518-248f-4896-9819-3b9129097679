package com.boot.iAdmin.core;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.boot.IAdmin.common.utils.SpringContextUtil;
import com.boot.iAdmin.core.config.CommonsMultipartAutoConfiguration;
import com.boot.iAdmin.core.config.DataSourceFilterConfig;
import com.boot.iAdmin.core.config.MvcConfig;
import com.boot.iAdmin.core.config.SwaggerConfig;
import com.boot.iAdmin.jwt.EnableJWT;

/**
 * 容器启动类
 * @fix:通过TOMCAT等外部web容器启动必须实现SpringBootServletInitializer，main方法启动可以不实现SpringBootServletInitializer
 * */
//禁用springboot自动数据源配置 exclude排除
@SpringBootApplication(exclude = {CommonsMultipartAutoConfiguration.class,QuartzAutoConfiguration.class,FreeMarkerAutoConfiguration.class},scanBasePackages= {"com.wfw","com.boot","com.zjhc"})
//加载配置类
@Import({MvcConfig.class,DataSourceFilterConfig.class,SwaggerConfig.class})
//mapper扫描包
@MapperScan(value="com.**.mapper.**")
@EnableTransactionManagement
@EnableJWT//开启JWT支持
@EnableDiscoveryClient // 开启服务发现
@EnableFeignClients(basePackages= {"com.wfw","com.zjhc"}) // 开启Feign,不指定basePackages则不会为jar包中的client实例化且如果启动类不在最外层时无法初始化
public class Application extends SpringBootServletInitializer {
	
	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);//内置web容器启动项目
		SpringContextUtil.setApplicationContext(context);//缓存IOC容器方便代码获取容器
	}
	
	/**
	 * 实现该方法用于使用外部tomcat启动时，让spring容器加载当前配置类，能够正确读取配置信息（主要是ComponentScan让容器知道扫描哪些包）从而能够正确初始化spring容器并顺利加载所有配置以及装载所需的bean
	 * @fix:如果是通过main方法启动系统则可不实现该方法
	 * */
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(Application.class);
	}
	
}
