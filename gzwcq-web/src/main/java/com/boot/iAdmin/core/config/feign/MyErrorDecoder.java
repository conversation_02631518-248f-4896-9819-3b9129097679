package com.boot.iAdmin.core.config.feign;

import org.springframework.context.annotation.Configuration;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

/**
 * 统一的远程调用异常处理
 * <AUTHOR> 2022年3月24日 12:49:12
 * */
@Configuration
@Slf4j
public class MyErrorDecoder implements ErrorDecoder {

	@Override
    public Exception decode(String methodKey, Response response) {
        String message = null;
        try {
            if (response.body() != null) {
                message = Util.toString(response.body().asReader(Util.UTF_8));
                log.warn("远程调用异常："+message);
                return new RuntimeException("操作失败，请稍后再试！");
                /*Map<String,Object> map = JsonUtil.readValue(message, Map.class);
                if(map.get("message") != null) {
                	return new RuntimeException(String.valueOf(map.get("message")));
                }else {
                	return new RuntimeException("操作失败，请稍后再试！");
                }*/
            }
        } catch (Exception ignored) {
        	ignored.printStackTrace();
        }
        return new RuntimeException("系统繁忙，请稍后再试！");
    }
}