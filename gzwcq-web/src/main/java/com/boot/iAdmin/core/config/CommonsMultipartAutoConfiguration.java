package com.boot.iAdmin.core.config;

import javax.servlet.MultipartConfigElement;
import javax.servlet.Servlet;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.autoconfigure.web.servlet.MultipartProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication.Type;

/**
 * spring文件上传配置
 * <P> springboot 默认使用StandardMultipartHttpServletRequest来处理文件，此处配置使用CommonsMultipartFile
 * */
@Configuration
@ConditionalOnClass({ Servlet.class, CommonsMultipartResolver.class })
@ConditionalOnProperty(value = "spring.servlet.multipart.enabled", havingValue = "true")
@ConditionalOnWebApplication(type = Type.SERVLET)
@EnableConfigurationProperties(MultipartProperties.class)
public class CommonsMultipartAutoConfiguration {
	
	private final MultipartProperties multipartProperties;

	public CommonsMultipartAutoConfiguration(MultipartProperties multipartProperties) {
		this.multipartProperties = multipartProperties;
	}
	
	@Bean
	@ConditionalOnMissingBean({ MultipartConfigElement.class,CommonsMultipartResolver.class })
	public MultipartConfigElement multipartConfigElement() {
		return this.multipartProperties.createMultipartConfig();
	}
	
	// 显示声明CommonsMultipartResolver为mutipartResolver(主要负责处理文件上传内容)  
	@Bean(name = DispatcherServlet.MULTIPART_RESOLVER_BEAN_NAME)
	@ConditionalOnMissingBean(MultipartResolver.class)
    public MultipartResolver multipartResolver()  
    {  
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();  
        resolver.setMaxUploadSize(1024*1024*10);
        return resolver;  
    }
}
