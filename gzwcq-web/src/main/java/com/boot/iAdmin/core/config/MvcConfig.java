package com.boot.iAdmin.core.config;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.MultipartConfigElement;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@EnableConfigurationProperties({MvcConfig.MvcProperties.class})
public class MvcConfig implements WebMvcConfigurer{
	
	private final MultipartConfigElement multipartConfig;
	
	private final MvcProperties mvcProperties;
	
//	private final ServerProperties serverProperties;
	
	private WebMvcProperties webMvcProperties;//DispatcherServlet 默认path从2.1后改为在mvc中配置，之前在server中
	
	public MvcConfig( WebMvcProperties webMvcProperties,ServerProperties serverProperties, ObjectProvider<MultipartConfigElement> multipartConfigProvider,MvcProperties mvcProperties) {
		this.webMvcProperties = webMvcProperties;
		this.multipartConfig = multipartConfigProvider.getIfAvailable();
		this.mvcProperties = mvcProperties;
//		this.serverProperties = serverProperties;
	}
	
	/**
	 * 自定义满足多个匹配规则
	 */
	@Bean
	public ServletRegistrationBean<DispatcherServlet> apiServlet(@Qualifier(DispatcherServletAutoConfiguration.DEFAULT_DISPATCHER_SERVLET_BEAN_NAME)DispatcherServlet dispatcherServlet) {
		ServletRegistrationBean<DispatcherServlet> registration = new ServletRegistrationBean<DispatcherServlet>(dispatcherServlet);
		// 注入上传配置到自己注册的ServletRegistrationBean
		registration.addUrlMappings(this.webMvcProperties.getServlet().getPath());
		//自定义urlMappings
		for(String urlMapping : this.mvcProperties.urlMappings) {
			registration.addUrlMappings(urlMapping);
		}
		if(multipartConfig != null) {
			registration.setMultipartConfig(multipartConfig);
		}
		registration.setLoadOnStartup(1);//必须大于0
		registration.setName(DispatcherServletAutoConfiguration.DEFAULT_DISPATCHER_SERVLET_BEAN_NAME);//覆盖默认DispatcherServlet
		return registration;
	}
	
    
    /**
	 * 设置默认首页
	 * */
    /*@Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/").setViewName("redirect:auto/access/index");
        registry.addViewController("/login").setViewName("redirect:auto/access/index");
        registry.setOrder(Ordered.HIGHEST_PRECEDENCE);
    }*/
    
    @ConfigurationProperties(prefix = "com.auto.mvc")
    static class MvcProperties {
       
    	private List<String> urlMappings = new ArrayList<String>();//不验证地址

		public List<String> getUrlMappings() {
			return urlMappings;
		}

		public void setUrlMappings(List<String> urlMappings) {
			this.urlMappings = urlMappings;
		}

    }

}
