package com.boot.iAdmin.core.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 线程池自动配置
 * <AUTHOR>
 * @time 2018年10月18日 10:09:58
 * */
@Configuration
@ConditionalOnClass(value= {ThreadPoolTaskExecutor.class})
@ConditionalOnProperty(value="com.auto.executor.enable",havingValue="true")
@EnableConfigurationProperties(ExecutorConfiguration.ExecutorProperties.class)
public class ExecutorConfiguration {
	
	Log log = LogFactory.getLog(this.getClass());
	
	private final ExecutorProperties executorProperties;
	
	public ExecutorConfiguration(ExecutorProperties executorProperties) {
		this.executorProperties = executorProperties;
	}
	
	@Bean(value="myExecutor")
    public Executor bulidServiceExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(executorProperties.getCorePoolSize());
        //配置最大线程数
        executor.setMaxPoolSize(executorProperties.getMaxPoolSize());
        //配置队列大小
        executor.setQueueCapacity(executorProperties.getQueueCapacity());
        //空闲时间
        executor.setKeepAliveSeconds(executorProperties.getKeepAliveSeconds());
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        log.info("=======线程池初始化成功，名称为myExecutor========");
        return executor;
    }
	
	@ConfigurationProperties(prefix="com.auto.executor")
	static class ExecutorProperties{
		
		private int corePoolSize = 5; //线程池维护线程的最少数量
		
		private int maxPoolSize = Integer.MAX_VALUE; //线程池维护线程的最大数量
		
		private int keepAliveSeconds = 60;//允许的空闲时间
		
		private int queueCapacity = Integer.MAX_VALUE;//缓存队列

		public int getCorePoolSize() {
			return corePoolSize;
		}

		public void setCorePoolSize(int corePoolSize) {
			this.corePoolSize = corePoolSize;
		}

		public int getMaxPoolSize() {
			return maxPoolSize;
		}

		public void setMaxPoolSize(int maxPoolSize) {
			this.maxPoolSize = maxPoolSize;
		}

		public int getKeepAliveSeconds() {
			return keepAliveSeconds;
		}

		public void setKeepAliveSeconds(int keepAliveSeconds) {
			this.keepAliveSeconds = keepAliveSeconds;
		}

		public int getQueueCapacity() {
			return queueCapacity;
		}

		public void setQueueCapacity(int queueCapacity) {
			this.queueCapacity = queueCapacity;
		}
		
	}
}
