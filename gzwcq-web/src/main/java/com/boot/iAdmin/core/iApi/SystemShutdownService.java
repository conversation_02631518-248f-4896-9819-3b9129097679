package com.boot.iAdmin.core.iApi;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.SpringContextUtil;

/**
 * 系统服务-服务下线
 * */
@RestController
@RequestMapping("/iApi")
public class SystemShutdownService {
	
	private final Logger logger = LoggerFactory.getLogger(SystemShutdownService.class);
	
	@RequestMapping(path="/shutdownApp",method=RequestMethod.GET)
	public ResponseEnvelope shutdownApp() {
		ResponseEnvelope re = new ResponseEnvelope();
		try {
			new Thread(new ContextCloseThread()).start();// 其实在这里可以编写自定义业务逻辑(比如校验用户名、密码等),校验成功后才执行close()方法
			re.setMessage("The Application shut down successful!");
		} catch (Exception e) {
			logger.error("The Application shut down failed：", e);
			re.setSuccess(false);
			re.setMessage("The Application shut down failed：" + e.getMessage());
		}
		return re;
	}

	private class ContextCloseThread implements Runnable {

		@Override
		public void run() {
			try {
				Thread.sleep(3000); // 休眠3秒，保证应用有充足的时间相应前端的HTTP关闭命令
				((ConfigurableApplicationContext)SpringContextUtil.getApplicationContext()).close();// 执行应用关闭操作
			} catch (Exception e) {
				logger.error(ExceptionUtils.getStackTrace(e));
			} finally {

			}
		}

	}
}
