#开发环境
com:
  environment:
    namespace: cloud #项目命名空间
    profile: dev #环境
    db-master-url: ***************************************************************************************************
    db-master-name: root #主数据库用户
    db-master-password: zjhc@123 #主数据库密码
    redis:
      host: 127.0.0.1
      port: 6379
    sessionStoreKey: ${spring.application.name}_${com.environment.profile} #session中存储的key
    redis_metadata_key: redis_metadata_key_${spring.environment.namespace}_${com.environment.profile} #redis中缓存KEY值
    cache-name: ${spring.environment.namespace}:cache:${com.environment.profile}