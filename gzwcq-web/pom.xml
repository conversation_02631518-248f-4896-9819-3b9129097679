<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zjhc.gzw</groupId>
		<artifactId>gzwcq-parent</artifactId>
		<version>1.0.0</version>
	</parent>
	<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
	<artifactId>gzwcq-web</artifactId>

	<dependencies>

		<!--Nacos服务注册/发现依赖 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--Nacos配置中心 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		
		<!-- 链路追踪 -->
		<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
     	</dependency>
     	
     	<!-- <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
      	</dependency> -->

		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-access</groupId>
			<artifactId>gzwcq-access-web</artifactId>
			<version>${wfw.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
		</dependency>
		<!-- servlet 依赖. -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
		</dependency>

		<!-- 监控 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>

		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-httpclient</artifactId>
		</dependency>

		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-bus</groupId>
			<artifactId>gzwcq-bus-client</artifactId>
			<version>${wfw.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
        <dependency>
            <groupId>com.zjhc.gzw.gzwcq-parent</groupId>
            <artifactId>gzwcq-ftp-starter</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
            <exclusions>
            	<exclusion>
            		<groupId>com.alibaba</groupId>
            		<artifactId>fastjson</artifactId>
            	</exclusion>
            </exclusions>
        </dependency>
		<!--获取pdf页数-->
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>1.8.11</version>
		</dependency>
		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-report</groupId>
			<artifactId>gzwcq-report-client</artifactId>
			<version>${wfw.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<!-- mvn clean install -->
		<finalName>${artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>1.4.2.RELEASE</version><!--$NO-MVN-MAN-VER$ -->
				<configuration>
					<mainClass>com.boot.iAdmin.core.Application</mainClass><!-- 指定SpringBoot的main入口 -->
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<!-- 打包时将jsp文件拷贝到META-INF目录下 -->
			<resource>
				<!-- 指定resources插件处理哪个目录下的资源文件 -->
				<directory>src/main/webapp</directory>
				<!--注意此次必须要放在此目录下才能被访问到 -->
				<targetPath>META-INF/resources</targetPath>
				<includes>
					<include>**/**</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/**</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<!--<resource>
			<directory>src/main/java</directory>&lt;!&ndash;所在的目录&ndash;&gt;
			<includes>&lt;!&ndash;包括目录下的.properties,.xml文件都会扫描到&ndash;&gt;
			<include>**/*.properties</include>
			<include>**/*.xml</include>
			</includes>
			<filtering>false</filtering>
			</resource>-->
		</resources>
	</build>
</project>