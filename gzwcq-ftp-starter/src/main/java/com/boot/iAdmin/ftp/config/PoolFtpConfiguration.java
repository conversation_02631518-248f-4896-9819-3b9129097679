package com.boot.iAdmin.ftp.config;

import javax.annotation.PreDestroy;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.boot.iAdmin.ftp.util.FtpPoolHelper;

/**
 * FTP连接池配置类
 * 
 * <AUTHOR>
 * @date 2018年10月16日 09:40:02
 */
@Configuration
@ConditionalOnClass({ GenericObjectPool.class, FTPClient.class })
@ConditionalOnProperty(value = "com.ftp.enabled", havingValue = "true")
@EnableConfigurationProperties(PoolFtpConfiguration.FtpConfigProperties.class)
public class PoolFtpConfiguration {

	static Log log = LogFactory.getLog(PoolFtpConfiguration.class);

	private ObjectPool<FTPClient> pool;

	public PoolFtpConfiguration(FtpConfigProperties props) {
		// 默认最大连接数与最大空闲连接数都为8，最小空闲连接数为0
		// 其他未设置属性使用默认值，可根据需要添加相关配置
		GenericObjectPoolConfig<FTPClient> poolConfig = new GenericObjectPoolConfig<FTPClient>();
		poolConfig.setTestOnBorrow(true);//借出验证
		poolConfig.setTestOnReturn(false);//返还验证
		poolConfig.setTestWhileIdle(true);//空闲验证（逐出扫描）,验证间隔取决于timeBetweenEvictionRunsMillis
		poolConfig.setMinEvictableIdleTimeMillis(props.getMinEvictableIdleTimeMillis());// 逐出连接的最小空闲时间 默认1800000毫秒(30分钟)
		// 对象空闲多久后逐出, 当空闲时间>该值 且 空闲连接>最大空闲数 时直接逐出,不再根据MinEvictableIdleTimeMillis判断
		// (默认逐出策略)
		poolConfig.setSoftMinEvictableIdleTimeMillis(props.getSoftMinEvictableIdleTimeMillis());
		// 逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
		poolConfig.setTimeBetweenEvictionRunsMillis(props.getTimeBetweenEvictionRunsMillis());
		poolConfig.setMinIdle(props.getInitialSize());// 最小空闲连接
		poolConfig.setMaxIdle(props.getMaxIdle());// 最大空闲连接
		poolConfig.setMaxTotal(props.getMaxTotal());// 最大连接数
		pool = new GenericObjectPool<>(new FtpClientPooledObjectFactory(props), poolConfig);
		preLoadingFtpClient(props.getInitialSize(), props.getMaxIdle());
		// 初始化ftp工具类中的ftpClientPool
		FtpPoolHelper.init(pool, props.getEncoding());
		log.info("===================初始化FTP连接池成功===============");
	}

	/**
	 * 预先加载FTPClient连接到对象池中
	 * 
	 * @param initialSize
	 *            初始化连接数
	 * @param maxIdle
	 *            最大空闲连接数
	 */
	private void preLoadingFtpClient(Integer initialSize, int maxIdle) {
		if (initialSize == null || initialSize <= 0) {
			return;
		}

		int size = Math.min(initialSize.intValue(), maxIdle);
		for (int i = 0; i < size; i++) {
			try {
				pool.addObject();
			} catch (Exception e) {
				log.error("preLoadingFtpClient error...", e);
			}
		}
	}

	@PreDestroy
	public void destroy() {
		if (pool != null) {
			pool.close();
			log.info("销毁ftpClientPool...");
		}
	}

	/**
	 * Ftp配置属性类，建立ftpClient时使用
	 */
	@ConfigurationProperties(prefix = "com.ftp")
	static final class FtpConfigProperties {

		private String host = "localhost";

		private int port = FTPClient.DEFAULT_PORT;

		private String username;

		private String password;

		private int bufferSize = 8096;

		private String encoding;

		private Integer maxIdle = GenericObjectPoolConfig.DEFAULT_MAX_IDLE;

		private Integer maxTotal = GenericObjectPoolConfig.DEFAULT_MAX_TOTAL;

		private int transferFileType = FTPClient.BINARY_FILE_TYPE;// 文件格式

		private String connection_mode = "PORT";// 连接类型 #默认主动模式

		private String workingDirectory = "/";// 工作目录

		// 逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
		private Long timeBetweenEvictionRunsMillis = GenericObjectPoolConfig.DEFAULT_TIME_BETWEEN_EVICTION_RUNS_MILLIS;

		// 对象空闲多久后逐出, 当空闲时间>该值 且 空闲连接>最大空闲数 时直接逐出,不再根据MinEvictableIdleTimeMillis判断
		// (默认逐出策略)
		private Long softMinEvictableIdleTimeMillis = GenericObjectPoolConfig.DEFAULT_SOFT_MIN_EVICTABLE_IDLE_TIME_MILLIS;

		// 逐出连接的最小空闲时间 默认1800000毫秒(30分钟)
		private Long minEvictableIdleTimeMillis = GenericObjectPoolConfig.DEFAULT_MIN_EVICTABLE_IDLE_TIME_MILLIS;

		/**
		 * 初始化连接数
		 */
		private Integer initialSize = GenericObjectPoolConfig.DEFAULT_MIN_IDLE;

		public String getHost() {
			return host;
		}

		public void setHost(String host) {
			this.host = host;
		}

		public int getPort() {
			return port;
		}

		public void setPort(int port) {
			this.port = port;
		}

		public String getUsername() {
			return username;
		}

		public void setUsername(String username) {
			this.username = username;
		}

		public String getPassword() {
			return password;
		}

		public void setPassword(String password) {
			this.password = password;
		}

		public int getBufferSize() {
			return bufferSize;
		}

		public void setBufferSize(int bufferSize) {
			this.bufferSize = bufferSize;
		}

		public Integer getInitialSize() {
			return initialSize;
		}

		public void setInitialSize(Integer initialSize) {
			this.initialSize = initialSize;
		}

		public String getEncoding() {
			return encoding;
		}

		public void setEncoding(String encoding) {
			this.encoding = encoding;
		}

		public Integer getMaxIdle() {
			return maxIdle;
		}

		public void setMaxIdle(Integer maxIdle) {
			this.maxIdle = maxIdle;
		}

		public String getConnection_mode() {
			return connection_mode;
		}

		public void setConnection_mode(String connection_mode) {
			this.connection_mode = connection_mode;
		}

		public Integer getMaxTotal() {
			return maxTotal;
		}

		public void setMaxTotal(Integer maxTotal) {
			this.maxTotal = maxTotal;
		}

		public Long getTimeBetweenEvictionRunsMillis() {
			return timeBetweenEvictionRunsMillis;
		}

		public void setTimeBetweenEvictionRunsMillis(Long timeBetweenEvictionRunsMillis) {
			this.timeBetweenEvictionRunsMillis = timeBetweenEvictionRunsMillis;
		}

		public Long getSoftMinEvictableIdleTimeMillis() {
			return softMinEvictableIdleTimeMillis;
		}

		public void setSoftMinEvictableIdleTimeMillis(Long softMinEvictableIdleTimeMillis) {
			this.softMinEvictableIdleTimeMillis = softMinEvictableIdleTimeMillis;
		}

		public Long getMinEvictableIdleTimeMillis() {
			return minEvictableIdleTimeMillis;
		}

		public void setMinEvictableIdleTimeMillis(Long minEvictableIdleTimeMillis) {
			this.minEvictableIdleTimeMillis = minEvictableIdleTimeMillis;
		}

		public int getTransferFileType() {
			return transferFileType;
		}

		public void setTransferFileType(int transferFileType) {
			this.transferFileType = transferFileType;
		}

		public String getWorkingDirectory() {
			return workingDirectory;
		}

		public void setWorkingDirectory(String workingDirectory) {
			this.workingDirectory = workingDirectory;
		}

	}

	/**
	 * FtpClient对象工厂类
	 */
	static class FtpClientPooledObjectFactory extends BasePooledObjectFactory<FTPClient> {

		private Log log = LogFactory.getLog(FtpClientPooledObjectFactory.class);

		private FtpConfigProperties props;

		public FtpClientPooledObjectFactory(FtpConfigProperties props) {
			this.props = props;
		}

		@Override
		public FTPClient create() throws Exception {
			FTPClient ftpClient = new FTPClient();
			try {
				ftpClient.connect(props.getHost(), props.getPort());
				int reply = ftpClient.getReplyCode();
				if (!FTPReply.isPositiveCompletion(reply)) {
					ftpClient.disconnect();
					log.error("FTPServer 拒绝连接");
					return null;
				}
				boolean result = ftpClient.login(props.getUsername(), props.getPassword());
				if (!result) {
					log.error("ftpClient登陆失败!");
					throw new Exception("ftpClient登陆失败! userName:" + props.getUsername() + " ; password:" + props.getPassword());
				}
				ftpClient.setBufferSize(props.getBufferSize());
				ftpClient.setControlEncoding(props.getEncoding());
				ftpClient.setFileType(props.getTransferFileType());
				if (StringUtils.equalsIgnoreCase(props.getConnection_mode(), "PASV")) {// 被动模式
					log.debug("=========设置Ftp连接方式为被动模式========");
					ftpClient.enterLocalPassiveMode();
				}
				ftpClient.changeWorkingDirectory(props.getWorkingDirectory());// 切换工作目录
				log.debug("==============创建FtpClient连接成功==============");
				return ftpClient;
			} catch (Exception e) {
				log.error("建立FTP连接失败", e);
				if (ftpClient.isAvailable()) {
					ftpClient.disconnect();
				}
				ftpClient = null;
				throw new Exception("建立FTP连接失败", e);
			}
		}

		@Override
		public PooledObject<FTPClient> wrap(FTPClient ftpClient) {
			return new DefaultPooledObject<FTPClient>(ftpClient);
		}

		/**
		 * 销毁FtpClient
		 */
		@Override
		public void destroyObject(PooledObject<FTPClient> p) throws Exception {
			FTPClient ftpClient = getObject(p);
			if (ftpClient != null && ftpClient.isConnected()) {
				try {
					ftpClient.logout();// 登出
				} finally {
					ftpClient.disconnect();// 断开连接
				}
			}
			log.debug(String.format("销毁FtpClient%s成功", ftpClient));
		}

		/**
		 * 验证FtpClient是否可用
		 */
		@Override
		public boolean validateObject(PooledObject<FTPClient> p) {
			FTPClient ftpClient = getObject(p);
			if (ftpClient == null || !ftpClient.isConnected()) {
				log.warn(String.format("FTP连接%s不可用，将从连接池中移除", ftpClient));
				return false;
			}
			try {
				boolean connect = ftpClient.sendNoOp();
				if(connect){                
	                ftpClient.changeWorkingDirectory(props.getWorkingDirectory());
	            }else {
	            	log.warn(String.format("FTP连接%s不可用，将从连接池中移除", ftpClient));
	            }
				return connect;
			} catch (Exception e) {
				log.error(String.format("验证FTP连接失败::%s", ExceptionUtils.getStackTrace(e)));
				return false;
			}
		}

		private FTPClient getObject(PooledObject<FTPClient> p) {
			if (p == null || p.getObject() == null) {
				return null;
			}
			return p.getObject();
		}

	}

}
