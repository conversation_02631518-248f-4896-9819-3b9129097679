package com.boot.iAdmin.ftp.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.ObjectPool;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

/**
 * FTP工具类
 * <P>整合FTP连接池
 * <AUTHOR>
 * @time 2018年10月16日 09:40:16
 * @version 1.0.0
 * */
@SuppressWarnings("all")
public class FtpPoolHelper {
	
    /** 本地字符编码  **/
    private static String LOCALCHARSET = "UTF-8";
    
    /** FTP协议里面，规定文件名编码为iso-8859-1 **/
    private static String SERVERCHARSET = "ISO-8859-1";
    
    /** FTP基础目录 **/
    private static final String BASE_PATH = "/";
	
	static Log log = LogFactory.getLog(FtpPoolHelper.class);

    /**
     * ftpClient连接池初始化标志
     */
    private static volatile boolean hasInit = false;
    /**
     * ftpClient连接池
     */
    private static ObjectPool<FTPClient> ftpClientPool;

    /**
     * 初始化ftpClientPool
     *
     * @param ftpClientPool
     */
    public static void init(ObjectPool<FTPClient> ftpClientPool,String encoding) {
        if (!hasInit) {
            synchronized (FtpPoolHelper.class) {
                if (!hasInit) {
                    FtpPoolHelper.ftpClientPool = ftpClientPool;
                    if(StringUtils.isNotBlank(encoding)) FtpPoolHelper.LOCALCHARSET = encoding;
                    hasInit = true;
                }
            }
        }
    }
    
    
    /**
     * 本地文件上传到FTP服务器
     * 
     * @param ftpPath FTP服务器文件相对路径，例如：test/123
     * @param savePath 本地文件路径，例如：D:/test/123/test.txt
     * @param fileName 上传到FTP服务的文件名，例如：123.txt
     * @return boolean 成功返回true，否则返回false
     */
    public static boolean uploadLocalFile(String ftpPath, String savePath, String fileName) {
        // 登录
    	FTPClient ftpClient = getFtpClient();
        boolean flag = false;
        if (ftpClient != null) {
            File file = new File(savePath);
            FileInputStream fis = null;
            try {
                fis = new FileInputStream(file);
                String path = encodingPath(BASE_PATH + ftpPath);
                // 目录不存在，则递归创建
				if (!ftpClient.changeWorkingDirectory(path)) {
					createDirectorys(path,ftpClient);
				}
                // 设置被动模式，开通一个端口来传输数据
                // 上传文件           
                flag = ftpClient.storeFile(new String(fileName.getBytes(LOCALCHARSET), SERVERCHARSET), fis);
            } catch (Exception e) {
            	log.error("本地文件上传FTP失败", e);
            } finally {
            	IOUtils.closeQuietly(fis);
            	releaseFtpClient(ftpClient);
            }
        }
        return flag;
    }
    
    /**
     * copy了上面的uploadLocalFile，仅仅是将savePath换成了file
     * @param ftpPath
     * @param file
     * @param fileName
     * @return
     */
    public static boolean uploadLocalFile(String ftpPath, File file, String fileName) {
        // 登录
    	FTPClient ftpClient = getFtpClient();
        boolean flag = false;
        if (ftpClient != null) {
            FileInputStream fis = null;
            try {
                fis = new FileInputStream(file);
                String path = encodingPath(BASE_PATH + ftpPath);
                // 目录不存在，则递归创建
				if (!ftpClient.changeWorkingDirectory(path)) {
					createDirectorys(path,ftpClient);
				}
                // 设置被动模式，开通一个端口来传输数据
                // 上传文件           
                flag = ftpClient.storeFile(new String(fileName.getBytes(LOCALCHARSET), SERVERCHARSET), fis);
            } catch (Exception e) {
            	log.error("本地文件上传FTP失败", e);
            } finally {
            	releaseFtpClient(ftpClient);
                IOUtils.closeQuietly(fis);
            }
        }
        return flag;
    }
    
    /**
     * 和上面的方法类似：只不过传递的参数由文件转变成了MultipartFile
     * @param ftpPath
     * @param mFile
     * @param fileName
     * @return
     */
    public static boolean uploadLocalFile(String ftpPath, MultipartFile mFile, String fileName) {
        // 登录
    	FTPClient ftpClient = getFtpClient();
        boolean flag = false;
        if (ftpClient != null) {
        	InputStream fis = null;
            try {
            	if(mFile instanceof CommonsMultipartFile) {
            		CommonsMultipartFile cFile = (CommonsMultipartFile) mFile;
                    DiskFileItem fileItem = (DiskFileItem) cFile.getFileItem();
                    fis = fileItem.getInputStream();
            	}else {
            		fis = mFile.getInputStream();
            	}
            	if(fis == null) throw new RuntimeException("获取文件流失败");
                String path = encodingPath(BASE_PATH + ftpPath);
                // 目录不存在，则递归创建
				if (!ftpClient.changeWorkingDirectory(path)) {
					createDirectorys(path,ftpClient);
				}
                // 设置被动模式，开通一个端口来传输数据
                // 上传文件           
                flag = ftpClient.storeFile(new String(fileName.getBytes(LOCALCHARSET), SERVERCHARSET), fis);
            } catch (Exception e) {
            	log.error("文件上传FTP失败", e);
            } finally {
            	releaseFtpClient(ftpClient);
                IOUtils.closeQuietly(fis);
            }
        }
        return flag;
    }
    
    /**
	 * 在服务器上递归创建目录
	 * 
	 * @param dirPath
	 *            上传目录路径
	 * @return
	 */
	private static void createDirectorys(String dirPath,FTPClient ftpClient) {
		try {
			if (!dirPath.endsWith("/")) {
				dirPath += "/";
			}
			String directory = dirPath.substring(0, dirPath.lastIndexOf("/") + 1);
			ftpClient.makeDirectory("/");
			int start = 0;
			int end = 0;
			if (directory.startsWith("/")) {
				start = 1;
			} else {
				start = 0;
			}
			end = directory.indexOf("/", start);
			while (true) {
				String subDirectory = new String(dirPath.substring(start, end));
				if (!ftpClient.changeWorkingDirectory(subDirectory)) {
					if (ftpClient.makeDirectory(subDirectory)) {
						ftpClient.changeWorkingDirectory(subDirectory);
					} else {
						log.info("创建目录失败");
						return;
					}
				}
				start = end + 1;
				end = directory.indexOf("/", start);
				// 检查所有目录是否创建完毕
				if (end <= start) {
					break;
				}
			}
		} catch (Exception e) {
			log.error("上传目录创建失败", e);
		}
	}
	
	
	/**
	 * 复制文件
	 *
	 * @param sourceFileName 文件名
	 * @param sourceDir 文件目录
	 * @param targetDir 目标文件目录
	 * @throws IOException
	 */
	public static boolean copyFile(String sourceFileName, String sourceDir, String targetDir) throws Exception {
		sourceDir = encodingPath(BASE_PATH + sourceDir);
		targetDir = encodingPath(BASE_PATH + targetDir);
		// 获取连接
		FTPClient ftpClient = getFtpClient();
		ByteArrayInputStream in = null;
		ByteArrayOutputStream fos = new ByteArrayOutputStream();
		try {
			ftpClient.changeWorkingDirectory(sourceDir);// 变更工作路径
			ftpClient.retrieveFile(new String(sourceFileName.getBytes(LOCALCHARSET), SERVERCHARSET), fos);// 将文件读到内存中
			in = new ByteArrayInputStream(fos.toByteArray());
			if (in != null) {
				//重置
				resetDirPath(ftpClient);
				if (!ftpClient.changeWorkingDirectory(targetDir)) {
					createDirectorys(targetDir,ftpClient);
				}
				ftpClient.storeFile(new String(sourceFileName.getBytes(LOCALCHARSET), SERVERCHARSET), in);
			}
		} finally {
			IOUtils.closeQuietly(in);
			IOUtils.closeQuietly(fos);
			releaseFtpClient(ftpClient);
		}
		return true;
	}
	
	/**
	 * 复制文件并删除.
	 *
	 * @param sourceFileName 文件名
	 * @param sourceDir 文件目录
	 * @param targetDir 目标文件目录
	 * @throws IOException
	 */
	public static void copyAndDeleteFile(String sourceFileName, String sourceDir, String targetDir) throws Exception {
		sourceDir = encodingPath(BASE_PATH + sourceDir);
		targetDir = encodingPath(BASE_PATH + targetDir);
		// 获取连接
		FTPClient ftpClient = getFtpClient();
		ByteArrayInputStream in = null;
		ByteArrayOutputStream fos = new ByteArrayOutputStream();
		try {
			ftpClient.changeWorkingDirectory(sourceDir);// 变更工作路径
			ftpClient.retrieveFile(new String(sourceFileName.getBytes(LOCALCHARSET), SERVERCHARSET), fos);// 将文件读到内存中
			in = new ByteArrayInputStream(fos.toByteArray());
			if (in != null) {
				//重置
				resetDirPath(ftpClient);
				if (!ftpClient.changeWorkingDirectory(targetDir)) {
					createDirectorys(targetDir,ftpClient);
				}
				ftpClient.storeFile(new String(sourceFileName.getBytes(LOCALCHARSET), SERVERCHARSET), in);
				//删除
				ftpClient.deleteFile(sourceDir + "/" + sourceFileName);
			}
		} finally {
			IOUtils.closeQuietly(in);
			IOUtils.closeQuietly(fos);
			releaseFtpClient(ftpClient);
		}
	}
	
    
	
	/**
	 * 根据名称获取文件，以输入流返回
	 * 
	 * @param ftpPath
	 *            FTP服务器上文件相对路径，例如：test/123
	 * @param fileName
	 *            文件名，例如：test.txt
	 * @return InputStream 输入流对象
	 */
	public static InputStream getInputStreamByName(String ftpPath, String fileName) {
		FTPClient ftpClient = getFtpClient();//获取连接
//		InputStream input = null;
		ByteArrayInputStream input = null;
		ByteArrayOutputStream fos = new ByteArrayOutputStream();
		if (ftpClient != null) {
			try {
				String path = encodingPath(BASE_PATH + ftpPath);
				// 判断是否存在该目录
				if (!ftpClient.changeWorkingDirectory(path)) {
					log.error(BASE_PATH + ftpPath + "该目录不存在");
					return input;
				}
				String ftpName = new String(fileName.getBytes(LOCALCHARSET), SERVERCHARSET);
				ftpClient.retrieveFile(ftpName, fos);// 将文件读到内存中,不使用retrieveFileStream，让ftp server判断流是否结束，避免input未关闭completePendingCommand卡死
				if(fos.toByteArray().length > 0) input = new ByteArrayInputStream(fos.toByteArray());
//				input = ftpClient.retrieveFileStream(ftpName);
			} catch (IOException e) {
				log.error("获取文件失败", e);
			} finally {
				IOUtils.closeQuietly(fos);
				releaseFtpClient(ftpClient);//释放连接
			}
		}
		return input;
	}
	
	/**
	 * 文件下载
	 * @param totalFtpPath 全路径
	 * */
	public static InputStream getInputStreamByName(String totalFtpPath) {
		if(StringUtils.isNotBlank(totalFtpPath) && totalFtpPath.indexOf("/") > -1) {
			return getInputStreamByName(totalFtpPath.substring(0, totalFtpPath.lastIndexOf("/")),
					totalFtpPath.substring(totalFtpPath.lastIndexOf("/") + 1 ,totalFtpPath.length()));
		}else {
			return null;
		}
	}
	
	/**
	 * 删除指定文件
	 * 
	 * @param filePath
	 *            文件相对路径，例如：test/123/test.txt
	 * @return 成功返回true，否则返回false
	 */
	public static boolean deleteFile(String filePath) {
		FTPClient ftpClient = getFtpClient();//获取连接
		boolean flag = false;
		if (ftpClient != null) {
			try {
				String path = encodingPath(BASE_PATH + filePath);
				flag = ftpClient.deleteFile(path);
			} catch (IOException e) {
				log.error("删除文件失败", e);
			} finally {
				releaseFtpClient(ftpClient);//释放连接
			}
		}
		return flag;
	}
	
	/**
	 * 删除目录下所有文件
	 * 
	 * @param dirPath
	 *            文件相对路径，例如：test/123
	 * @return 成功返回true，否则返回false
	 */
	public static boolean deleteFiles(String dirPath) {
		// 登录
		FTPClient ftpClient = getFtpClient();//获取连接
		boolean flag = false;
		if (ftpClient != null) {
			try {
				ftpClient.enterLocalPassiveMode(); // 设置被动模式，开通一个端口来传输数据
				String path = encodingPath(BASE_PATH + dirPath);
				String[] fs = ftpClient.listNames(path);
				// 判断该目录下是否有文件
				if (fs == null || fs.length == 0) {
					log.error(BASE_PATH + dirPath + "该目录下没有文件");
					return flag;
				}
				for (String ftpFile : fs) {
					ftpClient.deleteFile(ftpFile);
				}
				flag = true;
			} catch (IOException e) {
				log.error("删除文件失败", e);
			} finally {
				releaseFtpClient(ftpClient);//释放连接
			}
		}
		return flag;
	}
	
	
    /**
     * 编码文件路径
     */
    private static String encodingPath(String path) throws UnsupportedEncodingException {
        // FTP协议里面，规定文件名编码为iso-8859-1，所以目录名或文件名需要转码
        return new String(path.replaceAll("//", "/").getBytes(LOCALCHARSET), SERVERCHARSET);
    }

    /**
     * 获取ftpClient
     *
     * @return
     */
    private static FTPClient getFtpClient() {
        checkFtpClientPoolAvailable();
        FTPClient ftpClient = null;
        Exception ex = null;
        // 获取连接最多尝试3次
        for (int i = 0; i < 3; i++) {
            try {
            	log.info(String.format("FtpClient连接池中当前可用连接为%s,借出连接为%s", ftpClientPool.getNumIdle(),ftpClientPool.getNumActive()));
                ftpClient = ftpClientPool.borrowObject();
//                resetDirPath(ftpClient);
                break;
            } catch (Exception e) {
                ex = e;
            }
        }
        if (ftpClient == null) {
            throw new RuntimeException("Could not get a ftpClient from the pool", ex);
        }
        return ftpClient;
    }

    /**
     * 释放ftpClient
     */
    private static void releaseFtpClient(FTPClient ftpClient) {
        if (ftpClient == null) {
            return;
        }

        try {
            ftpClientPool.returnObject(ftpClient);
        } catch (Exception e) {
            log.error("Could not return the ftpClient to the pool", e);
            // destoryFtpClient
            if (ftpClient.isAvailable()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException io) {
                }
            }
        }
    }

    /**
     * 检查ftpClientPool是否可用
     */
    private static void checkFtpClientPoolAvailable() {
        Assert.state(hasInit, "FTP未启用或连接失败！");
    }
    
    /**
     * 重置路径
     * */
    private static void resetDirPath(FTPClient ftpClient) throws IOException{
    	ftpClient.changeWorkingDirectory(BASE_PATH);
    }
    
    /**
     * 获取文件后必须调用该方法，否则当前连接无法再次获取文件
     * */
    private static void completePendingCommand(FTPClient ftpClient) {
    	try {
			ftpClient.completePendingCommand();
			log.debug("====================流结束成功==================");
		} catch (IOException e) {
			e.printStackTrace();
		}
    }

}
