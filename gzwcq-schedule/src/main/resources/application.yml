server:
  port: 8807
  servlet:
    context-path: /gzwcq-schedule
    session:
      timeout: 1800 # Session timeout. If a duration suffix is not specified, seconds will be used

spring:
  profiles:
    active:
     - pst #环境，dev/pst/prod
    include:
     - mysql #数据库
     - ftp
  application:
    name: server-schedule
  security:
    filter:
      dispatcher-types: request #设置只拦截request类型请求
  jackson:
    time-zone: GMT+8 #时区
    serialization:
      write-dates-as-timestamps: true #springboot 2.0以上版本默认返回string,修改转为long
  session:
    timeout: 3600
mybatis:
  configuration:
    map-underscore-to-camel-case: false #驼峰自动转换 user_name ==> userName
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #日志,只会在控制台输出
  mapper-locations: 
    - classpath*:com/**/mapper/*.xml

#加载所有的端点/默认只加载了 info / health
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"

#日志配置 logback/log4j2
logging:
  file:
    name: logs/${spring.application.name}.log #日志输出路径
    max-size: 100MB
    max-history: 10
  level:
    com.zjh: INFO #业务日志输出
  pattern:
    dateformat: yyyy-MM-dd HH:mm:ss.SSS #时间格式
    console: "%d{yyyy-MM-dd HH:mm:ss.S}:%p:%c%n[MESSAGE]%m%n%n" #控制台输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss.S}:%p:%c%n[MESSAGE]%m%n%n" #日志文件中输出格式
    
    
    