#开发环境
com:
  environment:
    profile: dev #环境
    db-master-url: *****************************************************************************************************
    db-master-name: root
    db-master-password: zjhc@123
    ftp-host: ************** #文件服务器地址
    ftp-user: hhycrm #FTP用户
    ftp-password: zjhc@123 #FTP密码
    ftp-connection_mode: PASV
  api:
    config:
      username: R1pXQ1FESjA=
      password: yCe7+YpUuEXJgC2uj91rPw==
      loginUrl: /login
      prefixUrl: http://*************:5050
      projectUrl: /prjs/cjdeal
      projectTransfereeUrl: /prjs/projectYXFInfo
    push:
      config:
        ip: https://opweb.sasac.gov.cn
        updateAllDfqyUrl: ${com.api.push.config.ip}/cqglapitest/platform/province/updateAllDfqy
        getDfqyUploadStatusUrl: ${com.api.push.config.ip}/cqglapitest/platform/province/getDfqyUploadStatus
        getGsInfoUrl: ${com.api.push.config.ip}/cqglapitest/platform/province/getGsInfo
        appId: 878ca94a37b5e64b35
        appSecret: 6b9e251e6a2842388a904811ccb21fe8
GjPush:
  config:
    ip: **********:18082
    authServerUrl: http://${GjPush.config.ip}/api/dmp/oauth/client/token #请求地址
    appId: tkd8ew84hzzczwrg # appId
    appSecret: tvmpaq1noml7zpakyl4wdr79pivbuu31ju93tdib8ljla6qp # appSecret
    resourceCode: 1BASE0120250217194514275920 #数据资源编号
    pushServerUrl: http://${GjPush.config.ip}/api/optChangeReceiver/crypto
xxl:
  job:
    admin:
      addresses: http://:/xxl-job-admin #xxljob管理中心地址
    accessToken:
    executor:
      appname: gzwcq-schedule
      ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      address: 
      ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip: 
      port: 18808
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 366
