#ftp config 本地
com:
  ftp:
    enabled: true
    host: ${com.environment.ftp-host} #IP
    port: 21
    username: ${com.environment.ftp-user}
    password: ${com.environment.ftp-password}
    encoding: GBK #编码 根据ftp server编码，如果server支持UTF-8则使用UTF-8
    initialSize: 5 #初始化连接数
    maxIdle: 50 #最大空闲对象
    maxTotal: 100 #最大连接数
    #空闲验证，逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
    timeBetweenEvictionRunsMillis: 60000
    #对象空闲多久后逐出, 当空闲时间>该值 且 空闲连接>最大空闲数 时直接逐出,不再根据MinEvictableIdleTimeMillis判断  (默认逐出策略)
    softMinEvictableIdleTimeMillis: 300000
    #逐出连接的最小空闲时间 默认1800000毫秒(30分钟)0000
    minEvictableIdleTimeMillis: 600000
    connection_mode: ${com.environment.ftp-connection_mode} #PASV 被动  /PORT 主动 （默认）被动模式服务端需要随机大于1024的端口
    #0=ASCII_FILE_TYPE（ASCII格式） 1=EBCDIC_FILE_TYPE 2=LOCAL_FILE_TYPE（二进制文件）  
    transferFileType: 2
    workingDirectory: / #工作目录
    