com:
  environment:
    profile: prod #环境
    db-master-url: ****************************************************************************************************
    db-master-name: root
    db-master-password: hc@135246
    ftp-host: *************  #文件服务器地址
    ftp-user: gzwftp #FTP用户
    ftp-password: Gzw@135246  #FTP密码
    ftp-connection_mode: PASV
  ftp:
    encoding: UTF-8
  api:
    config:
      username: R1pXQ1FESlhU
      password: kCVvkNZxl8KSOSnijHlvkw==
      loginUrl: /login
      prefixUrl: http://*************:5052
      projectUrl: /prjs/cjdeal
      projectTransfereeUrl: /prjs/projectYXFInfo
    push:
      config:
        ip: https://opweb.sasac.gov.cn/
        updateAllDfqyUrl: ${com.api.push.config.ip}/cqglapi/platform/province/updateAllDfqy
        getDfqyUploadStatusUrl: ${com.api.push.config.ip}/cqglapi/platform/province/getDfqyUploadStatus
        getGsInfoUrl: ${com.api.push.config.ip}/cqglapi/platform/province/getGsInfo
        appId: 51b56da96042deda83
        appSecret: 22b438ff42c64be39cebf9afe8e254a9
        filePath: /data/gzwPush/
xxl:
  job:
    admin:
      addresses: http://*************:9998/xxl-job-admin #xxljob管理中心地址
    accessToken: ''
    executor:
      appname: gzwcq-schedule
      ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题
      address: ''
      ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"
      ip: ''
      port: 8808
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 60
GjPush:
  config:
    ip: ***********:18081
    authServerUrl: http://${GjPush.config.ip}/api/dmp/oauth/client/token #请求地址
    appId: i4c5yhrd181qkq39 # appId
    appSecret: o0ujmj6eld7dkk52rafth4iku6u3d9cy1ik0z6mtjsv2zev1 # appSecret
    resourceCode: 1BASE0120250218103419538041 #数据资源编号
    pushServerUrl: http://${GjPush.config.ip}/api/optChangeReceiver/crypto