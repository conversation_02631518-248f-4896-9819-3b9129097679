package com.zjhc.gzwcq.job.zjh.entity.gsInfoInvestors.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR> <br/>
 *         表名： scjd_gs_info_investors <br/>
 *         描述：GsInfoInvestors查询类 <br/>
 */
@ApiModel(value="GsInfoInvestors对象",description="gsInfoInvestors")
public class GsInfoInvestorsParam extends GsInfoInvestors{

	private static final long serialVersionUID = 18L;
  	
  	@ApiParam(value="查询页（和偏移量二选一）")
	private int pageNumber;
	
  	@ApiParam(value="每页数量")
	private int limit;
	
  	@ApiParam(value="当前偏移量（和查询页二选一）")
	private int offest;

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public int getOffest() {
		return offest;
	}

	public void setOffest(int offest) {
		this.offest = offest;
	}
}
