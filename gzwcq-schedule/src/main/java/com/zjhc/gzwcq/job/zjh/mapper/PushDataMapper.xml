<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.PushDataMapper">

    <select id="selectOrgId" resultType="string">
        SELECT
            DISTINCT
            o.ORGANIZATION_ID
        FROM
            (
                SELECT
                    ds.ORGANIZATION_ID,
                    if(ds.`code` is not null or ds.`code` != '',
                       ds.`code` ,ds.ORGANIZATION_ID) as `code`,
                    if(ds.`code` is not null or ds.`code` != '',
                       SUBSTRING_INDEX(ds.`code`,'_',-1) ,ds.ORGANIZATION_ID) as `_code`,
                    jb.JB_RELA,
                    TRIM(ds.ORGANIZATION_NAME) as ORGANIZATION_NAME
                FROM
                    (
                        SELECT o.ORGANIZATION_ID,
                               SUBSTRING_INDEX(o.ORGANIZATION_CODE ,'_',-1) as `code`,
                               o.ORGANIZATION_NAME
                        FROM sys_organization o join (
                            select
                                jb.UNITID,
                                jb.JB_DATA_STATUS,
                                jb.BUSINESS_NATURE,
                                jb.JB_RELA,
                                jb.JB_ZXCQDJQX
                            from (
                                     select
                                         jb.UNITID,
                                         jb.JB_DATA_STATUS,
                                         jb.BUSINESS_NATURE,
                                         jb.JB_RELA,
                                         jb.JB_ZXCQDJQX
                                     from cq_jbxxb jb  JOIN
                                          `rg_business_info` rbi on rbi.JBXX_ID = jb.ID
                                     WHERE
                                         rbi.RG_UNITSTATE = '2'
                                       AND
                                         jb.JB_DELETED = 'N'
                                       AND
                                         jb.JB_SHZT = '4'
                                     ORDER BY
                                         rbi.RG_TIMEMARK desc
                                     LIMIT 100000000000
                                 )jb
                            group by
                                jb.UNITID
                        ) b on b.UNITID = o.ORGANIZATION_ID
                        where 1=1 and b.JB_DATA_STATUS = 'new' and b.BUSINESS_NATURE = 1
                          and (b.JB_ZXCQDJQX not in('30400','30330','30300','30310','30320') or o.isdeleted = 'N')
                          AND o.ORGANIZATION_ID not IN(
                            SELECT val FROM `sys_dictionary` dic WHERE dic.type_id  = ( SELECT id FROM sys_dictionary dic WHERE dic.type_code = 'BTS')
                        )
                          and o.PARENTS LIKE '%39DC82B5A000000125E54F37FE103416%'
                        group by
                            o.ORGANIZATION_ID
                        union
                        SELECT jb.UNITID as ORGANIZATION_ID ,jb.jb_zzjgdm as `code`,
                               jb.jb_qymc as ORGANIZATION_NAME
                        FROM `rg_business_info` info join cq_jbxxb jb on jb.id = info.JBXX_ID
                        WHERE info.RG_UNITSTATE = '9'
                          AND
                            (
                                        jb.JB_DELETED = 'N'  or  jb.UNITID in(
                                    SELECT val FROM `sys_dictionary` dic WHERE dic.type_id  = ( SELECT id FROM sys_dictionary dic WHERE dic.type_code = 'CG_GSDJ_TS')
                                ))

                          and jb.BUSINESS_NATURE = 1
                        union
                        select ORGANIZATION_ID , SUBSTRING_INDEX(o.ORGANIZATION_CODE ,'_',-1) as `code`,
                               ORGANIZATION_NAME
                        from sys_organization o  where
                                o.ORGANIZATION_ID in (
                                SELECT val FROM `sys_dictionary` dic WHERE dic.type_id  = ( SELECT id FROM sys_dictionary dic WHERE dic.type_code = 'CG_TS')

                            )

                    )ds
                        JOIN cq_jbxxb jb on jb.UNITID = ds.ORGANIZATION_ID
                WHERE

                        ds.ORGANIZATION_ID not IN(
                        SELECT val FROM `sys_dictionary` dic WHERE dic.type_id  = ( SELECT id FROM sys_dictionary dic WHERE dic.type_code = 'BTS')
                        UNION
                        SELECT o.ORGANIZATION_ID FROM `sys_organization` o
                        WHERE
                            FIND_IN_SET('46EFE2F820000001A766F9F4682A1F1E',PARENTS)
                        UNION
                        SELECT o.ORGANIZATION_ID FROM `sys_organization` o
                        WHERE
                            FIND_IN_SET('46EFE2F860000081431E493EF1348CF8',PARENTS)
                        UNION
                        SELECT o.ORGANIZATION_ID FROM `sys_organization` o
                        WHERE
                            FIND_IN_SET('zsgq',PARENTS)
                    )
                  AND jb.JB_CZRZZJGID not IN(
                    SELECT o.ORGANIZATION_ID FROM `sys_organization` o
                    WHERE
                        FIND_IN_SET('zsgq',PARENTS)
                )
                  AND jb.JB_RELA is not null
                ORDER BY
                    jb.JB_RELA asc
                LIMIT 10000000000000000
            )o
        GROUP BY
            o.`_code`,o.ORGANIZATION_NAME
    </select>

    <select id="selectPushData" resultType="com.zjhc.gzwcq.job.zjh.entity.Enterprise">
        SELECT
            cqjb.UNITID as id,
            cqjb.JB_ZZJGDM as xybm,
            cqjb.JB_QYMC as qymc,
            cqjb.JB_GJCZQY as gjczqy,
            cqjb.JB_QYLB as qylb,
            cqjb.JB_RELA as gjczqygx,
            case
                when cqjb.JB_SFZY = 2 then 0
                else
                    cqjb.JB_SFZY
                end as sfzy,
            cqjb.jb_czrzzjgdm as zyczr,
            cqjb.JB_ZCZB as zczbrmb,
            dic.text  as BWBMC,
            IFNULL(cqjb.JB_RJZB,cqjb.JB_ZCZBJW) as ZCZBBWB,
            cqjb.JB_ZCRQ as zcrq,
            cqjb.jb_qzwjkjglx as gzjgjglx,
            dic2.text as gzjgjgmx,
            cqjb.jb_ZYHY1 as zyhy1,
            cqjb.jb_ZYHY1 as zyhy2,
            cqjb.jb_ZYHY1 as zyhy3,
            cqjb.JB_ZZXS as zzxs,
            cqjb.JB_QYJC as cqjc,
            cqjb.jb_qygljc as gljc,
            cqjb.JB_JNJW as jnjwbs,
            cqjb.JB_ZCD as zcdjn,
            cqjb.JB_ZCDJW as zcdjw,
            cqjb.jb_yyzzzc as zhusuo,
            cqjb.jb_sf_bb as sfbb,
            cqjb.jb_sf_ss as sfssgs,
            cqjb.jb_sf_czblzt as sfxmty,
            cqjb.jb_sf_tgqy as sfdgtg,
            cqjb.jb_sf_kzgs as sfkgs,
            if(cqjb.JB_SFTSMDGS= 2,0,cqjb.JB_SFTSMDGS) as sftsmd,
            cqjb.JB_ZCMD as zcmd,
            cqjb.JB_SFCZGRDCG as sfgrdcg,
            case
                when (cqjb.JB_ZXCQDJQX is not null and  cqjb.JB_ZXCQDJQX != '') then 4
                when cqjb.jb_qyslzt = '产权工商均已设立登记'  then 3
                when cqjb.jb_qyslzt = '产权已设立登记，工商未设立登记'  then 1
                end  as qydjzt
        FROM
            cq_jbxxb cqjb
                JOIN
            (
                SELECT
                    *
                FROM
                    (
                        SELECT
                            cjb.ID,
                            cjb.UNITID
                        FROM
                            `cq_jbxxb`  cjb
                                JOIN
                            `rg_business_info` rbi on rbi.JBXX_ID = cjb.ID
                        WHERE
                            cjb.UNITID = #{orgId}
                          AND
                            (rbi.RG_UNITSTATE = '2' or rbi.RG_UNITSTATE='9')
                          AND
                            cjb.JB_DELETED = 'N'
                          AND
                            (cjb.JB_SHZT = '4' or cjb.JB_SHZT = '9')
                        ORDER BY
                            rbi.RG_TIMEMARK desc
                        LIMIT 1000000000
                    )cjb
                GROUP BY
                    cjb.UNITID
            ) as iscjbb
            ON
                iscjbb.ID = cqjb.ID
                left join sys_organization o on o.ORGANIZATION_ID = cqjb.jb_czrzzjgid
                left JOIN (SELECT * FROM sys_dictionary d WHERE d.type_id IN
                                                                (SELECT id FROM sys_dictionary dic WHERE dic.type_code = 'BZXZ'))as dic on dic.val =   IF(cqjb.JB_RJZBBZ is null or cqjb.JB_RJZBBZ = '',IF(cqjb.jb_Zczbbz is not null and cqjb.jb_Zczbbz != '' ,cqjb.jb_Zczbbz,null),cqjb.JB_RJZBBZ)
                left JOIN (SELECT * FROM sys_dictionary d WHERE d.type_id IN
                                                                (SELECT id FROM sys_dictionary dic WHERE dic.type_code = 'GZJGJG'))as dic2 on dic2.val = cqjb.jb_gzwjkjgmx
        where
            cqjb.JB_DATA_STATUS = 'new'
          and cqjb.BUSINESS_NATURE = 1
        GROUP BY
            cqjb.UNITID
    </select>
    <select id="selectDefaultData" resultType="com.zjhc.gzwcq.job.zjh.entity.Enterprise">
      <!--  SELECT
            jb.id,
            jb.qymc,
            jb.xybm,
            jb.qylb,
            jb.qydjzt,
            jb.zyczr
        FROM
            (
                SELECT
                    o.ORGANIZATION_ID as ID,
                    o.ORGANIZATION_NAME as qymc,
                    SUBSTRING_INDEX(o.ORGANIZATION_CODE, '_', -1) as xybm,
                    5 as qylb,
                    3 as qydjzt,
                    IF(o.PARENT_ID = '39DC82B5A000000125E54F37FE103416','3300',SUBSTRING_INDEX(os.ORGANIZATION_CODE, '_', -1))  as zyczr,
                    o.`order`
                FROM
                    (
                        SELECT o.ORGANIZATION_NAME , o.ORGANIZATION_CODE,o.PARENT_ID ,o.ORGANIZATION_ID ,	2 as `order` FROM `gzwcq`.`sys_organization` o WHERE `PARENT_ID` = '39DC82B5A000000125E54F37FE103416'  AND o.isdeleted = 'N' AND
                            o.PARENT_ID  != '39DC82B5A000000125E54F37FE103416'
                                                                                                                                                              AND
                            o.ORGANIZATION_ID  not IN('53129A8420000021177AD54D2465FF2F')
                        UNION
                        SELECT o.ORGANIZATION_NAME , o.ORGANIZATION_CODE ,o.PARENT_ID ,o.ORGANIZATION_ID, 3 as `order` FROM `gzwcq`.`sys_organization` o  WHERE BUSINESSTYPE ='1'  AND o.isdeleted = 'N' and o.ORGANIZATION_ID not IN (
                                                                                                                                                                                                                                       '39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F','3B123053C0000001B43B17219C8B258D'
                            )
                    ) o
                        LEFT JOIN sys_organization os on os.ORGANIZATION_ID = o.PARENT_ID
                UNION
                SELECT
                    o.ORGANIZATION_ID as ID,
                    o.ORGANIZATION_NAME as qymc,
                    IF(o.ORGANIZATION_NAME = '浙江省','3300',   SUBSTRING_INDEX(o.ORGANIZATION_CODE, '_', -1) ) as xybm,
                    6 as qylb,
                    3 as qydjzt,
                    IF(o.ORGANIZATION_NAME = '浙江省',NULL,'3300')  as zyczr,
                    IF(o.ORGANIZATION_NAME = '浙江省',1,2)    as `order`
                FROM `sys_organization` o WHERE o.`ORGANIZATION_ID` =  '39DC82B5A000000125E54F37FE103416'
                                             OR 	o.PARENT_ID  = '39DC82B5A000000125E54F37FE103416'
                                             OR
                        o.ORGANIZATION_ID IN('53129A8420000021177AD54D2465FF2F','3B123053C0000001B43B17219C8B258D')
            ) jb
        ORDER BY
            jb.`order` asc -->
        SELECT
        jb.id,
        jb.qymc,
        jb.xybm,
        jb.qylb,
        jb.qydjzt,
        jb.zyczr
        FROM
        (
        SELECT
        o.ORGANIZATION_ID as ID,
        o.ORGANIZATION_NAME as qymc,
        SUBSTRING_INDEX(o.ORGANIZATION_CODE, '_', -1) as xybm,
        o.qylb as qylb,
        3 as qydjzt,
        IF(o.PARENT_ID = '39DC82B5A000000125E54F37FE103416','3300',SUBSTRING_INDEX(os.ORGANIZATION_CODE, '_', -1))  as zyczr,
        o.`order`
        FROM
        (
        SELECT o.ORGANIZATION_NAME , o.ORGANIZATION_CODE ,o.PARENT_ID ,o.ORGANIZATION_ID, 3 as `order` ,o.qylb FROM `sys_organization` o  WHERE BUSINESSTYPE ='1'  AND o.isdeleted = 'N'
        ) o
        LEFT JOIN sys_organization os on os.ORGANIZATION_ID = o.PARENT_ID
        WHERE
        o.PARENT_ID  != '39DC82B5A000000125E54F37FE103416'
        UNION
        SELECT
        o.ORGANIZATION_ID as ID,
        o.ORGANIZATION_NAME as qymc,
        IF(o.ORGANIZATION_NAME = '浙江省','3300',   SUBSTRING_INDEX(o.ORGANIZATION_CODE, '_', -1)) as xybm,
        o.qylb as qylb,
        3 as qydjzt,
        IF(o.ORGANIZATION_NAME = '浙江省',NULL,'3300')  as zyczr,
        IF(o.ORGANIZATION_NAME = '浙江省',1,2)    as `order`
        FROM `sys_organization` o WHERE o.`ORGANIZATION_ID` =  '39DC82B5A000000125E54F37FE103416'
        OR 	o.PARENT_ID  = '39DC82B5A000000125E54F37FE103416'
        ) jb
        ORDER BY
        jb.`order` asc


    </select>
    <select id="selectInvestorByJbxxbId" resultType="com.zjhc.gzwcq.job.zjh.entity.Investor">

        SELECT
            c.FD_CZRMC as czrmc,
            c.FD_CZRZZJGDM as czrbm,
            c.FD_CZRLB as czrlb,
            c.FD_RJZB as rjzbrmb,
            c.FD_RJZBBZ as rjzbbwb,
            c.FD_SJZCZBBZ as sjzbbwb,
            c.FD_SJZCJ as sjzbrmb,
            c.FD_GQBL as gqbl,
            c.UNITID as qyid
        FROM cq_czfd c
        where
            c.JBXX_ID = #{id}

    </select>

    <resultMap type="com.zjhc.gzwcq.job.zjh.entity.Hhrqkfd" id="baseResultMapbak">
        <id column="id" property="id"/>
        <result column="jbxx_id" property="jbxxId"/>
        <result column="unitid" property="unitid"/>
        <result column="name" property="name"/>
        <result column="hhr_code" property="hhrCode"/>
        <result column="type" property="type"/>
        <result column="category" property="category"/>
        <result column="fd_cze" property="fdCze"/>
        <result column="rjcze" property="rjcze"/>
        <result column="rjczbl" property="rjczbl"/>
        <result column="sjcze" property="sjcze"/>
        <result column="czfs" property="czfs"/>
        <result column="jfqx" property="jfqx"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="last_update_user" property="lastUpdateUser"/>
        <result column="last_update_time" property="lastUpdateTime"/>
    </resultMap>

    <resultMap id="baseResultMapExtbak" type="com.zjhc.gzwcq.job.zjh.entity.HhrqkfdVo" extends="baseResultMapbak">
        <result column="typeStr" property="typeStr"/>
        <result column="categoryStr" property="categoryStr"/>
        <result column="czfsStr" property="czfsStr"/>
    </resultMap>

    <select id="selectByJbxxbId" parameterType="java.lang.String" resultMap="baseResultMapExtbak">
        select
            t.id,
            t.jbxx_id,
            t.unitid,
            t.name,
            t.hhr_code,
            t.type,
            t.category,
            t.fd_cze,
            t.rjcze,
            t.rjczbl,
            t.sjcze,
            t.czfs,
            t.jfqx,
            t.create_user,
            t.create_time,
            t.last_update_user,
            t.last_update_time,sd1.text typeStr,sd2.text categoryStr,
        sd3.text czfsStr
        from
        cq_hhrqkfd t
        left join sys_dictionary sd1 on sd1.val=t.type and sd1.type_id=(select id from sys_dictionary where type_code='HHRLX')
        left join sys_dictionary sd2 on sd2.val=t.category and sd2.type_id=(select id from sys_dictionary where type_code='HHRLB')
        left join sys_dictionary sd3 on sd3.val=t.czfs and sd3.type_id=
        (select id from sys_dictionary where type_code=
        case when t.type = '1' then 'CZFSPT' else 'CZFSYX' end)
        where t.jbxx_id = #{jbxxbId}
    </select>
    <select id="selectDcgqkListByJbxxbId" resultType="com.zjhc.gzwcq.job.zjh.entity.Dcgqk">
        select
            d.CZR_NAME as myczrxm,
            if(d.CZR_AREA = 2 ,'0',d.CZR_AREA) as myczrdq,
            d.CZR_CODE as myczrbm,
            d.SJ_CZR as sjczr,
            d.REASON as slyy,
            d.DC_RATE as dcgqbl,
            d.measures as bqcs,
            b.UNITID as qyid
        from
            cq_dcgqk as d join cq_jbxxb b on b.id = d.JBXX_ID
        where
            d.JBXX_ID = #{id}
    </select>
    <select id="selectJbxxbStatus" resultType="hashmap">
        select
            cqjb.BUSINESS_NATURE as status,
            cqjb.ID as jbxxbId,
            if(isnull(cqjb.JB_ZXCQDJQX) or cqjb.JB_ZXCQDJQX = '','true','false') as iscjbb
        FROM
            cq_jbxxb cqjb
                JOIN
            (
                SELECT
                    cjb.ID
                FROM
                    `cq_jbxxb`  cjb
                        JOIN
                    `rg_business_info` rbi on rbi.JBXX_ID = cjb.ID
                WHERE
                    cjb.UNITID = #{orgId}
                  AND
                    (rbi.RG_UNITSTATE = '2' or rbi.RG_UNITSTATE='9')
                  AND
                    cjb.JB_DELETED = 'N'
                  AND
                    (cjb.JB_SHZT = '4' or cjb.JB_SHZT = '9')
                ORDER BY
                    rbi.RG_TIMEMARK desc
                LIMIT 100000
            ) as iscjbb
            ON
                iscjbb.ID = cqjb.ID
        GROUP BY
            cqjb.UNITID
    </select>

    <select id="selectDataJbxxb" resultType="com.zjhc.gzwcq.job.zjh.entity.Jbxxb">
        SELECT
            jb.UNITID,
            jb.ID
        FROM
            (
                SELECT
                    cjb.ID,
                    cjb.UNITID,
                    cjb.JB_ZCD,
                    cjb.JB_ZCRQ,
                    cjb.jb_gsdjrq
                FROM
                    `sys_organization` so
                        JOIN
                    `cq_jbxxb`  cjb  on cjb.UNITID = so.ORGANIZATION_ID
                        JOIN
                    `rg_business_info` rbi on rbi.JBXX_ID = cjb.ID
                WHERE
                    1=1
                  AND
                    (rbi.RG_UNITSTATE = '2' or rbi.RG_UNITSTATE='9')
                  AND
                    cjb.JB_DELETED = 'N'
                  AND
                    (cjb.JB_SHZT = '4' or cjb.JB_SHZT = '9')
                ORDER BY
                    rbi.RG_TIMEMARK desc
                LIMIT 1000000000
            )jb
        WHERE
            ((ISNULL(jb.JB_ZCD) or jb.JB_ZCD = '') OR (ISNULL(jb.JB_ZCRQ)) or (isnull(jb.jb_gsdjrq)))
        GROUP BY
            jb.UNITID
    </select>

    <resultMap type="com.zjhc.gzwcq.job.zjh.entity.Jbxxb" id="baseResultMapJbxxb">
        <id column="id" property="id"/>
        <result column="unitid" property="unitid"/>
        <result column="datatime" property="datatime"/>
        <result column="floatorder" property="floatorder"/>
        <result column="jb_zczb" property="jbZczb"/>
        <result column="jb_gjsbs" property="jbGjsbs"/>
        <result column="jb_gjsds" property="jbGjsds"/>
        <result column="jb_gyfrsbs" property="jbGyfrsbs"/>
        <result column="jb_gyfrsds" property="jbGyfrsds"/>
        <result column="jb_gyjdkgsbs" property="jbGyjdkgsbs"/>
        <result column="jb_gyjdkgsds" property="jbGyjdkgsds"/>
        <result column="jb_gysjkzsbs" property="jbGysjkzsbs"/>
        <result column="jb_gysjkzsds" property="jbGysjkzsds"/>
        <result column="jb_qtqysbs" property="jbQtqysbs"/>
        <result column="jb_qtsds" property="jbQtsds"/>
        <result column="jb_hjqysbs" property="jbHjqysbs"/>
        <result column="jb_hjsds" property="jbHjsds"/>
        <result column="jb_sjblrq" property="jbSjblrq"/>
        <result column="jb_hjcze" property="jbHjcze"/>
        <result column="jb_hjsjzcj" property="jbHjsjzcj"/>
        <result column="jb_hjbl" property="jbHjbl"/>
        <result column="jb_qymc" property="jbQymc"/>
        <result column="jb_zzjgdm" property="jbZzjgdm"/>
        <result column="jb_zcmd" property="jbZcmd"/>
        <result column="jb_cgrxm" property="jbCgrxm"/>
        <result column="jb_jhqlsj" property="jbJhqlsj"/>
        <result column="jb_sjczr" property="jbSjczr"/>
        <result column="jb_czrzzjgdm" property="jbCzrzzjgdm"/>
        <result column="jb_hjsjzcjbz" property="jbHjsjzcjbz"/>
        <result column="jb_gjczsdsbz" property="jbGjczsdsbz"/>
        <result column="jb_gjczqysbsbz" property="jbGjczqysbsbz"/>
        <result column="jb_gyfrczsbsbz" property="jbGyfrczsbsbz"/>
        <result column="jb_gyfrczsdsbz" property="jbGyfrczsdsbz"/>
        <result column="jb_gyjdkgfrsbsbz" property="jbGyjdkgfrsbsbz"/>
        <result column="jb_gyjdkgfrsdsbz" property="jbGyjdkgfrsdsbz"/>
        <result column="jb_gysjkzfrsbsbz" property="jbGysjkzfrsbsbz"/>
        <result column="jb_gysjkzfrsdsbz" property="jbGysjkzfrsdsbz"/>
        <result column="jb_qtsbsbz" property="jbQtsbsbz"/>
        <result column="jb_qtqysdsbz" property="jbQtqysdsbz"/>
        <result column="jb_hjqysbsbz" property="jbHjqysbsbz"/>
        <result column="jb_hjsdsbz" property="jbHjsdsbz"/>
        <result column="jb_shzt" property="jbShzt"/>
        <result column="jb_sshy" property="jbSshy"/>
        <result column="jb_sfzy" property="jbSfzy"/>
        <result column="jb_zzxs" property="jbZzxs"/>
        <result column="jb_qylb" property="jbQylb"/>
        <result column="jb_qyjc" property="jbQyjc"/>
        <result column="jb_ssbm" property="jbSsbm"/>
        <result column="jb_jyzk" property="jbJyzk"/>
        <result column="jb_sftsmdgs" property="jbSftsmdgs"/>
        <result column="jb_sfczgrdcg" property="jbSfczgrdcg"/>
        <result column="jb_gjczqy" property="jbGjczqy"/>
        <result column="jb_qysbsbzxz" property="jbQysbsbzxz"/>
        <result column="jb_sdsbzxz" property="jbSdsbzxz"/>
        <result column="jb_jnjw" property="jbJnjw"/>
        <result column="jb_zycqdjqx" property="jbZycqdjqx"/>
        <result column="jb_bdcqdjqx" property="jbBdcqdjqx"/>
        <result column="jb_zxcqdjqx" property="jbZxcqdjqx"/>
        <result column="jb_gzjgjg" property="jbGzjgjg"/>
        <result column="jb_zcd" property="jbZcd"/>
        <result column="jb_gsdjrq" property="jbGsdjrq"/>
        <result column="jb_zcrq" property="jbZcrq"/>
        <result column="jb_sfybgs" property="jbSfybgs"/>
        <result column="jb_gsblzk" property="jbGsblzk"/>
        <result column="jb_gsdjxgzl" property="jbGsdjxgzl"/>
        <result column="jb_sfyz" property="jbSfyz"/>
        <result column="jb_byzly" property="jbByzly"/>
        <result column="jb_zyhy" property="jbZyhy"/>
        <result column="jb_zcdjw" property="jbZcdjw"/>
        <result column="jb_hjcjebz" property="jbHjcjebz"/>
        <result column="jb_czebzxz" property="jbCzebzxz"/>
        <result column="jb_sjzczbbzxz" property="jbSjzczbbzxz"/>
        <result column="jb_blqshzt" property="jbBlqshzt"/>
        <result column="jb_sfztjn" property="jbSfztjn"/>
        <result column="jb_zczbbz" property="jbZczbbz"/>
        <result column="jb_zczbjw" property="jbZczbjw"/>
        <result column="jb_sfzdyjsj" property="jbSfzdyjsj"/>
        <result column="jb_sfsjscdm" property="jbSfsjscdm"/>
        <result column="jb_xzqy" property="jbXzqy"/>
        <result column="jb_shtgrq" property="jbShtgrq"/>
        <result column="jb_dylsh" property="jbDylsh"/>
        <result column="jb_ssgzjgjg" property="jbSsgzjgjg"/>
        <result column="jb_qljh" property="jbQljh"/>
        <result column="jb_hjrjzb" property="jbHjrjzb"/>
        <result column="jb_rjzbbzxz" property="jbRjzbbzxz"/>
        <result column="jb_hjrjzbbz" property="jbHjrjzbbz"/>
        <result column="jb_zczbbzxz" property="jbZczbbzxz"/>
        <result column="jb_gykgcz" property="jbGykgcz"/>
        <result column="jb_gykgczsbs" property="jbGykgczsbs"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="last_update_user" property="lastUpdateUser"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="business_nature" property="businessNature"/>
        <result column="jb_gyzb" property="jbGyzb"/>
        <result column="jb_rela" property="jbRela"/>
        <result column="jb_hgqy" property="jbHgqy"/>
        <result column="jb_czrzzjgid" property="jbCzrzzjgid"/>
        <result column="jb_deleted" property="jbDeleted"/>
        <result column="jb_sf_ss" property="jbSfss"/>
        <result column="jb_sf_bb" property="jbSfbb"/>
        <result column="jb_qyjycs" property="jbQyjycs"/>
        <result column="jb_sf_tgqy" property="jbSftgqy"/>
        <result column="jb_sf_czblzt" property="jbSfczblzt"/>
        <result column="jb_sf_kzgs" property="jbSfkzgs"/>
        <result column="jb_zyhy2" property="jbZyhy2"/>
        <result column="jb_zyhy3" property="jbZyhy3"/>
        <result column="jb_qygljc" property="jbQygljc"/>
        <result column="jb_qyslzt" property="jbQyslzt"/>
        <result column="jb_qzwjkjglx" property="jbQzwjkjglx"/>
        <result column="jb_gzwjkjgmx" property="jbGzwjkjgmx"/>
        <result column="jb_yyzzzc" property="jbYyzzzc"/>
        <result column="jb_zyhy1" property="jbZyhy1"/>
        <result column="jb_rjzb" property="jbRjzb"/>
        <result column="jb_rjzbbz" property="jbRjzbbz"/>
        <result column="jb_data_status" property="jbDataStatus"/>
        <result column="jb_myqysbsbz" property="jbMyqysbsbz"/>
        <result column="jb_myqysbs" property="jbMyqysbs"/>
        <result column="jb_wzqysbsbz" property="jbWzqysbsbz"/>
        <result column="jb_wzqysbs" property="jbWzqysbs"/>
        <result column="jb_zrrsbsbz" property="jbZrrsbsbz"/>
        <result column="jb_zrrsbs" property="jbZrrsbs"/>
    </resultMap>

    <select id="selectById" resultMap="baseResultMapJbxxb">
        select
            *
        from
            cq_jbxxb
        where
            UNITID = #{id}
        order by
            create_time asc
    </select>
    <update id="upDateJbxxb">
        update
            cq_jbxxb
        set
            JB_ZCD= #{jbZcdNew},
            JB_ZCRQ = #{jbZcrqNew},
            jb_gsdjrq = #{JBGSDJRQ}
    where
            id = #{id}
    </update>
</mapper>
