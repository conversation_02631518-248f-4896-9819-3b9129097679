package com.zjhc.gzwcq.job.zjh.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> <br/>
 *         表名： cq_jbxxb <br/>
 *         描述：产权基本信息表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Jbxxb implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期")
	protected String datatime;// 数据时期
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="注册资本(万元)")
	protected BigDecimal jbZczb;// 注册资本(万元)
  	@ApiParam(value="国家出资企业申报数")
	protected BigDecimal jbGjsbs;// 国家出资企业申报数
  	@ApiParam(value="国家出资审定数")
	protected BigDecimal jbGjsds;// 国家出资审定数
  	@ApiParam(value="国有全资法人出资企业申报数")
	protected BigDecimal jbGyfrsbs;// 国有全资法人出资企业申报数
  	@ApiParam(value="国有法人出资审定数")
	protected BigDecimal jbGyfrsds;// 国有法人出资审定数
  	@ApiParam(value="国有绝对控股法人出资企业申报数")
	protected BigDecimal jbGyjdkgsbs;// 国有绝对控股法人出资企业申报数
  	@ApiParam(value="国有绝对控股法人出资审定数")
	protected BigDecimal jbGyjdkgsds;// 国有绝对控股法人出资审定数
  	@ApiParam(value="国有实际控制法人出资企业申报数")
	protected BigDecimal jbGysjkzsbs;// 国有实际控制法人出资企业申报数
  	@ApiParam(value="国有实际控制法人出资审定数")
	protected BigDecimal jbGysjkzsds;// 国有实际控制法人出资审定数
  	@ApiParam(value="其他企业申报数")
	protected BigDecimal jbQtqysbs;// 其他企业申报数
  	@ApiParam(value="其他审定数")
	protected BigDecimal jbQtsds;// 其他审定数
  	@ApiParam(value="合计企业申报数")
	protected BigDecimal jbHjqysbs;// 合计企业申报数
  	@ApiParam(value="合计审定数")
	protected BigDecimal jbHjsds;// 合计审定数
  	@ApiParam(value="实际办理日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date jbSjblrq;// 实际办理日期
  	@ApiParam(value="合计出资额")
	protected BigDecimal jbHjcze;// 合计出资额
  	@ApiParam(value="合计实缴注册金")
	protected BigDecimal jbHjsjzcj;// 合计实缴注册金
  	@ApiParam(value="合计比例")
	protected BigDecimal jbHjbl;// 合计比例
  	@ApiParam(value="企业名称")
	protected String jbQymc;// 企业名称
  	@ApiParam(value="组织机构代码")
	protected String jbZzjgdm;// 组织机构代码
  	@ApiParam(value="注册目的")
	protected String jbZcmd;// 注册目的
  	@ApiParam(value="持股人姓名")
	protected String jbCgrxm;// 持股人姓名
  	@ApiParam(value="计划清理时间")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date jbJhqlsj;// 计划清理时间
  	@ApiParam(value="实际出资人")
	protected String jbSjczr;// 实际出资人
  	@ApiParam(value="主要出资企业组织机构代码")
	protected String jbCzrzzjgdm;// 主要出资企业组织机构代码
  	@ApiParam(value="合计实缴注册金币种")
	protected BigDecimal jbHjsjzcjbz;// 合计实缴注册金币种
  	@ApiParam(value="国家出资审定数币种")
	protected BigDecimal jbGjczsdsbz;// 国家出资审定数币种
  	@ApiParam(value="国家出资企业申报数币种")
	protected BigDecimal jbGjczqysbsbz;// 国家出资企业申报数币种
  	@ApiParam(value="国有全资法人出资企业申报数币种")
	protected BigDecimal jbGyfrczsbsbz;// 国有全资法人出资企业申报数币种
  	@ApiParam(value="国有法人出资审定数币种")
	protected BigDecimal jbGyfrczsdsbz;// 国有法人出资审定数币种
  	@ApiParam(value="国有绝对控股法人出资企业申报数币种")
	protected BigDecimal jbGyjdkgfrsbsbz;// 国有绝对控股法人出资企业申报数币种
  	@ApiParam(value="国有绝对控股法人出资审定数币种")
	protected BigDecimal jbGyjdkgfrsdsbz;// 国有绝对控股法人出资审定数币种
  	@ApiParam(value="国有实际控制法人出资企业申报数币种")
	protected BigDecimal jbGysjkzfrsbsbz;// 国有实际控制法人出资企业申报数币种
  	@ApiParam(value="国有实际控制法人出资审定数币种")
	protected BigDecimal jbGysjkzfrsdsbz;// 国有实际控制法人出资审定数币种
  	@ApiParam(value="其他企业申报数币种")
	protected BigDecimal jbQtsbsbz;// 其他企业申报数币种
  	@ApiParam(value="其他审定数币种")
	protected BigDecimal jbQtqysdsbz;// 其他审定数币种
  	@ApiParam(value="合计企业申报数币种")
	protected BigDecimal jbHjqysbsbz;// 合计企业申报数币种
  	@ApiParam(value="合计审定数币种")
	protected BigDecimal jbHjsdsbz;// 合计审定数币种
  	@ApiParam(value="审核状态  3 审核中（包含待审核、回退等） 4 审核通过 8 填报中/待上报（新增状态，流程未发起=草稿）")
	protected Integer jbShzt;// 审核状态  3 审核中（包含待审核、回退等） 4 审核通过 8 填报中/待上报（新增状态，流程未发起=草稿）
  	@ApiParam(value="主要行业")
	protected String jbSshy;// 主要行业
  	@ApiParam(value="是否国家出资企业主业")
	protected String jbSfzy;// 是否国家出资企业主业
  	@ApiParam(value="组织形式")
	protected String jbZzxs;// 组织形式
  	@ApiParam(value="企业类别")
	protected String jbQylb;// 企业类别
  	@ApiParam(value="企业级次")
	protected String jbQyjc;// 企业级次
  	@ApiParam(value="所属部门")
	protected String jbSsbm;// 所属部门
  	@ApiParam(value="经营状况")
	protected String jbJyzk;// 经营状况
  	@ApiParam(value="是否特殊目的公司")
	protected String jbSftsmdgs;// 是否特殊目的公司
  	@ApiParam(value="是否存在个人代持股")
	protected String jbSfczgrdcg;// 是否存在个人代持股
  	@ApiParam(value="国家出资企业")
	protected String jbGjczqy;// 国家出资企业
  	@ApiParam(value="企业申报数币种选择")
	protected String jbQysbsbzxz;// 企业申报数币种选择
  	@ApiParam(value="审定数币种选择")
	protected String jbSdsbzxz;// 审定数币种选择
  	@ApiParam(value="境内境外")
	protected String jbJnjw;// 境内境外
  	@ApiParam(value="占有产权登记情形")
	protected String jbZycqdjqx;// 占有产权登记情形
  	@ApiParam(value="变动产权登记情形")
	protected String jbBdcqdjqx;// 变动产权登记情形
  	@ApiParam(value="注销产权登记情形")
	protected String jbZxcqdjqx;// 注销产权登记情形
  	@ApiParam(value="国资监管机构")
	protected String jbGzjgjg;// 国资监管机构
  	@ApiParam(value="注册地")
	protected String jbZcd;// 注册地
  	@ApiParam(value="工商登记日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date jbGsdjrq;// 工商登记日期
  	@ApiParam(value="设立注册日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date jbZcrq;// 设立注册日期
  	@ApiParam(value="是否已办工商")
	protected String jbSfybgs;// 是否已办工商
  	@ApiParam(value="工商办理状况")
	protected String jbGsblzk;// 工商办理状况
  	@ApiParam(value="工商登记相关资料")
	protected String jbGsdjxgzl;// 工商登记相关资料
  	@ApiParam(value="是否一致")
	protected String jbSfyz;// 是否一致
  	@ApiParam(value="不一致理由")
	protected String jbByzly;// 不一致理由
  	@ApiParam(value="主要行业")
	protected String jbZyhy;// 主要行业
  	@ApiParam(value="注册地（境外）")
	protected String jbZcdjw;// 注册地（境外）
  	@ApiParam(value="合计出资额币种")
	protected BigDecimal jbHjcjebz;// 合计出资额币种
  	@ApiParam(value="出资额币种选择")
	protected String jbCzebzxz;// 出资额币种选择
  	@ApiParam(value="实缴注册资本币种选择")
	protected String jbSjzczbbzxz;// 实缴注册资本币种选择
  	@ApiParam(value="补录前审核状态(0的数据3条，4的数据788条，可忽略)")
	protected Integer jbBlqshzt;// 补录前审核状态(0的数据3条，4的数据788条，可忽略)
  	@ApiParam(value="是否境外转投境内")
	protected String jbSfztjn;// 是否境外转投境内
  	@ApiParam(value="注册资本币种")
	protected String jbZczbbz;// 注册资本币种
  	@ApiParam(value="注册资本（境外）")
	protected BigDecimal jbZczbjw;// 注册资本（境外）
  	@ApiParam(value="是否自动预警数据")
	protected String jbSfzdyjsj;// 是否自动预警数据
  	@ApiParam(value="是否随机生成代码")
	protected String jbSfsjscdm;// 是否随机生成代码
  	@ApiParam(value="行政区域")
	protected String jbXzqy;// 行政区域
  	@ApiParam(value="审核通过日期")
	protected String jbShtgrq;// 审核通过日期
  	@ApiParam(value="打印流水号")
	protected String jbDylsh;// 打印流水号
  	@ApiParam(value="所属国资监管机构")
	protected String jbSsgzjgjg;// 所属国资监管机构
  	@ApiParam(value="清理计划")
	protected String jbQljh;// 清理计划
  	@ApiParam(value="合计认缴资本数")
	protected BigDecimal jbHjrjzb;// 合计认缴资本数
  	@ApiParam(value="认缴资本币种选择")
	protected String jbRjzbbzxz;// 认缴资本币种选择
  	@ApiParam(value="合计认缴资本币种")
	protected BigDecimal jbHjrjzbbz;// 合计认缴资本币种
  	@ApiParam(value="注册资本币种选择")
	protected String jbZczbbzxz;// 注册资本币种选择
  	@ApiParam(value="国有控股出资审定数")
	protected BigDecimal jbGykgcz;// 国有控股出资审定数
  	@ApiParam(value="国有控股出资企业申报数")
	protected BigDecimal jbGykgczsbs;// 国有控股出资企业申报数
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间
  	@ApiParam(value="企业性质（国有企业/合伙企业 默认国有）")
	protected String businessNature;// 企业性质（国有企业/合伙企业 默认国有）
  	@ApiParam(value="国有资本（万元）")
	protected BigDecimal jbGyzb;// 国有资本（万元）
  	@ApiParam(value="与国家出资企业的关系")
	protected String jbRela;// 与国家出资企业的关系
  	@ApiParam(value="是否混改企业")
	protected String jbHgqy;// 是否混改企业
	@ApiParam(value="主要出资人组织机构id")
	protected String jbCzrzzjgid;//主要出资人组织机构id

	@ApiParam(value="合伙企业出资人id(目前仅用于唯一性校验)")
	protected String hhCzqyId;
	@ApiParam(value="是否被删除（Y已删除/N未删除）")
	protected String jbDeleted;
	@ApiParam(value="是否上市公司")
	protected String jbSfss;
	@ApiParam(value="是否并表")
	protected String jbSfbb;
	@ApiParam(value="主要经营场所")
	protected String jbQyjycs;
	@ApiParam(value="是否代管托管")
	protected String jbSftgqy;
	@ApiParam(value="是否存在休眠、停业、歇业")
	protected String jbSfczblzt;
	@ApiParam(value="是否壳公司")
	protected String jbSfkzgs;
	@ApiParam(value = "主要行业2")
	protected String jbZyhy2;
	@ApiParam(value = "主要行业3")
	protected String jbZyhy3;
	@ApiParam(value = "企业管理级次")
	protected String jbQygljc;
	@ApiParam(value = "企业设立登记状态")
	protected String jbQyslzt;
	@ApiParam(value = "国资监管机构类型")
	protected String jbQzwjkjglx;
	@ApiParam(value = "国资监管机构明细")
	protected String jbGzwjkjgmx;
	@ApiParam(value = "营业执照住所")
	protected String  jbYyzzzc;
	@ApiParam(value = "国有主要行业1")
	protected String jbZyhy1;
	@ApiParam(value = "认缴资本币种")
	protected String jbRjzbbz;
	@ApiParam(value = "认缴资本金额")
	protected String jbRjzb;

	@ApiParam(value = "数据状态 old 老数据 / new 新数据")
	protected String jbDataStatus;

	@ApiParam(value = "民营企业申报数币种")
	protected String jbMyqysbsbz;

	@ApiParam(value = "民营企业申报数")
	protected String jbMyqysbs;

	@ApiParam(value = "外资企业申报数币种")
	protected String jbWzqysbsbz;

	@ApiParam(value = "外资企业申报数")
	protected String jbWzqysbs;

	@ApiParam(value = "自然人申报数币种")
	protected String jbZrrsbsbz;

	@ApiParam(value = "自然人申报数")
	protected String jbZrrsbs;

	public String getJbRjzbbz() {
		return jbRjzbbz;
	}

	public void setJbRjzbbz(String jbRjzbbz) {
		this.jbRjzbbz = jbRjzbbz;
	}

	public String getJbRjzb() {
		return jbRjzb;
	}

	public void setJbRjzb(String jbRjzb) {
		this.jbRjzb = jbRjzb;
	}

	public String getJbZyhy1() {
		return jbZyhy1;
	}

	public void setJbZyhy1(String jbZyhy1) {
		this.jbZyhy1 = jbZyhy1;
	}

	public String getJbSfss() {
		return jbSfss;
	}

	public void setJbSfss(String jbSfss) {
		this.jbSfss = jbSfss;
	}

	public String getJbSfbb() {
		return jbSfbb;
	}

	public void setJbSfbb(String jbSfbb) {
		this.jbSfbb = jbSfbb;
	}

	public String getJbQyjycs() {
		return jbQyjycs;
	}

	public void setJbQyjycs(String jbQyjycs) {
		this.jbQyjycs = jbQyjycs;
	}

	public String getJbSftgqy() {
		return jbSftgqy;
	}

	public void setJbSftgqy(String jbSftgqy) {
		this.jbSftgqy = jbSftgqy;
	}

	public String getJbSfczblzt() {
		return jbSfczblzt;
	}

	public void setJbSfczblzt(String jbSfczblzt) {
		this.jbSfczblzt = jbSfczblzt;
	}

	public String getJbSfkzgs() {
		return jbSfkzgs;
	}

	public void setJbSfkzgs(String jbSfkzgs) {
		this.jbSfkzgs = jbSfkzgs;
	}

	public String getJbZyhy2() {
		return jbZyhy2;
	}

	public void setJbZyhy2(String jbZyhy2) {
		this.jbZyhy2 = jbZyhy2;
	}

	public String getJbZyhy3() {
		return jbZyhy3;
	}

	public void setJbZyhy3(String jbZyhy3) {
		this.jbZyhy3 = jbZyhy3;
	}

	public String getJbQygljc() {
		return jbQygljc;
	}

	public void setJbQygljc(String jbQygljc) {
		this.jbQygljc = jbQygljc;
	}

	public String getJbQyslzt() {
		return jbQyslzt;
	}

	public void setJbQyslzt(String jbQyslzt) {
		this.jbQyslzt = jbQyslzt;
	}

	public String getJbQzwjkjglx() {
		return jbQzwjkjglx;
	}

	public void setJbQzwjkjglx(String jbQzwjkjglx) {
		this.jbQzwjkjglx = jbQzwjkjglx;
	}

	public String getJbGzwjkjgmx() {
		return jbGzwjkjgmx;
	}

	public void setJbGzwjkjgmx(String jbGzwjkjgmx) {
		this.jbGzwjkjgmx = jbGzwjkjgmx;
	}

	public String getJbYyzzzc() {
		return jbYyzzzc;
	}

	public void setJbYyzzzc(String jbYyzzzc) {
		this.jbYyzzzc = jbYyzzzc;
	}

	public Jbxxb() {
		super();
	}
	
  	public Jbxxb(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public BigDecimal getJbZczb() {
		return jbZczb;
	}
	public void setJbZczb(BigDecimal jbZczb) {
		this.jbZczb = jbZczb;
	}
	public BigDecimal getJbGjsbs() {
		return jbGjsbs;
	}
	public void setJbGjsbs(BigDecimal jbGjsbs) {
		this.jbGjsbs = jbGjsbs;
	}
	public BigDecimal getJbGjsds() {
		return jbGjsds;
	}
	public void setJbGjsds(BigDecimal jbGjsds) {
		this.jbGjsds = jbGjsds;
	}
	public BigDecimal getJbGyfrsbs() {
		return jbGyfrsbs;
	}
	public void setJbGyfrsbs(BigDecimal jbGyfrsbs) {
		this.jbGyfrsbs = jbGyfrsbs;
	}
	public BigDecimal getJbGyfrsds() {
		return jbGyfrsds;
	}
	public void setJbGyfrsds(BigDecimal jbGyfrsds) {
		this.jbGyfrsds = jbGyfrsds;
	}
	public BigDecimal getJbGyjdkgsbs() {
		return jbGyjdkgsbs;
	}
	public void setJbGyjdkgsbs(BigDecimal jbGyjdkgsbs) {
		this.jbGyjdkgsbs = jbGyjdkgsbs;
	}
	public BigDecimal getJbGyjdkgsds() {
		return jbGyjdkgsds;
	}
	public void setJbGyjdkgsds(BigDecimal jbGyjdkgsds) {
		this.jbGyjdkgsds = jbGyjdkgsds;
	}
	public BigDecimal getJbGysjkzsbs() {
		return jbGysjkzsbs;
	}
	public void setJbGysjkzsbs(BigDecimal jbGysjkzsbs) {
		this.jbGysjkzsbs = jbGysjkzsbs;
	}
	public BigDecimal getJbGysjkzsds() {
		return jbGysjkzsds;
	}
	public void setJbGysjkzsds(BigDecimal jbGysjkzsds) {
		this.jbGysjkzsds = jbGysjkzsds;
	}
	public BigDecimal getJbQtqysbs() {
		return jbQtqysbs;
	}
	public void setJbQtqysbs(BigDecimal jbQtqysbs) {
		this.jbQtqysbs = jbQtqysbs;
	}
	public BigDecimal getJbQtsds() {
		return jbQtsds;
	}
	public void setJbQtsds(BigDecimal jbQtsds) {
		this.jbQtsds = jbQtsds;
	}
	public BigDecimal getJbHjqysbs() {
		return jbHjqysbs;
	}
	public void setJbHjqysbs(BigDecimal jbHjqysbs) {
		this.jbHjqysbs = jbHjqysbs;
	}
	public BigDecimal getJbHjsds() {
		return jbHjsds;
	}
	public void setJbHjsds(BigDecimal jbHjsds) {
		this.jbHjsds = jbHjsds;
	}
	public Date getJbSjblrq() {
		return jbSjblrq;
	}
	public void setJbSjblrq(Date jbSjblrq) {
		this.jbSjblrq = jbSjblrq;
	}
	public BigDecimal getJbHjcze() {
		return jbHjcze;
	}
	public void setJbHjcze(BigDecimal jbHjcze) {
		this.jbHjcze = jbHjcze;
	}
	public BigDecimal getJbHjsjzcj() {
		return jbHjsjzcj;
	}
	public void setJbHjsjzcj(BigDecimal jbHjsjzcj) {
		this.jbHjsjzcj = jbHjsjzcj;
	}
	public BigDecimal getJbHjbl() {
		return jbHjbl;
	}
	public void setJbHjbl(BigDecimal jbHjbl) {
		this.jbHjbl = jbHjbl;
	}
	public String getJbQymc() {
		return jbQymc;
	}
	public void setJbQymc(String jbQymc) {
		this.jbQymc = jbQymc;
	}
	public String getJbZzjgdm() {
		return jbZzjgdm;
	}
	public void setJbZzjgdm(String jbZzjgdm) {
		this.jbZzjgdm = jbZzjgdm;
	}
	public String getJbZcmd() {
		return jbZcmd;
	}
	public void setJbZcmd(String jbZcmd) {
		this.jbZcmd = jbZcmd;
	}
	public String getJbCgrxm() {
		return jbCgrxm;
	}
	public void setJbCgrxm(String jbCgrxm) {
		this.jbCgrxm = jbCgrxm;
	}
	public Date getJbJhqlsj() {
		return jbJhqlsj;
	}
	public void setJbJhqlsj(Date jbJhqlsj) {
		this.jbJhqlsj = jbJhqlsj;
	}
	public String getJbSjczr() {
		return jbSjczr;
	}
	public void setJbSjczr(String jbSjczr) {
		this.jbSjczr = jbSjczr;
	}
	public String getJbCzrzzjgdm() {
		return jbCzrzzjgdm;
	}
	public void setJbCzrzzjgdm(String jbCzrzzjgdm) {
		this.jbCzrzzjgdm = jbCzrzzjgdm;
	}
	public BigDecimal getJbHjsjzcjbz() {
		return jbHjsjzcjbz;
	}
	public void setJbHjsjzcjbz(BigDecimal jbHjsjzcjbz) {
		this.jbHjsjzcjbz = jbHjsjzcjbz;
	}
	public BigDecimal getJbGjczsdsbz() {
		return jbGjczsdsbz;
	}
	public void setJbGjczsdsbz(BigDecimal jbGjczsdsbz) {
		this.jbGjczsdsbz = jbGjczsdsbz;
	}
	public BigDecimal getJbGjczqysbsbz() {
		return jbGjczqysbsbz;
	}
	public void setJbGjczqysbsbz(BigDecimal jbGjczqysbsbz) {
		this.jbGjczqysbsbz = jbGjczqysbsbz;
	}
	public BigDecimal getJbGyfrczsbsbz() {
		return jbGyfrczsbsbz;
	}
	public void setJbGyfrczsbsbz(BigDecimal jbGyfrczsbsbz) {
		this.jbGyfrczsbsbz = jbGyfrczsbsbz;
	}
	public BigDecimal getJbGyfrczsdsbz() {
		return jbGyfrczsdsbz;
	}
	public void setJbGyfrczsdsbz(BigDecimal jbGyfrczsdsbz) {
		this.jbGyfrczsdsbz = jbGyfrczsdsbz;
	}
	public BigDecimal getJbGyjdkgfrsbsbz() {
		return jbGyjdkgfrsbsbz;
	}
	public void setJbGyjdkgfrsbsbz(BigDecimal jbGyjdkgfrsbsbz) {
		this.jbGyjdkgfrsbsbz = jbGyjdkgfrsbsbz;
	}
	public BigDecimal getJbGyjdkgfrsdsbz() {
		return jbGyjdkgfrsdsbz;
	}
	public void setJbGyjdkgfrsdsbz(BigDecimal jbGyjdkgfrsdsbz) {
		this.jbGyjdkgfrsdsbz = jbGyjdkgfrsdsbz;
	}
	public BigDecimal getJbGysjkzfrsbsbz() {
		return jbGysjkzfrsbsbz;
	}
	public void setJbGysjkzfrsbsbz(BigDecimal jbGysjkzfrsbsbz) {
		this.jbGysjkzfrsbsbz = jbGysjkzfrsbsbz;
	}
	public BigDecimal getJbGysjkzfrsdsbz() {
		return jbGysjkzfrsdsbz;
	}
	public void setJbGysjkzfrsdsbz(BigDecimal jbGysjkzfrsdsbz) {
		this.jbGysjkzfrsdsbz = jbGysjkzfrsdsbz;
	}
	public BigDecimal getJbQtsbsbz() {
		return jbQtsbsbz;
	}
	public void setJbQtsbsbz(BigDecimal jbQtsbsbz) {
		this.jbQtsbsbz = jbQtsbsbz;
	}
	public BigDecimal getJbQtqysdsbz() {
		return jbQtqysdsbz;
	}
	public void setJbQtqysdsbz(BigDecimal jbQtqysdsbz) {
		this.jbQtqysdsbz = jbQtqysdsbz;
	}
	public BigDecimal getJbHjqysbsbz() {
		return jbHjqysbsbz;
	}
	public void setJbHjqysbsbz(BigDecimal jbHjqysbsbz) {
		this.jbHjqysbsbz = jbHjqysbsbz;
	}
	public BigDecimal getJbHjsdsbz() {
		return jbHjsdsbz;
	}
	public void setJbHjsdsbz(BigDecimal jbHjsdsbz) {
		this.jbHjsdsbz = jbHjsdsbz;
	}
	public Integer getJbShzt() {
		return jbShzt;
	}
	public void setJbShzt(Integer jbShzt) {
		this.jbShzt = jbShzt;
	}
	public String getJbSshy() {
		return jbSshy;
	}
	public void setJbSshy(String jbSshy) {
		this.jbSshy = jbSshy;
	}
	public String getJbSfzy() {
		return jbSfzy;
	}
	public void setJbSfzy(String jbSfzy) {
		this.jbSfzy = jbSfzy;
	}
	public String getJbZzxs() {
		return jbZzxs;
	}
	public void setJbZzxs(String jbZzxs) {
		this.jbZzxs = jbZzxs;
	}
	public String getJbQylb() {
		return jbQylb;
	}
	public void setJbQylb(String jbQylb) {
		this.jbQylb = jbQylb;
	}
	public String getJbQyjc() {
		return jbQyjc;
	}
	public void setJbQyjc(String jbQyjc) {
		this.jbQyjc = jbQyjc;
	}
	public String getJbSsbm() {
		return jbSsbm;
	}
	public void setJbSsbm(String jbSsbm) {
		this.jbSsbm = jbSsbm;
	}
	public String getJbJyzk() {
		return jbJyzk;
	}
	public void setJbJyzk(String jbJyzk) {
		this.jbJyzk = jbJyzk;
	}
	public String getJbSftsmdgs() {
		return jbSftsmdgs;
	}
	public void setJbSftsmdgs(String jbSftsmdgs) {
		this.jbSftsmdgs = jbSftsmdgs;
	}
	public String getJbSfczgrdcg() {
		return jbSfczgrdcg;
	}
	public void setJbSfczgrdcg(String jbSfczgrdcg) {
		this.jbSfczgrdcg = jbSfczgrdcg;
	}
	public String getJbGjczqy() {
		return jbGjczqy;
	}
	public void setJbGjczqy(String jbGjczqy) {
		this.jbGjczqy = jbGjczqy;
	}
	public String getJbQysbsbzxz() {
		return jbQysbsbzxz;
	}
	public void setJbQysbsbzxz(String jbQysbsbzxz) {
		this.jbQysbsbzxz = jbQysbsbzxz;
	}
	public String getJbSdsbzxz() {
		return jbSdsbzxz;
	}
	public void setJbSdsbzxz(String jbSdsbzxz) {
		this.jbSdsbzxz = jbSdsbzxz;
	}
	public String getJbJnjw() {
		return jbJnjw;
	}
	public void setJbJnjw(String jbJnjw) {
		this.jbJnjw = jbJnjw;
	}
	public String getJbZycqdjqx() {
		return jbZycqdjqx;
	}
	public void setJbZycqdjqx(String jbZycqdjqx) {
		this.jbZycqdjqx = jbZycqdjqx;
	}
	public String getJbBdcqdjqx() {
		return jbBdcqdjqx;
	}
	public void setJbBdcqdjqx(String jbBdcqdjqx) {
		this.jbBdcqdjqx = jbBdcqdjqx;
	}
	public String getJbZxcqdjqx() {
		return jbZxcqdjqx;
	}
	public void setJbZxcqdjqx(String jbZxcqdjqx) {
		this.jbZxcqdjqx = jbZxcqdjqx;
	}
	public String getJbGzjgjg() {
		return jbGzjgjg;
	}
	public void setJbGzjgjg(String jbGzjgjg) {
		this.jbGzjgjg = jbGzjgjg;
	}
	public String getJbZcd() {
		return jbZcd;
	}
	public void setJbZcd(String jbZcd) {
		this.jbZcd = jbZcd;
	}
	public Date getJbGsdjrq() {
		return jbGsdjrq;
	}
	public void setJbGsdjrq(Date jbGsdjrq) {
		this.jbGsdjrq = jbGsdjrq;
	}
	public Date getJbZcrq() {
		return jbZcrq;
	}
	public void setJbZcrq(Date jbZcrq) {
		this.jbZcrq = jbZcrq;
	}
	public String getJbSfybgs() {
		return jbSfybgs;
	}
	public void setJbSfybgs(String jbSfybgs) {
		this.jbSfybgs = jbSfybgs;
	}
	public String getJbGsblzk() {
		return jbGsblzk;
	}
	public void setJbGsblzk(String jbGsblzk) {
		this.jbGsblzk = jbGsblzk;
	}
	public String getJbGsdjxgzl() {
		return jbGsdjxgzl;
	}
	public void setJbGsdjxgzl(String jbGsdjxgzl) {
		this.jbGsdjxgzl = jbGsdjxgzl;
	}
	public String getJbSfyz() {
		return jbSfyz;
	}
	public void setJbSfyz(String jbSfyz) {
		this.jbSfyz = jbSfyz;
	}
	public String getJbByzly() {
		return jbByzly;
	}
	public void setJbByzly(String jbByzly) {
		this.jbByzly = jbByzly;
	}
	public String getJbZyhy() {
		return jbZyhy;
	}
	public void setJbZyhy(String jbZyhy) {
		this.jbZyhy = jbZyhy;
	}
	public String getJbZcdjw() {
		return jbZcdjw;
	}
	public void setJbZcdjw(String jbZcdjw) {
		this.jbZcdjw = jbZcdjw;
	}
	public BigDecimal getJbHjcjebz() {
		return jbHjcjebz;
	}
	public void setJbHjcjebz(BigDecimal jbHjcjebz) {
		this.jbHjcjebz = jbHjcjebz;
	}
	public String getJbCzebzxz() {
		return jbCzebzxz;
	}
	public void setJbCzebzxz(String jbCzebzxz) {
		this.jbCzebzxz = jbCzebzxz;
	}
	public String getJbSjzczbbzxz() {
		return jbSjzczbbzxz;
	}
	public void setJbSjzczbbzxz(String jbSjzczbbzxz) {
		this.jbSjzczbbzxz = jbSjzczbbzxz;
	}
	public Integer getJbBlqshzt() {
		return jbBlqshzt;
	}
	public void setJbBlqshzt(Integer jbBlqshzt) {
		this.jbBlqshzt = jbBlqshzt;
	}
	public String getJbSfztjn() {
		return jbSfztjn;
	}
	public void setJbSfztjn(String jbSfztjn) {
		this.jbSfztjn = jbSfztjn;
	}
	public String getJbZczbbz() {
		return jbZczbbz;
	}
	public void setJbZczbbz(String jbZczbbz) {
		this.jbZczbbz = jbZczbbz;
	}
	public BigDecimal getJbZczbjw() {
		return jbZczbjw;
	}
	public void setJbZczbjw(BigDecimal jbZczbjw) {
		this.jbZczbjw = jbZczbjw;
	}
	public String getJbSfzdyjsj() {
		return jbSfzdyjsj;
	}
	public void setJbSfzdyjsj(String jbSfzdyjsj) {
		this.jbSfzdyjsj = jbSfzdyjsj;
	}
	public String getJbSfsjscdm() {
		return jbSfsjscdm;
	}
	public void setJbSfsjscdm(String jbSfsjscdm) {
		this.jbSfsjscdm = jbSfsjscdm;
	}
	public String getJbXzqy() {
		return jbXzqy;
	}
	public void setJbXzqy(String jbXzqy) {
		this.jbXzqy = jbXzqy;
	}
	public String getJbShtgrq() {
		return jbShtgrq;
	}
	public void setJbShtgrq(String jbShtgrq) {
		this.jbShtgrq = jbShtgrq;
	}
	public String getJbDylsh() {
		return jbDylsh;
	}
	public void setJbDylsh(String jbDylsh) {
		this.jbDylsh = jbDylsh;
	}
	public String getJbSsgzjgjg() {
		return jbSsgzjgjg;
	}
	public void setJbSsgzjgjg(String jbSsgzjgjg) {
		this.jbSsgzjgjg = jbSsgzjgjg;
	}
	public String getJbQljh() {
		return jbQljh;
	}
	public void setJbQljh(String jbQljh) {
		this.jbQljh = jbQljh;
	}
	public BigDecimal getJbHjrjzb() {
		return jbHjrjzb;
	}
	public void setJbHjrjzb(BigDecimal jbHjrjzb) {
		this.jbHjrjzb = jbHjrjzb;
	}
	public String getJbRjzbbzxz() {
		return jbRjzbbzxz;
	}
	public void setJbRjzbbzxz(String jbRjzbbzxz) {
		this.jbRjzbbzxz = jbRjzbbzxz;
	}
	public BigDecimal getJbHjrjzbbz() {
		return jbHjrjzbbz;
	}
	public void setJbHjrjzbbz(BigDecimal jbHjrjzbbz) {
		this.jbHjrjzbbz = jbHjrjzbbz;
	}
	public String getJbZczbbzxz() {
		return jbZczbbzxz;
	}
	public void setJbZczbbzxz(String jbZczbbzxz) {
		this.jbZczbbzxz = jbZczbbzxz;
	}
	public BigDecimal getJbGykgcz() {
		return jbGykgcz;
	}
	public void setJbGykgcz(BigDecimal jbGykgcz) {
		this.jbGykgcz = jbGykgcz;
	}
	public BigDecimal getJbGykgczsbs() {
		return jbGykgczsbs;
	}
	public void setJbGykgczsbs(BigDecimal jbGykgczsbs) {
		this.jbGykgczsbs = jbGykgczsbs;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	public String getBusinessNature() {
		return businessNature;
	}
	public void setBusinessNature(String businessNature) {
		this.businessNature = businessNature;
	}
	public BigDecimal getJbGyzb() {
		return jbGyzb;
	}
	public void setJbGyzb(BigDecimal jbGyzb) {
		this.jbGyzb = jbGyzb;
	}
	public String getJbRela() {
		return jbRela;
	}
	public void setJbRela(String jbRela) {
		this.jbRela = jbRela;
	}
	public String getJbHgqy() {
		return jbHgqy;
	}
	public void setJbHgqy(String jbHgqy) {
		this.jbHgqy = jbHgqy;
	}

	public String getJbCzrzzjgid() {
		return jbCzrzzjgid;
	}

	public void setJbCzrzzjgid(String jbCzrzzjgid) {
		this.jbCzrzzjgid = jbCzrzzjgid;
	}

	public String getHhCzqyId() {
		return hhCzqyId;
	}

	public void setHhCzqyId(String hhCzqyId) {
		this.hhCzqyId = hhCzqyId;
	}

	public String getJbDeleted() {
		return jbDeleted;
	}

	public void setJbDeleted(String jbDeleted) {
		this.jbDeleted = jbDeleted;
	}

	public String getJbDataStatus() {
		return jbDataStatus;
	}

	public void setJbDataStatus(String jbDataStatus) {
		this.jbDataStatus = jbDataStatus;
	}

	public String getJbMyqysbsbz() {
		return jbMyqysbsbz;
	}

	public void setJbMyqysbsbz(String jbMyqysbsbz) {
		this.jbMyqysbsbz = jbMyqysbsbz;
	}

	public String getJbMyqysbs() {
		return jbMyqysbs;
	}

	public void setJbMyqysbs(String jbMyqysbs) {
		this.jbMyqysbs = jbMyqysbs;
	}

	public String getJbWzqysbsbz() {
		return jbWzqysbsbz;
	}

	public void setJbWzqysbsbz(String jbWzqysbsbz) {
		this.jbWzqysbsbz = jbWzqysbsbz;
	}

	public String getJbWzqysbs() {
		return jbWzqysbs;
	}

	public void setJbWzqysbs(String jbWzqysbs) {
		this.jbWzqysbs = jbWzqysbs;
	}

	public String getJbZrrsbsbz() {
		return jbZrrsbsbz;
	}

	public void setJbZrrsbsbz(String jbZrrsbsbz) {
		this.jbZrrsbsbz = jbZrrsbsbz;
	}

	public String getJbZrrsbs() {
		return jbZrrsbs;
	}

	public void setJbZrrsbs(String jbZrrsbs) {
		this.jbZrrsbs = jbZrrsbs;
	}
}
