package com.zjhc.gzwcq.job.zjh.mapper;

import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.ErrorInfo;
import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfo;
import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfoParam;
import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfoVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
@Repository
public interface IRequestResultInfoMapper {
	
	/*保存对象*/
	void insert(RequestResultInfo requestResultInfo);
	void insertErrorInfo(ErrorInfo errorInfo);
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(RequestResultInfo requestResultInfo);
	String selectOrgInfo(@Param("xybm")String xybm);
	/**更新*/
	void update(RequestResultInfo requestResultInfo);
	
	/*分页查询对象*/
	List<RequestResultInfoVo> queryRequestResultInfoForList(RequestResultInfoParam requestResultInfoParam);
	
	/*数据总量查询*/
	long queryTotalRequestResultInfos(RequestResultInfoParam requestResultInfoParam);
	
	/*根据主键查询对象*/
	RequestResultInfo selectRequestResultInfoByPrimaryKey(RequestResultInfo requestResultInfo);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<RequestResultInfo> selectForList();
	
	/**
	 * 数据唯一性验证
	 * */
	List<RequestResultInfo> selectForUnique(RequestResultInfo requestResultInfo);
	
}