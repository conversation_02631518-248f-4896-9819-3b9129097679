package com.zjhc.gzwcq.job.zjh.service.impl;

import com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectCompleteParam;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectCompleteVo;
import com.zjhc.gzwcq.job.zjh.mapper.IExtProjectCompleteMapper;
import com.zjhc.gzwcq.job.zjh.service.api.IExtProjectCompleteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class ExtProjectCompleteServiceImpl implements IExtProjectCompleteService {
	
	@Autowired
	private IExtProjectCompleteMapper extProjectCompleteMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(ExtProjectComplete extProjectComplete){
		extProjectComplete.setCreateTime(new Date());//创建时间
		extProjectComplete.setLastUpdateTime(new Date());//更新时间
		extProjectCompleteMapper.insert(extProjectComplete);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		extProjectCompleteMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		extProjectCompleteMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(ExtProjectComplete extProjectComplete){
		extProjectComplete.setLastUpdateTime(new Date());//更新时间
		extProjectCompleteMapper.updateIgnoreNull(extProjectComplete);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(ExtProjectComplete extProjectComplete){
		extProjectComplete.setLastUpdateTime(new Date());//更新时间
		extProjectCompleteMapper.update(extProjectComplete);
	}
	
	public List<ExtProjectCompleteVo> queryExtProjectCompleteByPage(ExtProjectCompleteParam extProjectCompleteParam) {
      	//分页
      		return extProjectCompleteMapper.queryExtProjectCompleteForList(extProjectCompleteParam);
	}
	

	public ExtProjectComplete selectExtProjectCompleteByPrimaryKey(ExtProjectComplete ExtProjectComplete) {
		return extProjectCompleteMapper.selectExtProjectCompleteByPrimaryKey(ExtProjectComplete);
	}
	
	public long queryTotalExtProjectCompletes(ExtProjectCompleteParam extProjectCompleteParam) {
		return extProjectCompleteMapper.queryTotalExtProjectCompletes(extProjectCompleteParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<ExtProjectComplete> selectForList(ExtProjectComplete extProjectComplete){
		return extProjectCompleteMapper.selectForList(extProjectComplete);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(ExtProjectComplete extProjectComplete) {
		return extProjectCompleteMapper.selectForUnique(extProjectComplete).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(ExtProjectComplete extProjectComplete) {
      	if(extProjectComplete.getId() == null) {
			this.insert(extProjectComplete);
		}else {
			this.updateIgnoreNull(extProjectComplete);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(ExtProjectComplete[] objs) {
		for(ExtProjectComplete extProjectComplete : objs) {
			this.saveOne(extProjectComplete);
		}
	}
}
