<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IHhqyMapper">

	<resultMap type="com.zjhc.gzwcq.job.zjh.entity.Hhqy" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="hh_company_name" property="hhCompanyName"/>
		<result column="hh_zxswhhr" property="hhZxswhhr"/>
		<result column="hh_zxswhhr_code" property="hhZxswhhrCode"/>
		<result column="hh_qx" property="hhQx"/>
		<result column="hh_zyjycs" property="hhZyjycs"/>
		<result column="hh_sfsmtzjj" property="hhSfsmtzjj"/>
		<result column="hh_jyfw" property="hhJyfw"/>
		<result column="hh_rjcze" property="hhRjcze"/>
		<result column="hh_rjczebz" property="hhRjczebz"/>
		<result column="hh_sjcze" property="hhSjcze"/>
		<result column="hh_sjczebz" property="hhSjczebz"/>
		<result column="hh_gjczqy" property="hhGjczqy"/>
		<result column="hh_gjczqy_code" property="hhGjczqyCode"/>
		<result column="hh_czqy" property="hhCzqy"/>
		<result column="hh_czqy_code" property="hhCzqyCode"/>
		<result column="hh_hhxy" property="hhHhxy"/>
		<result column="hjrjcze" property="hjrjcze"/>
		<result column="hjrjczbl" property="hjrjczbl"/>
		<result column="hjsjcze" property="hjsjcze"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="hh_czqyid" property="hhCzqyId"/>
		<result column="hh_sszb" property="hhSszb"/>
		<result column="hh_rjczermb" property="hhRjczermb"/>
		<result column="hh_sjczermb" property="hhSjczermb"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.job.zjh.entity.HhqyVo" extends="baseResultMap">
		<result column="hhGjczqyStr" property="hhGjczqyStr"/>
		<result column="hhSfsmtzjjStr" property="hhSfsmtzjjStr"/>
		<result column="hhRjczebzStr" property="hhRjczebzStr"/>
		<result column="hhSjczebzStr" property="hhSjczebzStr"/>
		<result column="hhHhxyStr" property="hhHhxyStr"/>
		<result column="hhCzqyStr" property="hhCzqyStr"/>
		<result column="hhRjczebzStr" property="hhRjczebzStr"/>
		<result column="hhSjczebzStr" property="hhSjczebzStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		hh_company_name, 
		hh_zxswhhr,
		hh_zxswhhr_code, 
		hh_qx,
		hh_zyjycs, 
		hh_sfsmtzjj, 
		hh_jyfw, 
		hh_rjcze, 
		hh_rjczebz, 
		hh_sjcze, 
		hh_sjczebz, 
		hh_gjczqy, 
		hh_gjczqy_code, 
		hh_czqy, 
		hh_czqy_code, 
		hh_hhxy, 
		hjrjcze, 
		hjrjczbl, 
		hjsjcze, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time,
		hh_czqyid,
		hh_sszb,
		hh_rjczermb,
		hh_sjczermb
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.hh_company_name, 
		t.hh_zxswhhr,
		t.hh_zxswhhr_code, 
		t.hh_qx,
		t.hh_zyjycs, 
		t.hh_sfsmtzjj, 
		t.hh_jyfw, 
		t.hh_rjcze, 
		t.hh_rjczebz, 
		t.hh_sjcze, 
		t.hh_sjczebz, 
		t.hh_gjczqy, 
		t.hh_gjczqy_code, 
		t.hh_czqy, 
		t.hh_czqy_code, 
		t.hh_hhxy, 
		t.hjrjcze, 
		t.hjrjczbl, 
		t.hjsjcze, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time,
		t.hh_czqyid,
		t.hh_sszb,
		t.hh_rjczermb,
		t.hh_sjczermb
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{hhCompanyName}, 
		#{hhZxswhhr},
		#{hhZxswhhrCode}, 
		#{hhQx},
		#{hhZyjycs}, 
		#{hhSfsmtzjj}, 
		#{hhJyfw}, 
		#{hhRjcze}, 
		#{hhRjczebz}, 
		#{hhSjcze}, 
		#{hhSjczebz}, 
		#{hhGjczqy}, 
		#{hhGjczqyCode}, 
		#{hhCzqy}, 
		#{hhCzqyCode}, 
		#{hhHhxy}, 
		#{hjrjcze}, 
		#{hjrjczbl}, 
		#{hjsjcze}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime},
		#{hhCzqyId},
		#{hhSszb},
		#{hhRjczermb},
		#{hhSjczermb}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="hhCompanyName != null and hhCompanyName != ''">
			and t.hh_company_name = #{hhCompanyName}
		</if>
		<if test="hhZxswhhr != null and hhZxswhhr != ''">
			and t.hh_zxswhhr = #{hhZxswhhr}
		</if>
		<if test="hhZxswhhrCode != null and hhZxswhhrCode != ''">
			and t.hh_zxswhhr_code = #{hhZxswhhrCode}
		</if>
		<if test="hhQx != null and hhQx != ''">
			and t.hh_qx = #{hhQx}
		</if>
		<if test="hhZyjycs != null and hhZyjycs != ''">
			and t.hh_zyjycs = #{hhZyjycs}
		</if>
		<if test="hhSfsmtzjj != null and hhSfsmtzjj != ''">
			and t.hh_sfsmtzjj = #{hhSfsmtzjj}
		</if>
		<if test="hhJyfw != null and hhJyfw != ''">
			and t.hh_jyfw = #{hhJyfw}
		</if>
		<if test="hhRjcze != null">
			and t.hh_rjcze = #{hhRjcze}
		</if>
		<if test="hhRjczebz != null and hhRjczebz != ''">
			and t.hh_rjczebz = #{hhRjczebz}
		</if>
		<if test="hhSjcze != null">
			and t.hh_sjcze = #{hhSjcze}
		</if>
		<if test="hhSjczebz != null and hhSjczebz != ''">
			and t.hh_sjczebz = #{hhSjczebz}
		</if>
		<if test="hhGjczqy != null and hhGjczqy != ''">
			and t.hh_gjczqy = #{hhGjczqy}
		</if>
		<if test="hhGjczqyCode != null and hhGjczqyCode != ''">
			and t.hh_gjczqy_code = #{hhGjczqyCode}
		</if>
		<if test="hhCzqy != null and hhCzqy != ''">
			and t.hh_czqy = #{hhCzqy}
		</if>
		<if test="hhCzqyCode != null and hhCzqyCode != ''">
			and t.hh_czqy_code = #{hhCzqyCode}
		</if>
		<if test="hhHhxy != null and hhHhxy != ''">
			and t.hh_hhxy = #{hhHhxy}
		</if>
		<if test="hjrjcze != null">
			and t.hjrjcze = #{hjrjcze}
		</if>
		<if test="hjrjczbl != null">
			and t.hjrjczbl = #{hjrjczbl}
		</if>
		<if test="hjsjcze != null">
			and t.hjsjcze = #{hjsjcze}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="hhCzqyId != null and hhCzqyId != ''">
			and t.hh_czqyid = #{hhCzqyId}
		</if>
		<if test="hhSszb != null and hhSszb != ''">
			and t.hh_sszb = #{hhSszb}
		</if>
	</sql>

	<resultMap id="resultMapWithJyfw" type="com.zjhc.gzwcq.job.zjh.entity.HhqyVo" extends="baseResultMapExt">
		<collection property="hhJyfwList" ofType="java.lang.String">
			<result column="hhJyfwList"/>
		</collection>
	</resultMap>
	<select id="selectByJbxxbId" parameterType="java.lang.String" resultMap="resultMapWithJyfw">
		select
		<include refid="columnsAlias"/>,
		sa.id a_id,sa.file_name a_file_name,sa.last_file_name a_last_file_name,
		sa.file_type a_file_type,sa.ftp_file_path a_ftp_file_path,sa.org_id a_org_id,
		sa.remark a_remark,sa.create_user a_create_user,sa.create_time a_create_time,
		sa.last_update_user a_last_update_user,sa.last_update_time a_last_update_time,
		sd1.text hhGjczqyStr,sd2.text hhSfsmtzjjStr,sd3.text hhRjczebzStr,
		sd4.text hhSjczebzStr,sa.file_name hhHhxyStr,
		sd5.text hhJyfwList,sa.file_name hhHhxyStr,
		concat(so1.organization_code,' ',so1.organization_name) hhCzqyStr,
		j.JB_JNJW as jbJnjw
		from cq_hhqy t join cq_jbxxb j on j.ID = t.jbxx_id
		left join sys_attachment sa on t.hh_hhxy = sa.id
		left join sys_dictionary sd1 on sd1.val=t.hh_gjczqy and sd1.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		left join sys_dictionary sd2 on sd2.val=t.hh_sfsmtzjj and sd2.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd3 on sd3.val=t.hh_rjczebz and sd3.type_id=(select id from sys_dictionary where type_code='RJCZEBC')
		left join sys_dictionary sd4 on sd4.val=t.hh_sjczebz and sd4.type_id=(select id from sys_dictionary where type_code='RJCZEBC')
		left join sys_dictionary sd5 on FIND_IN_SET(sd5.val,t.HH_JYFW) and sd5.type_id=(select id from sys_dictionary where type_code='INDUSTRY_CLASSIFICATION_TREE')
		left join sys_organization so1 on so1.organization_id = t.HH_CZQYID
		where t.jbxx_id=#{jbxxId}
	</select>

	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			hh_company_name,
			hh_zxswhhr,
			hh_zxswhhr_code,
			hh_qx,
			hh_zyjycs,
			hh_sfsmtzjj,
			hh_jyfw,
			hh_rjcze,
			hh_rjczebz,
			hh_sjcze,
			hh_sjczebz,
			hh_gjczqy,
			hh_gjczqy_code,
			hh_czqy,
			hh_czqy_code,
			hh_hhxy,
			hjrjcze,
			hjrjczbl,
			hjsjcze,
			hh_czqyid,
			hh_sszb
		FROM `cq_hhqy`
		WHERE
			JBXX_ID = #{jbxxId}
	</select>
</mapper>