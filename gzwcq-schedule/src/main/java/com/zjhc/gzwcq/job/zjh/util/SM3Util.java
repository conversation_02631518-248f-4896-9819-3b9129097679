package com.zjhc.gzwcq.job.zjh.util;

import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.util.encoders.Hex;

public class SM3Util {

    public static final String SM3Encode(String str) {
        byte[] bytes = str.getBytes();
        return SM3Encode(bytes);
    }

    public static String SM3Encode(byte[] bytes) {
        byte[] md = new byte[32];
        SM3Digest sm3 = new SM3Digest();
        sm3.update(bytes, 0, bytes.length);
        sm3.doFinal(md, 0);
        String s = new String(Hex.encode(md));
        return s;
    }

    public static void main(String[] args){
        System.out.println(SM3Util.SM3Encode(("admin111111")));
    }
}
