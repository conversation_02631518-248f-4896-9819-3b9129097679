package com.zjhc.gzwcq.job.zjh.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogsVo;
import com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogsParam;
import org.apache.ibatis.annotations.Param;

public interface IApiCallLogsMapper {
	
	/*保存对象*/
	void insert(ApiCallLogs apiCallLogs);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(ApiCallLogs apiCallLogs);
	
	/**更新*/
	void update(ApiCallLogs apiCallLogs);
	
	/*分页查询对象*/
	List<ApiCallLogsVo> queryApiCallLogsForList(ApiCallLogsParam apiCallLogsParam);
	
	/*数据总量查询*/
	long queryTotalApiCallLogss(ApiCallLogsParam apiCallLogsParam);
	
	/*根据主键查询对象*/
	ApiCallLogs selectApiCallLogsByPrimaryKey(ApiCallLogs apiCallLogs);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<ApiCallLogs> selectForList(ApiCallLogs apiCallLogs);
	
	/**
	 * 数据唯一性验证
	 * */
	List<ApiCallLogs> selectForUnique(ApiCallLogs apiCallLogs);
	
}