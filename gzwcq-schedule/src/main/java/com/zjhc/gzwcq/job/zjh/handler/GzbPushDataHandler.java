package com.zjhc.gzwcq.job.zjh.handler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjhc.gzwcq.job.zjh.service.api.IPushDataService;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/06:15:10:37
 **/
@Component
@Slf4j
public class GzbPushDataHandler {
    private static Logger logger = LoggerFactory.getLogger(GzbPushDataHandler.class);

    @Autowired
    private IPushDataService service;
    @XxlJob(value = "GzbPushDataHandler", init = "init", destroy = "destroy")
//    @PostConstruct
    public void pushDataService() {
        logger.info("开始推数据至国务院");
        long startTime = System.currentTimeMillis();
        service.pushDataService();
        long endTime = System.currentTimeMillis();
        logger.info("推数据至国务院结束用时：" + (endTime - startTime) /1000 + "秒");
    }

    @XxlJob(value = "pushDataServiceSendFTP", init = "init", destroy = "destroy")
//    @PostConstruct
    public void pushDataServiceSendFTP() {
        logger.info("开始生成国资委zib 文件");
        long startTime = System.currentTimeMillis();
        service.pushDataServiceSendFTP();
        long endTime = System.currentTimeMillis();
        logger.info("始生成国资委zib 文件用时：" + (endTime - startTime) /1000 + "秒");
    }

    @XxlJob(value = "jbxxbDataDispose", init = "init", destroy = "destroy")
    public void jbxxbDataDispose() {
        logger.info("处理数据");
        long startTime = System.currentTimeMillis();
        service.jbxxbDataDispose();
        long endTime = System.currentTimeMillis();
        logger.info("处理数据：" + (endTime - startTime) /1000 + "秒");
    }
//    @PostConstruct
    @XxlJob(value = "selectFileStatus", init = "init", destroy = "destroy")
    public void selectFileStatus() {
        logger.info("开始查询国资委文件处理结果");
        long startTime = System.currentTimeMillis();
        service.selectFileStatus();
        long endTime = System.currentTimeMillis();
        logger.info("开始查询国资委文件处理结果：" + (endTime - startTime) /1000 + "秒");
    }

    public void init(){
        logger.info("init");
    }

    public void destroy(){
        logger.info("destroy");
    }
}
