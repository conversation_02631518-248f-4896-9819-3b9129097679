package com.zjhc.gzwcq.job.zjh.service.api;

import com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectCompleteParam;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectCompleteVo;

import java.util.List;
import java.util.Map;

public interface IExtProjectCompleteService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(ExtProjectComplete extProjectComplete);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(ExtProjectComplete extProjectComplete);
	
	/**
	* 更新
	*/
	void update(ExtProjectComplete extProjectComplete);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<ExtProjectCompleteVo> queryExtProjectCompleteByPage(ExtProjectCompleteParam extProjectCompleteParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalExtProjectCompletes(ExtProjectCompleteParam extProjectCompleteParam);
  
	
	/**
	 *通过ID查询数据
	 */
	ExtProjectComplete selectExtProjectCompleteByPrimaryKey(ExtProjectComplete extProjectComplete);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<ExtProjectComplete> selectForList(ExtProjectComplete extProjectComplete);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(ExtProjectComplete extProjectComplete);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(ExtProjectComplete extProjectComplete);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(ExtProjectComplete[] objs);
	
}