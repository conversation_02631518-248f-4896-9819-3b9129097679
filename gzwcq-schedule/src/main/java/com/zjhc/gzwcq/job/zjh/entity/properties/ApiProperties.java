package com.zjhc.gzwcq.job.zjh.entity.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:10:13:34
 **/
public class ApiProperties {
    private String username;
    private String password;
    private String loginUrl;
    private String prefixUrl;
    private String projectUrl;
    private String projectTransfereeUrl;
    public static final String OK_STATUS = "200";

    public String getProjectTransfereeUrl() {
        return projectTransfereeUrl;
    }

    public void setProjectTransfereeUrl(String projectTransfereeUrl) {
        this.projectTransfereeUrl = projectTransfereeUrl;
    }

    public String getProjectUrl() {
        return projectUrl;
    }

    public void setProjectUrl(String projectUrl) {
        this.projectUrl = projectUrl;
    }

    public String getPrefixUrl() {
        return prefixUrl;
    }

    public void setPrefixUrl(String prefixUrl) {
        this.prefixUrl = prefixUrl;
    }

    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
