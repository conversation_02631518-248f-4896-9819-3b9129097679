<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IGsInfoMapper">
	<resultMap type="com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfo" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="ywid" property="ywid"/>
		<result column="cqqymc" property="cqqymc"/>
		<result column="gsqymc" property="gsqymc"/>
		<result column="xybm" property="xybm"/>
		<result column="qylx" property="qylx"/>
		<result column="bwbmc" property="bwbmc"/>
		<result column="zczbbwb" property="zczbbwb"/>
		<result column="zczbrmb" property="zczbrmb"/>
		<result column="zcrq" property="zcrq"/>
		<result column="zhusuo" property="zhusuo"/>
		<result column="jyzt" property="jyzt"/>
		<result column="jyfw" property="jyfw"/>
		<result column="ywrq" property="ywrq"/>
		<result column="ywlx" property="ywlx"/>
		<result column="sjrq" property="sjrq"/>
		<result column="yuliu1" property="yuliu1"/>
		<result column="yuliu2" property="yuliu2"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		ywid, 
		cqqymc, 
		gsqymc, 
		xybm, 
		qylx, 
		bwbmc, 
		zczbbwb, 
		zczbrmb, 
		zcrq, 
		zhusuo, 
		jyzt, 
		jyfw, 
		ywrq, 
		ywlx, 
		sjrq, 
		yuliu1, 
		yuliu2
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.ywid, 
		t.cqqymc, 
		t.gsqymc, 
		t.xybm, 
		t.qylx, 
		t.bwbmc, 
		t.zczbbwb, 
		t.zczbrmb, 
		t.zcrq, 
		t.zhusuo, 
		t.jyzt, 
		t.jyfw, 
		t.ywrq, 
		t.ywlx, 
		t.sjrq, 
		t.yuliu1, 
		t.yuliu2
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{ywid}, 
		#{cqqymc}, 
		#{gsqymc}, 
		#{xybm}, 
		#{qylx}, 
		#{bwbmc}, 
		#{zczbbwb}, 
		#{zczbrmb}, 
		#{zcrq}, 
		#{zhusuo}, 
		#{jyzt}, 
		#{jyfw}, 
		#{ywrq}, 
		#{ywlx}, 
		#{sjrq}, 
		#{yuliu1}, 
		#{yuliu2}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null">
			and t.id = #{id}
		</if>
		<if test="ywid != null and ywid != ''">
			and t.ywid = #{ywid}
		</if>
		<if test="cqqymc != null and cqqymc != ''">
			and t.cqqymc = #{cqqymc}
		</if>
		<if test="gsqymc != null and gsqymc != ''">
			and t.gsqymc = #{gsqymc}
		</if>
		<if test="xybm != null and xybm != ''">
			and t.xybm = #{xybm}
		</if>
		<if test="qylx != null and qylx != ''">
			and t.qylx = #{qylx}
		</if>
		<if test="bwbmc != null and bwbmc != ''">
			and t.bwbmc = #{bwbmc}
		</if>
		<if test="zczbbwb != null">
			and t.zczbbwb = #{zczbbwb}
		</if>
		<if test="zczbrmb != null">
			and t.zczbrmb = #{zczbrmb}
		</if>
		<if test="zcrq != null">
			and t.zcrq = #{zcrq}
		</if>
		<if test="zhusuo != null and zhusuo != ''">
			and t.zhusuo = #{zhusuo}
		</if>
		<if test="jyzt != null and jyzt != ''">
			and t.jyzt = #{jyzt}
		</if>
		<if test="jyfw != null and jyfw != ''">
			and t.jyfw = #{jyfw}
		</if>
		<if test="ywrq != null">
			and t.ywrq = #{ywrq}
		</if>
		<if test="ywlx != null and ywlx != ''">
			and t.ywlx = #{ywlx}
		</if>
		<if test="sjrq != null">
			and t.sjrq = #{sjrq}
		</if>
		<if test="yuliu1 != null and yuliu1 != ''">
			and t.yuliu1 = #{yuliu1}
		</if>
		<if test="yuliu2 != null and yuliu2 != ''">
			and t.yuliu2 = #{yuliu2}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfo" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into scjd_gs_info (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	<insert id="insertForList">
		insert into scjd_gs_info(<include refid="columns" />)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.id},
			#{item.ywid},
			#{item.cqqymc},
			#{item.gsqymc},
			#{item.xybm},
			#{item.qylx},
			#{item.bwbmc},
			#{item.zczbbwb},
			#{item.zczbrmb},
			#{item.zcrq},
			#{item.zhusuo},
			#{item.jyzt},
			 #{item.jyfw},
			 #{item.ywrq},
			 #{item.ywlx},
			 #{item.sjrq},
			 #{item.yuliu1},
			 #{item.yuliu2}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		cqqymc=VALUES(cqqymc),
		gsqymc=VALUES(gsqymc),
		xybm=VALUES(xybm),
		qylx=VALUES(qylx),
		bwbmc=VALUES(bwbmc),
		zczbbwb=VALUES(zczbbwb),
		zczbrmb=VALUES(zczbrmb),
		zcrq=VALUES(zcrq),
		zhusuo=VALUES(zhusuo),
		jyzt=VALUES(jyzt),
		jyfw=VALUES(jyfw),
		ywrq=VALUES(ywrq),
		ywlx=VALUES(ywlx),
		sjrq=VALUES(sjrq),
		yuliu1=VALUES(yuliu1),
		yuliu2=VALUES(yuliu2)
	</insert>
	<sql id="investorsColumns">
		id,
		ywid,
		xybm,
		czrlb,
		czrmc,
		czrzzlx,
		czrbm,
		bwbmc,
		rjzbbwb,
		rjzbrmb,
		gqbl,
		yuliu1,
		yuliu2
	</sql>
	<insert id="insertForInvestorsList">
		insert into scjd_gs_info_investors(<include refid="investorsColumns" />)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.id},
			#{item.ywid},
			#{item.xybm},
			#{item.czrlb},
			#{item.czrmc},
			#{item.czrzzlx},
			#{item.czrbm},
			#{item.bwbmc},
			#{item.rjzbbwb},
			#{item.rjzbrmb},
			#{item.gqbl},
			#{item.yuliu1},
			#{item.yuliu2}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		xybm=VALUES(xybm),
		czrlb=VALUES(czrlb),
		czrmc=VALUES(czrmc),
		czrzzlx=VALUES(czrzzlx),
		czrbm=VALUES(czrbm),
		bwbmc=VALUES(bwbmc),
		rjzbbwb=VALUES(rjzbbwb),
		rjzbrmb=VALUES(rjzbrmb),
		gqbl=VALUES(gqbl),
		yuliu1=VALUES(yuliu1),
		yuliu2=VALUES(yuliu2)
	</insert>
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update scjd_gs_info set isDeleted = 'Y' where
		id in
		<foreach collection="gsInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update scjd_gs_info set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from scjd_gs_info  where
		id in
		<foreach collection="gsInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from scjd_gs_info  where id = #{id}
	</delete>
	
	<select id="selectGsInfoByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from scjd_gs_info
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update scjd_gs_info
		<set>
			<if test="ywid != null">
				ywid=#{ywid},
			</if>
			<if test="cqqymc != null">
				cqqymc=#{cqqymc},
			</if>
			<if test="gsqymc != null">
				gsqymc=#{gsqymc},
			</if>
			<if test="xybm != null">
				xybm=#{xybm},
			</if>
			<if test="qylx != null">
				qylx=#{qylx},
			</if>
			<if test="bwbmc != null">
				bwbmc=#{bwbmc},
			</if>
			<if test="zczbbwb != null">
				zczbbwb=#{zczbbwb},
			</if>
			<if test="zczbrmb != null">
				zczbrmb=#{zczbrmb},
			</if>
			<if test="zcrq != null">
				zcrq=#{zcrq},
			</if>
			<if test="zhusuo != null">
				zhusuo=#{zhusuo},
			</if>
			<if test="jyzt != null">
				jyzt=#{jyzt},
			</if>
			<if test="jyfw != null">
				jyfw=#{jyfw},
			</if>
			<if test="ywrq != null">
				ywrq=#{ywrq},
			</if>
			<if test="ywlx != null">
				ywlx=#{ywlx},
			</if>
			<if test="sjrq != null">
				sjrq=#{sjrq},
			</if>
			<if test="yuliu1 != null">
				yuliu1=#{yuliu1},
			</if>
			<if test="yuliu2 != null">
				yuliu2=#{yuliu2}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update scjd_gs_info
		<set>
			ywid=#{ywid},
			cqqymc=#{cqqymc},
			gsqymc=#{gsqymc},
			xybm=#{xybm},
			qylx=#{qylx},
			bwbmc=#{bwbmc},
			zczbbwb=#{zczbbwb},
			zczbrmb=#{zczbrmb},
			zcrq=#{zcrq},
			zhusuo=#{zhusuo},
			jyzt=#{jyzt},
			jyfw=#{jyfw},
			ywrq=#{ywrq},
			ywlx=#{ywlx},
			sjrq=#{sjrq},
			yuliu1=#{yuliu1},
			yuliu2=#{yuliu2}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfo" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			scjd_gs_info t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalGsInfos" parameterType="com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoParam" resultType="java.lang.Long">
		select
			count(id)
		from scjd_gs_info t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryGsInfoForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			scjd_gs_info t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfo" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from scjd_gs_info t
		where t.id != #{id}
			<if test="ywid != null and ywid != ''">
				and t.ywid = #{ywid}
			</if>
			<if test="cqqymc != null and cqqymc != ''">
				and t.cqqymc = #{cqqymc}
			</if>
			<if test="gsqymc != null and gsqymc != ''">
				and t.gsqymc = #{gsqymc}
			</if>
			<if test="xybm != null and xybm != ''">
				and t.xybm = #{xybm}
			</if>
			<if test="qylx != null and qylx != ''">
				and t.qylx = #{qylx}
			</if>
			<if test="bwbmc != null and bwbmc != ''">
				and t.bwbmc = #{bwbmc}
			</if>
			<if test="zczbbwb != null and zczbbwb != ''">
				and t.zczbbwb = #{zczbbwb}
			</if>
			<if test="zczbrmb != null and zczbrmb != ''">
				and t.zczbrmb = #{zczbrmb}
			</if>
			<if test="zcrq != null">
				and t.zcrq = #{zcrq}
			</if>
			<if test="zhusuo != null and zhusuo != ''">
				and t.zhusuo = #{zhusuo}
			</if>
			<if test="jyzt != null and jyzt != ''">
				and t.jyzt = #{jyzt}
			</if>
			<if test="jyfw != null and jyfw != ''">
				and t.jyfw = #{jyfw}
			</if>
			<if test="ywrq != null">
				and t.ywrq = #{ywrq}
			</if>
			<if test="ywlx != null and ywlx != ''">
				and t.ywlx = #{ywlx}
			</if>
			<if test="sjrq != null">
				and t.sjrq = #{sjrq}
			</if>
			<if test="yuliu1 != null and yuliu1 != ''">
				and t.yuliu1 = #{yuliu1}
			</if>
			<if test="yuliu2 != null and yuliu2 != ''">
				and t.yuliu2 = #{yuliu2}
			</if>
	</select>

</mapper>