package com.zjhc.gzwcq.job.zjh.util;

import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:09:49:59
 **/
public class HttpUtils {
    /**
     * 发送GET请求
     *
     * @param url    请求的URL地址
     * @param params 请求参数
     * @return 获取到的响应字符串
     */
    public static String doGet(String url, Map<String, String> params) {
        // 定义一个字符串来保存响应内容
        String result = "";
        // 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            // 创建URIBuilder对象，设置请求的URL、请求参数
            URIBuilder builder = new URIBuilder(url);
            // 如果存在请求参数，则将其添加到builder对象中
            if (params != null) {
                for (String key : params.keySet()) {
                    builder.addParameter(key, params.get(key));
                }
            }
            // 创建HttpGet对象，设置URI
            HttpGet httpGet = new HttpGet(builder.build());
            // 执行get请求
            HttpResponse response = httpClient.execute(httpGet);
            // 如果成功获取响应的状态码
            if (response.getStatusLine().getStatusCode() == 200) {
                // 获取响应内容
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    // 使用EntityUtils工具类将响应实体转换为字符串
                    result = EntityUtils.toString(entity, "UTF-8");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                // 关闭HttpClient对象
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 发送POST请求
     *
     * @param url    请求的URL地址
     * @param params 请求参数
     * @return 获取到的响应字符串
     */
    public static String doPost(String url, Map<String, String> params) {
        // 定义一个字符串来保存响应内容
        String result = "";
        // 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(url);
            // 创建请求参数列表
            List<NameValuePair> parameters = new ArrayList<>();
            // 如果存在请求参数，则将其添加到parameters列表中
            if (params != null) {
                for (String key : params.keySet()) {
                    parameters.add(new BasicNameValuePair(key, params.get(key)));
                }
            }
            // 创建UrlEncodedFormEntity对象
            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(parameters, "UTF-8");
            // 将请求参数绑定到HttpPost对象中
            httpPost.setEntity(formEntity);
            // 执行post请求
            HttpResponse response = httpClient.execute(httpPost);
            // 如果成功获取响应的状态码
            if (response.getStatusLine().getStatusCode() == 200) {
                // 获取响应内容
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    // 使用EntityUtils工具类将响应实体转换为字符串
                    result = EntityUtils.toString(entity, "UTF-8");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                // 关闭HttpClient对象
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 发送GET请求
     *
     * @param url     请求地址
     * @param headers 请求头部信息
     * @return 响应结果
     * @throws IOException
     * @throws ClientProtocolException
     */
    public static String sendGet(String url, Map<String, String> headers) throws IOException, ClientProtocolException {
        HttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        configHeaders(httpGet, headers);
        HttpResponse response = httpClient.execute(httpGet);
        return getResponseBody(response);
    }

    /**
     * 发送POST请求
     *
     * @param url     请求地址
     * @param params  请求参数，格式为JSON字符串
     * @param headers 请求头部信息
     * @return 响应结果
     * @throws IOException
     * @throws ClientProtocolException
     */
    public static String sendPost(String url, String params, Map<String, String> headers) throws IOException, ClientProtocolException {
        HttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        configHeaders(httpPost, headers);
        StringEntity entity = new StringEntity(params, "UTF-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        HttpResponse response = httpClient.execute(httpPost);
        return getResponseBody(response);
    }

    /**
     * 发送PUT请求
     *
     * @param url     请求地址
     * @param params  请求参数，格式为JSON字符串
     * @param headers 请求头部信息
     * @return 响应结果
     * @throws IOException
     * @throws ClientProtocolException
     */
    public static String sendPut(String url, String params, Map<String, String> headers) throws IOException, ClientProtocolException {
        HttpClient httpClient = HttpClients.createDefault();
        HttpPut httpPut = new HttpPut(url);
        configHeaders(httpPut, headers);
        StringEntity entity = new StringEntity(params, "UTF-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPut.setEntity(entity);
        HttpResponse response = httpClient.execute(httpPut);
        return getResponseBody(response);
    }

    /**
     * 发送DELETE请求
     *
     * @param url     请求地址
     * @param headers 请求头部信息
     * @return 响应结果
     * @throws IOException
     * @throws ClientProtocolException
     */
    public static String sendDelete(String url, Map<String, String> headers) throws IOException, ClientProtocolException {
        HttpClient httpClient = HttpClients.createDefault();
        HttpDelete httpDelete = new HttpDelete(url);
        configHeaders(httpDelete, headers);
        HttpResponse response = httpClient.execute(httpDelete);
        return getResponseBody(response);
    }

    /**
     * 对HTTP请求添加请求头部信息
     *
     * @param request HTTP请求对象
     * @param headers 请求头部信息
     */
    private static void configHeaders(HttpRequestBase request, Map<String, String> headers) {
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                request.setHeader(key, value);
            }
        }
    }

    /**
     * 获取HTTP响应结果
     *
     * @param response HTTP响应对象
     * @return 响应结果字符串
     * @throws IOException
     */
    private static String getResponseBody(HttpResponse response) throws IOException {
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            return EntityUtils.toString(entity, "UTF-8");
        }
        return null;
    }

    public static void main(String[] args) {
        // 发送GET请求
//        String result1 = HttpUtils.doGet("https://www.baidu.com", null);
//        System.out.println(result1);
        // 发送POST请求
        Map<String, String> params = new HashMap<>();
        params.put("username", "em5mYw==");
        params.put("password", "F+rIw7bt19UWab6uVfUCiQ==");
        String toJSONString = JSON.toJSONString(params);
        String result2 = HttpUtils.doPost("http://60.191.19.181:5050/login?loginUrl=/login", params);
        try {
            String sendPost = HttpUtils.sendPost("http://60.191.19.181:5050/login?loginUrl=/login", toJSONString, null);
            System.out.println("sendPost = " + sendPost);
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println(result2);
    }
}
