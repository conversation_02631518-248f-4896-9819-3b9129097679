package com.zjhc.gzwcq.job.zjh.util;

import com.zjhc.gzwcq.job.zjh.entity.BusiInfo;
import net.lingala.zip4j.exception.ZipException;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/05/23:11:08:48
 **/
public class XmlZipConverterUtils<T> {
    public static MultipartFile convertToMultipartFile(List<File> xmlFiles,String fileName) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ZipOutputStream zipOut = new ZipOutputStream(byteArrayOutputStream);
        for (File xmlFile : xmlFiles) {
            FileInputStream fileInputStream = new FileInputStream(xmlFile);
            zipOut.putNextEntry(new ZipEntry(xmlFile.getName()));

            byte[] buffer = new byte[1024];
            int length;
            while ((length = fileInputStream.read(buffer)) > 0) {
                zipOut.write(buffer, 0, length);
            }
            fileInputStream.close();
            zipOut.closeEntry();
        }

        zipOut.close();
        byte[] zipBytes = byteArrayOutputStream.toByteArray();
        fileName = fileName +".zip";
        return new MockMultipartFile(fileName, fileName, "application/zip", zipBytes);
    }
    public static Node convertToNode(Object object) throws Exception {
        JAXBContext context = JAXBContext.newInstance(object.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

        // 创建包含XML声明的初始Document
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        Document doc = dbf.newDocumentBuilder().newDocument();

        // 将Java对象转换到doc
        marshaller.marshal(object, doc);

        return doc.getDocumentElement();
    }

    public static File convertJavaToXml(Object javaObject, Transformer transformer,String fileName) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        transformer.transform(new DOMSource(convertToNode(javaObject)), new StreamResult(byteArrayOutputStream));
        byte[] xmlBytes = byteArrayOutputStream.toByteArray();
        // 使用Transformer添加缩进和换行
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        File xmlFile = File.createTempFile(fileName, ".xml");
//        File xmlFile = new File(fileName+".xml");
        try (FileOutputStream fileOutputStream = new FileOutputStream(xmlFile)) {
            fileOutputStream.write(xmlBytes);
        }

        return xmlFile;
    }
    public static File convertJavaToXmlAndSaveToLocal(Object javaObject, Transformer transformer, String localFilePath) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        transformer.transform(new DOMSource(convertToNode(javaObject)), new StreamResult(byteArrayOutputStream));
        byte[] xmlBytes = byteArrayOutputStream.toByteArray();
        // 使用Transformer添加缩进和换行
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
           // 创建或覆盖指定路径的文件
        File xmlFile = new File(localFilePath);
        try (FileOutputStream fileOutputStream = new FileOutputStream(xmlFile)) {
            fileOutputStream.write(xmlBytes);
        }

        return xmlFile;
    }

    // 假设这是Java对象
    @XmlRootElement
    public static class JavaObject {
        private String name = "John Doe";

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }



    /**
     * 示例转换方法  T 为 后续的实体类
     * @param javaObject
     * @return
     * @throws Exception
     */
    public static MultipartFile getMultipartFile( BusiInfo javaObject,String password) throws Exception {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        // 添加这行来确保XML声明被包含
        transformer.setOutputProperty(OutputKeys.VERSION, "1.0");
        transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
        transformer.setOutputProperty(OutputKeys.STANDALONE, "yes");
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");

        List<File> xmlFiles = new ArrayList<>();
            // 填充对象数据
            File xmlFile = convertJavaToXml(javaObject, transformer,"data");
            //查看单个生成的xml里的信息
//            convertJavaToXmlAndSaveToLocal(javaObject, transformer, "D:\\javaCode\\git\\gzwcq\\gzwcq-schedule\\src\\main\\java\\com\\zjhc\\gzwcq\\job\\zjh\\util\\temp.xml");
            xmlFiles.add(xmlFile);

    //不加密的
//        MultipartFile zipFile = convertToMultipartFile(xmlFiles,"data");
        //加密的
        MultipartFile zipFile = encryptZip2(xmlFiles, password);
        return zipFile;

    }

    public static MultipartFile encryptZip2(List<File> xmlFiles,String password) throws IOException, ZipException {
        Path tempFile = null;
        File encryptedZipFile = null;
        byte[] encryptedBytes;
        try {
            // 创建临时文件
            tempFile = Files.createTempFile("data", ".zip");

            // 创建输出的加密 ZIP 文件
            encryptedZipFile = new File(tempFile.toFile().getParent(), "data.zip");

            // 使用 Zip4j 创建 ZIP 输出流
            ZipFile zip = new ZipFile(encryptedZipFile);

            // 设置压缩参数
            ZipParameters parameters = new ZipParameters();
            parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
            parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);
            parameters.setEncryptFiles(true);
            parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_STANDARD);
            parameters.setAesKeyStrength(Zip4jConstants.AES_STRENGTH_256);
            parameters.setPassword(password);
            for (File path : xmlFiles) {
                // 加密整个 ZIP 文件
                zip.addFile(path, parameters);
                path.delete();
            }

            // 读取生成的加密 ZIP 文件内容
            encryptedBytes = Files.readAllBytes(encryptedZipFile.toPath());
        } finally {
            if(tempFile != null)
            Files.delete(tempFile);
            if(encryptedZipFile != null)
            Files.delete(encryptedZipFile.toPath());
        }



        // 创建一个新的 MultipartFile 对象
        MockMultipartFile encryptedMultipartFile = new MockMultipartFile(
                "data",  // 名称
                "data.zip",  // 原始文件名
                "application/zip",  // MIME 类型
                encryptedBytes  // 内容
        );
        return encryptedMultipartFile;
    }
}
