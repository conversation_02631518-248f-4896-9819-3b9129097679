package com.zjhc.gzwcq.job.zjh.handler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjhc.gzwcq.job.zjh.entity.enums.WarnModelEnum;
import com.zjhc.gzwcq.job.zjh.service.api.IGjPushMsgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/27:14:55:19
 **/
@Component
public class GjPushMsgHandler {
    private static Logger logger = LoggerFactory.getLogger(GjPushMsgHandler.class);
    @Autowired
    private IGjPushMsgService  gjPushMsgService;

    /**
     * @description 预警模型1推送
     * <AUTHOR>
     * @date 2025/7/31 11:22
     */
    @XxlJob(value = "gjPushMsgHandler")
    public void pushMsg1() {
        logger.info("开始推送消息");
        long startTime = System.currentTimeMillis();
        try {
            gjPushMsgService.pushMsg(WarnModelEnum.MODEL_1);
        } catch (Exception e) {
            logger.error("推送消息未知异常：" + e.getMessage());
        }
        long endTime = System.currentTimeMillis();
        logger.info("推送消息结束 用时：" + (endTime - startTime) / 1000);
    }

    /**
     * @description 预警模型2推送
     * <AUTHOR>
     * @date 2025/7/31 11:22
     */
    @XxlJob(value = "gjPushMsgHandler2")
    public void pushMsg2() {
        logger.info("开始推送模型2消息");
        XxlJobHelper.log("开始推送模型2消息");
        long startTime = System.currentTimeMillis();
        try {
            gjPushMsgService.pushMsg(WarnModelEnum.MODEL_2);
        } catch (Exception e) {
            logger.error("推送消息未知异常：" + e.getMessage());
            XxlJobHelper.log("推送消息未知异常：" + e.getMessage());
        }
        long endTime = System.currentTimeMillis();
        logger.info("推送消息结束 用时：" + (endTime - startTime) / 1000);
        XxlJobHelper.log("推送消息结束 用时：" + (endTime - startTime) / 1000);
    }
}
