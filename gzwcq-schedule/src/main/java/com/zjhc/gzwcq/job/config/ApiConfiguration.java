package com.zjhc.gzwcq.job.config;

import com.zjhc.gzwcq.job.zjh.entity.properties.ApiGwyPushProperties;
import com.zjhc.gzwcq.job.zjh.entity.properties.ApiProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:10:11:53
 **/
@Configuration
public class ApiConfiguration {
    @Bean
    @ConfigurationProperties(prefix = "com.api.config")
    public ApiProperties myConfig() {
        return new ApiProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "com.api.push.config")
    public ApiGwyPushProperties pushDataConfig() {
        return new ApiGwyPushProperties();
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
