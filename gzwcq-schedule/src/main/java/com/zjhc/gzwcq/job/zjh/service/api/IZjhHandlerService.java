package com.zjhc.gzwcq.job.zjh.service.api;

import com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.job.zjh.entity.ResultEntity;
import com.zjhc.gzwcq.job.zjh.entity.api.DTO.ExtProjectTransfereeDTO;
import com.zjhc.gzwcq.job.zjh.entity.api.DTO.ProjectCompleteDTO;
import com.zjhc.gzwcq.job.zjh.entity.api.VO.ResultLoginVO;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:11:13:22
 **/
public interface IZjhHandlerService {
    /**
     * 登录浙交汇获取token
     *
     * @param
     * @param
     * @return
     */
    ResultEntity<ResultLoginVO> login() throws IOException;

    List<ExtProjectComplete> extProjectCompleteData(ProjectCompleteDTO projectCompleteDTO) throws IOException;

    List<ExtProjectTransferee> extProjectTransfereeData(List<ExtProjectComplete> list, ProjectCompleteDTO projectCompleteDTO) throws IOException;
}
