package com.zjhc.gzwcq.job.zjh.mapper;

import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeParam;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
@Repository
public interface IExtProjectTransfereeMapper {
	
	/*保存对象*/
	void insert(ExtProjectTransferee extProjectTransferee);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(ExtProjectTransferee extProjectTransferee);
	
	/**更新*/
	void update(ExtProjectTransferee extProjectTransferee);
	
	/*分页查询对象*/
	List<ExtProjectTransfereeVo> queryExtProjectTransfereeForList(ExtProjectTransfereeParam extProjectTransfereeParam);
	
	/*数据总量查询*/
	long queryTotalExtProjectTransferees(ExtProjectTransfereeParam extProjectTransfereeParam);
	
	/*根据主键查询对象*/
	ExtProjectTransferee selectExtProjectTransfereeByPrimaryKey(ExtProjectTransferee extProjectTransferee);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<ExtProjectTransferee> selectForList(ExtProjectTransferee extProjectTransferee);
	
	/**
	 * 数据唯一性验证
	 * */
	List<ExtProjectTransferee> selectForUnique(ExtProjectTransferee extProjectTransferee);

	Long selectCount();

	void insertOrUpdate(@Param("list") List<ExtProjectTransferee> list);
	
}