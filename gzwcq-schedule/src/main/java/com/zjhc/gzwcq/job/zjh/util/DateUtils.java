package com.zjhc.gzwcq.job.zjh.util;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:09:56:42
 **/
public class DateUtils {
    public final static String DATE_PATTERN_YYYYMMDD = "yyyyMMdd";
    public final static String DATE_PATTERN_YYYY_MM_DD = "yyyy-MM-dd";
    public final static String DATE_PATTERN_YYYY_MM_DD_HH_MM_SS = "YYYY-MM-DD HH:mm:ss";
    public final static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public final static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    /**
     * 将日期转换为指定格式的字符串
     *
     * @param date    日期对象
     * @param pattern 日期格式，如"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期字符串
     */
    public static String format(Date date, String pattern) {
        DateFormat df = new SimpleDateFormat(pattern);
        return df.format(date);
    }

    /**
     * 将指定格式的字符串转换为日期对象
     *
     * @param dateStr 日期字符串
     * @param pattern 日期格式，如"yyyy-MM-dd HH:mm:ss"
     * @return 转换后的日期对象
     */
    public static Date parse(String dateStr, String pattern) throws ParseException {
        DateFormat df = new SimpleDateFormat(pattern);
        return df.parse(dateStr);
    }

    public static List<String> getAllDatesInRange(String startDateStr, String endDateStr,String pattern,String outPattern) {
        List<String> allDates = new ArrayList<>();
        DateFormat df = new SimpleDateFormat(pattern);
        DateFormat outDf = new SimpleDateFormat(outPattern);
        try {
            Date startDate = df.parse(startDateStr);
            Date endDate = df.parse(endDateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            while (!calendar.getTime().after(endDate)) {
                allDates.add(outDf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return allDates;
    }
    /**
     * 计算两个日期之间的天数差
     *
     * @param date1 第一个日期对象
     * @param date2 第二个日期对象
     * @return 日期差值
     */
    public static int daysBetween(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        long t1 = cal1.getTimeInMillis();
        long t2 = cal2.getTimeInMillis();
        return (int) ((t2 - t1) / (1000 * 3600 * 24));
    }

    /**
     * 获取日期
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> getDatesBetween(String startDate, String endDate, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDate start = LocalDate.parse(startDate,formatter);
        LocalDate end = LocalDate.parse(endDate,formatter);
        List<String> dates = new ArrayList<>();
        while (!start.isAfter(end)) {
            dates.add(start.format(formatter));
            start = start.plusDays(1);
        }
        return dates;
    }

    /**
     * 将日期增加指定天数
     *
     * @param date 日期对象
     * @param days 增加的天数
     * @return 增加后的日期对象
     */
    public static Date addDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        return cal.getTime();
    }

    /**
     * 将日期增加指定小时数
     *
     * @param date  日期对象
     * @param hours 增加的小时数
     * @return 增加后的日期对象
     */
    public static Date addHours(Date date, int hours) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR_OF_DAY, hours);
        return cal.getTime();
    }

    /**
     * 将日期增加指定分钟数
     *
     * @param date    日期对象
     * @param minutes 增加的分钟数
     * @return 增加后的日期对象
     */
    public static Date addMinutes(Date date, int minutes) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, minutes);
        return cal.getTime();
    }

    /**
     * 将日期增加指定秒数
     *
     * @param date    日期对象
     * @param seconds 增加的秒数
     * @return 增加后的日期对象
     */
    public static Date addSeconds(Date date, int seconds) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.SECOND, seconds);
        return cal.getTime();
    }
}
