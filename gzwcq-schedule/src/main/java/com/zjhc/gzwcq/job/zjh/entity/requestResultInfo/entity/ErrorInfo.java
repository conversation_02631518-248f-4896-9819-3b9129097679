package com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/07/05:17:19:40
 **/
public class ErrorInfo {
    private short errorIndex;
    private String errorType;
    private String errorLevel;
    private String processMode;
    private String qymc;
    private String xybm;
    private String errorMessage;
    private String orgId;
    private Long apiRequestInfoId;

    public Long getApiRequestInfoId() {
        return apiRequestInfoId;
    }

    public void setApiRequestInfoId(Long apiRequestInfoId) {
        this.apiRequestInfoId = apiRequestInfoId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public short getErrorIndex() {
        return errorIndex;
    }

    public void setErrorIndex(short errorIndex) {
        this.errorIndex = errorIndex;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorLevel() {
        return errorLevel;
    }

    public void setErrorLevel(String errorLevel) {
        this.errorLevel = errorLevel;
    }

    public String getProcessMode() {
        return processMode;
    }

    public void setProcessMode(String processMode) {
        this.processMode = processMode;
    }

    public String getQymc() {
        return qymc;
    }

    public void setQymc(String qymc) {
        this.qymc = qymc;
    }

    public String getXybm() {
        return xybm;
    }

    public void setXybm(String xybm) {
        this.xybm = xybm;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
