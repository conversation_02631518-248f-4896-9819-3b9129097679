package com.zjhc.gzwcq.job.zjh.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ext_project_transferee <br/>
 *         描述：项目受让方 <br/>
 */
public class ExtProjectTransfereeVo extends ExtProjectTransferee {

	private static final long serialVersionUID = 18L;

	private List<ExtProjectTransfereeVo> extProjectTransfereeList;

	public ExtProjectTransfereeVo() {
		super();
	}

  	public ExtProjectTransfereeVo(Long id) {
  		super();
  		this.id = id;
	}

	public List<ExtProjectTransfereeVo> getExtProjectTransfereeList() {
		return extProjectTransfereeList;
	}

	public void setExtProjectTransfereeList(List<ExtProjectTransfereeVo> extProjectTransfereeList) {
		this.extProjectTransfereeList = extProjectTransfereeList;
	}

}
