package com.zjhc.gzwcq.job.zjh.handler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjhc.gzwcq.job.zjh.service.api.IGsInfoService;
import com.zjhc.gzwcq.job.zjh.util.DateUtils;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/08/19:16:36:45
 **/
@Component
@Slf4j
public class GetGsInfoHandler {
    private static Logger logger = LoggerFactory.getLogger(GzbPushDataHandler.class);

    @Autowired
    private IGsInfoService service;

    @XxlJob(value = "QlGsInfoFullHandler", init = "init", destroy = "destroy")
    public void getQlGsInfoFull() {
        logger.info("开始全量同步工商数据");
        long startTime = System.currentTimeMillis();
        try {
            service.getQlGsInfo("2023-01-01 00:00:00", DateUtils.format(DateUtils.addDays(new Date(), -1), DateUtils.DATE_PATTERN_YYYY_MM_DD));
        } catch (Exception e) {
            logger.error("全量同步工商数据未知异常：" + e.getMessage());
        }
        long endTime = System.currentTimeMillis();
        logger.info("全量同步工商数据结束 用时：" + (endTime - startTime) / 1000);
    }

    @XxlJob(value = "QlGsInfoHandler", init = "init", destroy = "destroy")
    public void getQlGsInfo() {
        logger.info("开始增量同步工商数据");
        long startTime = System.currentTimeMillis();
        Date date = DateUtils.addDays(new Date(), -1);
        String end = DateUtils.format(new Date(), DateUtils.DATE_PATTERN_YYYY_MM_DD);
        try {
            service.getQlGsInfo(DateUtils.format(date, DateUtils.DATE_PATTERN_YYYY_MM_DD), end);
        } catch (Exception e) {
            logger.error("增量同步工商数据未知异常：" + e.getMessage());
        }
        long endTime = System.currentTimeMillis();
        logger.info("增量同步数据结束 用时：" + (endTime - startTime) / 1000);
    }


    public void init() {
        logger.info("init");
    }

    public void destroy() {
        logger.info("destroy");
    }
}
