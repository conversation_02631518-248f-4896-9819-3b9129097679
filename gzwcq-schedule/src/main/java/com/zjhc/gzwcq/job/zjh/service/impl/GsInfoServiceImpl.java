package com.zjhc.gzwcq.job.zjh.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.zjhc.gzwcq.job.zjh.entity.getGsInfo.GetGsInfoResultInfo;
import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfo;
import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoParam;
import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoVo;
import com.zjhc.gzwcq.job.zjh.entity.gsInfoInvestors.entity.GsInfoInvestors;
import com.zjhc.gzwcq.job.zjh.entity.properties.ApiGwyPushProperties;
import com.zjhc.gzwcq.job.zjh.mapper.IGsInfoMapper;
import com.zjhc.gzwcq.job.zjh.service.api.IGsInfoService;
import com.zjhc.gzwcq.job.zjh.util.DateUtils;
import com.zjhc.gzwcq.job.zjh.util.HttpUtils;
import com.zjhc.gzwcq.job.zjh.util.SM3Util;
import io.netty.util.internal.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.http.client.methods.HttpPost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GsInfoServiceImpl implements IGsInfoService {

    @Autowired
    private IGsInfoMapper gsInfoMapper;
    @Autowired
    private ApiGwyPushProperties pushProperties;

    @Transactional(rollbackFor = Exception.class)
    public void insert(GsInfo gsInfo) {
        gsInfoMapper.insert(gsInfo);
    }

    @Override
    public void getQlGsInfo(String startTime, String endTime) throws ParseException {
        List<String> datesInRange = DateUtils.getAllDatesInRange(startTime, endTime, DateUtils.DATE_PATTERN_YYYY_MM_DD,DateUtils.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        startTime =  DateUtils.format(DateUtils.parse(startTime,DateUtils.DATE_PATTERN_YYYY_MM_DD),DateUtils.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        for (String end : datesInRange) {
            List<GsInfo> gsInfos = gsInfos(startTime, end);
			this.saveGsInfo(gsInfos);
        }
    }

    private List<GsInfo> gsInfos(String startTime, String endTime) {
        List<GsInfo> vo = new ArrayList<>();
        String gsInfoUrl = pushProperties.getGetGsInfoUrl();
        String appId = pushProperties.getAppId();
        String appSecret = pushProperties.getAppSecret();
        Map<String, String> httpPost = new HashMap<>();
        String rid = UUID.randomUUID().toString().replaceAll("-", "");
        Long fileSize = 0L;
        long ts = System.currentTimeMillis();
        String sign = SM3Util.SM3Encode(appSecret + rid + ts + (fileSize == null || fileSize == 0 ? "" : fileSize));
        // 设置请求头
        httpPost.put("Accept", "application/json");
        httpPost.put("ts", String.valueOf(ts));
        httpPost.put("rid", rid);
        httpPost.put("sign", sign);
        httpPost.put("appId", appId);
        httpPost.put("appSecret", appSecret);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("startTime", startTime);
        jsonObject.put("endTime", endTime);
        String params = jsonObject.toJSONString();

        try {
            String json = HttpUtils.sendPost(gsInfoUrl, params, httpPost);
            GetGsInfoResultInfo<GsInfo> gsInfos = JSON.parseObject(json, new TypeReference<GetGsInfoResultInfo<GsInfo>>() {
            });
            if (!gsInfos.getCode().isEmpty() && Objects.nonNull(gsInfos.getData()))
                vo.addAll(gsInfos.getData());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return vo;
    }

    private void saveGsInfo(List<GsInfo> gsInfos) {
		List<GsInfoInvestors> investors = gsInfos.stream().map(GsInfo::getInvestors).flatMap(List::stream).collect(Collectors.toList());
		int batchSize = 5000;
		int investorsSize = investors.size();
		int size = gsInfos.size();
		//基本信息
        for (int number = 0; number < size; number += batchSize) {
            int endIndex = Math.min(number + batchSize, size);
			List<GsInfo> gsInfos1 = gsInfos.subList(number, endIndex);
			gsInfoMapper.insertForList(gsInfos1);
		}
		//出资人信息
		for (int number = 0; number < investorsSize; number += batchSize) {
			int endIndex = Math.min(number + batchSize, investorsSize);
			List<GsInfoInvestors> gsInfoInvestors = investors.subList(number, endIndex);
			gsInfoMapper.insertForInvestorsList(gsInfoInvestors);
		}

    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKeys(Map<String, Object> map) {
        gsInfoMapper.deleteByPrimaryKeys(map);
    }

    /**
     * 删除一个对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKey(String id) {
        gsInfoMapper.deleteByPrimaryKey(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateIgnoreNull(GsInfo gsInfo) {
        gsInfoMapper.updateIgnoreNull(gsInfo);
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(GsInfo gsInfo) {
        gsInfoMapper.update(gsInfo);
    }

    public List<GsInfoVo> queryGsInfoByPage(GsInfoParam gsInfoParam) {
        //分页
        PageHelper.startPage(gsInfoParam.getPageNumber(), gsInfoParam.getLimit(), false);
        return gsInfoMapper.queryGsInfoForList(gsInfoParam);
    }


    public GsInfo selectGsInfoByPrimaryKey(GsInfo GsInfo) {
        return gsInfoMapper.selectGsInfoByPrimaryKey(GsInfo);
    }

    public long queryTotalGsInfos(GsInfoParam gsInfoParam) {
        return gsInfoMapper.queryTotalGsInfos(gsInfoParam);
    }

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    public List<GsInfo> selectForList(GsInfo gsInfo) {
        return gsInfoMapper.selectForList(gsInfo);
    }

    /**
     * 数据唯一性验证
     */
    @Override
    public boolean validateUniqueParam(GsInfo gsInfo) {
        return gsInfoMapper.selectForUnique(gsInfo).size() == 0;
    }

    /**
     * 保存单个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOne(GsInfo gsInfo) {
        if (gsInfo.getId() == null) {
            this.insert(gsInfo);
        } else {
            this.updateIgnoreNull(gsInfo);
        }
    }

    /**
     * 保存多个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void multipleSaveAndEdit(GsInfo[] objs) {
        for (GsInfo gsInfo : objs) {
            this.saveOne(gsInfo);
        }
    }
}
