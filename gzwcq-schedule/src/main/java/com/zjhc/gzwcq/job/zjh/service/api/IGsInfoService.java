package com.zjhc.gzwcq.job.zjh.service.api;

import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfo;
import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoParam;
import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoVo;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface IGsInfoService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(GsInfo gsInfo);

	void  getQlGsInfo(String startTime,String endTime) throws ParseException;
	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(GsInfo gsInfo);
	
	/**
	* 更新
	*/
	void update(GsInfo gsInfo);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<GsInfoVo> queryGsInfoByPage(GsInfoParam gsInfoParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalGsInfos(GsInfoParam gsInfoParam);
  
	
	/**
	 *通过ID查询数据
	 */
	GsInfo selectGsInfoByPrimaryKey(GsInfo gsInfo);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<GsInfo> selectForList(GsInfo gsInfo);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(GsInfo gsInfo);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(GsInfo gsInfo);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(GsInfo[] objs);
	
}