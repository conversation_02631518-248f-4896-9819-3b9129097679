package com.zjhc.gzwcq.job.zjh.util;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/06/13 17:02:56
 **/
@Slf4j
public class GjPushOAuth2TokenUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final RestTemplate restTemplate;
    private final String authServerUrl; // 授权服务器地址
    private final String appId;
    private final String appSecret;

    public GjPushOAuth2TokenUtil(RestTemplate restTemplate, String authServerUrl, String appId, String appSecret) {
        this.restTemplate = restTemplate;
        this.authServerUrl = authServerUrl;
        this.appId = appId;
        this.appSecret = appSecret;
    }

    public static void main(String[] args) {
        try {
            Map<String, Object> accessToken = new GjPushOAuth2TokenUtil(new RestTemplate(), "http://**********:18082/api/dmp/oauth/client/token",
                    "tkd8ew84hzzczwrg", "tvmpaq1noml7zpakyl4wdr79pivbuu31ju93tdib8ljla6qp").getAccessToken();
            System.out.println("accessToken = " + accessToken.get("access_token"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取完整的 token 响应数据
     *
     * @return 包含 access_token 等信息的 Map 对象
     */
    public Map<String, Object> getAccessToken() throws Exception {
        // 构建请求头中的 Authorization: Basic base64encode(appId:appSecret)
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization", "Basic " + encodeCredentials(appId, appSecret));

        HttpEntity<String> entity = new HttpEntity<>(headers);
        //打一个请求参数的日志
        log.info("获取鉴权请求参数:{}",entity);
        ResponseEntity<String> responseEntity = restTemplate.exchange(
                authServerUrl,
                HttpMethod.POST,
                entity,
                String.class
        );

        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            String responseBody = responseEntity.getBody();

            // 解析 JSON 响应
            Map<String, Object> responseMap = objectMapper.readValue(responseBody, HashMap.class);

            Boolean success = (Boolean) responseMap.get("success");
            if (Boolean.TRUE.equals(success)) {
                return (Map<String, Object>) responseMap.get("data");
            } else {
                String msg = (String) responseMap.get("msg");
                throw new RuntimeException("获取 Token 失败: " + msg);
            }
        } else {
            throw new RuntimeException("HTTP 请求失败: " + responseEntity.getStatusCode());
        }
    }

    /**
     * 使用 Base64 编码 appId:appSecret
     */
    private String encodeCredentials(String appId, String appSecret) {
        String credentials = appId + ":" + appSecret;
        byte[] encodedBytes = Base64.getEncoder().encode(credentials.getBytes(StandardCharsets.UTF_8));
        return new String(encodedBytes, StandardCharsets.UTF_8);
    }

    // ---- 提取常用字段 ---- //

    public String getAccessTokenString() throws Exception {
        return (String) getAccessToken().get("access_token");
    }

    public String getUserName() throws Exception {
        return (String) getAccessToken().get("user_name");
    }

    public String getAccount() throws Exception {
        return (String) getAccessToken().get("account");
    }

    public String getPublicKey() throws Exception {
        return (String) getAccessToken().get("public_key");
    }

    public Integer getExpiresIn() throws Exception {
        return (Integer) getAccessToken().get("expires_in");
    }
}

