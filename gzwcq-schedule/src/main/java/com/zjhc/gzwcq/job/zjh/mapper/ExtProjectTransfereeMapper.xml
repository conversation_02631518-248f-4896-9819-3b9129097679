<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IExtProjectTransfereeMapper">

    <resultMap type="com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee" id="baseResultMap">
        <id column="id" property="id"/>
        <result column="xmlx" property="xmlx"/>
        <result column="xmbh" property="xmbh"/>
        <result column="xmmc" property="xmmc"/>
        <result column="khzjhm" property="khzjhm"/>
        <result column="khqc" property="khqc"/>
        <result column="mobile" property="mobile"/>
        <result column="djsj" property="djsj"/>
        <result column="sfyx" property="sfyx"/>
        <result column="zt" property="zt"/>
        <result column="yxffj" property="yxffj"/>
        <result column="fatt" property="fatt"/>
        <result column="create_time" property="createTime"/>
        <result column="last_update_time" property="lastUpdateTime"/>
    </resultMap>

    <resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeVo"
               extends="baseResultMap">
    </resultMap>

    <sql id="columns">
        id,
		xmlx, 
		xmbh, 
		xmmc, 
		khzjhm, 
		khqc, 
		mobile, 
		djsj, 
		sfyx, 
		zt, 
		yxffj, 
		fatt, 
		create_time, 
		last_update_time
    </sql>
    <sql id="insetColumns">
		xmlx,
		xmbh,
		xmmc,
		khzjhm,
		khqc,
		mobile,
		djsj,
		sfyx,
		zt,
		yxffj,
		fatt
    </sql>

    <!--带别名的列-->
    <sql id="columnsAlias">
        t.id,
		t.xmlx, 
		t.xmbh, 
		t.xmmc, 
		t.khzjhm, 
		t.khqc, 
		t.mobile, 
		t.djsj, 
		t.sfyx, 
		t.zt, 
		t.yxffj, 
		t.fatt, 
		t.create_time, 
		t.last_update_time
    </sql>

    <sql id="vals">
        #{id},
        #{xmlx},
        #{xmbh},
        #{xmmc},
        #{khzjhm},
        #{khqc},
        #{mobile},
        #{djsj},
        #{sfyx},
        #{zt},
        #{yxffj},
        #{fatt},
        #{createTime},
        #{lastUpdateTime}
    </sql>
    <sql id="insetVals">
        #{item.xmlx},
        #{item.xmbh},
        #{item.xmmc},
        #{item.khzjhm},
        #{item.khqc},
        #{item.mobile},
        #{item.djsj},
        #{item.sfyx},
        #{item.zt},
        #{item.yxffj},
        #{item.fatt}
    </sql>

    <!-- 给where查询的表起别名t,方便多表关联查询 -->
    <sql id="whereSql">
        <if test="id != null">
            and t.id = #{id}
        </if>
        <if test="xmlx != null and xmlx != ''">
            and t.xmlx = #{xmlx}
        </if>
        <if test="xmbh != null and xmbh != ''">
            and t.xmbh = #{xmbh}
        </if>
        <if test="xmmc != null and xmmc != ''">
            and t.xmmc = #{xmmc}
        </if>
        <if test="khzjhm != null and khzjhm != ''">
            and t.khzjhm = #{khzjhm}
        </if>
        <if test="khqc != null and khqc != ''">
            and t.khqc = #{khqc}
        </if>
        <if test="mobile != null and mobile != ''">
            and t.mobile = #{mobile}
        </if>
        <if test="djsj != null and djsj != ''">
            and t.djsj = #{djsj}
        </if>
        <if test="sfyx != null">
            and t.sfyx = #{sfyx}
        </if>
        <if test="zt != null">
            and t.zt = #{zt}
        </if>
        <if test="yxffj != null and yxffj != ''">
            and t.yxffj = #{yxffj}
        </if>
        <if test="fatt != null and fatt != ''">
            and t.fatt = #{fatt}
        </if>
        <if test="createTime != null">
            and t.create_time = #{createTime}
        </if>
        <if test="lastUpdateTime != null">
            and t.last_update_time = #{lastUpdateTime}
        </if>
    </sql>


    <insert id="insert" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee" useGeneratedKeys="true"
            keyProperty="id">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
        insert into cq_ext_project_transferee (<include refid="columns"/>)
        values (<include refid="vals"/>)
    </insert>

    <!-- 逻辑删除 -->
    <update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
        update cq_ext_project_transferee set isDeleted = 'Y' where
        id in
        <foreach collection="extProjectTransferees" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
        update cq_ext_project_transferee
        set isDeleted = 'Y'
        where id = #{id}
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
        delete from cq_ext_project_transferee where
        id in
        <foreach collection="extProjectTransferees" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from cq_ext_project_transferee
        where id = #{id}
    </delete>

    <select id="selectExtProjectTransfereeByPrimaryKey" resultMap="baseResultMapExt">
        select
        <include refid="columns"/>
        from cq_ext_project_transferee
        where id=#{id}
    </select>

    <update id="updateIgnoreNull">
        update cq_ext_project_transferee
        <set>
            <if test="xmlx != null">
                xmlx=#{xmlx},
            </if>
            <if test="xmbh != null">
                xmbh=#{xmbh},
            </if>
            <if test="xmmc != null">
                xmmc=#{xmmc},
            </if>
            <if test="khzjhm != null">
                khzjhm=#{khzjhm},
            </if>
            <if test="khqc != null">
                khqc=#{khqc},
            </if>
            <if test="mobile != null">
                mobile=#{mobile},
            </if>
            <if test="djsj != null">
                djsj=#{djsj},
            </if>
            <if test="sfyx != null">
                sfyx=#{sfyx},
            </if>
            <if test="zt != null">
                zt=#{zt},
            </if>
            <if test="yxffj != null">
                yxffj=#{yxffj},
            </if>
            <if test="fatt != null">
                fatt=#{fatt},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="lastUpdateTime != null">
                last_update_time=#{lastUpdateTime}
            </if>
        </set>
        where id=#{id}
    </update>

    <!-- 更新 -->
    <update id="update">
        update cq_ext_project_transferee
        <set>
            xmlx=#{xmlx},
            xmbh=#{xmbh},
            xmmc=#{xmmc},
            khzjhm=#{khzjhm},
            khqc=#{khqc},
            mobile=#{mobile},
            djsj=#{djsj},
            sfyx=#{sfyx},
            zt=#{zt},
            yxffj=#{yxffj},
            fatt=#{fatt},
            create_time=#{createTime},
            last_update_time=#{lastUpdateTime}
        </set>
        where id=#{id}
    </update>


    <!-- 根据部分属性对象查询全部结果，不分页 -->
    <select id="selectForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee"
            resultMap="baseResultMapExt">
        select
        <include refid="columnsAlias"/>
        from
        cq_ext_project_transferee t
        where 1 = 1
        <include refid="whereSql"/>
    </select>

    <select id="queryTotalExtProjectTransferees" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeParam"
            resultType="java.lang.Long">
        select
        count(id)
        from cq_ext_project_transferee t
        where 1=1
        <include refid="whereSql"/>
    </select>

    <!-- 列表页查询 -->
    <select id="queryExtProjectTransfereeForList"
            parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeParam" resultMap="baseResultMapExt">
        select
        <include refid="columnsAlias"/>
        from
        cq_ext_project_transferee t
        where 1=1
        <include refid="whereSql"/>
    </select>

    <!-- 根据唯一性参数查询数据 -->
    <select id="selectForUnique" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee"
            resultMap="baseResultMapExt">
        select
        <include refid="columnsAlias"/>
        from cq_ext_project_transferee t
        where t.id != #{id}
        <if test="xmlx != null and xmlx != ''">
            and t.xmlx = #{xmlx}
        </if>
        <if test="xmbh != null and xmbh != ''">
            and t.xmbh = #{xmbh}
        </if>
        <if test="xmmc != null and xmmc != ''">
            and t.xmmc = #{xmmc}
        </if>
        <if test="khzjhm != null and khzjhm != ''">
            and t.khzjhm = #{khzjhm}
        </if>
        <if test="khqc != null and khqc != ''">
            and t.khqc = #{khqc}
        </if>
        <if test="mobile != null and mobile != ''">
            and t.mobile = #{mobile}
        </if>
        <if test="djsj != null and djsj != ''">
            and t.djsj = #{djsj}
        </if>
        <if test="sfyx != null">
            and t.sfyx = #{sfyx}
        </if>
        <if test="zt != null">
            and t.zt = #{zt}
        </if>
        <if test="yxffj != null and yxffj != ''">
            and t.yxffj = #{yxffj}
        </if>
        <if test="fatt != null and fatt != ''">
            and t.fatt = #{fatt}
        </if>
        <if test="createTime != null">
            and t.create_time = #{createTime}
        </if>
        <if test="lastUpdateTime != null">
            and t.last_update_time = #{lastUpdateTime}
        </if>
    </select>
    <select id="selectCount" resultType="long">
        select count(1)
        from cq_ext_project_transferee
    </select>
    <insert id="insertOrUpdate">
        <selectKey keyProperty="id" resultType="Long" order="AFTER">
            select last_insert_id()
        </selectKey>
        insert into cq_ext_project_transferee (<include refid="insetColumns"/>) values
        <foreach collection="list" item="item" separator=",">
            (<include refid="insetVals"/>)
        </foreach>
            on duplicate key update
        xmlx = values(xmlx),
        xmmc = values(xmmc),
        khqc = values(khqc),
        mobile = values(mobile),
        djsj = values(djsj),
        sfyx = values(sfyx),
        zt = values(zt),
        yxffj = values(yxffj),
        fatt = values(fatt)

    </insert>
</mapper>