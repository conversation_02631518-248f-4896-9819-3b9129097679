<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IExtProjectCompleteMapper">

    <resultMap type="com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete" id="baseResultMap">
        <id column="id" property="id"/>
        <result column="organization_id" property="organizationId"/>
        <result column="xmlx" property="xmlx"/>
        <result column="xmbh" property="xmbh"/>
        <result column="xmmc" property="xmmc"/>
        <result column="cjjg" property="cjjg"/>
        <result column="cjje" property="cjje"/>
        <result column="cjrq" property="cjrq"/>
        <result column="cjsj" property="cjsj"/>
        <result column="sjcjfs" property="sjcjfs"/>
        <result column="srfmc" property="srfmc"/>
        <result column="zrdj" property="zrdj"/>
        <result column="pgj" property="pgj"/>
        <result column="cgbl" property="cgbl"/>
        <result column="ggzyptbh" property="ggzyptbh"/>
        <result column="cljg" property="cljg"/>
        <result column="jgsm" property="jgsm"/>
        <result column="zccz_cjzq" property="zcczCjzq"/>
        <result column="bdcfczh" property="bdcfczh"/>
        <result column="jjjssj" property="jjjssj"/>
        <result column="srflxfs" property="srflxfs"/>
        <result column="fczh" property="fczh"/>
        <result column="create_time" property="createTime"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.job.zjh.entity.ExtProjectCompleteVo" extends="baseResultMap">
    </resultMap>

    <sql id="columns">
        id,
		organization_id, 
		xmlx, 
		xmbh, 
		xmmc, 
		cjjg, 
		cjje, 
		cjrq, 
		cjsj, 
		sjcjfs, 
		srfmc, 
		zrdj, 
		pgj, 
		cgbl, 
		ggzyptbh, 
		cljg, 
		jgsm, 
		zccz_cjzq, 
		bdcfczh, 
		jjjssj, 
		srflxfs, 
		fczh, 
		create_time, 
		last_update_time, 
		status
    </sql>

    <sql id="insetColumns">
		organization_id,
		xmlx,
		xmbh,
		xmmc,
		cjjg,
		cjje,
		cjrq,
		cjsj,
		sjcjfs,
		srfmc,
		zrdj,
		pgj,
		cgbl,
		ggzyptbh,
		cljg,
		jgsm,
		zccz_cjzq,
		bdcfczh,
		jjjssj,
		srflxfs,
		fczh
    </sql>
    <!--带别名的列-->
    <sql id="columnsAlias">
        t.id,
		t.organization_id, 
		t.xmlx, 
		t.xmbh, 
		t.xmmc, 
		t.cjjg, 
		t.cjje, 
		t.cjrq, 
		t.cjsj, 
		t.sjcjfs, 
		t.srfmc, 
		t.zrdj, 
		t.pgj, 
		t.cgbl, 
		t.ggzyptbh, 
		t.cljg, 
		t.jgsm, 
		t.zccz_cjzq, 
		t.bdcfczh, 
		t.jjjssj, 
		t.srflxfs, 
		t.fczh, 
		t.create_time, 
		t.last_update_time, 
		t.status
    </sql>

    <sql id="vals">
        #{id},
        #{organizationId},
        #{xmlx},
        #{xmbh},
        #{xmmc},
        #{cjjg},
        #{cjje},
        #{cjrq},
        #{cjsj},
        #{sjcjfs},
        #{srfmc},
        #{zrdj},
        #{pgj},
        #{cgbl},
        #{ggzyptbh},
        #{cljg},
        #{jgsm},
        #{zcczCjzq},
        #{bdcfczh},
        #{jjjssj},
        #{srflxfs},
        #{fczh},
        #{createTime},
        #{lastUpdateTime},
        #{status}
    </sql>
    <sql id="insetVals">
        #{item.organizationId},
        #{item.xmlx},
        #{item.xmbh},
        #{item.xmmc},
        #{item.cjjg},
        #{item.cjje},
        #{item.cjrq},
        #{item.cjsj},
        #{item.sjcjfs},
        #{item.srfmc},
        #{item.zrdj},
        #{item.pgj},
        #{item.cgbl},
        #{item.ggzyptbh},
        #{item.cljg},
        #{item.jgsm},
        #{item.zcczCjzq},
        #{item.bdcfczh},
        #{item.jjjssj},
        #{item.srflxfs},
        #{item.fczh}
    </sql>

    <!-- 给where查询的表起别名t,方便多表关联查询 -->
    <sql id="whereSql">
        <if test="id != null">
            and t.id = #{id}
        </if>
        <if test="organizationId != null and organizationId != ''">
            and t.organization_id = #{organizationId}
        </if>
        <if test="xmlx != null and xmlx != ''">
            and t.xmlx = #{xmlx}
        </if>
        <if test="xmbh != null and xmbh != ''">
            and t.xmbh = #{xmbh}
        </if>
        <if test="xmmc != null and xmmc != ''">
            and t.xmmc = #{xmmc}
        </if>
        <if test="cjjg != null and cjjg != ''">
            and t.cjjg = #{cjjg}
        </if>
        <if test="cjje != null">
            and t.cjje = #{cjje}
        </if>
        <if test="cjrq != null and cjrq != ''">
            and t.cjrq = #{cjrq}
        </if>
        <if test="cjsj != null and cjsj != ''">
            and t.cjsj = #{cjsj}
        </if>
        <if test="sjcjfs != null and sjcjfs != ''">
            and t.sjcjfs = #{sjcjfs}
        </if>
        <if test="srfmc != null and srfmc != ''">
            and t.srfmc = #{srfmc}
        </if>
        <if test="zrdj != null and zrdj != ''">
            and t.zrdj = #{zrdj}
        </if>
        <if test="pgj != null">
            and t.pgj = #{pgj}
        </if>
        <if test="cgbl != null">
            and t.cgbl = #{cgbl}
        </if>
        <if test="ggzyptbh != null and ggzyptbh != ''">
            and t.ggzyptbh = #{ggzyptbh}
        </if>
        <if test="cljg != null and cljg != ''">
            and t.cljg = #{cljg}
        </if>
        <if test="jgsm != null and jgsm != ''">
            and t.jgsm = #{jgsm}
        </if>
        <if test="zcczCjzq != null and zcczCjzq != ''">
            and t.zccz_cjzq = #{zcczCjzq}
        </if>
        <if test="bdcfczh != null and bdcfczh != ''">
            and t.bdcfczh = #{bdcfczh}
        </if>
        <if test="jjjssj != null and jjjssj != ''">
            and t.jjjssj = #{jjjssj}
        </if>
        <if test="srflxfs != null and srflxfs != ''">
            and t.srflxfs = #{srflxfs}
        </if>
        <if test="fczh != null and fczh != ''">
            and t.fczh = #{fczh}
        </if>
        <if test="createTime != null">
            and t.create_time = #{createTime}
        </if>
        <if test="lastUpdateTime != null">
            and t.last_update_time = #{lastUpdateTime}
        </if>
        <if test="status != null">
            and t.status = #{status}
        </if>
    </sql>


    <insert id="insert" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete" useGeneratedKeys="true"
            keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="AFTER">
            select last_insert_id()
        </selectKey>
        insert into cq_ext_project_complete (<include refid="columns"/>)
        values (<include refid="vals"/>)
    </insert>

    <!-- 逻辑删除 -->
    <update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
        update cq_ext_project_complete set isDeleted = 'Y' where
        id in
        <foreach collection="extProjectCompletes" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
        update cq_ext_project_complete
        set isDeleted = 'Y'
        where id = #{id}
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
        delete from cq_ext_project_complete where
        id in
        <foreach collection="extProjectCompletes" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from cq_ext_project_complete
        where id = #{id}
    </delete>

    <select id="selectExtProjectCompleteByPrimaryKey" resultMap="baseResultMapExt">
        select
        <include refid="columns"/>
        from cq_ext_project_complete
        where id=#{id}
    </select>

    <update id="updateIgnoreNull">
        update cq_ext_project_complete
        <set>
            <if test="organizationId != null">
                organization_id=#{organizationId},
            </if>
            <if test="xmlx != null">
                xmlx=#{xmlx},
            </if>
            <if test="xmbh != null">
                xmbh=#{xmbh},
            </if>
            <if test="xmmc != null">
                xmmc=#{xmmc},
            </if>
            <if test="cjjg != null">
                cjjg=#{cjjg},
            </if>
            <if test="cjje != null">
                cjje=#{cjje},
            </if>
            <if test="cjrq != null">
                cjrq=#{cjrq},
            </if>
            <if test="cjsj != null">
                cjsj=#{cjsj},
            </if>
            <if test="sjcjfs != null">
                sjcjfs=#{sjcjfs},
            </if>
            <if test="srfmc != null">
                srfmc=#{srfmc},
            </if>
            <if test="zrdj != null">
                zrdj=#{zrdj},
            </if>
            <if test="pgj != null">
                pgj=#{pgj},
            </if>
            <if test="cgbl != null">
                cgbl=#{cgbl},
            </if>
            <if test="ggzyptbh != null">
                ggzyptbh=#{ggzyptbh},
            </if>
            <if test="cljg != null">
                cljg=#{cljg},
            </if>
            <if test="jgsm != null">
                jgsm=#{jgsm},
            </if>
            <if test="zcczCjzq != null">
                zccz_cjzq=#{zcczCjzq},
            </if>
            <if test="bdcfczh != null">
                bdcfczh=#{bdcfczh},
            </if>
            <if test="jjjssj != null">
                jjjssj=#{jjjssj},
            </if>
            <if test="srflxfs != null">
                srflxfs=#{srflxfs},
            </if>
            <if test="fczh != null">
                fczh=#{fczh},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="lastUpdateTime != null">
                last_update_time=#{lastUpdateTime},
            </if>
            <if test="status != null">
                status=#{status}
            </if>
        </set>
        where id=#{id}
    </update>

    <!-- 更新 -->
    <update id="update">
        update cq_ext_project_complete
        <set>
            organization_id=#{organizationId},
            xmlx=#{xmlx},
            xmbh=#{xmbh},
            xmmc=#{xmmc},
            cjjg=#{cjjg},
            cjje=#{cjje},
            cjrq=#{cjrq},
            cjsj=#{cjsj},
            sjcjfs=#{sjcjfs},
            srfmc=#{srfmc},
            zrdj=#{zrdj},
            pgj=#{pgj},
            cgbl=#{cgbl},
            ggzyptbh=#{ggzyptbh},
            cljg=#{cljg},
            jgsm=#{jgsm},
            zccz_cjzq=#{zcczCjzq},
            bdcfczh=#{bdcfczh},
            jjjssj=#{jjjssj},
            srflxfs=#{srflxfs},
            fczh=#{fczh},
            create_time=#{createTime},
            last_update_time=#{lastUpdateTime},
            status=#{status}
        </set>
        where id=#{id}
    </update>


    <!-- 根据部分属性对象查询全部结果，不分页 -->
    <select id="selectForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete"
            resultMap="baseResultMapExt">
        select
        <include refid="columnsAlias"/>
        from
        cq_ext_project_complete t
        where 1 = 1
        <include refid="whereSql"/>
    </select>

    <select id="queryTotalExtProjectCompletes" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectCompleteParam"
            resultType="java.lang.Long">
        select
        count(id)
        from cq_ext_project_complete t
        where 1=1
        <include refid="whereSql"/>
    </select>

    <!-- 列表页查询 -->
    <select id="queryExtProjectCompleteForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectCompleteParam"
            resultMap="baseResultMapExt">
        select
        <include refid="columnsAlias"/>
        from
        cq_ext_project_complete t
        where 1=1
        <include refid="whereSql"/>
    </select>

    <!-- 根据唯一性参数查询数据 -->
    <select id="selectForUnique" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete"
            resultMap="baseResultMapExt">
        select
        <include refid="columnsAlias"/>
        from cq_ext_project_complete t
        where t.id != #{id}
        <if test="organizationId != null and organizationId != ''">
            and t.organization_id = #{organizationId}
        </if>
        <if test="xmlx != null and xmlx != ''">
            and t.xmlx = #{xmlx}
        </if>
        <if test="xmbh != null and xmbh != ''">
            and t.xmbh = #{xmbh}
        </if>
        <if test="xmmc != null and xmmc != ''">
            and t.xmmc = #{xmmc}
        </if>
        <if test="cjjg != null and cjjg != ''">
            and t.cjjg = #{cjjg}
        </if>
        <if test="cjje != null and cjje != ''">
            and t.cjje = #{cjje}
        </if>
        <if test="cjrq != null and cjrq != ''">
            and t.cjrq = #{cjrq}
        </if>
        <if test="cjsj != null and cjsj != ''">
            and t.cjsj = #{cjsj}
        </if>
        <if test="sjcjfs != null and sjcjfs != ''">
            and t.sjcjfs = #{sjcjfs}
        </if>
        <if test="srfmc != null and srfmc != ''">
            and t.srfmc = #{srfmc}
        </if>
        <if test="zrdj != null and zrdj != ''">
            and t.zrdj = #{zrdj}
        </if>
        <if test="pgj != null and pgj != ''">
            and t.pgj = #{pgj}
        </if>
        <if test="cgbl != null and cgbl != ''">
            and t.cgbl = #{cgbl}
        </if>
        <if test="ggzyptbh != null and ggzyptbh != ''">
            and t.ggzyptbh = #{ggzyptbh}
        </if>
        <if test="cljg != null and cljg != ''">
            and t.cljg = #{cljg}
        </if>
        <if test="jgsm != null and jgsm != ''">
            and t.jgsm = #{jgsm}
        </if>
        <if test="zcczCjzq != null and zcczCjzq != ''">
            and t.zccz_cjzq = #{zcczCjzq}
        </if>
        <if test="bdcfczh != null and bdcfczh != ''">
            and t.bdcfczh = #{bdcfczh}
        </if>
        <if test="jjjssj != null and jjjssj != ''">
            and t.jjjssj = #{jjjssj}
        </if>
        <if test="srflxfs != null and srflxfs != ''">
            and t.srflxfs = #{srflxfs}
        </if>
        <if test="fczh != null and fczh != ''">
            and t.fczh = #{fczh}
        </if>
        <if test="createTime != null">
            and t.create_time = #{createTime}
        </if>
        <if test="lastUpdateTime != null">
            and t.last_update_time = #{lastUpdateTime}
        </if>
        <if test="status != null">
            and t.status = #{status}
        </if>
    </select>

    <select id="selectCount" resultType="Long">
        select count(1)
        from cq_ext_project_complete
    </select>
    <insert id="insertOrUpdate" parameterType="com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete">
        <selectKey keyProperty="id" resultType="Long" order="AFTER">
            select last_insert_id()
        </selectKey>
        insert into cq_ext_project_complete (<include refid="insetColumns"/>) values
        <foreach collection="list" item="item" separator=",">
            (<include refid="insetVals"/>)
        </foreach>
            on duplicate key update
            organization_id = VALUES(organization_id),
            xmlx = VALUES(xmlx),
            xmmc = VALUES(xmmc),
            cjjg = VALUES(cjjg),
            cjje = VALUES(cjje),
            cjrq = VALUES(cjrq),
            cjsj = VALUES(cjsj),
            sjcjfs = VALUES(sjcjfs),
            srfmc = VALUES(srfmc),

            zrdj = VALUES(zrdj),

            pgj = VALUES(pgj),

            cgbl = VALUES(cgbl),

            ggzyptbh = VALUES(ggzyptbh),

            cljg = VALUES(cljg),

            jgsm = VALUES(jgsm),

            zccz_cjzq = VALUES(zccz_cjzq),

            bdcfczh = VALUES(bdcfczh),

            jjjssj = VALUES(jjjssj),

            srflxfs = VALUES(srflxfs),

            fczh = VALUES(fczh)


    </insert>
</mapper>