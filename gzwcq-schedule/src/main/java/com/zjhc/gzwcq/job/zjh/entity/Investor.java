package com.zjhc.gzwcq.job.zjh.entity;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/03:09:49:47
 **/
@XmlRootElement
@XmlType(propOrder = {
        "qyid",
        "czrmc",
        "czrbm",
        "czrlb",
        "rjzbbwb",
        "rjzbrmb",
        "sjzbbwb",
        "sjzbrmb",
        "gqbl"
})
public class Investor {
    private String qyid;
    private String czrmc;
    private String czrbm;
    private String czrlb;
    private BigDecimal rjzbbwb;
    private BigDecimal rjzbrmb;
    private BigDecimal sjzbbwb;
    private BigDecimal sjzbrmb;
    private BigDecimal gqbl;

    public Investor() {
    }

    public Investor(String qyid, String czrmc, String czrbm, String czrlb, BigDecimal rjzbbwb, BigDecimal rjzbrmb, BigDecimal sjzbbwb, BigDecimal sjzbrmb, BigDecimal gqbl) {
        this.qyid = qyid;
        this.czrmc = czrmc;
        this.czrbm = czrbm;
        this.czrlb = czrlb;
        this.rjzbbwb = rjzbbwb;
        this.rjzbrmb = rjzbrmb;
        this.sjzbbwb = sjzbbwb;
        this.sjzbrmb = sjzbrmb;
        this.gqbl = gqbl;
    }

    public String getQyid() {
        return qyid;
    }

    public void setQyid(String qyid) {
        this.qyid = qyid;
    }

    public String getCzrmc() {
        return czrmc;
    }

    public void setCzrmc(String czrmc) {
        this.czrmc = czrmc;
    }

    public String getCzrbm() {
        return czrbm;
    }

    public void setCzrbm(String czrbm) {
        this.czrbm = czrbm;
    }

    public String getCzrlb() {
        return czrlb;
    }

    public void setCzrlb(String czrlb) {
        this.czrlb = czrlb;
    }

    public BigDecimal getRjzbbwb() {
        return rjzbbwb;
    }

    public void setRjzbbwb(BigDecimal rjzbbwb) {
        this.rjzbbwb = rjzbbwb;
    }

    public BigDecimal getRjzbrmb() {
        return rjzbrmb;
    }

    public void setRjzbrmb(BigDecimal rjzbrmb) {
        this.rjzbrmb = rjzbrmb;
    }

    public BigDecimal getSjzbbwb() {
        return sjzbbwb;
    }

    public void setSjzbbwb(BigDecimal sjzbbwb) {
        this.sjzbbwb = sjzbbwb;
    }

    public BigDecimal getSjzbrmb() {
        return sjzbrmb;
    }

    public void setSjzbrmb(BigDecimal sjzbrmb) {
        this.sjzbrmb = sjzbrmb;
    }

    public BigDecimal getGqbl() {
        return gqbl;
    }

    public void setGqbl(BigDecimal gqbl) {
        this.gqbl = gqbl;
    }
}
