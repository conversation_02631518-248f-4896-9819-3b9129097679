package com.zjhc.gzwcq.job.zjh.entity.api.DTO;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:11:30:21
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectCompleteDTO {
    //地市分中心
    @JSONField(name = "SSJG")
    private Long SSJG;
    //转让方(信用代码证)
    private String ZRFZJHM;
    //组织机构代码
    private Long JGDM;
    //项目类型
    private String XMLX;
    //项目编号
    @JSONField(name = "XMBH")
    private String XMBH;
    //成交日期
    @JSONField(name = "CJRQ")
    private Long CJRQ;
    //公共资源平台编号
    private String GGZYPTBH;
    //是否国资 0:否，1:是
    @JSONField(name = "SFGZ")
    private Long SFGZ;

    @JSONField(serialize = false, deserialize = false)
    private String token;
    //页码
    @J<PERSON><PERSON>ield(name = "PAGEN<PERSON>")
    private Integer PAGENO;
    //页数
    @J<PERSON><PERSON>ield(name = "PAGESIZE")
    private Integer PAGESIZE;

    public Integer getPAGENO() {
        return PAGENO;
    }

    public void setPAGENO(Integer PAGENO) {
        this.PAGENO = PAGENO;
    }

    public Integer getPAGESIZE() {
        return PAGESIZE;
    }

    public void setPAGESIZE(Integer PAGESIZE) {
        this.PAGESIZE = PAGESIZE;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getSSJG() {
        return SSJG;
    }

    public void setSSJG(Long SSJG) {
        this.SSJG = SSJG;
    }

    public String getZRFZJHM() {
        return ZRFZJHM;
    }

    public void setZRFZJHM(String ZRFZJHM) {
        this.ZRFZJHM = ZRFZJHM;
    }

    public Long getJGDM() {
        return JGDM;
    }

    public void setJGDM(Long JGDM) {
        this.JGDM = JGDM;
    }

    public String getXMLX() {
        return XMLX;
    }

    public void setXMLX(String XMLX) {
        this.XMLX = XMLX;
    }

    public String getXMBH() {
        return XMBH;
    }

    public void setXMBH(String XMBH) {
        this.XMBH = XMBH;
    }

    public Long getCJRQ() {
        return CJRQ;
    }

    public void setCJRQ(Long CJRQ) {
        this.CJRQ = CJRQ;
    }

    public String getGGZYPTBH() {
        return GGZYPTBH;
    }

    public void setGGZYPTBH(String GGZYPTBH) {
        this.GGZYPTBH = GGZYPTBH;
    }

    public Long getSFGZ() {
        return SFGZ;
    }

    public void setSFGZ(Long SFGZ) {
        this.SFGZ = SFGZ;
    }
}
