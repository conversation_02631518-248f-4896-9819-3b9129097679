package com.zjhc.gzwcq.job.zjh.entity.api.DTO;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:17:53:36
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtProjectTransfereeDTO {
    //地市分中心
    @JSONField(name = "SSJG")
    private Long SSJG;
    //转让方(信用代码证)
    private String ZRFZJHM;
    //组织机构代码
    private Long JGDM;
    //项目类型
    private String XMLX;
    //项目编号
    private String XMBH;
    @JSONField(serialize = false, deserialize = false)
    private String token;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getSSJG() {
        return SSJG;
    }

    public void setSSJG(Long SSJG) {
        this.SSJG = SSJG;
    }

    public String getZRFZJHM() {
        return ZRFZJHM;
    }

    public void setZRFZJHM(String ZRFZJHM) {
        this.ZRFZJHM = ZRFZJHM;
    }

    public Long getJGDM() {
        return JGDM;
    }

    public void setJGDM(Long JGDM) {
        this.JGDM = JGDM;
    }

    public String getXMLX() {
        return XMLX;
    }

    public void setXMLX(String XMLX) {
        this.XMLX = XMLX;
    }

    public String getXMBH() {
        return XMBH;
    }

    public void setXMBH(String XMBH) {
        this.XMBH = XMBH;
    }
}
