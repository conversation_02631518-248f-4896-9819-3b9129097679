package com.zjhc.gzwcq.job.zjh.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ext_project_complete <br/>
 *         描述：项目成交 <br/>
 */
public class ExtProjectCompleteVo extends ExtProjectComplete {

	private static final long serialVersionUID = 18L;

	private List<ExtProjectCompleteVo> extProjectCompleteList;

	public ExtProjectCompleteVo() {
		super();
	}

  	public ExtProjectCompleteVo(Long id) {
  		super();
  		this.id = id;
	}

	public List<ExtProjectCompleteVo> getExtProjectCompleteList() {
		return extProjectCompleteList;
	}

	public void setExtProjectCompleteList(List<ExtProjectCompleteVo> extProjectCompleteList) {
		this.extProjectCompleteList = extProjectCompleteList;
	}

}
