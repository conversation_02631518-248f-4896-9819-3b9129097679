package com.zjhc.gzwcq.job.zjh.mapper;

import com.zjhc.gzwcq.job.zjh.entity.Dcgqk;
import com.zjhc.gzwcq.job.zjh.entity.Enterprise;
import com.zjhc.gzwcq.job.zjh.entity.HhrqkfdVo;
import com.zjhc.gzwcq.job.zjh.entity.Investor;
import com.zjhc.gzwcq.job.zjh.entity.Jbxxb;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/03:10:06:18
 **/
@Repository
public interface PushDataMapper {

    List<String> selectOrgId();
    Enterprise selectPushData(@Param("orgId")String orgId);
    List<Enterprise> selectDefaultData();
    List<Investor> selectInvestorByJbxxbId(@Param("id")String id);
    List<HhrqkfdVo>selectByJbxxbId(@Param("jbxxbId")String  jbxxbId);
    List<Dcgqk> selectDcgqkListByJbxxbId(@Param("id")String id);
    Map<String,String> selectJbxxbStatus(@Param("orgId")String orgId);
    List<Jbxxb> selectDataJbxxb();
    List<Jbxxb> selectById(@Param("id") String id);

    void  upDateJbxxb(@Param("jbZcdNew") String jbZcdNew,@Param("jbZcrqNew") Date jbZcrqNew,
                      @Param("id") String id,@Param("JBGSDJRQ") Date JBGSDJRQ);
}
