package com.zjhc.gzwcq.job.zjh.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.boot.iAdmin.ftp.util.FtpPoolHelper;
import com.zjhc.gzwcq.job.zjh.entity.BusiInfo;
import com.zjhc.gzwcq.job.zjh.entity.DataList;
import com.zjhc.gzwcq.job.zjh.entity.Dcgqk;
import com.zjhc.gzwcq.job.zjh.entity.DcgqkList;
import com.zjhc.gzwcq.job.zjh.entity.Enterprise;
import com.zjhc.gzwcq.job.zjh.entity.HhqyVo;
import com.zjhc.gzwcq.job.zjh.entity.Investor;
import com.zjhc.gzwcq.job.zjh.entity.InvestorList;
import com.zjhc.gzwcq.job.zjh.entity.Jbxxb;
import com.zjhc.gzwcq.job.zjh.entity.properties.ApiGwyPushProperties;
import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.ErrorInfo;
import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfo;
import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.ResultData;
import com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.ResultInfo;
import com.zjhc.gzwcq.job.zjh.mapper.IHhqyMapper;
import com.zjhc.gzwcq.job.zjh.mapper.IRequestResultInfoMapper;
import com.zjhc.gzwcq.job.zjh.mapper.PushDataMapper;
import com.zjhc.gzwcq.job.zjh.service.api.IPushDataService;
import com.zjhc.gzwcq.job.zjh.util.HttpUtils;
import com.zjhc.gzwcq.job.zjh.util.SM3Util;
import com.zjhc.gzwcq.job.zjh.util.XmlZipConverterUtils;
import com.zjhc.gzwcq.job.zjh.util.ZipEncryptorUtils;
import groovy.util.logging.Slf4j;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.io.ZipOutputStream;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.InputStreamBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/04:08:47:16
 **/
@Service
@Slf4j
public class PushDataServiceImpl implements IPushDataService {
    private static Logger logger = LoggerFactory.getLogger(PushDataServiceImpl.class);
    @Autowired
    private PushDataMapper dataMapper;
    @Autowired
    private IHhqyMapper hhqyMapper;
    @Autowired
    private ApiGwyPushProperties pushProperties;
    private static final String QYLX_HHQY = "2";
    private static final String COMPLETED = "COMPLETED";
    private static final String TRUE = "1";
    private static final String FALSE = "0";
    @Autowired
    private IRequestResultInfoMapper requestResultInfoMapper;

    @Override
    public void pushDataService() {
        BusiInfo data = this.getData();
        MultipartFile multipartFile = null;
        try {
            multipartFile = XmlZipConverterUtils.getMultipartFile(data,pushProperties.getAppSecret());
//            // 保存加密后的文件到本地目录
//            String downloadDir = "D:\\javaCode\\git\\gzwcq\\gzwcq-schedule\\src\\main\\java\\com\\zjhc\\gzwcq\\job\\zjh\\util\\";  // 替换为你的目标目录
//            File downloadFile = new File(downloadDir, "encrypted.zip");
//            // 确保目标目录存在
//            File dir = new File(downloadDir);
//            if (!dir.exists()) {
//                dir.mkdirs();
//            }
//
//            // 将加密后的文件内容写入到本地文件
//            try (FileOutputStream fos = new FileOutputStream(downloadFile)) {
//                fos.write(multipartFile.getBytes());
//            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("转换为xml + zib 文件流失败：" + e);
        }
        try {
            this.uploadFile(multipartFile);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("国务院请求的文件 上传ftp失败：" + e);
        }
        RequestResultInfo requestResultInfo = new RequestResultInfo();
        if (Objects.isNull(multipartFile)) return;
        try {
            String s = this.postWithMultipart(multipartFile);
            logger.info("发送文件流至国务院返回msg: " + s);
            Map<String, String> map = JSON.parseObject(s, new TypeReference<HashMap<String, String>>() {
            });
            String uploadId = map.get("uploadId");
            if (StringUtils.isBlank(uploadId)) {
                logger.info("发送文件流至国务院返回报错信息: " + s);
                return;
            }
            requestResultInfo.setFileUploadId(uploadId);
            requestResultInfo.setRequstAppId(pushProperties.getAppId());
            requestResultInfo.setRequstAppSecret(pushProperties.getAppSecret());
            requestResultInfo.setRequstUrl(pushProperties.getUpdateAllDfqyUrl());
            requestResultInfo.setRequstTime(new Date());
            requestResultInfo.setSelectStatus("0");
            requestResultInfoMapper.insert(requestResultInfo);
        } catch (Exception e) {
            logger.error("请求国务院接口失败：" + e);
        }

    }

    /**
     * 获取数据
     *
     * @return
     */
    private BusiInfo getData() {
        BusiInfo info = new BusiInfo();
        DataList dataList = new DataList();
        String[] ids = new String[]{"10", "12", "14", "20", "22", "25", "30", "31", "32", "34", "35", "36", "37", "39", "38"};
        List<String> collect = Arrays.stream(ids).collect(Collectors.toList());
        List<Enterprise> enterprise = new ArrayList<>();
        logger.info("开始查询写死数据(浙江省)");
        List<Enterprise> defaultData = dataMapper.selectDefaultData();
        enterprise.addAll(defaultData);
        logger.info("开始查询基本数据");
        List<String> strings = dataMapper.selectOrgId();
        strings.stream().forEach(item -> {
            Enterprise vo = dataMapper.selectPushData(item);
            if (Objects.isNull(vo)) return;

            String gjczqy = vo.getGjczqy();
            if (StringUtils.isNotBlank(gjczqy)) {
                String[] s1 = gjczqy.split("_");
                if (s1.length > 1) {
                    gjczqy = s1[1];
                } else {
                    gjczqy = s1[0];
                }
                vo.setGjczqy(gjczqy);
            }
            String zyczr = vo.getZyczr();
            if (StringUtils.isNotBlank(zyczr)) {
                String[] s = zyczr.split("_");
                if (s.length > 1) {
                    zyczr = s[1];
                } else {
                    zyczr = s[0];
                }
                vo.setZyczr(zyczr);
            }
            //将字典不对的改字典值
            String gzjgjglx = vo.getGzjgjglx();
            if (StringUtils.isNotBlank(gzjgjglx)) {
                gzjgjglx = StringUtils.equals(gzjgjglx, "5") ? "4" : StringUtils.equals(gzjgjglx, "6") ? "5" : gzjgjglx;
                vo.setGzjgjglx(gzjgjglx);
            }
            String sfgrdcg = vo.getSfgrdcg();
            if (StringUtils.isNotBlank(sfgrdcg)) {
                sfgrdcg = sfgrdcg.equals("2") ? "0" : sfgrdcg;
                vo.setSfgrdcg(sfgrdcg);
            }
            String jnjwbs = vo.getJnjwbs();
            if (StringUtils.isNotBlank(jnjwbs)) {
                jnjwbs = StringUtils.equals(jnjwbs, "2") ? "0" : jnjwbs;
                vo.setJnjwbs(jnjwbs);
            }
            String zzxs = vo.getZzxs();
            if (StringUtils.isNotBlank(zzxs)) {
                zzxs = collect.contains(zzxs) ? "34" : zzxs;
//                zzxs = StringUtils.equals(zzxs, "38") ? "34" : zzxs;
                vo.setZzxs(zzxs);
            } else {
//                vo.setZzxs("34");
            }
            Map<String, String> map = dataMapper.selectJbxxbStatus(item);
            String jbxxbId = map.get("jbxxbId");
            List<Investor> investors = null;
            //合伙企业
            if (map.containsKey("status") && QYLX_HHQY.equals(map.get("status"))) {
                HhqyVo hhqVo = hhqyMapper.selectByJbxxbId(jbxxbId);
                //主要出资人
                String hhCzqyCode = hhqVo.getHhCzqyCode();
                String[] s = hhCzqyCode.split("_");
                if (s.length > 1) {
                    hhCzqyCode = s[1];
                } else {
                    hhCzqyCode = s[0];
                }
                vo.setZyczr(hhCzqyCode);
                String jbJnjw = vo.getJnjwbs();
                if (StringUtils.equals(jbJnjw, "0")) {
                    //注册地 境外
                    String hhZyjycs = hhqVo.getHhZyjycs();
                    vo.setZcdjw(hhZyjycs);
                } else {
                    //注册地 境内
                    String hhZyjycs = hhqVo.getHhZyjycs();
                    vo.setZcdjn(hhZyjycs);
                }

                //注册资本/认缴资本
                BigDecimal hhRjcze = hhqVo.getHhRjcze();
                String hhRjczebz = hhqVo.getHhRjczebzStr();
//                hhRjczebz = StringUtils.isBlank(hhRjczebz) ? "人民币":hhRjczebz;
                vo.setZczbbwb(hhRjcze);
                vo.setBwbmc(hhRjczebz);
                BigDecimal hhRjczermb = hhqVo.getHhRjczermb();
                vo.setZczbrmb(hhRjczermb);

//                List<HhrqkfdVo> hhrqkfdVos = dataMapper.selectByJbxxbId(jbxxbId);
//                investors =   hhrqkfdVos.stream().map(itemBase->{
//                    String id = itemBase.getUnitid();
//                    String name = itemBase.getName();
//                    String hhrCode = itemBase.getHhrCode();
//                    String category = itemBase.getCategory();
//
//                    return new Investor();
//                }).collect(Collectors.toList());

            } else {
                investors = dataMapper.selectInvestorByJbxxbId(jbxxbId);
            }
            if (!investors.isEmpty()) {
                //合并出资人信息
                investors = this.dataDispose(investors);
            }
            String bwbmc = vo.getBwbmc();
            if (!StringUtils.isBlank(bwbmc)) {
                vo.setBwbmc(getBz(bwbmc));
            }
            if (map.containsKey("iscjbb") && "true".equals(map.get("iscjbb"))) {
                InvestorList investorList = new InvestorList();
                investorList.setInvestor(investors);
                vo.setInvestorList(investorList);
            }
            List<Dcgqk> dcgqks = dataMapper.selectDcgqkListByJbxxbId(jbxxbId);
            DcgqkList dcgqkList = new DcgqkList();
            dcgqkList.setDcgqk(dcgqks);
            vo.setDcgqkList(dcgqkList);
            enterprise.add(vo);
        });
        dataList.setEnterprise(enterprise);
        info.setDataList(dataList);
        return info;
    }

    private List<Investor> dataDispose(List<Investor> investors) {
        List<Investor> vos = new ArrayList<>();
        //出资人列表代码没有默认给 - 因为数据治理 取消了
//        List<String> collect = investors.stream().filter(item -> StringUtils.equals("8", item.getCzrlb())).map(item-> item.getCzrbm()).collect(Collectors.toList());
//        int size = collect.size();
//        if (size>1){
//            investors.stream().forEach(item->{
//                String czrlb = item.getCzrlb();
//                if (StringUtils.equals("8", czrlb)){
//                    String czrbm = item.getCzrbm();
//                    long count = collect.stream().filter(item2 -> StringUtils.equals(item2,czrbm)).count();
//                    if (count>1){
//                        item.setCzrbm("-");
//                    }else if (StringUtils.isBlank(czrbm)){
//                        item.setCzrbm("-");
//                    }
//                }else if (StringUtils.equals("5", czrlb)){
//                    if (StringUtils.isBlank(item.getCzrbm())){
//                        item.setCzrbm("-");
//                    }
//                }
//            });
//        }else {
//            investors.stream().forEach(item->{
//                String czrlb = item.getCzrlb();
//                if (StringUtils.equals("8", czrlb)||StringUtils.equals("5", czrlb)){
//                    if (StringUtils.isBlank(item.getCzrbm())){
//                        item.setCzrbm("-");
//                    }
//                }
//            });
//        }
        //出资人编码为空的 不做处理
        List<Investor> investorList = investors.stream().filter(item -> StringUtils.isBlank(item.getCzrbm()) || StringUtils.equals(item.getCzrbm(), "-")).collect(Collectors.toList());
        if (!investorList.isEmpty()) {
            vos.addAll(investorList);
        }
        //合并出资人数据
        Map<String, List<Investor>> collect = investors.stream().filter(item -> StringUtils.isNotBlank(item.getCzrbm()) && !StringUtils.equals(item.getCzrbm(), "-")).collect(Collectors.groupingBy(item -> item.getCzrbm().trim()));
        Set<Map.Entry<String, List<Investor>>> entries = collect.entrySet();
        for (Map.Entry<String, List<Investor>> entry : entries) {
            List<Investor> value = entry.getValue();
            Investor vo = new Investor();
            BigDecimal rjzbbwb = new BigDecimal(0);
            BigDecimal rjzbrmb = new BigDecimal(0);
            BigDecimal sjzbbwb = new BigDecimal(0);
            BigDecimal sjzbrmb = new BigDecimal(0);
            BigDecimal gqbl = new BigDecimal(0);
            for (Investor item2 : value) {
                BigDecimal rjzbbwb1 = item2.getRjzbbwb();
                BigDecimal rjzbrmb1 = item2.getRjzbrmb();
                BigDecimal sjzbbwb1 = item2.getSjzbbwb();
                BigDecimal sjzbrmb1 = item2.getSjzbrmb();
                BigDecimal gqbl1 = item2.getGqbl();
                vo.setCzrlb(item2.getCzrlb());
                vo.setCzrmc(item2.getCzrmc());
                vo.setCzrbm(item2.getCzrbm());
                vo.setQyid(item2.getQyid());
                if (Objects.nonNull(rjzbbwb1)) {
                    rjzbbwb = rjzbbwb.add(rjzbbwb1);
                }
                if (Objects.nonNull(rjzbrmb1)) {
                    rjzbrmb = rjzbrmb.add(rjzbrmb1);
                }
                if (Objects.nonNull(sjzbbwb1)) {
                    sjzbbwb = sjzbbwb.add(sjzbbwb1);
                }
                if (Objects.nonNull(sjzbrmb1)) {
                    sjzbrmb = sjzbrmb.add(sjzbrmb1);
                }
                if (Objects.nonNull(gqbl1)) {
                    gqbl = gqbl.add(gqbl1);
                }
            }
            vo.setRjzbbwb(rjzbbwb);
            vo.setRjzbrmb(rjzbrmb);
            vo.setSjzbbwb(sjzbbwb);
            vo.setSjzbrmb(sjzbrmb);
            vo.setGqbl(gqbl);
            vos.add(vo);
        }
        return vos;
    }

    @Override
    public void pushDataServiceSendFTP() {
        BusiInfo data = this.getData();
        MultipartFile multipartFile = null;
        try {
            multipartFile = XmlZipConverterUtils.getMultipartFile(data,pushProperties.getAppSecret());

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("转换为xml + zib 文件流失败：" + e);
        }
        try {
            this.uploadFile(multipartFile);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("国务院请求的文件 上传ftp失败：" + e);
        }
    }

    @Override
    public void selectFileStatus() {
            List<RequestResultInfo> requestResultInfos = requestResultInfoMapper.selectForList();
            requestResultInfos.stream().forEach(item ->selectDataStatus(item));
    }

    private void selectDataStatus(RequestResultInfo resultInfo)  {
        String fileUploadId = resultInfo.getFileUploadId();
        Long id = resultInfo.getId();
        try {
            String postWithMultipartStatus = getPostWithMultipartStatus(fileUploadId);
            logger.info("查询文件发送状态返回: " + postWithMultipartStatus);
            ResultInfo resultInfo1 = JSON.parseObject(postWithMultipartStatus, new TypeReference<ResultInfo>() {
            });
            ResultData data = resultInfo1.getData();
            String status = data.getStatus();
            //说明国务院文件没有解析完
            if (!StringUtils.equals(status,COMPLETED)){
                resultInfo.setRequstSuccess(FALSE);
                resultInfo.setSelectStatus(TRUE);
                requestResultInfoMapper.updateIgnoreNull(resultInfo);
                return;
            }
            List<ErrorInfo> errorArray = data.getErrorArray();
            Long trueCont = data.getRecordCount();
            Long total = data.getTotal();
            Long falseCont = total - trueCont;
            resultInfo.setRequstSuccess(TRUE);
            resultInfo.setSelectStatus(TRUE);
            resultInfo.setTrueCont(trueCont);
            resultInfo.setFalseCont(falseCont);
            resultInfo.setTotalCont(total);
            requestResultInfoMapper.updateIgnoreNull(resultInfo);
            this.errInfo(errorArray,id);
        } catch (Exception e) {
            logger.info("解析返回数据失败: " + e);
            logger.info("解析返回数据失败: " + e.getMessage());
        }
    }
    private void  errInfo( List<ErrorInfo> errorArray,Long id){
        errorArray.stream().forEach(item->dataProcessing(item,id));
    }
    private void dataProcessing(ErrorInfo errorInfo,Long id){
        String xybm = errorInfo.getXybm();
        String orgId = null;
        try {
            orgId = requestResultInfoMapper.selectOrgInfo(xybm);
        } catch (Exception e) {
        }
        errorInfo.setOrgId(orgId);
        errorInfo.setApiRequestInfoId(id);
        requestResultInfoMapper.insertErrorInfo(errorInfo);
    }
    @Override
    public void jbxxbDataDispose() {
        List<Jbxxb> strings = dataMapper.selectDataJbxxb();
        //处理注册地与注册日期
        strings.stream().forEach(item -> {
            String orgId = item.getUnitid();
            String id = item.getId();
            String jbZcdNew = "";
            Date jbZcrqNew = null;
            Date JBGSDJRQ = null;
            List<Jbxxb> jbxxbs = dataMapper.selectById(orgId);
            for (Jbxxb jbxxb : jbxxbs) {
                String jbZcd = jbxxb.getJbZcd();
                Date jbZcrq = jbxxb.getJbZcrq();
                Date jbGsdjrq = jbxxb.getJbGsdjrq();
                if (!StringUtils.isBlank(jbZcd) && StringUtils.isBlank(jbZcdNew)) {
                    jbZcdNew = jbZcd;
                }
                if (!Objects.isNull(jbZcrq) && Objects.isNull(jbZcrqNew)) {
                    jbZcrqNew = jbZcrq;
                }
                if (!Objects.isNull(jbGsdjrq) && Objects.isNull(JBGSDJRQ)) {
                    JBGSDJRQ = jbGsdjrq;
                }

            }
            if (!StringUtils.isBlank(jbZcdNew) && !Objects.isNull(jbZcrqNew) && !Objects.isNull(JBGSDJRQ)) {
                dataMapper.upDateJbxxb(jbZcdNew, jbZcrqNew, id,JBGSDJRQ);
            }

        });

    }

    private String getBz(String bz) {
        return bz.split("（")[0];
    }

    public String postWithMultipart(MultipartFile multipartFile) throws IOException {
        logger.info("开始发送文件至国务院接口");
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String updateAllDfqyUrl = pushProperties.getUpdateAllDfqyUrl();
            String appId = pushProperties.getAppId();
            String appSecret = pushProperties.getAppSecret();
            HttpPost httpPost = new HttpPost(updateAllDfqyUrl);
            String rid = UUID.randomUUID().toString().replaceAll("-", "");
            Long fileSize = multipartFile.getSize();
            long ts = System.currentTimeMillis();
            String sign = SM3Util.SM3Encode(appSecret + rid + ts + (fileSize == null || fileSize == 0 ? "" : fileSize));
            // 设置请求头
            httpPost.addHeader("Accept", "application/json");
            httpPost.addHeader("ts", String.valueOf(ts));
            httpPost.addHeader("rid", rid);
            httpPost.addHeader("sign", sign);
            httpPost.addHeader("appId", appId);
            httpPost.addHeader("appSecret", appSecret);
            String collect = Arrays.stream(httpPost.getAllHeaders()).map(header -> header.getName() + ":" + header.getValue()).collect(Collectors.joining(","));
            logger.info("请求头:" + collect);

            // 创建multipart实体
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setBoundary("WebAppBoundary");
            builder.setCharset(java.nio.charset.StandardCharsets.UTF_8);
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);

            try (InputStream content = multipartFile.getInputStream()) {
                builder.addPart(
                        "file", // 这里的"file"是API期望的表单字段名
                        new InputStreamBody(content, ContentType.DEFAULT_BINARY, multipartFile.getOriginalFilename())
                );
            }

            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(600000) // Timeout for waiting for data
                    .setConnectTimeout(600000) // Timeout for establishing connection
                    .build();
            httpPost.setConfig(requestConfig);

            HttpEntity multipart = builder.build();
            httpPost.setEntity(multipart);

            // 发送请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode >= 200 && statusCode < 300) {
                    // 请求成功，返回响应体内容
                    return EntityUtils.toString(response.getEntity());
                } else {
                    // 请求失败，处理错误
                    throw new RuntimeException("Request failed with status code: " + statusCode);
                }
            }
        }
    }

    public String getPostWithMultipartStatus(String uploadId) throws IOException {
        logger.info("查询文件推送状态");
        String getDfqyUploadStatusUrl = pushProperties.getGetDfqyUploadStatusUrl();
        String appId = pushProperties.getAppId();
        String appSecret = pushProperties.getAppSecret();
        JSONObject json = new JSONObject();
        json.put("uploadId", uploadId);
        String rid = UUID.randomUUID().toString().replaceAll("-", "");
        long ts = System.currentTimeMillis();
        String sign = SM3Util.SM3Encode(appSecret + rid + ts + "");
        Map<String, String> header = new HashMap<>();
        header.put("ts", String.valueOf(ts));
        header.put("rid", rid);
        header.put("sign", sign);
        header.put("appId", appId);
        header.put("appSecret", appSecret);
        header.put("Accept", "application/json");
        logger.info("请求头:" + header);
        logger.info("请求体:" + json);
        return HttpUtils.sendPost(getDfqyUploadStatusUrl, json.toString(), header);
    }

    public void uploadFile(MultipartFile file) {
        String result = "";
        if (!file.isEmpty()) {
            //文件上传到FTP
            String fileName = file.getOriginalFilename();
            int length = fileName.length();
            fileName = fileName.substring(0, fileName.lastIndexOf(".")) + UUID.randomUUID() + fileName.substring(fileName.lastIndexOf("."), length);
            String ftpPath = pushProperties.getFilePath() + new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            logger.info(String.format("开始上传文件%s，大小%s，目标地址%s", fileName, file.getSize(), ftpPath));
            if (FtpPoolHelper.uploadLocalFile(ftpPath, file, fileName)) {//上传成功
            } else {
                logger.info(String.format("上传文件%s失败", fileName));
                throw new RuntimeException(String.format("文件[%s]上传失败；", file.getOriginalFilename()));
            }
        } else {
            throw new RuntimeException(String.format("文件[%s]为空；", file.getOriginalFilename()));
        }
    }
}
