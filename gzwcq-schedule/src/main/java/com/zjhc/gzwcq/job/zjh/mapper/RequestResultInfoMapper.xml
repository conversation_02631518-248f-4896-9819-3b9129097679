<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IRequestResultInfoMapper">

	<resultMap type="com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfo" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="requst_time" property="requstTime"/>
		<result column="requst_app_id" property="requstAppId"/>
		<result column="requst_app_secret" property="requstAppSecret"/>
		<result column="requst_url" property="requstUrl"/>
		<result column="result_info" property="resultInfo"/>
		<result column="requst_success" property="requstSuccess"/>
		<result column="file_upload_id" property="fileUploadId"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfoVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		requst_time, 
		requst_app_id, 
		requst_app_secret, 
		requst_url, 
		result_info, 
		requst_success, 
		file_upload_id,
		select_status,
		true_cont,
		false_cont,
		total_cont
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.requst_time, 
		t.requst_app_id, 
		t.requst_app_secret, 
		t.requst_url, 
		t.result_info, 
		t.requst_success, 
		t.file_upload_id,
		t.select_status,
		t.true_cont,
		t.false_cont,
		t.total_cont
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{requstTime}, 
		#{requstAppId}, 
		#{requstAppSecret}, 
		#{requstUrl}, 
		#{resultInfo}, 
		#{requstSuccess}, 
		#{fileUploadId},
		#{selectStatus},
		#{trueCont},
		#{falseCont},
		#{totalCont}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null">
			and t.id = #{id}
		</if>
		<if test="requstTime != null">
			and t.requst_time = #{requstTime}
		</if>
		<if test="requstAppId != null and requstAppId != ''">
			and t.requst_app_id = #{requstAppId}
		</if>
		<if test="requstAppSecret != null and requstAppSecret != ''">
			and t.requst_app_secret = #{requstAppSecret}
		</if>
		<if test="requstUrl != null and requstUrl != ''">
			and t.requst_url = #{requstUrl}
		</if>
		<if test="resultInfo != null and resultInfo != ''">
			and t.result_info = #{resultInfo}
		</if>
		<if test="requstSuccess != null and requstSuccess != ''">
			and t.requst_success = #{requstSuccess}
		</if>
		<if test="fileUploadId != null and fileUploadId != ''">
			and t.file_upload_id = #{fileUploadId}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfo" useGeneratedKeys="true" keyProperty="id">
		insert into api_request_result_info (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>

	<insert id="insertErrorInfo" parameterType="com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.ErrorInfo">
		insert into api_request_erro_info (api_request_info_id,org_id,qymc,xybm,error_message,error_index,error_type,error_level,process_mode)
		values (
		        #{apiRequestInfoId},#{orgId},#{qymc},#{xybm},#{errorMessage},#{errorIndex},#{errorType},#{errorLevel},#{processMode})
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update api_request_result_info set isDeleted = 'Y' where
		id in
		<foreach collection="requestResultInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update api_request_result_info set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from api_request_result_info  where
		id in
		<foreach collection="requestResultInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from api_request_result_info  where id = #{id}
	</delete>
	
	<select id="selectRequestResultInfoByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from api_request_result_info
		where id=#{id}
	</select>
	<select id="selectOrgInfo" resultType="string">
		SELECT
			jb.ORGANIZATION_ID
		FROM
			(
				SELECT
					iscjbb.ORGANIZATION_ID
				FROM
					(
						SELECT
							so.ORGANIZATION_ID,
						    so.code
						FROM
						(
						SELECT
							o.ORGANIZATION_ID,
							SUBSTRING_INDEX(o.ORGANIZATION_CODE, '_', -1) as `code`
						FROM
							sys_organization o
						WHERE
							(SUBSTRING_INDEX(o.ORGANIZATION_CODE, '_', -1) like concat('%',#{xybm},'%')
								or  o.ORGANIZATION_CODE like concat('%',#{xybm},'%'))
								ORDER BY
								o.CREATE_TIME DESC
								LIMIT
								1000
							) so
								JOIN
							`cq_jbxxb`  cjb  on cjb.UNITID = so.ORGANIZATION_ID
								JOIN
							`rg_business_info` rbi on rbi.JBXX_ID = cjb.ID
						WHERE
							1=1
						  AND
							rbi.RG_UNITSTATE = '2'
						  AND
							cjb.JB_DELETED = 'N'
						  AND
							cjb.JB_SHZT = '4'
					      AND
							cjb.JB_RELA != 4
						ORDER BY
							rbi.RG_TIMEMARK desc
						LIMIT 1000000000
					) as iscjbb
				GROUP BY
					iscjbb.code
			) jb
	limit 1
	</select>
	<update id="updateIgnoreNull">
		update api_request_result_info
		<set>
			<if test="requstTime != null">
				requst_time=#{requstTime},
			</if>
			<if test="requstAppId != null">
				requst_app_id=#{requstAppId},
			</if>
			<if test="requstAppSecret != null">
				requst_app_secret=#{requstAppSecret},
			</if>
			<if test="requstUrl != null">
				requst_url=#{requstUrl},
			</if>
			<if test="resultInfo != null">
				result_info=#{resultInfo},
			</if>
			<if test="requstSuccess != null">
				requst_success=#{requstSuccess},
			</if>
			<if test="fileUploadId != null">
				file_upload_id=#{fileUploadId},
			</if>
		    <if test="selectStatus != null ">
				select_status = #{selectStatus},
			</if>
			<if test="trueCont != null ">
				true_cont = #{trueCont},
			</if>
			<if test="falseCont != null ">
				false_cont = #{falseCont},
			</if>
			<if test="totalCont != null ">
				total_cont = #{totalCont}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update api_request_result_info
		<set>
			requst_time=#{requstTime},
			requst_app_id=#{requstAppId},
			requst_app_secret=#{requstAppSecret},
			requst_url=#{requstUrl},
			result_info=#{resultInfo},
			requst_success=#{requstSuccess},
			file_upload_id=#{fileUploadId}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList"  resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			api_request_result_info t
		where 1 = 1
		and
			(t.select_status = 0 or t.requst_success = 0)
		ORDER BY
			t.requst_time DESC
		LIMIT 1
	</select>	

	<select id="queryTotalRequestResultInfos" parameterType="com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfoParam" resultType="java.lang.Long">
		select
			count(id)
		from api_request_result_info t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryRequestResultInfoForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfoParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			api_request_result_info t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity.RequestResultInfo" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from api_request_result_info t
		where t.id != #{id}
			<if test="requstTime != null">
				and t.requst_time = #{requstTime}
			</if>
			<if test="requstAppId != null and requstAppId != ''">
				and t.requst_app_id = #{requstAppId}
			</if>
			<if test="requstAppSecret != null and requstAppSecret != ''">
				and t.requst_app_secret = #{requstAppSecret}
			</if>
			<if test="requstUrl != null and requstUrl != ''">
				and t.requst_url = #{requstUrl}
			</if>
			<if test="resultInfo != null and resultInfo != ''">
				and t.result_info = #{resultInfo}
			</if>
			<if test="requstSuccess != null and requstSuccess != ''">
				and t.requst_success = #{requstSuccess}
			</if>
			<if test="fileUploadId != null and fileUploadId != ''">
				and t.file_upload_id = #{fileUploadId}
			</if>
	</select>

</mapper>