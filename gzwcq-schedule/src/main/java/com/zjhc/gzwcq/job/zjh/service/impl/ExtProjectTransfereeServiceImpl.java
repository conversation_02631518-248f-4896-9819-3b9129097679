package com.zjhc.gzwcq.job.zjh.service.impl;

import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeParam;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeVo;
import com.zjhc.gzwcq.job.zjh.mapper.IExtProjectTransfereeMapper;
import com.zjhc.gzwcq.job.zjh.service.api.IExtProjectTransfereeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class ExtProjectTransfereeServiceImpl implements IExtProjectTransfereeService {
	
	@Autowired
	private IExtProjectTransfereeMapper extProjectTransfereeMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(ExtProjectTransferee extProjectTransferee){
		extProjectTransferee.setCreateTime(new Date());//创建时间
		extProjectTransferee.setLastUpdateTime(new Date());//更新时间
		extProjectTransfereeMapper.insert(extProjectTransferee);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		extProjectTransfereeMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		extProjectTransfereeMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(ExtProjectTransferee extProjectTransferee){
		extProjectTransferee.setLastUpdateTime(new Date());//更新时间
		extProjectTransfereeMapper.updateIgnoreNull(extProjectTransferee);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(ExtProjectTransferee extProjectTransferee){
		extProjectTransferee.setLastUpdateTime(new Date());//更新时间
		extProjectTransfereeMapper.update(extProjectTransferee);
	}
	
	public List<ExtProjectTransfereeVo> queryExtProjectTransfereeByPage(ExtProjectTransfereeParam extProjectTransfereeParam) {
      	//分页
     	return extProjectTransfereeMapper.queryExtProjectTransfereeForList(extProjectTransfereeParam);
	}
	

	public ExtProjectTransferee selectExtProjectTransfereeByPrimaryKey(ExtProjectTransferee ExtProjectTransferee) {
		return extProjectTransfereeMapper.selectExtProjectTransfereeByPrimaryKey(ExtProjectTransferee);
	}
	
	public long queryTotalExtProjectTransferees(ExtProjectTransfereeParam extProjectTransfereeParam) {
		return extProjectTransfereeMapper.queryTotalExtProjectTransferees(extProjectTransfereeParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<ExtProjectTransferee> selectForList(ExtProjectTransferee extProjectTransferee){
		return extProjectTransfereeMapper.selectForList(extProjectTransferee);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(ExtProjectTransferee extProjectTransferee) {
		return extProjectTransfereeMapper.selectForUnique(extProjectTransferee).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(ExtProjectTransferee extProjectTransferee) {
      	if(extProjectTransferee.getId() == null) {
			this.insert(extProjectTransferee);
		}else {
			this.updateIgnoreNull(extProjectTransferee);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(ExtProjectTransferee[] objs) {
		for(ExtProjectTransferee extProjectTransferee : objs) {
			this.saveOne(extProjectTransferee);
		}
	}
}
