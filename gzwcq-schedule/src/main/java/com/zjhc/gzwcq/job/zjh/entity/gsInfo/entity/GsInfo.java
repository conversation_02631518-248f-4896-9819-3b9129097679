package com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zjhc.gzwcq.job.zjh.entity.gsInfoInvestors.entity.GsInfoInvestors;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： scjd_gs_info <br/>
 *         描述：市场监管总局返回数据 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GsInfo implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="id")
	protected Long id;// id
  	@ApiParam(value="唯一标识一笔登记业务")
	protected String ywid;// 唯一标识一笔登记业务
  	@ApiParam(value="产权登记企业名称")
	protected String cqqymc;// 产权登记企业名称
  	@ApiParam(value="工商登记企业名称")
	protected String gsqymc;// 工商登记企业名称
  	@ApiParam(value="统一社会信用编码")
	protected String xybm;// 统一社会信用编码
  	@ApiParam(value="企业类别")
	protected String qylx;// 企业类别
  	@ApiParam(value="本位币名称")
	protected String bwbmc;// 本位币名称
  	@ApiParam(value="注册资本(本位币)万元单位")
	protected BigDecimal zczbbwb;// 注册资本(本位币)万元单位
  	@ApiParam(value="注册资本(人民币) 万元")
	protected BigDecimal zczbrmb;// 注册资本(人民币) 万元
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="注册日期")
	protected Date zcrq;// 注册日期
  	@ApiParam(value="住所")
	protected String zhusuo;// 住所
  	@ApiParam(value="经营状态 枚举值见“登记状态代码”，传递代码。")
	protected String jyzt;// 经营状态 枚举值见“登记状态代码”，传递代码。
  	@ApiParam(value="经营范围")
	protected String jyfw;// 经营范围
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="业务日期 工商登记业务完成的日期。")
	protected Date ywrq;// 业务日期 工商登记业务完成的日期。
  	@ApiParam(value="业务类型 枚举值见“业务类型”，传递代码。")
	protected String ywlx;// 业务类型 枚举值见“业务类型”，传递代码。
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="数据日期 数据在市场监管总局的创建日期，含时间。")
	protected Date sjrq;// 数据日期 数据在市场监管总局的创建日期，含时间。
  	@ApiParam(value="预留1")
	protected String yuliu1;// 预留1
  	@ApiParam(value="预留2")
	protected String yuliu2;// 预留2
	private List<GsInfoInvestors>  investors;

	public List<GsInfoInvestors> getInvestors() {
		return investors;
	}

	public void setInvestors(List<GsInfoInvestors> investors) {
		this.investors = investors;
	}

	public GsInfo() {
		super();
	}
	
  	public GsInfo(Long id) {
  		super();
  		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getYwid() {
		return ywid;
	}
	public void setYwid(String ywid) {
		this.ywid = ywid;
	}
	public String getCqqymc() {
		return cqqymc;
	}
	public void setCqqymc(String cqqymc) {
		this.cqqymc = cqqymc;
	}
	public String getGsqymc() {
		return gsqymc;
	}
	public void setGsqymc(String gsqymc) {
		this.gsqymc = gsqymc;
	}
	public String getXybm() {
		return xybm;
	}
	public void setXybm(String xybm) {
		this.xybm = xybm;
	}
	public String getQylx() {
		return qylx;
	}
	public void setQylx(String qylx) {
		this.qylx = qylx;
	}
	public String getBwbmc() {
		return bwbmc;
	}
	public void setBwbmc(String bwbmc) {
		this.bwbmc = bwbmc;
	}
	public BigDecimal getZczbbwb() {
		return zczbbwb;
	}
	public void setZczbbwb(BigDecimal zczbbwb) {
		this.zczbbwb = zczbbwb;
	}
	public BigDecimal getZczbrmb() {
		return zczbrmb;
	}
	public void setZczbrmb(BigDecimal zczbrmb) {
		this.zczbrmb = zczbrmb;
	}
	public Date getZcrq() {
		return zcrq;
	}
	public void setZcrq(Date zcrq) {
		this.zcrq = zcrq;
	}
	public String getZhusuo() {
		return zhusuo;
	}
	public void setZhusuo(String zhusuo) {
		this.zhusuo = zhusuo;
	}
	public String getJyzt() {
		return jyzt;
	}
	public void setJyzt(String jyzt) {
		this.jyzt = jyzt;
	}
	public String getJyfw() {
		return jyfw;
	}
	public void setJyfw(String jyfw) {
		this.jyfw = jyfw;
	}
	public Date getYwrq() {
		return ywrq;
	}
	public void setYwrq(Date ywrq) {
		this.ywrq = ywrq;
	}
	public String getYwlx() {
		return ywlx;
	}
	public void setYwlx(String ywlx) {
		this.ywlx = ywlx;
	}
	public Date getSjrq() {
		return sjrq;
	}
	public void setSjrq(Date sjrq) {
		this.sjrq = sjrq;
	}
	public String getYuliu1() {
		return yuliu1;
	}
	public void setYuliu1(String yuliu1) {
		this.yuliu1 = yuliu1;
	}
	public String getYuliu2() {
		return yuliu2;
	}
	public void setYuliu2(String yuliu2) {
		this.yuliu2 = yuliu2;
	}
}
