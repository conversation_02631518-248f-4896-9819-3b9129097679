package com.zjhc.gzwcq.job.zjh.util;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.io.ZipOutputStream;
import net.lingala.zip4j.model.FileHeader;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/31:11:24:34
 **/
public class ZipEncryptorUtils {
    public static MockMultipartFile encryptZip(MultipartFile multipartFile, String password) throws IOException, ZipException {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             InputStream inputStream = multipartFile.getInputStream()) {
            Path tempFilePath = Files.createTempFile("upload-", "encrypted.zip");
            File tempFile = tempFilePath.toFile();
            multipartFile.transferTo(tempFile);
            // 使用 ZipOutputStream 创建加密的 ZIP 文件
            try (ZipOutputStream zoss = new ZipOutputStream(byteArrayOutputStream)) {
                ZipParameters parameters = new ZipParameters();
                parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
                parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);
                parameters.setEncryptFiles(true);
                parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_AES); // 使用 AES 加密
                parameters.setAesKeyStrength(Zip4jConstants.AES_STRENGTH_256); // 256位 AES 密钥
                parameters.setPassword(password);

                // 添加原始 ZIP 文件内容到新的 ZIP 输出流，并设置加密参数
                zoss.putNextEntry(tempFile, parameters);
                byte[] buffer = new byte[1024];
                int readLen;
                while ((readLen = inputStream.read(buffer)) != -1) {
                    zoss.write(buffer, 0, readLen);
                }

                // 关闭当前 ZIP 条目
                zoss.closeEntry();

                // 完成 ZIP 输出流
                zoss.finish();
            }

            // 获取加密后的 ZIP 文件内容
            byte[] encryptedBytes = byteArrayOutputStream.toByteArray();

            // 创建一个新的 MultipartFile 对象
            return new MockMultipartFile(
                    "data",  // 名称
                    multipartFile.getOriginalFilename(),  // 原始文件名
                    "application/zip",  // MIME 类型
                    new ByteArrayInputStream(encryptedBytes)  // 内容
            );
        } catch (IOException e) {
            throw new RuntimeException("Failed to encrypt the ZIP file", e);
        }
    }

    public static MultipartFile encryptZip2(MultipartFile multipartFile,String password) throws IOException, ZipException {
        // 创建临时文件
        Path tempFile = Files.createTempFile("upload-", "encrypted.zip");
        try (InputStream inputStream = multipartFile.getInputStream()) {
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
        }
        Path fileName = tempFile.getFileName();

        // 创建输出的加密 ZIP 文件
        File encryptedZipFile = new File(tempFile.toFile().getParent(), "data.zip");

        // 使用 Zip4j 创建 ZIP 输出流
        ZipFile zip = new ZipFile(encryptedZipFile);

        // 设置压缩参数
        ZipParameters parameters = new ZipParameters();
        parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
        parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);
        parameters.setEncryptFiles(true);
        parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_STANDARD);
        parameters.setAesKeyStrength(Zip4jConstants.AES_STRENGTH_256);
        parameters.setPassword(password);

        // 加密整个 ZIP 文件
        zip.addFile(tempFile.toFile(), parameters);

        // 读取生成的加密 ZIP 文件内容
        byte[] encryptedBytes = Files.readAllBytes(tempFile.toFile().toPath());

        // 删除临时文件
        Files.delete(tempFile);
        Files.delete(encryptedZipFile.toPath());

        // 创建一个新的 MultipartFile 对象
        MockMultipartFile encryptedMultipartFile = new MockMultipartFile(
                "data",  // 名称
                "data.zip",  // 原始文件名
                "application/zip",  // MIME 类型
                encryptedBytes  // 内容
        );
        return encryptedMultipartFile;
    }
}
