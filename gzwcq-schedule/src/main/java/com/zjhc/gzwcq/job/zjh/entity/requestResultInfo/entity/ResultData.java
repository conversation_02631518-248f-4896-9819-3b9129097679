package com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/07/05:16:39:31
 **/
public class ResultData {
    private Long checkResult;
    private Long recordCount;
    private String status;
    private Long total;
    private List<ErrorInfo> errorArray;

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(Long checkResult) {
        this.checkResult = checkResult;
    }

    public Long getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(Long recordCount) {
        this.recordCount = recordCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<ErrorInfo> getErrorArray() {
        return errorArray;
    }

    public void setErrorArray(List<ErrorInfo> errorArray) {
        this.errorArray = errorArray;
    }
}

