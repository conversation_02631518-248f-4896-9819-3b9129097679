package com.zjhc.gzwcq.job.zjh.entity.gsInfoInvestors.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： scjd_gs_info_investors <br/>
 *         描述：scjd_gs_info_investors <br/>
 */
public class GsInfoInvestorsVo extends GsInfoInvestors {

	private static final long serialVersionUID = 18L;

	private List<GsInfoInvestorsVo> gsInfoInvestorsList;

	public GsInfoInvestorsVo() {
		super();
	}

  	public GsInfoInvestorsVo(Long id) {
  		super();
  		this.id = id;
	}

	public List<GsInfoInvestorsVo> getGsInfoInvestorsList() {
		return gsInfoInvestorsList;
	}

	public void setGsInfoInvestorsList(List<GsInfoInvestorsVo> gsInfoInvestorsList) {
		this.gsInfoInvestorsList = gsInfoInvestorsList;
	}

}
