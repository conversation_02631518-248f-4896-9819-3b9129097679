package com.zjhc.gzwcq.job.zjh.mapper;

import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfo;
import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoParam;
import com.zjhc.gzwcq.job.zjh.entity.gsInfo.entity.GsInfoVo;
import com.zjhc.gzwcq.job.zjh.entity.gsInfoInvestors.entity.GsInfoInvestors;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IGsInfoMapper {
	
	/*保存对象*/
	void insert(GsInfo gsInfo);

	void insertForList(@Param("list") List<GsInfo> list);

	void  insertForInvestorsList(@Param("list") List<GsInfoInvestors> list);

	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(GsInfo gsInfo);
	
	/**更新*/
	void update(GsInfo gsInfo);
	
	/*分页查询对象*/
	List<GsInfoVo> queryGsInfoForList(GsInfoParam gsInfoParam);
	
	/*数据总量查询*/
	long queryTotalGsInfos(GsInfoParam gsInfoParam);
	
	/*根据主键查询对象*/
	GsInfo selectGsInfoByPrimaryKey(GsInfo gsInfo);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<GsInfo> selectForList(GsInfo gsInfo);
	
	/**
	 * 数据唯一性验证
	 * */
	List<GsInfo> selectForUnique(GsInfo gsInfo);
	
}