package com.zjhc.gzwcq.job.zjh.entity.gjPush.request;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/28:11:28:49
 **/
@Data
public class ValueDTO {
    /**
     * 来源系统编号*
     */
    private String source_system_no;
    /**
     * 第三方唯一编号(业务系统预警唯一编号)*
     */
    private String unique_key;
    /**
     * 来源规则编号*
     */
    private String source_rule_no;
    /**
     * 集团编号*
     */
    private String group_code;
    /**
     * 集团名称*
     */
    private String group_name;
    /**
     * 企业代码*
     */
    private String enterprise_code;
    /**
     * 企业名称*
     */
    private String enterprise_name;
    /**
     * 部门名称*
     */
    private String dept_name;
    /**
     * 部门编号*
     */
    private String dept_code;
    /**
     * * 预警详情
     */
    private String warning_detail;
    /**
     * *来源数据（数据推送时 序列化成json字符串）
     */
    private String source_content;
    /**
     * 预警时间*
     */
    private String warning_time;
    /**
     * 创建时间*
     */
    private String log_time;
    /**
     * 预警类型*
     */
    private String entity_type;


    public ValueDTO() {
    }

    /**
     * *
     * @param source_system_no
     * @param unique_key
     * @param source_rule_no
     * @param group_code
     * @param group_name
     * @param enterprise_code
     * @param enterprise_name
     * @param dept_name
     * @param dept_code
     * @param warning_detail
     * @param source_content
     * @param warning_time
     * @param log_time
     * @param entity_type
     */
    public ValueDTO(String source_system_no, String unique_key, String source_rule_no, String group_code, String group_name, String enterprise_code, String enterprise_name, String dept_name, String dept_code, String warning_detail, String source_content, String warning_time, String log_time, String entity_type) {
        this.source_system_no = source_system_no;
        this.unique_key = unique_key;
        this.source_rule_no = source_rule_no;
        this.group_code = group_code;
        this.group_name = group_name;
        this.enterprise_code = enterprise_code;
        this.enterprise_name = enterprise_name;
        this.dept_name = dept_name;
        this.dept_code = dept_code;
        this.warning_detail = warning_detail;
        this.source_content = source_content;
        this.warning_time = warning_time;
        this.log_time = log_time;
        this.entity_type = entity_type;
    }

    public String getSource_system_no() {
        return source_system_no;
    }

    public void setSource_system_no(String source_system_no) {
        this.source_system_no = source_system_no;
    }

    public String getUnique_key() {
        return unique_key;
    }

    public void setUnique_key(String unique_key) {
        this.unique_key = unique_key;
    }

    public String getSource_rule_no() {
        return source_rule_no;
    }

    public void setSource_rule_no(String source_rule_no) {
        this.source_rule_no = source_rule_no;
    }

    public String getGroup_code() {
        return group_code;
    }

    public void setGroup_code(String group_code) {
        this.group_code = group_code;
    }

    public String getGroup_name() {
        return group_name;
    }

    public void setGroup_name(String group_name) {
        this.group_name = group_name;
    }

    public String getEnterprise_code() {
        return enterprise_code;
    }

    public void setEnterprise_code(String enterprise_code) {
        this.enterprise_code = enterprise_code;
    }

    public String getEnterprise_name() {
        return enterprise_name;
    }

    public void setEnterprise_name(String enterprise_name) {
        this.enterprise_name = enterprise_name;
    }

    public String getDept_name() {
        return dept_name;
    }

    public void setDept_name(String dept_name) {
        this.dept_name = dept_name;
    }

    public String getDept_code() {
        return dept_code;
    }

    public void setDept_code(String dept_code) {
        this.dept_code = dept_code;
    }

    public String getWarning_detail() {
        return warning_detail;
    }

    public void setWarning_detail(String warning_detail) {
        this.warning_detail = warning_detail;
    }

    public String getSource_content() {
        return source_content;
    }

    public void setSource_content(String source_content) {
        this.source_content = source_content;
    }

    public String getWarning_time() {
        return warning_time;
    }

    public void setWarning_time(String warning_time) {
        this.warning_time = warning_time;
    }

    public String getLog_time() {
        return log_time;
    }

    public void setLog_time(String log_time) {
        this.log_time = log_time;
    }

    public String getEntity_type() {
        return entity_type;
    }

    public void setEntity_type(String entity_type) {
        this.entity_type = entity_type;
    }
}
