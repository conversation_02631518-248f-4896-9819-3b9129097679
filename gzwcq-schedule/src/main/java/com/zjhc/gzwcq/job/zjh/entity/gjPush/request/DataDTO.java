package com.zjhc.gzwcq.job.zjh.entity.gjPush.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/28:10:30:12
 **/
@Data
public class DataDTO {
    /**
    * @description: TODO 请求类型
    * @author: hhy
    * @date: 2025/6/13 16:12
    * @param null 参数说明
    * @return
    */
    private String operateType;
    /**
    * @description: TODO 请求时间
    * @author: hhy
    * @date: 2025/6/13 16:12
    * @param null 参数说明
    * @return
    */
    private String operateTime;
    /**
    * @description: TODO 请求参数
    * @author: hhy
    * @date: 2025/6/13 16:13
    * @param null 参数说明
    * @return
    */
    private ValueDTO value;

    public DataDTO() {
    }

    public DataDTO(String operateType, String operateTime, ValueDTO value) {
        this.operateType = operateType;
        this.operateTime = operateTime;
        this.value = value;
    }
}
