<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IGjPushMsgMapper">
    <select id="selectJbxxbBO" resultType="com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO">
        SELECT
            jb.JB_QYMC as jbQymc,
            jb.J<PERSON>_ZZJGDM as jbQybm,
            jb.id  as jbQyId,
            jbo.parentsId,
            jbo.ORGANIZATION_ID as orgId,
            info.LAST_UPDATE_TIME as infoTime,
            IFNULL(dic17.text,IFNULL(dic18.text,dic19.text)) ,
            IF(dic17.text is null,IF(dic18.text is null,'注销','变动'),'占有') as infoType
        FROM
            (
                SELECT
                    jb.id,
                    jb.RG_TIMEMARK,
                    jb.parentsId,
                    jb.ORGANIZATION_ID
                FROM
                    (
                        SELECT
                            info.JBXX_ID id,
                            info.RG_TIMEMARK,
                            info.UNITID,
                            o.parentsId,
                            o.ORGANIZATION_ID
                        FROM
                            (
                                SELECT
                                    o.ORGANIZATION_ID,
                                    oid.ORGANIZATION_ID as parentsId
                                FROM
                                    (
                                        SELECT
                                            o.ORGANIZATION_ID
                                        FROM
                                            sys_organization o
                                        WHERE
                                                o.ORGANIZATION_ID IN (
                                                                      '3B1230BE60000041C9273F458A52D4AF',
                                                                      '3A4E70B2400002217D8A4BBD0FD5088C',
                                                                      '3B123053C0000081C1BD59E708CB2D75',
                                                                          '3AEDFC5DE000000129FA000318B384FD',
                                                                          '3A86340260000021BDC43236F4BCA409',
                                                                          '3AB6E46CE00000410EA2FD1D415C5956',
                                                                          '3B21463480000041C6D24CBCAF4A4E25',
                                                                          '3B2D48502000000139C3676E0BA738B1',
                                                                          '3AC1A0720000000183A3BA429E01CC0B',
                                                                          '39DC82B5C00000E1996BA4F3F49632B6',
                                                                          '3A4DEB4900000001F458D955E27A1664',
                                                                          '3F135971E0000021F8007519B85CB393',
                                                                          '4C77B6B10000004195C703A922268DA4',
                                                                          '51EC450620000021AA41C9E6231D82A4',
                                                                          '4F060870A0000021A0DA1951BCBEA650',
                                                                          '8a8a34788016c8a40180220beaf70005',
                                                                          '8a8a34788634b79401864dabf0c40036'
                                                )
                                          AND o.isdeleted = 'N'
                                    ) oid
                                        JOIN sys_organization o ON FIND_IN_SET( oid.ORGANIZATION_ID, o.PARENTS )
                                        AND o.isdeleted = 'N'
                            ) o
                                JOIN rg_business_info info on info.UNITID = o.ORGANIZATION_ID
                        WHERE
                            info.RG_UNITSTATE = 2
                        ORDER BY
                            info.RG_TIMEMARK  desc
                        LIMIT 1000000000
                    ) jb
                GROUP BY
                    jb.UNITID
            )jbo JOIN  view_cq_jbxxb jb on jb.ID = jbo.id
                 JOIN rg_business_info info on info.JBXX_ID = jb.id
                 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                          (SELECT id FROM sys_dictionary WHERE type_code ='RELA')) dic3
                           on dic3.val = jb.JB_RELA
                 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                          (SELECT id FROM sys_dictionary WHERE type_code ='ZYCQDJQX')) dic17
                           on dic17.val = jb.JB_ZYCQDJQX

                 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                          (SELECT id FROM sys_dictionary WHERE type_code ='BDCQDJQX')) dic18
                           on dic18.val = jb.JB_BDCQDJQX

                 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                          (SELECT id FROM sys_dictionary WHERE type_code ='ZXCQDJQX')) dic19
                           on dic19.val = jb.JB_ZXCQDJQX
        <where>
            <if test="warnModelEnum != null and warnModelEnum.name() == 'MODEL_1'">
                and dic3.text = '参股'
            </if>
            <if test="warnModelEnum != null and warnModelEnum.name() == 'MODEL_2'">
                and (dic3.text in ('全资', '控股', '实际控制') and jb.jb_sf_bb = '0')
            </if>
        </where>
    </select>
    <select id="selectJbxxbBOToal" resultType="Long">
        select
            count(1)
        FROM
            (
                SELECT
                    jb.id,
                    jb.RG_TIMEMARK,
                    jb.parentsId
                FROM
                    (
                        SELECT
                            info.JBXX_ID id,
                            info.RG_TIMEMARK,
                            info.UNITID,
                            parentsId
                        FROM
                            (
                                SELECT
                                    o.ORGANIZATION_ID,
                                    oid.ORGANIZATION_ID as parentsId
                                FROM
                                    (
                                        SELECT
                                            o.ORGANIZATION_ID
                                        FROM
                                            sys_organization o
                                        WHERE
                                                o.ORGANIZATION_ID IN (
                                                                      '3B1230BE60000041C9273F458A52D4AF',
                                                                      '3A4E70B2400002217D8A4BBD0FD5088C',
                                                                      '3B123053C0000081C1BD59E708CB2D75',
                                                                          '3AEDFC5DE000000129FA000318B384FD',
                                                                          '3A86340260000021BDC43236F4BCA409',
                                                                          '3AB6E46CE00000410EA2FD1D415C5956',
                                                                          '3B21463480000041C6D24CBCAF4A4E25',
                                                                          '3B2D48502000000139C3676E0BA738B1',
                                                                          '3AC1A0720000000183A3BA429E01CC0B',
                                                                          '39DC82B5C00000E1996BA4F3F49632B6',
                                                                          '3A4DEB4900000001F458D955E27A1664',
                                                                          '3F135971E0000021F8007519B85CB393',
                                                                          '4C77B6B10000004195C703A922268DA4',
                                                                          '51EC450620000021AA41C9E6231D82A4',
                                                                          '4F060870A0000021A0DA1951BCBEA650',
                                                                          '8a8a34788016c8a40180220beaf70005',
                                                                          '8a8a34788634b79401864dabf0c40036'
                                                )
                                          AND o.isdeleted = 'N'
                                    ) oid
                                        JOIN sys_organization o ON FIND_IN_SET( oid.ORGANIZATION_ID, o.PARENTS )
                                        AND o.isdeleted = 'N'
                            ) o
                                JOIN rg_business_info info on info.UNITID = o.ORGANIZATION_ID
                        WHERE
                            info.RG_UNITSTATE = 2
                        ORDER BY
                            info.RG_TIMEMARK  desc
                        LIMIT 1000000000
                    ) jb
                GROUP BY
                    jb.UNITID
            )jbo JOIN  view_cq_jbxxb jb on jb.ID = jbo.id
                 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
                                                                          (SELECT id FROM sys_dictionary WHERE type_code ='RELA')) dic3
                           on dic3.val = jb.JB_RELA
        <where>
            <if test="warnModelEnum != null and  warnModelEnum.name() == 'MODEL_1'">
                and dic3.text = '参股'
            </if>
            <if test="warnModelEnum != null and warnModelEnum.name() == 'MODEL_2'">
                and (dic3.text in ('全资', '控股', '实际控制') and jb.jb_sf_bb = '0')
            </if>
        </where>       

    </select>
    <select id="selectEarlyWarning" resultType="com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.DicWarningBO">
        select sd1.val as orgId ,sd1.text  as text from sys_dictionary sd1
             join sys_dictionary sd2 on sd1.type_id = sd2.id
        where
           sd2.type_code = #{warningKey}
    </select>
    
    <select id="getOrgName" resultType="com.zjhc.gzwcq.job.zjh.entity.gjPush.vo.OrgInfoVO">
        select
            org.ORGANIZATION_CODE as orgCode,
            org.ORGANIZATION_NAME as orgName
        from
            sys_organization org
        where
            org.ORGANIZATION_ID = #{orgId}
    </select>
</mapper>