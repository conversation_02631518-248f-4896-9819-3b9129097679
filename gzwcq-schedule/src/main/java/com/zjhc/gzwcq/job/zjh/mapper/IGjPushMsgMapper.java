package com.zjhc.gzwcq.job.zjh.mapper;

import com.zjhc.gzwcq.job.zjh.entity.enums.WarnModelEnum;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.DicWarningBO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO;
import com.zjhc.gzwcq.job.zjh.entity.gjPush.vo.OrgInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/06/13 10:26:41
 **/
public interface IGjPushMsgMapper {

    List<JbxxbBO> selectJbxxbBO(@Param("warnModelEnum") WarnModelEnum warnModelEnum);

    Long selectJbxxbBOToal(@Param("warnModelEnum") WarnModelEnum warnModelEnum);

    List<DicWarningBO> selectEarlyWarning(@Param("warningKey") String warningKey);

    OrgInfoVO getOrgName(@Param("orgId") String orgId);
}
