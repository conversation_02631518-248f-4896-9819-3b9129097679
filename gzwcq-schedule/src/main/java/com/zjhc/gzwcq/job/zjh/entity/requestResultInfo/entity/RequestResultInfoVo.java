package com.zjhc.gzwcq.job.zjh.entity.requestResultInfo.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： api_request_result_info <br/>
 *         描述：api_request_result_info <br/>
 */
public class RequestResultInfoVo extends RequestResultInfo {

	private static final long serialVersionUID = 18L;

	private List<RequestResultInfoVo> requestResultInfoList;

	public RequestResultInfoVo() {
		super();
	}

  	public RequestResultInfoVo(Long id) {
  		super();
  		this.id = id;
	}

	public List<RequestResultInfoVo> getRequestResultInfoList() {
		return requestResultInfoList;
	}

	public void setRequestResultInfoList(List<RequestResultInfoVo> requestResultInfoList) {
		this.requestResultInfoList = requestResultInfoList;
	}

}
