package com.zjhc.gzwcq.job.zjh.handler;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete;
import com.zjhc.gzwcq.job.zjh.entity.api.DTO.ProjectCompleteDTO;
import com.zjhc.gzwcq.job.zjh.entity.api.VO.ResultLoginVO;
import com.zjhc.gzwcq.job.zjh.service.api.IZjhHandlerService;
import groovy.util.logging.Slf4j;


/**
 * <AUTHOR>
 * @ClassName: ZjhHandler
 * @date 2023-04-21 11:24:57
 * @Description: 浙交汇定时任务
 */
@Component
@Slf4j
public class ZjhHandler{
	
	private static Logger logger = LoggerFactory.getLogger(ZjhHandler.class);


    @Autowired
    private IZjhHandlerService service;

    /***
     *@MethodName: doHandle
     *@Description: 同步数据
     *@see com.zjhc.gzwcq.job.handler.IBaseHandler#doHandle()
     *<AUTHOR>
     *@date 2023-04-21 11:25:27
     */
    @XxlJob(value = "zjhHandler", init = "init", destroy = "destroy")
    @Transactional(rollbackFor = Exception.class)
    public void doHandle() {
        long endTime;
        long starTime;
        Long time = null;
        starTime = System.currentTimeMillis();
        try {
            XxlJobHelper.log("同步浙交汇项目成交信息开始");
        	logger.info("同步浙交汇项目成交信息开始");
            //获取token
            ResultLoginVO loginVO = service.login().getData();
            logger.info("访问浙交汇登录返回：\t" + loginVO);
            String token = loginVO.getToken();
            if (StringUtils.isBlank(token)) {
                logger.error("浙交汇登录没返回token");
                XxlJobHelper.log("浙交汇登录没返回token");
                //当自定义异常使用
                throw new NullPointerException();
            }
            ProjectCompleteDTO dto = new ProjectCompleteDTO();
            dto.setToken(token);
            //查询并插入项目成交数据
            List<ExtProjectComplete> projectCompleteList = service.extProjectCompleteData(dto);
            if (Objects.isNull(projectCompleteList) || projectCompleteList.isEmpty()) {
                XxlJobHelper.log("无项目成交数据");
                logger.info("无项目成交数据");
                //自定义异常使用
                throw new NullPointerException();
            }
            //开始查询并插入项目受让方数据
            service.extProjectTransfereeData(projectCompleteList, dto);
            endTime = System.currentTimeMillis();
        } catch (NullPointerException e) {
            //当自定义异常使用 todo 因为不可能出现空指针 空值处理非常可
            endTime = System.currentTimeMillis();
        } catch (Exception e) {
            logger.error("浙交汇同步接口未知异常err：\t", e);
            XxlJobHelper.log("浙交汇同步接口未知异常err：\t", e);
            throw new RuntimeException();
        }
        time = (endTime - starTime) / 1000;
        XxlJobHelper.log("同步浙交汇项目成交信息结束 用时：\t" + time);
        logger.info("同步浙交汇项目成交信息结束 用时：\t" + time);
    }
    
    public void init(){
        logger.info("init");
    }
    
    public void destroy(){
        logger.info("destroy");
    }
}
