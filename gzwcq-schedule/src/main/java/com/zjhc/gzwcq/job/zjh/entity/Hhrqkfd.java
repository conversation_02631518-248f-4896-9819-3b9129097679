package com.zjhc.gzwcq.job.zjh.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> <br/>
 *         表名： cq_hhrqkfd <br/>
 *         描述：合伙人情况浮动 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Hhrqkfd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="合伙人名称")
	protected String name;// 合伙人名称
  	@ApiParam(value="统一信用代码")
	protected String hhrCode;// 统一信用代码
  	@ApiParam(value="合伙人类型")
	protected String type;// 合伙人类型
  	@ApiParam(value="合伙人类别")
	protected String category;// 合伙人类别
	@ApiParam(value="出资额（万元）")
	protected BigDecimal fdCze;// 出资额（万元）
  	@ApiParam(value="认缴出资额（万元）")
	protected BigDecimal rjcze;// 认缴出资额（万元）
  	@ApiParam(value="认缴出资比例")
	protected BigDecimal rjczbl;// 认缴出资比例
  	@ApiParam(value="实缴出资额（万元）")
	protected BigDecimal sjcze;// 实缴出资额（万元）
  	@ApiParam(value="出资方式")
	protected String czfs;// 出资方式
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	@ApiParam(value="缴付期限")
	protected Date jfqx;// 缴付期限
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Hhrqkfd() {
		super();
	}
	
  	public Hhrqkfd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getHhrCode() {
		return hhrCode;
	}

	public void setHhrCode(String hhrCode) {
		this.hhrCode = hhrCode;
	}

	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getCategory() {
		return category;
	}
	public void setCategory(String category) {
		this.category = category;
	}
	public BigDecimal getRjcze() {
		return rjcze;
	}
	public void setRjcze(BigDecimal rjcze) {
		this.rjcze = rjcze;
	}
	public BigDecimal getRjczbl() {
		return rjczbl;
	}
	public void setRjczbl(BigDecimal rjczbl) {
		this.rjczbl = rjczbl;
	}
	public BigDecimal getSjcze() {
		return sjcze;
	}
	public void setSjcze(BigDecimal sjcze) {
		this.sjcze = sjcze;
	}
	public String getCzfs() {
		return czfs;
	}
	public void setCzfs(String czfs) {
		this.czfs = czfs;
	}

	public Date getJfqx() {
		return jfqx;
	}

	public void setJfqx(Date jfqx) {
		this.jfqx = jfqx;
	}

	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public BigDecimal getFdCze() {
		return fdCze;
	}

	public void setFdCze(BigDecimal fdCze) {
		this.fdCze = fdCze;
	}
}
