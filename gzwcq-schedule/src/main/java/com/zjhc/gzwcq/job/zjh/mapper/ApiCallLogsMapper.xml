<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.job.zjh.mapper.IApiCallLogsMapper">

	<resultMap type="com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogs" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="type" property="type"/>
		<result column="app_id" property="appId"/>
		<result column="api_name" property="apiName"/>
		<result column="request_method" property="requestMethod"/>
		<result column="request_url" property="requestUrl"/>
		<result column="request_headers" property="requestHeaders"/>
		<result column="request" property="request"/>
		<result column="response_status_code" property="responseStatusCode"/>
		<result column="response" property="response"/>
		<result column="execution_time_ms" property="executionTimeMs"/>
		<result column="client_ip" property="clientIp"/>
		<result column="status" property="status"/>
		<result column="err_msg" property="errMsg"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogsVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		type, 
		app_id, 
		api_name, 
		request_method, 
		request_url, 
		request_headers, 
		request, 
		response_status_code, 
		response, 
		execution_time_ms, 
		client_ip, 
		status, 
		err_msg, 
		create_time, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.type, 
		t.app_id, 
		t.api_name, 
		t.request_method, 
		t.request_url, 
		t.request_headers, 
		t.request, 
		t.response_status_code, 
		t.response, 
		t.execution_time_ms, 
		t.client_ip, 
		t.status, 
		t.err_msg, 
		t.create_time, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{type}, 
		#{appId}, 
		#{apiName}, 
		#{requestMethod}, 
		#{requestUrl}, 
		#{requestHeaders}, 
		#{request}, 
		#{responseStatusCode}, 
		#{response}, 
		#{executionTimeMs}, 
		#{clientIp}, 
		#{status}, 
		#{errMsg}, 
		#{createTime}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null">
			and t.id = #{id}
		</if>
		<if test="type != null">
			and t.type = #{type}
		</if>
		<if test="appId != null and appId != ''">
			and t.app_id = #{appId}
		</if>
		<if test="apiName != null and apiName != ''">
			and t.api_name = #{apiName}
		</if>
		<if test="requestMethod != null and requestMethod != ''">
			and t.request_method = #{requestMethod}
		</if>
		<if test="requestUrl != null and requestUrl != ''">
			and t.request_url = #{requestUrl}
		</if>
		<if test="requestHeaders != null and requestHeaders != ''">
			and t.request_headers = #{requestHeaders}
		</if>
		<if test="request != null and request != ''">
			and t.request = #{request}
		</if>
		<if test="responseStatusCode != null">
			and t.response_status_code = #{responseStatusCode}
		</if>
		<if test="response != null and response != ''">
			and t.response = #{response}
		</if>
		<if test="executionTimeMs != null">
			and t.execution_time_ms = #{executionTimeMs}
		</if>
		<if test="clientIp != null and clientIp != ''">
			and t.client_ip = #{clientIp}
		</if>
		<if test="status != null">
			and t.status = #{status}
		</if>
		<if test="errMsg != null and errMsg != ''">
			and t.err_msg = #{errMsg}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogs" useGeneratedKeys="true" keyProperty="id">
		<!-- <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey> -->
		insert into tbl_api_call_logs (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update tbl_api_call_logs set isDeleted = 'Y' where
		id in
		<foreach collection="apiCallLogss" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update tbl_api_call_logs set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from tbl_api_call_logs  where
		id in
		<foreach collection="apiCallLogss" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from tbl_api_call_logs  where id = #{id}
	</delete>
	
	<select id="selectApiCallLogsByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from tbl_api_call_logs
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update tbl_api_call_logs
		<set>
			<if test="type != null">
				type=#{type},
			</if>
			<if test="appId != null">
				app_id=#{appId},
			</if>
			<if test="apiName != null">
				api_name=#{apiName},
			</if>
			<if test="requestMethod != null">
				request_method=#{requestMethod},
			</if>
			<if test="requestUrl != null">
				request_url=#{requestUrl},
			</if>
			<if test="requestHeaders != null">
				request_headers=#{requestHeaders},
			</if>
			<if test="request != null">
				request=#{request},
			</if>
			<if test="responseStatusCode != null">
				response_status_code=#{responseStatusCode},
			</if>
			<if test="response != null">
				response=#{response},
			</if>
			<if test="executionTimeMs != null">
				execution_time_ms=#{executionTimeMs},
			</if>
			<if test="clientIp != null">
				client_ip=#{clientIp},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
			<if test="errMsg != null">
				err_msg=#{errMsg},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update tbl_api_call_logs
		<set>
			type=#{type},
			app_id=#{appId},
			api_name=#{apiName},
			request_method=#{requestMethod},
			request_url=#{requestUrl},
			request_headers=#{requestHeaders},
			request=#{request},
			response_status_code=#{responseStatusCode},
			response=#{response},
			execution_time_ms=#{executionTimeMs},
			client_ip=#{clientIp},
			status=#{status},
			err_msg=#{errMsg},
			create_time=#{createTime},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogs" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			tbl_api_call_logs t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalApiCallLogss" parameterType="com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogsParam" resultType="java.lang.Long">
		select
			count(id)
		from tbl_api_call_logs t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryApiCallLogsForList" parameterType="com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogsParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			tbl_api_call_logs t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.job.zjh.entity.apiCallLogs.entity.ApiCallLogs" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from tbl_api_call_logs t
		where t.id != #{id}
			<if test="type != null">
				and t.type = #{type}
			</if>
			<if test="appId != null and appId != ''">
				and t.app_id = #{appId}
			</if>
			<if test="apiName != null and apiName != ''">
				and t.api_name = #{apiName}
			</if>
			<if test="requestMethod != null and requestMethod != ''">
				and t.request_method = #{requestMethod}
			</if>
			<if test="requestUrl != null and requestUrl != ''">
				and t.request_url = #{requestUrl}
			</if>
			<if test="requestHeaders != null and requestHeaders != ''">
				and t.request_headers = #{requestHeaders}
			</if>
			<if test="request != null and request != ''">
				and t.request = #{request}
			</if>
			<if test="responseStatusCode != null">
				and t.response_status_code = #{responseStatusCode}
			</if>
			<if test="response != null and response != ''">
				and t.response = #{response}
			</if>
			<if test="executionTimeMs != null and executionTimeMs != ''">
				and t.execution_time_ms = #{executionTimeMs}
			</if>
			<if test="clientIp != null and clientIp != ''">
				and t.client_ip = #{clientIp}
			</if>
			<if test="status != null">
				and t.status = #{status}
			</if>
			<if test="errMsg != null and errMsg != ''">
				and t.err_msg = #{errMsg}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

</mapper>