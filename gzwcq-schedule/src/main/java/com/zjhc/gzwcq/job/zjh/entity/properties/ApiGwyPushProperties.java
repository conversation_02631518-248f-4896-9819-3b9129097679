package com.zjhc.gzwcq.job.zjh.entity.properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/07:16:25:19
 **/
public class ApiGwyPushProperties {
    private String updateAllDfqyUrl;
    private String appId;
    private String appSecret;
    private String getDfqyUploadStatusUrl;
    private String filePath;
    private String getGsInfoUrl;

    public String getGetGsInfoUrl() {
        return getGsInfoUrl;
    }

    public void setGetGsInfoUrl(String getGsInfoUrl) {
        this.getGsInfoUrl = getGsInfoUrl;
    }

    public String getUpdateAllDfqyUrl() {
        return updateAllDfqyUrl;
    }

    public ApiGwyPushProperties() {
    }

    public ApiGwyPushProperties(String updateAllDfqyUrl, String appId, String appSecret, String getDfqyUploadStatusUrl) {
        this.updateAllDfqyUrl = updateAllDfqyUrl;
        this.appId = appId;
        this.appSecret = appSecret;
        this.getDfqyUploadStatusUrl = getDfqyUploadStatusUrl;
    }

    public void setUpdateAllDfqyUrl(String updateAllDfqyUrl) {
        this.updateAllDfqyUrl = updateAllDfqyUrl;
    }

    public String getAppId() {
        return appId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getGetDfqyUploadStatusUrl() {
        return getDfqyUploadStatusUrl;
    }

    public void setGetDfqyUploadStatusUrl(String getDfqyUploadStatusUrl) {
        this.getDfqyUploadStatusUrl = getDfqyUploadStatusUrl;
    }
}
