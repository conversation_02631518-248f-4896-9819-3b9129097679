package com.zjhc.gzwcq.job.zjh.entity.gjPush.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/28:10:04:42
 **/
@Data
public class GjPushDTO {
    /**
     * 消息类型: 数据变更（data），这里应该是数据变更(data)  * 必填
     */
    private String infoType;
    /**
     * 上报批次标识符： 如果填写批次号，表示分批次传输，支持断点续传。 如果不填表示，则表示实时推送
     * 非必填 * *
     */
    private String batchId;
    /**
     * * 本次分发是这个批次的第几个包，如果batchId不为空，则这个字段必填
     */
    private Integer currentBatchIndex;
    /**
     * *这个批次总计分了多少次分发，如果batchId不为空，则这个字段必填
     */
    private Integer batchCount;
    /**
     * * 这个批次总计有多少变更数据，如果batchId不为空，则这个字段必填
     */
    private Integer dataCount;
    /**
     * * 上报单位的单位编码 必填
     */
    private String orgCode;
    /**
     * 数据资源编号   见 4数据资源清单  必填*
     */
    private String resourceCode;
    /**
     * * 转发规则 来源系统编码：如0600 必填
     */
    private String forwardRule;
    /**
     * 消息摘要      非必填*
     */
    private String message;
    /**
     * 用公钥加密后的数据密钥，以base64编码 *
     */
    private String sk;
    /**
     * 加密后的数据，以base64编码 *
     */
    private String data;

    public GjPushDTO() {
    }

    /**
     * *
     * @param infoType
     * @param batchId
     * @param currentBatchIndex
     * @param batchCount
     * @param dataCount
     * @param orgCode
     * @param resourceCode
     * @param forwardRule
     * @param message
     * @param sk
     * @param data
     */
    public GjPushDTO(String infoType, String batchId, Integer currentBatchIndex, Integer batchCount, Integer dataCount, String orgCode, String resourceCode, String forwardRule, String message, String sk, String data) {
        this.infoType = infoType;
        this.batchId = batchId;
        this.currentBatchIndex = currentBatchIndex;
        this.batchCount = batchCount;
        this.dataCount = dataCount;
        this.orgCode = orgCode;
        this.resourceCode = resourceCode;
        this.forwardRule = forwardRule;
        this.message = message;
        this.sk = sk;
        this.data = data;
    }

    public String getInfoType() {
        return infoType;
    }

    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Integer getCurrentBatchIndex() {
        return currentBatchIndex;
    }

    public void setCurrentBatchIndex(Integer currentBatchIndex) {
        this.currentBatchIndex = currentBatchIndex;
    }

    public Integer getBatchCount() {
        return batchCount;
    }

    public void setBatchCount(Integer batchCount) {
        this.batchCount = batchCount;
    }

    public Integer getDataCount() {
        return dataCount;
    }

    public void setDataCount(Integer dataCount) {
        this.dataCount = dataCount;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getResourceCode() {
        return resourceCode;
    }

    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode;
    }

    public String getForwardRule() {
        return forwardRule;
    }

    public void setForwardRule(String forwardRule) {
        this.forwardRule = forwardRule;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
