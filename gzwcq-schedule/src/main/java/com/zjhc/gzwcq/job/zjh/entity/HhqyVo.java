package com.zjhc.gzwcq.job.zjh.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_hhqy <br/>
 *         描述：合伙企业信息扩展表 <br/>
 */
public class HhqyVo extends Hhqy {

		private static final long serialVersionUID = 18L;

		private List<HhqyVo> hhqyList;
		private String jbJnjw;
		private String hhGjczqyStr;//国家出资企业名称
		private String hhSfsmtzjjStr;//是否私募投资基金
		private String hhRjczebzStr;//认缴出资额币种
		private String hhSjczebzStr;//实缴出资额币种
		private String hhHhxyStr;//合伙协议文件名称
		private String hhCzqyStr;//出资企业

		private String hhZyjycsStr;//主要经营场所
		private List<String> hhJyfwList;//经营范围集合
		private String hhJyfwStr;//经营范围

		public HhqyVo() {
			super();
		}

		public HhqyVo(String id) {
			super();
			this.id = id;
		}

	public String getJbJnjw() {
		return jbJnjw;
	}

	public void setJbJnjw(String jbJnjw) {
		this.jbJnjw = jbJnjw;
	}

	public List<HhqyVo> getHhqyList() {
			return hhqyList;
		}

		public void setHhqyList(List<HhqyVo> hhqyList) {
			this.hhqyList = hhqyList;
		}

		public String getHhSfsmtzjjStr() {
			return hhSfsmtzjjStr;
		}

		public void setHhSfsmtzjjStr(String hhSfsmtzjjStr) {
			this.hhSfsmtzjjStr = hhSfsmtzjjStr;
		}

		public String getHhRjczebzStr() {
			return hhRjczebzStr;
		}

		public void setHhRjczebzStr(String hhRjczebzStr) {
			this.hhRjczebzStr = hhRjczebzStr;
		}

		public String getHhSjczebzStr() {
			return hhSjczebzStr;
		}

		public void setHhSjczebzStr(String hhSjczebzStr) {
			this.hhSjczebzStr = hhSjczebzStr;
		}

		public String getHhGjczqyStr() {
			return hhGjczqyStr;
		}

		public void setHhGjczqyStr(String hhGjczqyStr) {
			this.hhGjczqyStr = hhGjczqyStr;
		}

		public String getHhHhxyStr() {
			return hhHhxyStr;
		}

		public void setHhHhxyStr(String hhHhxyStr) {
			this.hhHhxyStr = hhHhxyStr;
		}

		public String getHhCzqyStr() {
			return hhCzqyStr;
		}

		public void setHhCzqyStr(String hhCzqyStr) {
			this.hhCzqyStr = hhCzqyStr;
		}

		public List<String> getHhJyfwList() {
			return hhJyfwList;
		}

		public void setHhJyfwList(List<String> hhJyfwList) {
			this.hhJyfwList = hhJyfwList;
		}

		public String getHhZyjycsStr() {
			return hhZyjycsStr;
		}

		public void setHhZyjycsStr(String hhZyjycsStr) {
			this.hhZyjycsStr = hhZyjycsStr;
		}

		public String getHhJyfwStr() {
			if (hhJyfwList != null && !hhJyfwList.isEmpty()){
				StringBuilder builder = new StringBuilder();
				hhJyfwList.forEach(h -> {
					builder.append(h).append(",");
				});
				return builder.substring(0,builder.length()-1);
			}
			return "";
		}

		public void setHhJyfwStr(String hhJyfwStr) {
			if (hhJyfwList != null && !hhJyfwList.isEmpty()){
				StringBuilder builder = new StringBuilder();
				hhJyfwList.forEach(h -> {
					builder.append(h).append(",");
				});
				this.hhJyfwStr = builder.substring(0,builder.length()-1);
			}else {
				this.hhJyfwStr = "";
			}
		}
}
