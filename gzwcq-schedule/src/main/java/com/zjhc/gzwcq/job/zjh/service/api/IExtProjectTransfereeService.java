package com.zjhc.gzwcq.job.zjh.service.api;

import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeParam;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransfereeVo;

import java.util.List;
import java.util.Map;

public interface IExtProjectTransfereeService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(ExtProjectTransferee extProjectTransferee);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(ExtProjectTransferee extProjectTransferee);
	
	/**
	* 更新
	*/
	void update(ExtProjectTransferee extProjectTransferee);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<ExtProjectTransfereeVo> queryExtProjectTransfereeByPage(ExtProjectTransfereeParam extProjectTransfereeParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalExtProjectTransferees(ExtProjectTransfereeParam extProjectTransfereeParam);
  
	
	/**
	 *通过ID查询数据
	 */
	ExtProjectTransferee selectExtProjectTransfereeByPrimaryKey(ExtProjectTransferee extProjectTransferee);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<ExtProjectTransferee> selectForList(ExtProjectTransferee extProjectTransferee);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(ExtProjectTransferee extProjectTransferee);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(ExtProjectTransferee extProjectTransferee);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(ExtProjectTransferee[] objs);
	
}