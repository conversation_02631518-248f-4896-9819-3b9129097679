package com.zjhc.gzwcq.job.zjh.entity.gsInfoInvestors.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;

/**
 * <AUTHOR> <br/>
 *         表名： scjd_gs_info_investors <br/>
 *         描述：scjd_gs_info_investors <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GsInfoInvestors implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="id")
	protected Long id;// id
  	@ApiParam(value="关联“全级次企业企业数据唯一标识 工商登记业务”业务ID,标识出资人属于哪一笔业务；")
	protected String ywid;// 关联“全级次企业企业数据唯一标识 工商登记业务”业务ID,标识出资人属于哪一笔业务；
  	@ApiParam(value="企业数据统一信用编码")
	protected String xybm;// 企业数据统一信用编码
  	@ApiParam(value="出资人类别 区分机构、自然人股东。枚举值见“出资人类别代码集”工作表。")
	protected String czrlb;// 出资人类别 区分机构、自然人股东。枚举值见“出资人类别代码集”工作表。
  	@ApiParam(value="出资人名称")
	protected String czrmc;// 出资人名称
  	@ApiParam(value="出资人证照类型")
	protected String czrzzlx;// 出资人证照类型
  	@ApiParam(value="出资人统一社会信用编码 出资人证照类型、出资人统一社会信用编码两个字段，当出资人是自然人时，")
	protected String czrbm;// 出资人统一社会信用编码 出资人证照类型、出资人统一社会信用编码两个字段，当出资人是自然人时，
  	@ApiParam(value="本位币名称 该币别需要与“全级次企业工商登记业务”的“本位币名称”保持一致。")
	protected String bwbmc;// 本位币名称 该币别需要与“全级次企业工商登记业务”的“本位币名称”保持一致。
  	@ApiParam(value="认缴资本(本位币) 万元。该金额以本位币计量。")
	protected BigDecimal rjzbbwb;// 认缴资本(本位币) 万元。该金额以本位币计量。
  	@ApiParam(value="万元。该金额为认缴资本（本位币）折算为人民币的金额，如果本位币为人民币，则与认缴资本（本位币）相等。")
	protected BigDecimal rjzbrmb;// 万元。该金额为认缴资本（本位币）折算为人民币的金额，如果本位币为人民币，则与认缴资本（本位币）相等。
  	@ApiParam(value="股权比例")
	protected BigDecimal gqbl;// 股权比例
  	@ApiParam(value="预留1")
	protected String yuliu1;// 预留1
  	@ApiParam(value="预留2")
	protected String yuliu2;// 预留2

	public GsInfoInvestors() {
		super();
	}
	
  	public GsInfoInvestors(Long id) {
  		super();
  		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getYwid() {
		return ywid;
	}
	public void setYwid(String ywid) {
		this.ywid = ywid;
	}
	public String getXybm() {
		return xybm;
	}
	public void setXybm(String xybm) {
		this.xybm = xybm;
	}
	public String getCzrlb() {
		return czrlb;
	}
	public void setCzrlb(String czrlb) {
		this.czrlb = czrlb;
	}
	public String getCzrmc() {
		return czrmc;
	}
	public void setCzrmc(String czrmc) {
		this.czrmc = czrmc;
	}
	public String getCzrzzlx() {
		return czrzzlx;
	}
	public void setCzrzzlx(String czrzzlx) {
		this.czrzzlx = czrzzlx;
	}
	public String getCzrbm() {
		return czrbm;
	}
	public void setCzrbm(String czrbm) {
		this.czrbm = czrbm;
	}
	public String getBwbmc() {
		return bwbmc;
	}
	public void setBwbmc(String bwbmc) {
		this.bwbmc = bwbmc;
	}
	public BigDecimal getRjzbbwb() {
		return rjzbbwb;
	}
	public void setRjzbbwb(BigDecimal rjzbbwb) {
		this.rjzbbwb = rjzbbwb;
	}
	public BigDecimal getRjzbrmb() {
		return rjzbrmb;
	}
	public void setRjzbrmb(BigDecimal rjzbrmb) {
		this.rjzbrmb = rjzbrmb;
	}
	public BigDecimal getGqbl() {
		return gqbl;
	}
	public void setGqbl(BigDecimal gqbl) {
		this.gqbl = gqbl;
	}
	public String getYuliu1() {
		return yuliu1;
	}
	public void setYuliu1(String yuliu1) {
		this.yuliu1 = yuliu1;
	}
	public String getYuliu2() {
		return yuliu2;
	}
	public void setYuliu2(String yuliu2) {
		this.yuliu2 = yuliu2;
	}
}
