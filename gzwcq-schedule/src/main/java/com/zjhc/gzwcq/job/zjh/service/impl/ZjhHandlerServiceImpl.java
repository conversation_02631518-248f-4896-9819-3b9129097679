package com.zjhc.gzwcq.job.zjh.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xxl.job.core.context.XxlJobHelper;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectComplete;
import com.zjhc.gzwcq.job.zjh.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.job.zjh.entity.FileInfoDTO;
import com.zjhc.gzwcq.job.zjh.entity.ResultEntity;
import com.zjhc.gzwcq.job.zjh.entity.api.DTO.ProjectCompleteDTO;
import com.zjhc.gzwcq.job.zjh.entity.api.VO.ResultLoginVO;
import com.zjhc.gzwcq.job.zjh.entity.properties.ApiProperties;
import com.zjhc.gzwcq.job.zjh.mapper.IExtProjectCompleteMapper;
import com.zjhc.gzwcq.job.zjh.mapper.IExtProjectTransfereeMapper;
import com.zjhc.gzwcq.job.zjh.service.api.IZjhHandlerService;
import com.zjhc.gzwcq.job.zjh.util.DateUtils;
import com.zjhc.gzwcq.job.zjh.util.HttpUtils;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:11:13:47
 **/
@Service
@Slf4j
public class ZjhHandlerServiceImpl implements IZjhHandlerService {
    @Autowired
    private ApiProperties apiProperties;
    @Autowired
    private IExtProjectTransfereeMapper iExtProjectTransfereeMapper;
    @Autowired
    private IExtProjectCompleteMapper mapper;

    @Override
    public ResultEntity<ResultLoginVO> login() throws IOException {
        ResultEntity<ResultLoginVO> resultEntity = new ResultEntity<ResultLoginVO>();
        Map<String, String> parMap = new HashMap<>();
        String url = apiProperties.getPrefixUrl() + apiProperties.getLoginUrl();
        parMap.put("username", apiProperties.getUsername());
        parMap.put("password", apiProperties.getPassword());
        String jsonString = JSON.toJSONString(parMap);
        String login = HttpUtils.sendPost(url, jsonString, null);
        resultEntity = JSON.parseObject(login, new TypeReference<ResultEntity<ResultLoginVO>>() {
        });
        XxlJobHelper.log("访问浙交汇登录返回：\t" + login);
        if (Objects.isNull(resultEntity)) {
            throw new RuntimeException("访问浙交汇登录返回解析为空");
        }
        return resultEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ExtProjectComplete> extProjectCompleteData(ProjectCompleteDTO projectCompleteDTO) throws IOException {
        XxlJobHelper.log("开始同步项目成交数据");
        List<String> datesBetween = null;
        ProjectCompleteDTO dto = null;
        Long number = mapper.selectCount();
        //看看是否为第一次同步，全量同步
        if (Objects.isNull(number) || number == 0) {
            dto = new ProjectCompleteDTO();
            dto.setSSJG(9999L);
        }
        //看看是否为第N次 同步 为空说明是全量同步
        if (Objects.isNull(dto)) {
            dto = new ProjectCompleteDTO();
            dto.setSSJG(9999L);
            Date thisDate = new Date();
            Date date = DateUtils.addDays(thisDate, -2);
            String startTime = DateUtils.format(date, DateUtils.DATE_PATTERN_YYYYMMDD);
            String endTime = DateUtils.format(thisDate, DateUtils.DATE_PATTERN_YYYYMMDD);
            datesBetween = DateUtils.getDatesBetween(startTime, endTime, DateUtils.DATE_PATTERN_YYYYMMDD);
        }
        dto.setToken(projectCompleteDTO.getToken());
        //开始获取数据
        List<ExtProjectComplete> extProjectComplete = this.getExtProjectComplete(datesBetween, dto);
        if (Objects.isNull(extProjectComplete) || extProjectComplete.size() == 0) {
            //说明没有数据需要同步
            return null;
        }
        //开始插入数据库
        List<ExtProjectComplete> list = this.insetExtProjectCompleteData(extProjectComplete);
        XxlJobHelper.log("项目成交数据更新or插入完成");
        return list;
    }

    @Override
    public List<ExtProjectTransferee> extProjectTransfereeData(List<ExtProjectComplete> list, ProjectCompleteDTO projectCompleteDTO) throws IOException {
        XxlJobHelper.log("项目受让方数据开始同步");
        ProjectCompleteDTO dto = null;
        if (Objects.isNull(list) || list.isEmpty()) {
            //自定义异常使用
            throw new NullPointerException();
        }
        Long number = iExtProjectTransfereeMapper.selectCount();
        //是否采取全量同步 默认是false
        Boolean status = false;
        //第一次
        if (Objects.isNull(number) || number == 0) {
            dto = new ProjectCompleteDTO();
            dto.setSSJG(9999L);
/**
 *          todo    暂时取消全量导出，全部都是增量 通过项目成交的全量同步后的数据来同步，后续如果想全量把status = true;放开
 *            因为 浙江汇接口没办法只能返回国资的数据 只能通过项目成交数据中的项目编号来实现只取国资的数据 只能才用增量的方式进行全量同步
 *          status = true;
 * */
        }
        //第N次 dto为空说明是增量同步
        if (Objects.isNull(dto)) {
            dto = new ProjectCompleteDTO();
            dto.setSSJG(9999L);
        }
        dto.setToken(projectCompleteDTO.getToken());
        //查询数据
        List<ExtProjectTransferee> extProjectTransfereeData = this.getExtProjectTransfereeData(dto, list, status);
        if (Objects.isNull(extProjectTransfereeData) && extProjectTransfereeData.isEmpty()) {
            //自定义异常
            throw new NullPointerException();
        }
        //插入数据
        List<ExtProjectTransferee> extProjectTransferees = insetExtProjectTransfereeData(extProjectTransfereeData);
        return extProjectTransferees;
    }

    public List<ExtProjectTransferee> getExtProjectTransfereeData(ProjectCompleteDTO dto, List<ExtProjectComplete> list, Boolean status) throws IOException {
        List<ExtProjectTransferee> vos = new ArrayList<>();
        String url = apiProperties.getPrefixUrl() + apiProperties.getProjectTransfereeUrl();
        //请求参数 目前只有全量同步使用
        String toJSONString = JSON.toJSONString(dto);
        HashMap dtoMap = JSON.parseObject(toJSONString, new TypeReference<HashMap<String, String>>() {
        });
        //请求头
        dtoMap.put("Access-Token", dto.getToken());
        if (!status) {
            //说明不是第一次 说明是增量同步
            for (ExtProjectComplete projectComplete : list) {
                String xmbh = projectComplete.getXmbh();
                dto.setXMBH(xmbh);
                //请求参数
                toJSONString = JSON.toJSONString(dto);
                String post = HttpUtils.sendPost(url, toJSONString, dtoMap);
                ResultEntity<List<ExtProjectTransferee>> resultEntity = JSON.parseObject(post, new TypeReference<ResultEntity<List<ExtProjectTransferee>>>() {
                });
                //日志记录 定时任务框架的日志
                if (this.logResultEntity(resultEntity, post, toJSONString)) {
                    //说明返回数据有问题跳过这次 因为解析出来的 resultEntity.getData() 一定全是空字段 防止数据库存空值
                    continue;
                }
                vos.addAll(resultEntity.getData());
            }
            return vos;
        }
        //第一次
        vos = this.sendExtProjectTransfereePost(url, dto, dtoMap, 3000);
        return vos;
    }

    /**
     * @param url    请求utl
     * @param dto    请求的dto
     * @param dtoMap 请求头
     * @return
     * @throws IOException
     */
    public List<ExtProjectComplete> sendExtProjectCompletePost(String url, ProjectCompleteDTO dto, Map<String, String> dtoMap, int pageSize) throws IOException {
        List<ExtProjectComplete> listVO = new ArrayList<>();
        dto.setPAGESIZE(pageSize);
        //只取国资的数据
        dto.setSFGZ(1L);
        //分页查询停止的状态
        boolean sendStatus = true;
        for (int pageNumber = 1; sendStatus; pageNumber++) {
            dto.setPAGENO(pageNumber);
            String toJSONString = JSON.toJSONString(dto);
            String post = HttpUtils.sendPost(url, toJSONString, dtoMap);
            ResultEntity<List<ExtProjectComplete>> resultEntity = JSON.parseObject(post, new TypeReference<ResultEntity<List<ExtProjectComplete>>>() {
            });
            //日志记录 定时任务框架的日志
            if (this.logResultEntity(resultEntity, post, toJSONString)) {
                //说明返回数据有问题跳过这次 因为解析出来的 resultEntity.getData() 一定全是空字段 防止数据库存空值
                continue;
            }
            List resultEntityData = resultEntity.getData();
            listVO.addAll(resultEntityData);
            //说明数据被取完了 可以停止了
            if (resultEntityData.isEmpty() || resultEntityData.size() < pageSize) sendStatus = false;
        }
        return listVO;
    }

    /**
     * @param url    请求utl
     * @param dto    请求的dto
     * @param dtoMap 请求头
     * @return
     * @throws IOException
     */
    public List<ExtProjectTransferee> sendExtProjectTransfereePost(String url, ProjectCompleteDTO dto, Map<String, String> dtoMap, int pageSize) throws IOException {
        List<ExtProjectTransferee> listVO = new ArrayList<>();
        dto.setPAGESIZE(pageSize);
        boolean sendStatus = true;
        for (int pageNumber = 1; sendStatus; pageNumber++) {
            dto.setPAGENO(pageNumber);

            String toJSONString = JSON.toJSONString(dto);
            String post = HttpUtils.sendPost(url, toJSONString, dtoMap);
            ResultEntity<List<ExtProjectTransferee>> resultEntity = JSON.parseObject(post, new TypeReference<ResultEntity<List<ExtProjectTransferee>>>() {
            });
            //日志记录 定时任务框架的日志
            if (this.logResultEntity(resultEntity, post, toJSONString)) {
                //说明返回数据有问题跳过这次 因为解析出来的 resultEntity.getData() 一定全是空字段 防止数据库存空值
                continue;
            }
            List resultEntityData = resultEntity.getData();
            listVO.addAll(resultEntityData);
            //说明数据被取完了
            if (resultEntityData.isEmpty() || resultEntityData.size() < pageSize) sendStatus = false;
        }
        return listVO;
    }

    public List<ExtProjectComplete> getExtProjectComplete(List<String> datesBetween, ProjectCompleteDTO dto) throws IOException {
        //获取请求的url
        String url = apiProperties.getPrefixUrl() + apiProperties.getProjectUrl();
        List<ExtProjectComplete> listVO = new ArrayList<>();
        //状态 判断是否为增量同步
        Boolean whileStatus = false;
        //请求参数 用于全量同步
        String toJSONString = JSON.toJSONString(dto);
        HashMap dtoMap = JSON.parseObject(toJSONString, new TypeReference<HashMap<String, String>>() {
        });
        //请求头
        dtoMap.put("Access-Token", dto.getToken());
        if (Objects.nonNull(datesBetween) && datesBetween.size() > 0) {
            whileStatus = true;
        }
        if (whileStatus) {
            //说明不是第一次同步
            for (String date : datesBetween) {
                dto.setCJRQ(Long.valueOf(date));
                //请求参数 用于增量
                toJSONString = JSON.toJSONString(dto);
                String post = HttpUtils.sendPost(url, toJSONString, dtoMap);
                ResultEntity<List<ExtProjectComplete>> resultEntity = JSON.parseObject(post, new TypeReference<ResultEntity<List<ExtProjectComplete>>>() {
                });
                //日志记录 定时任务框架的日志
                if (this.logResultEntity(resultEntity, post, toJSONString)) {
                    //说明返回数据有问题跳过这次 因为解析出来的 resultEntity.getData() 一定全是空字段 防止数据库存空值
                    continue;
                }
                listVO.addAll(resultEntity.getData());
            }
            return listVO;
        }
        //第一次同步 全量同步
        listVO = this.sendExtProjectCompletePost(url, dto, dtoMap, 3000);
        return listVO;
    }

    /**
     * 定时任务框架日志
     *
     * @param resultEntity 请求返回
     * @param msg          响应的json
     * @param request      请求参数
     */
    public boolean logResultEntity(ResultEntity resultEntity, String msg, String request) {
        if (Objects.isNull(resultEntity)) {
            XxlJobHelper.log("浙交汇返回格式变化: \t" + msg);
            return true;
        }
        String code = resultEntity.getCode();
        //当状态码不对或者code为空时候 说明返回格式变化或者第三方异常
        if (!StringUtils.equals(ApiProperties.OK_STATUS, code) || StringUtils.isBlank(code)) {
            XxlJobHelper.log("浙交汇返回格式变化或者接口报错 调用返回: \t" + msg);
            XxlJobHelper.log("我方系统的请求参数：\t" + request);
            return true;
        }
        return false;
    }

    public List<ExtProjectTransferee> insetExtProjectTransfereeData(List<ExtProjectTransferee> List) {
        List<ExtProjectTransferee> vos = new ArrayList<>();
        //数据处理
        this.ExtProjectCompleteData(List);
        while (!List.isEmpty()) {
            List<ExtProjectTransferee> subList = this.subList(List, 3000);
            //开始插入数据
            iExtProjectTransfereeMapper.insertOrUpdate(subList);
            //获取插入的数据
            vos.addAll(subList);
        }
        return vos;
    }

    /**
     * 数据处理将一个字段的数据分为二个字段的数据
     *
     * @param vos
     */
    public void ExtProjectCompleteData(List<ExtProjectTransferee> vos) {
        vos.stream().forEach(item -> {
            String yxffj = item.getYxffj();
            //说明没数据不需要执行
            if (StringUtils.isBlank(yxffj)) {
                return;
            }
            List<FileInfoDTO> dto = JSON.parseObject(yxffj, new TypeReference<List<FileInfoDTO>>() {
            });
            int size = dto.size();
            //说明没有数据不需要做数据处理
            if (size <= 0) {
                item.setYxffj("");
                item.setFatt("");
                return;
            } else if (size > 1) {
                //说明有多个数据全要处理
                String fName = "";
                List<String> fAtt = new ArrayList<>();
                for (FileInfoDTO fileInfoDTO : dto) {
                    if (StringUtils.isBlank(fName)) {
                        //第一次不需要加，
                        fName = fileInfoDTO.getFName();
                    } else {
                        fName += "," + fileInfoDTO.getFName();
                    }
                    fAtt.addAll(fileInfoDTO.getFAtt());
                }
                //处理url
                String fAttString = "";
                for (String url : fAtt) {
                    if (StringUtils.isBlank(fAttString)) {
                        //第一次不需要加，
                        fAttString = url;
                    } else {
                        fAttString += "," + url;
                    }
                }
                item.setYxffj(fName);
                item.setFatt(fAttString);
            } else {
                //说明就一条数据需要处理
                String fName = "";
                String fAttString = "";
                for (FileInfoDTO fileInfoDTO : dto) {
                    fName = fileInfoDTO.getFName();
                    fAttString = fileInfoDTO.getFAtt().get(0);
                }
                item.setYxffj(fName);
                item.setFatt(fAttString);
            }
        });
    }

    public List<ExtProjectComplete> insetExtProjectCompleteData(List<ExtProjectComplete> List) {
        List<ExtProjectComplete> vos = new ArrayList<>();
        while (!List.isEmpty()) {
            List<ExtProjectComplete> subList = this.subList(List, 3000);
            //开始插入数据
            mapper.insertOrUpdate(subList);
            //获取插入的数据
            vos.addAll(subList);
        }
        return vos;
    }

    public List subList(List list, int batchSize) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("List is null or empty");
        }
        if (batchSize <= 0) {
            throw new IllegalArgumentException("Batch size must be positive");
        }
        List subList = new ArrayList(batchSize);
        if (list.size() > batchSize) {
            synchronized (list) {
                subList = list.subList(0, batchSize);
                //数据回填 不能直接clear 会出现err
                list = list.subList(batchSize, list.size());
            }
        } else {
            subList.addAll(list);
            list.clear();
        }
        return subList;
    }

}
