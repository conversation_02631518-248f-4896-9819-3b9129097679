package com.zjhc.gzwcq.job.zjh.entity;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/03:09:54:29
 **/
@XmlRootElement
@XmlType(propOrder = {"qyid",
        "myczrxm",
        "myczrdq",
        "myczrbm",
        "sjczr",
        "slyy",
        "dcgqbl",
        "bqcs"})
public class Dcgqk {
    private String qyid;
    private String myczrxm;
    private String myczrdq;
    private String myczrbm;
    private String sjczr;
    private String slyy;
    private BigDecimal dcgqbl;
    private String bqcs;

    public String getQyid() {
        return qyid;
    }

    public void setQyid(String qyid) {
        this.qyid = qyid;
    }

    public String getMyczrxm() {
        return myczrxm;
    }

    public void setMyczrxm(String myczrxm) {
        this.myczrxm = myczrxm;
    }

    public String getMyczrdq() {
        return myczrdq;
    }

    public void setMyczrdq(String myczrdq) {
        this.myczrdq = myczrdq;
    }

    public String getMyczrbm() {
        return myczrbm;
    }

    public void setMyczrbm(String myczrbm) {
        this.myczrbm = myczrbm;
    }

    public String getSjczr() {
        return sjczr;
    }

    public void setSjczr(String sjczr) {
        this.sjczr = sjczr;
    }

    public String getSlyy() {
        return slyy;
    }

    public void setSlyy(String slyy) {
        this.slyy = slyy;
    }

    public BigDecimal getDcgqbl() {
        return dcgqbl;
    }

    public void setDcgqbl(BigDecimal dcgqbl) {
        this.dcgqbl = dcgqbl;
    }

    public String getBqcs() {
        return bqcs;
    }

    public void setBqcs(String bqcs) {
        this.bqcs = bqcs;
    }
}
