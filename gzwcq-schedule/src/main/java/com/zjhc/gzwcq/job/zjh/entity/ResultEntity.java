package com.zjhc.gzwcq.job.zjh.entity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23:10:26:13
 **/
public class ResultEntity<T> {
    private String code;
    private T data;
    private Object etxra;
    private String msg;

    @Override
    public String toString() {
        return "ResultEntity{" +
                "code='" + code + '\'' +
                ", data=" + data +
                ", etxra=" + etxra +
                ", msg='" + msg + '\'' +
                '}';
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Object getEtxra() {
        return etxra;
    }

    public void setEtxra(Object etxra) {
        this.etxra = etxra;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
