{"hints": [], "groups": [{"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration.DynamicDataSourceProperties", "name": "com.dynamicdatasource.datasource.master", "sourceMethod": "dbMaster()", "type": "com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration.DynamicDataSourceProperties", "name": "com.dynamicdatasource.datasource.slave", "sourceMethod": "db<PERSON><PERSON><PERSON>()", "type": "com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration.DynamicDataSourceProperties", "name": "com.dynamicdatasource.datasource", "type": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration.DynamicDataSourceProperties"}], "properties": [{"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration.DynamicDataSourceProperties", "name": "com.dynamicdatasource.datasource.mapper-locations", "type": "java.lang.String", "defaultValue": "classpath*:com/**/mapper/**/*Mapper.xml"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.enable", "type": "java.lang.Bo<PERSON>an", "defaultValue": false}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.url", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.username", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.password", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.filters", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.initial-size", "type": "java.lang.Integer"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.max-active", "type": "java.lang.Integer"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.min-idle", "type": "java.lang.Integer"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.max-wait", "type": "java.lang.Long"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.time-between-eviction-runs-millis", "type": "java.lang.Long"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.min-evictable-idle-time-millis", "type": "java.lang.Long"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.test-on-borrow", "type": "java.lang.Bo<PERSON>an", "defaultValue": false}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.test-on-return", "type": "java.lang.Bo<PERSON>an", "defaultValue": false}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.test-while-idle", "type": "java.lang.Bo<PERSON>an", "defaultValue": true}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.validation-query", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.pool-prepared-statements", "type": "java.lang.Bo<PERSON>an"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.master.max-open-prepared-statements", "type": "java.lang.Integer"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.url", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.username", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.password", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.filters", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.initial-size", "type": "java.lang.Integer"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.max-active", "type": "java.lang.Integer"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.min-idle", "type": "java.lang.Integer"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.max-wait", "type": "java.lang.Long"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.time-between-eviction-runs-millis", "type": "java.lang.Long"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.min-evictable-idle-time-millis", "type": "java.lang.Long"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.test-on-borrow", "type": "java.lang.Bo<PERSON>an", "defaultValue": false}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.test-on-return", "type": "java.lang.Bo<PERSON>an", "defaultValue": false}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.test-while-idle", "type": "java.lang.Bo<PERSON>an", "defaultValue": true}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.validation-query", "type": "java.lang.String"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.pool-prepared-statements", "type": "java.lang.Bo<PERSON>an"}, {"sourceType": "com.auto.dynamicDataSource.config.DynamicDataSourceConfiguration", "name": "com.dynamicdatasource.datasource.slave.max-open-prepared-statements", "type": "java.lang.Integer"}]}