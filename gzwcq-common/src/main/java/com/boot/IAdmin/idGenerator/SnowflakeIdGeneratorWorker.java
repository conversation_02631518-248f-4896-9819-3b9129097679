package com.boot.IAdmin.idGenerator;

import org.springframework.context.annotation.Bean;

/**
 * 雪花ID生成器帮助类
 * <AUTHOR>
 * */
public class SnowflakeIdGeneratorWorker {
	
	private static SnowflakeIdGenerator snowflakeIdGenerator;
	
	private void setSnowflakeIdGenerator(SnowflakeIdGenerator snowflakeIdGenerator) {
		SnowflakeIdGeneratorWorker.snowflakeIdGenerator = snowflakeIdGenerator;
	}

	public static long generate() {
		return snowflakeIdGenerator.generate();
	}
	
	public static String generateStr() {
		return snowflakeIdGenerator.generateStr();
	}
	
	@Bean
	public SnowflakeIdGenerator createSnowflakeIdGenerator() {
		SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator();
		this.setSnowflakeIdGenerator(snowflakeIdGenerator);
		return snowflakeIdGenerator;
	}
}
