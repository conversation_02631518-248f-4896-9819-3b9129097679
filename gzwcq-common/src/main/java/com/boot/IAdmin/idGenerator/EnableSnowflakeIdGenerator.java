package com.boot.IAdmin.idGenerator;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

/**
 * 注解引入雪花算法
 * <AUTHOR>
 * */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(SnowflakeIdGeneratorWorker.class)
public @interface EnableSnowflakeIdGenerator {
	
}