package com.boot.IAdmin.dict.model;

import java.io.Serializable;

/**
 * <AUTHOR> <br/>
 *         表名： s_dictionary <br/>
 *         描述：字典表 <br/>
 */
public class Dictionary implements Serializable {
	/**
	 * 序列化
	 */
	private static final long serialVersionUID = 1L;
	protected Long id;// 流水
	protected String type;// 类型
	protected String type_code;//类型编码--因为现在类型允许输入中文，类型编码使用英文，呼应
	protected String val;// 值
	protected String text;// 文本
	protected String seq;// 序号
	protected String active;// 是否可用
	protected String description;// 描述
	protected Long parent;// 父节点
	protected Long type_id;// 类型Id
	protected Integer level;//级次
	protected String disabled;//是否可用

	public Dictionary(String type) {
		super();
		this.type = type;
	}

	public Dictionary() {
		super();
	}
	
	public Dictionary(long id) {
		super();
		this.id = id;
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	
	public String getType_code() {
		return type_code;
	}

	public void setType_code(String type_code) {
		this.type_code = type_code;
	}

	public String getVal() {
		return val;
	}
	public void setVal(String val) {
		this.val = val;
	}
	
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}

	public String getSeq() {
		return seq;
	}

	public void setSeq(String seq) {
		this.seq = seq;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public String getActive() {
		return active;
	}
	public void setActive(String active) {
		this.active = active;
	}
	
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	public Long getParent() {
		return parent;
	}

	public void setParent(Long parent) {
		this.parent = parent;
	}

	public Long getType_id() {
		return type_id;
	}

	public void setType_id(Long type_id) {
		this.type_id = type_id;
	}

	public String getDisabled() {
		return disabled;
	}

	public void setDisabled(String disabled) {
		this.disabled = disabled;
	}
}
