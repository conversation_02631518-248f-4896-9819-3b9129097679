package com.boot.IAdmin.dict.service.impl;

import java.util.*;

import org.apache.commons.codec.binary.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ztree.ZTreeNode;
import com.boot.IAdmin.dict.mapper.DictionaryMapper;
import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.IAdmin.dict.service.api.IDictionaryService;

@Service
public class DictionaryServiceImpl implements IDictionaryService {
	
	@Autowired
	public DictionaryMapper iDictionaryMapper;

	public void save(Dictionary dictionary){
		iDictionaryMapper.save(dictionary);
	}

	public void delete(Map<String, Object> map){
		iDictionaryMapper.delete(map);
	}
	
	public void deleteByExample(Dictionary dictionary){
		iDictionaryMapper.deleteByExample(dictionary);
	}

	public void update(Dictionary dictionary){
		iDictionaryMapper.update(dictionary);
	}
	
	public void updateIgnoreNull(Dictionary dictionary){
		iDictionaryMapper.updateIgnoreNull(dictionary);
	}
		
	public void updateByExample(Dictionary dictionary){
		iDictionaryMapper.update(dictionary);
	}

	public DictionaryVo load(Dictionary dictionary){
		return (DictionaryVo)iDictionaryMapper.reload(dictionary);
	}
	
	public List<DictionaryVo> selectForList(Dictionary dictionary){
		return (List<DictionaryVo>)iDictionaryMapper.selectForList(dictionary);
	}

	public BootstrapTableModel<Dictionary> queryDictionaryByPage(BootstrapTableModel<Dictionary> bootModel) {
		return bootModel.addRows(iDictionaryMapper.queryDictionaryByPage(bootModel)).addTotals(iDictionaryMapper.queryTotalDictionarys(bootModel));
	}

	public DictionaryVo queryDictionaryById(Dictionary dictionary) {
		return iDictionaryMapper.queryDictionaryById(dictionary);
	}
	
	/**
	 * 根据字典值和类型查询字典文本
	 * 
	 * @param type 类型
	 * @param val 值
	 * */
	@Override
	@Deprecated
	public String getTextByValAndType(String type, String val) {
		List<DictionaryVo> list = this.selectForList(new Dictionary(type));
		for(DictionaryVo dic : list){
			if(dic.getVal().equals(val)) return dic.getText();
		}
		return null;
	}

	@Override
	@Deprecated
	public Dictionary queryByValAndType(String type, String val) {
		List<DictionaryVo> list = this.selectForList(new Dictionary(type));
		for(Dictionary dic : list){
			if(dic.getVal().equals(val))	return dic;
		}
		return null;
	}

	@Override
	public String queryByTest(String text) {
		return iDictionaryMapper.selectByTest(text);
	}

	@Override
	public Collection<ZTreeNode> loadByParentId(Dictionary dictionary) {
		Collection<ZTreeNode> nodes = iDictionaryMapper.loadByParentId(dictionary);
		return nodes;
	}

	@Override
	public boolean validateUniqueParam(Dictionary dictionary) {
		return iDictionaryMapper.selectForUnique(dictionary).size() == 0;
	}

	@Override
	public List<Dictionary> getByTypeCommon(String type) {
		return iDictionaryMapper.getByTypeCommon(type);
	}
	
	@Override
	public List<Dictionary> getByTypeCodeCommon(String type_code) {
		return iDictionaryMapper.getByTypeCodeCommon(type_code);
	}

	@Override
	public List<DictionaryVo> getByTypeCodeCommonVo(String type_code) {
		return getTree(iDictionaryMapper.getByTypeCodeCommonVo(type_code));
	}

	List<DictionaryVo> getTree(List<DictionaryVo> dictionaries){
		for (DictionaryVo dictionary : dictionaries) {
			DictionaryVo vo = new DictionaryVo();
			vo.setParent(dictionary.getId());
			List<DictionaryVo> byTypeCodeCommon = iDictionaryMapper.selectForList(vo);
			if(byTypeCodeCommon.size()>0){
				getTree(byTypeCodeCommon);
			}
			dictionary.setDictionaryList(byTypeCodeCommon);
		}
		return dictionaries;
	}

	@Override
	public List<DictionaryVo> getByParent(Long parent) {
		return iDictionaryMapper.getByParent(parent);
	}

	@Override
	public List<Dictionary> getByGrandTypeCodeCommon(String type_code) {
		return iDictionaryMapper.getByGrandTypeCodeCommon(type_code);
	}
	
	/**
	 * 根据val获取值
	 * */
	@Override
	public String findTextByVal(String val, List<Dictionary> list) {
		for(Dictionary dic : list){
			if(StringUtils.equals(val, dic.getVal())) return dic.getText();
		}
		return null;
	}

	@Override
	public Dictionary getByTypeCodeCommon(String dic_type, String dic_val) {
		return iDictionaryMapper.getOneByTypeCodeCommon(dic_type, dic_val);
	}
	
	/**
	 * 根据parentId和字典类型，获取当前类型下父节点下的子节点列表
	 * */
	@Override
	public List<Dictionary> getByParent(String type_code, Long parent) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("type_code", type_code);
		map.put("parentId", parent);
		return iDictionaryMapper.getByTypeAndParent(map);
	}

	@Override
	public void updateVal(String val) {
		iDictionaryMapper.updateVal(val);
	}

	@Override
	public List<DictionaryVo> selectForCheckListVals(String[] split) {
		return iDictionaryMapper.selectForCheckListVals(split);
	}

	@Override
	public List<DictionaryVo> getDicTree(String type) {
		Dictionary dictionary = new Dictionary();
		dictionary.setType_code(type);
		List<DictionaryVo> dictionaryList = iDictionaryMapper.selectForList(dictionary);
		if(dictionaryList.size()!=1){
			return null;
		}
		Long parentId = dictionaryList.get(0).getId();

		return getTree(parentId);
	}

	@Override
	public List<DictionaryVo> getTreeByTypeCode(String type_code) {
		final Dictionary dictionary = iDictionaryMapper.loadOneByTypeCode(type_code);
		if (dictionary == null){
			return null;
		}
		return iDictionaryMapper.getTreeByParent(dictionary.getId());
	}

	private List<DictionaryVo> getTree(Long parentId) {
		List<DictionaryVo> byParent = iDictionaryMapper.getByParent(parentId);
		for (DictionaryVo dictionary : byParent) {
			List<DictionaryVo> tree = getTree(dictionary.getId());
			if(tree.size()>0){
				dictionary.setDictionaryList(tree);
			}
		}
		return byParent;
	}

	@Override
	public List<DictionaryVo> getRootNode(Dictionary dictionary) {
		return iDictionaryMapper.getRootNode(dictionary);
	}
}
