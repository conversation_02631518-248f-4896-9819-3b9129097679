<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.IAdmin.dict.mapper.DictionaryMapper">

	<resultMap type="com.boot.IAdmin.dict.model.Dictionary" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="type" property="type"/>
		<result column="type_code" property="type_code"/>
		<result column="val" property="val"/>
		<result column="text" property="text"/>
		<result column="seq" property="seq"/>
		<result column="active" property="active"/>
		<result column="description" property="description"/>
		<result column="parent" property="parent"/>
		<result column="type_id" property="type_id"/>
		<result column="level" property="level"/>
		<result column="disabled" property="disabled"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.boot.IAdmin.dict.model.DictionaryVo" extends="baseResultMap">
	</resultMap>


	<sql id="columns">
		 id,type,type_code, val, text, seq, active, description,parent,type_id,level,disabled
	</sql>
	<sql id="vals">
		#{id}, #{type},#{type_code}, #{val}, #{text}, #{seq}, #{active}, #{description},#{parent},#{type_id},#{level},#{disabled}
	</sql>
	<sql id="conds">
		<if test="id != null and id != ''">
			and id = #{id}
		</if>
		<if test="type != null and type != ''">
			and type = #{type}
		</if>
		<if test="type_code != null and type_code != ''">
			and type_code = #{type_code}
		</if>
		<if test="val != null and val != ''">
			and val = #{val}
		</if>
		<if test="text != null and text != ''">
			and text = #{text}
		</if>
		<if test="seq != null and seq != ''">
			and seq = #{seq}
		</if>
		<if test="active != null and active != ''">
			and active = #{active}
		</if>
		<if test="description != null and description != ''">
			and description = #{description}
		</if>
		<if test="parent != null">
			and parent = #{parent}
		</if>
		<if test="type_id != null">
			and type_id = #{type_id}
		</if>
		<if test="level != null">
			and level = #{level}
		</if>
	</sql>
	<sql id="columns_no_active">
		 id,type, val, text, seq, description,parent,level,disabled
	</sql>
	<sql id="queryPage">
	    <if test="obj.type != null and obj.type != ''">
			and type like CONCAT('%',#{obj.type},'%')
		</if>
		<if test="obj.val != null and obj.val != ''">
			and val like  CONCAT('%',#{obj.val},'%')
		</if>
		<if test="obj.text != null and obj.text != ''">
			and text like CONCAT('%',#{obj.text},'%')	
		</if>
		<if test="obj.parent != null">
			and parent = #{obj.parent}
		</if>
		<if test="obj.type_id != null">
			and type_id = #{obj.type_id}
		</if>
		<if test="obj.level != null">
			and level = #{obj.level}
		</if>
	</sql>
	<!-- 自增长ID-->
	<insert id="save" parameterType="com.boot.IAdmin.dict.model.Dictionary" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" resultType="Long" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into sys_dictionary (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>  
	
	<delete id="delete" parameterType="java.util.Map">
		<!-- delete from sys_dictionary where 
		id in
		<foreach collection="dictionarys" open="(" close=")" separator="," item="id">
		    #{id} 
		</foreach> -->
		DELETE
			FROM
				sys_dictionary
			WHERE
				id IN (
					SELECT
						*
					FROM
						(
							SELECT
								id
							FROM
								sys_dictionary
							WHERE
							(type_id = #{typeId} or id = #{typeId})
							and
								FIND_IN_SET(
									id,
									getDicChildList (#{id})
								)
						) AS TEMP
				)
	</delete>

	<delete id="deleteByExample">
		delete from sys_dictionary
		where 1=1
		<include refid="conds" />
	</delete>
	
	<select id="reload" resultType="com.boot.IAdmin.dict.model.DictionaryVo">
		select 
		<include refid="columns"/>
		from sys_dictionary
		where id=#{id} 
	</select>


	<select id="selectForCheckListVals"  resultMap="baseResultMap">
		select * from sys_dictionary where type_code = 'ATTACHMENT_LIST' and val in
		<foreach collection="vals" open="(" close=")" separator="," item="val">
			#{val}
		</foreach>
	</select>
	
	<select id="select" resultType="com.boot.IAdmin.dict.model.DictionaryVo">
		select 
		<include refid="columns"/>
		from sys_dictionary
		where 1=1
		<include refid="conds" />
	</select>
	
	<!-- 尽量用上seq字段，界面下拉排序使用 -->
	<select id="selectForList" parameterType="com.boot.IAdmin.dict.model.Dictionary" resultMap="baseResultMapExt">
		select 
			<include refid="columns"/>,
		case when disabled = 'true' then 1 else 0 end as disabledOrder
			from sys_dictionary
			where 1=1
			<include refid="conds" />
			order by disabledOrder,seq+0 asc
	</select>

	<update id="update">
		update sys_dictionary
		<set>
			id=#{id}, 
			type=#{type}, 
			val=#{val}, 
			text=#{text}, 
			seq=#{seq}, 
			active=#{active}, 
			description=#{description},
			parent = #{parent},
			type_id = #{type_id},
			level = #{level},
			disabled = #{disabled}
		</set>
		where id=#{id} 
	</update>

	<update id="updateIgnoreNull">
		update sys_dictionary
		<set>
		<if test="id != null">
			id=#{id}, 
		</if>
		<if test="type != null">
			type=#{type}, 
		</if>
		<if test="type_code != null">
			type_code=#{type_code}, 
		</if>
		<if test="val != null">
			val=#{val}, 
		</if>
		<if test="text != null">
			text=#{text}, 
		</if>
		<if test="seq != null and seq != ''">
			seq=#{seq}, 
		</if>
		<if test="active != null">
			active=#{active}, 
		</if>
		<if test="description != null">
			description=#{description},
		</if>
		<if test="parent != null">
			parent = #{parent},
		</if>
		<if test="type_id != null">
			type_id = #{type_id},
		</if>
		<if test="level != null">
			level = #{level},
		</if>
		<if test="disabled != null">
			disabled = #{disabled}
		</if>
		</set>
		where id=#{id} 
	</update>

	<update id="updateByExample">
		update sys_dictionary
		<set>
			<if test="id != null and id != ''">
			id=#{id}, 
			</if>
			<if test="type != null and type != ''">
			type=#{type}, 
			</if>
			<if test="type_code != null and type_code != ''">
			type_code=#{type_code}, 
			</if>
			<if test="val != null and val != ''">
			val=#{val}, 
			</if>
			<if test="text != null and text != ''">
			text=#{text}, 
			</if>
			<if test="seq != null and seq != ''">
			seq=#{seq}, 
			</if>
			<if test="active != null and active != ''">
			active=#{active}, 
			</if>
			<if test="description != null and description != ''">
			description=#{description},
			</if>
			<if test="parent != null">
			parent = #{parent},
			</if>
			<if test="type_id != null">
			type_id = #{type_id},
			</if>
			<if test="level != null">
				level = #{level},
			</if>
			<if test="disabled != null">
				disabled = #{disabled}
			</if>
		</set>
		where 1=1
		<include refid="conds" />
	</update>
	<select id="queryDictionaryByPage" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultMap="baseResultMapExt">
		select
		<include refid="columns_no_active"/>
		,case when active = 1 then '可用' else '不可用' end as active
		from
			sys_dictionary
		where 1=1
		<include refid="queryPage" />
		order by id desc limit #{offset},#{limit} 
	</select>
	
	<select id="queryTotalDictionarys" parameterType="com.boot.IAdmin.common.domain.BootstrapTableModel" resultType="java.lang.Long">
		select
		count(1)
		from sys_dictionary
		where 1=1
		<include refid="queryPage" />
	</select>
	<select id="queryDictionaryById" resultMap="baseResultMapExt">
	    select 
		<include refid="columns"/>
		from sys_dictionary
		where id=#{id} 	    
	</select>
	
	<select id="loadByParentId" parameterType="com.boot.IAdmin.dict.model.Dictionary" resultType="com.boot.IAdmin.dict.model.DictionaryNode">
    	select a.id,
		   CASE parent 
		   	WHEN '-1' THEN type
		   	ELSE text
		   	END AS name,
		   (select count(1) from sys_dictionary t where t.parent = a.id) childNum
		from sys_dictionary a
		where a.parent = #{id}
		order by seq+0
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.boot.IAdmin.dict.model.Dictionary" resultType="com.boot.IAdmin.dict.model.Dictionary">
		select <include refid="columns"/> from sys_dictionary t
			where t.id != #{id}
			<if test="type != null and type != ''">
				and t.type = #{type}
			</if>
			<if test="type_code != null and type_code != ''">
				and t.type_code = #{type_code}
			</if>
			<if test="text != null and text != ''">
				and t.text = #{text}
			</if>
			<if test="val != null and val != ''">
				and t.val = #{val}
			</if>
			<if test="type_id != null and type_id != ''">
				and t.type_id = #{type_id}
			</if>
			<if test="level != null">
				and t.level = #{level}
			</if>
	</select>

	<select id="selectByTest" resultType="java.lang.String">
		select val from sys_dictionary where text = #{text}
	</select>
	
	<select id="getByTypeCommon" resultMap="baseResultMapExt">
		select 
			<include refid="columns"/>
			from sys_dictionary
			where parent = (select id from sys_dictionary where type = #{type} and parent = -1 limit 1)
			order by seq+0 asc
	</select>
	
	<select id="getByTypeCodeCommon" resultMap="baseResultMapExt">
		select 
			<include refid="columns"/>
			from sys_dictionary
			where parent = (select id from sys_dictionary where type_code = #{type_code} and parent = -1 limit 1)
			order by seq+0 asc
	</select>

	<select id="getByTypeCodeCommonVo" resultMap="baseResultMapExt">
		select
			<include refid="columns"/>
			from sys_dictionary
			where parent = (select id from sys_dictionary where type_code = #{type_code} and parent = -1 limit 1)
			order by seq+0 asc
	</select>
	<select id="getByGrandTypeCodeCommon" resultMap="baseResultMapExt">
		select
			<include refid="columns"/>
			from sys_dictionary
			where parent in(
			select id from sys_dictionary where parent=(select id from sys_dictionary where type_code = #{type_code} and parent = -1 limit 1 ))
	</select>
	
	<select id="getByParent" resultMap="baseResultMapExt">
		select 
			<include refid="columns"/>
			from sys_dictionary
			where parent = #{parent}
			order by seq+0 asc
	</select>
	
	<select id="getOneByTypeCodeCommon" resultMap="baseResultMapExt">
		select 
			<include refid="columns"/>
			from sys_dictionary
			where parent = (select id from sys_dictionary where type_code = #{type} and parent = -1 limit 1)
			and val = #{val}
			limit 1
	</select>
	
	<select id="getByTypeAndParent" resultMap="baseResultMapExt" parameterType="java.util.Map">
		select 
			<include refid="columns"/>
			from sys_dictionary
			where parent = #{parent} and type_id = (select id from sys_dictionary where type_code = #{type_code} and parent = -1 limit 1)
			order by seq+0 asc
	</select>

	<update id="updateVal">
		update sys_dictionary set val = #{val} WHERE id = 875
	</update>

	<select id="loadOneByTypeCode" parameterType="java.lang.String" resultMap="baseResultMap">
		select <include refid="columns"/>
		from sys_dictionary
		where type_code = #{type_code}
	</select>
	<!--递归查询某节点下所有子节点-->
	<resultMap type="com.boot.IAdmin.dict.model.DictionaryVo" id="DictionariesWithChildren" extends="baseResultMap">
		<collection property="dictionaryList" ofType="com.boot.IAdmin.dict.model.DictionaryVo"
					select="com.boot.IAdmin.dict.mapper.DictionaryMapper.getTreeByParent" column="id">
		</collection>
	</resultMap>
	<select id="getTreeByParent" parameterType="java.lang.Long" resultMap="DictionariesWithChildren">
		select <include refid="columns"/>,
		case when disabled = 'true' then 1 else 0 end as disabledOrder
		from sys_dictionary
		where parent = #{id}
		order by disabledOrder,seq+0
	</select>

	<select id="getRootNode" parameterType="com.boot.IAdmin.dict.model.Dictionary" resultMap="baseResultMapExt">
		SELECT
			<include refid="columns"/>
		FROM
			`sys_dictionary`
		<where>
			parent = - 1
			AND type IS NOT NULL
			AND type_code IS NOT NULL
			and type_id is null
			<include refid="conds"/>
		</where>
	</select>

	<select id="getAllLevelByTypeCode" parameterType="java.lang.String" resultMap="baseResultMapExt">
		select
			<include refid="columns"/>
		from
			sys_dictionary
		where type_id = (select id from `sys_dictionary` where type_code = #{typeCode})
		order by seq
	</select>
</mapper>