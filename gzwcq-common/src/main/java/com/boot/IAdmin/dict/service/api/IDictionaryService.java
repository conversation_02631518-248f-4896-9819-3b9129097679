package com.boot.IAdmin.dict.service.api;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ztree.ZTreeNode;
import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.model.DictionaryVo;

public interface IDictionaryService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	public abstract void save(Dictionary dictionary);

	/**
	 * 按对象中的主键进行删除，如果是DRDS，还需要添加拆分键
	 */
	public abstract void delete(Map<String, Object> map);
	
	/**
	 * 按对象中的非空属性作为条件，进行删除
	 */
	public abstract void deleteByExample(Dictionary dictionary);

	/**
	 * 按对象中的主键进行所有属性的修改，如果是DRDS，还需要添加拆分键
	 */
	public abstract void update(Dictionary dictionary);
	
	/**
	 * 按对象中的主键进行所有非空属性的修改，如果是DRDS，还需要添加拆分键
	 */
	public abstract void updateIgnoreNull(Dictionary dictionary);
	
	/**
	 * 按对象中的非空属性作为条件，进行修改
	 */
	public abstract void updateByExample(Dictionary dictionary);

	/**
	 * 按对象中的主键进行数据加载，如果是DRDS，还需要添加拆分键
	 */
	public abstract DictionaryVo load(Dictionary dictionary);
	
	/**
	 * 按对象中的非空属性作为条件，进行查询
	 */
	public abstract List<DictionaryVo> selectForList(Dictionary dictionary);
	/**
	 * 分页查询数据字典数据信息
	 * @Title: queryDictionaryByPage
	 * <AUTHOR>
	 * @param bootModel
	 * @return
	 */
	public abstract BootstrapTableModel<Dictionary> queryDictionaryByPage(BootstrapTableModel<Dictionary> bootModel);
	/**
	 * 根据字段ID查询数据字典信息
	 * @Title: queryDictionaryById
	 * <AUTHOR>
	 * @param dictionary
	 * @return 
	 */
	public abstract DictionaryVo queryDictionaryById(Dictionary dictionary);
	
	/**
	 * 根据字典值和类型查询字典文本
	 * 
	 * @param type 类型
	 * @param val 值
	 * */
	public abstract String getTextByValAndType(String type, String val);
	
	/**
	 * 根据字典值和类型查询字典对象
	 * @param type 类型
	 * @param val 值
	 * */
	public abstract Dictionary queryByValAndType(String type, String val);
	/**
	 * 根据字典文本查询字典对象
	 * @param text 类型
	 * @return*/
	public abstract String queryByTest(String text);

	public abstract Collection<ZTreeNode> loadByParentId(Dictionary dictionary);
	
	/**
	 * 验证参数唯一性
	 * */
	public abstract boolean validateUniqueParam(Dictionary dictionary);
	
	/**
	 * 根据字典域类型获取字典通用方法
	 * <P>只获取一级
	 * */
	public abstract List<Dictionary> getByTypeCommon(String type);
	
	/**
	 * 根据字典域类型编码获取字典通用方法
	 * <P>只获取一级
	 * */
	public abstract List<Dictionary> getByTypeCodeCommon(String type_code);
	public abstract List<DictionaryVo> getByTypeCodeCommonVo(String type_code);

	/**
	 * 根据字典父ID获取子列表
	 * */
	public abstract List<DictionaryVo> getByParent(Long parent);


	/**
	 * 查找孙子类
	 * @param type_code
	 * @return
	 */
	List<Dictionary> getByGrandTypeCodeCommon(String type_code);

	public abstract String findTextByVal(String val, List<Dictionary> byTypeCodeCommon);
	
	/**
	 * 根据字典类型和字典值获取唯一字典对象
	 * */
	public abstract Dictionary getByTypeCodeCommon(String dic_type, String dic_val);
	
	/**
	 * 根据字典父ID和类型获取子列表
	 * */
	public abstract List<Dictionary> getByParent(String type_code, Long parent);

	/**
	 * 脱敏配置
	 * @param val
	 */
	void updateVal(String val);


	List<DictionaryVo> selectForCheckListVals(String[] split);

	List<DictionaryVo> getDicTree(String type);

	List<DictionaryVo> getTreeByTypeCode(String type_code);

	List<DictionaryVo> getRootNode(Dictionary dictionary);
}