package com.boot.IAdmin.dict.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ztree.ZTreeNode;
import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.model.DictionaryVo;

@Repository
public interface DictionaryMapper  {

	void save(Dictionary dictionary);

	void delete(Map<String, Object> map);

	void deleteByExample(Dictionary dictionary);

	void update(String string, Dictionary dictionary);

	void updateIgnoreNull(Dictionary dictionary);

	void update(Dictionary dictionary);

	DictionaryVo reload(Dictionary dictionary);

	List<DictionaryVo> selectForList(Dictionary dictionary);

	Collection<Dictionary> queryDictionaryByPage(BootstrapTableModel<Dictionary> bootModel);

	long queryTotalDictionarys(BootstrapTableModel<Dictionary> bootModel);

	DictionaryVo queryDictionaryById(Dictionary dictionary);

	Collection<ZTreeNode> loadByParentId(Dictionary dictionary);

	List<Dictionary> selectForUnique(Dictionary dictionary);

	String selectByTest( String text);

	List<Dictionary> getByTypeCommon(String type);
	
	List<Dictionary> getByTypeCodeCommon(String type_code);

	List<DictionaryVo> getByTypeCodeCommonVo(String type_code);

	List<DictionaryVo> getByParent(Long parent);

	Object getByDicTypeAndParent(Dictionary dictionary);

	List<Dictionary> getByGrandTypeCodeCommon(String type_code);

	Dictionary getOneByTypeCodeCommon(@Param("type")String dic_type, @Param("val")String dic_val);
	
	/**
	 * 根据parentId和字典类型，获取当前类型下父节点下的子节点列表
	 * */
	List<Dictionary> getByTypeAndParent(Map<String, Object> map);

	/**
	 * 脱敏配置
	 * @param val
	 */
	void updateVal(@Param("val")String val);

    List<DictionaryVo> selectForCheckListVals(@Param("vals") String[] vals);

    List<DictionaryVo> getTreeByParent(Long id);

	Dictionary loadOneByTypeCode(String type_code);

    List<DictionaryVo> getRootNode(Dictionary dictionary);

    List<DictionaryVo> getAllLevelByTypeCode(String typeCode);
}