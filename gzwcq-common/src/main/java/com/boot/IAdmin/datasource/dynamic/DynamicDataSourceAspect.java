package com.boot.IAdmin.datasource.dynamic;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.core.annotation.Order;

@Aspect
@Order(-1) // 保证在@Transactional之前执行
public class DynamicDataSourceAspect {

	private static final Log logger = LogFactory.getLog(DynamicDataSourceAspect.class);

	/**
	 * 执行方法前更换数据源
	 *
	 * @param joinPoint
	 *            切点
	 * @param targetDataSource
	 *            动态数据源
	 */
	@Before("@annotation(targetDataSource)")
	public void doBefore(JoinPoint joinPoint, TargetDataSource targetDataSource) {
		logger.info(String.format("类{%s}切换数据源：{%s}", joinPoint.getTarget(),targetDataSource.value()));
		DynamicDataSourceContextHolder.set(targetDataSource.value());
	}

	/**
	 * 执行方法后清除数据源设置
	 *
	 * @param joinPoint
	 *            切点
	 * @param targetDataSource
	 *            动态数据源
	 */
	@After("@annotation(targetDataSource)")
	public void doAfter(JoinPoint joinPoint, TargetDataSource targetDataSource) {
		logger.debug(String.format("当前数据源  %s  执行清理方法", targetDataSource.value()));
		DynamicDataSourceContextHolder.clear();
	}

}
