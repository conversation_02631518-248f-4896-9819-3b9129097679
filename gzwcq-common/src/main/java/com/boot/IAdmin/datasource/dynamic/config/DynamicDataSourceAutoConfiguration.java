package com.boot.IAdmin.datasource.dynamic.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.boot.IAdmin.datasource.dynamic.DynamicDataSourceAspect;
import com.boot.IAdmin.datasource.dynamic.DynamicDataSourceBuilder;
import com.boot.IAdmin.datasource.dynamic.DynamicRoutingDataSource;

/**
 * 动态数据源自动配置，此数据源配置会先于默认数据源以及DruidDataSource执行并创建数据源
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass({ DynamicRoutingDataSource.class, DruidDataSource.class })
@AutoConfigureBefore({ DruidDataSourceAutoConfigure.class, DataSourceAutoConfiguration.class })
@ConditionalOnProperty(value = "com.dynamicdatasource.enable", havingValue = "true")
//@EnableConfigurationProperties
//@MapperScan(basePackages = "com.**.mapper*.**")
public class DynamicDataSourceAutoConfiguration {

	/*
	 * private DynamicDataSourceProperties properties;
	 * 
	 * public DynamicDataSourceAutoConfiguration(DynamicDataSourceProperties
	 * properties) { this.properties = properties; }
	 */
	
	@Bean(initMethod="init")
	@ConfigurationProperties(prefix = "com.dynamicdatasource.datasource.master")
	public DynamicDataSourceBuilder masterBuilder() {
		return new DynamicDataSourceBuilder();

	}
	
	@Bean(initMethod="init")
	@ConfigurationProperties(prefix = "com.dynamicdatasource.datasource.slave")
	public DynamicDataSourceBuilder slaveBuilder() {
		return new DynamicDataSourceBuilder();
	}
	
	/**
	 * 核心动态数据源
	 *
	 * @return 数据源实例
	 */
	@Bean
	@Primary
	public DataSource dataSource() {
		DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
		dataSource.setDefaultTargetDataSource(masterBuilder().getDataSource());
		Map<Object, Object> dataSourceMap = new HashMap<>();
		dataSourceMap.put("master", masterBuilder().getDataSource());
		dataSourceMap.put("slave", slaveBuilder().getDataSource());
		dataSource.setTargetDataSources(dataSourceMap);
		return dataSource;
	}

	/**
	 * 事务管理
	 *
	 * @return 事务管理实例
	 */
//	@Bean
	public PlatformTransactionManager platformTransactionManager() {
		return new DataSourceTransactionManager(dataSource());
	}
	

	/**
	 * 注册数据源切换AOP
	 */
	@Bean
	public DynamicDataSourceAspect aynamicDataSourceAspect() {
		return new DynamicDataSourceAspect();
	}
}
