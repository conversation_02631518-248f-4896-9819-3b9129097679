package com.boot.IAdmin.datasource.dynamic;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

public class DynamicRoutingDataSource extends AbstractRoutingDataSource {
	
    private final Log logger = LogFactory.getLog(this.getClass());
    
    @Override
    protected Object determineCurrentLookupKey() {
    	logger.debug(String.format("当前数据源：{%s}", DynamicDataSourceContextHolder.get()));
        return DynamicDataSourceContextHolder.get();
    }
}
