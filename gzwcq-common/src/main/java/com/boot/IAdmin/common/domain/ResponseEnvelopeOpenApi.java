package com.boot.IAdmin.common.domain;

import java.io.Serializable;

/**
 * 通用返回
 * */
public class ResponseEnvelopeOpenApi implements Serializable {


	private static final long serialVersionUID = 1L;
	protected boolean success = true;
	protected String message = "ok";
	protected String data;
	protected boolean state = true;
	protected String resultCode;
	protected Object result;
	//数据条数
	protected Long total;

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public ResponseEnvelopeOpenApi(){

	}

	public ResponseEnvelopeOpenApi(String message){
		this(true, message);
	}

	public ResponseEnvelopeOpenApi(boolean state, String message){
		super();
		this.state=state;
		this.message=message;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public boolean isState() {
		return state;
	}

	public void setState(boolean state) {
		this.state = state;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public Object getResult() {
		return result;
	}

	public void setResult(Object result) {
		this.result = result;
	}

}
