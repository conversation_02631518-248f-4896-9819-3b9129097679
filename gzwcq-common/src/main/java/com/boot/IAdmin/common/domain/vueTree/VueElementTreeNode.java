package com.boot.IAdmin.common.domain.vueTree;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Element树节点
 * */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VueElementTreeNode {
	
	private String id;//节点值
	
	private String label;//节点文本
	
	private String path;//节点路径
	
	private String parent;//父节点
	
	private List<VueElementTreeNode> children;//子节点
	
	private boolean isDisabled;//是否失效
	
	private boolean isNew;//Used for giving new nodes a different color.
	
	private boolean isDefaultExpanded;//是否默认展开
	
	public VueElementTreeNode(){}
	
	public VueElementTreeNode(List<VueElementTreeNode> vueSelectTreeNodeArr, String rootId,String rootLabel){
		this.setId(rootId);
		this.setLabel(rootLabel);
		bulidVueTree(vueSelectTreeNodeArr,this);
		if(this.children.size() > 0) this.setIsDefaultExpanded(true);//默认展开
	}
	
	public VueElementTreeNode(String id, String label) {
		super();
		this.id = id;
		this.label = label;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public List<VueElementTreeNode> getChildren() {
		return children;
	}

	public void setChildren(List<VueElementTreeNode> children) {
		this.children = children;
	}

	
	
	public boolean getIsDisabled() {
		return isDisabled;
	}

	public void setIsDisabled(boolean isDisabled) {
		this.isDisabled = isDisabled;
	}

	public boolean getIsNew() {
		return isNew;
	}

	public void setIsNew(boolean isNew) {
		this.isNew = isNew;
	}

	public boolean getIsDefaultExpanded() {
		return isDefaultExpanded;
	}

	public void setIsDefaultExpanded(boolean isDefaultExpanded) {
		this.isDefaultExpanded = isDefaultExpanded;
	}

	//构建下拉框树
	public String getParent() {
		return parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}
	
	/**
	 * 构建下拉框树
	 * */
	private void bulidVueTree(List<VueElementTreeNode> vueSelectTreeNodeArr,VueElementTreeNode parentNode) {
		for(VueElementTreeNode treeNode : vueSelectTreeNodeArr) {
			if(StringUtils.equalsIgnoreCase(treeNode.getParent(), parentNode.getId())) {//子节点
				if(parentNode.getChildren() == null) parentNode.setChildren(new ArrayList<VueElementTreeNode>());
				parentNode.getChildren().add(treeNode);
				bulidVueTree(vueSelectTreeNodeArr,treeNode);
			}
		}
	}
	
}
