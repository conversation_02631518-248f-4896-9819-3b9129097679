package com.boot.IAdmin.common.domain;

/**
 * 商机常量
 */
public class BusinessConstants {
    //商机阶段
    public static final String BUSINESS_OPPORTUNITY_REPORT = "1001";//商机报备
    public static final String BID_INITIATION = "1002";//投标立项
    public static final String JOINT_EXAMINATION_OF_TENDER = "1003";//投标会审
    public static final String PROJECT_ARRANGEMENT = "1004";//项目安排
    public static final String JOINT_REVIEW_OF_EQU_SEL = "1005";//设备选型会审
    public static final String COST_ESTIMATE_AUDIT = "1006";//成本预估审核
    public static final String COM_TEC_BID_REVIEW = "1008";//商务/技术标审核
    public static final String JOINT_REVIEW_OF_CRE= "1007";//信用等级评价使用会审
    public static final String BID_OPENING_INFORMATION = "1009";//开标信息
    public static final String BID_WINNING_INFORMATION = "1010";//中标信息
    public static final String TEAM_SCORING = "1011";//团队打分
    public static final String PROJECT_HANDOVER = "1012";//项目交接

    //商机状态
    public static final String EARLY_STAGE = "1";	//前期
    public static final String BID = "2";	//投标
    public static final String BID_END = "3";	//成交-中标
    public static final String DIRECT_SIGNATURE_END = "4";	//成交-直签
    public static final String NO_CONTRACT_END = "5";	//成交-无合同
    public static final String FAILED_BID = "6";	//未成交-未中标
    public static final String ABOLISH = "7";	//未成交-废止

    //审核状态
    public static final String TO_BE_SUBMITTED = "1001";	//待提交
    public static final String UNDER_REVIEW = "1002";	//审核中
    public static final String AUDIT_REJECTION = "1003";	//审核拒绝
    public static final String APPROVED = "1004";	//审核通过

    //商机编码前缀
    public static final String PREFIX = "BUSINESS"; //商机编码前缀
    public static final String WORK_FLOW_PREFIX = "WORK_FLOW"; //工作流编码前缀
    //文件链接编码前缀
    public static final String FILE_LINK_PREFIX = "FILE_LINK";

    public static final String DEFAULT_PROCESS = "111111111111"; //默认商机阶段流程二进制
    public static final String DEFAULT_MUST = "110000000000"; //默认商机流程必须二进制

    //投标类型
    public static final String BID_TYPE = "1001"; //投标
    public static final String DIRECT_SIGN = "1002"; //直签
    public static final String NO_CONTRACT = "1003"; //无合同

    //文件夹大类
    public static final String FOLDER_CATEGORIES_ONE = "1";	//业绩库
    public static final String FOLDER_CATEGORIES_TWO = "2";	//资质库
    public static final String FOLDER_CATEGORIES_THREE = "3";	//线索
    public static final String FOLDER_CATEGORIES_FOUR = "4";	//客户
    public static final String FOLDER_CATEGORIES_FIVE = "5";	//合同
    public static final String FOLDER_CATEGORIES_SIX = "6";	//商机

    //文件夹业务类型		FOLDER_BUSINESS_TYPE
    public static final String  FOLDER_BUSINESS_TYPE_ONE = "1";	//线索
    public static final String  FOLDER_BUSINESS_TYPE_TWO = "2";	//客户
    public static final String  FOLDER_BUSINESS_TYPE_THREE = "3";	//合同
    public static final String  FOLDER_BUSINESS_TYPE_FOUR = "4";	//商机
    public static final String  FOLDER_BUSINESS_TYPE_FIVE = "5";	//合同审批
    public static final String  FOLDER_BUSINESS_TYPE_SIX = "6";	//合同移交


    //文档权限
    public static final String DOC_PERMISSIONS_ONE = "1"; //仅我查看
    public static final String DOC_PERMISSIONS_TWO = "2"; //所有人查看
    public static final String DOC_PERMISSIONS_THREE = "3"; //指定人查看

    //高信中标单位id
    public static final String BID_UNIT_GAOXIN = "gaoxin";

    //文件权限
    public static final String owner_auth = "all"; //拥有者权限
    public static final String share_auth = "part"; //分享者权限
    public static final String lock_auth = "lock"; //申请者权限-锁住
    public static final String unlock_auth = "unlock"; //申请者权限-解锁

    //日志类型
    public static final String moduleType = "business"; //商机

    //附件来源 ATTACHMENT_SOURCE
    public static final String ATTACHMENT_SOURCE_ONE = "3" ;//商机报备
    public static final String ATTACHMENT_SOURCE_TWO = "4" ;//商机项目立项
    public static final String ATTACHMENT_SOURCE_THREE = "5" ;//投标会审
    public static final String ATTACHMENT_SOURCE_FOUR = "6" ;//项目安排
    public static final String ATTACHMENT_SOURCE_FIVE = "7" ;//设备选型
    public static final String ATTACHMENT_SOURCE_SIX = "8" ;//成本预估
    public static final String ATTACHMENT_SOURCE_SEVEN = "9" ;//信用评价
    public static final String ATTACHMENT_SOURCE_EIGHT = "10" ;//商务技术标
    public static final String ATTACHMENT_SOURCE_NINE = "11" ;//开标信息
    public static final String ATTACHMENT_SOURCE_TEN = "12" ;//中标信息
    public static final String ATTACHMENT_SOURCE_ELEVEN = "13" ;//项目交接

    //商机读写权限
    public static final String PERMISSIONS_READ = "1"; //只读
    public static final String PERMISSIONS_READ_AND_WRITE = "2"; //读写

    //消息任务跳转路由
    public static final String NOTICE_JUMP = "/approvalDetails/";

    //消息任务类型
    public static final String NOTICE_TYPE_ONE = "1"; //消息
    public static final String NOTICE_TYPE_TWO = "2"; //任务

    //文件链接失效日期类型
    public static final String PERMANENT_FAILURE = "0"; //永久有效
    //商机阶段常量
    public static final String BUSINESS_OPPORTUNITIES_PHASE = "BUSINESS_OPPORTUNITIES_PHASE"; //永久有效

}
