package com.boot.IAdmin.common.utils;

import java.util.regex.Pattern;

public class RegexUtil {
	
	 /**
     * 正则表达式：验证用户名
     */
    public static final String REGEX_USERNAME = "^[a-zA-Z]\\w{5,17}$";
 
    /**
     * 正则表达式：验证密码
     */
    public static final String REGEX_PASSWORD = "^[a-zA-Z0-9]{6,16}$";
 
    /**
     * 正则表达式：验证手机号
     */
//    public static final String REGEX_MOBILE = "^((13[0-9])|(15[^4,\\D])|(18[0,5-9]))\\d{8}$";
    
    /**
     * 13(老)号段:130：中国联通，GSM		131：中国联通，GSM		132：中国联通，GSM		133：中国联通，后转给中国电信，CDMA		134：中国移动，GSM		135：中国移动，GSM		136：中国移动，GSM		137：中国移动，GSM		138：中国移动，GSM		139：中国移动，GSM
     * 14          号段:145：中国联通，上网卡	147：中国移动，上网卡
     * 15(新)号段:150：中国移动，GSM		151：中国移动，GSM		152：中国联通，GSM		153：中国联通，后转给中国电信，CDMA		154：154号段暂时没有分配   155：中国联通，GSM		156：中国联通，GSM		157：中国移动，GSM		158：中国移动，GSM		159：中国移动，GSM
     * 17(4G)号段:170：虚拟运营商		176：联通				177：电信				178：移动
     * 18(3G)号段:180：中国电信，3G		181：中国电信，3G		182：中国移动，3G		183：中国移动，3G						184：中国移动，3G		185：中国联通，3G		186：中国联通，3G		187：中国移动，3G		188：中国移动，3G，TD-CDMA		189：中国电信，3G，CDMA，天翼189
     * 经过分析：13全号段,14没有,15段除了154以外都可用,17段170 176 177 178,18段都可用
     */
    public static final String REGEX_MOBILE = "^((13[0-9])|(15[^4,\\D])|(17[0,6,7,8])|(18[0-9]))\\d{8}$";
 
    /**
     * 正则表达式：验证邮箱
     */
    public static final String REGEX_EMAIL = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
 
    /**
     * 正则表达式：验证汉字
     */
    public static final String REGEX_CHINESE = "^[\u4e00-\u9fa5],{0,}$";
 
    /**
     * 正则表达式：验证身份证
     */
    public static final String REGEX_ID_CARD = "(^\\d{18}$)|(^\\d{15}$)";
 
    /**
     * 正则表达式：验证URL
     */
    public static final String REGEX_URL = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
 
    /**
     * 正则表达式：验证IP地址
     */
    public static final String REGEX_IP_ADDR = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
 
    /**
     * 校验用户名
     * 
     * @param username
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isUsername(String username) {
        return Pattern.matches(REGEX_USERNAME, username);
    }
 
    /**
     * 校验密码
     * 
     * @param password
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isPassword(String password) {
        return Pattern.matches(REGEX_PASSWORD, password);
    }
 
    /**
     * 校验手机号
     * 
     * @param mobile
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isMobile(String mobile) {
        return Pattern.matches(REGEX_MOBILE, mobile);
    }
 
    /**
     * 校验邮箱
     * 
     * @param email
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isEmail(String email) {
        return Pattern.matches(REGEX_EMAIL, email);
    }
 
    /**
     * 校验汉字
     * 
     * @param chinese
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isChinese(String chinese) {
        return Pattern.matches(REGEX_CHINESE, chinese);
    }
 
    /**
     * 校验身份证
     * 
     * @param idCard
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isIDCard(String idCard) {
        return Pattern.matches(REGEX_ID_CARD, idCard);
    }
 
    /**
     * 校验URL
     * 
     * @param url
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isUrl(String url) {
        return Pattern.matches(REGEX_URL, url);
    }
 
    /**
     * 校验IP地址
     * 
     * @param ipAddr
     * @return
     */
    public static boolean isIPAddr(String ipAddr) {
        return Pattern.matches(REGEX_IP_ADDR, ipAddr);
    }
 
    /*public static void main(String[] args) {
        String username = "fdsdfsdj";
        String phone = "18812345678";
        System.out.println(RegexUtil.isUsername(username));
        System.out.println(RegexUtil.isChinese(username));
        System.out.println(RegexUtil.isMobile(phone));
    }*/
    
    
}
