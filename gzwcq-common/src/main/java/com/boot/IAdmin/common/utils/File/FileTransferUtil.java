package com.boot.IAdmin.common.utils.File;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;

import com.boot.IAdmin.common.utils.HttpClientUtils;

/**
 * 文件传输工具类
 * 
 * <AUTHOR>
 * @date 2016年4月27日 09:06:36
 * */
public class FileTransferUtil {
	
	public final static String SEPARATOR = "/";
	
	private static final Log logger = LogFactory.getLog(FileTransferUtil.class);

	
	/**
	 * 检查目录是否存在，不存在则创建
	 * */
	public static void checkAndMkdir(String fileName) {
		File newFile = new File(fileName);
		if (!newFile.exists()) {
			newFile.mkdirs();
		}
	}

	/**
	 * 文件传输
	 * <P>
	 * 作为客户端向服务端推送文件
	 * 
	 * @param file 当前文件
	 * @param url 远程服务地址
	 * @param remoteDir 远程文件目录
	 * */
	public static String httpFileTransfer(File file, String url,String remoteDir) throws Exception {
		CloseableHttpClient httpclient = (CloseableHttpClient)HttpClientUtils.getHttpClient();
		String msg = ""; 
		try {
			HttpPost httppost = new HttpPost(url);

			FileBody bin = new FileBody(file,ContentType.DEFAULT_BINARY,URLEncoder.encode(file.getName(),"UTF-8"));
			
//			StringBody comment = new StringBody(file.getName(), Consts.UTF_8);
			
			StringBody remoteDirBin = new StringBody(remoteDir,ContentType.TEXT_PLAIN);
			
			HttpEntity reqEntity = MultipartEntityBuilder.create().addPart("file", bin).addPart("remoteDir", remoteDirBin).build();
			
			httppost.setEntity(reqEntity);
			
			CloseableHttpResponse response = httpclient.execute(httppost);
			try {
				HttpEntity resEntity = response.getEntity();
				if (resEntity != null) {
					msg = EntityUtils.toString(resEntity);
					logger.info("@推送状态："+msg);
				}
				EntityUtils.consume(resEntity);
			} finally {
				response.close();
			}
		} finally {
			httpclient.close();
		}
		return msg;
	}

	/**
	 * 把文件从原始路径拷贝到新路径下并删除原来路径下的文件
	 * <P>自动在目标路径下新建日期文件夹
	 * @author: ruanhj
	 * 
	 * @param oldPath
	 *            旧的文件目录
	 * @param newPath
	 *            新的文件目录
	 * @throws Exception
	 */
	public static void transferFile(File oldFile, String newPath) throws Exception {
		int byteread = 0;
		FileInputStream fin = null;
		FileOutputStream fout = null;
		try {
			if (oldFile.exists()) {
				logger.info(String.format("@开始移动文件[%s]", oldFile.getName()));
				fin = new FileInputStream(oldFile);
				File newFile = new File(newPath+SEPARATOR+new SimpleDateFormat("yyyyMM").format(new Date()));
				if(!newFile.exists()) {
					newFile.mkdirs();
				}
				fout = new FileOutputStream(newFile.getAbsolutePath()+SEPARATOR+oldFile.getName());
				byte[] buffer = new byte[1024];
				while ((byteread = fin.read(buffer)) != -1) {
					fout.write(buffer, 0, byteread);
				}
				if(fin != null) fin.close();
				logger.info(String.format("@移动文件[%s]成功", oldFile.getName()));
				delleteFile(oldFile);
			} else {
				throw new Exception("需要转移的文件不存在!");
			}
		} catch (Exception e) {
			logger.error(e);
			throw e;
		} finally {
			if (fin != null)
				fin.close();
			if (fout != null)
				fout.close();
		}
	}
	
	
	/**
	 * 把文件从原始路径拷贝到新路径下
	 * <P>不做处理直接拷贝,不删除
	 * @author: ruanhj
	 * 
	 * @param oldFile
	 *            旧的文件
	 * @param newFile
	 *            新的文件
	 * @throws Exception
	 */
	public static void transferFileOnly(File oldFile, File newFile) throws Exception {
		int byteread = 0;
		FileInputStream fin = null;
		FileOutputStream fout = null;
		try {
			if (oldFile.exists()) {
				logger.info(String.format("@开始移动文件FROM[%s]TO[%s]", oldFile.getAbsolutePath(),newFile.getAbsolutePath()));
				fin = new FileInputStream(oldFile);
				fout = new FileOutputStream(newFile);
				byte[] buffer = new byte[1024];
				while ((byteread = fin.read(buffer)) != -1) {
					fout.write(buffer, 0, byteread);
				}
				if(fin != null) fin.close();
				logger.info(String.format("@移动文件[%s]成功", oldFile.getAbsolutePath()));
			} else {
				throw new Exception("需要转移的文件不存在!");
			}
		} catch (Exception e) {
			logger.error(e);
			throw e;
		} finally {
			if (fin != null)
				fin.close();
			if (fout != null)
				fout.close();
		}
	}
	
	
	/**
	 * 删除文件,只支持删除文件,不支持删除目录
	 * 
	 * @param file
	 *            要删除的文件
	 * @throws Exception
	 */
	public static void delleteFile(File file) throws Exception {
		logger.info(String.format("@开始删除文件[%s]", file.getName()));
		if (!file.exists()) {
			throw new Exception("文件" + file.getName() + "不存在,请确认!");
		}
		if (file.isFile()) {
			if (file.canWrite()) {
				file.delete();
				logger.info(String.format("@删除文件[%s]成功", file.getName()));
			} else {
				throw new Exception("文件" + file.getName() + "只读,无法删除,请手动删除!");
			}
		} else {
			throw new Exception("文件" + file.getName() + "不是一个标准的文件,有可能为目录,请确认!");
		}
	}
	
	/**
	 * 判断文件是否已经存在
	 * */
	public static boolean isExists(File file) {
		if(file.exists()){
			return true;
		}
		return false;
	}
}
