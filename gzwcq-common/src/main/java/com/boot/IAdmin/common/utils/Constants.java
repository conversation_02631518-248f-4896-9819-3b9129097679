package com.boot.IAdmin.common.utils;

/**
 * 常量
 */
public class Constants {

    public static final String OBJECT_UPDATE_RECORD = "object_update_record";    //对象修改记录
    //图片格式
    public static final String[] IMAGE_FILE_TYPE_FILTER = {"png", "jpg", "bmp", "jpeg", "PNG", "JPG", "BMP", "JPEG", "mp4", "avi", "ogv", "m4v", "mpeg", "wmv", "mov", "ogm", "webm", "asx", "mpg"};

    //模块修改记录类别
    public static final String MODEL_RECORD_TYPE_WO = "";
    public static final String MODEL_RECORD_TYPE_PU = "";

    //删除状态（框架需要，用于逻辑删除）
    public static final String NO_DELETED = "N";
    public static final String DELETED = "Y";

    //请求参数
    public static final String CLIENT_ID = "ClientId";
    public static final String OPERATION_CODE = "OperationCode";

    //cico主数据状态
    public static final String CICO_STATUS_0 = "1";//活动数据
    public static final String CICO_STATUS_1 = "0";//冻结数据

    public static final String ROOT_ORG = "39DC82B5A000000125E54F37FE103416";//组织根节点编号

    //字典表对应常量
    public static final String DIC_FLOW = "DIC_FLOW";
    public static final String DIC_FLOW_STATUS = "DIC_FLOW_STATUS";
    public static final String EARLY_WARNING_TYPE = "EARLY_WARNING_TYPE";

    //是否为业务单元
    public static final String YES = "Y";
    public static final String NO = "N";
    public static final String REPORT_AUDIT_STATUS = "REPORT_AUDIT_STATUS";
    public static final String ApproveStatus_1 = "1001";
    public static final String ApproveStatus_2 = "1002";
    public static final String ApproveStatus_3 = "1003";
    public static final String ApproveStatus_4 = "1004";

    //上移下移类型
    public static final String MOVE_UP = "up"; //上移
    public static final String MOVE_DOWN = "down"; //上移

    //超管角色
    public static final String SUPER_ADMIN = "SUPER_ADMIN";  //超管角色

    //企业类型
    public static final String QYLX_GYQY = "1";  //国有资本(企业性质空的默认为国有企业)
    public static final String QYLX_HHQY = "2";  //合伙企业

    //审核节点审核状态
    public static final String APPROVAL_STATUS_1 = "3"; //上报
    public static final String APPROVAL_STATUS_2 = "6"; //待审核
    public static final String APPROVAL_STATUS_3 = "1"; //审核通过
    public static final String APPROVAL_STATUS_4 = "2"; //审核退回
    public static final String APPROVAL_STATUS_5 = "5"; //待上报
    public static final String APPROVAL_STATUS_9 = "9"; //工商登记资料补录

    //登记表审核状态
    public static final String REVIEW_STATUS_1 = "0"; //待上报
    public static final String REVIEW_STATUS_2 = "1"; //待审核
    public static final String REVIEW_STATUS_3 = "2"; //审核通过
    public static final String REVIEW_STATUS_4 = "3"; //退回
    public static final String REVIEW_STATUS_9 = "9"; //工商登记资料补录

    //基本信息表审核状态
    public static final String JBXX_REVIEW_STATUS_1 = "3"; //审核中
    public static final String JBXX_REVIEW_STATUS_2 = "4"; //审核通过
    public static final String JBXX_REVIEW_STATUS_3 = "8"; //流程未发起
    public static final String JBXX_REVIEW_STATUS_9 = "9"; //工商登记资料补录

    //初审、复审
    public static final String REVIEW_FIRST_TRIAL = "初审";
    public static final String REVIEW_SECEND_TRIAL = "复审";

    //用户初审、复审枚举值
    public static final String REVIEW_FIRST_TRIAL_1 = "1";  //初审
    public static final String REVIEW_SECEND_TRIAL_2 = "2"; //复审

    public static final String REVIEW_AUDITOR = "审核人";
    public static final String REVIEW_FQZ = "发起人";

    //是否已办工商
    public static final String JBXX_SFYBGS_Y = "1";//是
    public static final String JBXX_SFYBGS_N = "2";//否
    public static final String JBXX_SFYBGS_UN = "3";//不涉及

    //登记类型
    public static final String RG_TYPE_ZY = "0";//占有
    public static final String RG_TYPE_BD = "1";//变动
    public static final String RG_TYPE_ZX = "3";//注销

    //预警大类
    public static final String YJDL_ZD = "1";//自动
    public static final String YJDL_BZD = "2";//半自动

    //变动类型
    public static final String BDLX_QYJC = "QYJC";//企业级次
    public static final String BDLX_ZZXS = "ZZXS";//组织形式
    public static final String BDLX_CZRXX = "CZRXX";//出资人信息
    public static final String BDLX_QYLB = "QYLB";//企业类别

    //变动状态
    public static final String BDZT_0 = "0";//未处理
    public static final String BDZT_1 = "1";//已处理

    //境内境外
    public static final String JNJW_1 = "1";//境内
    public static final String JNJW_2 = "2";//境外

    //字段类型
    public static final String FIELD_TYPE_DIC = "dic";//字典
    public static final String FIELD_TYPE_DATE = "date";//日期
    public static final String FIELD_TYPE_DATE_RANGE = "dateRange";//日期区间
    public static final String FIELD_TYPE_TEXT = "text";//文本
    public static final String FIELD_TYPE_NUM = "num";//数字

    //账号类型
    public static final String SYSTEM_ADMIN_TYPE = "1";//系统管理员
    public static final String BUSINESS_ADMIN_TYPE = "2";//业务管理员

    //系统管理员
    public static final String SYSTEM_ADMIN = "SYSTEM_ADMIN"; //企业系统管理员：公告、文件、组织机构、用户
    public static final String GZW_ADMIN = "GZW_ADMIN"; //国资委管理员（企业为国资委+账号类型为系统管理员）
    //public static final String ONE_LEVEL_ADMIN = "ONE_LEVEL_ADMIN"; //一级企业管理员（企业级次为“1”+账号类型为系统管理员）
    public static final String ZJSGZW_ORG_ID = "39DC82B5C0000021A568D4D612672F5A";//浙江省国资委unitId
    //业务管理员：
    public static final String SGZW_FIRST_TRIAL = "SGZW_FIRST_TRIAL"; //省国资委复审（企业为浙江省国资委+账号类型为业务管理员+审核层级复审）
    public static final String SGZW_RECHECK = "SGZW_RECHECK"; //省国资委复审（企业为浙江省国资委+账号类型为业务管理员+审核层级复审）

    public static final String GZW_RECHECK = "GZW_RECHECK"; //国资委复审（企业为非浙江省国资委+账号类型为业务管理员+审核层级复审）
    public static final String GZW_FIRST_TRIAL = "GZW_FIRST_TRIAL"; //国资委初审（企业为非浙江省国资委+账号类型为业务管理员+审核层级初审）
    //public static final String ONE_LEVEL_RECHECK = "ONE_LEVEL_RECHECK"; //一级企业复审（企业级次为“1”+账号类型为业务管理员+审核层级复审）
    //public static final String ONE_LEVEL_FIRST_TRIAL = "ONE_LEVEL_FIRST_TRIAL"; //一级企业初审（企业级次为“1”+账号类型为业务管理员+审核层级初审）
    //public static final String NORMAL_REVIEW = "NORMAL_REVIEW"; //普通审核（企业级次为“2”及以上数字+账号类型为业务管理员）
    public static final String FIRST_TRIAL = "FIRST_TRIAL"; //企业初审（企业非国资委+账号类型为业务管理员,审核层级为初审）
    public static final String RECHECK = "RECHECK"; //企业复审（企业非国资委+账号类型为业务管理员,审核层级为复审）
    public static final String ZYCQDJQX = "ZYCQDJQX";
    public static final String BDCQDJQX = "BDCQDJQX";
    public static final String ZXCQDJQX = "ZXCQDJQX";

    public static final String GWY_GZW_ZDBL_QX = "22000"; //国务院国资委新增字段补录 - 变动情形

    public static final String GSDJYBLZT = "产权工商均已设立登记"; //产权工商均已设立登记
}
