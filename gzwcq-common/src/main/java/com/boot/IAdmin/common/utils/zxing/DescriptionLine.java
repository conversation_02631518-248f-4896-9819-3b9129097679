package com.boot.IAdmin.common.utils.zxing;

import org.apache.commons.lang3.StringUtils;

/**
 * 描述行
 * */
public class DescriptionLine {
	
	private String lineStr = StringUtils.EMPTY;
	
	private int lineWith;

	public String getLineStr() {
		return lineStr;
	}

	public void setLineStr(String lineStr) {
		this.lineStr = lineStr;
	}

	public int getLineWith() {
		return lineWith;
	}

	public void setLineWith(int lineWith) {
		this.lineWith = lineWith;
	}
	
	public void appendLineStr(char str) {
		this.lineStr += str;
	}
	
}
