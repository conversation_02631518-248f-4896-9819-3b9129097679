/*
 * 版权所有：杭州领域向尚科技有限公司
 * 地址：浙江省杭州市滨江区滨安路688号天和高科技产业园5-2311
 *
 * (c) Copyright Hangzhou  Area Up Technology Co., Ltd.
 */

package com.boot.IAdmin.common.resource.impl;

import com.boot.IAdmin.common.resource.StaticResourceService;
import com.boot.IAdmin.common.resource.base.HttpFileResource;
import com.boot.IAdmin.common.resource.vfs.VfsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * 开发模式下静态资源处理
 *
 * <AUTHOR>
 * @date 04/12/2017
 */
@Service
public class LocalSharedResourceServiceImpl implements StaticResourceService {
    private final String urlPrefix;
    @Autowired
    private VfsHelper vfsHelper;


    public LocalSharedResourceServiceImpl() {
        urlPrefix = "http://************/file/";
    }

    private String createConnectionString(String path) throws URISyntaxException {
        String user = "root";
        String passwd = "zjhc@crm6";
        String host = "************";
        path = "/usr/local/app/file/" + path;

        String userInfo = user + ":" + passwd;

        URI uri = new URI("sftp", userInfo, host, 22,
                    path, null, null);

        //String s = uri.toString();
        return uri.toString();
    }

    @Override
    public String upload(String path, File file) throws IOException, URISyntaxException {
        vfsHelper.uploadBySftp(file.getAbsolutePath(), createConnectionString(path));
        return urlPrefix + path;
    }

    @Override
    public Resource get(String path) throws URISyntaxException {
        String uri = urlPrefix + path;
        return new HttpFileResource(new URI(uri));
    }

    @Override
    public void delete(String path) throws IOException {

    }
}
