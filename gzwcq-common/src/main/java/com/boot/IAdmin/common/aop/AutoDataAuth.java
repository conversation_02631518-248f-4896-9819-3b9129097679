package com.boot.IAdmin.common.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AutoDataAuth {
	
	String createdBy()  default "create_user";//创建人字段
	
	String orgId()  default "org_id";//创建人组织字段
	
	int queryObjectSeq() default 0; //查询对象在参数中的序号
}
