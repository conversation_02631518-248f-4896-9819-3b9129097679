package com.boot.IAdmin.common.utils.office;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import javax.imageio.ImageIO;
import org.apache.commons.io.IOUtils;
import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;

/**
 * 文件操作工具类
 * */
public class OfficeUtils {
	
	/**
	 * 图片转pdf
	 * */
	public static void convertImageToPdf(BufferedImage imgage, OutputStream out) throws Exception {
		ByteArrayOutputStream imageOut = null;
		try {
			Document doc = new Document(null, 0, 0, 0, 0);
			doc.setPageSize(new Rectangle(imgage.getWidth(), imgage.getHeight()));
			imageOut = new ByteArrayOutputStream();
			ImageIO.write(imgage, "jpg", imageOut);
			Image image = Image.getInstance(imageOut.toByteArray());
			PdfWriter.getInstance(doc, out);
			doc.open();
			doc.add(image);
			doc.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			IOUtils.closeQuietly(imageOut);
		}
	}
	
	/**
	 * 合并pdf
	 * @param pdfOuts 需要合并的pdf集合
	 * @param out 合并后输出流
	 * */
	public static OutputStream mergePdfFiles(ByteArrayOutputStream[] pdfOuts,OutputStream out) {  
        Document document = null;
        try {  
            document = new Document(new PdfReader(pdfOuts[0].toByteArray()).getPageSize(1));  
            PdfCopy copy = new PdfCopy(document, out);  
            document.open();  
            for (int i = 0; i < pdfOuts.length; i++) {
                PdfReader reader = new PdfReader(pdfOuts[i].toByteArray());  
                int n = reader.getNumberOfPages();  
                for (int j = 1; j <= n; j++) {  
                    document.newPage();  
                    PdfImportedPage page = copy.getImportedPage(reader, j);  
                    copy.addPage(page);  
                }  
            }  
        } catch (Exception e) {  
            e.printStackTrace();  
        } finally {  
            document.close();  
        }  
        return out;
    }  
}
