package com.boot.IAdmin.common.domain.ztree;

import java.util.Collection;

/**
 * jquery.zTree 节点对象
 * 
 * <AUTHOR>
 * @date 2016年9月20日 15:00:29
 * */
public class ZTreeNode {
	
	// ID
	protected String id;
	// 节点名称
	protected String name;
	// 子集
	protected Collection<ZTreeNode> children;
	// 是否是父节点
	protected boolean isParent;
	//子节点数目
	protected Long childNum;
	//是否选中
	protected boolean checked;
	//描述
	protected String description;
	//编码
	protected String code;
	
	protected boolean showCheckBox;//自定义属性--是否需要在ztree上面展示chekckbox框(全局设置的会导致页面ztree全部checkbox展示或者消失)

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Collection<ZTreeNode> getChildren() {
		return children;
	}

	public void setChildren(Collection<ZTreeNode> children) {
		this.children = children;
	}

	public boolean getIsParent() {
		return isParent;
	}

	public void setIsParent(boolean isParent) {
		this.isParent = isParent;
	}

	public Long getChildNum() {
		return childNum;
	}

	public void setChildNum(Long childNum) {
		this.childNum = childNum;
		//判断是否为父节点
		this.setIsParent(childNum.longValue()>0);
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public boolean isShowCheckBox() {
		return showCheckBox;
	}

	public void setShowCheckBox(boolean showCheckBox) {
		this.showCheckBox = showCheckBox;
	}
	
}
