/*
 * 版权所有：杭州领域向尚科技有限公司
 * 地址：浙江省杭州市滨江区滨安路688号天和高科技产业园5-2311
 *
 * (c) Copyright Hangzhou  Area Up Technology Co., Ltd.
 */

package com.boot.IAdmin.common.resource.base;


import com.boot.IAdmin.common.resource.function.FileObjectFunction;
import com.boot.IAdmin.common.resource.vfs.VfsHelper;
import lombok.Getter;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.vfs2.FileObject;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.WritableResource;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.net.URI;
import java.net.URL;

/**
 * Created by allan on 04/12/2017.
 */
@Getter
public class VfsFileSystemResource extends AbstractResource implements WritableResource {
    private static final Log log = LogFactory.getLog(VfsFileSystemResource.class);

    /**
     * 资源访问路径
     */
    private final URI fileUri;
    /**
     * 资源绝对路径
     */
    private final String fileName;

    /**
     * 相对路径之前的路径，以"/"结尾
     */
    private final String fileHome;

    /**
     * 资源相对路径
     */
    private final String filePath;

    private transient final VfsHelper helper;

    public VfsFileSystemResource(VfsHelper vfsHelper, String fileHome, String filePath, URI fileUri) {
        this.fileUri = fileUri;
        this.helper = vfsHelper;
        this.filePath = filePath;
        this.fileHome = fileHome;
        this.fileName = fileHome + filePath;

    }

    public <T> T accessFileObject(FileObjectFunction<FileObject, T> function) throws IOException {
        return helper.handle(fileName, function);
    }

    @Override
    public boolean exists() {
        try {
            return accessFileObject(FileObject::exists);
        } catch (IOException e) {
            log.error("exists", e);
            return false;
        }
    }

    @Override
    public long contentLength() throws IOException {
        return accessFileObject(fileObject -> fileObject.getContent().getSize());
    }

    @Override
    public long lastModified() throws IOException {
        return accessFileObject(fileObject -> fileObject.getContent().getLastModifiedTime());
    }

    @Override
    public String getFilename() {
        return super.getFilename();
    }

    @Override
    public URI getURI() throws IOException {
        return fileUri;
    }

    @Override
    public URL getURL() throws IOException {
        return fileUri.toURL();
    }

    @Override
    public boolean isWritable() {
        try {
            return accessFileObject(FileObject::isWriteable);
        } catch (IOException e) {
            log.error("isWritable", e);
            return false;
        }
    }

    @Override
    public OutputStream getOutputStream() throws IOException {
        return new ByteArrayOutputStream() {
            @Override
            public void close() throws IOException {
                accessFileObject(fileObject -> {
                    OutputStream outputStream = fileObject.getContent().getOutputStream();
                    StreamUtils.copy(buf, outputStream);
                    outputStream.flush();
                    outputStream.close();
                    return null;
                });
            }
        };
    }

    @Override
    public String getDescription() {
        return fileName;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return accessFileObject(fileObject -> {
            InputStream inputStream = fileObject.getContent().getInputStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            StreamUtils.copy(inputStream, byteArrayOutputStream);
            inputStream.close();
            return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        });

    }
}
