package com.boot.IAdmin.common.utils.zxing;

import java.io.File;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 二维码实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CodeModel {
	
	private String contents;// 二维码信息
	private int width = 300;
	private int height = 300;
	private String format = "jpg";
	private String character_set = "utf-8";
	private int fontSize = 12;
	private File logoFile;// 二维码中间图标
	private float logoRatio = 0.20f;
	private String bottomDesc;// 二维码下面的字
	private String topDesc;// 二维码上面的字
	private int whiteWidth;// 白边的宽度
	private int[] bottomStart;// 二维码最下边的开始坐标
	private int[] bottomEnd;// 二维码最下边的结束坐标
	
	public String getContents() {
		return contents;
	}

	public void setContents(String contents) {
		this.contents = contents;
	}

	public int getWidth() {
		return width;
	}

	public void setWidth(int width) {
		this.width = width;
	}

	public int getHeight() {
		return height;
	}

	public void setHeight(int height) {
		this.height = height;
	}

	public String getFormat() {
		return format;
	}

	public void setFormat(String format) {
		this.format = format;
	}

	public String getCharacter_set() {
		return character_set;
	}

	public void setCharacter_set(String character_set) {
		this.character_set = character_set;
	}

	public int getFontSize() {
		return fontSize;
	}

	public void setFontSize(int fontSize) {
		this.fontSize = fontSize;
	}

	public File getLogoFile() {
		return logoFile;
	}

	public void setLogoFile(File logoFile) {
		this.logoFile = logoFile;
	}

	public float getLogoRatio() {
		return logoRatio;
	}

	public void setLogoRatio(float logoRatio) {
		this.logoRatio = logoRatio;
	}

	public String getBottomDesc() {
		return bottomDesc;
	}

	public void setBottomDesc(String bottomDesc) {
		this.bottomDesc = bottomDesc;
	}

	public int getWhiteWidth() {
		return whiteWidth;
	}

	public void setWhiteWidth(int whiteWidth) {
		this.whiteWidth = whiteWidth;
	}

	public int[] getBottomStart() {
		return bottomStart;
	}

	public void setBottomStart(int[] bottomStart) {
		this.bottomStart = bottomStart;
	}

	public int[] getBottomEnd() {
		return bottomEnd;
	}

	public void setBottomEnd(int[] bottomEnd) {
		this.bottomEnd = bottomEnd;
	}

	public String getTopDesc() {
		return topDesc;
	}

	public void setTopDesc(String topDesc) {
		this.topDesc = topDesc;
	}
	
}