package com.boot.IAdmin.common.utils.zxing;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.imageio.ImageIO;
import org.apache.commons.lang3.StringUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

/**
 * 二维码生成
 */
public class CodeCreator {
	private static int BLACK = 0x000000;
	private static int WHITE = 0xFFFFFF;

	public static BufferedImage createCodeImage(CodeModel info) {
		String contents = info.getContents();
		int width = info.getWidth();
		int height = info.getHeight();
		Map<EncodeHintType, Object> hint = new HashMap<>();
		hint.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
		hint.put(EncodeHintType.CHARACTER_SET, info.getCharacter_set());
		hint.put(EncodeHintType.MARGIN, 0);
		MultiFormatWriter writer = new MultiFormatWriter();
		BufferedImage img = null;
		try {
			BitMatrix bm = writer.encode(contents, BarcodeFormat.QR_CODE, width, height, hint);
			int[] locationTopLeft = bm.getTopLeftOnBit();
			int[] locationBottomRight = bm.getBottomRightOnBit();
			info.setBottomStart(new int[] { locationTopLeft[0], locationBottomRight[1] });
			info.setBottomEnd(locationBottomRight);
			int w = bm.getWidth();
			int h = bm.getHeight();
			img = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
			for (int x = 0; x < w; x++) {
				for (int y = 0; y < h; y++) {
					img.setRGB(x, y, bm.get(x, y) ? BLACK : WHITE);
				}
			}
		} catch (WriterException e) {
			e.printStackTrace();
		}
		return img;
	}
	
	public static BufferedImage createCodeTotalImage(CodeModel info) throws IOException {
		// 创建二维码
		BufferedImage bm = createCodeImage(info);
		// 如果存在logo则增加
		if (info.getLogoFile() != null && info.getLogoFile().exists())
			bm = addLogoImage(bm, info);
		// 增加头部描述
		if (StringUtils.isNotBlank(info.getTopDesc()))
			bm = addTopDesc(bm, info);
		// 增加底部描述
		if (StringUtils.isNotBlank(info.getBottomDesc()))
			bm = addBottomDesc(bm, info);
		return bm;
	}

	public static void createCodeImage(CodeModel info, OutputStream output) throws IOException {
		// 创建二维码
		BufferedImage bm = createCodeImage(info);
		// 如果存在logo则增加
		if (info.getLogoFile() != null && info.getLogoFile().exists())
			bm = addLogoImage(bm, info);
		// 增加头部描述
		if (StringUtils.isNotBlank(info.getTopDesc()))
			bm = addTopDesc(bm, info);
		// 增加底部描述
		if (StringUtils.isNotBlank(info.getBottomDesc()))
			bm = addBottomDesc(bm, info);
		// 输出
		ImageIO.write(bm, StringUtils.isEmpty(info.getFormat()) ? info.getFormat() : info.getFormat(), output);
	}
	
	private static BufferedImage addTopDesc(BufferedImage bm, CodeModel info) {
		String desc = info.getTopDesc();
		int width = bm.getWidth();// 二维码图片宽度
		int height = bm.getHeight();// 二维码图片高度
		Graphics g = bm.getGraphics();
		int whiteWidth = info.getHeight() - info.getBottomEnd()[1];
		Font font = new Font("宋体", Font.PLAIN, info.getFontSize());
		int fontHeight = g.getFontMetrics(font).getHeight();
		// 计算需要多少行
		List<DescriptionLine> lineArr = new ArrayList<DescriptionLine>();
		int lineNum = 1;
		int currentLineLen = 0;
		DescriptionLine line = new DescriptionLine();
		lineArr.add(line);
		for (int i = 0; i < desc.length(); i++) {
			char c = desc.charAt(i);
			int charWidth = g.getFontMetrics(font).charWidth(c);
			if (currentLineLen + charWidth > width) {
				lineNum++;
				currentLineLen = 0;
				line = new DescriptionLine();
				line.appendLineStr(c);
				lineArr.add(line);
				continue;
			}
			currentLineLen += charWidth;
			line.appendLineStr(c);
			line.setLineWith(currentLineLen);
		}
		int totalFontHeight = fontHeight * lineNum;// 段落高度
		int wordTopMargin = 0;// 上间距
		int wordImageMargin = 4;// 文本和图片间距
		// 创建一张新的图片
		BufferedImage bm1 = new BufferedImage(width, height + totalFontHeight + wordTopMargin + wordImageMargin, BufferedImage.TYPE_INT_RGB);
		Graphics g1 = bm1.getGraphics();
		if (totalFontHeight + wordTopMargin + wordImageMargin - whiteWidth > 0) {
			g1.setColor(Color.WHITE);
			g1.fillRect(0, 0, width, totalFontHeight + wordTopMargin + wordImageMargin);
		}
		g1.setColor(new Color(BLACK));
		g1.setFont(font);
		width = info.getBottomEnd()[0] - info.getBottomStart()[0];
		int baseLo = g1.getFontMetrics().getAscent();
		// 画出描述信息
		for (int lineIndex = 0; lineIndex < lineArr.size(); lineIndex++) {
			DescriptionLine dLine = lineArr.get(lineIndex);
			g1.drawString(dLine.getLineStr(), ((width > dLine.getLineWith()) ? (width - dLine.getLineWith()) / 2 : 0), baseLo + fontHeight * lineIndex + wordTopMargin);
		}
		// 将二维码图片添加到新图片中
		g1.drawImage(bm, 0, totalFontHeight + wordTopMargin + wordImageMargin, null);
		g1.dispose();
		bm = bm1;
		return bm;
	}

	private static BufferedImage addBottomDesc(BufferedImage bm, CodeModel info) {
		String desc = info.getBottomDesc();
		int width = bm.getWidth();// 二维码图片宽度
		int height = bm.getHeight();// 二维码图片高度
		Graphics g = bm.getGraphics();
		int whiteWidth = info.getHeight() - info.getBottomEnd()[1];
		Font font = new Font("宋体", Font.PLAIN, info.getFontSize());
		int fontHeight = g.getFontMetrics(font).getHeight();
		// 计算需要多少行
		List<DescriptionLine> lineArr = new ArrayList<DescriptionLine>();
		int lineNum = 1;
		int currentLineLen = 0;
		DescriptionLine line = new DescriptionLine();
		lineArr.add(line);
		for (int i = 0; i < desc.length(); i++) {
			char c = desc.charAt(i);
			int charWidth = g.getFontMetrics(font).charWidth(c);
			if (currentLineLen + charWidth > width) {
				lineNum++;
				currentLineLen = 0;
				line = new DescriptionLine();
				line.appendLineStr(c);
				lineArr.add(line);
				continue;
			}
			currentLineLen += charWidth;
			line.appendLineStr(c);
			line.setLineWith(currentLineLen);
		}
		int totalFontHeight = fontHeight * lineNum;// 段落高度
		int wordTopMargin = 5;// 上间距
		// 创建一张新的图片
		BufferedImage bm1 = new BufferedImage(width, height + totalFontHeight + wordTopMargin - whiteWidth + 2, BufferedImage.TYPE_INT_RGB);
		Graphics g1 = bm1.getGraphics();
		if (totalFontHeight + wordTopMargin - whiteWidth > 0) {
			g1.setColor(Color.WHITE);
			g1.fillRect(0, height, width, totalFontHeight + wordTopMargin - whiteWidth+ 2);
		}
		g1.setColor(new Color(BLACK));
		g1.setFont(font);
		g1.drawImage(bm, 0, 0, null);// 将二维码图片添加到新图片中
		// width = info.getBottomEnd()[0] - info.getBottomStart()[0];
		// height = info.getBottomEnd()[1] + 1;

		// 画出描述信息
		int baseLo = g1.getFontMetrics().getAscent();
		for (int lineIndex = 0; lineIndex < lineArr.size(); lineIndex++) {
			DescriptionLine dLine = lineArr.get(lineIndex);
			g1.drawString(dLine.getLineStr(), ((width > dLine.getLineWith()) ? (width - dLine.getLineWith()) / 2 : 0), height + baseLo + fontHeight * lineIndex + wordTopMargin);
		}
		/*
		 * int currentLineIndex =0; for (int i = 0; i < desc.length(); i++) { String c =
		 * desc.substring(i, i + 1); int charWidth =
		 * g.getFontMetrics(font).stringWidth(c); if (currentLineLen + charWidth >
		 * width) { currentLineIndex++; currentLineLen = 0; g1.drawString(c,
		 * currentLineLen + whiteWidth, height + baseLo + fontHeight *
		 * (currentLineIndex) + wordTopMargin); currentLineLen = charWidth; continue; }
		 * g1.drawString(c, currentLineLen + whiteWidth, height + baseLo + fontHeight *
		 * (currentLineIndex) + wordTopMargin); currentLineLen += charWidth; }
		 */
		g1.dispose();
		bm = bm1;
		return bm;
	}

	/**
	 * 在二维码中增加logo
	 * 
	 * @throws IOException
	 */
	private static BufferedImage addLogoImage(BufferedImage bm, CodeModel info) throws IOException {
		int width = bm.getWidth();// 二维码图片宽度
		int height = bm.getHeight();// 二维码图片高度
		BufferedImage logoImg = ImageIO.read(info.getLogoFile());
		int logoWidth = logoImg.getWidth();
		int logoHeight = logoImg.getHeight();
		float ratio = info.getLogoRatio();
		if (ratio > 0) {
			logoWidth = logoWidth > width * ratio ? (int) (width * ratio) : logoWidth;
			logoHeight = logoHeight > height * ratio ? (int) (height * ratio) : logoHeight;
		}
		int x = (width - logoWidth) / 2;
		int y = (height - logoHeight) / 2;
		Graphics g = bm.getGraphics();
		g.drawImage(logoImg, x, y, logoWidth, logoHeight, null);
		g.dispose();
		return bm;
	}

	public static void createCodeImage(CodeModel info, File file) {
		File parent = file.getParentFile();
		if (!parent.exists())
			parent.mkdirs();
		OutputStream output = null;
		try {
			output = new BufferedOutputStream(new FileOutputStream(file));
			createCodeImage(info, output);
			output.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				output.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	public void createCodeImage(CodeModel info, String filePath) {
		createCodeImage(info, new File(filePath));
	}
}