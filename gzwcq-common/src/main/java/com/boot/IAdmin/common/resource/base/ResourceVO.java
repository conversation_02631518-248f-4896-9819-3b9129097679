/*
 * 版权所有：杭州领域向尚科技有限公司
 * 地址：浙江省杭州市滨江区滨安路688号天和高科技产业园5-2311
 *
 * (c) Copyright Hangzhou  Area Up Technology Co., Ltd.
 */

package com.boot.IAdmin.common.resource.base;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 04/12/2017
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ResourceVO<T> {
    /**
     * 资源路径
     */
    private String fileUri;
    /**
     * 资源相对路径（数据库中保存的路径）
     */
    private String filePath;

    private T data;

    public ResourceVO(String fileUri, String filePath) {
        this.fileUri = fileUri;
        this.filePath = filePath;
    }
}
