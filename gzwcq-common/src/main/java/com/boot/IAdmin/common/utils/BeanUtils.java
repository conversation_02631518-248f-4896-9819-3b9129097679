package com.boot.IAdmin.common.utils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiPredicate;

import org.apache.commons.beanutils.PropertyUtilsBean;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

public class BeanUtils {

	public static List<Field> getAllFields(Object obj) {
		List<Field> fieldList = new ArrayList<>();
		Class<?> tempClass = obj.getClass();
		while (tempClass != null) {// 当父类为null的时候说明到达了最上层的父类(Object类).
			fieldList.addAll(Arrays.asList(tempClass.getDeclaredFields()));
			tempClass = tempClass.getSuperclass(); // 得到父类,然后赋给自己
		}
		return fieldList;
	}

	// 根据属性名，获取对象属性值
	public static Object getFieldVal(Object obj, String fieldName) {
		Object value = null;
		if (obj != null && StringUtils.isNotBlank(fieldName)) {
			List<Field> fieldList = getAllFields(obj);
			for (Field field : fieldList) {
				if (field.getName().equals(fieldName)) {
					field.setAccessible(true);
					try {
						value = field.get(obj);
					} catch (IllegalArgumentException e) {
						e.printStackTrace();
					} catch (IllegalAccessException e) {
						e.printStackTrace();
					}
					break;
				}
			}
		}
		return value;
	}

	// 将javabean实体类转为map类型，然后返回一个map类型的值
	public static Map<String, Object> beanToMap(Object obj) {
		Map<String, Object> params = new HashMap<String, Object>(0);
		try {
			PropertyUtilsBean propertyUtilsBean = new PropertyUtilsBean();
			PropertyDescriptor[] descriptors = propertyUtilsBean.getPropertyDescriptors(obj);
			for (int i = 0; i < descriptors.length; i++) {
				String name = descriptors[i].getName();
				if (!"class".equals(name)) {
					params.put(name, propertyUtilsBean.getNestedProperty(obj, name));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return params;
	}

	/**
	 * 比较两个对象中所有字段,返回不一致的字段名称和值
	 *
	 * @param past         原对象
	 * @param now          新对象
	 * @param ignoreFields 忽略的字段名
	 * @return 不一致的字段名称和值
	 */
	public static Map<String, Map<String, Object>> fieldsCompare(Object past, Object now, String[] ignoreFields) throws IllegalAccessException {
		List<String> ignoreFieldsList = Arrays.asList(ignoreFields);
		Map<String, Object> map;
		Map<String, Map<String, Object>> diffMap = new LinkedHashMap<>();
		if (past == null || now == null){
			return new HashMap<>(0);
		}
		Class<?> pastClz = past.getClass();
		Class<?> nowClz = now.getClass();
		Field[] pastFields = {};
		Field[] nowFields = {};
		//本类及父类所有的属性
		while (pastClz != null && nowClz != null){
			pastFields = ArrayUtils.addAll(pastFields, pastClz.getDeclaredFields());
			nowFields = ArrayUtils.addAll(nowFields, nowClz.getDeclaredFields());
			pastClz = pastClz.getSuperclass();
			nowClz = nowClz.getSuperclass();
		}
		BiPredicate<Object, Object> biPredicate = (o1, o2) -> {
			if (o1 == null && o2 == null) {
				return true;
			}
			if (o1 == null ^ o2 == null) {
				return false;
			}
			//String,Integer等
			if (o1.equals(o2)) {
				return true;
			}
			//BigDecimal
			if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
				return ((BigDecimal) o1).compareTo((BigDecimal) o2) == 0;
			}
			//Date
			if (o1 instanceof Date && o2 instanceof Date) {
				return ((Date) o1).compareTo((Date) o2) == 0;
			}
			return false;
		};
		for (Field pastField : pastFields) {
			for (Field nowField : nowFields) {
				if (ignoreFieldsList.contains(pastField.getName()) || ignoreFieldsList.contains(nowField.getName())) {
					continue;
				}
				if (pastField.getName().equals(nowField.getName())) {
					pastField.setAccessible(true);
					nowField.setAccessible(true);
					//如果字段值不相同
					if (!biPredicate.test(pastField.get(past), nowField.get(now))) {
						map = new HashMap<>(2);
						map.put("past", pastField.get(past));//上版本的字段值
						map.put("now", nowField.get(now));//此版本的字段值
						diffMap.put(pastField.getName(), map);
					}
				}
			}
		}
		return diffMap;
	}
}
