package com.boot.IAdmin.common.aop;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解用于记录系统日志
 * 
 * <AUTHOR>
 * @date 2017年9月27日 16:29:47
 * */
@Target({ElementType.METHOD})  
@Retention(RetentionPolicy.RUNTIME)  
@Documented 
public @interface AutoSystemLog {
	
	/**模块名*/
    String moduleName()  default "";
    
    /**方法名*/
    String methodName()  default "";
}