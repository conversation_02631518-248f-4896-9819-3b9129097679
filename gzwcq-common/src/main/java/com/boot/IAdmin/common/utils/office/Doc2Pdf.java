package com.boot.IAdmin.common.utils.office;

import java.io.*;

import org.apache.commons.io.IOUtils;

import com.aspose.words.*;         //引入aspose-words-15.8.0-jdk16.jar包

public class Doc2Pdf {

    public static boolean getLicense() {
        boolean result = false;
        try {
        	String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
			ByteArrayInputStream is = new ByteArrayInputStream(s.getBytes());
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static OutputStream doc2pdf(InputStream in) {
        if (!getLicense()) {          // 验证License 若不验证则转化出的pdf文档会有水印产生
            return null;
        }
        OutputStream out = new ByteArrayOutputStream();
        try {
//             long old = System.currentTimeMillis();
//            File file = new File("e:/temp/pdf1111.pdf");  //新建一个空白pdf文档
//            FileOutputStream os = new FileOutputStream(file);
            Document doc = new Document(in);                    //Address是将要被转化的word文档
            doc.save(out, SaveFormat.PDF);//全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
//            long now = System.currentTimeMillis();
//            System.out.println("共耗时：" + ((now - old) / 1000.0) + "秒");  //转化用时
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
        	IOUtils.closeQuietly(in);
        }
        return out;
    }

    /**
     * 将InputStream写入本地文件
     * @param destination 写入本地目录
     * @param input 输入流
     * @throws IOException IOException
     */
    public static void writeToLocal(String destination, InputStream input)
            throws IOException {
        int index;
        byte[] bytes = new byte[1024];
        FileOutputStream downloadFile = new FileOutputStream(destination);
        while ((index = input.read(bytes)) != -1) {
            downloadFile.write(bytes, 0, index);
            downloadFile.flush();
        }
        input.close();
        downloadFile.close();

    }

    public static void main(String[] args) throws IOException {
        File file = new File("E:/tyf/test.docx");

        ByteArrayOutputStream out = (ByteArrayOutputStream) Doc2Pdf.doc2pdf(new FileInputStream(file));
        ByteArrayInputStream swapStream = new ByteArrayInputStream(out.toByteArray());
        //生成新的pdf
        writeToLocal("E:/tyf/test.pdf",swapStream);
	}
}