package com.boot.IAdmin.common.domain;

import java.io.Serializable;
import java.util.Collection;

import com.boot.IAdmin.common.domain.dataAuth.IDataAuthSql;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * bootstrap实体类
 * */
public class BootstrapTableModel<T> extends ResponseEnvelope implements Serializable,IDataAuthSql{
	
	private static final long serialVersionUID = 353779855484349586L;

	private Collection<T> rows;//数据集合
	
	private long total;//记录数
	
	private long offset;//当前记录数
	
	private long limit;//每页显示条数
	
	@JsonIgnore
	private T obj;//查询对象
	
	@JsonIgnore
	private String whereSqlForDataAuth;//用于控制数据权限的sql

	public Collection<T> getRows() {
		return rows;
	}

	public void setRows(Collection<T> rows) {
		this.rows = rows;
	}

	public long getTotal() {
		return total;
	}

	public void setTotal(long total) {
		this.total = total;
	}

	public long getOffset() {
		return offset;
	}

	public void setOffset(long offset) {
		this.offset = offset;
	}

	public long getLimit() {
		return limit;
	}

	public void setLimit(long limit) {
		this.limit = limit;
	}
	
	@JsonIgnore
	public T getObj() {
		return obj;
	}

	public void setObj(T obj) {
		this.obj = obj;
	}
	
	/**
	 * 增加查询结果集合，并返回对象本身
	 * <P>
	 * 返回对象本身
	 * */
	public BootstrapTableModel<T> addRows(Collection<T> objs) {
		this.rows = objs;
		return this;
	}

	/**
	 * 设置查询总条数，总页数
	 * <P>
	 * 返回对象本身
	 * */
	public BootstrapTableModel<T> addTotals(long total) {
		this.total = total;
		return this;
	}

	public String getWhereSqlForDataAuth() {
		return whereSqlForDataAuth;
	}

	public void setWhereSqlForDataAuth(String whereSqlForDataAuth) {
		this.whereSqlForDataAuth = whereSqlForDataAuth;
	}
	
}
