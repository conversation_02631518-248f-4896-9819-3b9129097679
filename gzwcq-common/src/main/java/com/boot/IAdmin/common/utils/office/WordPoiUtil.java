package com.boot.IAdmin.common.utils.office;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFFooter;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.xmlbeans.XmlException;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

/**
 * 使用POI操作替换word模板内容
 * */
public class WordPoiUtil {

    /**
     * 根据模板生成新word文档
     * 判断表格是需要替换还是需要插入，判断逻辑有$为替换，表格无$为插入
     * @param input 模板存放地址输入流
     * @param outPutUrl 新文档存放地址
     * @param textMap 需要替换的信息集合
     * @param tableList 需要插入的表格信息集合
     * @return 成功返回true,失败返回false
     */
    public static boolean changWord(InputStream input, String outputUrl,
            Map<String, String> textMap, List<String[]> tableList) {
        
        //模板转换默认成功
        boolean changeFlag = true;
        try {
            //获取docx解析对象
            XWPFDocument document = new XWPFDocument(input);
//                    new XWPFDocument(POIXMLDocument.openPackage(inputUrl));
            //解析替换文本段落对象
            WordPoiUtil.changeText(document, textMap);
            WordPoiUtil.changeFooter(document,textMap);
            //解析替换表格对象
            WordPoiUtil.changeTable(document, textMap, tableList);
            
            //生成新的word
            File file = new File(outputUrl);
            FileOutputStream stream = new FileOutputStream(file);
            document.write(stream);
            stream.close();
            
        } catch (IOException e) {
            e.printStackTrace();
            changeFlag = false;
        }
        
        return changeFlag;

    }
    
    /**
     * 返回流
     * */
    public static OutputStream changWord(InputStream input,Map<String, String> textMap, List<String[]> tableList) {
        //模板转换默认成功
    	OutputStream out = new ByteArrayOutputStream();
        try {
            //获取docx解析对象
            XWPFDocument document = new XWPFDocument(input);
            //解析替换文本段落对象
            WordPoiUtil.changeText(document, textMap);
            WordPoiUtil.changeFooter(document,textMap);
            //解析替换表格对象
            WordPoiUtil.changeTable(document, textMap, tableList);
            //输出流
            document.write(out);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
        	IOUtils.closeQuietly(input);
        }
        return out;

    }

    private static void changeFooter(XWPFDocument document, Map<String, String> textMap) {
    	 //获取段落集合
        List<XWPFFooter> footers = document.getFooterList();
        
        for (XWPFFooter footer : footers) {
            //判断此段落时候需要进行替换
//            String text = footer.getText();
        	List<XWPFParagraph> paragraphs = footer.getParagraphs();
        	for (XWPFParagraph paragraph : paragraphs) {
                //判断此段落时候需要进行替换
                String text = paragraph.getText();
                if(checkText(text)){
                    List<XWPFRun> runs = paragraph.getRuns();
                    for (XWPFRun run : runs) {
                        //替换模板原来位置
                        run.setText(changeValue(run.toString(), textMap),0);
                    }
                }
            }
        }
	}

	/**
     * 替换段落文本
     * @param document docx解析对象
     * @param textMap 需要替换的信息集合
     */
    public static void changeText(XWPFDocument document, Map<String, String> textMap){
        //获取段落集合
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        
        for (XWPFParagraph paragraph : paragraphs) {
            //判断此段落时候需要进行替换
            String text = paragraph.getText();
            if(checkText(text)){
                List<XWPFRun> runs = paragraph.getRuns();
                for (XWPFRun run : runs) {
                    //替换模板原来位置
                    run.setText(changeValue(run.toString(), textMap),0);
                }
            }
        }
        
    }
    
    /**
     * 替换表格对象方法
     * @param document docx解析对象
     * @param textMap 需要替换的信息集合
     * @param tableList 需要插入的表格信息集合
     */
    public static void changeTable(XWPFDocument document, Map<String, String> textMap,
            List<String[]> tableList){
        //获取表格对象集合
        List<XWPFTable> tables = document.getTables();
        for (int i = 0; i < tables.size(); i++) {
            //只处理行数大于等于2的表格，且不循环表头
            XWPFTable table = tables.get(i);
            if(table.getRows().size()>1){
                //判断表格是需要替换还是需要插入，判断逻辑有$为替换，表格无$为插入
                if(checkText(table.getText())){
                    List<XWPFTableRow> rows = table.getRows();
                    //遍历表格,并替换模板
                    eachTable(rows, textMap);
                }else{
//                  System.out.println("插入"+table.getText());
//                    insertTable(table, tableList);
                    try {
						insertValueToTable(table, tableList,true);
					} catch (Exception e) {
						e.printStackTrace();
					}
                }
            }
        }
    }
    
    /**
     * 遍历表格
     * @param rows 表格行对象
     * @param textMap 需要替换的信息集合
     */
    public static void eachTable(List<XWPFTableRow> rows ,Map<String, String> textMap){
        for (XWPFTableRow row : rows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                //判断单元格是否需要替换
                if(checkText(cell.getText())){
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    for (XWPFParagraph paragraph : paragraphs) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        for (XWPFRun run : runs) {
                            run.setText(changeValue(run.toString(), textMap),0);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 为表格插入数据，行数不够添加新行
     * @param table 需要插入数据的表格
     * @param tableList 插入数据集合
     */
    public static void insertTable(XWPFTable table, List<String[]> tableList){
        //创建行,根据需要插入的数据添加新行，不处理表头
        for(int i = 1; i < tableList.size(); i++){
            table.createRow();
        }
        //遍历表格插入数据
        List<XWPFTableRow> rows = table.getRows();
        for(int i = 1; i < rows.size(); i++){
            XWPFTableRow newRow = table.getRow(i);
            List<XWPFTableCell> cells = newRow.getTableCells();
            for(int j = 0; j < cells.size(); j++){
                XWPFTableCell cell = cells.get(j);
                cell.setText(tableList.get(i-1)[j]);
            }
        }
        
    }
    
    /**
     * @Description: 按模版行样式填充数据,暂未实现特殊样式填充(如列合并)，只能用于普通样式(如段落间距 缩进 字体 对齐)
     * @param resultList  填充数据
//     * @param tableRowSize  模版表格行数 取第一个行数相等列数相等的表格填充
     * @param isDelTmpRow 是否删除模版行
     */
    public static void insertValueToTable(XWPFTable table,
            List<String[]> resultList/*, int tableRowSize*/, boolean isDelTmpRow)
            throws Exception {
        List<XWPFTableRow> rows = null;
        List<XWPFTableCell> cells = null;
        List<XWPFTableCell> tmpCells = null;// 模版列
        XWPFTableRow tmpRow = null;// 匹配用
        XWPFTableCell tmpCell = null;// 匹配用
        
        rows = table.getRows();
        tmpRow = rows.get(1);//模板行
        cells = tmpRow.getTableCells();
        tmpCells = tmpRow.getTableCells();
        
        for (int i = 0, len = resultList.size(); i < len; i++) {
            XWPFTableRow row = table.createRow();
            row.setHeight(tmpRow.getHeight());
            String[] list = resultList.get(i);
            cells = row.getTableCells();
            // 插入的行会填充与表格第一行相同的列数
            for (int k = 0/*, klen = cells.size()*/; k < cells.size(); k++) {
                tmpCell = tmpCells.get(k);
                XWPFTableCell cell = cells.get(k);
                setCellText(tmpCell, cell, list[k]);
            }
            // 继续写剩余的列
            /*for (int j = cells.size(), jlen = list.length; j < jlen; j++) {
                tmpCell = tmpCells.get(j);
                XWPFTableCell cell = row.addNewTableCell();
                setCellText(tmpCell, cell, list[j]);
            }*/
        }
        // 删除模版行
        if (isDelTmpRow) table.removeRow(1);
    }
    
    public static void setCellText(XWPFTableCell tmpCell, XWPFTableCell cell,
            String text) throws Exception {
        CTTc cttc2 = tmpCell.getCTTc();
        CTTcPr ctPr2 = cttc2.getTcPr();

        CTTc cttc = cell.getCTTc();
        CTTcPr ctPr = cttc.addNewTcPr();
        cell.setColor(tmpCell.getColor());
        cell.setVerticalAlignment(tmpCell.getVerticalAlignment());
        if (ctPr2.getTcW() != null) {
            ctPr.addNewTcW().setW(ctPr2.getTcW().getW());
        }
        if (ctPr2.getVAlign() != null) {
            ctPr.addNewVAlign().setVal(ctPr2.getVAlign().getVal());
        }
        if (cttc2.getPList().size() > 0) {
            CTP ctp = cttc2.getPList().get(0);
            if (ctp.getPPr() != null) {
                if (ctp.getPPr().getJc() != null) {
                    cttc.getPList().get(0).addNewPPr().addNewJc().setVal(
                            ctp.getPPr().getJc().getVal());
                }
            }
        }

        if (ctPr2.getTcBorders() != null) {
            ctPr.setTcBorders(ctPr2.getTcBorders());
        }

        XWPFParagraph tmpP = tmpCell.getParagraphs().get(0);
        XWPFParagraph cellP = cell.getParagraphs().get(0);
        XWPFRun tmpR = null;
        if (tmpP.getRuns() != null && tmpP.getRuns().size() > 0) {
            tmpR = tmpP.getRuns().get(0);
        }
        XWPFRun cellR = cellP.createRun();
        cellR.setText(text);
        // 复制字体信息
        if (tmpR != null) {
            cellR.setBold(tmpR.isBold());
            cellR.setItalic(tmpR.isItalic());
            cellR.setStrikeThrough(tmpR.isStrikeThrough());
            cellR.setUnderline(tmpR.getUnderline());
            cellR.setColor(tmpR.getColor());
            cellR.setTextPosition(tmpR.getTextPosition());
            if (tmpR.getFontSize() != -1) {
                cellR.setFontSize(tmpR.getFontSize());
            }
            if (tmpR.getFontFamily() != null) {
                cellR.setFontFamily(tmpR.getFontFamily());
            }
            if (tmpR.getCTR() != null) {
                if (tmpR.getCTR().isSetRPr()) {
                    CTRPr tmpRPr = tmpR.getCTR().getRPr();
                    if (tmpRPr.isSetRFonts()) {
                        CTFonts tmpFonts = tmpRPr.getRFonts();
                        CTRPr cellRPr = cellR.getCTR().isSetRPr() ? cellR
                                .getCTR().getRPr() : cellR.getCTR().addNewRPr();
                        CTFonts cellFonts = cellRPr.isSetRFonts() ? cellRPr
                                .getRFonts() : cellRPr.addNewRFonts();
                        cellFonts.setAscii(tmpFonts.getAscii());
                        cellFonts.setAsciiTheme(tmpFonts.getAsciiTheme());
                        cellFonts.setCs(tmpFonts.getCs());
                        cellFonts.setCstheme(tmpFonts.getCstheme());
                        cellFonts.setEastAsia(tmpFonts.getEastAsia());
                        cellFonts.setEastAsiaTheme(tmpFonts.getEastAsiaTheme());
                        cellFonts.setHAnsi(tmpFonts.getHAnsi());
                        cellFonts.setHAnsiTheme(tmpFonts.getHAnsiTheme());
                    }
                }
            }
        }
        // 复制段落信息
        cellP.setAlignment(tmpP.getAlignment());
        cellP.setVerticalAlignment(tmpP.getVerticalAlignment());
        cellP.setBorderBetween(tmpP.getBorderBetween());
        cellP.setBorderBottom(tmpP.getBorderBottom());
        cellP.setBorderLeft(tmpP.getBorderLeft());
        cellP.setBorderRight(tmpP.getBorderRight());
        cellP.setBorderTop(tmpP.getBorderTop());
        cellP.setPageBreak(tmpP.isPageBreak());
        if (tmpP.getCTP() != null) {
            if (tmpP.getCTP().getPPr() != null) {
                CTPPr tmpPPr = tmpP.getCTP().getPPr();
                CTPPr cellPPr = cellP.getCTP().getPPr() != null ? cellP
                        .getCTP().getPPr() : cellP.getCTP().addNewPPr();
                // 复制段落间距信息
                CTSpacing tmpSpacing = tmpPPr.getSpacing();
                if (tmpSpacing != null) {
                    CTSpacing cellSpacing = cellPPr.getSpacing() != null ? cellPPr
                            .getSpacing()
                            : cellPPr.addNewSpacing();
                    if (tmpSpacing.getAfter() != null) {
                        cellSpacing.setAfter(tmpSpacing.getAfter());
                    }
                    if (tmpSpacing.getAfterAutospacing() != null) {
                        cellSpacing.setAfterAutospacing(tmpSpacing
                                .getAfterAutospacing());
                    }
                    if (tmpSpacing.getAfterLines() != null) {
                        cellSpacing.setAfterLines(tmpSpacing.getAfterLines());
                    }
                    if (tmpSpacing.getBefore() != null) {
                        cellSpacing.setBefore(tmpSpacing.getBefore());
                    }
                    if (tmpSpacing.getBeforeAutospacing() != null) {
                        cellSpacing.setBeforeAutospacing(tmpSpacing
                                .getBeforeAutospacing());
                    }
                    if (tmpSpacing.getBeforeLines() != null) {
                        cellSpacing.setBeforeLines(tmpSpacing.getBeforeLines());
                    }
                    if (tmpSpacing.getLine() != null) {
                        cellSpacing.setLine(tmpSpacing.getLine());
                    }
                    if (tmpSpacing.getLineRule() != null) {
                        cellSpacing.setLineRule(tmpSpacing.getLineRule());
                    }
                }
                // 复制段落缩进信息
                CTInd tmpInd = tmpPPr.getInd();
                if (tmpInd != null) {
                    CTInd cellInd = cellPPr.getInd() != null ? cellPPr.getInd()
                            : cellPPr.addNewInd();
                    if (tmpInd.getFirstLine() != null) {
                        cellInd.setFirstLine(tmpInd.getFirstLine());
                    }
                    if (tmpInd.getFirstLineChars() != null) {
                        cellInd.setFirstLineChars(tmpInd.getFirstLineChars());
                    }
                    if (tmpInd.getHanging() != null) {
                        cellInd.setHanging(tmpInd.getHanging());
                    }
                    if (tmpInd.getHangingChars() != null) {
                        cellInd.setHangingChars(tmpInd.getHangingChars());
                    }
                    if (tmpInd.getLeft() != null) {
                        cellInd.setLeft(tmpInd.getLeft());
                    }
                    if (tmpInd.getLeftChars() != null) {
                        cellInd.setLeftChars(tmpInd.getLeftChars());
                    }
                    if (tmpInd.getRight() != null) {
                        cellInd.setRight(tmpInd.getRight());
                    }
                    if (tmpInd.getRightChars() != null) {
                        cellInd.setRightChars(tmpInd.getRightChars());
                    }
                }
            }
        }
    }
    
    /**
     * 判断文本中时候包含$
     * @param text 文本
     * @return 包含返回true,不包含返回false
     */
    public static boolean checkText(String text){
        boolean check  =  false;
        if(text.indexOf("$")!= -1){
            check = true;
        }
        return check;
        
    }
    
    /**
     * 匹配传入信息集合与模板
     * @param value 模板需要替换的区域
     * @param textMap 传入信息集合
     * @return 模板需要替换区域信息集合对应值
     */
    public static String changeValue(String value, Map<String, String> textMap){
        Set<Entry<String, String>> textSets = textMap.entrySet();
        for (Entry<String, String> textSet : textSets) {
            //匹配模板与替换值 格式${key}
            String key = "${"+textSet.getKey()+"}";
            if(value.indexOf(key)!= -1){
                value = textSet.getValue();
                break;
            }
        }
        if(StringUtils.isBlank(value)) return "";
        //模板未匹配到区域替换为空
        if(checkText(value)){
            value = "";
        }
        return value;
    }

    /**
     * 根据模板生成新word文档
     * 判断表格是需要替换还是需要插入，判断逻辑有$为替换，表格无$为插入
     * @param is 模板输入流
     * @param outputUrl 新文档存放地址
     * @param tableDataList 需要替换的信息集合
     * @param startListRow 列表开始循环行 (作为列表第一行)
     * @param listNum 循环列表行数
     * @return 成功返回true,失败返回false
     */
    public static boolean changWord(InputStream is, String outputUrl,Map<String, String> textMap,
                                    List<Map<String, String>> tableDataList,int startListRow,int listNum) {

        //模板转换默认成功
        boolean changeFlag = true;
        try {
            //获取docx解析对象
//            XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(inputUrl));
            XWPFDocument document = new XWPFDocument(is);
            //解析替换文本段落对象
            WordPoiUtil.changeText(document, textMap);
//            WordPoiUtil.changeFooter(document,textMap);
            //解析替换表格对象
            WordPoiUtil.changeTableHasList(document, tableDataList,startListRow,listNum);

            //生成新的word
            File file = new File(outputUrl);
            FileOutputStream stream = new FileOutputStream(file);
            document.write(stream);
            stream.close();

        } catch (IOException | XmlException e) {
            e.printStackTrace();
            changeFlag = false;
        }

        return changeFlag;

    }

    /**
     * 替换表格对象方法(指定值开始列循环创建行)
     * @param document docx解析对象
     * @param tableDataList 需要插入的表格信息集合
     * @param startListRow 列表开始循环行 (作为列表第一行)
     * @param listNum 循环列表行数
     */
    public static void changeTableHasList(XWPFDocument document,
                                    List<Map<String, String>> tableDataList,int startListRow,int listNum) throws IOException, XmlException {
        //获取表格对象集合
        List<XWPFTable> tables = document.getTables();
        for (int i = 0; i < tables.size(); i++) {
            //只处理行数大于等于2的表格，且不循环表头
            XWPFTable table = tables.get(i);
            if(table.getRows().size()>1){
                //判断表格是需要替换还是需要插入，判断逻辑有$为替换，表格无$为插入
                if(checkText(table.getText())){
                    List<XWPFTableRow> rows = table.getRows();
//                    XWPFTableRow startRow = rows.get(startListRow-1);
                    //按列表数据增加相应行
                    for(int n = startListRow; n < listNum+startListRow-1; n++){
//                        CTRow ctrow = CTRow.Factory.parse(startRow.getCtRow().newInputStream());//重点行
//                        XWPFTableRow newRow = new XWPFTableRow(ctrow, table);
//                        table.addRow(newRow,n);
                        insertRow(table,startListRow-1,n);
                    }
                    //遍历表格,并替换模板
                    eachTableHasList(rows, tableDataList);
                    for(int k = 0; k < rows.size(); k++){
                        for(int j = 0; j < rows.get(k).getTableCells().size(); j++){
                            System.out.print(rows.get(k).getTableCells().get(j).getText());
                        }
                        System.out.println("--");
                    }
                }
            }
        }
    }

    /**
     * insertRow 在word表格中指定位置插入一行，并将某一行的样式复制到新增行
     * @param copyrowIndex 需要复制的行位置
     * @param newrowIndex 需要新增一行的位置
     * */

    public static void insertRow(XWPFTable table, int copyrowIndex, int newrowIndex) {
        // 在表格中指定的位置新增一行
        XWPFTableRow targetRow = table.insertNewTableRow(newrowIndex);
        // 获取需要复制行对象
        XWPFTableRow copyRow = table.getRow(copyrowIndex);
        //复制行对象
        targetRow.getCtRow().setTrPr(copyRow.getCtRow().getTrPr());
        //或许需要复制的行的列
        List<XWPFTableCell> copyCells = copyRow.getTableCells();
        //复制列对象
        XWPFTableCell targetCell = null;
        for (int i = 0; i < copyCells.size(); i++) {
            XWPFTableCell copyCell = copyCells.get(i);
            targetCell = targetRow.addNewTableCell();
            targetCell.getCTTc().setTcPr(copyCell.getCTTc().getTcPr());
            if (copyCell.getParagraphs() != null && copyCell.getParagraphs().size() > 0) {
                targetCell.getParagraphs().get(0).getCTP().setPPr(copyCell.getParagraphs().get(0).getCTP().getPPr());
                if (copyCell.getParagraphs().get(0).getRuns() != null
                        && copyCell.getParagraphs().get(0).getRuns().size() > 0) {
                    XWPFRun cellR = targetCell.getParagraphs().get(0).createRun();
                    cellR.setBold(copyCell.getParagraphs().get(0).getRuns().get(0).isBold());
                    cellR.setText(copyCell.getParagraphs().get(0).getRuns().get(0).toString());
                }
            }
        }
    }

    /**
     * 遍历表格
     * @param rows 表格行对象
     * @param tableDataList 需要替换的信息集合
     */
    public static void eachTableHasList(List<XWPFTableRow> rows ,List<Map<String, String>> tableDataList){
        for (int i = 0; i < rows.size(); i++) {
            XWPFTableRow row = rows.get(i);
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                //判断单元格是否需要替换
                if(checkText(cell.getText())){
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    for (XWPFParagraph paragraph : paragraphs) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        for (XWPFRun run : runs) {
                            run.setText(changeValue(run.toString(), tableDataList.get(i)),0);
                        }
                    }
                }
            }
        }
    }
    
    
    
    public static void main(String[] args) throws FileNotFoundException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //模板文件地址
        String inputUrl = "E:/tyf/gs/个人需求/无人机/登记表模板.docx";
        //新生产的模板文件
        String outputUrl = "E:/tyf/test.docx";

        List<Map<String, String>> tableDataList = new ArrayList<>();

        Map<String, String> map1 = new HashMap<String, String>();
        map1.put("qymc", "浙江海航科技有限公司");
        tableDataList.add(map1);
        Map<String, String> map2 = new HashMap<String, String>();
        map2.put("gjczqy", "浙江国资委");
        map2.put("qyjc", "2");
        tableDataList.add(map2);
        Map<String, String> map3 = new HashMap<String, String>();
        map3.put("zcdd", "浙江省");
        map3.put("zcrq", sdf.format(new Date()));
        tableDataList.add(map3);
        Map<String, String> map4 = new HashMap<String, String>();
        map4.put("zczb", "123");
        map4.put("zzxs", "123");
        tableDataList.add(map4);
        Map<String, String> map5 = new HashMap<String, String>();
        tableDataList.add(map5);
        for(int i = 0; i < 10; i++){
            Map<String, String> mapList = new HashMap<String, String>();
            mapList.put("rownum", String.valueOf(i));
            mapList.put("czrmc", "c1");
            mapList.put("sjzb", "3");
            mapList.put("rjzb", "3");
            mapList.put("gqbl", "3");
            tableDataList.add(mapList);
        }
        Map<String, String> map7 = new HashMap<String, String>();
        map7.put("sjzbSum", "3");
        map7.put("rjzbSum", "3");
        map7.put("gqblSum", "3");
        tableDataList.add(map7);

        FileInputStream input = new FileInputStream(inputUrl);

        WordPoiUtil.changWord(input, outputUrl, null,tableDataList,6,10);
    }
}