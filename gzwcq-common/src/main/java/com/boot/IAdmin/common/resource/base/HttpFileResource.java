/*
 * 版权所有：杭州领域向尚科技有限公司
 * 地址：浙江省杭州市滨江区滨安路688号天和高科技产业园5-2311
 *
 * (c) Copyright Hangzhou  Area Up Technology Co., Ltd.
 */

package com.boot.IAdmin.common.resource.base;

import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.WritableResource;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;

/**
 * 基于http协议的文件资源
 *
 * <AUTHOR>
 * @date 04/12/2017
 */
public class HttpFileResource extends AbstractResource implements WritableResource {
    public final URI fileUri;

    public HttpFileResource(URI fileUri) {
        this.fileUri = fileUri;
    }

    @Override
    public URI getURI() throws IOException {
        return fileUri;
    }

    @Override
    public boolean isWritable() {
        return false;
    }

    @Override
    public OutputStream getOutputStream() throws IOException {
        return null;
    }

    @Override
    public String getDescription() {
        return null;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return null;
    }
}
