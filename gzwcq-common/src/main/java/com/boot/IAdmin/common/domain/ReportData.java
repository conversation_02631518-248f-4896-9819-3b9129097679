package com.boot.IAdmin.common.domain;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.boot.IAdmin.common.domain.echarts.Series;

public class ReportData implements Serializable {

	private static final long serialVersionUID = 1L;

	public List<Object> categories = new ArrayList<Object>();// 类别

	public List<Series> series = new ArrayList<Series>();// 系列
	
	public List<Object> getCategories() {
		return categories;
	}

	public void setCategories(List<Object> categories) {
		this.categories = categories;
	}

	public List<Series> getSeries() {
		return series;
	}

	public void setSeries(List<Series> series) {
		this.series = series;
	}
}
