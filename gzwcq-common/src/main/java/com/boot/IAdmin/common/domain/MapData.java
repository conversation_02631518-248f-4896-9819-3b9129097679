package com.boot.IAdmin.common.domain;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MapData {

    //字典值
    public final static Map<String,Map<String,String>> dicMap = new HashMap<String,Map<String,String>>(){{
        put("cico_project_report_list",new HashMap<String,String>(){{
            put("project_repoet","DIC_PROJECTLIST");
        }});
        put("cico_sales_contract",new HashMap<String,String>(){{
            put("rela","DIC_IS");
            put("Inside_or_outside","DIC_INSIDEOROUTSIDE");
            put("source","DIC_SOURCE");
        }});
        put("cico_sales_contract_items",new HashMap<String,String>(){{
            put("is_optimization","DIC_IS");
            put("is_header","DIC_IS");
            put("is_modify","DIC_IS");
        }});
        put("cico_sales_contract_buy",new HashMap<String,String>(){{
            put("main_material","DIC_MAIN_MATERIAL");
            put("signed","DIC_BUY_CONTRACT_STATUS");
        }});
    }};

    //时间格式
    public final static Map<String,Map<String,String>> timeMap = new HashMap<String,Map<String,String>>(){{
        put("cico_project",new HashMap<String,String>(){{
            put("start_time","yyyy-MM-dd");
            put("finish_time","yyyy-MM-dd");
            put("over_time","yyyy-MM-dd");
            put("complete_time","yyyy-MM-dd");
            put("maintain_start_time","yyyy-MM-dd");
            put("maintain_end_time","yyyy-MM-dd");
            put("zy_time","yyyy-MM-dd");
            put("buy_contract_complete_time","yyyy-MM-dd");
        }});
        put("cico_sales_contract",new HashMap<String,String>(){{
            put("sign_time","yyyy-MM-dd");
        }});
        put("cico_sales_contract_install",new HashMap<String,String>(){{
            put("instal_time_start","yyyy-MM-dd");
            put("install_month","yyyy-MM");
            put("instal_time_end","yyyy-MM-dd");
        }});
        put("cico_sales_contract_stock",new HashMap<String,String>(){{
            put("in_time","yyyy-MM-dd");
            put("in_month","yyyy-MM");
            put("examine_time","yyyy-MM-dd");
        }});
        put("cico_sales_contract_quality",new HashMap<String,String>(){{
            put("install_time_start","yyyy-MM-dd");
            put("quality_time","yyyy-MM-dd");
            put("credit_time","yyyy-MM-dd");
            put("install_time_end","yyyy-MM-dd");
            put("quality_time_start","yyyy-MM-dd");
            put("quality_time_end","yyyy-MM-dd");
            put("month","yyyy-MM");
        }});
    }};

    //需要转换的组织
    public final static Map<String,Map<String,String>> orgMap = new HashMap<String,Map<String,String>>(){{
        put("cico_project_summary2",new HashMap<String,String>(){{
            put("org_name","1");
        }});
        put("cico_sales_contract",new HashMap<String,String>(){{
            put("org_id","1");
        }});
    }};

    //需要转省份的字段
    public final static Map<String, List<String>> provincesMap = new HashMap<String,List<String>>(){{
        put("cico_sales_contract", new ArrayList<String>() {{
            add("province");
        }});
    }};

    //可删除的字段
    public final static Map<String, List<String>> delMap = new HashMap<String,List<String>>(){{
       put("cico_sales_contract",new ArrayList<String>(){{
           add("sale_contract_no");
           add("customer_code");
           add("attachments");
       }});
        put("cico_b_sales_contract_items",new ArrayList<String>(){{
            add("is_header");
        }});
       put("cico_sales_contract_buy",new ArrayList<String>(){{
           add("sale_contract_no");
           add("buy_contract_no");
           add("material_no");
           add("equip_num");
       }});
       put("cico_sales_contract_construction",new ArrayList<String>(){{
           add("sale_contract_no");
           add("s_contract_no");
           add("supplier_no");
       }});
       put("cico_sales_contract_install",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_items",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_items_final",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_list",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_measure",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_outside_construction",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_outside_material",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_quality",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
       put("cico_sales_contract_summary",new ArrayList<String>(){{
           add("sale_contract_no");
       }});
    }};



}
