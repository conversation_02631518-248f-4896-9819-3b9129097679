package com.boot.IAdmin.common.domain.enumpkg;

import com.boot.IAdmin.common.domain.enumpkg.IBaseEnum;

/**
 * iot枚举类集
 */
public interface CrmEnum {

    /**
     * 根据枚举值得到枚举类型
     *
     * @param cls
     * @param code
     * @param <T>
     * @return
     */
    public static <T extends IBaseEnum> T getEnumTypeByCode(Class<T> cls, Object code) {
        if (code == null) {
            return null;
        }
        for (T item : cls.getEnumConstants()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据枚举类型得到枚举值
     * @param cls
     * @param desc
     * @param <T>
     * @return
     */
    public static <T extends IBaseEnum> T getEnumTypeByDesc(Class<T> cls, Object desc) {
        if (desc == null) {
            return null;
        }
        for (T item : cls.getEnumConstants()) {
            if (item.getDesc().equals(desc)) {
                return item;
            }
        }
        return null;
    }


    enum businessClass implements IBaseEnum{
        /**
         * 商机报备
         */
        class_1("1001","com.zjhcsoft.crm.business.service.impl.IBusinessServiceImpl"),
        /**
         * 项目立项
         */
        class_2("1002","com.zjhcsoft.crm.businessTenderProject.service.impl.IBusinessTenderProjectServiceImpl"),
        /**
         * 投标会审
         */
        class_3("1003","com.zjhcsoft.crm.businessTenderCheckup.service.impl.IBusinessTenderCheckupServiceImpl"),
        /**
         * 项目安排
         */
        class_4("1004","com.zjhcsoft.crm.businessProjectArrangement.service.impl.IBusinessProjectArrangementServiceImpl"),
        /**
         * 设备选型
         */
        class_5("1005","com.zjhcsoft.crm.businessEquipmentSelection.service.impl.IBusinessEquipmentSelectionServiceImpl"),
        /**
         * 成本预估
         */
        class_6("1006","com.zjhcsoft.crm.businessCostEstimate.service.impl.IBusinessCostEstimateServiceImpl"),
        /**
         * 信用评价
         */
        class_7("1007","com.zjhcsoft.crm.businessCreditRating.service.impl.IBusinessCreditRatingServiceImpl"),
        /**
         * 商务技术标
         */
        class_8("1008","com.zjhcsoft.crm.businessTechnology.service.impl.IBusinessTechnologyServiceImpl"),
        /**
         * 开标信息
         */
        class_9("1009","com.zjhcsoft.crm.businessBidInfo.service.impl.IBusinessBidInfoServiceImpl"),
        /**
         * 中标信息
         */
        class_10("1010","com.zjhcsoft.crm.businessBidSuccessfulInfo.service.impl.IBusinessBidSuccessfulInfoServiceImpl"),
        /**
         * 团队打分
         */
        class_11("1011","com.zjhcsoft.crm.businessTeamScore.service.impl.IBusinessTeamScoreServiceImpl"),
        /**
         * 项目交接
         */
        class_12("1012","com.zjhcsoft.crm.technicalClarification.service.impl.TechnicalClarificationServiceImplImpl");
        /**
         * 商机阶段
         */
        private String code;
        /**
         * 类
         */
        private String desc;

        @Override
        public String getCode() {
            return this.code;
        }

        @Override
        public String getDesc() {
            return this.desc;
        }

        businessClass(String code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }

    enum businessStatus implements IBaseEnum{
        /**
         * 前期商机报备
         */
        class_1("0","前期-商机报备"),
        /**
         * 前期项目立项
         */
        class_2("1","前期-项目立项"),
        /**
         * 投标
         */
        class_3("2","投标"),
        /**
         * 中标
         */
        class_4("4","成交-中标");
        /**
         * 值
         */
        private String code;
        /**
         * text
         */
        private String desc;

        @Override
        public String getCode() {
            return this.code;
        }

        @Override
        public String getDesc() {
            return this.desc;
        }

        businessStatus(String code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }

    enum fromEnum implements IBaseEnum{
        /**
         * 前期商机报备
         */
        class_1("1","crm_clue_customize_field","clue_id"),
        /**
         * 前期项目立项
         */
        class_2("2","crm_customer_customize_field","customer_id"),
        /**
         * 投标
         */
        class_3("3","crm_business_customize_field","business_id"),
        /**
         * 中标
         */
        class_4("4","crm_contact_customize_field","contact_id");
        /**
         * 值）
         */
        private String code;
        /**
         * text（表名）
         */
        private String desc;
        /**
         * 关联字段
         */
        private String value;

        @Override
        public String getCode() {
            return this.code;
        }

        @Override
        public String getDesc() {
            return this.desc;
        }
        public String getValue() {
            return this.value;
        }

        fromEnum(String code, String desc,String value){
            this.code = code;
            this.desc = desc;
            this.value = value;
        }
    }

    /**
     * 基础数据状态
     */
    enum jbxxbDataStatusEnum implements IBaseEnum{
        /**
         * 前期商机报备
         */
        old_status("1","老数据","old"),
        /**
         * 前期项目立项
         */
        new_status("2","新数据","new"),
        ;
        /**
         * 值）
         */
        private String code;
        /**
         * text（表名）
         */
        private String desc;
        /**
         * 关联字段
         */
        private String value;

        @Override
        public String getCode() {
            return this.code;
        }

        @Override
        public String getDesc() {
            return this.desc;
        }
        public String getValue() {
            return this.value;
        }

        jbxxbDataStatusEnum(String code, String desc,String value){
            this.code = code;
            this.desc = desc;
            this.value = value;
        }
    }


}
