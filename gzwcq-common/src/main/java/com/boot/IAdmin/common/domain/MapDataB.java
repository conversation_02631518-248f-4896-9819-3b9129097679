package com.boot.IAdmin.common.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MapDataB {

    //字典值
    public final static Map<String,Map<String,String>> dicMap = new HashMap<String,Map<String,String>>(){{
        put("cico_b_project_report_list",new HashMap<String,String>(){{
            put("project_repoet","DIC_B_PROJECTLIST");
        }});
        put("cico_b_sales_contract",new HashMap<String,String>(){{
            put("source","DIC_SOURCE");
            put("business_unit","DIC_BUSINESS_UNIT");
        }});
        put("cico_b_sales_contract_buy",new HashMap<String,String>(){{
            put("signed","DIC_B_BUY_CONTRACT_STATUS");
        }});
        put("cico_b_sales_contract_cost",new HashMap<String,String>(){{
            put("signed","YesNo");
        }});
        put("cico_b_sales_contract_in",new HashMap<String,String>(){{
            put("signed","YesNo");
        }});
        put("cico_b_sales_contract_items",new HashMap<String,String>(){{
            put("is_header","DIC_IS");
        }});
        put("cico_b_sales_contract_out",new HashMap<String,String>(){{
            put("signed","YesNo");
        }});
        put("cico_b_sales_contract_service",new HashMap<String,String>(){{
            put("signed","YesNo");
        }});
        put("cico_b_sales_contract_service_final",new HashMap<String,String>(){{
            put("signed","YesNo");
        }});
        put("cico_b_sales_contract_sub",new HashMap<String,String>(){{
            put("rela","YesNo");
        }});
    }};

    //时间格式
    public final static Map<String,Map<String,String>> timeMap = new HashMap<String,Map<String,String>>(){{
        put("cico_b_project",new HashMap<String,String>(){{
            put("start_time","yyyy-MM-dd");
            put("finish_time","yyyy-MM-dd");
            put("over_time","yyyy-MM-dd");
            put("complete_time","yyyy-MM-dd");
            put("maintain_start_time","yyyy-MM-dd");
            put("maintain_end_time","yyyy-MM-dd");
            put("zy_time","yyyy-MM-dd");
            put("zb_time","yyyy-MM-dd");
            put("cy_time","yyyy-MM-dd");
            put("buy_contract_complete_time","yyyy-MM-dd");
        }});
        put("cico_b_sales_contract",new HashMap<String,String>(){{
            put("sign_time","yyyy-MM-dd");
        }});
        put("cico_b_sales_contract_in",new HashMap<String,String>(){{
            put("in_time","yyyy-MM-dd");
        }});
        put("cico_b_sales_contract_invoice",new HashMap<String,String>(){{
            put("Invoice_time","yyyy-MM-dd");
            put("back_time","yyyy-MM-dd");
        }});
        put("cico_b_sales_contract_list",new HashMap<String,String>(){{
            put("last_report_time","yyyy-MM-dd HH:mm:ss");
        }});
        put("cico_b_sales_contract_out",new HashMap<String,String>(){{
            put("out_time","yyyy-MM-dd");
        }});
    }};

    //需要转换的组织
    public final static Map<String,Map<String,String>> orgMap = new HashMap<String,Map<String,String>>(){{
        put("cico_b_sales_contract",new HashMap<String,String>(){{
            put("org_id","1");
        }});
        put("cico_sales_contract",new HashMap<String,String>(){{
            put("org_id","1");
        }});
    }};

    //可删除的字段
    public final static Map<String, List<String>> delMap = new HashMap<String,List<String>>(){{
       put("cico_b_sales_contract",new ArrayList<String>(){{
           add("attachments");
           add("sale_contract_no");
           add("customer_code");
       }});
        put("cico_b_sales_contract_items",new ArrayList<String>(){{
            add("is_header");
        }});
    }};

}
