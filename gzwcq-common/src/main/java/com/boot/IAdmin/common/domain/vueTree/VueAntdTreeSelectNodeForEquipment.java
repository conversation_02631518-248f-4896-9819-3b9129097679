package com.boot.IAdmin.common.domain.vueTree;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Antd下拉框树节点
 * */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VueAntdTreeSelectNodeForEquipment {
	
	private String key;//同值
	
	private String value;//值
	
	private String title;//文本
	
	private boolean selectable = true;
	
	private boolean disableCheckbox;
	
	private boolean disabled;
	
	private boolean isLeaf = true;
	
	private String id;//节点ID
	
	private String parent;//父节点Id
	
	private List<VueAntdTreeSelectNodeForEquipment> children;//子节点
	
	public VueAntdTreeSelectNodeForEquipment(){}
	
	public VueAntdTreeSelectNodeForEquipment(List<VueAntdTreeSelectNodeForEquipment> vueTreeNodeArr, String rootId, String rootLabel, boolean selectable){
		this.setId(rootId);
		this.setDisabled(true);
		this.setValue(rootId);
		this.setKey(rootId);
		this.setSelectable(selectable);
		this.setTitle(rootLabel);
		bulidAntdTree(vueTreeNodeArr,this);
	}
	
	public VueAntdTreeSelectNodeForEquipment(String key, String value, String title) {
		super();
		this.key = key;
		this.value = value;
		this.title = title;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public boolean isSelectable() {
		return selectable;
	}

	public void setSelectable(boolean selectable) {
		this.selectable = selectable;
	}

	public boolean isDisableCheckbox() {
		return disableCheckbox;
	}

	public void setDisableCheckbox(boolean disableCheckbox) {
		this.disableCheckbox = disableCheckbox;
	}

	public boolean isDisabled() {
		return disabled;
	}

	public void setDisabled(boolean disabled) {
		this.disabled = disabled;
	}

	public boolean getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(boolean isLeaf) {
		this.isLeaf = isLeaf;
	}
	
	public List<VueAntdTreeSelectNodeForEquipment> getChildren() {
		return children;
	}

	public void setChildren(List<VueAntdTreeSelectNodeForEquipment> children) {
		this.children = children;
	}
	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getParent() {
		return parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}

	/**
	 * 构建下拉框树
	 * */
	private void bulidAntdTree(List<VueAntdTreeSelectNodeForEquipment> vueSelectTreeNodeArr,VueAntdTreeSelectNodeForEquipment parentNode) {
		for(VueAntdTreeSelectNodeForEquipment treeNode : vueSelectTreeNodeArr) {
			if(StringUtils.equalsIgnoreCase(treeNode.getParent(), parentNode.getId())) {//子节点
				if(parentNode.getChildren() == null) {
					parentNode.setChildren(new ArrayList<VueAntdTreeSelectNodeForEquipment>());
				}
				treeNode.setKey(treeNode.getId());
				treeNode.setValue(treeNode.getId());
				parentNode.getChildren().add(treeNode);
				parentNode.setIsLeaf(false);
				bulidAntdTree(vueSelectTreeNodeArr,treeNode);
			}
		}
	}
	
}
