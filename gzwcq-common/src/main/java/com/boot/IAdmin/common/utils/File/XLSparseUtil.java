package com.boot.IAdmin.common.utils.File;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName: XLSparseUtil
 * @Description: TODO(类功能描述) EXCEL文件解析与生成
 * @author: josephy
 * @date: 2017年5月8日 下午2:57:31  (参数描述)
 */
@Repository
public class XLSparseUtil {
    static private SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
    static private SimpleDateFormat sdf_full = new SimpleDateFormat("yyyy年MM月dd日");
    static private SimpleDateFormat sdf_half = new SimpleDateFormat("M月dd日");
    static private DateFormat sdf_time = new SimpleDateFormat("H时mm分");
//    static private String Context = File.separator + "app";
//    static private String relativePath = "upLoad";//+File.separator+sdf1.format(new Date());
    static private String Context ="d:/";
    static private String relativePath = "demo";//+File.separator+sdf1.format(new Date());
    static private int maxRow = 65534;//去掉表头

    private SimpleDateFormat sdf_date = new SimpleDateFormat("yyyyMMdd");
    static String FTP_Path = File.separator + "app" + File.separator + "FTP-Local";
    /**
     * IE浏览器标识
     */
    static private final String IE_MSIE = "msie";
    /**
     * IE浏览器标识
     */
    static private final String IE_GECKO = "like gecko";


    /**
     * @param title    表头  格式String[]
     * @param data     表数据  格式List<Map<String,String>>,其中key为col-num
     * @param fileName 文件名称 不需要加后缀
     * @return
     * @throws IOException (参数描述，入参中文描述，返回值描述)
     * @Title: toFile
     * @Package: com.ctzj.biz.stms.util
     * @Description: TODO(方法功能描述) 文件生成 数据多的时候会创建多个sheet
     * @author: josephy
     * @date: 2017年11月3日 下午4:43:25
     * @version: V2.0
     */
    static public byte[] toFile(String[] title, List<Map<String, String>> data, String fileName) throws IOException {
//        File file = new File(Context + File.separator + relativePath);
//        if (!file.exists()) {
//            file.mkdirs();
//        }
//        file = new File(Context + File.separator + relativePath, fileName);
//        if (!file.exists()) {
//            file.mkdirs();
//        }
        HSSFWorkbook workbook = new HSSFWorkbook();
        int sheetNum = (data.size() - 1) / maxRow + 1;
        for (int index = 0; index < sheetNum; index++) {
            Sheet sheet = workbook.createSheet();
            int rownum = 0;
            if (title != null) {
                Row row = sheet.createRow(rownum);
                for (int column = 0; column < title.length; column++) {
                    Cell cell = row.createCell(column, CellType.STRING);
                    cell.setCellValue(title[column]);
                }
                rownum += 1;
            }
            List<Map<String, String>> tempData = data.subList(index * maxRow, (index + 1) * maxRow > data.size() ? data.size() : (index + 1) * maxRow);
            for (int i = 0; i < tempData.size(); i++) {
                Map<String, String> temp = tempData.get(i);
                Row row = sheet.createRow(rownum++);
                Iterator<String> it = temp.keySet().iterator();
                while (it.hasNext()) {
                    String key = it.next();
                    String value = String.valueOf(temp.get(key));
                    if ("col-".equals(String.valueOf(key).substring(0, 4))) {
                        int index_ = Integer.parseInt(String.valueOf(key).substring(4));
                        Cell cell = row.createCell(index_, CellType.STRING);
                        cell.setCellValue(value);
                    }
                }
            }
        }
        File f = new File(fileName);

        FileOutputStream fis = null;
        byte[] byt = null;
        InputStream input = null;
        try {
            fis = new FileOutputStream(f);
            workbook.write(fis);
            input = new FileInputStream(fileName);
            byt = new byte[input.available()];
            int read = input.read(byt);
            fis.flush();
            fis.close();
        } finally {
            if(fis != null){
                fis.close();
            }
            if(input != null){
                input.close();
            }
            if(workbook != null){
                workbook.close();
            }
            if(f.exists()){
                System.gc();
                f.delete();
            }
        }

        return byt;
    }

    /**
     * @param title      表头
     * @param name       sql里对应属性
     * @param data       数据
     * @param fileName   生成文件的名字
     * @param dateFormat 时间类型返回的格式
     * @return
     * @throws IOException (参数描述，入参中文描述，返回值描述)
     * @Title: toFile
     * @Package: com.ctzj.biz.stms.util
     * @Description: TODO(方法功能描述)
     * @author: josephy
     * @date: 2018年1月10日 下午3:10:46
     * @version: V1.0
     */
    static public File toFile(String[] title, String[] name, List<Map<String, Object>> data, String fileName, String dateFormat) throws IOException {
        if (dateFormat != null) {
            sdf = new SimpleDateFormat(dateFormat);
        }
        return toFile(title, name, data, fileName);
    }

    static public File toFile(String[] title, String[] name, List<Map<String, Object>> exportData, String fileName) throws IOException {
        File csvFile = null;
        BufferedWriter csvFileOutputStream = null;
        String outPutPath = File.separator + "app" + File.separator + "upLoad" + File.separator;
        File file = new File(outPutPath);
        if (!file.exists()) {
            file.mkdir();
        }
        // 定义文件名格式并创建
        fileName = fileName.replaceAll(".xls", "");
        csvFile = File.createTempFile(fileName, ".csv", new File(outPutPath));
        System.out.println("csvFile：" + csvFile);
        // UTF-8使正确读取分隔符","
        csvFileOutputStream = new BufferedWriter(
                new OutputStreamWriter(
                        new FileOutputStream(csvFile), "gbk"), 1024);
        // 写入文件头部
        for (int i = 0; title != null && i < title.length; i++) {
            csvFileOutputStream.write("" + title[i] == null ? "" : title[i] + "");
            if (i == title.length - 1) {
                csvFileOutputStream.newLine();
                break;
            }
            csvFileOutputStream.write(",");
        }
        // 写入文件内容
        for (Iterator iterator = exportData.iterator(); iterator.hasNext(); ) {
            Map object = (Map) iterator.next();
            int len = title == null ? name.length : Math.min(name.length, title.length);
            for (int i = 0; i < len; i++) {
//	                    csvFileOutputStream.write((String) BeanUtils.getProperty(
//	                    		object, name[i])+ "\t");
                if (object.get(name[i]) != null) {
                    csvFileOutputStream.write(object.get(name[i]) + "\t");
                }
                csvFileOutputStream.write(",");
            }
            if (iterator.hasNext()) {
                csvFileOutputStream.newLine();
            }
        }
        csvFileOutputStream.flush();
        csvFileOutputStream.close();
        return csvFile;
    }

    //	static public File toFile(String[] title, String[] name, List<Map<String,Object>> data,String fileName) throws IOException{
//		File file = new File(Context+File.separator+relativePath);
//		if (!file.exists()) {
//			file.mkdirs();
//		}
//		file = new File(Context+File.separator+relativePath, fileName);
//		HSSFWorkbook  workbook = new HSSFWorkbook();
//		int sheetNum = (data.size()-1)/maxRow + 1;
//		for (int index = 0; index < sheetNum; index++) {
//			Sheet sheet = workbook.createSheet();
//			int rownum = 0;
//			if (title!=null) {
//				Row row = sheet.createRow(rownum);
//				for (int column = 0; column < title.length; column++) {
//					Cell cell = row.createCell(column, Cell.CELL_TYPE_STRING);
//					cell.setCellValue(title[column]);
//				}
//				rownum+=1;
//			}
//			List<Map<String, Object>> tempData=data.subList(index*maxRow, (index+1)*maxRow>data.size()?data.size():(index+1)*maxRow);
//			for (int i = 0; i < tempData.size(); i++) {
//				Map<String, Object> temp = tempData.get(i);
//				Row row = sheet.createRow(rownum++);
//				int index_ = 0;
//				for (String key : name) {
//					String value = ObjectToString(temp.get(key));
//					Cell cell = row.createCell(index_++, Cell.CELL_TYPE_STRING);
//					cell.setCellValue(value);
//				}
//			}
//		}
//		FileOutputStream fis = new FileOutputStream(file);
//		workbook.write(fis);
//		fis.flush();
//		fis.close();
//		return file;
//	}
    static public String ObjectToString(Object obj) {
        String value;
        if (obj instanceof BigDecimal) {
            value = obj.toString();
        } else if (obj instanceof Date) {
            value = sdf.format(obj);
        } else {
            value = (String) obj;
        }
        if (value == null || "null".equals(value)) {
            value = "";
        }
        return value;
    }

    static public Map<String, Object> parse(File f, String dateFormat, String[][] limit, int head) throws Exception {
        if (dateFormat != null) {
            sdf = new SimpleDateFormat(dateFormat);
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("success", false);
        String name = f.getName();
        String[] split = name.split("[.]");
        int size = split.length - 1;
        if (split[size].equals("xls") || split[size].equals("xlsx")) { //HSSF or XSSF
            try {
                Map<String, Object> map = parseXLS(f, limit, head);
                String error = (String) map.get("error");
                if (error != null) {
                    result.put("msg", error);
                } else {
                    List<Map<String, String>> data = (List<Map<String, String>>) map.get("data");
                    result.put("success", true);
                    result.put("data", data);
                }
            } catch (Exception e) {
                e.printStackTrace();
//				result.put("msg", "文件解析异常,请确认文档无误后重试!");
                throw e;
            }
        } else {
//			result.put("msg", "文件格式错误,请确认文档无误后重试!");
            throw new Exception("文件格式错误,请确认文档无误后重试!");
        }
        return result;
    }

    static private Map<String, Object> parseXLS(File f, String[][] limit, int head) throws InvalidFormatException, IOException {
        FileInputStream fis = new FileInputStream(f);
        Workbook xssfWorkbook = WorkbookFactory.create(fis);
        Map<String, Object> returnMap = new HashMap<String, Object>();
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        Map<String, String> temp;
        for (int i = 0; i < xssfWorkbook.getNumberOfSheets(); i++) {
            Sheet xssfSheet = xssfWorkbook.getSheetAt(i);
            if (xssfSheet == null) {
                continue;
            }
            for (int rowNum = head; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {//the first row is doc, second row is title
                Row row = xssfSheet.getRow(rowNum);
                temp = new HashMap<String, String>();
                for (int cellNum = 0; cellNum <= limit[0].length || cellNum <= row.getLastCellNum(); cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String value = getValue(cell);
                    if (!compare(limit, cellNum, value)) {
                        String msg = "第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列数据无效,请确认后重新上传";
                        returnMap.put("error", msg);
                        return returnMap;
                    }
                    temp.put("col-" + cellNum, value);

                }
                result.add(temp);
            }
        }
        fis.close();
        returnMap.put("data", result);
        return returnMap;
    }

    static public Map<String, Object> parse_new(File f, String dateFormat, String[][] limit, int head) throws Exception {
        if (dateFormat != null) {
            sdf = new SimpleDateFormat(dateFormat);
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("success", false);
        String name = f.getName();
        String[] split = name.split("[.]");
        int size = split.length - 1;
        if (split[size].equals("xls") || split[size].equals("xlsx")) { //HSSF or XSSF
            try {
                Map<String, Object> map = parseXLS_new(f, limit, head);
                String error = (String) map.get("error");
                if (error != null) {
                    result.put("msg", error);
                } else {
                    List<Map<String, String>> data = (List<Map<String, String>>) map.get("data");
                    result.put("success", true);
                    result.put("data", data);
                    result.put("errorList", map.get("errorList"));
                }
            } catch (Exception e) {
                e.printStackTrace();
//				result.put("msg", "文件解析异常,请确认文档无误后重试!");
                throw e;
            }
        } else {
//			result.put("msg", "文件格式错误,请确认文档无误后重试!");
            throw new Exception("文件格式错误,请确认文档无误后重试!");
        }
        return result;
    }

    static public Map<String, Object> parseXLS_new(File f, String[][] limit, int head) throws Exception {
        FileInputStream fis = new FileInputStream(f);
        Workbook xssfWorkbook = WorkbookFactory.create(fis);
        Map<String, Object> returnMap = new HashMap<String, Object>();
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        Map<String, String> temp;
        List<String> errorList = new ArrayList<String>();
        for (int i = 0; i < xssfWorkbook.getNumberOfSheets(); i++) {
            Sheet xssfSheet = xssfWorkbook.getSheetAt(i);
            if (xssfSheet == null) {
                continue;
            }
            for (int rowNum = head; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {//the first row is doc, second row is title
                Row row = xssfSheet.getRow(rowNum);
                temp = new HashMap<String, String>();
                temp.put("col-r", String.valueOf(rowNum + 1));
                boolean isError = true;
                for (int cellNum = 0; cellNum <= limit[0].length || cellNum <= row.getLastCellNum(); cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String value = "";
                    try {
                        value = getValue(cell);
                    } catch (Exception e) {
                        e.printStackTrace();
                        String msg = e.toString();
                        throw new Exception("error date sheet:" + i + " rowNum:" + rowNum + "  cellNum:" + cellNum + "   " + (msg.length() > 100 ? msg.substring(0, 100) : msg));
                    }
                    if (!compare(limit, cellNum, value)) {
                        String msg = "第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列数据无效,请确认后重新上传";
//						returnMap.put("error", msg);
//						return returnMap;
                        errorList.add(msg);
                        isError = false;
                        continue;
//						temp.put("col-"+cellNum, value);
                    }
                    temp.put("col-" + cellNum, value);

                }
                if (isError) {
                    result.add(temp);
                }
            }
        }
        fis.close();
        returnMap.put("errorList", errorList);
        returnMap.put("data", result);
        return returnMap;
    }

    static public Map<String, Object> parse_new(File f, String dateFormat, String[][] limit, int head, int sheetNum) throws Exception {
        if (dateFormat != null) {
            sdf = new SimpleDateFormat(dateFormat);
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("success", false);
        String name = f.getName();
        String[] split = name.split("[.]");
        int size = split.length - 1;
        if (split[size].equals("xls") || split[size].equals("xlsx")) { //HSSF or XSSF
            try {
                Map<String, Object> map = parseXLS_new(f, limit, head, sheetNum);
                String error = (String) map.get("error");
                if (error != null) {
                    result.put("msg", error);
                } else {
                    List<Map<String, String>> data = (List<Map<String, String>>) map.get("data");
                    result.put("success", true);
                    result.put("data", data);
                    result.put("errorList", map.get("errorList"));
                }
            } catch (Exception e) {
                e.printStackTrace();
//				result.put("msg", "文件解析异常,请确认文档无误后重试!");
                throw e;
            }
        } else {
//			result.put("msg", "文件格式错误,请确认文档无误后重试!");
            throw new Exception("文件格式错误,请确认文档无误后重试!");
        }
        return result;
    }

    static public Map<String, Object> parse_newForPoint(File f, String dateFormat, String[][] limit, int head, int sheetNum) throws Exception {
        if (dateFormat != null) {
            sdf = new SimpleDateFormat(dateFormat);
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("success", false);
        String name = f.getName();
        String[] split = name.split("[.]");
        int size = split.length - 1;
        if (split[size].equals("xls") || split[size].equals("xlsx")) { //HSSF or XSSF
            try {
                Map<String, Object> map = parseXLS_newForPoint(f, limit, head, sheetNum);
                String error = (String) map.get("error");
                if (error != null) {
                    result.put("msg", error);
                } else {
                    List<Map<String, String>> data = (List<Map<String, String>>) map.get("data");
                    result.put("success", true);
                    result.put("data", data);
//					result.put("errorList", map.get("errorList"));
                }
            } catch (Exception e) {
                e.printStackTrace();
//				result.put("msg", "文件解析异常,请确认文档无误后重试!");
                throw e;
            }
        } else {
//			result.put("msg", "文件格式错误,请确认文档无误后重试!");
            throw new Exception("文件格式错误,请确认文档无误后重试!");
        }
        return result;
    }

    static public Map<String, Object> parse_new_health(File f, String dateFormat, String[][] limit, int head, int sheetNum) throws Exception {
        if (dateFormat != null) {
            sdf = new SimpleDateFormat(dateFormat);
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("success", false);
        String name = f.getName();
        String[] split = name.split("[.]");
        int size = split.length - 1;
        if (split[size].equals("xls") || split[size].equals("xlsx")) { //HSSF or XSSF
            try {
                Map<String, Object> map = parseXLS_new_health(f, limit, head, sheetNum);
                String error = (String) map.get("error");
                if (error != null) {
                    result.put("msg", error);
                } else {
                    List<Map<String, String>> data = (List<Map<String, String>>) map.get("data");
                    result.put("success", true);
                    result.put("data", data);
                    result.put("errorList", map.get("errorList"));
                }
            } catch (Exception e) {
                e.printStackTrace();
//				result.put("msg", "文件解析异常,请确认文档无误后重试!");
                throw e;
            }
        } else {
//			result.put("msg", "文件格式错误,请确认文档无误后重试!");
            throw new Exception("文件格式错误,请确认文档无误后重试!");
        }
        return result;
    }

    static public Map<String, Object> parseXLS_new(File f, String[][] limit, int head, int sheetNum) throws Exception {
        FileInputStream fis = new FileInputStream(f);
        Workbook xssfWorkbook = WorkbookFactory.create(fis);
        Map<String, Object> returnMap = new HashMap<String, Object>();
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        Map<String, String> temp;
        List<String> errorList = new ArrayList<String>();
        for (int i = 0; i < xssfWorkbook.getNumberOfSheets(); i++) {
            if (i != sheetNum) {
                continue;
            }
            Sheet xssfSheet = xssfWorkbook.getSheetAt(i);
            if (xssfSheet == null) {
                continue;
            }
            for (int rowNum = head; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {//the first row is doc, second row is title
                Row row = xssfSheet.getRow(rowNum);
                temp = new HashMap<String, String>();
                temp.put("col-r", String.valueOf(rowNum + 1));
                boolean isError = true;
                for (int cellNum = 0; cellNum <= limit[0].length || cellNum <= row.getLastCellNum(); cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String value = "";
                    try {
                        value = getValue(cell);
                    } catch (Exception e) {
                        e.printStackTrace();
                        String msg = e.toString();
                        throw new Exception("error date sheet:" + i + " rowNum:" + rowNum + "  cellNum:" + cellNum + "   " + (msg.length() > 100 ? msg.substring(0, 100) : msg));
                    }
                    if (!compare(limit, cellNum, value)) {
                        String msg = "第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列数据无效,请确认后重新上传";
//						returnMap.put("error", msg);
//						return returnMap;
                        errorList.add(msg);
                        isError = false;
                        continue;
//						temp.put("col-"+cellNum, value);
                    }
                    temp.put("col-" + cellNum, value);

                }
                if (isError) {
                    result.add(temp);
                }
            }
        }
        fis.close();
        returnMap.put("errorList", errorList);
        returnMap.put("data", result);
        return returnMap;
    }

    static public Map<String, Object> parseXLS_newForPoint(File f, String[][] limit, int head, int sheetNum) throws Exception {
        FileInputStream fis = new FileInputStream(f);
        Workbook xssfWorkbook = WorkbookFactory.create(fis);
        Map<String, Object> returnMap = new HashMap<String, Object>();
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        Map<String, String> temp;
        Map<Integer, String> errorMap = new HashMap();
        try {
	        for (int i = 0; i < xssfWorkbook.getNumberOfSheets(); i++) {
	            if (i != sheetNum) {
	                continue;
	            }
	            Sheet xssfSheet = xssfWorkbook.getSheetAt(i);
	            if (xssfSheet == null) {
	                continue;
	            }
	            for (int rowNum = head; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {//the first row is doc, second row is title
	                Row row = xssfSheet.getRow(rowNum);
	                if(row == null){
	                    continue;
                    }
	                temp = new HashMap<String, String>();
	                temp.put("col-r", String.valueOf(rowNum + 1));
	                boolean isError = true;
	                int emptyCellNum = 0;
	                for (int cellNum = 0; cellNum <= limit[0].length || cellNum <= row.getLastCellNum(); cellNum++) {
//	                    if(row.getCell(cellNum) == null){
//                            emptyCellNum = row.getLastCellNum() + 1;
//	                        break;
//                        }
	                    Cell cell = row.getCell(cellNum);
	                    String value = "";
	                    try {
	                        value = getValue(cell);
	                        if(StringUtils.isEmpty(value)){
	                            emptyCellNum++;
	                        }
	                    } catch (Exception e) {
	                        e.printStackTrace();
	                        String msg = e.toString();
	                        throw new Exception("error date sheet:" + i + " rowNum:" + rowNum + "  cellNum:" + cellNum + "   " + (msg.length() > 100 ? msg.substring(0, 100) : msg));
	                    }
	
	                    temp.put("col-" + cellNum, value);
	
	                }
	                if (isError && emptyCellNum != row.getLastCellNum() + 1) {
	                    result.add(temp);
	                }
	            }
	        }
	        returnMap.put("data", result);
        }finally {
        	IOUtils.closeQuietly(fis);
        	xssfWorkbook.close();
        }
        return returnMap;
    }

    static public Map<String, Object> parseXLS_new_health(File f, String[][] limit, int head, int sheetNum) throws Exception {
        FileInputStream fis = new FileInputStream(f);
        Workbook xssfWorkbook = WorkbookFactory.create(fis);
        Map<String, Object> returnMap = new HashMap<String, Object>();
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        Map<String, String> temp;
        List<String> errorList = new ArrayList<String>();
        for (int i = 0; i < xssfWorkbook.getNumberOfSheets(); i++) {
            if (i != sheetNum) {
                continue;
            }
            Sheet xssfSheet = xssfWorkbook.getSheetAt(i);
            if (xssfSheet == null) {
                continue;
            }
            for (int rowNum = head; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {//the first row is doc, second row is title
                Row row = xssfSheet.getRow(rowNum);
                temp = new HashMap<String, String>();
                temp.put("col-r", String.valueOf(rowNum + 1));
                boolean isError = true;
                for (int cellNum = 0; cellNum <= limit[0].length || cellNum <= row.getLastCellNum(); cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String value = "";
                    try {
                        int type = cell == null ? 1 : cell.getCellType().getCode();
                        if (type == CellType.NUMERIC.getCode()) {
                            DecimalFormat df = new DecimalFormat("0.00");
                            value = df.format(cell.getNumericCellValue());
                        } else {
                            value = getValue(cell);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        String msg = e.toString();
                        throw new Exception("error date sheet:" + i + " rowNum:" + rowNum + "  cellNum:" + cellNum + "   " + (msg.length() > 100 ? msg.substring(0, 100) : msg));
                    }
                    if (!compare(limit, cellNum, value)) {
                        String msg = "第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列数据无效,请确认后重新上传";
//						returnMap.put("error", msg);
//						return returnMap;
                        errorList.add(msg);
                        isError = false;
                        continue;
//						temp.put("col-"+cellNum, value);
                    }
                    temp.put("col-" + cellNum, value);

                }
                if (isError) {
                    result.add(temp);
                }
            }
        }
        fis.close();
        returnMap.put("errorList", errorList);
        returnMap.put("data", result);
        return returnMap;
    }

    //第0行 类型  第1行 限制值
    //包含类型如果可以为空  值中请多加一个空格
    //数字类型,如果是几个固定值 请用包含类型  如果是范围 可以写"1,3"代表起始值
    static boolean compare(String[][] limit, int cellNum, Object obj) {
        if (limit[0].length <= cellNum) {
            return true;
        }
        String orign = limit[0][cellNum];
        if (orign == null) {
            return true;
        }
        if (orign.equals("非空")) {
            return obj != null;
        }
        if (orign.equals("包含")) {
            if (obj == null) {
                return limit[1][cellNum].contains(" ");
            }
            String[] check = limit[1][cellNum].split("[,]");
            for (String str : check) {
                if (str.equals(obj)) {
                    return true;
                }
            }
            return false;
        }
        if (orign.equals("数字")) {
            if (obj == null) {
                return true;
            }
            boolean isNumeric = StringUtils.isNumeric(obj.toString());
            if (limit[1][cellNum] == null) {
                return isNumeric;
            }
            if (isNumeric) {
                int num = Integer.parseInt(obj.toString());
                String[] split = limit[1][cellNum].split("[,]");
                int start = Integer.parseInt(split[0]);
                int end = Integer.parseInt(split[1]);
                if (num < end && num > start) {
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    static public String getValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        CellType type = cell.getCellType();

        String value;
        switch (type) {
            case STRING:
                value = cell.getStringCellValue();
                break;
            case BOOLEAN:
                value = "" + cell.getBooleanCellValue();
                break;
            case BLANK:
                value = "";
                break;
            case ERROR:
                value = "";
                break;
            case NUMERIC:
//			yyyy年m月d日----->31
//			m月d日---->58
//			h时mm分--->32
                short dateType = cell.getCellStyle().getDataFormat();
                double v1 = cell.getNumericCellValue();
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date = DateUtil.getJavaDate(v1);
                    value = sdf.format(date);
                } else if (dateType == 58) {
                    double value1 = cell.getNumericCellValue();
                    Date date = DateUtil.getJavaDate(value1);
                    value = sdf_half.format(date);
                } else if (dateType == 31) {
                    double value1 = cell.getNumericCellValue();
                    Date date = DateUtil.getJavaDate(value1);
                    value = sdf_full.format(date);
                } else if (dateType == 32) {
                    double value1 = cell.getNumericCellValue();
                    Date date = DateUtil.getJavaDate(value1);
                    value = sdf_time.format(date);
                } else {
                    DecimalFormat df = new DecimalFormat("0.000000");
                    value = df.format(cell.getNumericCellValue());
//                    BigDecimal v3 = BigDecimal.valueOf(v1).stripTrailingZeros();
//                    value = v3.toString();
                }
                break;
            case FORMULA:
                value = String.valueOf(cell.getNumericCellValue());
                if (value.equals("NaN")) {// 如果获取的数据值为非法值,则转换为获取字符串
                    value = cell.getStringCellValue().toString();
                }
                break;
            default:
                value = cell.getStringCellValue();
                break;
        }
        value = value.trim();
        if ("".equals(value.trim())) {
            value = null;
        }
        return value;
    }

    public static void downloadFile(HttpServletRequest request, HttpServletResponse response, String filePath, byte[] bFile) {
        OutputStream os = null;
        boolean flag = true;
        String fileName = "";
        try {
            String userAgent = request.getHeader("user-agent").toLowerCase();
            if (userAgent.contains(IE_MSIE) || userAgent.contains(IE_GECKO)) {
                // win10 ie edge 浏览器 和其他系统的ie
                fileName = URLEncoder.encode(filePath.substring(filePath.lastIndexOf("/") + 1 ,filePath.length()), "UTF-8");
            } else {
                fileName = new String(filePath.substring(filePath.lastIndexOf("/") + 1 ,filePath.length()).getBytes("UTF-8"), "iso-8859-1");
            }
            os = response.getOutputStream();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setContentLength(bFile.length);
            response.getOutputStream().write(bFile, 0, bFile.length);
        } catch (Exception e) {
            flag = false;
            e.printStackTrace();
        } finally {
            try {
                if (!flag) {
                    os.write(("服务繁忙,请稍后重试").getBytes());
                }
                os.flush();
                os.close();
                response.flushBuffer();
                //删除临时文件
//                File file = new File(filePath.substring(0, filePath.lastIndexOf("/"))+File.separator+relativePath);
//                if (file.exists()) {
//                    file.delete();
//                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    //public static void main(String[] args) throws Exception {
//		String[][] limit={{null,"非空","包含","数字"},{null,null,"测试, 测试1, 测试2",null}};
//		String[] value={"1",null,"测试1","21"};
//		boolean x;
//		for (int i = 0; i < value.length; i++) {
//			x= compare(limit, i, value[i]);
//		}
//
//        downloadFile(request, response, "text.xlsx", Bfile);
//		System.out.println(f.getName());
// 		String[] split = "f:/x\\ds\\dsf".split("\\\\");
// 		String str="f:/x\\ds\\dsf";
// 		int indexOf = str.lastIndexOf(File.separator);
// 		str = str.substring(indexOf+1);
// 		System.out.println("col-1234".substring(4));
// 		String[][] limit={{"包含","包含","非空","数字",null,null,null,null,null,"非空","包含"},
//		 		   {"判断题,单选题,多选题,简答题, ","低,中,高",null,null,null,null,null,null,null,null,"是,否"}};
       // String[][] limit = {{null}, {null}};
//        Map<String, Object> map = parse(f, null, limit, 2);
//        System.out.println(map.get("success") + "" + map.get("msg") + map.get("data"));
//		String[] title={"题型","题目难度","题目批次","题目内容","选项个数","A","B","C","D","E","答案","是否可用"};
//		String newName = new Date().getTime()+"_"+"题库.xls";
//		try {
//			Map map1 = new HashMap();
//			map1.put("col-0", "ceshi");
//			map1.put("col-1", "ceshi1");
//			List list = new ArrayList();
//			list.add(map1);
//			list.add(map1);
//			toFile(title,list,newName);
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//		System.out.println(0);
//  }

    public void setRegionBorder(BorderStyle border, CellRangeAddress region, Sheet sheet) {
        RegionUtil.setBorderBottom(border, region, sheet);
        RegionUtil.setBorderLeft(border, region, sheet);
        RegionUtil.setBorderRight(border, region, sheet);
        RegionUtil.setBorderTop(border, region, sheet);
    }

    /**
     * 动态合并单元格
     * @param headNum 表头数字，“0,2,0,0”  ===>  “起始行，截止行，起始列，截止列”
     * @param sheet
     */
    public void mergeCell(String[] headNum, Sheet sheet) {
        // 动态合并单元格
        for (int i = 0; i < headNum.length; i++) {
            sheet.autoSizeColumn(i, true);
            String[] temp = headNum[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);

            CellRangeAddress cra = new CellRangeAddress(startrow, overrow, startcol, overcol);
            sheet.addMergedRegion(cra);
//            this.setRegionBorder(BorderStyle.THIN, cra, sheet);
        }
    }

    /**
     * 设置合并表格，空缺单元格样式
     * @param row
     * @param startNum
     * @param endNum
     * @param style
     */
    public void setEmptyCellStyle(HSSFRow row, int startNum, int endNum, HSSFCellStyle style) {
        for (int j = startNum; j < endNum; j++) {
            HSSFCell cell = row.createCell(j);
            cell.setCellStyle(style);
        }
    }


}
