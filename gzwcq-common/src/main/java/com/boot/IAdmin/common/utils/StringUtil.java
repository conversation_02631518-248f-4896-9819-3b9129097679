package com.boot.IAdmin.common.utils;

import java.io.StringReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.xml.sax.InputSource;

public class StringUtil {
	
	//去除所有字符串中的空格、回车、换行符、制表符
	public static String replaceBlank(String str) {
		String dest = "";
		if (str!=null) {//\\s*|\t|\r|\n
			Pattern p = Pattern.compile("\t|\r|\n");
			Matcher m = p.matcher(str);
			dest = m.replaceAll("");
		}
		return dest;
	}
	
	//是否是xml格式字符串
	public static boolean isXmlDocument(String rtnMsg){
		 
		boolean flag = true;
		try {
			DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = documentBuilderFactory.newDocumentBuilder();
			builder.parse( new InputSource( new StringReader( rtnMsg )));
		} catch (Exception e) {
		    flag = false;
		}
		return flag;

	}
	
	/**
     * Returns the string representation of the {@code Object} argument.
     *
     * @param   obj   an {@code Object}.
     * @return  if the argument is {@code null}, then a string equal to
     *          {@code ""}; otherwise, the value of
     *          {@code obj.toString()} is returned.
     * @see     java.lang.Object#toString()
     */
	public static String valueOf(Object obj) {
        return (obj == null) ? "" : obj.toString();
    }
}
