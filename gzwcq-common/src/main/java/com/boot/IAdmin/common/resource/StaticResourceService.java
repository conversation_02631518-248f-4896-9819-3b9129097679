/*
 * 版权所有：杭州领域向尚科技有限公司
 * 地址：浙江省杭州市滨江区滨安路688号天和高科技产业园5-2311
 *
 * (c) Copyright Hangzhou  Area Up Technology Co., Ltd.
 */

package com.boot.IAdmin.common.resource;

import org.springframework.core.io.Resource;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

/**
 * 静态资源服务
 *
 * <AUTHOR>
 * @date 26/10/2017
 */
public interface StaticResourceService {
    /**
     * 上传资源
     *
     * @param path 相对路径
     * @param data 数据流
     * @return 新资源的资源定位符
     * @throws IOException
     */
    String upload(String path, File data) throws IOException, URISyntaxException;

    /**
     * 获取指定资源的资源定位符
     *
     * @param path 资源路径（相对）
     * @return 资源实体
     */
    Resource get(String path) throws URISyntaxException;

    /**
     * 删除资源
     *
     * @param path 资源路径（相对）
     * @throws IOException 删除时错误
     */
    void delete(String path) throws IOException;

}
