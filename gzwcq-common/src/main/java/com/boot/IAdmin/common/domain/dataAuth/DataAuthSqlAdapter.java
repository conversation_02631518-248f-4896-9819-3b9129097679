package com.boot.IAdmin.common.domain.dataAuth;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 数据权限适配
 * */
public class DataAuthSqlAdapter implements IDataAuthSql{
	
	@JsonIgnore
	protected String whereSqlForDataAuth;//用于控制数据权限的sql

	@Override
	public String getWhereSqlForDataAuth() {
		return whereSqlForDataAuth;
	}

	@Override
	public void setWhereSqlForDataAuth(String sql) {
		this.whereSqlForDataAuth = sql;
	}
	
}
