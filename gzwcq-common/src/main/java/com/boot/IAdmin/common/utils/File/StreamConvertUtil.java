package com.boot.IAdmin.common.utils.File;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 文件流转换工具类
 * */
public class StreamConvertUtil {
	// inputStream转outputStream
	public static ByteArrayOutputStream parse(final InputStream in) throws Exception {
		final ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
		int ch;
		while ((ch = in.read()) != -1) {
			swapStream.write(ch);
		}
		return swapStream;
	}

	// outputStream转inputStream
	public static ByteArrayInputStream parse(final OutputStream out) throws Exception {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		baos = (ByteArrayOutputStream) out;
		final ByteArrayInputStream swapStream = new ByteArrayInputStream(baos.toByteArray());
		return swapStream;
	}

	// inputStream转String
	public static String parse_String(final InputStream in) throws Exception {
		final ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
		int ch;
		while ((ch = in.read()) != -1) {
			swapStream.write(ch);
		}
		return swapStream.toString();
	}

	// OutputStream 转String
	public static String parse_String(final OutputStream out) throws Exception {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		baos = (ByteArrayOutputStream) out;
		final ByteArrayInputStream swapStream = new ByteArrayInputStream(baos.toByteArray());
		return swapStream.toString();
	}

	// String转inputStream
	public static ByteArrayInputStream parse_inputStream(final String in) throws Exception {
		final ByteArrayInputStream input = new ByteArrayInputStream(in.getBytes());
		return input;
	}

	// String 转outputStream
	public static ByteArrayOutputStream parse_outputStream(final String in) throws Exception {
		return parse(parse_inputStream(in));
	}
}