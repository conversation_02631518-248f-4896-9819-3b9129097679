package com.boot.IAdmin.common.jdbc;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * JDBC工厂
 * 
 * <AUTHOR>
 * @date 2017年11月17日 17:19:38
 */
public final class JdbcConnectionFactory {

	// 驱动
	public static final String driverClassName = "com.mysql.jdbc.Driver";

	static {
		try {
			Class.forName(driverClassName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
	}
	
	//获取连接
	public static Connection getConn(String url, String user, String password) throws SQLException {
		return DriverManager.getConnection(url, user, password);
	}
	
	//关闭连接
	public static void close(ResultSet rs, Statement st, Connection conn) {
		try {
			if (rs != null)
				rs.close();
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			try {
				if (st != null)
					st.close();
			} catch (SQLException e) {
				e.printStackTrace();
			} finally {
				try {
					if (conn != null)
						conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
	}
}
