package com.boot.IAdmin.common.domain.vueTree;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class VueAntdTreeSelectNodeExport {

    protected String key;//同值

    protected String value;//值

    protected String title;//文本

    protected boolean selectable = true;

    protected boolean disableCheckbox;

    protected boolean disabled;

    protected boolean isLeaf = true;

    protected String id;//节点ID

    protected String parent;//父节点Id

    protected String parentId;//父节点Id

    protected String parents;//所有父节点

    protected String childrenIdS;//直接子节点Id

    protected List<VueAntdTreeSelectNodeExport> children;//子节点

    // 是否是父节点
    protected boolean isParent;

    protected String SSGZJGJG; //所属国资监管机构/6位区域码

    protected String sjzb; //实缴资本

    protected String czr; //出资人
    protected String zcd; //注册地
    protected String zcrq; //注册日期
    protected String qylb; //企业类别
    protected String parentBl; //父节点注资比例
    protected String zyhy; //主要行业

    protected String code; //所属国资监管机构/6位区域码

    protected String createUserName; //创建人名称

    //子节点数目
    protected Long childNum;

    public VueAntdTreeSelectNodeExport(){}

    public VueAntdTreeSelectNodeExport(List<VueAntdTreeSelectNodeExport> vueTreeNodeArr, String rootId, String rootLabel, boolean selectable){
        this.setId(rootId);
        this.setValue(rootId);
        this.setKey(rootId);
        this.setSelectable(selectable);
        this.setTitle(rootLabel);
		/*this.setChildrenIdS(childrenIdS);
		this.setParents(parents);
		this.setCode(code);
		this.setCreateUserName(createUserName);
		this.setSSGZJGJG(SSGZJGJG);*/
        bulidAntdTree2(vueTreeNodeArr,this);
    }

    public VueAntdTreeSelectNodeExport(String key, String value, String title) {
        super();
        this.key = key;
        this.value = value;
        this.title = title;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getSSGZJGJG() {
        return SSGZJGJG;
    }

    public void setSSGZJGJG(String SSGZJGJG) {
        this.SSGZJGJG = SSGZJGJG;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getParents() {
        return parents;
    }

    public void setParents(String parents) {
        this.parents = parents;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isSelectable() {
        return selectable;
    }

    public void setSelectable(boolean selectable) {
        this.selectable = selectable;
    }

    public boolean isDisableCheckbox() {
        return disableCheckbox;
    }

    public void setDisableCheckbox(boolean disableCheckbox) {
        this.disableCheckbox = disableCheckbox;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(boolean isLeaf) {
        this.isLeaf = isLeaf;
    }

    public List<VueAntdTreeSelectNodeExport> getChildren() {
        return children;
    }

    public void setChildren(List<VueAntdTreeSelectNodeExport> children) {
        this.children = children;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getChildrenIdS() {
        return childrenIdS;
    }

    public void setChildrenIdS(String childrenIdS) {
        this.childrenIdS = childrenIdS;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSjzb() {
        return sjzb;
    }

    public void setSjzb(String sjzb) {
        this.sjzb = sjzb;
    }

    public String getCzr() {
        return czr;
    }

    public void setCzr(String czr) {
        this.czr = czr;
    }

    public String getZcd() {
        return zcd;
    }

    public void setZcd(String zcd) {
        this.zcd = zcd;
    }

    public String getZcrq() {
        return zcrq;
    }

    public void setZcrq(String zcrq) {
        this.zcrq = zcrq;
    }

    public String getQylb() {
        return qylb;
    }

    public void setQylb(String qylb) {
        this.qylb = qylb;
    }

    public String getParentBl() {
        return parentBl;
    }

    public void setParentBl(String parentBl) {
        this.parentBl = parentBl;
    }

    public String getZyhy() {
        return zyhy;
    }

    public void setZyhy(String zyhy) {
        this.zyhy = zyhy;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public boolean getIsParent() {
        return isParent;
    }

    public void setIsParent(boolean isParent) {
        this.isParent = isParent;
    }
    public Long getChildNum() {
        return childNum;
    }

    public void setChildNum(Long childNum) {
        this.childNum = childNum;
        //判断是否为父节点
        this.setIsParent(childNum.longValue()>0);
    }

    /**
     * 构建下拉框树
     * */
    @Deprecated
    private void bulidAntdTree(List<VueAntdTreeSelectNodeExport> vueSelectTreeNodeArr,VueAntdTreeSelectNodeExport parentNode) {
        for(VueAntdTreeSelectNodeExport treeNode : vueSelectTreeNodeArr) {
            if(StringUtils.equalsIgnoreCase(treeNode.getParent(), parentNode.getId())) {//子节点
                if(parentNode.getChildren() == null) parentNode.setChildren(new ArrayList<VueAntdTreeSelectNodeExport>());
                treeNode.setKey(treeNode.getId());
                if(StringUtils.isBlank(treeNode.getValue())) treeNode.setValue(treeNode.getId());
                parentNode.getChildren().add(treeNode);
                parentNode.setIsLeaf(false);
                bulidAntdTree(vueSelectTreeNodeArr,treeNode);
            }
        }
    }

    /**
     * 构建下拉框树
     * */
    private void bulidAntdTree2(List<VueAntdTreeSelectNodeExport> vueSelectTreeNodeArr, VueAntdTreeSelectNodeExport parentNode) {
        //按照父节点拆分
        Map<String,List<VueAntdTreeSelectNodeExport>> parentMap = new HashMap<String,List<VueAntdTreeSelectNodeExport>>();
        for(VueAntdTreeSelectNodeExport treeNode : vueSelectTreeNodeArr) {
            if(parentMap.get(treeNode.getId()) == null) {
                parentMap.put(treeNode.getId(), new ArrayList<VueAntdTreeSelectNodeExport>());
            }
            if(parentMap.get(treeNode.getParent()) == null) {
                parentMap.put(treeNode.getParent(), new ArrayList<VueAntdTreeSelectNodeExport>());
            }
            treeNode.setChildren(parentMap.get(treeNode.getId()));//和子节点建立引用
            parentMap.get(treeNode.getParent()).add(treeNode);
        }
        parentNode.setChildren(parentMap.get(parentNode.getId()));
    }

}
