package com.boot.IAdmin.common.domain;

import java.io.Serializable;

public class BasePropConfigConstants extends BasePropConfig implements Serializable{

	private static final long serialVersionUID = 8042355387234201583L;

	//短信发送配置信息
	public static String SMS_ENDPOINT = null;
	public static String SMS_COMPANYID = null;
	public static String SMS_LOGINNAME = null;
	public static String SMS_LOGINKEY = null;
	public static String SMS_CHARSET = "UTF-8";
	
	//邮件发送配置信息
	public static String MAIL_SERVERHOST = null;
	public static String MAIL_SERVERPORT = null;
	public static String MAIL_PROTOCOL = null;
	public static String MAIL_FROMADDRESS = null;
	public static String MAIL_USERNAME = null;
	public static String MAIL_PASSWORD = null;
	public static String MAIL_VALIDATE = null;
	public static String MAIL_SENDERNICK = null;
	
	//系统基本信息
	public static String SYSTEM_NAME = null;
	
	
	//读取配置并设置值
	public void parseConfigProperties() {
		//短信
		SMS_ENDPOINT = get("SMS_ENDPOINT", EMPTY_STR);
		SMS_COMPANYID = get("SMS_COMPANYID", EMPTY_STR);
		SMS_LOGINNAME = get("SMS_LOGINNAME", EMPTY_STR);
		SMS_LOGINKEY = get("SMS_LOGINKEY", EMPTY_STR);
		SMS_CHARSET = get("SMS_CHARSET", "UTF-8");
		
		//邮件
		MAIL_SERVERHOST = get("MAIL_SERVERHOST", EMPTY_STR);
		MAIL_SERVERPORT = get("MAIL_SERVERPORT", EMPTY_STR);
		MAIL_PROTOCOL = get("MAIL_PROTOCOL", EMPTY_STR);
		MAIL_FROMADDRESS = get("MAIL_FROMADDRESS", EMPTY_STR);
		MAIL_USERNAME = get("MAIL_USERNAME", EMPTY_STR);
		MAIL_PASSWORD = get("MAIL_PASSWORD", EMPTY_STR);
		MAIL_VALIDATE = get("MAIL_VALIDATE", EMPTY_STR);
		MAIL_SENDERNICK = get("MAIL_SENDERNICK", EMPTY_STR);
		
		//基本信息
		SYSTEM_NAME = get("SYSTEM_NAME", EMPTY_STR);
	}
}
