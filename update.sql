/*20220728新增数据查询模板*/
drop table if exists cq_data_search_temp;

/*==============================================================*/
/* Table: cq_data_search_temp                                   */
/*==============================================================*/
create table cq_data_search_temp
(
   id                   varchar(50) not null comment '主键',
   name                 varchar(100) comment '模板名称',
   descript             varchar(100) comment '备注',
   share                int(1) comment '是否共享0/1',
   temp_json            text comment '查询条件',
   create_user          varchar(50) comment '创建人',
   create_time          datetime comment '创建时间',
   last_update_user     varchar(50) comment '更新人',
   last_update_time     datetime comment '更新时间',
   primary key (id)
);

alter table cq_data_search_temp comment '数据查询模板';