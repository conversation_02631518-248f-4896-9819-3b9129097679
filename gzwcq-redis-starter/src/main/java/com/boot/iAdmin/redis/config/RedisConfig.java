package com.boot.iAdmin.redis.config;

import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import com.boot.iAdmin.redis.common.RedisUtil;

/**
 * 配置集成redis
 * */
@Configuration
public class RedisConfig extends CachingConfigurerSupport{
    
    @Bean
    public RedisUtil initRedisUtil() {
    	return new RedisUtil();
    }
    
    /**
     * RedisTemplate配置
     * @param factory
     * @return
     */
    @Bean
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
    	RedisTemplate<Object, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setKeySerializer(keySerializer());
        return redisTemplate;
    }
    
    private RedisSerializer<String> keySerializer() {
        return new StringRedisSerializer();
    }

}