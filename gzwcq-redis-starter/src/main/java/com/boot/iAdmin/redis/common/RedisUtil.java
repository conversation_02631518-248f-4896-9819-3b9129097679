package com.boot.iAdmin.redis.common;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
  
/** 
 * @Comment 
 * <P>
 */  
@SuppressWarnings("all")
public class RedisUtil {
    /** 
     * RedisTemplate是一个简化Redis数据访问的一个帮助类， 
     * 此类对Redis命令进行高级封装，通过此类可以调用ValueOperations和ListOperations等等方法。 
     */  
    @Autowired(required= false)
    private RedisTemplate redisTemplate;  
  
    /** 
     * 批量删除对应的value 
     *  
     * @param keys 
     */  
    public void remove(final String... keys) {  
        for (String key : keys) {  
            remove(key);  
        }  
    }
    public boolean extendExpiration(String key, long timeout, TimeUnit unit) {
            boolean success = redisTemplate.expire(key, timeout, unit);
            return success;
    }
    /** 
     * 批量删除key 
     *  
     * @param pattern 
     */  
    public void removePattern(final String pattern) {  
        Set<String> keys = redisTemplate.keys(pattern);  
        if (keys.size() > 0)  
            redisTemplate.delete(keys);  
    }  
  
    /** 
     * 删除对应的value 
     * @param key 
     */  
    public void remove(final String key) {  
        if (exists(key)) {  
            redisTemplate.delete(key);  
        }  
    }

    /**
     * 增加缓存中的自增值
     *<AUTHOR>
     * @param key 缓存的键
     * @return 自增值
     */
    public Long increment(String key) {
        return redisTemplate.opsForValue().increment(key, 1);
    }

    /**
     * 增加缓存中的自增值并设置过期时间
     *<AUTHOR>
     * @param key     缓存的键
     * @param timeout 过期时间
     * @param unit    时间单位
     * @return 自增值
     */
    public Long incrementWithExpire(String key, long timeout, TimeUnit unit) {
        Long value = redisTemplate.opsForValue().increment(key, 1);
        if (value != null && value == 1) {
            redisTemplate.expire(key, timeout, unit);
        }
        return value;
    }

    /** 
     * 
     * @param key 
     * @return 
     */  
    public boolean exists(final String key) {  
        return redisTemplate.hasKey(key);  
    }  
  
    /** 
     * 读取缓存 
     * @param key 
     * @return 
     */  
    public Object get(final String key) {  
        Object result = null;  
        ValueOperations<String, Object> operations = redisTemplate.opsForValue();  
        result = operations.get(key);  
        return result;  
    }  
      
    /** 
     *  
     * <AUTHOR> 
     * @Date 2016年12月15日 上午11:28:46 
     * @param key 
     * @param hashKey 
     * @return 
     */  
    public Object get(final String key, final String hashKey){  
        Object result = null;  
        HashOperations<String,Object,Object> operations = redisTemplate.opsForHash();  
        result = operations.get(key, hashKey);  
        return result;  
    }  
  
    /** 
     * 写入缓存 
     *  
     * @param key 
     * @param value 
     * @return 
     */  
    public boolean set(final String key, Object value) {  
        boolean result = false;  
        try {  
            ValueOperations<String, Object> operations = redisTemplate.opsForValue();  
            operations.set(key, value);  
            result = true;  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return result;  
    }  
      
    /** 
     *  
     * <AUTHOR> 
     * @param key 
     * @param hashKey 
     * @param value 
     * @return 
     */  
    public boolean setHash(final String key, final String hashKey, Object value) {  
        boolean result = false;  
        try {  
            HashOperations<String,Object,Object> operations = redisTemplate.opsForHash();  
            operations.put(key, hashKey, value);  
            result = true;  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return result;  
    }  
  
    /** 
     * 写入缓存 
     *  
     * @param key 
     * @param value 
     * @return 
     */  
    public boolean set(final String key, Object value, Long expireTime) {  
        boolean result = false;  
        try {  
            ValueOperations<String, Object> operations = redisTemplate.opsForValue();  
            operations.set(key, value);  
            redisTemplate.expire(key, expireTime, TimeUnit.SECONDS);  
            result = true;  
        } catch (Exception e) {  
            e.printStackTrace();
        }  
        return result;  
    }
    
    /**
     * 消息发布
     * 
     * @param topic 主题
     * @param message 消息
     * */
    public boolean publish(String topic,Object message){
    	try {
    		redisTemplate.convertAndSend(topic, message);
    	}catch(Exception e){
    		e.printStackTrace();
    		return false;
    	}
    	return true;
    }
}  