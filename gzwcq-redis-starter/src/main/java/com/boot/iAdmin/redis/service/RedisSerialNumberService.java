package com.boot.iAdmin.redis.service;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 唯一编码生成服务
 * */
@Service
public class RedisSerialNumberService {
	
	private Log logger = LogFactory.getLog(this.getClass());
	
	@Autowired(required= false)
    private RedisTemplate<Object,Object> redisTemplate;
	
	private static final String DEFAULT_DATE_FORMATTER = "yyyyMMdd";
	
    /**
    * @param prefix为业务前缀
    * @param dateFormatter是日期格式
    * @param length是流水号最后位数
    */
    public String getSerialNumberByForFixedLength(String prefix,String dateFormatter,int length) {
    	if(StringUtils.isBlank(dateFormatter)) dateFormatter = DEFAULT_DATE_FORMATTER;
    	String currentDate = new SimpleDateFormat(dateFormatter).format(new Date());
    	String key = prefix +currentDate+length;
        StringBuilder crlSerialNumber=new StringBuilder();
        try {
        	long cacheCurrentValue = redisTemplate.opsForValue().increment(key);
            if(cacheCurrentValue==getMaxValueString(length)){//最大值
            	return crlSerialNumber.append(prefix).append(System.currentTimeMillis()).toString();
            }
            String currentValue = getCurrentValueString(length, cacheCurrentValue);
            return crlSerialNumber.append(prefix).append(currentDate).append(currentValue).toString();
        } catch (Exception e) {//异常
        	logger.error("getSerialNumberByPrefixAndLength|按业务前缀和后缀长度获取流水号异常|prefix："+prefix+"|length:"+length+"|exception", e);
            return crlSerialNumber.append(prefix).append(System.currentTimeMillis()).toString();
        }
    }
 
    /**
     * 字符串补足方法
     * @param length
     * @param currentValue
     * @return
     */
    private  String getCurrentValueString(int length, long currentValue) {
        String cValue=String.valueOf(currentValue);
        for(int i=cValue.length();i<length;i++){
            cValue="0"+cValue;
        }
        return cValue;
    }
 
       /**
     * 字符串最大值
     * @param length
     * @return
     */
    private  int getMaxValueString(int length) {
        String value="";
        for(int i=0;i<length;i++){
            value="9"+value;
        }
        return Integer.parseInt(value);
    }

}
