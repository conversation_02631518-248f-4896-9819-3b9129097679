<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.zjhc.gzw</groupId>
    <artifactId>gzwcq-parent</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>com.zjhc.gzw.gzwcq-parent</groupId>
  <artifactId>gzwcq-redis-starter</artifactId>
  
  <dependencies>
		<!-- redis -->
		<!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-data-redis -->
		<dependency>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-starter-data-redis</artifactId>
		    <exclusions> <!-- lettuce和zipkin冲突 -->
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
		</dependency>
		
		<dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
		
	</dependencies>
</project>