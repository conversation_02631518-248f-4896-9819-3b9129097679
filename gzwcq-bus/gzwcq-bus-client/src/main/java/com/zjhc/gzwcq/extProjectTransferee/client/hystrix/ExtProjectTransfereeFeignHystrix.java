package com.zjhc.gzwcq.extProjectTransferee.client.hystrix;

import org.springframework.stereotype.Component;
import com.zjhc.gzwcq.extProjectTransferee.client.ExtProjectTransfereeFeignClient;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeParam;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@Component
public class ExtProjectTransfereeFeignHystrix implements ExtProjectTransfereeFeignClient {
  
  	
  	/**
     * 列表查询
     */
	public BootstrapTableModel<ExtProjectTransfereeVo> queryByPage(ExtProjectTransfereeParam extProjectTransfereeParam){
    	throw new RuntimeException("操作失败");
    }
  
	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	public void insert(ExtProjectTransferee extProjectTransferee){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKeys(Map<String, Object> map){
    	throw new RuntimeException("操作失败");
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKey(String id){
    	throw new RuntimeException("操作失败");
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	public void updateIgnoreNull(ExtProjectTransferee extProjectTransferee){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 更新
	*/
  	public void update(ExtProjectTransferee extProjectTransferee){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 *通过ID查询数据
	 */
	public ExtProjectTransferee selectExtProjectTransfereeByPrimaryKey(ExtProjectTransferee extProjectTransferee){
  		throw new RuntimeException("操作失败");
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<ExtProjectTransferee> selectForList(ExtProjectTransferee extProjectTransferee){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	public boolean validateUniqueParam(ExtProjectTransferee extProjectTransferee){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	public void saveOne(ExtProjectTransferee extProjectTransferee){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存多个对象
	*/
	public void multipleSaveAndEdit(ExtProjectTransferee[] objs){
    	throw new RuntimeException("操作失败");
    };
	
}