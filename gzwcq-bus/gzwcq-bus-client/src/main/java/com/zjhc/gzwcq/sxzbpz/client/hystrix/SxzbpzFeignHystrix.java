package com.zjhc.gzwcq.sxzbpz.client.hystrix;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.sxzbpz.client.SxzbpzFeignClient;
import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class SxzbpzFeignHystrix implements SxzbpzFeignClient {


  	/**
     * 列表查询
     */
  	@Override
	public BootstrapTableModel<SxzbpzVo> queryByPage(SxzbpzParam sxzbpzParam){
    	throw new RuntimeException("操作失败");
    }

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	@Override
	public void insert(Sxzbpz sxzbpz){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行删除，
	 */
	@Override
	public void deleteByPrimaryKeys(Map<String, Object> map){
    	throw new RuntimeException("操作失败");
    };

  	/**
	 * 按对象中的主键进行删除，
	 */
	@Override
	public void deleteByPrimaryKey(String id){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	@Override
	public void updateIgnoreNull(Sxzbpz sxzbpz){
    	throw new RuntimeException("操作失败");
    };

	/**
	* 更新
	*/
	@Override
  	public void update(Sxzbpz sxzbpz){
    	throw new RuntimeException("操作失败");
    };

	/**
	 *通过ID查询数据
	 */
	@Override
	public SxzbpzVo selectSxzbpzByPrimaryKey(Sxzbpz sxzbpz){
  		throw new RuntimeException("操作失败");
	};

	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@Override
	public List<Sxzbpz> selectForList(Sxzbpz sxzbpz){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@Override
	public boolean validateUniqueParam(Sxzbpz sxzbpz){
    	throw new RuntimeException("操作失败");
    };

	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@Override
	public void saveOne(Sxzbpz sxzbpz){
    	throw new RuntimeException("操作失败");
    };

	/**
	* 保存多个对象
	*/
	@Override
	public void multipleSaveAndEdit(Sxzbpz[] objs){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按条件获取业务相关表的所有字段,按表名分组展示
	 */
	@Override
	public List<SxzbpzVo> loadFields(SxzbpzParam param) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public void saveBatch(String ids, Sxzbpz sxzbpz) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public List<SxzbpzVo> loadByIndexType(SxzbpzParam param) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public List<SxzbpzVo> loadByFieldNameCn(Sxzbpz sxzbpz) {
		throw new RuntimeException("操作失败");
	}
}