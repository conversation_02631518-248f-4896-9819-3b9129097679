package com.zjhc.gzwcq.jbxxb.client;

import java.io.IOException;
import java.util.Map;
import java.util.List;

import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.itextpdf.text.DocumentException;
import com.zjhc.gzwcq.jbxxb.entity.*;
import com.zjhc.gzwcq.jbxxb.client.hystrix.JbxxbFeignHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@FeignClient(path = "gzwcq/jwt/jbxxbRemoteApi", value = "gzwcq-bus-service",fallback=JbxxbFeignHystrix.class)
public interface JbxxbFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<JbxxbVo> queryByPage(@RequestBody JbxxbParam jbxxbParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody Jbxxb jbxxb);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody Jbxxb jbxxb);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody Jbxxb jbxxb);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectJbxxbByPrimaryKey",method=RequestMethod.POST)
	JbxxbVo selectJbxxbByPrimaryKey(@RequestBody Jbxxb jbxxb);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<Jbxxb> selectForList(@RequestBody Jbxxb jbxxb);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody Jbxxb jbxxb);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody Jbxxb jbxxb);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody Jbxxb[] objs);


	/**
	 * 分页查询未办工商列表
	 */
	@RequestMapping(value = "/loadWbgs",method = RequestMethod.POST)
	BootstrapTableModel<JbxxbVo> loadWbgsByPage(JbxxbParam jbxxbParam);

	/**
	 * 合伙企业保存
	 * */
	@RequestMapping(value="/savePartnership",method=RequestMethod.POST)
	String savePartnership(@RequestBody FormVo vo);

	/**
	 * 国有资本保存
	 * */
	@RequestMapping(value="/saveGovernmentCapital",method=RequestMethod.POST)
	String saveGovernmentCapital(@RequestBody FormVo vo);

	/**
	 * 判断企业是否存在办理中业务
	 */
	@RequestMapping(value = "/hasProcessing",method = RequestMethod.POST)
	boolean hasProcessing(@RequestBody Jbxxb jbxxb);

	/**
	 * 根据组织id获取其最新的状态数据
	 */
	@RequestMapping(value = "/loadRecent",method = RequestMethod.POST)
	JbxxbVo loadRecentApprovedByUnitId(Jbxxb jbxxb);

	/**
	 * 是否国家出资企业主业(有交集为是,1:是;2:否)
	 */
	@RequestMapping(value = "/isMainBusiness",method = RequestMethod.POST)
    byte isMainBusiness(Jbxxb jbxxb);

	/**
	 * 比较当前填报中的企业的基本信息与其最新审核通过的基本信息的不同
	 */
	@RequestMapping(value = "/jbxxCompare",method = RequestMethod.POST)
    Map<String,Object> jbxxCompare(Jbxxb jbxxb) throws IllegalAccessException;

	/**
	 * 是否境外转投境内企业(1:是;2:否)
	 */
	@RequestMapping(value = "/isJwToJn",method = RequestMethod.POST)
	byte isJwToJn(Jbxxb jbxxb);

	/**
	 * 导出基本信息表excel
	 */
	@RequestMapping(value = "/export",method = RequestMethod.POST)
    ResponseEntity<byte[]> export(Jbxxb jbxxb) throws IOException;

	/**
	 * 根据主键获取对象,转为pdf
	 */
	@RequestMapping(value = "/loadPdf",method = RequestMethod.POST)
	ResponseEntity<byte[]> loadPdf(Jbxxb jbxxb) throws IOException;

	/**
	 * 获取所有登记情形
	 */
	@RequestMapping(value = "/loadAllSituations",method = RequestMethod.POST)
    List<DictionaryVo> loadAllSituations();

	/**
	 * 判断企业是否存在下级
	 */
	@RequestMapping(value = "/hasSubordinate",method = RequestMethod.POST)
	boolean hasSubordinate(Jbxxb jbxxb);

	/**
	 * 根据主键获取对象,转为pdf-登记证
	 */
	@RequestMapping(value = "/loadDJZPdf",method = RequestMethod.POST)
	ResponseEntity<byte[]> loadDJZPdf(Jbxxb jbxxb) throws IOException, DocumentException;

	/**
	 * 列表查询
	 */
	@RequestMapping(value="/queryDjByPage",method=RequestMethod.POST)
	public BootstrapTableModel<JbxxbVo> queryByDjPage(@RequestBody JbxxbParam jbxxbParam);

	/**
	 * 根据组织id获取其最新的状态数据(不含信息采集及合规资料)
	 */
	@RequestMapping(value = "/loadRecentJbxxOnly",method = RequestMethod.POST)
	JbxxbVo loadRecentJbxxOnly(Jbxxb jbxxb);
}