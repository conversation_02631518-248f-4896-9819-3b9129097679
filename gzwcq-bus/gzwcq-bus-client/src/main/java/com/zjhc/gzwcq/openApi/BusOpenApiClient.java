package com.zjhc.gzwcq.openApi;

import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.appInfo.entity.AppInfo;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 对外开放API，/jwt/openApi地址不拦截
 */
@FeignClient(path = "gzwcq/jwt/openApi", value = "gzwcq-bus-service")
public interface BusOpenApiClient {

    //=====================================对外接口==================================

    /**
     * 分页获取企业基本信息
     */
    @RequestMapping(value = "/getJbxxbByOrgId", method = RequestMethod.POST)
    public List<JbxxbVo> getJbxxbByOrgId(@RequestParam(value = "pageNumber") Integer pageNumber,
                                         @RequestParam(required = false) Integer limit,
                                         @RequestParam(required = false) String orgId, @RequestParam("appId") String appId);


    /**
     * 分页获取产权树列表
     */
    @RequestMapping(value = "/getOrgListPage", method = RequestMethod.POST)
    public List<SysOrganization> getOrgListPage(@RequestParam("pageNumber") Integer pageNumber, @RequestParam(required = false) Integer limit, @RequestParam("appId") String appId);

    /**
     * 下载附件
     */
    @RequestMapping(value = "/downloadFileForOpenApi", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_VALUE)
    byte[] downloadFileForOpenApi(@RequestParam("attachmentId") String attachmentId,@RequestParam("appId") String appId);

    @ApiResponses({
            @ApiResponse(code = 0, message = "验证成功"),
            @ApiResponse(code = 0501, message = "非法入侵接口，传入的组织id不存在")
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "string", name = "orgId", value = "组织id", required = true)})
    @ApiOperation(value = "企业详细信息获取接口", notes = "通过传入的组织id查询，企业", httpMethod = "GET")
    @RequestMapping("/getJbxxbDetailByOrgId")
    IntegrateDataVO getJbxxbDetailByOrgId(@RequestParam("orgId") String orgId,@RequestParam("appId") String appId);
    @RequestMapping("/getJbxxbOrCzrInfoDetail")
    List<JbxxbInfo> getJbxxbOrCzrInfoDetail(@RequestParam("pageNumber") Integer pageNumber,@RequestParam("appId") String appId,
                                  @RequestParam(name = "limit",required = false, defaultValue = "50") Integer limit, @RequestParam(name = "orgId",required = false, defaultValue = "") String orgId);
    @RequestMapping("/getJbxxbOrCzrInfoDetailTotal")
    Long getJbxxbOrCzrInfoDetailTotal(@RequestParam("appId") String appId,@RequestParam(name = "orgId",required = false, defaultValue = "") String orgId);
    @RequestMapping("/insetApiCallLogs")
    void insetApiCallLogs(@RequestBody ApiCallLogs apiCallLogs);

    @RequestMapping("/selectAppInfo")
    AppInfo selectAppInfo(@RequestParam("appId") String appId);
}