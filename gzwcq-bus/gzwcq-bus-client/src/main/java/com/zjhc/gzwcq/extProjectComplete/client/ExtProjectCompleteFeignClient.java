package com.zjhc.gzwcq.extProjectComplete.client;

import java.util.Map;
import java.util.List;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectComplete;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteVo;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteParam;
import com.zjhc.gzwcq.extProjectComplete.client.hystrix.ExtProjectCompleteFeignHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import org.springframework.web.bind.annotation.ResponseBody;

@FeignClient(path = "gzwcq/jwt/extProjectCompleteRemoteApi", value = "gzwcq-bus-service", fallback = ExtProjectCompleteFeignHystrix.class)
public interface ExtProjectCompleteFeignClient {


    /**
     * 列表查询
     */
    @RequestMapping(value = "/queryByPage", method = RequestMethod.POST)
    public BootstrapTableModel<ExtProjectCompleteVo> queryByPage(@RequestBody ExtProjectCompleteParam extProjectCompleteParam);

    /**
     * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    void insert(@RequestBody ExtProjectComplete extProjectComplete);

    /**
     * 按对象中的主键进行删除，
     */
    @RequestMapping(value = "/deleteByPrimaryKeys", method = RequestMethod.POST)
    void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);

    /**
     * 按对象中的主键进行删除，
     */
    @RequestMapping(value = "/deleteByPrimaryKey", method = RequestMethod.POST)
    void deleteByPrimaryKey(@RequestParam("id") String id);

    /**
     * 按对象中的主键进行所有非空属性的修改
     */
    @RequestMapping(value = "/updateIgnoreNull", method = RequestMethod.POST)
    void updateIgnoreNull(@RequestBody ExtProjectComplete extProjectComplete);

    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    void update(@RequestBody ExtProjectComplete extProjectComplete);

    /**
     * 通过ID查询数据
     */
    @RequestMapping(value = "/selectExtProjectCompleteByPrimaryKey", method = RequestMethod.POST)
    ExtProjectComplete selectExtProjectCompleteByPrimaryKey(@RequestBody ExtProjectComplete extProjectComplete);

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    @RequestMapping(value = "/selectForList", method = RequestMethod.POST)
    List<ExtProjectComplete> selectForList(@RequestBody ExtProjectComplete extProjectComplete);

    /**
     * 数据唯一性验证
     * <P>代码生成，必要时可以使用
     */
    @RequestMapping(value = "/validateUniqueParam", method = RequestMethod.POST)
    boolean validateUniqueParam(@RequestBody ExtProjectComplete extProjectComplete);

    /**
     * 保存单个对象
     * <P>存在则更新，不存在则新增
     */
    @RequestMapping(value = "/saveOne", method = RequestMethod.POST)
    void saveOne(@RequestBody ExtProjectComplete extProjectComplete);

    /**
     * 保存多个对象
     */
    @RequestMapping(value = "/multipleSaveAndEdit", method = RequestMethod.POST)
    void multipleSaveAndEdit(@RequestBody ExtProjectComplete[] objs);

    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    ResponseEnvelope updateStatus(@RequestParam("ids") String ids);

    @RequestMapping(value = "/unreadNumber", method = RequestMethod.POST)
    ResponseEnvelope unreadNumber();
}