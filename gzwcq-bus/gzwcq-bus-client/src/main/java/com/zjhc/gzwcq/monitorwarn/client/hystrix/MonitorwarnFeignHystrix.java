package com.zjhc.gzwcq.monitorwarn.client.hystrix;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;
import com.zjhc.gzwcq.monitorwarn.client.MonitorwarnFeignClient;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@Component
public class MonitorwarnFeignHystrix implements MonitorwarnFeignClient{


  	/**
     * 列表查询
     */
	public BootstrapTableModel<MonitorwarnVo> queryByPage(MonitorwarnParam monitorwarnParam){
    	throw new RuntimeException("操作失败");
    }

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	public void insert(Monitorwarn monitorwarn){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKeys(Map<String, Object> map){
    	throw new RuntimeException("操作失败");
    };

  	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKey(String id){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	public void updateIgnoreNull(Monitorwarn monitorwarn){
    	throw new RuntimeException("操作失败");
    };

	/**
	* 更新
	*/
  	public void update(Monitorwarn monitorwarn){
    	throw new RuntimeException("操作失败");
    };

	/**
	 *通过ID查询数据
	 */
	public Monitorwarn selectMonitorwarnByPrimaryKey(Monitorwarn monitorwarn){
  		throw new RuntimeException("操作失败");
	};

	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Monitorwarn> selectForList(Monitorwarn monitorwarn){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	public boolean validateUniqueParam(Monitorwarn monitorwarn){
    	throw new RuntimeException("操作失败");
    };

	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	public void saveOne(Monitorwarn monitorwarn){
    	throw new RuntimeException("操作失败");
    };

	/**
	* 保存多个对象
	*/
	public void multipleSaveAndEdit(Monitorwarn[] objs){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 全自动预警处理 (发起审核)
	 */
	public String submitWarnReview(Monitorwarn objs){
		throw new RuntimeException("操作失败");
	}

	public BootstrapTableModel<MonitorwarnVo> selectWarnReviewList(MonitorwarnParam monitorwarn) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public void readMessage(Monitorwarn monitorwarn) {
		throw new RuntimeException("操作失败");
	}

}