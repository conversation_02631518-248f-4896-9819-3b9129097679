package com.zjhc.gzwcq.businessInfo.client;

import java.util.Map;
import java.util.List;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.client.hystrix.BusinessInfoFeignHystrix;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@FeignClient(path = "gzwcq/jwt/businessInfoRemoteApi", value = "gzwcq-bus-service",fallback=BusinessInfoFeignHystrix.class)
public interface BusinessInfoFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<BusinessInfoVo> queryByPage(@RequestBody BusinessInfoParam businessInfoParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody BusinessInfo businessInfo);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody BusinessInfo businessInfo);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody BusinessInfo businessInfo);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectBusinessInfoByPrimaryKey",method=RequestMethod.POST)
	BusinessInfo selectBusinessInfoByPrimaryKey(@RequestBody BusinessInfo businessInfo);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<BusinessInfo> selectForList(@RequestBody BusinessInfo businessInfo);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody BusinessInfo businessInfo);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody BusinessInfo businessInfo);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody BusinessInfo[] objs);

	/**
	 *获取企业等级历史记录列表
	 */
	@RequestMapping(value="/loadHistoryList",method=RequestMethod.POST)
	List<BusinessInfoVo> loadHistoryList(@RequestBody BusinessInfoParam param);

	/**
	 *提交审核
	 */
	@RequestMapping(value="/submitReview",method=RequestMethod.POST)
	String submitReview(@RequestBody FormVo formVo);

	/**
	 *审核
	 */
	@RequestMapping(value="/review",method=RequestMethod.POST)
	void review(@RequestBody BusinessInfoParam param);

	@RequestMapping(value="/recallReview",method=RequestMethod.POST)
	ResponseEnvelope recallReview(@RequestBody BusinessInfoParam param);

	@RequestMapping(value ="/selectRecallReview",method=RequestMethod.POST)
	ResponseEnvelope selectRecallReview(@RequestBody BusinessInfoParam param);
	/**
	 *审核轨迹列表查询
	 */
	@RequestMapping(value="/reviewHistoryList",method=RequestMethod.POST)
	List<AuditflowHistoryVo> reviewHistoryList(@RequestBody BusinessInfoParam param);
	/**
	 *待办（待审核/退回）列表查询
	 */
	@RequestMapping(value="/todoList",method=RequestMethod.POST)
	List<BusinessInfoVo> todoList(@RequestBody BusinessInfoParam param);

	/**
	 * 待办（待审核/退回）列表查询(分页+筛选条件)
	 */
	@RequestMapping(value="/todoOrReturnList",method=RequestMethod.POST)
    BootstrapTableModel<BusinessInfoVo> todoOrReturnList(@RequestBody BusinessInfoParam param);

	/**
	 * 当前企业所有审核通过的历史列表和当前记录
	 */
	@RequestMapping(value="/allApprovedAndNowList",method=RequestMethod.POST)
    BootstrapTableModel<BusinessInfoVo> allApprovedAndNowList(@RequestBody BusinessInfo param);

	/**
	 * 工商登记资料补录上报
	 */
	@PostMapping(value = "/suppleSubmitReview")
    String suppleSubmitReview(@RequestBody Jbxxb jbxxb);
	/**
	 * 业务审核 已审核 待列表查询(分页+筛选条件)
	 * */
	@PostMapping(value = "/getPassData")
    BootstrapTableModel<BusinessInfoVo> getPassData(BusinessInfoParam param);
}