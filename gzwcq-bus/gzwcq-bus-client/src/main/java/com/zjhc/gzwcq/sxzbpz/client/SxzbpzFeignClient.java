package com.zjhc.gzwcq.sxzbpz.client;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.sxzbpz.client.hystrix.SxzbpzFeignHystrix;
import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(path = "gzwcq/jwt/sxzbpzRemoteApi", value = "gzwcq-bus-service", fallback = SxzbpzFeignHystrix.class)
public interface SxzbpzFeignClient {


    /**
     * 列表查询
     */
    @RequestMapping(value = "/queryByPage", method = RequestMethod.POST)
    public BootstrapTableModel<SxzbpzVo> queryByPage(@RequestBody SxzbpzParam sxzbpzParam);

    /**
     * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    void insert(@RequestBody Sxzbpz sxzbpz);

    /**
     * 按对象中的主键进行删除，
     */
    @RequestMapping(value = "/deleteByPrimaryKeys", method = RequestMethod.POST)
    void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);

    /**
     * 按对象中的主键进行删除，
     */
    @RequestMapping(value = "/deleteByPrimaryKey", method = RequestMethod.POST)
    void deleteByPrimaryKey(@RequestParam("id") String id);

    /**
     * 按对象中的主键进行所有非空属性的修改
     */
    @RequestMapping(value = "/updateIgnoreNull", method = RequestMethod.POST)
    void updateIgnoreNull(@RequestBody Sxzbpz sxzbpz);

    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    void update(@RequestBody Sxzbpz sxzbpz);

    /**
     * 通过ID查询数据
     */
    @RequestMapping(value = "/selectSxzbpzByPrimaryKey", method = RequestMethod.POST)
    SxzbpzVo selectSxzbpzByPrimaryKey(@RequestBody Sxzbpz sxzbpz);

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    @RequestMapping(value = "/selectForList", method = RequestMethod.POST)
    List<Sxzbpz> selectForList(@RequestBody Sxzbpz sxzbpz);

    /**
     * 数据唯一性验证
     * <P>代码生成，必要时可以使用
     */
    @RequestMapping(value = "/validateUniqueParam", method = RequestMethod.POST)
    boolean validateUniqueParam(@RequestBody Sxzbpz sxzbpz);

    /**
     * 保存单个对象
     * <P>存在则更新，不存在则新增
     */
    @RequestMapping(value = "/saveOne", method = RequestMethod.POST)
    void saveOne(@RequestBody Sxzbpz sxzbpz);

    /**
     * 保存多个对象
     */
    @RequestMapping(value = "/multipleSaveAndEdit", method = RequestMethod.POST)
    void multipleSaveAndEdit(@RequestBody Sxzbpz[] objs);

    /**
     * 按条件获取业务相关表的所有字段,按表名分组展示
     */
    @RequestMapping(value = "/loadFields",method = RequestMethod.POST)
    List<SxzbpzVo> loadFields(@RequestBody SxzbpzParam param);

    /**
     * 批量保存
     */
    @RequestMapping(value = "/saveBatch",method = RequestMethod.POST)
    void saveBatch(@RequestParam("ids") String ids,@RequestBody Sxzbpz sxzbpz);

    /**
     * 获取所有经济行为分析指标,按指标类型分组
     */
    @RequestMapping(value = "/loadByIndexType",method = RequestMethod.POST)
    List<SxzbpzVo> loadByIndexType(@RequestBody SxzbpzParam param);

    @PostMapping(value = "/loadByFieldNameCn")
    List<SxzbpzVo> loadByFieldNameCn(@RequestBody Sxzbpz sxzbpz);
}