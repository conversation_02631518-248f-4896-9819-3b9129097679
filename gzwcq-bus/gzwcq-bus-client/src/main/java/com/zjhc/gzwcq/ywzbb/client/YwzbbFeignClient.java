package com.zjhc.gzwcq.ywzbb.client;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbParam;
import com.zjhc.gzwcq.ywzbb.client.hystrix.YwzbbFeignHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@FeignClient(path = "gzwcq/jwt/ywzbbRemoteApi", value = "gzwcq-bus-service",fallback=YwzbbFeignHystrix.class)
public interface YwzbbFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<YwzbbVo> queryByPage(@RequestBody YwzbbParam ywzbbParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody Ywzbb ywzbb);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody Ywzbb ywzbb);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody Ywzbb ywzbb);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectYwzbbByPrimaryKey",method=RequestMethod.POST)
	YwzbbVo selectYwzbbByPrimaryKey(@RequestBody Ywzbb ywzbb);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<Ywzbb> selectForList(@RequestBody Ywzbb ywzbb);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody Ywzbb ywzbb);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody Ywzbb ywzbb);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody Ywzbb[] objs);
	
}