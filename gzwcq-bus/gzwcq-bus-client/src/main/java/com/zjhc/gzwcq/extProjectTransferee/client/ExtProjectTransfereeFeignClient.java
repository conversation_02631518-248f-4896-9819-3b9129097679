package com.zjhc.gzwcq.extProjectTransferee.client;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeVo;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeParam;
import com.zjhc.gzwcq.extProjectTransferee.client.hystrix.ExtProjectTransfereeFeignHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@FeignClient(path = "gzwcq/jwt/extProjectTransfereeRemoteApi", value = "gzwcq-bus-service",fallback=ExtProjectTransfereeFeignHystrix.class)
public interface ExtProjectTransfereeFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<ExtProjectTransfereeVo> queryByPage(@RequestBody ExtProjectTransfereeParam extProjectTransfereeParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody ExtProjectTransferee extProjectTransferee);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody ExtProjectTransferee extProjectTransferee);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody ExtProjectTransferee extProjectTransferee);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectExtProjectTransfereeByPrimaryKey",method=RequestMethod.POST)
	ExtProjectTransferee selectExtProjectTransfereeByPrimaryKey(@RequestBody ExtProjectTransferee extProjectTransferee);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<ExtProjectTransferee> selectForList(@RequestBody ExtProjectTransferee extProjectTransferee);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody ExtProjectTransferee extProjectTransferee);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody ExtProjectTransferee extProjectTransferee);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody ExtProjectTransferee[] objs);
	
}