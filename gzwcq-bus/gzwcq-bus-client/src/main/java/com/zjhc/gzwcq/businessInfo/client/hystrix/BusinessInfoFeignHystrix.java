package com.zjhc.gzwcq.businessInfo.client.hystrix;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import org.springframework.stereotype.Component;
import com.zjhc.gzwcq.businessInfo.client.BusinessInfoFeignClient;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@Component
public class BusinessInfoFeignHystrix implements BusinessInfoFeignClient {
  
  	
  	/**
     * 列表查询
     */
	public BootstrapTableModel<BusinessInfoVo> queryByPage(BusinessInfoParam businessInfoParam){
    	throw new RuntimeException("操作失败");
    }
  
	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	public void insert(BusinessInfo businessInfo){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKeys(Map<String, Object> map){
    	throw new RuntimeException("操作失败");
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKey(String id){
    	throw new RuntimeException("操作失败");
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	public void updateIgnoreNull(BusinessInfo businessInfo){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 更新
	*/
  	public void update(BusinessInfo businessInfo){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 *通过ID查询数据
	 */
	public BusinessInfo selectBusinessInfoByPrimaryKey(BusinessInfo businessInfo){
  		throw new RuntimeException("操作失败");
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<BusinessInfo> selectForList(BusinessInfo businessInfo){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	public boolean validateUniqueParam(BusinessInfo businessInfo){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	public void saveOne(BusinessInfo businessInfo){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存多个对象
	*/
	public void multipleSaveAndEdit(BusinessInfo[] objs){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 保存多个对象
	 */
	public List<BusinessInfoVo> loadHistoryList(BusinessInfoParam param){
		throw new RuntimeException("操作失败");
	};

	/**
	 * 提交审核
	 */
	public String submitReview(FormVo formVo){
		throw new RuntimeException("上报失败");
	};

	/**
	 * 审核
	 */
	public void review(BusinessInfoParam param){
		throw new RuntimeException("操作失败");
	}

	@Override
	public ResponseEnvelope recallReview(BusinessInfoParam param) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public ResponseEnvelope selectRecallReview(BusinessInfoParam param) {
		throw new RuntimeException("操作失败");
	}
	

	/**
	 * 审核轨迹列表查询
	 */
	public List<AuditflowHistoryVo> reviewHistoryList(BusinessInfoParam param){
		throw new RuntimeException("操作失败");
	};

	/**
	 * 待办（待审核/退回）列表查询
	 */
	public List<BusinessInfoVo> todoList(BusinessInfoParam param){
		throw new RuntimeException("操作失败");
	};

	/**
	 * 待办（待审核/退回）列表查询(分页+筛选条件)
	 */
	public BootstrapTableModel<BusinessInfoVo> todoOrReturnList(BusinessInfoParam param) {
		throw new RuntimeException("操作失败");
	};

	/**
	 * 当前企业所有审核通过的历史列表和当前这条记录
	 */
	public BootstrapTableModel<BusinessInfoVo> allApprovedAndNowList(BusinessInfo param) {
		throw new RuntimeException("操作失败");
	};

	/**
	 * 工商登记资料补录上报
	 */
	@Override
	public String suppleSubmitReview(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	/**
	 * 业务审核 已审核 待列表查询(分页+筛选条件)
	 *
	 * @param param
	 */
	@Override
	public BootstrapTableModel<BusinessInfoVo> getPassData(BusinessInfoParam param) {
		throw new RuntimeException("操作失败");
	}
}