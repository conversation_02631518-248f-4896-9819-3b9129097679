package com.zjhc.gzwcq.monitorwarn.client;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import com.zjhc.gzwcq.monitorwarn.client.hystrix.MonitorwarnFeignHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@FeignClient(path = "gzwcq/jwt/monitorwarnRemoteApi", value = "gzwcq-bus-service",fallback=MonitorwarnFeignHystrix.class)
public interface MonitorwarnFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<MonitorwarnVo> queryByPage(@RequestBody MonitorwarnParam monitorwarnParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody Monitorwarn monitorwarn);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody Monitorwarn monitorwarn);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody Monitorwarn monitorwarn);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectMonitorwarnByPrimaryKey",method=RequestMethod.POST)
	Monitorwarn selectMonitorwarnByPrimaryKey(@RequestBody Monitorwarn monitorwarn);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<Monitorwarn> selectForList(@RequestBody Monitorwarn monitorwarn);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody Monitorwarn monitorwarn);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody Monitorwarn monitorwarn);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody Monitorwarn[] objs);

	/**
	 * 全自动预警处理 (发起审核)
	 * */
	@RequestMapping(value="/submitWarnReview",method=RequestMethod.POST)
	String submitWarnReview(@RequestBody Monitorwarn monitorwarn);

	/**
	 * 全自动预警处理 (发起审核)
	 * */
	@PostMapping(value="/selectWarnReviewList")
	BootstrapTableModel<MonitorwarnVo> selectWarnReviewList(@RequestBody MonitorwarnParam monitorwarn);

	@PostMapping(value="/readMessage")
	void readMessage(@RequestBody Monitorwarn monitorwarn);
}