package com.zjhc.gzwcq.jbxxb.client.hystrix;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.zjhc.gzwcq.jbxxb.entity.*;
import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import com.zjhc.gzwcq.jbxxb.client.JbxxbFeignClient;
import java.util.List;
import java.util.Map;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class JbxxbFeignHystrix implements JbxxbFeignClient {
  
  	
  	/**
     * 列表查询
     */
	public BootstrapTableModel<JbxxbVo> queryByPage(JbxxbParam jbxxbParam){
    	throw new RuntimeException("操作失败");
    }
  
	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	public void insert(Jbxxb jbxxb){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKeys(Map<String, Object> map){
    	throw new RuntimeException("操作失败");
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKey(String id){
    	throw new RuntimeException("操作失败");
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	public void updateIgnoreNull(Jbxxb jbxxb){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 更新
	*/
  	public void update(Jbxxb jbxxb){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 *通过ID查询数据
	 */
	public JbxxbVo selectJbxxbByPrimaryKey(Jbxxb jbxxb){
  		throw new RuntimeException("操作失败");
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Jbxxb> selectForList(Jbxxb jbxxb){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	public boolean validateUniqueParam(Jbxxb jbxxb){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	public void saveOne(Jbxxb jbxxb){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存多个对象
	*/
	public void multipleSaveAndEdit(Jbxxb[] objs){
    	throw new RuntimeException("操作失败");
    }

	@Override
	public String savePartnership(@RequestBody FormVo vo) {
		throw new RuntimeException("保存失败");
	}

	@Override
	public String saveGovernmentCapital(FormVo vo) {
		throw new RuntimeException("保存失败");
	}

	public BootstrapTableModel<JbxxbVo> loadWbgsByPage(JbxxbParam jbxxbParam) {
		throw new RuntimeException("操作失败");
	};

	@Override
	public boolean hasProcessing(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public JbxxbVo loadRecentApprovedByUnitId(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public byte isMainBusiness(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public Map<String, Object> jbxxCompare(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public byte isJwToJn(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public ResponseEntity<byte[]> export(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public ResponseEntity<byte[]> loadPdf(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public List<DictionaryVo> loadAllSituations() {
		throw new RuntimeException("操作失败");
	}

	@Override
	public boolean hasSubordinate(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}

	@Override
	public ResponseEntity<byte[]> loadDJZPdf(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}
	/**
	 * 列表查询
	 */
	public BootstrapTableModel<JbxxbVo> queryByDjPage(JbxxbParam jbxxbParam){
		throw new RuntimeException("操作失败");
	}

	@Override
	public JbxxbVo loadRecentJbxxOnly(Jbxxb jbxxb) {
		throw new RuntimeException("操作失败");
	}
}