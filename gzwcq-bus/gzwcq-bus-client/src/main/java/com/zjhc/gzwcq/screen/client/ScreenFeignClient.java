package com.zjhc.gzwcq.screen.client;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.zjhc.gzwcq.screen.client.hystrix.ScreenFeignHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(path = "gzwcq/jwt/screenRemoteApi", value = "gzwcq-bus-service",fallback= ScreenFeignHystrix.class)
public interface ScreenFeignClient {

    /**
     * 待办事项
     */
    @PostMapping(value="/todoList")
    Map<String,Integer> todoList();

    /**
     * 企业户数(按组织形式)统计
     */
    @PostMapping(value="/companyNumByZzxs")
    List<Map<String,Object>> companyNumByZzxs(@RequestParam(value = "unitId",required = false) String unitId);

    /**
     * 企业户数(按组织形式)统计V2 - 直接从字典获取组织形式名称
     * <AUTHOR>
     */
    @PostMapping(value="/companyNumByZzxsV2")
    List<Map<String,Object>> companyNumByZzxsV2(@RequestParam(value = "unitId",required = false) String unitId);

    /**
     * 企业户数统计(按企业级次)
     */
    @PostMapping(value="/companyNumByLevel")
    List<Map<String,Object>> companyNumByLevel(@RequestParam(value = "unitId",required = false) String unitId);

    /**
     * 企业户数统计(按季度及企业类别)
     */
    @PostMapping(value="/companyNumBySeason")
    List<Map<String,Object>> companyNumBySeason(@RequestParam(value = "unitId",required = false) String unitId,
                                                @RequestParam(value = "year",required = false) String year);
    /**
     * 获取当前组织下拉树
     */
    @PostMapping(value="/loadOrgs")
    List<DictionaryVo> loadOrgs();

    /**
     * 业务考核
     * @return
     */
    @PostMapping(value="/businessAssessment")
    Map<String,Object> businessAssessment(@RequestParam(value = "type",required = false) String type,
                                          @RequestParam(value = "year",required = false) String year);
}