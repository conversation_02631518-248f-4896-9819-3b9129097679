package com.zjhc.gzwcq.placard.client;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.placard.client.hystrix.PlacardFeignHystrix;
import com.zjhc.gzwcq.placard.entity.Placard;
import com.zjhc.gzwcq.placard.entity.PlacardParam;
import com.zjhc.gzwcq.placard.entity.PlacardVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@FeignClient(path = "gzwcq/jwt/placardRemoteApi", value = "gzwcq-bus-service",fallback=PlacardFeignHystrix.class)
public interface PlacardFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<PlacardVo> queryByPage(@RequestBody PlacardParam placardParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody Placard placard);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody Placard placard);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody Placard placard);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectPlacardByPrimaryKey",method=RequestMethod.POST)
	PlacardVo selectPlacardByPrimaryKey(@RequestBody Placard placard);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<Placard> selectForList(@RequestBody Placard placard);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody Placard placard);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody Placard placard);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody Placard[] objs);

	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	@RequestMapping(value="/updateLookTimeAndRead",method=RequestMethod.POST)
	void updateLookTimeAndRead(@RequestBody PlacardVo placard);

	/**
	 * 消息数量
	 */
	@RequestMapping(value="/messageTotal",method=RequestMethod.POST)
	HashMap<String, Long> messageTotal(@RequestBody PlacardVo placard);
}