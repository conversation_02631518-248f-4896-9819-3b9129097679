package com.zjhc.gzwcq.newHome.client;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.newHome.client.hystrix.NewHomeFeignClientHystrix;
import com.zjhc.gzwcq.newHome.dto.BusinessAssessmentDTO;
import com.zjhc.gzwcq.newHome.vo.BusinessTransactionVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:37:51
 **/
@FeignClient(path = "gzwcq/jwt/newHomeApi", value = "gzwcq-bus-service", fallback = NewHomeFeignClientHystrix.class)
public interface NewHomeFeignClient {
    @PostMapping("/selectUserStatus")
    public ResponseEnvelope selectUserStatus();

    @PostMapping("/selectBusinessAssessment")
    public ResponseEnvelope selectBusinessAssessment(@RequestBody BusinessAssessmentDTO dto);

    @PostMapping("/selectBusinessAssessmentInfo")
    public ResponseEnvelope selectBusinessAssessmentInfo(@RequestBody BusinessAssessmentDTO dto);

    @PostMapping("/selectHandleMattersInfo")
    public BootstrapTableModel<BusinessTransactionVO> selectHandleMattersInfo(@RequestBody BusinessAssessmentDTO dto);
}
