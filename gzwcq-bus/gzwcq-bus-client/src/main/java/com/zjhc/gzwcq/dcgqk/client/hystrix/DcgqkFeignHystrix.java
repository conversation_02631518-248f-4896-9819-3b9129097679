package com.zjhc.gzwcq.dcgqk.client.hystrix;

import org.springframework.stereotype.Component;
import com.zjhc.gzwcq.dcgqk.client.DcgqkFeignClient;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkParam;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@Component
public class DcgqkFeignHystrix implements DcgqkFeignClient {
  
  	
  	/**
     * 列表查询
     */
	public BootstrapTableModel<DcgqkVo> queryByPage(DcgqkParam dcgqkParam){
    	throw new RuntimeException("操作失败");
    }

	@Override
	public BootstrapTableModel<DcgqkVo> loadByJbxxbId(DcgqkParam dcgqkParam) {
		throw new RuntimeException("操作失败");
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	public void insert(Dcgqk dcgqk){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKeys(Map<String, Object> map){
    	throw new RuntimeException("操作失败");
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKey(String id){
    	throw new RuntimeException("操作失败");
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	public void updateIgnoreNull(Dcgqk dcgqk){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 更新
	*/
  	public void update(Dcgqk dcgqk){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 *通过ID查询数据
	 */
	public Dcgqk selectDcgqkByPrimaryKey(Dcgqk dcgqk){
  		throw new RuntimeException("操作失败");
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Dcgqk> selectForList(Dcgqk dcgqk){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	public boolean validateUniqueParam(Dcgqk dcgqk){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	public void saveOne(Dcgqk dcgqk){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存多个对象
	*/
	public void multipleSaveAndEdit(Dcgqk[] objs){
    	throw new RuntimeException("操作失败");
    };
	
}