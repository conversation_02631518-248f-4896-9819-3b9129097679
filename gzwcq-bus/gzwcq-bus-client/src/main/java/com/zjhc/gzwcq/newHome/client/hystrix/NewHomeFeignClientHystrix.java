package com.zjhc.gzwcq.newHome.client.hystrix;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.newHome.client.NewHomeFeignClient;
import com.zjhc.gzwcq.newHome.dto.BusinessAssessmentDTO;
import com.zjhc.gzwcq.newHome.vo.BusinessTransactionVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:38:52
 **/
public class NewHomeFeignClientHystrix implements NewHomeFeignClient {
    @Override
    public ResponseEnvelope selectUserStatus() {
        return null;
    }

    @Override
    public ResponseEnvelope selectBusinessAssessment(BusinessAssessmentDTO dto) {
        return null;
    }

    @Override
    public ResponseEnvelope selectBusinessAssessmentInfo(BusinessAssessmentDTO dto) {
        return null;
    }

    @Override
    public BootstrapTableModel<BusinessTransactionVO> selectHandleMattersInfo(BusinessAssessmentDTO dto) {
        return null;
    }
}
