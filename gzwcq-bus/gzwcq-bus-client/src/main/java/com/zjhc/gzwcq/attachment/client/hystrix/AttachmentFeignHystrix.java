package com.zjhc.gzwcq.attachment.client.hystrix;

import feign.Response;
import org.springframework.stereotype.Component;
import com.zjhc.gzwcq.attachment.client.AttachmentFeignClient;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.entity.AttachmentParam;
import com.zjhc.gzwcq.attachment.entity.AttachmentVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class AttachmentFeignHystrix implements AttachmentFeignClient {
  
  	
  	/**
     * 列表查询
     */
	public BootstrapTableModel<AttachmentVo> queryByPage(AttachmentParam attachmentParam){
    	throw new RuntimeException("操作失败");
    }
  
	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	public void insert(Attachment attachment){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKeys(Map<String, Object> map){
    	throw new RuntimeException("操作失败");
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	public void deleteByPrimaryKey(String id){
    	throw new RuntimeException("操作失败");
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	public void updateIgnoreNull(Attachment attachment){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 更新
	*/
  	public void update(Attachment attachment){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 *通过ID查询数据
	 */
	public Attachment selectAttachmentByPrimaryKey(Attachment attachment){
  		throw new RuntimeException("操作失败");
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Attachment> selectForList(Attachment attachment){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	public boolean validateUniqueParam(Attachment attachment){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	public void saveOne(Attachment attachment){
    	throw new RuntimeException("操作失败");
    };
	
	/**
	* 保存多个对象
	*/
	public void multipleSaveAndEdit(Attachment[] objs){
    	throw new RuntimeException("操作失败");
    };

	/**
	 * 上传文件
	 */
	public Attachment uploadFile(MultipartFile file){
		throw new RuntimeException("操作失败");
	}

	@Override
	public Response download(String ftpPath) {
		throw new RuntimeException("操作失败");
	}


}