package com.zjhc.gzwcq.attachment.client;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.attachment.client.hystrix.AttachmentFeignHystrix;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.entity.AttachmentParam;
import com.zjhc.gzwcq.attachment.entity.AttachmentVo;

import feign.Response;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;

@FeignClient(path = "gzwcq/jwt/attachmentRemoteApi", value = "gzwcq-bus-service",fallback=AttachmentFeignHystrix.class)
public interface AttachmentFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<AttachmentVo> queryByPage(@RequestBody AttachmentParam attachmentParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody Attachment attachment);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody Attachment attachment);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody Attachment attachment);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectAttachmentByPrimaryKey",method=RequestMethod.POST)
	Attachment selectAttachmentByPrimaryKey(@RequestBody Attachment attachment);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<Attachment> selectForList(@RequestBody Attachment attachment);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody Attachment attachment);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody Attachment attachment);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody Attachment[] objs);

	/**
	 * 上传附件
	 */
	@RequestMapping(value="/uploadFile",method=RequestMethod.POST,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	Attachment uploadFile(@RequestPart("file") MultipartFile file);

	
	/**
	 * 下载附件
	 */
	@RequestMapping(value="/download",method=RequestMethod.GET,consumes = MediaType.APPLICATION_JSON_VALUE)
	Response download(@RequestParam("ftpPath") String ftpPath);
	

	/**
	 * 支持feign传递文件
	 * */
	class MyConfig {
        @Bean
        public Encoder feignFormEncoder() {
            return new SpringFormEncoder();
        }
    }
}