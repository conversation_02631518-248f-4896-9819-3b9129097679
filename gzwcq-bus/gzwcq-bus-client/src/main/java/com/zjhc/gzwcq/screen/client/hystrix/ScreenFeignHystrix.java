package com.zjhc.gzwcq.screen.client.hystrix;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.zjhc.gzwcq.screen.client.ScreenFeignClient;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class ScreenFeignHystrix implements ScreenFeignClient {

    @Override
    public Map<String, Integer> todoList() {
        throw new RuntimeException("操作失败");
    }

    @Override
    public List<Map<String,Object>> companyNumByZzxs(String unitId) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 企业户数统计(按组织形式)V2 - 直接从字典获取组织形式名称
     *
     * <AUTHOR>
     */
    @Override
    public List<Map<String,Object>> companyNumByZzxsV2(String unitId) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public List<Map<String, Object>> companyNumByLevel(String unitId) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public List<Map<String, Object>> companyNumBySeason(String unitId,String year) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public List<DictionaryVo> loadOrgs() {
        throw new RuntimeException("操作失败");
    }

    @Override
    public Map<String,Object> businessAssessment(String type,String year) {
        throw new RuntimeException("操作失败");
    }
}
