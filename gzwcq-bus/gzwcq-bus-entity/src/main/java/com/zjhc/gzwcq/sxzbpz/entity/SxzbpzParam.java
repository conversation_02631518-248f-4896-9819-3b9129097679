package com.zjhc.gzwcq.sxzbpz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_sxzbpz <br/>
 *         描述：Sxzbpz查询类 <br/>
 */
@ApiModel(value="Sxzbpz对象",description="sxzbpz")
public class SxzbpzParam extends Sxzbpz{

	private static final long serialVersionUID = 18L;
  	
  	@ApiParam(value="查询页（和偏移量二选一）")
	private int pageNumber;
	
  	@ApiParam(value="每页数量")
	private int limit;
	
  	@ApiParam(value="当前偏移量（和查询页二选一）")
	private int offest;

	@ApiParam(value="经济行为分析选中的登记情形")
	private List<String> situationList;

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public int getOffest() {
		return offest;
	}

	public void setOffest(int offest) {
		this.offest = offest;
	}

	public List<String> getSituationList() {
		return situationList;
	}

	public void setSituationList(List<String> situationList) {
		this.situationList = situationList;
	}
}
