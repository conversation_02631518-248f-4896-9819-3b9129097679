package com.zjhc.gzwcq.placard.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_placard <br/>
 *         描述：cq_placard <br/>
 */
public class PlacardVo extends Placard {

	private static final long serialVersionUID = 18L;

	private List<PlacardVo> placardList;

	private String createUserStr;//创建人姓名

	private String lastUpdateUserStr;//更新人姓名

	private String readStatus;//读的状态 已读y 未读n

	public PlacardVo() {
		super();
	}

  	public PlacardVo(String id) {
  		super();
  		this.id = id;
	}

	public List<PlacardVo> getPlacardList() {
		return placardList;
	}

	public void setPlacardList(List<PlacardVo> placardList) {
		this.placardList = placardList;
	}

	public String getCreateUserStr() {
		return createUserStr;
	}

	public void setCreateUserStr(String createUserStr) {
		this.createUserStr = createUserStr;
	}

	public String getLastUpdateUserStr() {
		return lastUpdateUserStr;
	}

	public void setLastUpdateUserStr(String lastUpdateUserStr) {
		this.lastUpdateUserStr = lastUpdateUserStr;
	}

	public String getReadStatus() {
		return readStatus;
	}

	public void setReadStatus(String readStatus) {
		this.readStatus = readStatus;
	}
}
