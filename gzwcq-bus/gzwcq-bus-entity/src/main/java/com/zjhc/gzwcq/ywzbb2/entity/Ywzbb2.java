package com.zjhc.gzwcq.ywzbb2.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ywzbb_2 <br/>
 *         描述：产权业务指标表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Ywzbb2 implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键(同业务指标表ID)")
	protected String id;// 主键(同业务指标表ID)
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="股东情况登记表_理由描述")
	protected String zlGdqkdjbLy;// 股东情况登记表_理由描述
  	@ApiParam(value="其他")
	protected String zlQt;// 其他
  	@ApiParam(value="工商注销证明_工商部门名称")
	protected String zlGszxzmGsbmmc;// 工商注销证明_工商部门名称
  	@ApiParam(value="有无非货币出资")
	protected String xxYwfhbcz;// 有无非货币出资
  	@ApiParam(value="有无非货币支付收购价款")
	protected String xxYwfhbzfsgjk;// 有无非货币支付收购价款
  	@ApiParam(value="标的企业评估净资产值(万元)")
	protected BigDecimal xxBdqypgjzcz;// 标的企业评估净资产值(万元)
  	@ApiParam(value="备注")
	protected String xxBz;// 备注
  	@ApiParam(value="标的企业审计净资产值(万元)")
	protected BigDecimal xxBdqysjjzcz;// 标的企业审计净资产值(万元)
  	@ApiParam(value="置换方（一）企业名称")
	protected String xxZhyqymc;// 置换方（一）企业名称
  	@ApiParam(value="置换方（一）所属国资监管机构")
	protected String xxZhyssgzjgjg;// 置换方（一）所属国资监管机构
  	@ApiParam(value="置换方（一）所属国家出资企业")
	protected String xxZhyssgjczqy;// 置换方（一）所属国家出资企业
  	@ApiParam(value="置换方（一）用于置换的资产评估值(万元)")
	protected BigDecimal xxZhyyyzhdzcpgz;// 置换方（一）用于置换的资产评估值(万元)
  	@ApiParam(value="置换方（一）备注")
	protected String xxZhybz;// 置换方（一）备注
  	@ApiParam(value="置换方（二）企业名称")
	protected String xxZheqymc;// 置换方（二）企业名称
  	@ApiParam(value="置换方（二）所属国资监管机构")
	protected String xxZhessgzjgjg;// 置换方（二）所属国资监管机构
  	@ApiParam(value="置换方（二）所属国家出资企业")
	protected String xxZhessgjczqy;// 置换方（二）所属国家出资企业
  	@ApiParam(value="置换方（二）用于置换的资产评估值(万元)")
	protected BigDecimal xxZheyyzhdzcpgz;// 置换方（二）用于置换的资产评估值(万元)
  	@ApiParam(value="置换方（二）备注")
	protected String xxZhebz;// 置换方（二）备注
  	@ApiParam(value="安置费用总额（万元）")
	protected BigDecimal xxAzfyze;// 安置费用总额（万元）
  	@ApiParam(value="核减（增）国有权益（万元）")
	protected BigDecimal xxGyqy;// 核减（增）国有权益（万元）
  	@ApiParam(value="被吸并方名称")
	protected String xxBxbfmc;// 被吸并方名称
  	@ApiParam(value="被吸并方评估净资产值(万元)")
	protected BigDecimal xxBxbfpgjzcz;// 被吸并方评估净资产值(万元)
  	@ApiParam(value="吸并方评估净资产值（万元）")
	protected BigDecimal xxXbfpgjzcz;// 吸并方评估净资产值（万元）
  	@ApiParam(value="被分立企业评估净资产值(万元)")
	protected BigDecimal xxBflqypgjzcz;// 被分立企业评估净资产值(万元)
  	@ApiParam(value="存续企业评估净资产值（万元）")
	protected BigDecimal xxCxqypgjzcz;// 存续企业评估净资产值（万元）
  	@ApiParam(value="存续企业折股标准（万元）")
	protected BigDecimal xxCxqyzgbz;// 存续企业折股标准（万元）
  	@ApiParam(value="用于投资的股权评估值(万元)")
	protected BigDecimal xxYytzdgqpgz;// 用于投资的股权评估值(万元)
  	@ApiParam(value="用于投资的股权作价（万元）")
	protected BigDecimal xxYytzdgqzj;// 用于投资的股权作价（万元）
  	@ApiParam(value="剩余净资产处置收入（万元）")
	protected BigDecimal xxSyjzcczsr;// 剩余净资产处置收入（万元）
  	@ApiParam(value="承接方所属国资监管机构")
	protected String xxCjfssgzjgjg;// 承接方所属国资监管机构
  	@ApiParam(value="标的企业名称")
	protected String xxBdqymc;// 标的企业名称
  	@ApiParam(value="标的企业所属国资监管机构")
	protected String xxBdqyssgzjgjg;// 标的企业所属国资监管机构
  	@ApiParam(value="清算分配职工工资、社保和法定补偿（万元）")
	protected BigDecimal xxQsfpbc;// 清算分配职工工资、社保和法定补偿（万元）
  	@ApiParam(value="清算分配所欠税款（万元）")
	protected BigDecimal xxQsfpsqsk;// 清算分配所欠税款（万元）
  	@ApiParam(value="清算财产是否足以清偿债务")
	protected String xxQsccsfzyqczw;// 清算财产是否足以清偿债务
  	@ApiParam(value="破产分配职工工资、社保和法定补偿（万元）")
	protected BigDecimal xxPcfpbc;// 破产分配职工工资、社保和法定补偿（万元）
  	@ApiParam(value="破产分配所欠税款（万元）")
	protected BigDecimal xxPcfpsqsk;// 破产分配所欠税款（万元）
  	@ApiParam(value="有无非货币减资")
	protected String xxYwfhbjz;// 有无非货币减资
  	@ApiParam(value="出资作价（万元）合计")
	protected BigDecimal fd1Czzjhj;// 出资作价（万元）合计
  	@ApiParam(value="评估值（万元）合计")
	protected BigDecimal fd1Pgzhj;// 评估值（万元）合计
  	@ApiParam(value="支付作价（万元）合计")
	protected BigDecimal fd1Zfzjhj;// 支付作价（万元）合计
  	@ApiParam(value="减资作价(万元)合计")
	protected BigDecimal fd1Jzzjhj;// 减资作价(万元)合计
  	@ApiParam(value="收购价格(万元)合计")
	protected BigDecimal fd2Sgjghj;// 收购价格(万元)合计
  	@ApiParam(value="收购股权的审计净资产值（万元）合计")
	protected BigDecimal fd2Sjjzczhj;// 收购股权的审计净资产值（万元）合计
  	@ApiParam(value="收购股权的评估净资产值（万元）合计")
	protected BigDecimal fd2Pgjzczhj;// 收购股权的评估净资产值（万元）合计
  	@ApiParam(value="转股数量合计")
	protected BigDecimal fd4Zgslhj;// 转股数量合计
  	@ApiParam(value="司法作价（万元）合计")
	protected BigDecimal fd5Sfzjhj;// 司法作价（万元）合计
  	@ApiParam(value="剩余财产分配结果（万元）合计")
	protected BigDecimal fd6Syccfpjghj;// 剩余财产分配结果（万元）合计
  	@ApiParam(value="划转股权比例合计")
	protected BigDecimal fd7Hzgqblhj;// 划转股权比例合计
  	@ApiParam(value="划转净资产值（万元）合计")
	protected BigDecimal fd7Hzjzczhj;// 划转净资产值（万元）合计
  	@ApiParam(value="受让股权审计净资产值（万元）合计")
	protected BigDecimal fd8Srgqsjjzczhj;// 受让股权审计净资产值（万元）合计
  	@ApiParam(value="受让股权评估净资产值（万元）合计")
	protected BigDecimal fd8Srgqpgjzczhj;// 受让股权评估净资产值（万元）合计
  	@ApiParam(value="成交价（万元）合计")
	protected BigDecimal fd8Cjjhj;// 成交价（万元）合计
  	@ApiParam(value="转让股权比例合计")
	protected BigDecimal fd8Zrgqblhj;// 转让股权比例合计
  	@ApiParam(value="转让股权审计净资产值（万元）合计")
	protected BigDecimal fd8Zrgqsjjzczhj;// 转让股权审计净资产值（万元）合计
  	@ApiParam(value="转让股权评估净资产值（万元）合计")
	protected BigDecimal fd8Zrgqpgjzczhj;// 转让股权评估净资产值（万元）合计
  	@ApiParam(value="受让股权比例合计")
	protected BigDecimal fd8Srgqblhj;// 受让股权比例合计
  	@ApiParam(value="收购股权比例合计")
	protected BigDecimal fd2Sggqblhj;// 收购股权比例合计
  	@ApiParam(value="发行股数")
	protected BigDecimal xxFxgs;// 发行股数
  	@ApiParam(value="其中：公开发行股数")
	protected BigDecimal xxGkfxgs;// 其中：公开发行股数
  	@ApiParam(value="安置人员总数")
	protected BigDecimal xxAzryzs;// 安置人员总数
  	@ApiParam(value="被吸并方净资产折合吸并方股权比例")
	protected BigDecimal xxBxbfjzczhxbfgqbl;// 被吸并方净资产折合吸并方股权比例
  	@ApiParam(value="用于投资股权折合标的企业股权比例")
	protected BigDecimal xxYytzgqzhbdqygqbl;// 用于投资股权折合标的企业股权比例
  	@ApiParam(value="附件")
	protected String zlFj;// 附件
  	@ApiParam(value="标的企业评估净资产值(万元)_被增资")
	protected BigDecimal xxBdqypgjzczBzz;// 标的企业评估净资产值(万元)_被增资
  	@ApiParam(value="标的企业评估净资产值(万元)_被减资")
	protected BigDecimal xxBdqypgjzczBjz;// 标的企业评估净资产值(万元)_被减资
  	@ApiParam(value="标的企业评估净资产值(万元)_被改制")
	protected BigDecimal xxBdqypgjzczBgz;// 标的企业评估净资产值(万元)_被改制
  	@ApiParam(value="标的企业审计净资产值(万元)_被改制")
	protected BigDecimal xxBdqysjjzczBgz;// 标的企业审计净资产值(万元)_被改制
  	@ApiParam(value="折股标准(元/股)_新增股本")
	protected BigDecimal xxZgbzXzgb;// 折股标准(元/股)_新增股本
  	@ApiParam(value="折股标准(元/股)_减少")
	protected BigDecimal xxZgbzJs;// 折股标准(元/股)_减少
  	@ApiParam(value="折股标准(元/股)_投资新增")
	protected BigDecimal xxZgbzTzxz;// 折股标准(元/股)_投资新增
  	@ApiParam(value="折股标准(元/股)_吸收合并")
	protected BigDecimal xxZgbzXshb;// 折股标准(元/股)_吸收合并
  	@ApiParam(value="折股标准(元/股)_股权出资")
	protected BigDecimal xxZgbzGqcz;// 折股标准(元/股)_股权出资
  	@ApiParam(value="企业名称预先核准通知书_有无")
	protected String zlYxhztzsYw;// 企业名称预先核准通知书_有无
  	@ApiParam(value="企业名称预先核准通知书_理由描述")
	protected String zlYxhztzsLy;// 企业名称预先核准通知书_理由描述
  	@ApiParam(value="企业名称预先核准通知书_核准单位")
	protected String zlYxhztzsHzdw;// 企业名称预先核准通知书_核准单位
  	@ApiParam(value="提示性公告日前30个交易日的每日加权平均价格的算术平均值")
	protected BigDecimal jc30sspj;// 提示性公告日前30个交易日的每日加权平均价格的算术平均值
  	@ApiParam(value="最近一个会计年度上市公司经审计的每股净资产值")
	protected BigDecimal jcMgjzcz;// 最近一个会计年度上市公司经审计的每股净资产值
  	@ApiParam(value="减持股数")
	protected BigDecimal jcJcgs;// 减持股数
  	@ApiParam(value="减持均价")
	protected BigDecimal jcJcjj;// 减持均价
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间
  	@ApiParam(value="清算报告")
	protected String zlQsbg;// 清算报告
  	@ApiParam(value="分立协议书")
	protected String zlFlxys;// 分立协议书
  	@ApiParam(value="工商注销证明")
	protected String zlGszxzm;// 工商注销证明
  	@ApiParam(value="职工代表大会决议")
	protected String zlZgdbdhjy;// 职工代表大会决议
  	@ApiParam(value="股权设置方案文件")
	protected String zlGqszfawj;// 股权设置方案文件
  	@ApiParam(value="注销公告")
	protected String zlZxgg;// 注销公告
  	@ApiParam(value="合并协议书")
	protected String zlHbxys;// 合并协议书
  	@ApiParam(value="基准日审计报告")
	protected String zlJzrsjbg;// 基准日审计报告
  	@ApiParam(value="置换协议")
	protected String zlZhxy;// 置换协议
  	@ApiParam(value="非货币评估备案表")
	protected String zlFhbpgba;// 非货币评估备案表
  	@ApiParam(value="最近一期审计报告")
	protected String zlZjyqsjbg;// 最近一期审计报告
  	@ApiParam(value="评估备案表")
	protected String zlPgba;// 评估备案表
  	@ApiParam(value="标的评估备案表")
	protected String zlBdpgba;// 标的评估备案表
  	@ApiParam(value="无偿划转协议")
	protected String zlWchzxy;// 无偿划转协议
  	@ApiParam(value="减资公告")
	protected String zlJzgg;// 减资公告
  	@ApiParam(value="营业执照")
	protected String zlYyzz;// 营业执照
  	@ApiParam(value="审计报告")
	protected String zlSjbg;// 审计报告
  	@ApiParam(value="验资报告")
	protected String zlYzbg;// 验资报告
  	@ApiParam(value="企业章程")
	protected String zlQyzc;// 企业章程
  	@ApiParam(value="股东情况登记表")
	protected String zlGdqkdjb;// 股东情况登记表
  	@ApiParam(value="置换一评估备案表")
	protected String zlZhypgbab;// 置换一评估备案表
  	@ApiParam(value="剩余资产处置协议")
	protected String zlSyzcczxy;// 剩余资产处置协议
  	@ApiParam(value="投资评估备案表")
	protected String zlTjpgbab;// 投资评估备案表
  	@ApiParam(value="股权转让协议")
	protected String zlGqzr;// 股权转让协议
  	@ApiParam(value="置换二评估备案表")
	protected String zlZhepgbab;// 置换二评估备案表
  	@ApiParam(value="吸并评估备案表")
	protected String zlXbpgbab;// 吸并评估备案表
  	@ApiParam(value="投资协议")
	protected String zlTzxy;// 投资协议
  	@ApiParam(value="被吸并评估备案表")
	protected String zlBxbpgbab;// 被吸并评估备案表
  	@ApiParam(value="国有土地管理部门备案")
	protected String zlGytdba;// 国有土地管理部门备案
  	@ApiParam(value="进场交易交割")
	protected String zlJcjg;// 进场交易交割
  	@ApiParam(value="经济行为决策文件")
	protected String zlJcwj;// 经济行为决策文件
  	@ApiParam(value="破产公告")
	protected String zlPcgg;// 破产公告
	@ApiParam(value="预先核准通知书")
	protected String zlYxhztzs;// 预先核准通知书

	public Ywzbb2() {
		super();
	}
	
  	public Ywzbb2(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getZlGdqkdjbLy() {
		return zlGdqkdjbLy;
	}

	public String getZlYxhztzs() {
		return zlYxhztzs;
	}

	public void setZlYxhztzs(String zlYxhztzs) {
		this.zlYxhztzs = zlYxhztzs;
	}

	public void setZlGdqkdjbLy(String zlGdqkdjbLy) {
		this.zlGdqkdjbLy = zlGdqkdjbLy;
	}
	public String getZlQt() {
		return zlQt;
	}
	public void setZlQt(String zlQt) {
		this.zlQt = zlQt;
	}
	public String getZlGszxzmGsbmmc() {
		return zlGszxzmGsbmmc;
	}
	public void setZlGszxzmGsbmmc(String zlGszxzmGsbmmc) {
		this.zlGszxzmGsbmmc = zlGszxzmGsbmmc;
	}
	public String getXxYwfhbcz() {
		return xxYwfhbcz;
	}
	public void setXxYwfhbcz(String xxYwfhbcz) {
		this.xxYwfhbcz = xxYwfhbcz;
	}
	public String getXxYwfhbzfsgjk() {
		return xxYwfhbzfsgjk;
	}
	public void setXxYwfhbzfsgjk(String xxYwfhbzfsgjk) {
		this.xxYwfhbzfsgjk = xxYwfhbzfsgjk;
	}
	public BigDecimal getXxBdqypgjzcz() {
		return xxBdqypgjzcz;
	}
	public void setXxBdqypgjzcz(BigDecimal xxBdqypgjzcz) {
		this.xxBdqypgjzcz = xxBdqypgjzcz;
	}
	public String getXxBz() {
		return xxBz;
	}
	public void setXxBz(String xxBz) {
		this.xxBz = xxBz;
	}
	public BigDecimal getXxBdqysjjzcz() {
		return xxBdqysjjzcz;
	}
	public void setXxBdqysjjzcz(BigDecimal xxBdqysjjzcz) {
		this.xxBdqysjjzcz = xxBdqysjjzcz;
	}
	public String getXxZhyqymc() {
		return xxZhyqymc;
	}
	public void setXxZhyqymc(String xxZhyqymc) {
		this.xxZhyqymc = xxZhyqymc;
	}
	public String getXxZhyssgzjgjg() {
		return xxZhyssgzjgjg;
	}
	public void setXxZhyssgzjgjg(String xxZhyssgzjgjg) {
		this.xxZhyssgzjgjg = xxZhyssgzjgjg;
	}
	public String getXxZhyssgjczqy() {
		return xxZhyssgjczqy;
	}
	public void setXxZhyssgjczqy(String xxZhyssgjczqy) {
		this.xxZhyssgjczqy = xxZhyssgjczqy;
	}
	public BigDecimal getXxZhyyyzhdzcpgz() {
		return xxZhyyyzhdzcpgz;
	}
	public void setXxZhyyyzhdzcpgz(BigDecimal xxZhyyyzhdzcpgz) {
		this.xxZhyyyzhdzcpgz = xxZhyyyzhdzcpgz;
	}
	public String getXxZhybz() {
		return xxZhybz;
	}
	public void setXxZhybz(String xxZhybz) {
		this.xxZhybz = xxZhybz;
	}
	public String getXxZheqymc() {
		return xxZheqymc;
	}
	public void setXxZheqymc(String xxZheqymc) {
		this.xxZheqymc = xxZheqymc;
	}
	public String getXxZhessgzjgjg() {
		return xxZhessgzjgjg;
	}
	public void setXxZhessgzjgjg(String xxZhessgzjgjg) {
		this.xxZhessgzjgjg = xxZhessgzjgjg;
	}
	public String getXxZhessgjczqy() {
		return xxZhessgjczqy;
	}
	public void setXxZhessgjczqy(String xxZhessgjczqy) {
		this.xxZhessgjczqy = xxZhessgjczqy;
	}
	public BigDecimal getXxZheyyzhdzcpgz() {
		return xxZheyyzhdzcpgz;
	}
	public void setXxZheyyzhdzcpgz(BigDecimal xxZheyyzhdzcpgz) {
		this.xxZheyyzhdzcpgz = xxZheyyzhdzcpgz;
	}
	public String getXxZhebz() {
		return xxZhebz;
	}
	public void setXxZhebz(String xxZhebz) {
		this.xxZhebz = xxZhebz;
	}
	public BigDecimal getXxAzfyze() {
		return xxAzfyze;
	}
	public void setXxAzfyze(BigDecimal xxAzfyze) {
		this.xxAzfyze = xxAzfyze;
	}
	public BigDecimal getXxGyqy() {
		return xxGyqy;
	}
	public void setXxGyqy(BigDecimal xxGyqy) {
		this.xxGyqy = xxGyqy;
	}
	public String getXxBxbfmc() {
		return xxBxbfmc;
	}
	public void setXxBxbfmc(String xxBxbfmc) {
		this.xxBxbfmc = xxBxbfmc;
	}
	public BigDecimal getXxBxbfpgjzcz() {
		return xxBxbfpgjzcz;
	}
	public void setXxBxbfpgjzcz(BigDecimal xxBxbfpgjzcz) {
		this.xxBxbfpgjzcz = xxBxbfpgjzcz;
	}
	public BigDecimal getXxXbfpgjzcz() {
		return xxXbfpgjzcz;
	}
	public void setXxXbfpgjzcz(BigDecimal xxXbfpgjzcz) {
		this.xxXbfpgjzcz = xxXbfpgjzcz;
	}
	public BigDecimal getXxBflqypgjzcz() {
		return xxBflqypgjzcz;
	}
	public void setXxBflqypgjzcz(BigDecimal xxBflqypgjzcz) {
		this.xxBflqypgjzcz = xxBflqypgjzcz;
	}
	public BigDecimal getXxCxqypgjzcz() {
		return xxCxqypgjzcz;
	}
	public void setXxCxqypgjzcz(BigDecimal xxCxqypgjzcz) {
		this.xxCxqypgjzcz = xxCxqypgjzcz;
	}
	public BigDecimal getXxCxqyzgbz() {
		return xxCxqyzgbz;
	}
	public void setXxCxqyzgbz(BigDecimal xxCxqyzgbz) {
		this.xxCxqyzgbz = xxCxqyzgbz;
	}
	public BigDecimal getXxYytzdgqpgz() {
		return xxYytzdgqpgz;
	}
	public void setXxYytzdgqpgz(BigDecimal xxYytzdgqpgz) {
		this.xxYytzdgqpgz = xxYytzdgqpgz;
	}
	public BigDecimal getXxYytzdgqzj() {
		return xxYytzdgqzj;
	}
	public void setXxYytzdgqzj(BigDecimal xxYytzdgqzj) {
		this.xxYytzdgqzj = xxYytzdgqzj;
	}
	public BigDecimal getXxSyjzcczsr() {
		return xxSyjzcczsr;
	}
	public void setXxSyjzcczsr(BigDecimal xxSyjzcczsr) {
		this.xxSyjzcczsr = xxSyjzcczsr;
	}
	public String getXxCjfssgzjgjg() {
		return xxCjfssgzjgjg;
	}
	public void setXxCjfssgzjgjg(String xxCjfssgzjgjg) {
		this.xxCjfssgzjgjg = xxCjfssgzjgjg;
	}
	public String getXxBdqymc() {
		return xxBdqymc;
	}
	public void setXxBdqymc(String xxBdqymc) {
		this.xxBdqymc = xxBdqymc;
	}
	public String getXxBdqyssgzjgjg() {
		return xxBdqyssgzjgjg;
	}
	public void setXxBdqyssgzjgjg(String xxBdqyssgzjgjg) {
		this.xxBdqyssgzjgjg = xxBdqyssgzjgjg;
	}
	public BigDecimal getXxQsfpbc() {
		return xxQsfpbc;
	}
	public void setXxQsfpbc(BigDecimal xxQsfpbc) {
		this.xxQsfpbc = xxQsfpbc;
	}
	public BigDecimal getXxQsfpsqsk() {
		return xxQsfpsqsk;
	}
	public void setXxQsfpsqsk(BigDecimal xxQsfpsqsk) {
		this.xxQsfpsqsk = xxQsfpsqsk;
	}
	public String getXxQsccsfzyqczw() {
		return xxQsccsfzyqczw;
	}
	public void setXxQsccsfzyqczw(String xxQsccsfzyqczw) {
		this.xxQsccsfzyqczw = xxQsccsfzyqczw;
	}
	public BigDecimal getXxPcfpbc() {
		return xxPcfpbc;
	}
	public void setXxPcfpbc(BigDecimal xxPcfpbc) {
		this.xxPcfpbc = xxPcfpbc;
	}
	public BigDecimal getXxPcfpsqsk() {
		return xxPcfpsqsk;
	}
	public void setXxPcfpsqsk(BigDecimal xxPcfpsqsk) {
		this.xxPcfpsqsk = xxPcfpsqsk;
	}
	public String getXxYwfhbjz() {
		return xxYwfhbjz;
	}
	public void setXxYwfhbjz(String xxYwfhbjz) {
		this.xxYwfhbjz = xxYwfhbjz;
	}
	public BigDecimal getFd1Czzjhj() {
		return fd1Czzjhj;
	}
	public void setFd1Czzjhj(BigDecimal fd1Czzjhj) {
		this.fd1Czzjhj = fd1Czzjhj;
	}
	public BigDecimal getFd1Pgzhj() {
		return fd1Pgzhj;
	}
	public void setFd1Pgzhj(BigDecimal fd1Pgzhj) {
		this.fd1Pgzhj = fd1Pgzhj;
	}
	public BigDecimal getFd1Zfzjhj() {
		return fd1Zfzjhj;
	}
	public void setFd1Zfzjhj(BigDecimal fd1Zfzjhj) {
		this.fd1Zfzjhj = fd1Zfzjhj;
	}
	public BigDecimal getFd1Jzzjhj() {
		return fd1Jzzjhj;
	}
	public void setFd1Jzzjhj(BigDecimal fd1Jzzjhj) {
		this.fd1Jzzjhj = fd1Jzzjhj;
	}
	public BigDecimal getFd2Sgjghj() {
		return fd2Sgjghj;
	}
	public void setFd2Sgjghj(BigDecimal fd2Sgjghj) {
		this.fd2Sgjghj = fd2Sgjghj;
	}
	public BigDecimal getFd2Sjjzczhj() {
		return fd2Sjjzczhj;
	}
	public void setFd2Sjjzczhj(BigDecimal fd2Sjjzczhj) {
		this.fd2Sjjzczhj = fd2Sjjzczhj;
	}
	public BigDecimal getFd2Pgjzczhj() {
		return fd2Pgjzczhj;
	}
	public void setFd2Pgjzczhj(BigDecimal fd2Pgjzczhj) {
		this.fd2Pgjzczhj = fd2Pgjzczhj;
	}
	public BigDecimal getFd4Zgslhj() {
		return fd4Zgslhj;
	}
	public void setFd4Zgslhj(BigDecimal fd4Zgslhj) {
		this.fd4Zgslhj = fd4Zgslhj;
	}
	public BigDecimal getFd5Sfzjhj() {
		return fd5Sfzjhj;
	}
	public void setFd5Sfzjhj(BigDecimal fd5Sfzjhj) {
		this.fd5Sfzjhj = fd5Sfzjhj;
	}
	public BigDecimal getFd6Syccfpjghj() {
		return fd6Syccfpjghj;
	}
	public void setFd6Syccfpjghj(BigDecimal fd6Syccfpjghj) {
		this.fd6Syccfpjghj = fd6Syccfpjghj;
	}
	public BigDecimal getFd7Hzgqblhj() {
		return fd7Hzgqblhj;
	}
	public void setFd7Hzgqblhj(BigDecimal fd7Hzgqblhj) {
		this.fd7Hzgqblhj = fd7Hzgqblhj;
	}
	public BigDecimal getFd7Hzjzczhj() {
		return fd7Hzjzczhj;
	}
	public void setFd7Hzjzczhj(BigDecimal fd7Hzjzczhj) {
		this.fd7Hzjzczhj = fd7Hzjzczhj;
	}
	public BigDecimal getFd8Srgqsjjzczhj() {
		return fd8Srgqsjjzczhj;
	}
	public void setFd8Srgqsjjzczhj(BigDecimal fd8Srgqsjjzczhj) {
		this.fd8Srgqsjjzczhj = fd8Srgqsjjzczhj;
	}
	public BigDecimal getFd8Srgqpgjzczhj() {
		return fd8Srgqpgjzczhj;
	}
	public void setFd8Srgqpgjzczhj(BigDecimal fd8Srgqpgjzczhj) {
		this.fd8Srgqpgjzczhj = fd8Srgqpgjzczhj;
	}
	public BigDecimal getFd8Cjjhj() {
		return fd8Cjjhj;
	}
	public void setFd8Cjjhj(BigDecimal fd8Cjjhj) {
		this.fd8Cjjhj = fd8Cjjhj;
	}
	public BigDecimal getFd8Zrgqblhj() {
		return fd8Zrgqblhj;
	}
	public void setFd8Zrgqblhj(BigDecimal fd8Zrgqblhj) {
		this.fd8Zrgqblhj = fd8Zrgqblhj;
	}
	public BigDecimal getFd8Zrgqsjjzczhj() {
		return fd8Zrgqsjjzczhj;
	}
	public void setFd8Zrgqsjjzczhj(BigDecimal fd8Zrgqsjjzczhj) {
		this.fd8Zrgqsjjzczhj = fd8Zrgqsjjzczhj;
	}
	public BigDecimal getFd8Zrgqpgjzczhj() {
		return fd8Zrgqpgjzczhj;
	}
	public void setFd8Zrgqpgjzczhj(BigDecimal fd8Zrgqpgjzczhj) {
		this.fd8Zrgqpgjzczhj = fd8Zrgqpgjzczhj;
	}
	public BigDecimal getFd8Srgqblhj() {
		return fd8Srgqblhj;
	}
	public void setFd8Srgqblhj(BigDecimal fd8Srgqblhj) {
		this.fd8Srgqblhj = fd8Srgqblhj;
	}
	public BigDecimal getFd2Sggqblhj() {
		return fd2Sggqblhj;
	}
	public void setFd2Sggqblhj(BigDecimal fd2Sggqblhj) {
		this.fd2Sggqblhj = fd2Sggqblhj;
	}
	public BigDecimal getXxFxgs() {
		return xxFxgs;
	}
	public void setXxFxgs(BigDecimal xxFxgs) {
		this.xxFxgs = xxFxgs;
	}
	public BigDecimal getXxGkfxgs() {
		return xxGkfxgs;
	}
	public void setXxGkfxgs(BigDecimal xxGkfxgs) {
		this.xxGkfxgs = xxGkfxgs;
	}
	public BigDecimal getXxAzryzs() {
		return xxAzryzs;
	}
	public void setXxAzryzs(BigDecimal xxAzryzs) {
		this.xxAzryzs = xxAzryzs;
	}
	public BigDecimal getXxBxbfjzczhxbfgqbl() {
		return xxBxbfjzczhxbfgqbl;
	}
	public void setXxBxbfjzczhxbfgqbl(BigDecimal xxBxbfjzczhxbfgqbl) {
		this.xxBxbfjzczhxbfgqbl = xxBxbfjzczhxbfgqbl;
	}
	public BigDecimal getXxYytzgqzhbdqygqbl() {
		return xxYytzgqzhbdqygqbl;
	}
	public void setXxYytzgqzhbdqygqbl(BigDecimal xxYytzgqzhbdqygqbl) {
		this.xxYytzgqzhbdqygqbl = xxYytzgqzhbdqygqbl;
	}
	public String getZlFj() {
		return zlFj;
	}
	public void setZlFj(String zlFj) {
		this.zlFj = zlFj;
	}
	public BigDecimal getXxBdqypgjzczBzz() {
		return xxBdqypgjzczBzz;
	}
	public void setXxBdqypgjzczBzz(BigDecimal xxBdqypgjzczBzz) {
		this.xxBdqypgjzczBzz = xxBdqypgjzczBzz;
	}
	public BigDecimal getXxBdqypgjzczBjz() {
		return xxBdqypgjzczBjz;
	}
	public void setXxBdqypgjzczBjz(BigDecimal xxBdqypgjzczBjz) {
		this.xxBdqypgjzczBjz = xxBdqypgjzczBjz;
	}
	public BigDecimal getXxBdqypgjzczBgz() {
		return xxBdqypgjzczBgz;
	}
	public void setXxBdqypgjzczBgz(BigDecimal xxBdqypgjzczBgz) {
		this.xxBdqypgjzczBgz = xxBdqypgjzczBgz;
	}
	public BigDecimal getXxBdqysjjzczBgz() {
		return xxBdqysjjzczBgz;
	}
	public void setXxBdqysjjzczBgz(BigDecimal xxBdqysjjzczBgz) {
		this.xxBdqysjjzczBgz = xxBdqysjjzczBgz;
	}
	public BigDecimal getXxZgbzXzgb() {
		return xxZgbzXzgb;
	}
	public void setXxZgbzXzgb(BigDecimal xxZgbzXzgb) {
		this.xxZgbzXzgb = xxZgbzXzgb;
	}
	public BigDecimal getXxZgbzJs() {
		return xxZgbzJs;
	}
	public void setXxZgbzJs(BigDecimal xxZgbzJs) {
		this.xxZgbzJs = xxZgbzJs;
	}
	public BigDecimal getXxZgbzTzxz() {
		return xxZgbzTzxz;
	}
	public void setXxZgbzTzxz(BigDecimal xxZgbzTzxz) {
		this.xxZgbzTzxz = xxZgbzTzxz;
	}
	public BigDecimal getXxZgbzXshb() {
		return xxZgbzXshb;
	}
	public void setXxZgbzXshb(BigDecimal xxZgbzXshb) {
		this.xxZgbzXshb = xxZgbzXshb;
	}
	public BigDecimal getXxZgbzGqcz() {
		return xxZgbzGqcz;
	}
	public void setXxZgbzGqcz(BigDecimal xxZgbzGqcz) {
		this.xxZgbzGqcz = xxZgbzGqcz;
	}
	public String getZlYxhztzsYw() {
		return zlYxhztzsYw;
	}
	public void setZlYxhztzsYw(String zlYxhztzsYw) {
		this.zlYxhztzsYw = zlYxhztzsYw;
	}
	public String getZlYxhztzsLy() {
		return zlYxhztzsLy;
	}
	public void setZlYxhztzsLy(String zlYxhztzsLy) {
		this.zlYxhztzsLy = zlYxhztzsLy;
	}
	public String getZlYxhztzsHzdw() {
		return zlYxhztzsHzdw;
	}
	public void setZlYxhztzsHzdw(String zlYxhztzsHzdw) {
		this.zlYxhztzsHzdw = zlYxhztzsHzdw;
	}
	public BigDecimal getJc30sspj() {
		return jc30sspj;
	}
	public void setJc30sspj(BigDecimal jc30sspj) {
		this.jc30sspj = jc30sspj;
	}
	public BigDecimal getJcMgjzcz() {
		return jcMgjzcz;
	}
	public void setJcMgjzcz(BigDecimal jcMgjzcz) {
		this.jcMgjzcz = jcMgjzcz;
	}
	public BigDecimal getJcJcgs() {
		return jcJcgs;
	}
	public void setJcJcgs(BigDecimal jcJcgs) {
		this.jcJcgs = jcJcgs;
	}
	public BigDecimal getJcJcjj() {
		return jcJcjj;
	}
	public void setJcJcjj(BigDecimal jcJcjj) {
		this.jcJcjj = jcJcjj;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	public String getZlQsbg() {
		return zlQsbg;
	}
	public void setZlQsbg(String zlQsbg) {
		this.zlQsbg = zlQsbg;
	}
	public String getZlFlxys() {
		return zlFlxys;
	}
	public void setZlFlxys(String zlFlxys) {
		this.zlFlxys = zlFlxys;
	}
	public String getZlGszxzm() {
		return zlGszxzm;
	}
	public void setZlGszxzm(String zlGszxzm) {
		this.zlGszxzm = zlGszxzm;
	}
	public String getZlZgdbdhjy() {
		return zlZgdbdhjy;
	}
	public void setZlZgdbdhjy(String zlZgdbdhjy) {
		this.zlZgdbdhjy = zlZgdbdhjy;
	}
	public String getZlGqszfawj() {
		return zlGqszfawj;
	}
	public void setZlGqszfawj(String zlGqszfawj) {
		this.zlGqszfawj = zlGqszfawj;
	}
	public String getZlZxgg() {
		return zlZxgg;
	}
	public void setZlZxgg(String zlZxgg) {
		this.zlZxgg = zlZxgg;
	}
	public String getZlHbxys() {
		return zlHbxys;
	}
	public void setZlHbxys(String zlHbxys) {
		this.zlHbxys = zlHbxys;
	}
	public String getZlJzrsjbg() {
		return zlJzrsjbg;
	}
	public void setZlJzrsjbg(String zlJzrsjbg) {
		this.zlJzrsjbg = zlJzrsjbg;
	}
	public String getZlZhxy() {
		return zlZhxy;
	}
	public void setZlZhxy(String zlZhxy) {
		this.zlZhxy = zlZhxy;
	}
	public String getZlFhbpgba() {
		return zlFhbpgba;
	}
	public void setZlFhbpgba(String zlFhbpgba) {
		this.zlFhbpgba = zlFhbpgba;
	}
	public String getZlZjyqsjbg() {
		return zlZjyqsjbg;
	}
	public void setZlZjyqsjbg(String zlZjyqsjbg) {
		this.zlZjyqsjbg = zlZjyqsjbg;
	}
	public String getZlPgba() {
		return zlPgba;
	}
	public void setZlPgba(String zlPgba) {
		this.zlPgba = zlPgba;
	}
	public String getZlBdpgba() {
		return zlBdpgba;
	}
	public void setZlBdpgba(String zlBdpgba) {
		this.zlBdpgba = zlBdpgba;
	}
	public String getZlWchzxy() {
		return zlWchzxy;
	}
	public void setZlWchzxy(String zlWchzxy) {
		this.zlWchzxy = zlWchzxy;
	}
	public String getZlJzgg() {
		return zlJzgg;
	}
	public void setZlJzgg(String zlJzgg) {
		this.zlJzgg = zlJzgg;
	}
	public String getZlYyzz() {
		return zlYyzz;
	}
	public void setZlYyzz(String zlYyzz) {
		this.zlYyzz = zlYyzz;
	}
	public String getZlSjbg() {
		return zlSjbg;
	}
	public void setZlSjbg(String zlSjbg) {
		this.zlSjbg = zlSjbg;
	}
	public String getZlYzbg() {
		return zlYzbg;
	}
	public void setZlYzbg(String zlYzbg) {
		this.zlYzbg = zlYzbg;
	}
	public String getZlQyzc() {
		return zlQyzc;
	}
	public void setZlQyzc(String zlQyzc) {
		this.zlQyzc = zlQyzc;
	}
	public String getZlGdqkdjb() {
		return zlGdqkdjb;
	}
	public void setZlGdqkdjb(String zlGdqkdjb) {
		this.zlGdqkdjb = zlGdqkdjb;
	}
	public String getZlZhypgbab() {
		return zlZhypgbab;
	}
	public void setZlZhypgbab(String zlZhypgbab) {
		this.zlZhypgbab = zlZhypgbab;
	}
	public String getZlSyzcczxy() {
		return zlSyzcczxy;
	}
	public void setZlSyzcczxy(String zlSyzcczxy) {
		this.zlSyzcczxy = zlSyzcczxy;
	}
	public String getZlTjpgbab() {
		return zlTjpgbab;
	}
	public void setZlTjpgbab(String zlTjpgbab) {
		this.zlTjpgbab = zlTjpgbab;
	}
	public String getZlGqzr() {
		return zlGqzr;
	}
	public void setZlGqzr(String zlGqzr) {
		this.zlGqzr = zlGqzr;
	}
	public String getZlZhepgbab() {
		return zlZhepgbab;
	}
	public void setZlZhepgbab(String zlZhepgbab) {
		this.zlZhepgbab = zlZhepgbab;
	}
	public String getZlXbpgbab() {
		return zlXbpgbab;
	}
	public void setZlXbpgbab(String zlXbpgbab) {
		this.zlXbpgbab = zlXbpgbab;
	}
	public String getZlTzxy() {
		return zlTzxy;
	}
	public void setZlTzxy(String zlTzxy) {
		this.zlTzxy = zlTzxy;
	}
	public String getZlBxbpgbab() {
		return zlBxbpgbab;
	}
	public void setZlBxbpgbab(String zlBxbpgbab) {
		this.zlBxbpgbab = zlBxbpgbab;
	}
	public String getZlGytdba() {
		return zlGytdba;
	}
	public void setZlGytdba(String zlGytdba) {
		this.zlGytdba = zlGytdba;
	}
	public String getZlJcjg() {
		return zlJcjg;
	}
	public void setZlJcjg(String zlJcjg) {
		this.zlJcjg = zlJcjg;
	}
	public String getZlJcwj() {
		return zlJcwj;
	}
	public void setZlJcwj(String zlJcwj) {
		this.zlJcwj = zlJcwj;
	}
	public String getZlPcgg() {
		return zlPcgg;
	}
	public void setZlPcgg(String zlPcgg) {
		this.zlPcgg = zlPcgg;
	}
}
