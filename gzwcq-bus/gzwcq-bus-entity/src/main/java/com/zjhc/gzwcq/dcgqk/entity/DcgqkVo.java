package com.zjhc.gzwcq.dcgqk.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_dcgqk <br/>
 *         描述：代持股情况浮动表 <br/>
 */
public class DcgqkVo extends Dcgqk {

	private static final long serialVersionUID = 18L;

	private List<DcgqkVo> dcgqkList;

	public DcgqkVo() {
		super();
	}

	 String reasonStr;// 设立原因
	String czrAreaStr;

	 String measuresStr;// 保全措施

	public String getReasonStr() {
		return reasonStr;
	}

	public String getCzrAreaStr() {
		return czrAreaStr;
	}

	public void setCzrAreaStr(String czrAreaStr) {
		this.czrAreaStr = czrAreaStr;
	}

	public void setReasonStr(String reasonStr) {
		this.reasonStr = reasonStr;
	}

	public String getMeasuresStr() {
		return measuresStr;
	}

	public void setMeasuresStr(String measuresStr) {
		this.measuresStr = measuresStr;
	}

	public DcgqkVo(String id) {
  		super();
  		this.id = id;
	}

	public List<DcgqkVo> getDcgqkList() {
		return dcgqkList;
	}

	public void setDcgqkList(List<DcgqkVo> dcgqkList) {
		this.dcgqkList = dcgqkList;
	}

}
