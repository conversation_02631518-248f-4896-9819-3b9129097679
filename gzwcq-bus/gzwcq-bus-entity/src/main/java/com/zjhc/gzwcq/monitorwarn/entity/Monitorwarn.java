package com.zjhc.gzwcq.monitorwarn.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_monitorwarn <br/>
 *         描述：自动预警表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Monitorwarn implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联登记ID")
	protected String jbxxId;// 关联登记ID
  	@ApiParam(value="单位ID 当前预警企业")
	protected String unitid;// 单位ID 当前预警企业
  	@ApiParam(value="引起预警单位ID")
	protected String fromUnitid;// 引起预警单位ID
  	@ApiParam(value="预警大类 1/2 自动/半自动 半自动预警需要在通知中提示")
	protected String changeCategory;// 预警大类 1/2 自动/半自动 半自动预警需要在通知中提示
  	@ApiParam(value="变动类型 QYJC 企业级次 ZZXS 组织形式 CZRXX 出资人信息 QYLB 企业类别")
	protected String changeType;// 变动类型 QYJC 企业级次 ZZXS 组织形式 CZRXX 出资人信息 QYLB 企业类别
  	@ApiParam(value="原企业级次")
	protected String changeQyjcOld;// 原企业级次
  	@ApiParam(value="变动后企业级次")
	protected String changeQyjcNew;// 变动后企业级次
  	@ApiParam(value="需变更的出资人机构代码")
	protected String changeCzrCode;// 需变更的出资人机构代码
  	@ApiParam(value="变动原因")
	protected String changeReason;// 变动原因
  	@ApiParam(value="变动状态 0/1 未处理/已处理")
	protected String changeStatus;// 变动状态 0/1 未处理/已处理
  	@ApiParam(value="变动描述")
	protected String changeInfo;// 变动描述
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="变动时间")
	protected Date createTime;// 变动时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
  	@ApiParam(value="已读人")
	protected String haveRead;// 已读人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Monitorwarn() {
		super();
	}
	
  	public Monitorwarn(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getFromUnitid() {
		return fromUnitid;
	}
	public void setFromUnitid(String fromUnitid) {
		this.fromUnitid = fromUnitid;
	}
	public String getChangeCategory() {
		return changeCategory;
	}
	public void setChangeCategory(String changeCategory) {
		this.changeCategory = changeCategory;
	}
	public String getChangeType() {
		return changeType;
	}
	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}
	public String getChangeQyjcOld() {
		return changeQyjcOld;
	}
	public void setChangeQyjcOld(String changeQyjcOld) {
		this.changeQyjcOld = changeQyjcOld;
	}
	public String getChangeQyjcNew() {
		return changeQyjcNew;
	}
	public void setChangeQyjcNew(String changeQyjcNew) {
		this.changeQyjcNew = changeQyjcNew;
	}
	public String getChangeCzrCode() {
		return changeCzrCode;
	}
	public void setChangeCzrCode(String changeCzrCode) {
		this.changeCzrCode = changeCzrCode;
	}
	public String getChangeReason() {
		return changeReason;
	}
	public void setChangeReason(String changeReason) {
		this.changeReason = changeReason;
	}
	public String getChangeStatus() {
		return changeStatus;
	}
	public void setChangeStatus(String changeStatus) {
		this.changeStatus = changeStatus;
	}
	public String getChangeInfo() {
		return changeInfo;
	}
	public void setChangeInfo(String changeInfo) {
		this.changeInfo = changeInfo;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getHaveRead() {
		return haveRead;
	}

	public void setHaveRead(String haveRead) {
		this.haveRead = haveRead;
	}
}
