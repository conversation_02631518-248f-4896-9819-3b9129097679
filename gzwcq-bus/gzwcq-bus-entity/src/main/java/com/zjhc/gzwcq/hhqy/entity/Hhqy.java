package com.zjhc.gzwcq.hhqy.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_hhqy <br/>
 *         描述：合伙企业信息扩展表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Hhqy implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="有限合伙企业名称")
	protected String hhCompanyName;// 有限合伙企业名称
  	// @ApiParam(value="统一信用代码")
	// protected String hhCreditCode;// 统一信用代码
  	@ApiParam(value="执行事务合伙人")
	protected String hhZxswhhr;// 执行事务合伙人
  	@ApiParam(value="执行事务合伙人统一信用代码")
	protected String hhZxswhhrCode;// 执行事务合伙人统一信用代码
	// @DateTimeFormat(pattern="yyyy-MM-dd")
	// @JsonFormat(pattern = "yyyy-MM-dd")
  	// @ApiParam(value="成立日期")
	// protected Date setupDate;// 成立日期
  	@ApiParam(value="合伙期限")
	protected String hhQx;// 合伙期限
  	@ApiParam(value="主要经营场所")
	protected String hhZyjycs;// 主要经营场所
  	@ApiParam(value="是否私募投资基金")
	protected String hhSfsmtzjj;// 是否私募投资基金
  	@ApiParam(value="经营范围")
	protected String hhJyfw;// 经营范围
  	@ApiParam(value="认缴出资额（万元）")
	protected BigDecimal hhRjcze;// 认缴出资额（万元）
  	@ApiParam(value="认缴出资额币种")
	protected String hhRjczebz;// 认缴出资额币种
  	@ApiParam(value="实缴出资额（万元）")
	protected BigDecimal hhSjcze;// 实缴出资额（万元）
  	@ApiParam(value="实缴出资额币种")
	protected String hhSjczebz;// 实缴出资额币种
  	@ApiParam(value="国家出资企业")
	protected String hhGjczqy;// 国家出资企业
  	@ApiParam(value="国家出资企业统一信用代码")
	protected String hhGjczqyCode;// 国家出资企业统一信用代码
  	@ApiParam(value="出资企业")
	protected String hhCzqy;// 出资企业
  	@ApiParam(value="出资企业统一信用代码")
	protected String hhCzqyCode;// 出资企业统一信用代码
  	@ApiParam(value="合伙协议附件")
	protected String hhHhxy;// 合伙协议附件
  	@ApiParam(value="合计认缴出资额（万元）")
	protected BigDecimal hjrjcze;// 合计认缴出资额（万元）
  	@ApiParam(value="合计认缴出资比例%")
	protected BigDecimal hjrjczbl;// 合计认缴出资比例%
  	@ApiParam(value="合计实缴出资额（万元）")
	protected BigDecimal hjsjcze;// 合计实缴出资额（万元）
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间
	@ApiParam(value="出资企业id")
	protected String hhCzqyId;// 出资企业id
	@ApiParam(value="实收资本(万元)")
	protected BigDecimal hhSszb;// 实收资本(万元)
	@ApiParam(value="认缴（万元）人民币")
	protected BigDecimal hhRjczermb;// 实缴出资额（万元）
	@ApiParam(value="实缴出资额币种人民币")
	protected BigDecimal hhSjczermb;// 实缴出资额币种

	public BigDecimal getHhRjczermb() {
		return hhRjczermb;
	}

	public void setHhRjczermb(BigDecimal hhRjczermb) {
		this.hhRjczermb = hhRjczermb;
	}

	public BigDecimal getHhSjczermb() {
		return hhSjczermb;
	}

	public void setHhSjczermb(BigDecimal hhSjczermb) {
		this.hhSjczermb = hhSjczermb;
	}

	public Hhqy() {
		super();
	}
	
  	public Hhqy(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getHhCompanyName() {
		return hhCompanyName;
	}
	public void setHhCompanyName(String hhCompanyName) {
		this.hhCompanyName = hhCompanyName;
	}
	public String getHhZxswhhr() {
		return hhZxswhhr;
	}
	public void setHhZxswhhr(String hhZxswhhr) {
		this.hhZxswhhr = hhZxswhhr;
	}
	public String getHhZxswhhrCode() {
		return hhZxswhhrCode;
	}
	public void setHhZxswhhrCode(String hhZxswhhrCode) {
		this.hhZxswhhrCode = hhZxswhhrCode;
	}
	public String getHhQx() {
		return hhQx;
	}
	public void setHhQx(String hhQx) {
		this.hhQx = hhQx;
	}
	public String getHhZyjycs() {
		return hhZyjycs;
	}
	public void setHhZyjycs(String hhZyjycs) {
		this.hhZyjycs = hhZyjycs;
	}
	public String getHhSfsmtzjj() {
		return hhSfsmtzjj;
	}
	public void setHhSfsmtzjj(String hhSfsmtzjj) {
		this.hhSfsmtzjj = hhSfsmtzjj;
	}
	public String getHhJyfw() {
		return hhJyfw;
	}
	public void setHhJyfw(String hhJyfw) {
		this.hhJyfw = hhJyfw;
	}
	public BigDecimal getHhRjcze() {
		return hhRjcze;
	}
	public void setHhRjcze(BigDecimal hhRjcze) {
		this.hhRjcze = hhRjcze;
	}
	public String getHhRjczebz() {
		return hhRjczebz;
	}
	public void setHhRjczebz(String hhRjczebz) {
		this.hhRjczebz = hhRjczebz;
	}
	public BigDecimal getHhSjcze() {
		return hhSjcze;
	}
	public void setHhSjcze(BigDecimal hhSjcze) {
		this.hhSjcze = hhSjcze;
	}
	public String getHhSjczebz() {
		return hhSjczebz;
	}
	public void setHhSjczebz(String hhSjczebz) {
		this.hhSjczebz = hhSjczebz;
	}
	public String getHhGjczqy() {
		return hhGjczqy;
	}
	public void setHhGjczqy(String hhGjczqy) {
		this.hhGjczqy = hhGjczqy;
	}
	public String getHhGjczqyCode() {
		return hhGjczqyCode;
	}
	public void setHhGjczqyCode(String hhGjczqyCode) {
		this.hhGjczqyCode = hhGjczqyCode;
	}
	public String getHhCzqy() {
		return hhCzqy;
	}
	public void setHhCzqy(String hhCzqy) {
		this.hhCzqy = hhCzqy;
	}
	public String getHhCzqyCode() {
		return hhCzqyCode;
	}
	public void setHhCzqyCode(String hhCzqyCode) {
		this.hhCzqyCode = hhCzqyCode;
	}
	public String getHhHhxy() {
		return hhHhxy;
	}
	public void setHhHhxy(String hhHhxy) {
		this.hhHhxy = hhHhxy;
	}
	public BigDecimal getHjrjcze() {
		return hjrjcze;
	}
	public void setHjrjcze(BigDecimal hjrjcze) {
		this.hjrjcze = hjrjcze;
	}
	public BigDecimal getHjrjczbl() {
		return hjrjczbl;
	}
	public void setHjrjczbl(BigDecimal hjrjczbl) {
		this.hjrjczbl = hjrjczbl;
	}
	public BigDecimal getHjsjcze() {
		return hjsjcze;
	}
	public void setHjsjcze(BigDecimal hjsjcze) {
		this.hjsjcze = hjsjcze;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getHhCzqyId() {
		return hhCzqyId;
	}

	public void setHhCzqyId(String hhCzqyId) {
		this.hhCzqyId = hhCzqyId;
	}

	public BigDecimal getHhSszb() {
		return hhSszb;
	}

	public void setHhSszb(BigDecimal hhSszb) {
		this.hhSszb = hhSszb;
	}
}
