package com.zjhc.gzwcq.attachment.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： sys_attachment <br/>
 *         描述：附件表 <br/>
 */
public class AttachmentVo extends Attachment {

	private static final long serialVersionUID = 18L;

	private List<AttachmentVo> attachmentList;

	public AttachmentVo() {
		super();
	}

  	public AttachmentVo(String id) {
  		super();
  		this.id = id;
	}

	public List<AttachmentVo> getAttachmentList() {
		return attachmentList;
	}

	public void setAttachmentList(List<AttachmentVo> attachmentList) {
		this.attachmentList = attachmentList;
	}

}
