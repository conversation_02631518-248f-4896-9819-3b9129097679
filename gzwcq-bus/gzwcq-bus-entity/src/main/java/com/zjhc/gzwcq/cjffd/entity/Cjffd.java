package com.zjhc.gzwcq.cjffd.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_cjffd <br/>
 *         描述：浮动 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Cjffd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="UNITID")
	protected String unitid;// UNITID
  	@ApiParam(value="DATATIME")
	protected String datatime;// DATATIME
  	@ApiParam(value="FLOATORDER")
	protected BigDecimal floatorder;// FLOATORDER
  	@ApiParam(value="FD5_CJFMC")
	protected String fd5Cjfmc;// FD5_CJFMC
  	@ApiParam(value="FD5_CJFXZ")
	protected String fd5Cjfxz;// FD5_CJFXZ
  	@ApiParam(value="FD5_SFZJ")
	protected BigDecimal fd5Sfzj;// FD5_SFZJ
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Cjffd() {
		super();
	}
	
  	public Cjffd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public String getFd5Cjfmc() {
		return fd5Cjfmc;
	}
	public void setFd5Cjfmc(String fd5Cjfmc) {
		this.fd5Cjfmc = fd5Cjfmc;
	}
	public String getFd5Cjfxz() {
		return fd5Cjfxz;
	}
	public void setFd5Cjfxz(String fd5Cjfxz) {
		this.fd5Cjfxz = fd5Cjfxz;
	}
	public BigDecimal getFd5Sfzj() {
		return fd5Sfzj;
	}
	public void setFd5Sfzj(BigDecimal fd5Sfzj) {
		this.fd5Sfzj = fd5Sfzj;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
