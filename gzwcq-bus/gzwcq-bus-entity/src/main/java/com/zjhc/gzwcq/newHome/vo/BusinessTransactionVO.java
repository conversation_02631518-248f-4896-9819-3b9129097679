package com.zjhc.gzwcq.newHome.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/05:16:14:14
 **/
@ApiModel("列表返回实体")
@Data
public class BusinessTransactionVO {
    @ApiModelProperty("组织id")
    private String id;
    @ApiModelProperty("组织名称")
    private String name;
    @ApiModelProperty("提交事项数")
    private String submitNum;
    @ApiModelProperty("审核事项数")
    private String auditNum;
    @ApiModelProperty("通过数")
    private String passNum;
    @ApiModelProperty("通过率")
    private String throughRate;
    @ApiModelProperty("退回数")
    private String rejectionNum;
    @ApiModelProperty("退回率")
    private String rejectionRate;
    @ApiModelProperty("序号")
    private String rank;

    public String getAuditNum() {
        return auditNum;
    }

    public void setAuditNum(String auditNum) {
        this.auditNum = auditNum;
    }

    public BusinessTransactionVO(String id, String name, String submitNum, String auditNum, String passNum, String throughRate, String rejectionNum, String rejectionRate, String rank) {
        this.id = id;
        this.name = name;
        this.submitNum = submitNum;
        this.auditNum = auditNum;
        this.passNum = passNum;
        this.throughRate = throughRate;
        this.rejectionNum = rejectionNum;
        this.rejectionRate = rejectionRate;
        this.rank = rank;
    }

    public BusinessTransactionVO() {
    }
}
