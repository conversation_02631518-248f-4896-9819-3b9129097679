package com.zjhc.gzwcq.sxzbpz.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR> <br/>
 *         表名： cq_sxzbpz <br/>
 *         描述：筛选指标配置 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Sxzbpz implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="id")
	protected String id;// id
  	@ApiParam(value="所属表名")
	protected String tableName;// 所属表名
  	@ApiParam(value="表中文名")
	protected String tableNameCn;// 表中文名
  	@ApiParam(value="字段")
	protected String fieldName;// 字段
  	@ApiParam(value="字段中文名")
	protected String fieldNameCn;// 字段中文名
  	@ApiParam(value="指标类型")
	protected String type;// 指标类型
  	@ApiParam(value="字典类型")
	protected String dicType;// 字典类型
  	@ApiParam(value="是否被数据查询使用")
	protected Integer dataSearch;// 是否被数据查询使用
  	@ApiParam(value="是否被经济行为分析使用")
	protected Integer economicBehaviorAnalysis;// 是否被经济行为分析使用
  	@ApiParam(value="是否在统计页展示")
	protected Integer statistics;// 是否在统计页展示
  	@ApiParam(value="是否在明细页展示")
	protected Integer detail;// 是否在明细页展示
  	@ApiParam(value="指标分类")
	protected String indexType;// 指标分类
  	@ApiParam(value="所属情形（多选）")
	protected String situations;// 所属情形（多选）

	public Sxzbpz() {
		super();
	}
	
  	public Sxzbpz(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public String getTableNameCn() {
		return tableNameCn;
	}
	public void setTableNameCn(String tableNameCn) {
		this.tableNameCn = tableNameCn;
	}
	public String getFieldName() {
		return fieldName;
	}
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	public String getFieldNameCn() {
		return fieldNameCn;
	}
	public void setFieldNameCn(String fieldNameCn) {
		this.fieldNameCn = fieldNameCn;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getDicType() {
		return dicType;
	}
	public void setDicType(String dicType) {
		this.dicType = dicType;
	}
	public Integer getDataSearch() {
		return dataSearch;
	}
	public void setDataSearch(Integer dataSearch) {
		this.dataSearch = dataSearch;
	}
	public Integer getEconomicBehaviorAnalysis() {
		return economicBehaviorAnalysis;
	}
	public void setEconomicBehaviorAnalysis(Integer economicBehaviorAnalysis) {
		this.economicBehaviorAnalysis = economicBehaviorAnalysis;
	}
	public Integer getStatistics() {
		return statistics;
	}
	public void setStatistics(Integer statistics) {
		this.statistics = statistics;
	}
	public Integer getDetail() {
		return detail;
	}
	public void setDetail(Integer detail) {
		this.detail = detail;
	}
	public String getIndexType() {
		return indexType;
	}
	public void setIndexType(String indexType) {
		this.indexType = indexType;
	}
	public String getSituations() {
		return situations;
	}
	public void setSituations(String situations) {
		this.situations = situations;
	}
}
