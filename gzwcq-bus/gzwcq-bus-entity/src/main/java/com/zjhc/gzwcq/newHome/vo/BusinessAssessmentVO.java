package com.zjhc.gzwcq.newHome.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:58:01
 **/
@ApiModel("业务考核返回实体")
@Data
public class BusinessAssessmentVO {
    @ApiModelProperty("及时率")
    private String timelinessRatio;
    @ApiModelProperty("通过率")
    private String throughRate;
    @ApiModelProperty("退回率")
    private String rejectionRate;
}
