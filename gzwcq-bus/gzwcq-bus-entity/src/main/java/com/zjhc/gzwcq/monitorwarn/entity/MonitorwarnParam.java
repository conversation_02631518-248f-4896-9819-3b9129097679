package com.zjhc.gzwcq.monitorwarn.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_monitorwarn <br/>
 *         描述：Monitorwarn查询类 <br/>
 */
@ApiModel(value="Monitorwarn对象",description="monitorwarn")
public class MonitorwarnParam extends Monitorwarn{

	private static final long serialVersionUID = 18L;
  	
  	@ApiParam(value="查询页（和偏移量二选一）")
	private int pageNumber;
	
  	@ApiParam(value="每页数量")
	private int limit;
	
  	@ApiParam(value="当前偏移量（和查询页二选一）")
	private int offest;

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public int getOffest() {
		return offest;
	}

	public void setOffest(int offest) {
		this.offest = offest;
	}

	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@ApiParam(value="开始时间")
	protected Date startTime;// 开始时间

	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@ApiParam(value="结束时间")
	protected Date endTime;// 结束时间
	@ApiParam(value="单位编码")
	private String unitCode; //单位编码
	@ApiParam(value="单位名称")
	private String unitName; //单位名称
	@ApiParam(value="预警审核列表权限列表")
	private List<String> auditHostingList; //预警审核列表权限列表
	@ApiParam(value="初审1/复审2")
	private String auditLevel;

	public String getAuditLevel() {
		return auditLevel;
	}

	public void setAuditLevel(String auditLevel) {
		this.auditLevel = auditLevel;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getUnitCode() {
		return unitCode;
	}

	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public List<String> getAuditHostingList() {
		return auditHostingList;
	}

	public void setAuditHostingList(List<String> auditHostingList) {
		this.auditHostingList = auditHostingList;
	}
}
