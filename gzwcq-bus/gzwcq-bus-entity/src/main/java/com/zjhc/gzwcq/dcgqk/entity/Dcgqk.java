package com.zjhc.gzwcq.dcgqk.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_dcgqk <br/>
 *         描述：代持股情况浮动表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Dcgqk implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="企业名称")
	protected String companyName;// 企业名称
  	@ApiParam(value="名义出资人姓名")
	protected String czrName;// 名义出资人姓名
  	@ApiParam(value="名义出资人代持人地区")
	protected String czrArea;// 名义出资人代持人地区
  	@ApiParam(value="名义出资人统一社会信用编码/身份证号")
	protected String czrCode;// 名义出资人统一社会信用编码/身份证号
  	@ApiParam(value="实际出资人")
	protected String sjCzr;// 实际出资人
  	@ApiParam(value="设立原因")
	protected String reason;// 设立原因
  	@ApiParam(value="代持股比例")
	protected Double dcRate;// 代持股比例
  	@ApiParam(value="保全措施")
	protected String measures;// 保全措施
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Dcgqk() {
		super();
	}
	
  	public Dcgqk(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCzrName() {
		return czrName;
	}
	public void setCzrName(String czrName) {
		this.czrName = czrName;
	}
	public String getCzrArea() {
		return czrArea;
	}
	public void setCzrArea(String czrArea) {
		this.czrArea = czrArea;
	}
	public String getCzrCode() {
		return czrCode;
	}
	public void setCzrCode(String czrCode) {
		this.czrCode = czrCode;
	}
	public String getSjCzr() {
		return sjCzr;
	}
	public void setSjCzr(String sjCzr) {
		this.sjCzr = sjCzr;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public Double getDcRate() {
		return dcRate;
	}
	public void setDcRate(Double dcRate) {
		this.dcRate = dcRate;
	}
	public String getMeasures() {
		return measures;
	}
	public void setMeasures(String measures) {
		this.measures = measures;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
