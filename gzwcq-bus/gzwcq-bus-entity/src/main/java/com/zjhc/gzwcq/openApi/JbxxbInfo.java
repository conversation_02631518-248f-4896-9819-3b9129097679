package com.zjhc.gzwcq.openApi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/29:14:22:09
 **/
@Api(tags = "返回表实体类", description = "返回所有基本表单+出资人+资本状态")
public class JbxxbInfo implements Serializable {
    private static final long serialVersionUID = 18L;
    private String id;
    @ApiParam(value="企业类别 国有企业1/合伙企业2 默认国有1")
    private String businessNature;
    @ApiParam(value="单位ID")
    protected String organizationId;// 单位ID
    @ApiParam(value="企业名称")
    private String jbQymc;//企业名称
    @ApiParam(value="组织机构代码")
    private String jbZzjgdm;
    //注册目的
    @ApiParam(value="注册目的")
    private String jbZcmd;
    @ApiParam(value="持股人名称")
    private String jbCgrxm;
    @ApiParam(value="企业设立状态")
    private String jbQyslzt;
    @ApiParam(value="国资监管机构")
    private String jbGzjgjg;
    //国资监管机构明细
    @ApiParam(value="国资监管机构明细")
    private String jbGzwjkjgmx;
    //国家出资企业名称
    @ApiParam(value="国家出资企业名称")
    private String jbGjczqymc;
    @ApiParam(value="国家出资企业编码")
    private String jbGjczqy;
    //与国家出资企业关系
    @ApiParam(value="与国家出资企业关系")
    private String jbYgjczqygx;
    //企业类别
    @ApiParam(value="企业类别")
    private String jbQylb;
    //是否混改企业
    @ApiParam(value="是否混改企业")
    private String jbSfhgqy;
    //组织形式
    @ApiParam(value="组织形式")
    private String jbZzxs;
    //是否上市公司
    @ApiParam(value="是否上市公司")
    private String jbSfss;
    //是否并表
    @ApiParam(value="是否并表")
    private String jbSfbb;
    //企业产权级次
    @ApiParam(value="企业产权级次")
    private String jbQycjc;
    //企业管理级次
    @ApiParam(value="企业管理级次")
    private String jbQyqjc;
    //注册/成立日期
    @ApiParam(value="注册/成立日期")
    private String  jbZclrq;
    //工商登记日期
    @ApiParam(value="工商登记日期")
    private String jbGsdjrq;
    //营业执照住所
    @ApiParam(value="营业执照住所")
    private String jbYyzzdz;
    //注册地
    @ApiParam(value="注册地")
    private String jbZcd;
    //主要出资人统一社会信用编码
    @ApiParam(value="主要出资人统一社会信用编码")
    private String jbZczyhxzjb;
    //主要出资人
    @ApiParam(value="主要出资人")
    private String jbZczy;
    //注册资本币种
    @ApiParam(value="注册资本币种")
    private String jbZczbz;
    //注册资本(万元)
    @ApiParam(value="注册资本(万元)")
    private BigDecimal jbZczb;
    //注册资本人民币(万元)
    @ApiParam(value="注册资本人民币(万元)")
    private BigDecimal jbZczbrmb;
    //国有资本（万元）
    @ApiParam(value="国有资本（万元）")
    private BigDecimal jbGzzb;
    //所属行业/经营范围
    @ApiParam(value="所属行业/经营范围")
    private String jbSshy;
    //是否国家出资企业主业
    @ApiParam(value="是否国家出资企业主业")
    private String jbIsMainBusiness;
    //主要行业1
    @ApiParam(value="主要行业1")
    private String jbZyhy1;
    //主要行业2
    @ApiParam(value="主要行业2")
    private String jbZyhy2;
    //主要行业3
    @ApiParam(value="主要行业3")
    private String jbZyhy3;
    //经营状况
    @ApiParam(value="经营状况")
    private String jbJyzk;
    //是否代管托管
    @ApiParam(value="是否代管托管")
    private String jbSfdtgt;
    //是否存在休眠、停业、歇业等情况
    @ApiParam(value="是否存在休眠、停业、歇业等情况")
    private String jbIsSuspend;
    //境内/境外
    @ApiParam(value="境内/境外")
    private String jbJNJW;
    //是否境外转投境内企业
    @ApiParam(value="是否境外转投境内企业")
    private String jbIsJwToJn;
    //是否已办工商
    @ApiParam(value="是否已办工商")
    private String jbIsBgs;
    //产权登记情形
    @ApiParam(value="产权登记情形")
    private String jbCqdjqx;
    //合伙期限
    @ApiParam(value="合伙期限")
    private String jbHhqx;
    //主要经营场所
    @ApiParam(value="主要经营场所")
    private String jbZyjycs;
    //执行事务合伙人
    @ApiParam(value="执行事务合伙人")
    private String jbZxswhhr;
    //执行事务合伙人统一信用编码
    @ApiParam(value="执行事务合伙人统一信用编码")
    private String jbZxswhhrCode;
    //实收资本/实缴出资额币种
    @ApiParam(value="实收资本/实缴出资额币种")
    private String jbSszbbz;
    //实收资本/实缴出资额(万元)
    @ApiParam(value="实收资本/实缴出资额(万元)")
    private BigDecimal jbSszb;
    //实收资本/实缴出资额人民币(万元)
    @ApiParam(value="实收资本/实缴出资额人民币(万元)")
    private BigDecimal jbSszbrmb;
    //是否为私募投资基金
    @ApiParam(value="是否为私募投资基金")
    private String jbIsSmr;
    //是否计划清理
    @ApiParam(value="是否计划清理")
    private String jbIsPlanClear;
    //计划清理时间
    @ApiParam(value="计划清理时间")
    private String jbPlanClearTime;
    //是否壳公司
    @ApiParam(value="是否壳公司")
    private String jbIsKg;
//是否存在个人代持股
    @ApiParam(value="是否存在个人代持股")
    private String jbIsPersonalCg;
    //是否特殊目的公司
    @ApiParam(value="是否特殊目的公司")
    private String jbSftsmdgs;

    private List<CzfdVo> czTableData;
    private List<HhrqkfdVo> hhrqkfdList;

    public String getOrganizationId() {
        return organizationId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getJbQymc() {
        return jbQymc;
    }

    public void setJbQymc(String jbQymc) {
        this.jbQymc = jbQymc;
    }

    public String getJbZzjgdm() {
        return jbZzjgdm;
    }

    public void setJbZzjgdm(String jbZzjgdm) {
        this.jbZzjgdm = jbZzjgdm;
    }

    public String getJbZcmd() {
        return jbZcmd;
    }

    public void setJbZcmd(String jbZcmd) {
        this.jbZcmd = jbZcmd;
    }

    public String getJbCgrxm() {
        return jbCgrxm;
    }

    public void setJbCgrxm(String jbCgrxm) {
        this.jbCgrxm = jbCgrxm;
    }

    public String getJbQyslzt() {
        return jbQyslzt;
    }

    public void setJbQyslzt(String jbQyslzt) {
        this.jbQyslzt = jbQyslzt;
    }

    public String getJbGzjgjg() {
        return jbGzjgjg;
    }

    public void setJbGzjgjg(String jbGzjgjg) {
        this.jbGzjgjg = jbGzjgjg;
    }

    public String getJbGzwjkjgmx() {
        return jbGzwjkjgmx;
    }

    public void setJbGzwjkjgmx(String jbGzwjkjgmx) {
        this.jbGzwjkjgmx = jbGzwjkjgmx;
    }

    public String getJbGjczqymc() {
        return jbGjczqymc;
    }

    public void setJbGjczqymc(String jbGjczqymc) {
        this.jbGjczqymc = jbGjczqymc;
    }

    public String getJbGjczqy() {
        return jbGjczqy;
    }

    public void setJbGjczqy(String jbGjczqy) {
        this.jbGjczqy = jbGjczqy;
    }

    public String getJbYgjczqygx() {
        return jbYgjczqygx;
    }

    public void setJbYgjczqygx(String jbYgjczqygx) {
        this.jbYgjczqygx = jbYgjczqygx;
    }

    public String getJbQylb() {
        return jbQylb;
    }

    public void setJbQylb(String jbQylb) {
        this.jbQylb = jbQylb;
    }

    public String getJbSfhgqy() {
        return jbSfhgqy;
    }

    public void setJbSfhgqy(String jbSfhgqy) {
        this.jbSfhgqy = jbSfhgqy;
    }

    public String getJbZzxs() {
        return jbZzxs;
    }

    public void setJbZzxs(String jbZzxs) {
        this.jbZzxs = jbZzxs;
    }

    public String getJbSfss() {
        return jbSfss;
    }

    public void setJbSfss(String jbSfss) {
        this.jbSfss = jbSfss;
    }

    public String getJbSfbb() {
        return jbSfbb;
    }

    public void setJbSfbb(String jbSfbb) {
        this.jbSfbb = jbSfbb;
    }

    public String getJbQycjc() {
        return jbQycjc;
    }

    public void setJbQycjc(String jbQycjc) {
        this.jbQycjc = jbQycjc;
    }

    public String getJbQyqjc() {
        return jbQyqjc;
    }

    public void setJbQyqjc(String jbQyqjc) {
        this.jbQyqjc = jbQyqjc;
    }

    public String getJbZclrq() {
        return jbZclrq;
    }

    public void setJbZclrq(String jbZclrq) {
        this.jbZclrq = jbZclrq;
    }

    public String getJbGsdjrq() {
        return jbGsdjrq;
    }

    public void setJbGsdjrq(String jbGsdjrq) {
        this.jbGsdjrq = jbGsdjrq;
    }

    public String getJbYyzzdz() {
        return jbYyzzdz;
    }

    public void setJbYyzzdz(String jbYyzzdz) {
        this.jbYyzzdz = jbYyzzdz;
    }

    public String getJbZcd() {
        return jbZcd;
    }

    public void setJbZcd(String jbZcd) {
        this.jbZcd = jbZcd;
    }

    public String getJbZczyhxzjb() {
        return jbZczyhxzjb;
    }

    public void setJbZczyhxzjb(String jbZczyhxzjb) {
        this.jbZczyhxzjb = jbZczyhxzjb;
    }

    public String getJbZczy() {
        return jbZczy;
    }

    public void setJbZczy(String jbZczy) {
        this.jbZczy = jbZczy;
    }

    public String getJbZczbz() {
        return jbZczbz;
    }

    public void setJbZczbz(String jbZczbz) {
        this.jbZczbz = jbZczbz;
    }

    public BigDecimal getJbZczb() {
        return jbZczb;
    }

    public void setJbZczb(BigDecimal jbZczb) {
        this.jbZczb = jbZczb;
    }

    public BigDecimal getJbZczbrmb() {
        return jbZczbrmb;
    }

    public void setJbZczbrmb(BigDecimal jbZczbrmb) {
        this.jbZczbrmb = jbZczbrmb;
    }

    public BigDecimal getJbGzzb() {
        return jbGzzb;
    }

    public void setJbGzzb(BigDecimal jbGzzb) {
        this.jbGzzb = jbGzzb;
    }

    public String getJbSshy() {
        return jbSshy;
    }

    public void setJbSshy(String jbSshy) {
        this.jbSshy = jbSshy;
    }

    public String getJbIsMainBusiness() {
        return jbIsMainBusiness;
    }

    public void setJbIsMainBusiness(String jbIsMainBusiness) {
        this.jbIsMainBusiness = jbIsMainBusiness;
    }

    public String getJbZyhy1() {
        return jbZyhy1;
    }

    public void setJbZyhy1(String jbZyhy1) {
        this.jbZyhy1 = jbZyhy1;
    }

    public String getJbZyhy2() {
        return jbZyhy2;
    }

    public void setJbZyhy2(String jbZyhy2) {
        this.jbZyhy2 = jbZyhy2;
    }

    public String getJbZyhy3() {
        return jbZyhy3;
    }

    public void setJbZyhy3(String jbZyhy3) {
        this.jbZyhy3 = jbZyhy3;
    }

    public String getJbJyzk() {
        return jbJyzk;
    }

    public void setJbJyzk(String jbJyzk) {
        this.jbJyzk = jbJyzk;
    }

    public String getJbSfdtgt() {
        return jbSfdtgt;
    }

    public void setJbSfdtgt(String jbSfdtgt) {
        this.jbSfdtgt = jbSfdtgt;
    }

    public String getJbIsSuspend() {
        return jbIsSuspend;
    }

    public void setJbIsSuspend(String jbIsSuspend) {
        this.jbIsSuspend = jbIsSuspend;
    }

    public String getJbJNJW() {
        return jbJNJW;
    }

    public void setJbJNJW(String jbJNJW) {
        this.jbJNJW = jbJNJW;
    }

    public String getJbIsJwToJn() {
        return jbIsJwToJn;
    }

    public void setJbIsJwToJn(String jbIsJwToJn) {
        this.jbIsJwToJn = jbIsJwToJn;
    }

    public String getJbIsBgs() {
        return jbIsBgs;
    }

    public void setJbIsBgs(String jbIsBgs) {
        this.jbIsBgs = jbIsBgs;
    }

    public String getJbCqdjqx() {
        return jbCqdjqx;
    }

    public void setJbCqdjqx(String jbCqdjqx) {
        this.jbCqdjqx = jbCqdjqx;
    }

    public String getJbHhqx() {
        return jbHhqx;
    }

    public void setJbHhqx(String jbHhqx) {
        this.jbHhqx = jbHhqx;
    }

    public String getJbZyjycs() {
        return jbZyjycs;
    }

    public void setJbZyjycs(String jbZyjycs) {
        this.jbZyjycs = jbZyjycs;
    }

    public String getJbZxswhhr() {
        return jbZxswhhr;
    }

    public void setJbZxswhhr(String jbZxswhhr) {
        this.jbZxswhhr = jbZxswhhr;
    }

    public String getJbZxswhhrCode() {
        return jbZxswhhrCode;
    }

    public void setJbZxswhhrCode(String jbZxswhhrCode) {
        this.jbZxswhhrCode = jbZxswhhrCode;
    }

    public String getJbSszbbz() {
        return jbSszbbz;
    }

    public String getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(String businessNature) {
        this.businessNature = businessNature;
    }

    public void setJbSszbbz(String jbSszbbz) {
        this.jbSszbbz = jbSszbbz;
    }

    public BigDecimal getJbSszb() {
        return jbSszb;
    }

    public void setJbSszb(BigDecimal jbSszb) {
        this.jbSszb = jbSszb;
    }

    public BigDecimal getJbSszbrmb() {
        return jbSszbrmb;
    }

    public void setJbSszbrmb(BigDecimal jbSszbrmb) {
        this.jbSszbrmb = jbSszbrmb;
    }

    public String getJbIsSmr() {
        return jbIsSmr;
    }

    public void setJbIsSmr(String jbIsSmr) {
        this.jbIsSmr = jbIsSmr;
    }

    public String getJbIsPlanClear() {
        return jbIsPlanClear;
    }

    public void setJbIsPlanClear(String jbIsPlanClear) {
        this.jbIsPlanClear = jbIsPlanClear;
    }

    public String getJbPlanClearTime() {
        return jbPlanClearTime;
    }

    public void setJbPlanClearTime(String jbPlanClearTime) {
        this.jbPlanClearTime = jbPlanClearTime;
    }

    public String getJbIsKg() {
        return jbIsKg;
    }

    public void setJbIsKg(String jbIsKg) {
        this.jbIsKg = jbIsKg;
    }

    public String getJbIsPersonalCg() {
        return jbIsPersonalCg;
    }

    public void setJbIsPersonalCg(String jbIsPersonalCg) {
        this.jbIsPersonalCg = jbIsPersonalCg;
    }

    public String getJbSftsmdgs() {
        return jbSftsmdgs;
    }

    public void setJbSftsmdgs(String jbSftsmdgs) {
        this.jbSftsmdgs = jbSftsmdgs;
    }

    public List<CzfdVo> getCzTableData() {
        return czTableData;
    }

    public void setCzTableData(List<CzfdVo> czTableData) {
        this.czTableData = czTableData;
    }

    public List<HhrqkfdVo> getHhrqkfdList() {
        return hhrqkfdList;
    }

    public void setHhrqkfdList(List<HhrqkfdVo> hhrqkfdList) {
        this.hhrqkfdList = hhrqkfdList;
    }
}
