package com.zjhc.gzwcq.extProjectTransferee.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR> <br/>
 * 表名： cq_ext_project_transferee <br/>
 * 描述：ExtProjectTransferee查询类 <br/>
 */
@ApiModel(value = "ExtProjectTransferee对象", description = "extProjectTransferee")
public class ExtProjectTransfereeParam extends ExtProjectTransferee {

    private static final long serialVersionUID = 18L;
    @ApiParam(value = "开始时间 yyyyMMdd")
    private String startTime;
    @ApiParam(value = "结束时间 yyyyMMdd")
    private String endTime;
    @ApiParam(value = "查询页（和偏移量二选一）")
    private int pageNumber;
    @ApiParam(value = "组织的id")
    private String organizationId;

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @ApiParam(value = "每页数量")
    private int limit;

    @ApiParam(value = "当前偏移量（和查询页二选一）")
    private int offest;

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffest() {
        return offest;
    }

    public void setOffest(int offest) {
        this.offest = offest;
    }
}
