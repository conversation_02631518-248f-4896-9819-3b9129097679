package com.zjhc.gzwcq.jbxxb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cjffd.entity.CjffdVo;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class FormVo implements Serializable {
    private static final long serialVersionUID = 18L;

    @ApiParam(value="企业性质（国有企业/合伙企业 默认国有）")
    protected String businessNature;
    @ApiParam(value="单位ID")
    protected String unitid;// 单位ID
    @ApiParam(value="基本信息id")
    protected String jbxxId;// 基本信息id
    @ApiParam(value="数据时期")
    protected String datatime;// 数据时期
    @ApiParam(value="浮动行顺序号")
    protected BigDecimal floatorder;// 浮动行顺序号
    @ApiParam(value="注册资本(万元)")
    protected BigDecimal jbZczb;// 注册资本(万元)
    @ApiParam(value="国家出资企业申报数")
    protected BigDecimal jbGjsbs;// 国家出资企业申报数
    @ApiParam(value="国家出资审定数")
    protected BigDecimal jbGjsds;// 国家出资审定数
    @ApiParam(value="国有全资法人出资企业申报数")
    protected BigDecimal jbGyfrsbs;// 国有全资法人出资企业申报数
    @ApiParam(value="国有法人出资审定数")
    protected BigDecimal jbGyfrsds;// 国有法人出资审定数
    @ApiParam(value="国有绝对控股法人出资企业申报数")
    protected BigDecimal jbGyjdkgsbs;// 国有绝对控股法人出资企业申报数
    @ApiParam(value="国有绝对控股法人出资审定数")
    protected BigDecimal jbGyjdkgsds;// 国有绝对控股法人出资审定数
    @ApiParam(value="国有实际控制法人出资企业申报数")
    protected BigDecimal jbGysjkzsbs;// 国有实际控制法人出资企业申报数
    @ApiParam(value="国有实际控制法人出资审定数")
    protected BigDecimal jbGysjkzsds;// 国有实际控制法人出资审定数
    @ApiParam(value="其他企业申报数")
    protected BigDecimal jbQtqysbs;// 其他企业申报数
    @ApiParam(value="其他审定数")
    protected BigDecimal jbQtsds;// 其他审定数
    @ApiParam(value="合计企业申报数")
    protected BigDecimal jbHjqysbs;// 合计企业申报数
    @ApiParam(value="合计审定数")
    protected BigDecimal jbHjsds;// 合计审定数
    @ApiParam(value="实际办理日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date jbSjblrq;// 实际办理日期
    @ApiParam(value="合计出资额")
    protected BigDecimal jbHjcze;// 合计出资额
    @ApiParam(value="合计实缴注册金")
    protected BigDecimal jbHjsjzcj;// 合计实缴注册金
    @ApiParam(value="合计比例")
    protected BigDecimal jbHjbl;// 合计比例
    @ApiParam(value="企业名称")
    protected String jbQymc;// 企业名称
    @ApiParam(value="组织机构代码")
    protected String jbZzjgdm;// 组织机构代码
    @ApiParam(value="注册目的")
    protected String jbZcmd;// 注册目的
    @ApiParam(value="持股人姓名")
    protected String jbCgrxm;// 持股人姓名
    @ApiParam(value="计划清理时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date jbJhqlsj;// 计划清理时间
    @ApiParam(value="实际出资人")
    protected String jbSjczr;// 实际出资人
    @ApiParam(value="主要出资企业组织机构代码")
    protected String jbCzrzzjgdm;// 主要出资企业组织机构代码
    @ApiParam(value="合计实缴注册金币种")
    protected BigDecimal jbHjsjzcjbz;// 合计实缴注册金币种
    @ApiParam(value="国家出资审定数币种")
    protected BigDecimal jbGjczsdsbz;// 国家出资审定数币种
    @ApiParam(value="国家出资企业申报数币种")
    protected BigDecimal jbGjczqysbsbz;// 国家出资企业申报数币种
    @ApiParam(value="国有全资法人出资企业申报数币种")
    protected BigDecimal jbGyfrczsbsbz;// 国有全资法人出资企业申报数币种
    @ApiParam(value="国有法人出资审定数币种")
    protected BigDecimal jbGyfrczsdsbz;// 国有法人出资审定数币种
    @ApiParam(value="国有绝对控股法人出资企业申报数币种")
    protected BigDecimal jbGyjdkgfrsbsbz;// 国有绝对控股法人出资企业申报数币种
    @ApiParam(value="国有绝对控股法人出资审定数币种")
    protected BigDecimal jbGyjdkgfrsdsbz;// 国有绝对控股法人出资审定数币种
    @ApiParam(value="国有实际控制法人出资企业申报数币种")
    protected BigDecimal jbGysjkzfrsbsbz;// 国有实际控制法人出资企业申报数币种
    @ApiParam(value="国有实际控制法人出资审定数币种")
    protected BigDecimal jbGysjkzfrsdsbz;// 国有实际控制法人出资审定数币种
    @ApiParam(value="其他企业申报数币种")
    protected BigDecimal jbQtsbsbz;// 其他企业申报数币种
    @ApiParam(value="其他审定数币种")
    protected BigDecimal jbQtqysdsbz;// 其他审定数币种
    @ApiParam(value="合计企业申报数币种")
    protected BigDecimal jbHjqysbsbz;// 合计企业申报数币种
    @ApiParam(value="合计审定数币种")
    protected BigDecimal jbHjsdsbz;// 合计审定数币种
    @ApiParam(value="审核状态  3 审核中（包含待审核、回退等） 4 审核通过 8 填报中/待上报（新增状态，流程未发起=草稿）")
    protected Integer jbShzt;// 审核状态  3 审核中（包含待审核、回退等） 4 审核通过 8 填报中/待上报（新增状态，流程未发起=草稿）
    @ApiParam(value="主要行业")
    protected String jbSshy;// 主要行业
    @ApiParam(value="是否国家出资企业主业")
    protected String jbSfzy;// 是否国家出资企业主业
    @ApiParam(value="组织形式")
    protected String jbZzxs;// 组织形式
    @ApiParam(value="企业类别")
    protected String jbQylb;// 企业类别
    @ApiParam(value="企业级次")
    protected String jbQyjc;// 企业级次
    @ApiParam(value="所属部门")
    protected String jbSsbm;// 所属部门
    @ApiParam(value="经营状况")
    protected String jbJyzk;// 经营状况
    @ApiParam(value="是否特殊目的公司")
    protected String jbSftsmdgs;// 是否特殊目的公司
    @ApiParam(value="是否存在个人代持股")
    protected String jbSfczgrdcg;// 是否存在个人代持股
    @ApiParam(value="国家出资企业")
    protected String jbGjczqy;// 国家出资企业
    @ApiParam(value="企业申报数币种选择")
    protected String jbQysbsbzxz;// 企业申报数币种选择
    @ApiParam(value="审定数币种选择")
    protected String jbSdsbzxz;// 审定数币种选择
    @ApiParam(value="境内境外")
    protected String jbJnjw;// 境内境外
    @ApiParam(value="占有产权登记情形")
    protected String jbZycqdjqx;// 占有产权登记情形
    @ApiParam(value="变动产权登记情形")
    protected String jbBdcqdjqx;// 变动产权登记情形
    @ApiParam(value="注销产权登记情形")
    protected String jbZxcqdjqx;// 注销产权登记情形
    @ApiParam(value="国资监管机构")
    protected String jbGzjgjg;// 国资监管机构
    @ApiParam(value="注册地")
    protected String jbZcd;// 注册地
    @ApiParam(value="工商登记日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date jbGsdjrq;// 工商登记日期
    @ApiParam(value="设立注册日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date jbZcrq;// 设立注册日期
    @ApiParam(value="是否已办工商")
    protected String jbSfybgs;// 是否已办工商
    @ApiParam(value="工商办理状况")
    protected String jbGsblzk;// 工商办理状况
    @ApiParam(value="工商登记相关资料")
    protected String jbGsdjxgzl;// 工商登记相关资料
    @ApiParam(value="是否一致")
    protected String jbSfyz;// 是否一致
    @ApiParam(value="不一致理由")
    protected String jbByzly;// 不一致理由
    @ApiParam(value="主要行业")
    protected String jbZyhy;// 主要行业
    @ApiParam(value="注册地（境外）")
    protected String jbZcdjw;// 注册地（境外）
    @ApiParam(value="合计出资额币种")
    protected BigDecimal jbHjcjebz;// 合计出资额币种
    @ApiParam(value="出资额币种选择")
    protected String jbCzebzxz;// 出资额币种选择
    @ApiParam(value="实缴注册资本币种选择")
    protected String jbSjzczbbzxz;// 实缴注册资本币种选择
    @ApiParam(value="补录前审核状态(0的数据3条，4的数据788条，可忽略)")
    protected Integer jbBlqshzt;// 补录前审核状态(0的数据3条，4的数据788条，可忽略)
    @ApiParam(value="是否境外转投境内")
    protected String jbSfztjn;// 是否境外转投境内
    @ApiParam(value="注册资本币种")
    protected String jbZczbbz;// 注册资本币种
    @ApiParam(value="注册资本（境外）")
    protected BigDecimal jbZczbjw;// 注册资本（境外）
    @ApiParam(value="是否自动预警数据")
    protected String jbSfzdyjsj;// 是否自动预警数据
    @ApiParam(value="是否随机生成代码")
    protected String jbSfsjscdm;// 是否随机生成代码
    @ApiParam(value="行政区域")
    protected String jbXzqy;// 行政区域
    @ApiParam(value="审核通过日期")
    protected String jbShtgrq;// 审核通过日期
    @ApiParam(value="打印流水号")
    protected String jbDylsh;// 打印流水号
    @ApiParam(value="所属国资监管机构")
    protected String jbSsgzjgjg;// 所属国资监管机构
    @ApiParam(value="清理计划")
    protected String jbQljh;// 清理计划
    @ApiParam(value="合计认缴资本数")
    protected BigDecimal jbHjrjzb;// 合计认缴资本数
    @ApiParam(value="认缴资本币种选择")
    protected String jbRjzbbzxz;// 认缴资本币种选择
    @ApiParam(value="合计认缴资本币种")
    protected BigDecimal jbHjrjzbbz;// 合计认缴资本币种
    @ApiParam(value="注册资本币种选择")
    protected String jbZczbbzxz;// 注册资本币种选择
    @ApiParam(value="国有控股出资审定数")
    protected BigDecimal jbGykgcz;// 国有控股出资审定数
    @ApiParam(value="国有控股出资企业申报数")
    protected BigDecimal jbGykgczsbs;// 国有控股出资企业申报数
    @ApiParam(value="创建人")
    protected String createUser;// 创建人
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiParam(value="创建时间")
    protected Date createTime;// 创建时间
    @ApiParam(value="更新人")
    protected String lastUpdateUser;// 更新人
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiParam(value="更新时间")
    protected Date lastUpdateTime;// 更新时间
    @ApiParam(value="国有资本（万元）")
    protected BigDecimal jbGyzb;// 国有资本（万元）
    @ApiParam(value="与国家出资企业的关系")
    protected String jbRela;// 与国家出资企业的关系
    @ApiParam(value="是否混改企业")
    protected String jbHgqy;// 是否混改企业
    @ApiParam(value="登记类型")
    protected String rgType;// 登记类型
    @ApiParam(value="是否上市公司")
    protected String jbSfss;
    @ApiParam(value="是否并表")
    protected String jbSfbb;
    @ApiParam(value="主要经营场所")
    protected String jbQyjycs;
    @ApiParam(value="是否代管托管")
    protected String jbSftgqy;
    @ApiParam(value="是否存在休眠、停业、歇业")
    protected String jbSfczblzt;
    @ApiParam(value="是否壳公司")
    protected String jbSfkzgs;
    @ApiParam(value = "国有主要行业1")
    protected String jbZyhy1;
    @ApiParam(value = "主要行业2")
    protected String jbZyhy2;
    @ApiParam(value = "主要行业3")
    protected String jbZyhy3;
    @ApiParam(value = "企业管理级次")
    protected String jbQygljc;
    @ApiParam(value = "企业设立登记状态")
    protected String jbQyslzt;
    @ApiParam(value = "国资监管机构类型")
    protected String jbQzwjkjglx;
    @ApiParam(value = "国资监管机构明细")
    protected String jbGzwjkjgmx;
    @ApiParam(value = "营业执照住所")
    protected String  jbYyzzzc;
    @ApiParam(value = "")
    protected List<Dcgqk> jbDcggkList;
    @ApiParam(value = "认缴资本币种")
    protected String jbRjzbbz;
    @ApiParam(value = "认缴资本金额")
    protected String jbRjzb;

    @ApiParam(value = "民营企业申报数币种")
    protected String jbMyqysbsbz;

    @ApiParam(value = "民营企业申报数")
    protected String jbMyqysbs;

    @ApiParam(value = "外资企业申报数币种")
    protected String jbWzqysbsbz;

    @ApiParam(value = "外资企业申报数")
    protected String jbWzqysbs;

    @ApiParam(value = "自然人申报数币种")
    protected String jbZrrsbsbz;

    @ApiParam(value = "自然人申报数")
    protected String jbZrrsbs;

    public String getJbRjzbbz() {
        return jbRjzbbz;
    }

    public void setJbRjzbbz(String jbRjzbbz) {
        this.jbRjzbbz = jbRjzbbz;
    }

    public String getJbRjzb() {
        return jbRjzb;
    }

    public void setJbRjzb(String jbRjzb) {
        this.jbRjzb = jbRjzb;
    }

    public List<Dcgqk> getJbDcggkList() {
        return jbDcggkList;
    }

    public String getJbZyhy1() {
        return jbZyhy1;
    }

    public void setJbZyhy1(String jbZyhy1) {
        this.jbZyhy1 = jbZyhy1;
    }

    public void setJbDcggkList(List<Dcgqk> jbDcggkList) {
        this.jbDcggkList = jbDcggkList;
    }

    public String getJbSfss() {
        return jbSfss;
    }

    public void setJbSfss(String jbSfss) {
        this.jbSfss = jbSfss;
    }

    public String getJbSfbb() {
        return jbSfbb;
    }

    public void setJbSfbb(String jbSfbb) {
        this.jbSfbb = jbSfbb;
    }

    public String getJbQyjycs() {
        return jbQyjycs;
    }

    public void setJbQyjycs(String jbQyjycs) {
        this.jbQyjycs = jbQyjycs;
    }

    public String getJbSftgqy() {
        return jbSftgqy;
    }

    public void setJbSftgqy(String jbSftgqy) {
        this.jbSftgqy = jbSftgqy;
    }

    public String getJbSfczblzt() {
        return jbSfczblzt;
    }

    public void setJbSfczblzt(String jbSfczblzt) {
        this.jbSfczblzt = jbSfczblzt;
    }

    public String getJbSfkzgs() {
        return jbSfkzgs;
    }

    public void setJbSfkzgs(String jbSfkzgs) {
        this.jbSfkzgs = jbSfkzgs;
    }

    public String getJbZyhy2() {
        return jbZyhy2;
    }

    public void setJbZyhy2(String jbZyhy2) {
        this.jbZyhy2 = jbZyhy2;
    }

    public String getJbZyhy3() {
        return jbZyhy3;
    }

    public void setJbZyhy3(String jbZyhy3) {
        this.jbZyhy3 = jbZyhy3;
    }

    public String getJbQygljc() {
        return jbQygljc;
    }

    public void setJbQygljc(String jbQygljc) {
        this.jbQygljc = jbQygljc;
    }

    public String getJbQyslzt() {
        return jbQyslzt;
    }

    public void setJbQyslzt(String jbQyslzt) {
        this.jbQyslzt = jbQyslzt;
    }

    public String getJbQzwjkjglx() {
        return jbQzwjkjglx;
    }

    public void setJbQzwjkjglx(String jbQzwjkjglx) {
        this.jbQzwjkjglx = jbQzwjkjglx;
    }

    public String getJbGzwjkjgmx() {
        return jbGzwjkjgmx;
    }

    public void setJbGzwjkjgmx(String jbGzwjkjgmx) {
        this.jbGzwjkjgmx = jbGzwjkjgmx;
    }

    public String getJbYyzzzc() {
        return jbYyzzzc;
    }

    public void setJbYyzzzc(String jbYyzzzc) {
        this.jbYyzzzc = jbYyzzzc;
    }

    protected List<Fhbzcfd> fd1TableData ;
    protected List<Czfd> fdTableData ;
    protected List<Ygqcyffd> fd2TableData ;
    protected List<Hrhcfd> fd7TableData ;
    protected List<Zrsrfd> fd8TableData ;
    protected List<Cgrfd> fd9TableData ;
    protected List<Xgzjgfd> fd3TableData;//新国资机构浮动
    protected List<Syccfpfd> fd6TableData;//


    //Ywzzb-2
    @ApiParam(value="股东情况登记表_理由描述")
    protected String zlGdqkdjbLy;// 股东情况登记表_理由描述
    @ApiParam(value="其他")
    protected String zlQt;// 其他
    @ApiParam(value="工商注销证明_工商部门名称")
    protected String zlGszxzmGsbmmc;// 工商注销证明_工商部门名称
    @ApiParam(value="有无非货币出资")
    protected String xxYwfhbcz;// 有无非货币出资
    @ApiParam(value="有无非货币支付收购价款")
    protected String xxYwfhbzfsgjk;// 有无非货币支付收购价款
    @ApiParam(value="标的企业评估净资产值(万元)")
    protected BigDecimal xxBdqypgjzcz;// 标的企业评估净资产值(万元)
    @ApiParam(value="备注")
    protected String xxBz;// 备注
    @ApiParam(value="标的企业审计净资产值(万元)")
    protected BigDecimal xxBdqysjjzcz;// 标的企业审计净资产值(万元)
    @ApiParam(value="置换方（一）企业名称")
    protected String xxZhyqymc;// 置换方（一）企业名称
    @ApiParam(value="置换方（一）所属国资监管机构")
    protected String xxZhyssgzjgjg;// 置换方（一）所属国资监管机构
    @ApiParam(value="置换方（一）所属国家出资企业")
    protected String xxZhyssgjczqy;// 置换方（一）所属国家出资企业
    @ApiParam(value="置换方（一）用于置换的资产评估值(万元)")
    protected BigDecimal xxZhyyyzhdzcpgz;// 置换方（一）用于置换的资产评估值(万元)
    @ApiParam(value="置换方（一）备注")
    protected String xxZhybz;// 置换方（一）备注
    @ApiParam(value="置换方（二）企业名称")
    protected String xxZheqymc;// 置换方（二）企业名称
    @ApiParam(value="置换方（二）所属国资监管机构")
    protected String xxZhessgzjgjg;// 置换方（二）所属国资监管机构
    @ApiParam(value="置换方（二）所属国家出资企业")
    protected String xxZhessgjczqy;// 置换方（二）所属国家出资企业
    @ApiParam(value="置换方（二）用于置换的资产评估值(万元)")
    protected BigDecimal xxZheyyzhdzcpgz;// 置换方（二）用于置换的资产评估值(万元)
    @ApiParam(value="置换方（二）备注")
    protected String xxZhebz;// 置换方（二）备注
    @ApiParam(value="安置费用总额（万元）")
    protected BigDecimal xxAzfyze;// 安置费用总额（万元）
    @ApiParam(value="核减（增）国有权益（万元）")
    protected BigDecimal xxGyqy;// 核减（增）国有权益（万元）
    @ApiParam(value="被吸并方名称")
    protected String xxBxbfmc;// 被吸并方名称
    @ApiParam(value="被吸并方评估净资产值(万元)")
    protected BigDecimal xxBxbfpgjzcz;// 被吸并方评估净资产值(万元)
    @ApiParam(value="吸并方评估净资产值（万元）")
    protected BigDecimal xxXbfpgjzcz;// 吸并方评估净资产值（万元）
    @ApiParam(value="被分立企业评估净资产值(万元)")
    protected BigDecimal xxBflqypgjzcz;// 被分立企业评估净资产值(万元)
    @ApiParam(value="存续企业评估净资产值（万元）")
    protected BigDecimal xxCxqypgjzcz;// 存续企业评估净资产值（万元）
    @ApiParam(value="存续企业折股标准（万元）")
    protected BigDecimal xxCxqyzgbz;// 存续企业折股标准（万元）
    @ApiParam(value="用于投资的股权评估值(万元)")
    protected BigDecimal xxYytzdgqpgz;// 用于投资的股权评估值(万元)
    @ApiParam(value="用于投资的股权作价（万元）")
    protected BigDecimal xxYytzdgqzj;// 用于投资的股权作价（万元）
    @ApiParam(value="剩余净资产处置收入（万元）")
    protected BigDecimal xxSyjzcczsr;// 剩余净资产处置收入（万元）
    @ApiParam(value="承接方所属国资监管机构")
    protected String xxCjfssgzjgjg;// 承接方所属国资监管机构
    @ApiParam(value="标的企业名称")
    protected String xxBdqymc;// 标的企业名称
    @ApiParam(value="标的企业所属国资监管机构")
    protected String xxBdqyssgzjgjg;// 标的企业所属国资监管机构
    @ApiParam(value="清算分配职工工资、社保和法定补偿（万元）")
    protected BigDecimal xxQsfpbc;// 清算分配职工工资、社保和法定补偿（万元）
    @ApiParam(value="清算分配所欠税款（万元）")
    protected BigDecimal xxQsfpsqsk;// 清算分配所欠税款（万元）
    @ApiParam(value="清算财产是否足以清偿债务")
    protected String xxQsccsfzyqczw;// 清算财产是否足以清偿债务
    @ApiParam(value="破产分配职工工资、社保和法定补偿（万元）")
    protected BigDecimal xxPcfpbc;// 破产分配职工工资、社保和法定补偿（万元）
    @ApiParam(value="破产分配所欠税款（万元）")
    protected BigDecimal xxPcfpsqsk;// 破产分配所欠税款（万元）
    @ApiParam(value="有无非货币减资")
    protected String xxYwfhbjz;// 有无非货币减资
    @ApiParam(value="出资作价（万元）合计")
    protected BigDecimal fd1Czzjhj;// 出资作价（万元）合计
    @ApiParam(value="评估值（万元）合计")
    protected BigDecimal fd1Pgzhj;// 评估值（万元）合计
    @ApiParam(value="支付作价（万元）合计")
    protected BigDecimal fd1Zfzjhj;// 支付作价（万元）合计
    @ApiParam(value="减资作价(万元)合计")
    protected BigDecimal fd1Jzzjhj;// 减资作价(万元)合计
    @ApiParam(value="收购价格(万元)合计")
    protected BigDecimal fd2Sgjghj;// 收购价格(万元)合计
    @ApiParam(value="收购股权的审计净资产值（万元）合计")
    protected BigDecimal fd2Sjjzczhj;// 收购股权的审计净资产值（万元）合计
    @ApiParam(value="收购股权的评估净资产值（万元）合计")
    protected BigDecimal fd2Pgjzczhj;// 收购股权的评估净资产值（万元）合计
    @ApiParam(value="转股数量合计")
    protected BigDecimal fd4Zgslhj;// 转股数量合计
    @ApiParam(value="司法作价（万元）合计")
    protected BigDecimal fd5Sfzjhj;// 司法作价（万元）合计
    @ApiParam(value="剩余财产分配结果（万元）合计")
    protected BigDecimal fd6Syccfpjghj;// 剩余财产分配结果（万元）合计
    @ApiParam(value="划转股权比例合计")
    protected BigDecimal fd7Hzgqblhj;// 划转股权比例合计
    @ApiParam(value="划转净资产值（万元）合计")
    protected BigDecimal fd7Hzjzczhj;// 划转净资产值（万元）合计
    @ApiParam(value="受让股权审计净资产值（万元）合计")
    protected BigDecimal fd8Srgqsjjzczhj;// 受让股权审计净资产值（万元）合计
    @ApiParam(value="受让股权评估净资产值（万元）合计")
    protected BigDecimal fd8Srgqpgjzczhj;// 受让股权评估净资产值（万元）合计
    @ApiParam(value="成交价（万元）合计")
    protected BigDecimal fd8Cjjhj;// 成交价（万元）合计
    @ApiParam(value="转让股权比例合计")
    protected BigDecimal fd8Zrgqblhj;// 转让股权比例合计
    @ApiParam(value="转让股权审计净资产值（万元）合计")
    protected BigDecimal fd8Zrgqsjjzczhj;// 转让股权审计净资产值（万元）合计
    @ApiParam(value="转让股权评估净资产值（万元）合计")
    protected BigDecimal fd8Zrgqpgjzczhj;// 转让股权评估净资产值（万元）合计
    @ApiParam(value="受让股权比例合计")
    protected BigDecimal fd8Srgqblhj;// 受让股权比例合计
    @ApiParam(value="收购股权比例合计")
    protected BigDecimal fd2Sggqblhj;// 收购股权比例合计
    @ApiParam(value="发行股数")
    protected BigDecimal xxFxgs;// 发行股数
    @ApiParam(value="其中：公开发行股数")
    protected BigDecimal xxGkfxgs;// 其中：公开发行股数
    @ApiParam(value="安置人员总数")
    protected BigDecimal xxAzryzs;// 安置人员总数
    @ApiParam(value="被吸并方净资产折合吸并方股权比例")
    protected BigDecimal xxBxbfjzczhxbfgqbl;// 被吸并方净资产折合吸并方股权比例
    @ApiParam(value="用于投资股权折合标的企业股权比例")
    protected BigDecimal xxYytzgqzhbdqygqbl;// 用于投资股权折合标的企业股权比例
    @ApiParam(value="附件")
    protected String zlFj;// 附件
    @ApiParam(value="标的企业评估净资产值(万元)_被增资")
    protected BigDecimal xxBdqypgjzczBzz;// 标的企业评估净资产值(万元)_被增资
    @ApiParam(value="标的企业评估净资产值(万元)_被减资")
    protected BigDecimal xxBdqypgjzczBjz;// 标的企业评估净资产值(万元)_被减资
    @ApiParam(value="标的企业评估净资产值(万元)_被改制")
    protected BigDecimal xxBdqypgjzczBgz;// 标的企业评估净资产值(万元)_被改制
    @ApiParam(value="标的企业审计净资产值(万元)_被改制")
    protected BigDecimal xxBdqysjjzczBgz;// 标的企业审计净资产值(万元)_被改制
    @ApiParam(value="折股标准(元/股)_新增股本")
    protected BigDecimal xxZgbzXzgb;// 折股标准(元/股)_新增股本
    @ApiParam(value="折股标准(元/股)_减少")
    protected BigDecimal xxZgbzJs;// 折股标准(元/股)_减少
    @ApiParam(value="折股标准(元/股)_投资新增")
    protected BigDecimal xxZgbzTzxz;// 折股标准(元/股)_投资新增
    @ApiParam(value="折股标准(元/股)_吸收合并")
    protected BigDecimal xxZgbzXshb;// 折股标准(元/股)_吸收合并
    @ApiParam(value="折股标准(元/股)_股权出资")
    protected BigDecimal xxZgbzGqcz;// 折股标准(元/股)_股权出资
    @ApiParam(value="企业名称预先核准通知书_有无")
    protected String zlYxhztzsYw;// 企业名称预先核准通知书_有无
    @ApiParam(value="企业名称预先核准通知书")
    protected String zlYxhztzs;// 企业名称预先核准通知书
    @ApiParam(value="企业名称预先核准通知书_理由描述")
    protected String zlYxhztzsLy;// 企业名称预先核准通知书_理由描述
    @ApiParam(value="企业名称预先核准通知书_核准单位")
    protected String zlYxhztzsHzdw;// 企业名称预先核准通知书_核准单位
    @ApiParam(value="提示性公告日前30个交易日的每日加权平均价格的算术平均值")
    protected BigDecimal jc30sspj;// 提示性公告日前30个交易日的每日加权平均价格的算术平均值
    @ApiParam(value="最近一个会计年度上市公司经审计的每股净资产值")
    protected BigDecimal jcMgjzcz;// 最近一个会计年度上市公司经审计的每股净资产值
    @ApiParam(value="减持股数")
    protected BigDecimal jcJcgs;// 减持股数
    @ApiParam(value="减持均价")
    protected BigDecimal jcJcjj;// 减持均价
    //YWZBB
    @ApiParam(value="业务指标表id")
    protected String ywzbbId;//业务指标表id
    @ApiParam(value="评估净资产值（万元）")
    protected BigDecimal xxPgjzcz;// 评估净资产值（万元）
    @ApiParam(value="审计净资产值(万元)")
    protected BigDecimal xxSjjzcz;// 审计净资产值(万元)
    @ApiParam(value="发行价格(万元)")
    protected BigDecimal xxFxjg;// 发行价格(万元)
    @ApiParam(value="经济行为决策文件_理由描述")
    protected String zlJcwjLy;// 经济行为决策文件_理由描述
    @ApiParam(value="验资报告_中介机构名称")
    protected String zlYzbgZjjgmc;// 验资报告_中介机构名称
    @ApiParam(value="验资报告_验资报告号")
    protected String zlYzbgYzbgh;// 验资报告_验资报告号
    @ApiParam(value="验资报告_理由描述")
    protected String zlYzbgLy;// 验资报告_理由描述
    @ApiParam(value="验资报告_报告出具日")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlYzbgYzcjr;// 验资报告_报告出具日
    @ApiParam(value="企业章程_理由描述")
    protected String zlQyzcLy;// 企业章程_理由描述
    @ApiParam(value="企业法人营业执照_理由描述")
    protected String zlYyzzLy;// 企业法人营业执照_理由描述
    @ApiParam(value="评估备案表_理由描述")
    protected String zlPgbaLy;// 评估备案表_理由描述
    @ApiParam(value="非货币评估备案表_理由描述")
    protected String zlFhbpgbaLy;// 非货币评估备案表_理由描述
    @ApiParam(value="标的评估备案表_理由描述")
    protected String zlBdpgbaLy;// 标的评估备案表_理由描述
    @ApiParam(value="国有土地管理部门备案_理由描述")
    protected String zlGytdbaLy;// 国有土地管理部门备案_理由描述
    @ApiParam(value="进场交易交割_交易机构")
    protected String zlJcjgJyjg;// 进场交易交割_交易机构
    @ApiParam(value="进场交易交割_交割单/鉴证书号")
    protected String zlJcjgJgd;// 进场交易交割_交割单/鉴证书号
    @ApiParam(value="进场交易交割_出具日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlJcjgCjrq;// 进场交易交割_出具日期
    @ApiParam(value="进场交易交割_理由描述")
    protected String zlJcjgLy;// 进场交易交割_理由描述
    @ApiParam(value="审计报告_中介机构")
    protected String zlSjbgZjjg;// 审计报告_中介机构
    @ApiParam(value="审计报告_报告号")
    protected String zlSjbgBgh;// 审计报告_报告号
    @ApiParam(value="审计报告_出具日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlSjbgCjrq;// 审计报告_出具日期
    @ApiParam(value="审计报告_理由描述")
    protected String zlSjbgLy;// 审计报告_理由描述
    @ApiParam(value="股权转让协议_理由描述")
    protected String zlGqzrLy;// 股权转让协议_理由描述
    @ApiParam(value="置换协议_理由描述")
    protected String zlZhxyLy;// 置换协议_理由描述
    @ApiParam(value="合并协议书_理由描述")
    protected String zlHbxysLy;// 合并协议书_理由描述
    @ApiParam(value="分立协议书_理由描述")
    protected String zlFlxysLy;// 分立协议书_理由描述
    @ApiParam(value="无偿划转协议_理由描述")
    protected String zlWchzxyLy;// 无偿划转协议_理由描述
    @ApiParam(value="基准日审计报告_中介机构名称")
    protected String zlJzrsjbgZjjgmc;// 基准日审计报告_中介机构名称
    @ApiParam(value="基准日审计报告_验资报告号")
    protected String zlJzrsjbgYzbgh;// 基准日审计报告_验资报告号
    @ApiParam(value="基准日审计报告_理由描述")
    protected String zlJzrsjbgLy;// 基准日审计报告_理由描述
    @ApiParam(value="减资公告_媒体名称")
    protected String zlJzggMtmc;// 减资公告_媒体名称
    @ApiParam(value="减资公告_公告日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlJzggGgrq;// 减资公告_公告日期
    @ApiParam(value="减资公告_理由描述")
    protected String zlJzggLy;// 减资公告_理由描述
    @ApiParam(value="承接方名称")
    protected String xxCjfmc;// 承接方名称
    @ApiParam(value="司法作价(万元)")
    protected BigDecimal xxSfzj;// 司法作价(万元)
    @ApiParam(value="最近一期审计报告_中介机构")
    protected String zlZjyqsjbgZjjg;// 最近一期审计报告_中介机构
    @ApiParam(value="最近一期审计报告_报告号")
    protected String zlZjyqsjbgBgh;// 最近一期审计报告_报告号
    @ApiParam(value="最近一期审计报告_理由描述")
    protected String zlZjyqsjbgLy;// 最近一期审计报告_理由描述
    @ApiParam(value="投资协议_理由描述")
    protected String zlTzxyLy;// 投资协议_理由描述
    @ApiParam(value="清算净资产值(万元)")
    protected BigDecimal xxQsjzcz;// 清算净资产值(万元)
    @ApiParam(value="清算费用（万元）")
    protected BigDecimal xxQsfy;// 清算费用（万元）
    @ApiParam(value="清偿债务（万元）")
    protected BigDecimal xxQczw;// 清偿债务（万元）
    @ApiParam(value="破产财产总额（万元）")
    protected BigDecimal xxPcccze;// 破产财产总额（万元）
    @ApiParam(value="破产费用和共益债务（万元）")
    protected BigDecimal xxPcfyhgyzw;// 破产费用和共益债务（万元）
    @ApiParam(value="普通债务（万元）")
    protected BigDecimal xxPtzw;// 普通债务（万元）
    @ApiParam(value="清算报告_中介机构名称")
    protected String zlQsbgZjjgmc;// 清算报告_中介机构名称
    @ApiParam(value="清算报告_报告号")
    protected String zlQsbgBgh;// 清算报告_报告号
    @ApiParam(value="清算报告_理由描述")
    protected String zlQsbgLy;// 清算报告_理由描述
    @ApiParam(value="注销公告_媒体名称")
    protected String zlZxggMtmc;// 注销公告_媒体名称
    @ApiParam(value="注销公告_公告日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlZxggGgrq;// 注销公告_公告日期
    @ApiParam(value="注销公告_理由描述")
    protected String zlZxggLy;// 注销公告_理由描述
    @ApiParam(value="工商注销证明_理由描述")
    protected String zlGszxzmLy;// 工商注销证明_理由描述
    @ApiParam(value="破产公告_媒体名称")
    protected String zlPcggMtmc;// 破产公告_媒体名称
    @ApiParam(value="破产公告_公告日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlPcggGgrq;// 破产公告_公告日期
    @ApiParam(value="破产公告_理由描述")
    protected String zlPcggLy;// 破产公告_理由描述
    @ApiParam(value="投资协议_有无")
    protected String zlTzxyYw;// 投资协议_有无
    @ApiParam(value="经济行为决策文件_有无")
    protected String zlJcwjYw;// 经济行为决策文件_有无
    @ApiParam(value="减资公告_有无")
    protected String zlJzggYw;// 减资公告_有无
    @ApiParam(value="破产公告_有无")
    protected String zlPcggYw;// 破产公告_有无
    @ApiParam(value="基准日审计报告_有无")
    protected String zlJzrsjbgYw;// 基准日审计报告_有无
    @ApiParam(value="企业章程_有无")
    protected String zlQyzcYw;// 企业章程_有无
    @ApiParam(value="进场交易交割_有无")
    protected String zlJcjgYw;// 进场交易交割_有无
    @ApiParam(value="审计报告_有无")
    protected String zlSjbgYw;// 审计报告_有无
    @ApiParam(value="非货币评估备案表_有无")
    protected String zlFhbpgbaYw;// 非货币评估备案表_有无
    @ApiParam(value="营业执照_有无")
    protected String zlYyzzYw;// 营业执照_有无
    @ApiParam(value="评估备案表_有无")
    protected String zlPgbaYw;// 评估备案表_有无
    @ApiParam(value="工商注销证明_有无")
    protected String zlGszxzmYw;// 工商注销证明_有无
    @ApiParam(value="国有土地管理部门备案_有无")
    protected String zlGytdbaYw;// 国有土地管理部门备案_有无
    @ApiParam(value="验资报告_有无")
    protected String zlYzbgYw;// 验资报告_有无
    @ApiParam(value="合并协议书_有无")
    protected String zlHbxysYw;// 合并协议书_有无
    @ApiParam(value="置换协议_有无")
    protected String zlZhxyYw;// 置换协议_有无
    @ApiParam(value="股权转让协议_有无")
    protected String zlGqzrYw;// 股权转让协议_有无
    @ApiParam(value="无偿划转协议_有无")
    protected String zlWchzxyYw;// 无偿划转协议_有无
    @ApiParam(value="标的评估备案表_有无")
    protected String zlBdpgbaYw;// 标的评估备案表_有无
    @ApiParam(value="最近一期审计报告_有无")
    protected String zlZjyqsjbgYw;// 最近一期审计报告_有无
    @ApiParam(value="分立协议书_有无")
    protected String zlFlxysYw;// 分立协议书_有无
    @ApiParam(value="注销公告_有无")
    protected String zlZxggYw;// 注销公告_有无
    @ApiParam(value="清算报告_有无")
    protected String zlQsbgYw;// 清算报告_有无
    @ApiParam(value="标的企业性质")
    protected String xxBdqyxz;// 标的企业性质
    @ApiParam(value="被吸并方性质")
    protected String xxBxbfxz;// 被吸并方性质
    @ApiParam(value="解算原因")
    protected String xxJsyy;// 解算原因
    @ApiParam(value="工商注销证明_注销日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlGszxzmZxrq;// 工商注销证明_注销日期
    @ApiParam(value="经济行为决策文件_单位名称")
    protected String zlJcwjDwmc;// 经济行为决策文件_单位名称
    @ApiParam(value="经济行为决策文件_文件名称")
    protected String zlJcwjWjmc;// 经济行为决策文件_文件名称
    @ApiParam(value="经济行为决策文件_文件号")
    protected String zlJcwjWjh;// 经济行为决策文件_文件号
    @ApiParam(value="评估备案表_中介机构名称")
    protected String zlPgbaZjjgmc;// 评估备案表_中介机构名称
    @ApiParam(value="评估备案表_评估报告号")
    protected String zlPgbaPgbgh;// 评估备案表_评估报告号
    @ApiParam(value="评估备案表_核准单位名称")
    protected String zlPgbaHzdwmc;// 评估备案表_核准单位名称
    @ApiParam(value="评估备案表_核准文件号")
    protected String zlPgbaHzwjh;// 评估备案表_核准文件号
    @ApiParam(value="非货币评估备案表_中介机构名称")
    protected String zlFhbpgbaZjjgmc;// 非货币评估备案表_中介机构名称
    @ApiParam(value="非货币评估备案表_评估报告号")
    protected String zlFhbpgbaPgbgh;// 非货币评估备案表_评估报告号
    @ApiParam(value="非货币评估备案表_核准单位名称")
    protected String zlFhbpgbaHzdwmc;// 非货币评估备案表_核准单位名称
    @ApiParam(value="非货币评估备案表_核准文件号")
    protected String zlFhbpgbaHzwjh;// 非货币评估备案表_核准文件号
    @ApiParam(value="标的评估备案表_中介机构名称")
    protected String zlBdpgbaZjjgmc;// 标的评估备案表_中介机构名称
    @ApiParam(value="标的评估备案表_评估报告号")
    protected String zlBdpgbaPgbgh;// 标的评估备案表_评估报告号
    @ApiParam(value="标的评估备案表_核准单位名称")
    protected String zlBdpgbaHzdwmc;// 标的评估备案表_核准单位名称
    @ApiParam(value="标的评估备案表_核准文件号")
    protected String zlBdpgbaHzwjh;// 标的评估备案表_核准文件号
    @ApiParam(value="国有土地管理部门备案_批准单位")
    protected String zlGytdbaPzdw;// 国有土地管理部门备案_批准单位
    @ApiParam(value="国有土地管理部门备案_批准文号")
    protected String zlGytdbaPzwh;// 国有土地管理部门备案_批准文号
    @ApiParam(value="业务办理申请文件")
    protected String zlYwblsqwj;// 业务办理申请文件
    @ApiParam(value="置换一评估备案表_有无")
    protected String zlZhypgbabYw;// 置换一评估备案表_有无
    @ApiParam(value="置换一评估备案表_中介机构名称")
    protected String zlZhypgbabZjjgmc;// 置换一评估备案表_中介机构名称
    @ApiParam(value="置换一评估备案表_评估报告号")
    protected String zlZhypgbabPgbgh;// 置换一评估备案表_评估报告号
    @ApiParam(value="置换一评估备案表_核准单位名称")
    protected String zlZhypgbabHzdwmc;// 置换一评估备案表_核准单位名称
    @ApiParam(value="置换一评估备案表_核准文件号")
    protected String zlZhypgbabHzwjh;// 置换一评估备案表_核准文件号
    @ApiParam(value="置换一评估备案表_理由描述")
    protected String zlZhypgbabLy;// 置换一评估备案表_理由描述
    @ApiParam(value="置换二评估备案表_有无")
    protected String zlZhepgbabYw;// 置换二评估备案表_有无
    @ApiParam(value="置换二评估备案表_中介机构名称")
    protected String zlZhepgbabZjjgmc;// 置换二评估备案表_中介机构名称
    @ApiParam(value="置换二评估备案表_评估报告号")
    protected String zlZhepgbabPgbgh;// 置换二评估备案表_评估报告号
    @ApiParam(value="置换二评估备案表_核准单位名称")
    protected String zlZhepgbabHzdwmc;// 置换二评估备案表_核准单位名称
    @ApiParam(value="置换二评估备案表_核准文件号")
    protected String zlZhepgbabHzwjh;// 置换二评估备案表_核准文件号
    @ApiParam(value="置换二评估备案表_理由描述")
    protected String zlZhepgbabLy;// 置换二评估备案表_理由描述
    @ApiParam(value="吸并评估备案表_有无")
    protected String zlXbpgbabYw;// 吸并评估备案表_有无
    @ApiParam(value="吸并评估备案表_中介机构名称")
    protected String zlXbpgbabZjjgmc;// 吸并评估备案表_中介机构名称
    @ApiParam(value="吸并评估备案表_评估报告号")
    protected String zlXbpgbabPgbgh;// 吸并评估备案表_评估报告号
    @ApiParam(value="吸并评估备案表_核准单位名称")
    protected String zlXbpgbabHzdwmc;// 吸并评估备案表_核准单位名称
    @ApiParam(value="吸并评估备案表_核准文件号")
    protected String zlXbpgbabHzwjh;// 吸并评估备案表_核准文件号
    @ApiParam(value="吸并评估备案表_理由描述")
    protected String zlXbpgbabLy;// 吸并评估备案表_理由描述
    @ApiParam(value="被吸并评估备案表_有无")
    protected String zlBxbpgbabYw;// 被吸并评估备案表_有无
    @ApiParam(value="被吸并评估备案表_中介机构名称")
    protected String zlBxbpgbabZjjgmc;// 被吸并评估备案表_中介机构名称
    @ApiParam(value="被吸并评估备案表_评估报告号")
    protected String zlBxbpgbabPgbgh;// 被吸并评估备案表_评估报告号
    @ApiParam(value="被吸并评估备案表_核准单位名称")
    protected String zlBxbpgbabHzdwmc;// 被吸并评估备案表_核准单位名称
    @ApiParam(value="被吸并评估备案表_核准文件号")
    protected String zlBxbpgbabHzwjh;// 被吸并评估备案表_核准文件号
    @ApiParam(value="被吸并评估备案表_理由描述")
    protected String zlBxbpgbabLy;// 被吸并评估备案表_理由描述
    @ApiParam(value="投资评估备案表_有无")
    protected String zlTjpgbabYw;// 投资评估备案表_有无
    @ApiParam(value="投资评估备案表_中介机构名称")
    protected String zlTjpgbabZjjgmc;// 投资评估备案表_中介机构名称
    @ApiParam(value="投资并评估备案表_评估报告号")
    protected String zlTjpgbabPgbgh;// 投资并评估备案表_评估报告号
    @ApiParam(value="投资评估备案表_核准单位名称")
    protected String zlTjpgbabHzdwmc;// 投资评估备案表_核准单位名称
    @ApiParam(value="投资评估备案表_核准文件号")
    protected String zlTjpgbabHzwjh;// 投资评估备案表_核准文件号
    @ApiParam(value="投资评估备案表_理由描述")
    protected String zlTjpgbabLy;// 投资评估备案表_理由描述
    @ApiParam(value="剩余资产处置协议_有无")
    protected String zlSyzcczxyYw;// 剩余资产处置协议_有无
    @ApiParam(value="剩余资产处置协议_理由描述")
    protected String zlSyzcczxyLy;// 剩余资产处置协议_理由描述
    @ApiParam(value="职工代表大会决议_有无")
    protected String zlZgdbdhjyYw;// 职工代表大会决议_有无
    @ApiParam(value="职工代表大会决议_意见")
    protected String zlZgdbdhjyYj;// 职工代表大会决议_意见
    @ApiParam(value="职工代表大会决议_理由描述")
    protected String zlZgdbdhjyLy;// 职工代表大会决议_理由描述
    @ApiParam(value="股权设置方案文件_有无")
    protected String zlGqszfawjYw;// 股权设置方案文件_有无
    @ApiParam(value="股权设置方案文件_批准单位")
    protected String zlGqszfawjPzdw;// 股权设置方案文件_批准单位
    @ApiParam(value="股权设置方案文件_批准文号")
    protected String zlGqszfawjPzwh;// 股权设置方案文件_批准文号
    @ApiParam(value="股权设置方案文件_理由描述")
    protected String zlGqszfawjLy;// 股权设置方案文件_理由描述
    @ApiParam(value="股东情况登记表_有无")
    protected String zlGdqkdjbYw;// 股东情况登记表_有无
    @ApiParam(value="出资人组织机构id")
    protected String jbCzrzzjgid;//出资人组织机构id

    //合规资料中的附件
    @ApiParam(value="清算报告")
    protected String zlQsbg;// 清算报告
    @ApiParam(value="分立协议书")
    protected String zlFlxys;// 分立协议书
    @ApiParam(value="工商注销证明")
    protected String zlGszxzm;// 工商注销证明
    @ApiParam(value="职工代表大会决议")
    protected String zlZgdbdhjy;// 职工代表大会决议
    @ApiParam(value="股权设置方案文件")
    protected String zlGqszfawj;// 股权设置方案文件
    @ApiParam(value="注销公告")
    protected String zlZxgg;// 注销公告
    @ApiParam(value="合并协议书")
    protected String zlHbxys;// 合并协议书
    @ApiParam(value="基准日审计报告")
    protected String zlJzrsjbg;// 基准日审计报告
    @ApiParam(value="置换协议")
    protected String zlZhxy;// 置换协议
    @ApiParam(value="非货币评估备案表")
    protected String zlFhbpgba;// 非货币评估备案表
    @ApiParam(value="最近一期审计报告")
    protected String zlZjyqsjbg;// 最近一期审计报告
    @ApiParam(value="评估备案表")
    protected String zlPgba;// 评估备案表
    @ApiParam(value="标的评估备案表")
    protected String zlBdpgba;// 标的评估备案表
    @ApiParam(value="无偿划转协议")
    protected String zlWchzxy;// 无偿划转协议
    @ApiParam(value="减资公告")
    protected String zlJzgg;// 减资公告
    @ApiParam(value="营业执照")
    protected String zlYyzz;// 营业执照
    @ApiParam(value="审计报告")
    protected String zlSjbg;// 审计报告
    @ApiParam(value="验资报告")
    protected String zlYzbg;// 验资报告
    @ApiParam(value="企业章程")
    protected String zlQyzc;// 企业章程
    @ApiParam(value="股东情况登记表")
    protected String zlGdqkdjb;// 股东情况登记表
    @ApiParam(value="置换一评估备案表")
    protected String zlZhypgbab;// 置换一评估备案表
    @ApiParam(value="剩余资产处置协议")
    protected String zlSyzcczxy;// 剩余资产处置协议
    @ApiParam(value="投资评估备案表")
    protected String zlTjpgbab;// 投资评估备案表
    @ApiParam(value="股权转让协议")
    protected String zlGqzr;// 股权转让协议
    @ApiParam(value="置换二评估备案表")
    protected String zlZhepgbab;// 置换二评估备案表
    @ApiParam(value="吸并评估备案表")
    protected String zlXbpgbab;// 吸并评估备案表
    @ApiParam(value="投资协议")
    protected String zlTzxy;// 投资协议
    @ApiParam(value="被吸并评估备案表")
    protected String zlBxbpgbab;// 被吸并评估备案表
    @ApiParam(value="国有土地管理部门备案")
    protected String zlGytdba;// 国有土地管理部门备案
    @ApiParam(value="进场交易交割")
    protected String zlJcjg;// 进场交易交割
    @ApiParam(value="经济行为决策文件")
    protected String zlJcwj;// 经济行为决策文件
    @ApiParam(value="破产公告")
    protected String zlPcgg;// 破产公告

    //表名： cq_cjffd
    @ApiParam(value="FD5_CJFMC")
    protected String fd5Cjfmc;// FD5_CJFMC
    @ApiParam(value="FD5_CJFXZ")
    protected String fd5Cjfxz;// FD5_CJFXZ
    @ApiParam(value="FD5_SFZJ")
    protected BigDecimal fd5Sfzj;// FD5_SFZJ

    private List<CjffdVo> fd5TableData;//承接方浮动

    //预警id
    @ApiParam(value="预警id")
    protected String monitorwarnId;// 预警id

    //原合伙form字段
    @ApiParam(value="标的类型")
    protected String bdlx;// 标的类型
    @ApiParam(value="标的名称")
    protected String bdmc;// 标的名称
    @ApiParam(value="统一信用代码")
    protected String code;// 统一信用代码
    @ApiParam(value="所属行业")
    protected String sshy;// 所属行业
    @ApiParam(value="注册地或所在地")
    protected String address;// 注册地或所在地
    @ApiParam(value="投资额（万元）")
    protected BigDecimal tze;// 投资额（万元）
    @ApiParam(value="投资比例%")
    protected BigDecimal tzbl;// 投资比例%
    @ApiParam(value="是否实际控制")
    protected String sfsjkz;// 是否实际控制

    protected List<Hhrqkfd> hhrqkfdList;
    protected List<Dwtzqkfd> dwtzqkfdList;


    //Hhqy
    @ApiParam(value="有限合伙企业名称")
    protected String hhCompanyName;// 有限合伙企业名称
    // @ApiParam(value="统一信用代码")
    // protected String hhCreditCode;// 统一信用代码
    @ApiParam(value="执行事务合伙人")
    protected String hhZxswhhr;// 执行事务合伙人
    @ApiParam(value="执行事务合伙人统一信用代码")
    protected String hhZxswhhrCode;// 执行事务合伙人统一信用代码
    // @DateTimeFormat(pattern="yyyy-MM-dd")
    // @JsonFormat(pattern = "yyyy-MM-dd")
    // @ApiParam(value="成立日期")
    // protected Date setupDate;// 成立日期
    @ApiParam(value="合伙期限")
    protected String hhQx;// 合伙期限
    @ApiParam(value="主要经营场所")
    protected String hhZyjycs;// 主要经营场所
    @ApiParam(value="是否私募投资基金")
    protected String hhSfsmtzjj;// 是否私募投资基金
    @ApiParam(value="经营范围")
    protected String hhJyfw;// 经营范围
    @ApiParam(value="认缴出资额（万元）")
    protected BigDecimal hhRjcze;// 认缴出资额（万元）
    @ApiParam(value="认缴出资额币种")
    protected String hhRjczebz;// 认缴出资额币种
    @ApiParam(value="实缴出资额（万元）")
    protected BigDecimal hhSjcze;// 实缴出资额（万元）
    @ApiParam(value="实缴出资额币种")
    protected String hhSjczebz;// 实缴出资额币种
    @ApiParam(value="认缴（万元）人民币")
    protected BigDecimal hhRjczermb;// 实缴出资额（万元）
    @ApiParam(value="实缴出资额币种人民币")
    protected BigDecimal hhSjczermb;// 实缴出资额币种
    @ApiParam(value="国家出资企业")
    protected String hhGjczqy;// 国家出资企业
    @ApiParam(value="国家出资企业统一信用代码")
    protected String hhGjczqyCode;// 国家出资企业统一信用代码
    @ApiParam(value="出资企业")
    protected String hhCzqy;// 出资企业
    @ApiParam(value="出资企业统一信用代码")
    protected String hhCzqyCode;// 出资企业统一信用代码
    @ApiParam(value="合伙协议附件")
    protected String hhHhxy;// 合伙协议附件
    @ApiParam(value="合计认缴出资额（万元）")
    protected BigDecimal hjrjcze;// 合计认缴出资额（万元）
    @ApiParam(value="合计认缴出资比例%")
    protected BigDecimal hjrjczbl;// 合计认缴出资比例%
    @ApiParam(value="合计实缴出资额（万元）")
    protected BigDecimal hjsjcze;// 合计实缴出资额（万元）
    @ApiParam(value="出资企业id")
    protected String hhCzqyId;// 出资企业id
    @ApiParam(value="实收资本")
    protected BigDecimal hhSszb;// 实收资本
    @ApiParam(value = "数据状态 old 老数据 / new 新数据")
    protected String jbDataStatus;

    public String getMonitorwarnId() {
        return monitorwarnId;
    }

    public BigDecimal getHhRjczermb() {
        return hhRjczermb;
    }

    public void setHhRjczermb(BigDecimal hhRjczermb) {
        this.hhRjczermb = hhRjczermb;
    }

    public BigDecimal getHhSjczermb() {
        return hhSjczermb;
    }

    public void setHhSjczermb(BigDecimal hhSjczermb) {
        this.hhSjczermb = hhSjczermb;
    }

    public void setMonitorwarnId(String monitorwarnId) {
        this.monitorwarnId = monitorwarnId;
    }

    public List<Syccfpfd> getFd6TableData() {
        return fd6TableData;
    }

    public void setFd6TableData(List<Syccfpfd> fd6TableData) {
        this.fd6TableData = fd6TableData;
    }

    public String getFd5Cjfmc() {
        return fd5Cjfmc;
    }

    public void setFd5Cjfmc(String fd5Cjfmc) {
        this.fd5Cjfmc = fd5Cjfmc;
    }

    public String getFd5Cjfxz() {
        return fd5Cjfxz;
    }

    public void setFd5Cjfxz(String fd5Cjfxz) {
        this.fd5Cjfxz = fd5Cjfxz;
    }

    public BigDecimal getFd5Sfzj() {
        return fd5Sfzj;
    }

    public void setFd5Sfzj(BigDecimal fd5Sfzj) {
        this.fd5Sfzj = fd5Sfzj;
    }

    public String getUnitid() {
        return unitid;
    }

    public void setUnitid(String unitid) {
        this.unitid = unitid;
    }

    public String getDatatime() {
        return datatime;
    }

    public void setDatatime(String datatime) {
        this.datatime = datatime;
    }

    public BigDecimal getFloatorder() {
        return floatorder;
    }

    public void setFloatorder(BigDecimal floatorder) {
        this.floatorder = floatorder;
    }

    public BigDecimal getJbZczb() {
        return jbZczb;
    }

    public void setJbZczb(BigDecimal jbZczb) {
        this.jbZczb = jbZczb;
    }

    public BigDecimal getJbGjsbs() {
        return jbGjsbs;
    }

    public String getZlQsbg() {
        return zlQsbg;
    }

    public void setZlQsbg(String zlQsbg) {
        this.zlQsbg = zlQsbg;
    }

    public String getZlFlxys() {
        return zlFlxys;
    }

    public void setZlFlxys(String zlFlxys) {
        this.zlFlxys = zlFlxys;
    }

    public String getZlGszxzm() {
        return zlGszxzm;
    }

    public void setZlGszxzm(String zlGszxzm) {
        this.zlGszxzm = zlGszxzm;
    }

    public String getZlZgdbdhjy() {
        return zlZgdbdhjy;
    }

    public void setZlZgdbdhjy(String zlZgdbdhjy) {
        this.zlZgdbdhjy = zlZgdbdhjy;
    }

    public String getZlGqszfawj() {
        return zlGqszfawj;
    }

    public void setZlGqszfawj(String zlGqszfawj) {
        this.zlGqszfawj = zlGqszfawj;
    }

    public String getZlZxgg() {
        return zlZxgg;
    }

    public void setZlZxgg(String zlZxgg) {
        this.zlZxgg = zlZxgg;
    }

    public String getZlHbxys() {
        return zlHbxys;
    }

    public void setZlHbxys(String zlHbxys) {
        this.zlHbxys = zlHbxys;
    }

    public String getZlJzrsjbg() {
        return zlJzrsjbg;
    }

    public void setZlJzrsjbg(String zlJzrsjbg) {
        this.zlJzrsjbg = zlJzrsjbg;
    }

    public String getZlZhxy() {
        return zlZhxy;
    }

    public void setZlZhxy(String zlZhxy) {
        this.zlZhxy = zlZhxy;
    }

    public String getZlFhbpgba() {
        return zlFhbpgba;
    }

    public void setZlFhbpgba(String zlFhbpgba) {
        this.zlFhbpgba = zlFhbpgba;
    }

    public String getZlZjyqsjbg() {
        return zlZjyqsjbg;
    }

    public void setZlZjyqsjbg(String zlZjyqsjbg) {
        this.zlZjyqsjbg = zlZjyqsjbg;
    }

    public String getZlPgba() {
        return zlPgba;
    }

    public void setZlPgba(String zlPgba) {
        this.zlPgba = zlPgba;
    }

    public String getZlBdpgba() {
        return zlBdpgba;
    }

    public void setZlBdpgba(String zlBdpgba) {
        this.zlBdpgba = zlBdpgba;
    }

    public String getZlWchzxy() {
        return zlWchzxy;
    }

    public void setZlWchzxy(String zlWchzxy) {
        this.zlWchzxy = zlWchzxy;
    }

    public String getZlJzgg() {
        return zlJzgg;
    }

    public void setZlJzgg(String zlJzgg) {
        this.zlJzgg = zlJzgg;
    }

    public String getZlYyzz() {
        return zlYyzz;
    }

    public void setZlYyzz(String zlYyzz) {
        this.zlYyzz = zlYyzz;
    }

    public String getZlSjbg() {
        return zlSjbg;
    }

    public void setZlSjbg(String zlSjbg) {
        this.zlSjbg = zlSjbg;
    }

    public String getZlYzbg() {
        return zlYzbg;
    }

    public void setZlYzbg(String zlYzbg) {
        this.zlYzbg = zlYzbg;
    }

    public String getZlQyzc() {
        return zlQyzc;
    }

    public void setZlQyzc(String zlQyzc) {
        this.zlQyzc = zlQyzc;
    }

    public String getZlGdqkdjb() {
        return zlGdqkdjb;
    }

    public void setZlGdqkdjb(String zlGdqkdjb) {
        this.zlGdqkdjb = zlGdqkdjb;
    }

    public String getZlZhypgbab() {
        return zlZhypgbab;
    }

    public void setZlZhypgbab(String zlZhypgbab) {
        this.zlZhypgbab = zlZhypgbab;
    }

    public String getZlSyzcczxy() {
        return zlSyzcczxy;
    }

    public void setZlSyzcczxy(String zlSyzcczxy) {
        this.zlSyzcczxy = zlSyzcczxy;
    }

    public String getZlTjpgbab() {
        return zlTjpgbab;
    }

    public void setZlTjpgbab(String zlTjpgbab) {
        this.zlTjpgbab = zlTjpgbab;
    }

    public String getZlGqzr() {
        return zlGqzr;
    }

    public void setZlGqzr(String zlGqzr) {
        this.zlGqzr = zlGqzr;
    }

    public String getZlZhepgbab() {
        return zlZhepgbab;
    }

    public void setZlZhepgbab(String zlZhepgbab) {
        this.zlZhepgbab = zlZhepgbab;
    }

    public String getZlXbpgbab() {
        return zlXbpgbab;
    }

    public void setZlXbpgbab(String zlXbpgbab) {
        this.zlXbpgbab = zlXbpgbab;
    }

    public String getZlTzxy() {
        return zlTzxy;
    }

    public void setZlTzxy(String zlTzxy) {
        this.zlTzxy = zlTzxy;
    }

    public String getZlBxbpgbab() {
        return zlBxbpgbab;
    }

    public void setZlBxbpgbab(String zlBxbpgbab) {
        this.zlBxbpgbab = zlBxbpgbab;
    }

    public String getZlGytdba() {
        return zlGytdba;
    }

    public void setZlGytdba(String zlGytdba) {
        this.zlGytdba = zlGytdba;
    }

    public String getZlJcjg() {
        return zlJcjg;
    }

    public void setZlJcjg(String zlJcjg) {
        this.zlJcjg = zlJcjg;
    }

    public String getZlJcwj() {
        return zlJcwj;
    }

    public void setZlJcwj(String zlJcwj) {
        this.zlJcwj = zlJcwj;
    }

    public String getZlPcgg() {
        return zlPcgg;
    }

    public void setZlPcgg(String zlPcgg) {
        this.zlPcgg = zlPcgg;
    }

    public void setJbGjsbs(BigDecimal jbGjsbs) {
        this.jbGjsbs = jbGjsbs;
    }

    public BigDecimal getJbGjsds() {
        return jbGjsds;
    }

    public void setJbGjsds(BigDecimal jbGjsds) {
        this.jbGjsds = jbGjsds;
    }

    public BigDecimal getJbGyfrsbs() {
        return jbGyfrsbs;
    }

    public void setJbGyfrsbs(BigDecimal jbGyfrsbs) {
        this.jbGyfrsbs = jbGyfrsbs;
    }

    public BigDecimal getJbGyfrsds() {
        return jbGyfrsds;
    }

    public void setJbGyfrsds(BigDecimal jbGyfrsds) {
        this.jbGyfrsds = jbGyfrsds;
    }

    public BigDecimal getJbGyjdkgsbs() {
        return jbGyjdkgsbs;
    }

    public void setJbGyjdkgsbs(BigDecimal jbGyjdkgsbs) {
        this.jbGyjdkgsbs = jbGyjdkgsbs;
    }

    public BigDecimal getJbGyjdkgsds() {
        return jbGyjdkgsds;
    }

    public void setJbGyjdkgsds(BigDecimal jbGyjdkgsds) {
        this.jbGyjdkgsds = jbGyjdkgsds;
    }

    public BigDecimal getJbGysjkzsbs() {
        return jbGysjkzsbs;
    }

    public void setJbGysjkzsbs(BigDecimal jbGysjkzsbs) {
        this.jbGysjkzsbs = jbGysjkzsbs;
    }

    public BigDecimal getJbGysjkzsds() {
        return jbGysjkzsds;
    }

    public void setJbGysjkzsds(BigDecimal jbGysjkzsds) {
        this.jbGysjkzsds = jbGysjkzsds;
    }

    public BigDecimal getJbQtqysbs() {
        return jbQtqysbs;
    }

    public void setJbQtqysbs(BigDecimal jbQtqysbs) {
        this.jbQtqysbs = jbQtqysbs;
    }

    public BigDecimal getJbQtsds() {
        return jbQtsds;
    }

    public void setJbQtsds(BigDecimal jbQtsds) {
        this.jbQtsds = jbQtsds;
    }

    public BigDecimal getJbHjqysbs() {
        return jbHjqysbs;
    }

    public void setJbHjqysbs(BigDecimal jbHjqysbs) {
        this.jbHjqysbs = jbHjqysbs;
    }

    public BigDecimal getJbHjsds() {
        return jbHjsds;
    }

    public void setJbHjsds(BigDecimal jbHjsds) {
        this.jbHjsds = jbHjsds;
    }

    public Date getJbSjblrq() {
        return jbSjblrq;
    }

    public void setJbSjblrq(Date jbSjblrq) {
        this.jbSjblrq = jbSjblrq;
    }

    public BigDecimal getJbHjcze() {
        return jbHjcze;
    }

    public void setJbHjcze(BigDecimal jbHjcze) {
        this.jbHjcze = jbHjcze;
    }

    public BigDecimal getJbHjsjzcj() {
        return jbHjsjzcj;
    }

    public void setJbHjsjzcj(BigDecimal jbHjsjzcj) {
        this.jbHjsjzcj = jbHjsjzcj;
    }

    public BigDecimal getJbHjbl() {
        return jbHjbl;
    }

    public void setJbHjbl(BigDecimal jbHjbl) {
        this.jbHjbl = jbHjbl;
    }

    public String getJbQymc() {
        return jbQymc;
    }

    public void setJbQymc(String jbQymc) {
        this.jbQymc = jbQymc;
    }

    public String getJbZzjgdm() {
        return jbZzjgdm;
    }

    public void setJbZzjgdm(String jbZzjgdm) {
        this.jbZzjgdm = jbZzjgdm;
    }

    public String getJbZcmd() {
        return jbZcmd;
    }

    public void setJbZcmd(String jbZcmd) {
        this.jbZcmd = jbZcmd;
    }

    public String getJbCgrxm() {
        return jbCgrxm;
    }

    public void setJbCgrxm(String jbCgrxm) {
        this.jbCgrxm = jbCgrxm;
    }

    public Date getJbJhqlsj() {
        return jbJhqlsj;
    }

    public void setJbJhqlsj(Date jbJhqlsj) {
        this.jbJhqlsj = jbJhqlsj;
    }

    public String getJbSjczr() {
        return jbSjczr;
    }

    public void setJbSjczr(String jbSjczr) {
        this.jbSjczr = jbSjczr;
    }

    public String getJbCzrzzjgdm() {
        return jbCzrzzjgdm;
    }

    public void setJbCzrzzjgdm(String jbCzrzzjgdm) {
        this.jbCzrzzjgdm = jbCzrzzjgdm;
    }

    public BigDecimal getJbHjsjzcjbz() {
        return jbHjsjzcjbz;
    }

    public void setJbHjsjzcjbz(BigDecimal jbHjsjzcjbz) {
        this.jbHjsjzcjbz = jbHjsjzcjbz;
    }

    public BigDecimal getJbGjczsdsbz() {
        return jbGjczsdsbz;
    }

    public void setJbGjczsdsbz(BigDecimal jbGjczsdsbz) {
        this.jbGjczsdsbz = jbGjczsdsbz;
    }

    public BigDecimal getJbGjczqysbsbz() {
        return jbGjczqysbsbz;
    }

    public void setJbGjczqysbsbz(BigDecimal jbGjczqysbsbz) {
        this.jbGjczqysbsbz = jbGjczqysbsbz;
    }

    public BigDecimal getJbGyfrczsbsbz() {
        return jbGyfrczsbsbz;
    }

    public void setJbGyfrczsbsbz(BigDecimal jbGyfrczsbsbz) {
        this.jbGyfrczsbsbz = jbGyfrczsbsbz;
    }

    public BigDecimal getJbGyfrczsdsbz() {
        return jbGyfrczsdsbz;
    }

    public void setJbGyfrczsdsbz(BigDecimal jbGyfrczsdsbz) {
        this.jbGyfrczsdsbz = jbGyfrczsdsbz;
    }

    public BigDecimal getJbGyjdkgfrsbsbz() {
        return jbGyjdkgfrsbsbz;
    }

    public void setJbGyjdkgfrsbsbz(BigDecimal jbGyjdkgfrsbsbz) {
        this.jbGyjdkgfrsbsbz = jbGyjdkgfrsbsbz;
    }

    public BigDecimal getJbGyjdkgfrsdsbz() {
        return jbGyjdkgfrsdsbz;
    }

    public void setJbGyjdkgfrsdsbz(BigDecimal jbGyjdkgfrsdsbz) {
        this.jbGyjdkgfrsdsbz = jbGyjdkgfrsdsbz;
    }

    public BigDecimal getJbGysjkzfrsbsbz() {
        return jbGysjkzfrsbsbz;
    }

    public void setJbGysjkzfrsbsbz(BigDecimal jbGysjkzfrsbsbz) {
        this.jbGysjkzfrsbsbz = jbGysjkzfrsbsbz;
    }

    public BigDecimal getJbGysjkzfrsdsbz() {
        return jbGysjkzfrsdsbz;
    }

    public void setJbGysjkzfrsdsbz(BigDecimal jbGysjkzfrsdsbz) {
        this.jbGysjkzfrsdsbz = jbGysjkzfrsdsbz;
    }

    public BigDecimal getJbQtsbsbz() {
        return jbQtsbsbz;
    }

    public void setJbQtsbsbz(BigDecimal jbQtsbsbz) {
        this.jbQtsbsbz = jbQtsbsbz;
    }

    public BigDecimal getJbQtqysdsbz() {
        return jbQtqysdsbz;
    }

    public void setJbQtqysdsbz(BigDecimal jbQtqysdsbz) {
        this.jbQtqysdsbz = jbQtqysdsbz;
    }

    public BigDecimal getJbHjqysbsbz() {
        return jbHjqysbsbz;
    }

    public void setJbHjqysbsbz(BigDecimal jbHjqysbsbz) {
        this.jbHjqysbsbz = jbHjqysbsbz;
    }

    public BigDecimal getJbHjsdsbz() {
        return jbHjsdsbz;
    }

    public void setJbHjsdsbz(BigDecimal jbHjsdsbz) {
        this.jbHjsdsbz = jbHjsdsbz;
    }

    public Integer getJbShzt() {
        return jbShzt;
    }

    public void setJbShzt(Integer jbShzt) {
        this.jbShzt = jbShzt;
    }

    public String getJbSshy() {
        return jbSshy;
    }

    public void setJbSshy(String jbSshy) {
        this.jbSshy = jbSshy;
    }

    public String getJbSfzy() {
        return jbSfzy;
    }

    public void setJbSfzy(String jbSfzy) {
        this.jbSfzy = jbSfzy;
    }

    public String getJbZzxs() {
        return jbZzxs;
    }

    public void setJbZzxs(String jbZzxs) {
        this.jbZzxs = jbZzxs;
    }

    public String getJbQylb() {
        return jbQylb;
    }

    public void setJbQylb(String jbQylb) {
        this.jbQylb = jbQylb;
    }

    public String getJbQyjc() {
        return jbQyjc;
    }

    public void setJbQyjc(String jbQyjc) {
        this.jbQyjc = jbQyjc;
    }

    public String getJbSsbm() {
        return jbSsbm;
    }

    public void setJbSsbm(String jbSsbm) {
        this.jbSsbm = jbSsbm;
    }

    public String getJbJyzk() {
        return jbJyzk;
    }

    public void setJbJyzk(String jbJyzk) {
        this.jbJyzk = jbJyzk;
    }

    public String getJbSftsmdgs() {
        return jbSftsmdgs;
    }

    public void setJbSftsmdgs(String jbSftsmdgs) {
        this.jbSftsmdgs = jbSftsmdgs;
    }

    public String getJbSfczgrdcg() {
        return jbSfczgrdcg;
    }

    public void setJbSfczgrdcg(String jbSfczgrdcg) {
        this.jbSfczgrdcg = jbSfczgrdcg;
    }

    public String getJbGjczqy() {
        return jbGjczqy;
    }

    public void setJbGjczqy(String jbGjczqy) {
        this.jbGjczqy = jbGjczqy;
    }

    public String getJbQysbsbzxz() {
        return jbQysbsbzxz;
    }

    public void setJbQysbsbzxz(String jbQysbsbzxz) {
        this.jbQysbsbzxz = jbQysbsbzxz;
    }

    public String getJbSdsbzxz() {
        return jbSdsbzxz;
    }

    public void setJbSdsbzxz(String jbSdsbzxz) {
        this.jbSdsbzxz = jbSdsbzxz;
    }

    public String getJbJnjw() {
        return jbJnjw;
    }

    public void setJbJnjw(String jbJnjw) {
        this.jbJnjw = jbJnjw;
    }

    public String getJbZycqdjqx() {
        return jbZycqdjqx;
    }

    public void setJbZycqdjqx(String jbZycqdjqx) {
        this.jbZycqdjqx = jbZycqdjqx;
    }

    public String getJbBdcqdjqx() {
        return jbBdcqdjqx;
    }

    public void setJbBdcqdjqx(String jbBdcqdjqx) {
        this.jbBdcqdjqx = jbBdcqdjqx;
    }

    public String getJbZxcqdjqx() {
        return jbZxcqdjqx;
    }

    public void setJbZxcqdjqx(String jbZxcqdjqx) {
        this.jbZxcqdjqx = jbZxcqdjqx;
    }

    public String getJbGzjgjg() {
        return jbGzjgjg;
    }

    public void setJbGzjgjg(String jbGzjgjg) {
        this.jbGzjgjg = jbGzjgjg;
    }

    public String getJbZcd() {
        return jbZcd;
    }

    public void setJbZcd(String jbZcd) {
        this.jbZcd = jbZcd;
    }

    public Date getJbGsdjrq() {
        return jbGsdjrq;
    }

    public void setJbGsdjrq(Date jbGsdjrq) {
        this.jbGsdjrq = jbGsdjrq;
    }

    public Date getJbZcrq() {
        return jbZcrq;
    }

    public void setJbZcrq(Date jbZcrq) {
        this.jbZcrq = jbZcrq;
    }

    public String getJbSfybgs() {
        return jbSfybgs;
    }

    public void setJbSfybgs(String jbSfybgs) {
        this.jbSfybgs = jbSfybgs;
    }

    public String getJbGsblzk() {
        return jbGsblzk;
    }

    public void setJbGsblzk(String jbGsblzk) {
        this.jbGsblzk = jbGsblzk;
    }

    public String getJbGsdjxgzl() {
        return jbGsdjxgzl;
    }

    public void setJbGsdjxgzl(String jbGsdjxgzl) {
        this.jbGsdjxgzl = jbGsdjxgzl;
    }

    public String getJbSfyz() {
        return jbSfyz;
    }

    public void setJbSfyz(String jbSfyz) {
        this.jbSfyz = jbSfyz;
    }

    public String getJbByzly() {
        return jbByzly;
    }

    public void setJbByzly(String jbByzly) {
        this.jbByzly = jbByzly;
    }

    public String getJbZyhy() {
        return jbZyhy;
    }

    public void setJbZyhy(String jbZyhy) {
        this.jbZyhy = jbZyhy;
    }

    public String getJbZcdjw() {
        return jbZcdjw;
    }

    public void setJbZcdjw(String jbZcdjw) {
        this.jbZcdjw = jbZcdjw;
    }

    public BigDecimal getJbHjcjebz() {
        return jbHjcjebz;
    }

    public void setJbHjcjebz(BigDecimal jbHjcjebz) {
        this.jbHjcjebz = jbHjcjebz;
    }

    public String getJbCzebzxz() {
        return jbCzebzxz;
    }

    public void setJbCzebzxz(String jbCzebzxz) {
        this.jbCzebzxz = jbCzebzxz;
    }

    public String getJbSjzczbbzxz() {
        return jbSjzczbbzxz;
    }

    public void setJbSjzczbbzxz(String jbSjzczbbzxz) {
        this.jbSjzczbbzxz = jbSjzczbbzxz;
    }

    public Integer getJbBlqshzt() {
        return jbBlqshzt;
    }

    public void setJbBlqshzt(Integer jbBlqshzt) {
        this.jbBlqshzt = jbBlqshzt;
    }

    public String getJbSfztjn() {
        return jbSfztjn;
    }

    public void setJbSfztjn(String jbSfztjn) {
        this.jbSfztjn = jbSfztjn;
    }

    public String getJbZczbbz() {
        return jbZczbbz;
    }

    public void setJbZczbbz(String jbZczbbz) {
        this.jbZczbbz = jbZczbbz;
    }

    public BigDecimal getJbZczbjw() {
        return jbZczbjw;
    }

    public void setJbZczbjw(BigDecimal jbZczbjw) {
        this.jbZczbjw = jbZczbjw;
    }

    public String getJbSfzdyjsj() {
        return jbSfzdyjsj;
    }

    public void setJbSfzdyjsj(String jbSfzdyjsj) {
        this.jbSfzdyjsj = jbSfzdyjsj;
    }

    public String getJbSfsjscdm() {
        return jbSfsjscdm;
    }

    public void setJbSfsjscdm(String jbSfsjscdm) {
        this.jbSfsjscdm = jbSfsjscdm;
    }

    public String getJbXzqy() {
        return jbXzqy;
    }

    public void setJbXzqy(String jbXzqy) {
        this.jbXzqy = jbXzqy;
    }

    public String getJbShtgrq() {
        return jbShtgrq;
    }

    public void setJbShtgrq(String jbShtgrq) {
        this.jbShtgrq = jbShtgrq;
    }

    public String getJbDylsh() {
        return jbDylsh;
    }

    public void setJbDylsh(String jbDylsh) {
        this.jbDylsh = jbDylsh;
    }

    public String getJbSsgzjgjg() {
        return jbSsgzjgjg;
    }

    public void setJbSsgzjgjg(String jbSsgzjgjg) {
        this.jbSsgzjgjg = jbSsgzjgjg;
    }

    public String getJbQljh() {
        return jbQljh;
    }

    public void setJbQljh(String jbQljh) {
        this.jbQljh = jbQljh;
    }

    public BigDecimal getJbHjrjzb() {
        return jbHjrjzb;
    }

    public void setJbHjrjzb(BigDecimal jbHjrjzb) {
        this.jbHjrjzb = jbHjrjzb;
    }

    public String getJbRjzbbzxz() {
        return jbRjzbbzxz;
    }

    public void setJbRjzbbzxz(String jbRjzbbzxz) {
        this.jbRjzbbzxz = jbRjzbbzxz;
    }

    public BigDecimal getJbHjrjzbbz() {
        return jbHjrjzbbz;
    }

    public void setJbHjrjzbbz(BigDecimal jbHjrjzbbz) {
        this.jbHjrjzbbz = jbHjrjzbbz;
    }

    public String getJbZczbbzxz() {
        return jbZczbbzxz;
    }

    public void setJbZczbbzxz(String jbZczbbzxz) {
        this.jbZczbbzxz = jbZczbbzxz;
    }

    public BigDecimal getJbGykgcz() {
        return jbGykgcz;
    }

    public void setJbGykgcz(BigDecimal jbGykgcz) {
        this.jbGykgcz = jbGykgcz;
    }

    public BigDecimal getJbGykgczsbs() {
        return jbGykgczsbs;
    }

    public void setJbGykgczsbs(BigDecimal jbGykgczsbs) {
        this.jbGykgczsbs = jbGykgczsbs;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public BigDecimal getJbGyzb() {
        return jbGyzb;
    }

    public void setJbGyzb(BigDecimal jbGyzb) {
        this.jbGyzb = jbGyzb;
    }

    public String getJbRela() {
        return jbRela;
    }

    public void setJbRela(String jbRela) {
        this.jbRela = jbRela;
    }

    public String getJbHgqy() {
        return jbHgqy;
    }

    public void setJbHgqy(String jbHgqy) {
        this.jbHgqy = jbHgqy;
    }

    public List<Fhbzcfd> getFd1TableData() {
        return fd1TableData;
    }

    public void setFd1TableData(List<Fhbzcfd> fd1TableData) {
        this.fd1TableData = fd1TableData;
    }

    public List<Czfd> getFdTableData() {
        return fdTableData;
    }

    public void setFdTableData(List<Czfd> fdTableData) {
        this.fdTableData = fdTableData;
    }

    public List<Ygqcyffd> getFd2TableData() {
        return fd2TableData;
    }

    public void setFd2TableData(List<Ygqcyffd> fd2TableData) {
        this.fd2TableData = fd2TableData;
    }

    public List<Hrhcfd> getFd7TableData() {
        return fd7TableData;
    }

    public void setFd7TableData(List<Hrhcfd> fd7TableData) {
        this.fd7TableData = fd7TableData;
    }

    public List<Zrsrfd> getFd8TableData() {
        return fd8TableData;
    }

    public void setFd8TableData(List<Zrsrfd> fd8TableData) {
        this.fd8TableData = fd8TableData;
    }

    public List<Cgrfd> getFd9TableData() {
        return fd9TableData;
    }

    public void setFd9TableData(List<Cgrfd> fd9TableData) {
        this.fd9TableData = fd9TableData;
    }

    public String getZlGdqkdjbLy() {
        return zlGdqkdjbLy;
    }

    public void setZlGdqkdjbLy(String zlGdqkdjbLy) {
        this.zlGdqkdjbLy = zlGdqkdjbLy;
    }

    public String getZlQt() {
        return zlQt;
    }

    public void setZlQt(String zlQt) {
        this.zlQt = zlQt;
    }

    public String getZlGszxzmGsbmmc() {
        return zlGszxzmGsbmmc;
    }

    public void setZlGszxzmGsbmmc(String zlGszxzmGsbmmc) {
        this.zlGszxzmGsbmmc = zlGszxzmGsbmmc;
    }

    public String getXxYwfhbcz() {
        return xxYwfhbcz;
    }

    public void setXxYwfhbcz(String xxYwfhbcz) {
        this.xxYwfhbcz = xxYwfhbcz;
    }

    public String getXxYwfhbzfsgjk() {
        return xxYwfhbzfsgjk;
    }

    public void setXxYwfhbzfsgjk(String xxYwfhbzfsgjk) {
        this.xxYwfhbzfsgjk = xxYwfhbzfsgjk;
    }

    public BigDecimal getXxBdqypgjzcz() {
        return xxBdqypgjzcz;
    }

    public void setXxBdqypgjzcz(BigDecimal xxBdqypgjzcz) {
        this.xxBdqypgjzcz = xxBdqypgjzcz;
    }

    public String getXxBz() {
        return xxBz;
    }

    public void setXxBz(String xxBz) {
        this.xxBz = xxBz;
    }

    public BigDecimal getXxBdqysjjzcz() {
        return xxBdqysjjzcz;
    }

    public void setXxBdqysjjzcz(BigDecimal xxBdqysjjzcz) {
        this.xxBdqysjjzcz = xxBdqysjjzcz;
    }

    public String getXxZhyqymc() {
        return xxZhyqymc;
    }

    public void setXxZhyqymc(String xxZhyqymc) {
        this.xxZhyqymc = xxZhyqymc;
    }

    public String getXxZhyssgzjgjg() {
        return xxZhyssgzjgjg;
    }

    public void setXxZhyssgzjgjg(String xxZhyssgzjgjg) {
        this.xxZhyssgzjgjg = xxZhyssgzjgjg;
    }

    public String getXxZhyssgjczqy() {
        return xxZhyssgjczqy;
    }

    public void setXxZhyssgjczqy(String xxZhyssgjczqy) {
        this.xxZhyssgjczqy = xxZhyssgjczqy;
    }

    public BigDecimal getXxZhyyyzhdzcpgz() {
        return xxZhyyyzhdzcpgz;
    }

    public void setXxZhyyyzhdzcpgz(BigDecimal xxZhyyyzhdzcpgz) {
        this.xxZhyyyzhdzcpgz = xxZhyyyzhdzcpgz;
    }

    public String getXxZhybz() {
        return xxZhybz;
    }

    public void setXxZhybz(String xxZhybz) {
        this.xxZhybz = xxZhybz;
    }

    public String getXxZheqymc() {
        return xxZheqymc;
    }

    public void setXxZheqymc(String xxZheqymc) {
        this.xxZheqymc = xxZheqymc;
    }

    public String getXxZhessgzjgjg() {
        return xxZhessgzjgjg;
    }

    public void setXxZhessgzjgjg(String xxZhessgzjgjg) {
        this.xxZhessgzjgjg = xxZhessgzjgjg;
    }

    public String getXxZhessgjczqy() {
        return xxZhessgjczqy;
    }

    public void setXxZhessgjczqy(String xxZhessgjczqy) {
        this.xxZhessgjczqy = xxZhessgjczqy;
    }

    public BigDecimal getXxZheyyzhdzcpgz() {
        return xxZheyyzhdzcpgz;
    }

    public void setXxZheyyzhdzcpgz(BigDecimal xxZheyyzhdzcpgz) {
        this.xxZheyyzhdzcpgz = xxZheyyzhdzcpgz;
    }

    public String getXxZhebz() {
        return xxZhebz;
    }

    public void setXxZhebz(String xxZhebz) {
        this.xxZhebz = xxZhebz;
    }

    public BigDecimal getXxAzfyze() {
        return xxAzfyze;
    }

    public void setXxAzfyze(BigDecimal xxAzfyze) {
        this.xxAzfyze = xxAzfyze;
    }

    public BigDecimal getXxGyqy() {
        return xxGyqy;
    }

    public void setXxGyqy(BigDecimal xxGyqy) {
        this.xxGyqy = xxGyqy;
    }

    public String getXxBxbfmc() {
        return xxBxbfmc;
    }

    public void setXxBxbfmc(String xxBxbfmc) {
        this.xxBxbfmc = xxBxbfmc;
    }

    public BigDecimal getXxBxbfpgjzcz() {
        return xxBxbfpgjzcz;
    }

    public void setXxBxbfpgjzcz(BigDecimal xxBxbfpgjzcz) {
        this.xxBxbfpgjzcz = xxBxbfpgjzcz;
    }

    public BigDecimal getXxXbfpgjzcz() {
        return xxXbfpgjzcz;
    }

    public void setXxXbfpgjzcz(BigDecimal xxXbfpgjzcz) {
        this.xxXbfpgjzcz = xxXbfpgjzcz;
    }

    public BigDecimal getXxBflqypgjzcz() {
        return xxBflqypgjzcz;
    }

    public void setXxBflqypgjzcz(BigDecimal xxBflqypgjzcz) {
        this.xxBflqypgjzcz = xxBflqypgjzcz;
    }

    public BigDecimal getXxCxqypgjzcz() {
        return xxCxqypgjzcz;
    }

    public void setXxCxqypgjzcz(BigDecimal xxCxqypgjzcz) {
        this.xxCxqypgjzcz = xxCxqypgjzcz;
    }

    public BigDecimal getXxCxqyzgbz() {
        return xxCxqyzgbz;
    }

    public void setXxCxqyzgbz(BigDecimal xxCxqyzgbz) {
        this.xxCxqyzgbz = xxCxqyzgbz;
    }

    public BigDecimal getXxYytzdgqpgz() {
        return xxYytzdgqpgz;
    }

    public void setXxYytzdgqpgz(BigDecimal xxYytzdgqpgz) {
        this.xxYytzdgqpgz = xxYytzdgqpgz;
    }

    public BigDecimal getXxYytzdgqzj() {
        return xxYytzdgqzj;
    }

    public void setXxYytzdgqzj(BigDecimal xxYytzdgqzj) {
        this.xxYytzdgqzj = xxYytzdgqzj;
    }

    public BigDecimal getXxSyjzcczsr() {
        return xxSyjzcczsr;
    }

    public void setXxSyjzcczsr(BigDecimal xxSyjzcczsr) {
        this.xxSyjzcczsr = xxSyjzcczsr;
    }

    public String getXxCjfssgzjgjg() {
        return xxCjfssgzjgjg;
    }

    public void setXxCjfssgzjgjg(String xxCjfssgzjgjg) {
        this.xxCjfssgzjgjg = xxCjfssgzjgjg;
    }

    public String getXxBdqymc() {
        return xxBdqymc;
    }

    public void setXxBdqymc(String xxBdqymc) {
        this.xxBdqymc = xxBdqymc;
    }

    public String getXxBdqyssgzjgjg() {
        return xxBdqyssgzjgjg;
    }

    public void setXxBdqyssgzjgjg(String xxBdqyssgzjgjg) {
        this.xxBdqyssgzjgjg = xxBdqyssgzjgjg;
    }

    public BigDecimal getXxQsfpbc() {
        return xxQsfpbc;
    }

    public void setXxQsfpbc(BigDecimal xxQsfpbc) {
        this.xxQsfpbc = xxQsfpbc;
    }

    public BigDecimal getXxQsfpsqsk() {
        return xxQsfpsqsk;
    }

    public void setXxQsfpsqsk(BigDecimal xxQsfpsqsk) {
        this.xxQsfpsqsk = xxQsfpsqsk;
    }

    public String getXxQsccsfzyqczw() {
        return xxQsccsfzyqczw;
    }

    public void setXxQsccsfzyqczw(String xxQsccsfzyqczw) {
        this.xxQsccsfzyqczw = xxQsccsfzyqczw;
    }

    public BigDecimal getXxPcfpbc() {
        return xxPcfpbc;
    }

    public void setXxPcfpbc(BigDecimal xxPcfpbc) {
        this.xxPcfpbc = xxPcfpbc;
    }

    public BigDecimal getXxPcfpsqsk() {
        return xxPcfpsqsk;
    }

    public void setXxPcfpsqsk(BigDecimal xxPcfpsqsk) {
        this.xxPcfpsqsk = xxPcfpsqsk;
    }

    public String getXxYwfhbjz() {
        return xxYwfhbjz;
    }

    public void setXxYwfhbjz(String xxYwfhbjz) {
        this.xxYwfhbjz = xxYwfhbjz;
    }

    public BigDecimal getFd1Czzjhj() {
        return fd1Czzjhj;
    }

    public void setFd1Czzjhj(BigDecimal fd1Czzjhj) {
        this.fd1Czzjhj = fd1Czzjhj;
    }

    public BigDecimal getFd1Pgzhj() {
        return fd1Pgzhj;
    }

    public void setFd1Pgzhj(BigDecimal fd1Pgzhj) {
        this.fd1Pgzhj = fd1Pgzhj;
    }

    public BigDecimal getFd1Zfzjhj() {
        return fd1Zfzjhj;
    }

    public void setFd1Zfzjhj(BigDecimal fd1Zfzjhj) {
        this.fd1Zfzjhj = fd1Zfzjhj;
    }

    public BigDecimal getFd1Jzzjhj() {
        return fd1Jzzjhj;
    }

    public void setFd1Jzzjhj(BigDecimal fd1Jzzjhj) {
        this.fd1Jzzjhj = fd1Jzzjhj;
    }

    public BigDecimal getFd2Sgjghj() {
        return fd2Sgjghj;
    }

    public void setFd2Sgjghj(BigDecimal fd2Sgjghj) {
        this.fd2Sgjghj = fd2Sgjghj;
    }

    public BigDecimal getFd2Sjjzczhj() {
        return fd2Sjjzczhj;
    }

    public void setFd2Sjjzczhj(BigDecimal fd2Sjjzczhj) {
        this.fd2Sjjzczhj = fd2Sjjzczhj;
    }

    public BigDecimal getFd2Pgjzczhj() {
        return fd2Pgjzczhj;
    }

    public void setFd2Pgjzczhj(BigDecimal fd2Pgjzczhj) {
        this.fd2Pgjzczhj = fd2Pgjzczhj;
    }

    public BigDecimal getFd4Zgslhj() {
        return fd4Zgslhj;
    }

    public void setFd4Zgslhj(BigDecimal fd4Zgslhj) {
        this.fd4Zgslhj = fd4Zgslhj;
    }

    public BigDecimal getFd5Sfzjhj() {
        return fd5Sfzjhj;
    }

    public void setFd5Sfzjhj(BigDecimal fd5Sfzjhj) {
        this.fd5Sfzjhj = fd5Sfzjhj;
    }

    public BigDecimal getFd6Syccfpjghj() {
        return fd6Syccfpjghj;
    }

    public void setFd6Syccfpjghj(BigDecimal fd6Syccfpjghj) {
        this.fd6Syccfpjghj = fd6Syccfpjghj;
    }

    public BigDecimal getFd7Hzgqblhj() {
        return fd7Hzgqblhj;
    }

    public void setFd7Hzgqblhj(BigDecimal fd7Hzgqblhj) {
        this.fd7Hzgqblhj = fd7Hzgqblhj;
    }

    public BigDecimal getFd7Hzjzczhj() {
        return fd7Hzjzczhj;
    }

    public void setFd7Hzjzczhj(BigDecimal fd7Hzjzczhj) {
        this.fd7Hzjzczhj = fd7Hzjzczhj;
    }

    public BigDecimal getFd8Srgqsjjzczhj() {
        return fd8Srgqsjjzczhj;
    }

    public void setFd8Srgqsjjzczhj(BigDecimal fd8Srgqsjjzczhj) {
        this.fd8Srgqsjjzczhj = fd8Srgqsjjzczhj;
    }

    public BigDecimal getFd8Srgqpgjzczhj() {
        return fd8Srgqpgjzczhj;
    }

    public void setFd8Srgqpgjzczhj(BigDecimal fd8Srgqpgjzczhj) {
        this.fd8Srgqpgjzczhj = fd8Srgqpgjzczhj;
    }

    public BigDecimal getFd8Cjjhj() {
        return fd8Cjjhj;
    }

    public void setFd8Cjjhj(BigDecimal fd8Cjjhj) {
        this.fd8Cjjhj = fd8Cjjhj;
    }

    public BigDecimal getFd8Zrgqblhj() {
        return fd8Zrgqblhj;
    }

    public void setFd8Zrgqblhj(BigDecimal fd8Zrgqblhj) {
        this.fd8Zrgqblhj = fd8Zrgqblhj;
    }

    public BigDecimal getFd8Zrgqsjjzczhj() {
        return fd8Zrgqsjjzczhj;
    }

    public void setFd8Zrgqsjjzczhj(BigDecimal fd8Zrgqsjjzczhj) {
        this.fd8Zrgqsjjzczhj = fd8Zrgqsjjzczhj;
    }

    public BigDecimal getFd8Zrgqpgjzczhj() {
        return fd8Zrgqpgjzczhj;
    }

    public void setFd8Zrgqpgjzczhj(BigDecimal fd8Zrgqpgjzczhj) {
        this.fd8Zrgqpgjzczhj = fd8Zrgqpgjzczhj;
    }

    public BigDecimal getFd8Srgqblhj() {
        return fd8Srgqblhj;
    }

    public void setFd8Srgqblhj(BigDecimal fd8Srgqblhj) {
        this.fd8Srgqblhj = fd8Srgqblhj;
    }

    public BigDecimal getFd2Sggqblhj() {
        return fd2Sggqblhj;
    }

    public void setFd2Sggqblhj(BigDecimal fd2Sggqblhj) {
        this.fd2Sggqblhj = fd2Sggqblhj;
    }

    public BigDecimal getXxFxgs() {
        return xxFxgs;
    }

    public void setXxFxgs(BigDecimal xxFxgs) {
        this.xxFxgs = xxFxgs;
    }

    public BigDecimal getXxGkfxgs() {
        return xxGkfxgs;
    }

    public void setXxGkfxgs(BigDecimal xxGkfxgs) {
        this.xxGkfxgs = xxGkfxgs;
    }

    public BigDecimal getXxAzryzs() {
        return xxAzryzs;
    }

    public void setXxAzryzs(BigDecimal xxAzryzs) {
        this.xxAzryzs = xxAzryzs;
    }

    public BigDecimal getXxBxbfjzczhxbfgqbl() {
        return xxBxbfjzczhxbfgqbl;
    }

    public void setXxBxbfjzczhxbfgqbl(BigDecimal xxBxbfjzczhxbfgqbl) {
        this.xxBxbfjzczhxbfgqbl = xxBxbfjzczhxbfgqbl;
    }

    public BigDecimal getXxYytzgqzhbdqygqbl() {
        return xxYytzgqzhbdqygqbl;
    }

    public void setXxYytzgqzhbdqygqbl(BigDecimal xxYytzgqzhbdqygqbl) {
        this.xxYytzgqzhbdqygqbl = xxYytzgqzhbdqygqbl;
    }

    public String getZlFj() {
        return zlFj;
    }

    public void setZlFj(String zlFj) {
        this.zlFj = zlFj;
    }

    public BigDecimal getXxBdqypgjzczBzz() {
        return xxBdqypgjzczBzz;
    }

    public void setXxBdqypgjzczBzz(BigDecimal xxBdqypgjzczBzz) {
        this.xxBdqypgjzczBzz = xxBdqypgjzczBzz;
    }

    public BigDecimal getXxBdqypgjzczBjz() {
        return xxBdqypgjzczBjz;
    }

    public void setXxBdqypgjzczBjz(BigDecimal xxBdqypgjzczBjz) {
        this.xxBdqypgjzczBjz = xxBdqypgjzczBjz;
    }

    public BigDecimal getXxBdqypgjzczBgz() {
        return xxBdqypgjzczBgz;
    }

    public void setXxBdqypgjzczBgz(BigDecimal xxBdqypgjzczBgz) {
        this.xxBdqypgjzczBgz = xxBdqypgjzczBgz;
    }

    public BigDecimal getXxBdqysjjzczBgz() {
        return xxBdqysjjzczBgz;
    }

    public void setXxBdqysjjzczBgz(BigDecimal xxBdqysjjzczBgz) {
        this.xxBdqysjjzczBgz = xxBdqysjjzczBgz;
    }

    public BigDecimal getXxZgbzXzgb() {
        return xxZgbzXzgb;
    }

    public void setXxZgbzXzgb(BigDecimal xxZgbzXzgb) {
        this.xxZgbzXzgb = xxZgbzXzgb;
    }

    public BigDecimal getXxZgbzJs() {
        return xxZgbzJs;
    }

    public void setXxZgbzJs(BigDecimal xxZgbzJs) {
        this.xxZgbzJs = xxZgbzJs;
    }

    public BigDecimal getXxZgbzTzxz() {
        return xxZgbzTzxz;
    }

    public void setXxZgbzTzxz(BigDecimal xxZgbzTzxz) {
        this.xxZgbzTzxz = xxZgbzTzxz;
    }

    public BigDecimal getXxZgbzXshb() {
        return xxZgbzXshb;
    }

    public void setXxZgbzXshb(BigDecimal xxZgbzXshb) {
        this.xxZgbzXshb = xxZgbzXshb;
    }

    public BigDecimal getXxZgbzGqcz() {
        return xxZgbzGqcz;
    }

    public void setXxZgbzGqcz(BigDecimal xxZgbzGqcz) {
        this.xxZgbzGqcz = xxZgbzGqcz;
    }

    public String getZlYxhztzsYw() {
        return zlYxhztzsYw;
    }

    public void setZlYxhztzsYw(String zlYxhztzsYw) {
        this.zlYxhztzsYw = zlYxhztzsYw;
    }

    public String getZlYxhztzsLy() {
        return zlYxhztzsLy;
    }

    public void setZlYxhztzsLy(String zlYxhztzsLy) {
        this.zlYxhztzsLy = zlYxhztzsLy;
    }

    public String getZlYxhztzsHzdw() {
        return zlYxhztzsHzdw;
    }

    public void setZlYxhztzsHzdw(String zlYxhztzsHzdw) {
        this.zlYxhztzsHzdw = zlYxhztzsHzdw;
    }

    public BigDecimal getXxPgjzcz() {
        return xxPgjzcz;
    }

    public void setXxPgjzcz(BigDecimal xxPgjzcz) {
        this.xxPgjzcz = xxPgjzcz;
    }

    public BigDecimal getXxSjjzcz() {
        return xxSjjzcz;
    }

    public void setXxSjjzcz(BigDecimal xxSjjzcz) {
        this.xxSjjzcz = xxSjjzcz;
    }

    public BigDecimal getXxFxjg() {
        return xxFxjg;
    }

    public void setXxFxjg(BigDecimal xxFxjg) {
        this.xxFxjg = xxFxjg;
    }

    public String getZlJcwjLy() {
        return zlJcwjLy;
    }

    public void setZlJcwjLy(String zlJcwjLy) {
        this.zlJcwjLy = zlJcwjLy;
    }

    public String getZlYzbgZjjgmc() {
        return zlYzbgZjjgmc;
    }

    public void setZlYzbgZjjgmc(String zlYzbgZjjgmc) {
        this.zlYzbgZjjgmc = zlYzbgZjjgmc;
    }

    public String getZlYzbgYzbgh() {
        return zlYzbgYzbgh;
    }

    public void setZlYzbgYzbgh(String zlYzbgYzbgh) {
        this.zlYzbgYzbgh = zlYzbgYzbgh;
    }

    public String getZlYzbgLy() {
        return zlYzbgLy;
    }

    public void setZlYzbgLy(String zlYzbgLy) {
        this.zlYzbgLy = zlYzbgLy;
    }

    public Date getZlYzbgYzcjr() {
        return zlYzbgYzcjr;
    }

    public void setZlYzbgYzcjr(Date zlYzbgYzcjr) {
        this.zlYzbgYzcjr = zlYzbgYzcjr;
    }

    public String getZlQyzcLy() {
        return zlQyzcLy;
    }

    public void setZlQyzcLy(String zlQyzcLy) {
        this.zlQyzcLy = zlQyzcLy;
    }

    public String getZlYyzzLy() {
        return zlYyzzLy;
    }

    public void setZlYyzzLy(String zlYyzzLy) {
        this.zlYyzzLy = zlYyzzLy;
    }

    public String getZlPgbaLy() {
        return zlPgbaLy;
    }

    public void setZlPgbaLy(String zlPgbaLy) {
        this.zlPgbaLy = zlPgbaLy;
    }

    public String getZlFhbpgbaLy() {
        return zlFhbpgbaLy;
    }

    public void setZlFhbpgbaLy(String zlFhbpgbaLy) {
        this.zlFhbpgbaLy = zlFhbpgbaLy;
    }

    public String getZlBdpgbaLy() {
        return zlBdpgbaLy;
    }

    public void setZlBdpgbaLy(String zlBdpgbaLy) {
        this.zlBdpgbaLy = zlBdpgbaLy;
    }

    public String getZlGytdbaLy() {
        return zlGytdbaLy;
    }

    public void setZlGytdbaLy(String zlGytdbaLy) {
        this.zlGytdbaLy = zlGytdbaLy;
    }

    public String getZlJcjgJyjg() {
        return zlJcjgJyjg;
    }

    public void setZlJcjgJyjg(String zlJcjgJyjg) {
        this.zlJcjgJyjg = zlJcjgJyjg;
    }

    public String getZlJcjgJgd() {
        return zlJcjgJgd;
    }

    public void setZlJcjgJgd(String zlJcjgJgd) {
        this.zlJcjgJgd = zlJcjgJgd;
    }

    public Date getZlJcjgCjrq() {
        return zlJcjgCjrq;
    }

    public void setZlJcjgCjrq(Date zlJcjgCjrq) {
        this.zlJcjgCjrq = zlJcjgCjrq;
    }

    public String getZlJcjgLy() {
        return zlJcjgLy;
    }

    public void setZlJcjgLy(String zlJcjgLy) {
        this.zlJcjgLy = zlJcjgLy;
    }

    public String getZlSjbgZjjg() {
        return zlSjbgZjjg;
    }

    public void setZlSjbgZjjg(String zlSjbgZjjg) {
        this.zlSjbgZjjg = zlSjbgZjjg;
    }

    public String getZlSjbgBgh() {
        return zlSjbgBgh;
    }

    public void setZlSjbgBgh(String zlSjbgBgh) {
        this.zlSjbgBgh = zlSjbgBgh;
    }

    public Date getZlSjbgCjrq() {
        return zlSjbgCjrq;
    }

    public void setZlSjbgCjrq(Date zlSjbgCjrq) {
        this.zlSjbgCjrq = zlSjbgCjrq;
    }

    public String getZlSjbgLy() {
        return zlSjbgLy;
    }

    public void setZlSjbgLy(String zlSjbgLy) {
        this.zlSjbgLy = zlSjbgLy;
    }

    public String getZlGqzrLy() {
        return zlGqzrLy;
    }

    public void setZlGqzrLy(String zlGqzrLy) {
        this.zlGqzrLy = zlGqzrLy;
    }

    public String getZlZhxyLy() {
        return zlZhxyLy;
    }

    public void setZlZhxyLy(String zlZhxyLy) {
        this.zlZhxyLy = zlZhxyLy;
    }

    public String getZlHbxysLy() {
        return zlHbxysLy;
    }

    public void setZlHbxysLy(String zlHbxysLy) {
        this.zlHbxysLy = zlHbxysLy;
    }

    public String getZlFlxysLy() {
        return zlFlxysLy;
    }

    public void setZlFlxysLy(String zlFlxysLy) {
        this.zlFlxysLy = zlFlxysLy;
    }

    public String getZlWchzxyLy() {
        return zlWchzxyLy;
    }

    public void setZlWchzxyLy(String zlWchzxyLy) {
        this.zlWchzxyLy = zlWchzxyLy;
    }

    public String getZlJzrsjbgZjjgmc() {
        return zlJzrsjbgZjjgmc;
    }

    public void setZlJzrsjbgZjjgmc(String zlJzrsjbgZjjgmc) {
        this.zlJzrsjbgZjjgmc = zlJzrsjbgZjjgmc;
    }

    public String getZlJzrsjbgYzbgh() {
        return zlJzrsjbgYzbgh;
    }

    public void setZlJzrsjbgYzbgh(String zlJzrsjbgYzbgh) {
        this.zlJzrsjbgYzbgh = zlJzrsjbgYzbgh;
    }

    public String getZlJzrsjbgLy() {
        return zlJzrsjbgLy;
    }

    public void setZlJzrsjbgLy(String zlJzrsjbgLy) {
        this.zlJzrsjbgLy = zlJzrsjbgLy;
    }

    public String getZlJzggMtmc() {
        return zlJzggMtmc;
    }

    public void setZlJzggMtmc(String zlJzggMtmc) {
        this.zlJzggMtmc = zlJzggMtmc;
    }

    public Date getZlJzggGgrq() {
        return zlJzggGgrq;
    }

    public void setZlJzggGgrq(Date zlJzggGgrq) {
        this.zlJzggGgrq = zlJzggGgrq;
    }

    public String getZlJzggLy() {
        return zlJzggLy;
    }

    public void setZlJzggLy(String zlJzggLy) {
        this.zlJzggLy = zlJzggLy;
    }

    public String getXxCjfmc() {
        return xxCjfmc;
    }

    public void setXxCjfmc(String xxCjfmc) {
        this.xxCjfmc = xxCjfmc;
    }

    public BigDecimal getXxSfzj() {
        return xxSfzj;
    }

    public void setXxSfzj(BigDecimal xxSfzj) {
        this.xxSfzj = xxSfzj;
    }

    public String getZlZjyqsjbgZjjg() {
        return zlZjyqsjbgZjjg;
    }

    public void setZlZjyqsjbgZjjg(String zlZjyqsjbgZjjg) {
        this.zlZjyqsjbgZjjg = zlZjyqsjbgZjjg;
    }

    public String getZlZjyqsjbgBgh() {
        return zlZjyqsjbgBgh;
    }

    public void setZlZjyqsjbgBgh(String zlZjyqsjbgBgh) {
        this.zlZjyqsjbgBgh = zlZjyqsjbgBgh;
    }

    public String getZlZjyqsjbgLy() {
        return zlZjyqsjbgLy;
    }

    public void setZlZjyqsjbgLy(String zlZjyqsjbgLy) {
        this.zlZjyqsjbgLy = zlZjyqsjbgLy;
    }

    public String getZlTzxyLy() {
        return zlTzxyLy;
    }

    public void setZlTzxyLy(String zlTzxyLy) {
        this.zlTzxyLy = zlTzxyLy;
    }

    public BigDecimal getXxQsjzcz() {
        return xxQsjzcz;
    }

    public void setXxQsjzcz(BigDecimal xxQsjzcz) {
        this.xxQsjzcz = xxQsjzcz;
    }

    public BigDecimal getXxQsfy() {
        return xxQsfy;
    }

    public void setXxQsfy(BigDecimal xxQsfy) {
        this.xxQsfy = xxQsfy;
    }

    public BigDecimal getXxQczw() {
        return xxQczw;
    }

    public void setXxQczw(BigDecimal xxQczw) {
        this.xxQczw = xxQczw;
    }

    public BigDecimal getXxPcccze() {
        return xxPcccze;
    }

    public void setXxPcccze(BigDecimal xxPcccze) {
        this.xxPcccze = xxPcccze;
    }

    public BigDecimal getXxPcfyhgyzw() {
        return xxPcfyhgyzw;
    }

    public void setXxPcfyhgyzw(BigDecimal xxPcfyhgyzw) {
        this.xxPcfyhgyzw = xxPcfyhgyzw;
    }

    public BigDecimal getXxPtzw() {
        return xxPtzw;
    }

    public void setXxPtzw(BigDecimal xxPtzw) {
        this.xxPtzw = xxPtzw;
    }

    public String getZlQsbgZjjgmc() {
        return zlQsbgZjjgmc;
    }

    public void setZlQsbgZjjgmc(String zlQsbgZjjgmc) {
        this.zlQsbgZjjgmc = zlQsbgZjjgmc;
    }

    public String getZlQsbgBgh() {
        return zlQsbgBgh;
    }

    public void setZlQsbgBgh(String zlQsbgBgh) {
        this.zlQsbgBgh = zlQsbgBgh;
    }

    public String getZlQsbgLy() {
        return zlQsbgLy;
    }

    public void setZlQsbgLy(String zlQsbgLy) {
        this.zlQsbgLy = zlQsbgLy;
    }

    public String getZlZxggMtmc() {
        return zlZxggMtmc;
    }

    public void setZlZxggMtmc(String zlZxggMtmc) {
        this.zlZxggMtmc = zlZxggMtmc;
    }

    public Date getZlZxggGgrq() {
        return zlZxggGgrq;
    }

    public void setZlZxggGgrq(Date zlZxggGgrq) {
        this.zlZxggGgrq = zlZxggGgrq;
    }

    public String getZlZxggLy() {
        return zlZxggLy;
    }

    public void setZlZxggLy(String zlZxggLy) {
        this.zlZxggLy = zlZxggLy;
    }

    public String getZlGszxzmLy() {
        return zlGszxzmLy;
    }

    public void setZlGszxzmLy(String zlGszxzmLy) {
        this.zlGszxzmLy = zlGszxzmLy;
    }

    public String getZlPcggMtmc() {
        return zlPcggMtmc;
    }

    public void setZlPcggMtmc(String zlPcggMtmc) {
        this.zlPcggMtmc = zlPcggMtmc;
    }

    public Date getZlPcggGgrq() {
        return zlPcggGgrq;
    }

    public void setZlPcggGgrq(Date zlPcggGgrq) {
        this.zlPcggGgrq = zlPcggGgrq;
    }

    public String getZlPcggLy() {
        return zlPcggLy;
    }

    public void setZlPcggLy(String zlPcggLy) {
        this.zlPcggLy = zlPcggLy;
    }

    public String getZlTzxyYw() {
        return zlTzxyYw;
    }

    public void setZlTzxyYw(String zlTzxyYw) {
        this.zlTzxyYw = zlTzxyYw;
    }

    public String getZlJcwjYw() {
        return zlJcwjYw;
    }

    public void setZlJcwjYw(String zlJcwjYw) {
        this.zlJcwjYw = zlJcwjYw;
    }

    public String getZlJzggYw() {
        return zlJzggYw;
    }

    public void setZlJzggYw(String zlJzggYw) {
        this.zlJzggYw = zlJzggYw;
    }

    public String getZlPcggYw() {
        return zlPcggYw;
    }

    public void setZlPcggYw(String zlPcggYw) {
        this.zlPcggYw = zlPcggYw;
    }

    public String getZlJzrsjbgYw() {
        return zlJzrsjbgYw;
    }

    public void setZlJzrsjbgYw(String zlJzrsjbgYw) {
        this.zlJzrsjbgYw = zlJzrsjbgYw;
    }

    public String getZlQyzcYw() {
        return zlQyzcYw;
    }

    public void setZlQyzcYw(String zlQyzcYw) {
        this.zlQyzcYw = zlQyzcYw;
    }

    public String getZlJcjgYw() {
        return zlJcjgYw;
    }

    public void setZlJcjgYw(String zlJcjgYw) {
        this.zlJcjgYw = zlJcjgYw;
    }

    public String getZlSjbgYw() {
        return zlSjbgYw;
    }

    public void setZlSjbgYw(String zlSjbgYw) {
        this.zlSjbgYw = zlSjbgYw;
    }

    public String getZlFhbpgbaYw() {
        return zlFhbpgbaYw;
    }

    public void setZlFhbpgbaYw(String zlFhbpgbaYw) {
        this.zlFhbpgbaYw = zlFhbpgbaYw;
    }

    public String getZlYyzzYw() {
        return zlYyzzYw;
    }

    public void setZlYyzzYw(String zlYyzzYw) {
        this.zlYyzzYw = zlYyzzYw;
    }

    public String getZlPgbaYw() {
        return zlPgbaYw;
    }

    public void setZlPgbaYw(String zlPgbaYw) {
        this.zlPgbaYw = zlPgbaYw;
    }

    public String getZlGszxzmYw() {
        return zlGszxzmYw;
    }

    public void setZlGszxzmYw(String zlGszxzmYw) {
        this.zlGszxzmYw = zlGszxzmYw;
    }

    public String getZlGytdbaYw() {
        return zlGytdbaYw;
    }

    public void setZlGytdbaYw(String zlGytdbaYw) {
        this.zlGytdbaYw = zlGytdbaYw;
    }

    public String getZlYzbgYw() {
        return zlYzbgYw;
    }

    public void setZlYzbgYw(String zlYzbgYw) {
        this.zlYzbgYw = zlYzbgYw;
    }

    public String getZlHbxysYw() {
        return zlHbxysYw;
    }

    public void setZlHbxysYw(String zlHbxysYw) {
        this.zlHbxysYw = zlHbxysYw;
    }

    public String getZlZhxyYw() {
        return zlZhxyYw;
    }

    public void setZlZhxyYw(String zlZhxyYw) {
        this.zlZhxyYw = zlZhxyYw;
    }

    public String getZlGqzrYw() {
        return zlGqzrYw;
    }

    public void setZlGqzrYw(String zlGqzrYw) {
        this.zlGqzrYw = zlGqzrYw;
    }

    public String getZlWchzxyYw() {
        return zlWchzxyYw;
    }

    public void setZlWchzxyYw(String zlWchzxyYw) {
        this.zlWchzxyYw = zlWchzxyYw;
    }

    public String getZlBdpgbaYw() {
        return zlBdpgbaYw;
    }

    public void setZlBdpgbaYw(String zlBdpgbaYw) {
        this.zlBdpgbaYw = zlBdpgbaYw;
    }

    public String getZlZjyqsjbgYw() {
        return zlZjyqsjbgYw;
    }

    public void setZlZjyqsjbgYw(String zlZjyqsjbgYw) {
        this.zlZjyqsjbgYw = zlZjyqsjbgYw;
    }

    public String getZlFlxysYw() {
        return zlFlxysYw;
    }

    public void setZlFlxysYw(String zlFlxysYw) {
        this.zlFlxysYw = zlFlxysYw;
    }

    public String getZlZxggYw() {
        return zlZxggYw;
    }

    public void setZlZxggYw(String zlZxggYw) {
        this.zlZxggYw = zlZxggYw;
    }

    public String getZlQsbgYw() {
        return zlQsbgYw;
    }

    public void setZlQsbgYw(String zlQsbgYw) {
        this.zlQsbgYw = zlQsbgYw;
    }

    public String getXxBdqyxz() {
        return xxBdqyxz;
    }

    public void setXxBdqyxz(String xxBdqyxz) {
        this.xxBdqyxz = xxBdqyxz;
    }

    public String getXxBxbfxz() {
        return xxBxbfxz;
    }

    public void setXxBxbfxz(String xxBxbfxz) {
        this.xxBxbfxz = xxBxbfxz;
    }

    public String getXxJsyy() {
        return xxJsyy;
    }

    public void setXxJsyy(String xxJsyy) {
        this.xxJsyy = xxJsyy;
    }

    public Date getZlGszxzmZxrq() {
        return zlGszxzmZxrq;
    }

    public void setZlGszxzmZxrq(Date zlGszxzmZxrq) {
        this.zlGszxzmZxrq = zlGszxzmZxrq;
    }

    public String getZlJcwjDwmc() {
        return zlJcwjDwmc;
    }

    public void setZlJcwjDwmc(String zlJcwjDwmc) {
        this.zlJcwjDwmc = zlJcwjDwmc;
    }

    public String getZlJcwjWjmc() {
        return zlJcwjWjmc;
    }

    public void setZlJcwjWjmc(String zlJcwjWjmc) {
        this.zlJcwjWjmc = zlJcwjWjmc;
    }

    public String getZlJcwjWjh() {
        return zlJcwjWjh;
    }

    public void setZlJcwjWjh(String zlJcwjWjh) {
        this.zlJcwjWjh = zlJcwjWjh;
    }

    public String getZlPgbaZjjgmc() {
        return zlPgbaZjjgmc;
    }

    public void setZlPgbaZjjgmc(String zlPgbaZjjgmc) {
        this.zlPgbaZjjgmc = zlPgbaZjjgmc;
    }

    public String getZlPgbaPgbgh() {
        return zlPgbaPgbgh;
    }

    public void setZlPgbaPgbgh(String zlPgbaPgbgh) {
        this.zlPgbaPgbgh = zlPgbaPgbgh;
    }

    public String getZlPgbaHzdwmc() {
        return zlPgbaHzdwmc;
    }

    public void setZlPgbaHzdwmc(String zlPgbaHzdwmc) {
        this.zlPgbaHzdwmc = zlPgbaHzdwmc;
    }

    public String getZlPgbaHzwjh() {
        return zlPgbaHzwjh;
    }

    public void setZlPgbaHzwjh(String zlPgbaHzwjh) {
        this.zlPgbaHzwjh = zlPgbaHzwjh;
    }

    public String getZlFhbpgbaZjjgmc() {
        return zlFhbpgbaZjjgmc;
    }

    public void setZlFhbpgbaZjjgmc(String zlFhbpgbaZjjgmc) {
        this.zlFhbpgbaZjjgmc = zlFhbpgbaZjjgmc;
    }

    public String getZlFhbpgbaPgbgh() {
        return zlFhbpgbaPgbgh;
    }

    public void setZlFhbpgbaPgbgh(String zlFhbpgbaPgbgh) {
        this.zlFhbpgbaPgbgh = zlFhbpgbaPgbgh;
    }

    public String getZlFhbpgbaHzdwmc() {
        return zlFhbpgbaHzdwmc;
    }

    public void setZlFhbpgbaHzdwmc(String zlFhbpgbaHzdwmc) {
        this.zlFhbpgbaHzdwmc = zlFhbpgbaHzdwmc;
    }

    public String getZlFhbpgbaHzwjh() {
        return zlFhbpgbaHzwjh;
    }

    public void setZlFhbpgbaHzwjh(String zlFhbpgbaHzwjh) {
        this.zlFhbpgbaHzwjh = zlFhbpgbaHzwjh;
    }

    public String getZlBdpgbaZjjgmc() {
        return zlBdpgbaZjjgmc;
    }

    public void setZlBdpgbaZjjgmc(String zlBdpgbaZjjgmc) {
        this.zlBdpgbaZjjgmc = zlBdpgbaZjjgmc;
    }

    public String getZlBdpgbaPgbgh() {
        return zlBdpgbaPgbgh;
    }

    public void setZlBdpgbaPgbgh(String zlBdpgbaPgbgh) {
        this.zlBdpgbaPgbgh = zlBdpgbaPgbgh;
    }

    public String getZlBdpgbaHzdwmc() {
        return zlBdpgbaHzdwmc;
    }

    public void setZlBdpgbaHzdwmc(String zlBdpgbaHzdwmc) {
        this.zlBdpgbaHzdwmc = zlBdpgbaHzdwmc;
    }

    public String getZlBdpgbaHzwjh() {
        return zlBdpgbaHzwjh;
    }

    public void setZlBdpgbaHzwjh(String zlBdpgbaHzwjh) {
        this.zlBdpgbaHzwjh = zlBdpgbaHzwjh;
    }

    public String getZlGytdbaPzdw() {
        return zlGytdbaPzdw;
    }

    public void setZlGytdbaPzdw(String zlGytdbaPzdw) {
        this.zlGytdbaPzdw = zlGytdbaPzdw;
    }

    public String getZlGytdbaPzwh() {
        return zlGytdbaPzwh;
    }

    public void setZlGytdbaPzwh(String zlGytdbaPzwh) {
        this.zlGytdbaPzwh = zlGytdbaPzwh;
    }

    public String getZlYwblsqwj() {
        return zlYwblsqwj;
    }

    public void setZlYwblsqwj(String zlYwblsqwj) {
        this.zlYwblsqwj = zlYwblsqwj;
    }

    public String getZlZhypgbabYw() {
        return zlZhypgbabYw;
    }

    public void setZlZhypgbabYw(String zlZhypgbabYw) {
        this.zlZhypgbabYw = zlZhypgbabYw;
    }

    public String getZlZhypgbabZjjgmc() {
        return zlZhypgbabZjjgmc;
    }

    public void setZlZhypgbabZjjgmc(String zlZhypgbabZjjgmc) {
        this.zlZhypgbabZjjgmc = zlZhypgbabZjjgmc;
    }

    public String getZlZhypgbabPgbgh() {
        return zlZhypgbabPgbgh;
    }

    public void setZlZhypgbabPgbgh(String zlZhypgbabPgbgh) {
        this.zlZhypgbabPgbgh = zlZhypgbabPgbgh;
    }

    public String getZlZhypgbabHzdwmc() {
        return zlZhypgbabHzdwmc;
    }

    public void setZlZhypgbabHzdwmc(String zlZhypgbabHzdwmc) {
        this.zlZhypgbabHzdwmc = zlZhypgbabHzdwmc;
    }

    public String getZlZhypgbabHzwjh() {
        return zlZhypgbabHzwjh;
    }

    public void setZlZhypgbabHzwjh(String zlZhypgbabHzwjh) {
        this.zlZhypgbabHzwjh = zlZhypgbabHzwjh;
    }

    public String getZlZhypgbabLy() {
        return zlZhypgbabLy;
    }

    public void setZlZhypgbabLy(String zlZhypgbabLy) {
        this.zlZhypgbabLy = zlZhypgbabLy;
    }

    public String getZlZhepgbabYw() {
        return zlZhepgbabYw;
    }

    public void setZlZhepgbabYw(String zlZhepgbabYw) {
        this.zlZhepgbabYw = zlZhepgbabYw;
    }

    public String getZlZhepgbabZjjgmc() {
        return zlZhepgbabZjjgmc;
    }

    public void setZlZhepgbabZjjgmc(String zlZhepgbabZjjgmc) {
        this.zlZhepgbabZjjgmc = zlZhepgbabZjjgmc;
    }

    public String getZlZhepgbabPgbgh() {
        return zlZhepgbabPgbgh;
    }

    public void setZlZhepgbabPgbgh(String zlZhepgbabPgbgh) {
        this.zlZhepgbabPgbgh = zlZhepgbabPgbgh;
    }

    public String getZlZhepgbabHzdwmc() {
        return zlZhepgbabHzdwmc;
    }

    public void setZlZhepgbabHzdwmc(String zlZhepgbabHzdwmc) {
        this.zlZhepgbabHzdwmc = zlZhepgbabHzdwmc;
    }

    public String getZlZhepgbabHzwjh() {
        return zlZhepgbabHzwjh;
    }

    public void setZlZhepgbabHzwjh(String zlZhepgbabHzwjh) {
        this.zlZhepgbabHzwjh = zlZhepgbabHzwjh;
    }

    public String getZlZhepgbabLy() {
        return zlZhepgbabLy;
    }

    public void setZlZhepgbabLy(String zlZhepgbabLy) {
        this.zlZhepgbabLy = zlZhepgbabLy;
    }

    public String getZlXbpgbabYw() {
        return zlXbpgbabYw;
    }

    public void setZlXbpgbabYw(String zlXbpgbabYw) {
        this.zlXbpgbabYw = zlXbpgbabYw;
    }

    public String getZlXbpgbabZjjgmc() {
        return zlXbpgbabZjjgmc;
    }

    public void setZlXbpgbabZjjgmc(String zlXbpgbabZjjgmc) {
        this.zlXbpgbabZjjgmc = zlXbpgbabZjjgmc;
    }

    public String getZlXbpgbabPgbgh() {
        return zlXbpgbabPgbgh;
    }

    public void setZlXbpgbabPgbgh(String zlXbpgbabPgbgh) {
        this.zlXbpgbabPgbgh = zlXbpgbabPgbgh;
    }

    public String getZlXbpgbabHzdwmc() {
        return zlXbpgbabHzdwmc;
    }

    public void setZlXbpgbabHzdwmc(String zlXbpgbabHzdwmc) {
        this.zlXbpgbabHzdwmc = zlXbpgbabHzdwmc;
    }

    public String getZlXbpgbabHzwjh() {
        return zlXbpgbabHzwjh;
    }

    public void setZlXbpgbabHzwjh(String zlXbpgbabHzwjh) {
        this.zlXbpgbabHzwjh = zlXbpgbabHzwjh;
    }

    public String getZlXbpgbabLy() {
        return zlXbpgbabLy;
    }

    public void setZlXbpgbabLy(String zlXbpgbabLy) {
        this.zlXbpgbabLy = zlXbpgbabLy;
    }

    public String getZlBxbpgbabYw() {
        return zlBxbpgbabYw;
    }

    public void setZlBxbpgbabYw(String zlBxbpgbabYw) {
        this.zlBxbpgbabYw = zlBxbpgbabYw;
    }

    public String getZlBxbpgbabZjjgmc() {
        return zlBxbpgbabZjjgmc;
    }

    public void setZlBxbpgbabZjjgmc(String zlBxbpgbabZjjgmc) {
        this.zlBxbpgbabZjjgmc = zlBxbpgbabZjjgmc;
    }

    public String getZlBxbpgbabPgbgh() {
        return zlBxbpgbabPgbgh;
    }

    public void setZlBxbpgbabPgbgh(String zlBxbpgbabPgbgh) {
        this.zlBxbpgbabPgbgh = zlBxbpgbabPgbgh;
    }

    public String getZlBxbpgbabHzdwmc() {
        return zlBxbpgbabHzdwmc;
    }

    public void setZlBxbpgbabHzdwmc(String zlBxbpgbabHzdwmc) {
        this.zlBxbpgbabHzdwmc = zlBxbpgbabHzdwmc;
    }

    public String getZlBxbpgbabHzwjh() {
        return zlBxbpgbabHzwjh;
    }

    public void setZlBxbpgbabHzwjh(String zlBxbpgbabHzwjh) {
        this.zlBxbpgbabHzwjh = zlBxbpgbabHzwjh;
    }

    public String getZlBxbpgbabLy() {
        return zlBxbpgbabLy;
    }

    public void setZlBxbpgbabLy(String zlBxbpgbabLy) {
        this.zlBxbpgbabLy = zlBxbpgbabLy;
    }

    public String getZlTjpgbabYw() {
        return zlTjpgbabYw;
    }

    public void setZlTjpgbabYw(String zlTjpgbabYw) {
        this.zlTjpgbabYw = zlTjpgbabYw;
    }

    public String getZlTjpgbabZjjgmc() {
        return zlTjpgbabZjjgmc;
    }

    public void setZlTjpgbabZjjgmc(String zlTjpgbabZjjgmc) {
        this.zlTjpgbabZjjgmc = zlTjpgbabZjjgmc;
    }

    public String getZlTjpgbabPgbgh() {
        return zlTjpgbabPgbgh;
    }

    public void setZlTjpgbabPgbgh(String zlTjpgbabPgbgh) {
        this.zlTjpgbabPgbgh = zlTjpgbabPgbgh;
    }

    public String getZlTjpgbabHzdwmc() {
        return zlTjpgbabHzdwmc;
    }

    public void setZlTjpgbabHzdwmc(String zlTjpgbabHzdwmc) {
        this.zlTjpgbabHzdwmc = zlTjpgbabHzdwmc;
    }

    public String getZlTjpgbabHzwjh() {
        return zlTjpgbabHzwjh;
    }

    public void setZlTjpgbabHzwjh(String zlTjpgbabHzwjh) {
        this.zlTjpgbabHzwjh = zlTjpgbabHzwjh;
    }

    public String getZlTjpgbabLy() {
        return zlTjpgbabLy;
    }

    public void setZlTjpgbabLy(String zlTjpgbabLy) {
        this.zlTjpgbabLy = zlTjpgbabLy;
    }

    public String getZlSyzcczxyYw() {
        return zlSyzcczxyYw;
    }

    public void setZlSyzcczxyYw(String zlSyzcczxyYw) {
        this.zlSyzcczxyYw = zlSyzcczxyYw;
    }

    public String getZlSyzcczxyLy() {
        return zlSyzcczxyLy;
    }

    public void setZlSyzcczxyLy(String zlSyzcczxyLy) {
        this.zlSyzcczxyLy = zlSyzcczxyLy;
    }

    public String getZlZgdbdhjyYw() {
        return zlZgdbdhjyYw;
    }

    public void setZlZgdbdhjyYw(String zlZgdbdhjyYw) {
        this.zlZgdbdhjyYw = zlZgdbdhjyYw;
    }

    public String getZlZgdbdhjyYj() {
        return zlZgdbdhjyYj;
    }

    public void setZlZgdbdhjyYj(String zlZgdbdhjyYj) {
        this.zlZgdbdhjyYj = zlZgdbdhjyYj;
    }

    public String getZlZgdbdhjyLy() {
        return zlZgdbdhjyLy;
    }

    public void setZlZgdbdhjyLy(String zlZgdbdhjyLy) {
        this.zlZgdbdhjyLy = zlZgdbdhjyLy;
    }

    public String getZlGqszfawjYw() {
        return zlGqszfawjYw;
    }

    public void setZlGqszfawjYw(String zlGqszfawjYw) {
        this.zlGqszfawjYw = zlGqszfawjYw;
    }

    public String getZlGqszfawjPzdw() {
        return zlGqszfawjPzdw;
    }

    public void setZlGqszfawjPzdw(String zlGqszfawjPzdw) {
        this.zlGqszfawjPzdw = zlGqszfawjPzdw;
    }

    public String getZlGqszfawjPzwh() {
        return zlGqszfawjPzwh;
    }

    public void setZlGqszfawjPzwh(String zlGqszfawjPzwh) {
        this.zlGqszfawjPzwh = zlGqszfawjPzwh;
    }

    public String getZlGqszfawjLy() {
        return zlGqszfawjLy;
    }

    public void setZlGqszfawjLy(String zlGqszfawjLy) {
        this.zlGqszfawjLy = zlGqszfawjLy;
    }

    public String getZlGdqkdjbYw() {
        return zlGdqkdjbYw;
    }

    public void setZlGdqkdjbYw(String zlGdqkdjbYw) {
        this.zlGdqkdjbYw = zlGdqkdjbYw;
    }

    public String getJbxxId() {
        return jbxxId;
    }

    public void setJbxxId(String jbxxId) {
        this.jbxxId = jbxxId;
    }

    public String getYwzbbId() {
        return ywzbbId;
    }

    public void setYwzbbId(String ywzbbId) {
        this.ywzbbId = ywzbbId;
    }

    public String getRgType() {
        return rgType;
    }

    public void setRgType(String rgType) {
        this.rgType = rgType;
    }

    public List<Xgzjgfd> getFd3TableData() {
        return fd3TableData;
    }

    public void setFd3TableData(List<Xgzjgfd> fd3TableData) {
        this.fd3TableData = fd3TableData;
    }

    public String getJbCzrzzjgid() {
        return jbCzrzzjgid;
    }

    public void setJbCzrzzjgid(String jbCzrzzjgid) {
        this.jbCzrzzjgid = jbCzrzzjgid;
    }

    public BigDecimal getJc30sspj() {
        return jc30sspj;
    }

    public void setJc30sspj(BigDecimal jc30sspj) {
        this.jc30sspj = jc30sspj;
    }

    public BigDecimal getJcMgjzcz() {
        return jcMgjzcz;
    }

    public void setJcMgjzcz(BigDecimal jcMgjzcz) {
        this.jcMgjzcz = jcMgjzcz;
    }

    public BigDecimal getJcJcgs() {
        return jcJcgs;
    }

    public void setJcJcgs(BigDecimal jcJcgs) {
        this.jcJcgs = jcJcgs;
    }

    public BigDecimal getJcJcjj() {
        return jcJcjj;
    }

    public void setJcJcjj(BigDecimal jcJcjj) {
        this.jcJcjj = jcJcjj;
    }

    public String getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(String businessNature) {
        this.businessNature = businessNature;
    }

    public String getBdlx() {
        return bdlx;
    }

    public void setBdlx(String bdlx) {
        this.bdlx = bdlx;
    }

    public String getBdmc() {
        return bdmc;
    }

    public void setBdmc(String bdmc) {
        this.bdmc = bdmc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSshy() {
        return sshy;
    }

    public void setSshy(String sshy) {
        this.sshy = sshy;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getTze() {
        return tze;
    }

    public void setTze(BigDecimal tze) {
        this.tze = tze;
    }

    public BigDecimal getTzbl() {
        return tzbl;
    }

    public void setTzbl(BigDecimal tzbl) {
        this.tzbl = tzbl;
    }

    public String getSfsjkz() {
        return sfsjkz;
    }

    public void setSfsjkz(String sfsjkz) {
        this.sfsjkz = sfsjkz;
    }

    public List<Hhrqkfd> getHhrqkfdList() {
        return hhrqkfdList;
    }

    public void setHhrqkfdList(List<Hhrqkfd> hhrqkfdList) {
        this.hhrqkfdList = hhrqkfdList;
    }

    public List<Dwtzqkfd> getDwtzqkfdList() {
        return dwtzqkfdList;
    }

    public void setDwtzqkfdList(List<Dwtzqkfd> dwtzqkfdList) {
        this.dwtzqkfdList = dwtzqkfdList;
    }

    public String getHhCompanyName() {
        return hhCompanyName;
    }

    public void setHhCompanyName(String hhCompanyName) {
        this.hhCompanyName = hhCompanyName;
    }

    public String getHhZxswhhr() {
        return hhZxswhhr;
    }

    public void setHhZxswhhr(String hhZxswhhr) {
        this.hhZxswhhr = hhZxswhhr;
    }

    public String getHhZxswhhrCode() {
        return hhZxswhhrCode;
    }

    public void setHhZxswhhrCode(String hhZxswhhrCode) {
        this.hhZxswhhrCode = hhZxswhhrCode;
    }

    public String getHhQx() {
        return hhQx;
    }

    public void setHhQx(String hhQx) {
        this.hhQx = hhQx;
    }

    public String getHhZyjycs() {
        return hhZyjycs;
    }

    public void setHhZyjycs(String hhZyjycs) {
        this.hhZyjycs = hhZyjycs;
    }

    public String getHhSfsmtzjj() {
        return hhSfsmtzjj;
    }

    public void setHhSfsmtzjj(String hhSfsmtzjj) {
        this.hhSfsmtzjj = hhSfsmtzjj;
    }

    public String getHhJyfw() {
        return hhJyfw;
    }

    public void setHhJyfw(String hhJyfw) {
        this.hhJyfw = hhJyfw;
    }

    public BigDecimal getHhRjcze() {
        return hhRjcze;
    }

    public void setHhRjcze(BigDecimal hhRjcze) {
        this.hhRjcze = hhRjcze;
    }

    public String getHhRjczebz() {
        return hhRjczebz;
    }

    public void setHhRjczebz(String hhRjczebz) {
        this.hhRjczebz = hhRjczebz;
    }

    public BigDecimal getHhSjcze() {
        return hhSjcze;
    }

    public void setHhSjcze(BigDecimal hhSjcze) {
        this.hhSjcze = hhSjcze;
    }

    public String getHhSjczebz() {
        return hhSjczebz;
    }

    public void setHhSjczebz(String hhSjczebz) {
        this.hhSjczebz = hhSjczebz;
    }

    public String getHhGjczqy() {
        return hhGjczqy;
    }

    public void setHhGjczqy(String hhGjczqy) {
        this.hhGjczqy = hhGjczqy;
    }

    public String getHhGjczqyCode() {
        return hhGjczqyCode;
    }

    public void setHhGjczqyCode(String hhGjczqyCode) {
        this.hhGjczqyCode = hhGjczqyCode;
    }

    public String getHhCzqy() {
        return hhCzqy;
    }

    public void setHhCzqy(String hhCzqy) {
        this.hhCzqy = hhCzqy;
    }

    public String getHhCzqyCode() {
        return hhCzqyCode;
    }

    public void setHhCzqyCode(String hhCzqyCode) {
        this.hhCzqyCode = hhCzqyCode;
    }

    public String getHhHhxy() {
        return hhHhxy;
    }

    public void setHhHhxy(String hhHhxy) {
        this.hhHhxy = hhHhxy;
    }

    public BigDecimal getHjrjcze() {
        return hjrjcze;
    }

    public void setHjrjcze(BigDecimal hjrjcze) {
        this.hjrjcze = hjrjcze;
    }

    public BigDecimal getHjrjczbl() {
        return hjrjczbl;
    }

    public void setHjrjczbl(BigDecimal hjrjczbl) {
        this.hjrjczbl = hjrjczbl;
    }

    public BigDecimal getHjsjcze() {
        return hjsjcze;
    }

    public void setHjsjcze(BigDecimal hjsjcze) {
        this.hjsjcze = hjsjcze;
    }

    public String getHhCzqyId() {
        return hhCzqyId;
    }

    public void setHhCzqyId(String hhCzqyId) {
        this.hhCzqyId = hhCzqyId;
    }

    public BigDecimal getHhSszb() {
        return hhSszb;
    }

    public void setHhSszb(BigDecimal hhSszb) {
        this.hhSszb = hhSszb;
    }

    public String getZlYxhztzs() {
        return zlYxhztzs;
    }

    public void setZlYxhztzs(String zlYxhztzs) {
        this.zlYxhztzs = zlYxhztzs;
    }

    public List<CjffdVo> getFd5TableData() {
        return fd5TableData;
    }

    public void setFd5TableData(List<CjffdVo> fd5TableData) {
        this.fd5TableData = fd5TableData;
    }

    public String getJbDataStatus() {
        return jbDataStatus;
    }

    public void setJbDataStatus(String jbDataStatus) {
        this.jbDataStatus = jbDataStatus;
    }

    public String getJbMyqysbsbz() {
        return jbMyqysbsbz;
    }

    public void setJbMyqysbsbz(String jbMyqysbsbz) {
        this.jbMyqysbsbz = jbMyqysbsbz;
    }

    public String getJbMyqysbs() {
        return jbMyqysbs;
    }

    public void setJbMyqysbs(String jbMyqysbs) {
        this.jbMyqysbs = jbMyqysbs;
    }

    public String getJbWzqysbsbz() {
        return jbWzqysbsbz;
    }

    public void setJbWzqysbsbz(String jbWzqysbsbz) {
        this.jbWzqysbsbz = jbWzqysbsbz;
    }

    public String getJbWzqysbs() {
        return jbWzqysbs;
    }

    public void setJbWzqysbs(String jbWzqysbs) {
        this.jbWzqysbs = jbWzqysbs;
    }

    public String getJbZrrsbsbz() {
        return jbZrrsbsbz;
    }

    public void setJbZrrsbsbz(String jbZrrsbsbz) {
        this.jbZrrsbsbz = jbZrrsbsbz;
    }

    public String getJbZrrsbs() {
        return jbZrrsbs;
    }

    public void setJbZrrsbs(String jbZrrsbs) {
        this.jbZrrsbs = jbZrrsbs;
    }
}
