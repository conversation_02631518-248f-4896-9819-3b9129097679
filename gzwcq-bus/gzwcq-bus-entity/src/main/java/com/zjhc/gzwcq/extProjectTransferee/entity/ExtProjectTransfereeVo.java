package com.zjhc.gzwcq.extProjectTransferee.entity;

import com.zjhc.gzwcq.extProjectComplete.enums.extProjectCompleteEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ext_project_transferee <br/>
 *         描述：项目受让方 <br/>
 */
public class ExtProjectTransfereeVo extends ExtProjectTransferee {

	private static final long serialVersionUID = 18L;

	private List<ExtProjectTransfereeVo> extProjectTransfereeList;
	private String xmlxName;
	private String zrfmc;

	public String getZrfmc() {
		return zrfmc;
	}

	public void setZrfmc(String zrfmc) {
		this.zrfmc = zrfmc;
	}

	@Override
	public void setXmlx(String xmlx) {
		String textName;
		if (StringUtils.isNotBlank(xmlx)) {
			textName = extProjectCompleteEnum.getTextName(xmlx);
			this.xmlxName = textName;
		}
		this.xmlx = xmlx;
	}

	public void setXmlxName(String xmlxName) {
		this.xmlxName = xmlxName;
	}

	public String getXmlxName() {
		String textName;
		if (StringUtils.isBlank(this.xmlxName)) {
			if (StringUtils.isNotBlank(this.xmlx)) {
				textName = extProjectCompleteEnum.getTextName(xmlx);
				this.xmlxName = textName;
			}
		}
		return xmlxName;
	}
	public ExtProjectTransfereeVo() {
		super();
	}

  	public ExtProjectTransfereeVo(Long id) {
  		super();
  		this.id = id;
	}

	public List<ExtProjectTransfereeVo> getExtProjectTransfereeList() {
		return extProjectTransfereeList;
	}

	public void setExtProjectTransfereeList(List<ExtProjectTransfereeVo> extProjectTransfereeList) {
		this.extProjectTransfereeList = extProjectTransfereeList;
	}

}
