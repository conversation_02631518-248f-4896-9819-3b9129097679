package com.zjhc.gzwcq.czfd.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_czfd <br/>
 *         描述：产权出资浮动行 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Czfd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期")
	protected String datatime;// 数据时期
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="出资人名称")
	protected String fdCzrmc;// 出资人名称
  	@ApiParam(value="出资额")
	protected BigDecimal fdCze;// 出资额
  	@ApiParam(value="实缴注册金")
	protected BigDecimal fdSjzcj;// 实缴注册金
  	@ApiParam(value="股权比例")
	protected BigDecimal fdGqbl;// 股权比例
  	@ApiParam(value="出资人组织机构代码")
	protected String fdCzrzzjgdm;// 出资人组织机构代码
  	@ApiParam(value="出资额币种")
	protected BigDecimal fdCzebz;// 出资额币种
  	@ApiParam(value="实缴注册资本币种")
	protected BigDecimal fdSjzczbbz;// 实缴注册资本币种
  	@ApiParam(value="出资人类别")
	protected String fdCzrlb;// 出资人类别
  	@ApiParam(value="认缴资本")
	protected BigDecimal fdRjzb;// 认缴资本
  	@ApiParam(value="认缴资本币种")
	protected BigDecimal fdRjzbbz;// 认缴资本币种
	@ApiParam(value="出资方式")
	protected String fdCzfs;// 出资方式
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	@ApiParam(value="缴付期限")
	protected Date fdJfqx;// 缴付期限
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Czfd() {
		super();
	}
	
  	public Czfd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public String getFdCzrmc() {
		return fdCzrmc;
	}
	public void setFdCzrmc(String fdCzrmc) {
		this.fdCzrmc = fdCzrmc;
	}
	public BigDecimal getFdCze() {
		return fdCze;
	}
	public void setFdCze(BigDecimal fdCze) {
		this.fdCze = fdCze;
	}
	public BigDecimal getFdSjzcj() {
		return fdSjzcj;
	}
	public void setFdSjzcj(BigDecimal fdSjzcj) {
		this.fdSjzcj = fdSjzcj;
	}
	public BigDecimal getFdGqbl() {
		return fdGqbl;
	}
	public void setFdGqbl(BigDecimal fdGqbl) {
		this.fdGqbl = fdGqbl;
	}
	public String getFdCzrzzjgdm() {
		return fdCzrzzjgdm;
	}
	public void setFdCzrzzjgdm(String fdCzrzzjgdm) {
		this.fdCzrzzjgdm = fdCzrzzjgdm;
	}
	public BigDecimal getFdCzebz() {
		return fdCzebz;
	}
	public void setFdCzebz(BigDecimal fdCzebz) {
		this.fdCzebz = fdCzebz;
	}
	public BigDecimal getFdSjzczbbz() {
		return fdSjzczbbz;
	}
	public void setFdSjzczbbz(BigDecimal fdSjzczbbz) {
		this.fdSjzczbbz = fdSjzczbbz;
	}
	public String getFdCzrlb() {
		return fdCzrlb;
	}
	public void setFdCzrlb(String fdCzrlb) {
		this.fdCzrlb = fdCzrlb;
	}
	public BigDecimal getFdRjzb() {
		return fdRjzb;
	}
	public void setFdRjzb(BigDecimal fdRjzb) {
		this.fdRjzb = fdRjzb;
	}
	public BigDecimal getFdRjzbbz() {
		return fdRjzbbz;
	}
	public void setFdRjzbbz(BigDecimal fdRjzbbz) {
		this.fdRjzbbz = fdRjzbbz;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getFdCzfs() {
		return fdCzfs;
	}

	public void setFdCzfs(String fdCzfs) {
		this.fdCzfs = fdCzfs;
	}

	public Date getFdJfqx() {
		return fdJfqx;
	}

	public void setFdJfqx(Date fdJfqx) {
		this.fdJfqx = fdJfqx;
	}
}
