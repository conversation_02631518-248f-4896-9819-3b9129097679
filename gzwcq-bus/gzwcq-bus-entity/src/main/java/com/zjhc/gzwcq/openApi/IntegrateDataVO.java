package com.zjhc.gzwcq.openApi;

import com.alibaba.fastjson.JSON;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cjffd.entity.Cjffd;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.hhqy.entity.Hhqy;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/24:14:38:20
 **/
@Api(tags = "返回表实体类", description = "返回所有实体")
public class IntegrateDataVO implements Serializable {
    @ApiParam("产权基本信息")
    private Jbxxb jbxxb;

    @ApiParam(value = "出资人列表")
    private List<Czfd> fdTableData;

    @ApiParam(value = "持股人数据")
    private List<Cgrfd> fd9TableData;

    @ApiParam("非货币资产浮动数据 ")
    private List<Fhbzcfd> fd1TableData;

    @ApiParam("查询新国资机构浮动")
    private List<Xgzjgfd> fd3TableData;

    @ApiParam("转让受让浮动")
    private List<Zrsrfd> fd8TableData;

    @ApiParam("划入划出浮动")
    private List<Hrhcfd> fd7TableData;

    @ApiParam("原股权持有方列表")
    private List<Ygqcyffd> fd2TableData;

    @ApiParam("剩余财产分配浮动数据")
    private List<Syccfpfd> fd6TableData;

    @ApiParam("查询承接方数据")
    private List<Cjffd> fd5TableData;

    @ApiParam("产权业务数据")
    private String ywzbb;

    @ApiParam("出资企业数据")
    private String hhqy;

    @ApiParam("合伙人数据")
    private List<Hhrqkfd> hhrqkfdList;

    @ApiParam("外投资情况数据")
    private List<Dwtzqkfd> dwtzqkfdList;

    public IntegrateDataVO() {
    }

    public IntegrateDataVO(Jbxxb jbxxb, List<Czfd> fdTableData, List<Cgrfd> fd9TableData, List<Fhbzcfd> fd1TableData, List<Xgzjgfd> fd3TableData, List<Zrsrfd> fd8TableData, List<Hrhcfd> fd7TableData, List<Ygqcyffd> fd2TableData, List<Syccfpfd> fd6TableData, List<Cjffd> fd5TableData, String ywzbb, String hhqy, List<Hhrqkfd> hhrqkfdList, List<Dwtzqkfd> dwtzqkfdList) {
        this.jbxxb = jbxxb;
        this.fdTableData = fdTableData;
        this.fd9TableData = fd9TableData;
        this.fd1TableData = fd1TableData;
        this.fd3TableData = fd3TableData;
        this.fd8TableData = fd8TableData;
        this.fd7TableData = fd7TableData;
        this.fd2TableData = fd2TableData;
        this.fd6TableData = fd6TableData;
        this.fd5TableData = fd5TableData;
        this.ywzbb = ywzbb;
        this.hhqy = hhqy;
        this.hhrqkfdList = hhrqkfdList;
        this.dwtzqkfdList = dwtzqkfdList;
    }

    @Override
    public String toString() {
        return "IntegrateDataVO{" +
                "jbxxb=" + jbxxb +
                ", fdTableData=" + fdTableData +
                ", fd9TableData=" + fd9TableData +
                ", fd1TableData=" + fd1TableData +
                ", fd3TableData=" + fd3TableData +
                ", fd8TableData=" + fd8TableData +
                ", fd7TableData=" + fd7TableData +
                ", fd2TableData=" + fd2TableData +
                ", fd6TableData=" + fd6TableData +
                ", fd5TableData=" + fd5TableData +
                ", ywzbb='" + ywzbb + '\'' +
                ", hhqy='" + hhqy + '\'' +
                ", hhrqkfdList=" + hhrqkfdList +
                ", dwtzqkfdList=" + dwtzqkfdList +
                '}';
    }

    public Jbxxb getJbxxb() {
        return jbxxb;
    }

    public void setJbxxb(Jbxxb jbxxb) {
        this.jbxxb = jbxxb;
    }

    public List<Czfd> getFdTableData() {
        return fdTableData;
    }

    public void setFdTableData(List<Czfd> fdTableData) {
        this.fdTableData = fdTableData;
    }

    public List<Cgrfd> getFd9TableData() {
        return fd9TableData;
    }

    public void setFd9TableData(List<Cgrfd> fd9TableData) {
        this.fd9TableData = fd9TableData;
    }

    public List<Fhbzcfd> getFd1TableData() {
        return fd1TableData;
    }

    public void setFd1TableData(List<Fhbzcfd> fd1TableData) {
        this.fd1TableData = fd1TableData;
    }

    public List<Xgzjgfd> getFd3TableData() {
        return fd3TableData;
    }

    public void setFd3TableData(List<Xgzjgfd> fd3TableData) {
        this.fd3TableData = fd3TableData;
    }

    public List<Zrsrfd> getFd8TableData() {
        return fd8TableData;
    }

    public void setFd8TableData(List<Zrsrfd> fd8TableData) {
        this.fd8TableData = fd8TableData;
    }

    public List<Hrhcfd> getFd7TableData() {
        return fd7TableData;
    }

    public void setFd7TableData(List<Hrhcfd> fd7TableData) {
        this.fd7TableData = fd7TableData;
    }

    public List<Ygqcyffd> getFd2TableData() {
        return fd2TableData;
    }

    public void setFd2TableData(List<Ygqcyffd> fd2TableData) {
        this.fd2TableData = fd2TableData;
    }

    public List<Syccfpfd> getFd6TableData() {
        return fd6TableData;
    }

    public void setFd6TableData(List<Syccfpfd> fd6TableData) {
        this.fd6TableData = fd6TableData;
    }

    public List<Cjffd> getFd5TableData() {
        return fd5TableData;
    }

    public void setFd5TableData(List<Cjffd> fd5TableData) {
        this.fd5TableData = fd5TableData;
    }

    public String getYwzbb() {
        return ywzbb;
    }

    public void setYwzbb(String ywzbb) {
        this.ywzbb = ywzbb;
    }

    public String getHhqy() {
        return hhqy;
    }

    public void setHhqy(String hhqy) {
        this.hhqy = hhqy;
    }

    public List<Hhrqkfd> getHhrqkfdList() {
        return hhrqkfdList;
    }

    public void setHhrqkfdList(List<Hhrqkfd> hhrqkfdList) {
        this.hhrqkfdList = hhrqkfdList;
    }

    public List<Dwtzqkfd> getDwtzqkfdList() {
        return dwtzqkfdList;
    }

    public void setDwtzqkfdList(List<Dwtzqkfd> dwtzqkfdList) {
        this.dwtzqkfdList = dwtzqkfdList;
    }
}
