package com.zjhc.gzwcq.monitorwarn.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_monitorwarn <br/>
 *         描述：自动预警表 <br/>
 */
public class MonitorwarnVo extends Monitorwarn {

	private static final long serialVersionUID = 18L;

	private List<MonitorwarnVo> monitorwarnList;
	private String readStatus;//读的状态 已读y 未读n
	public MonitorwarnVo() {
		super();
	}

  	public MonitorwarnVo(String id) {
  		super();
  		this.id = id;
	}

	public List<MonitorwarnVo> getMonitorwarnList() {
		return monitorwarnList;
	}

	public void setMonitorwarnList(List<MonitorwarnVo> monitorwarnList) {
		this.monitorwarnList = monitorwarnList;
	}

	private String unitName; //企业名称
	private String changeCategoryStr; //预警大类名称
	private String changeTypeStr; //变动类型名称
	private String changeStatusStr; //变动状态名称

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getChangeCategoryStr() {
		return changeCategoryStr;
	}

	public void setChangeCategoryStr(String changeCategoryStr) {
		this.changeCategoryStr = changeCategoryStr;
	}

	public String getChangeTypeStr() {
		return changeTypeStr;
	}

	public void setChangeTypeStr(String changeTypeStr) {
		this.changeTypeStr = changeTypeStr;
	}

	public String getChangeStatusStr() {
		return changeStatusStr;
	}

	public void setChangeStatusStr(String changeStatusStr) {
		this.changeStatusStr = changeStatusStr;
	}

	public String getReadStatus() {
		return readStatus;
	}

	public void setReadStatus(String readStatus) {
		this.readStatus = readStatus;
	}
}
