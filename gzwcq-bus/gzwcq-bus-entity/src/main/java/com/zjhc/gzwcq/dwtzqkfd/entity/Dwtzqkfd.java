package com.zjhc.gzwcq.dwtzqkfd.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_dwtzqkfd <br/>
 *         描述：对外投资情况浮动 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Dwtzqkfd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="序号")
	protected Integer floatorder;// 序号
  	@ApiParam(value="标的类型")
	protected String bdlx;// 标的类型
  	@ApiParam(value="标的名称")
	protected String bdmc;// 标的名称
  	@ApiParam(value="统一信用代码")
	protected String code;// 统一信用代码
  	@ApiParam(value="所属行业")
	protected String sshy;// 所属行业
  	@ApiParam(value="注册地或所在地")
	protected String address;// 注册地或所在地
  	@ApiParam(value="投资额（万元）")
	protected BigDecimal tze;// 投资额（万元）
  	@ApiParam(value="投资比例%")
	protected BigDecimal tzbl;// 投资比例%
  	@ApiParam(value="是否实际控制")
	protected String sfsjkz;// 是否实际控制
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Dwtzqkfd() {
		super();
	}
	
  	public Dwtzqkfd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public Integer getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(Integer floatorder) {
		this.floatorder = floatorder;
	}
	public String getBdlx() {
		return bdlx;
	}
	public void setBdlx(String bdlx) {
		this.bdlx = bdlx;
	}
	public String getBdmc() {
		return bdmc;
	}
	public void setBdmc(String bdmc) {
		this.bdmc = bdmc;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getSshy() {
		return sshy;
	}
	public void setSshy(String sshy) {
		this.sshy = sshy;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public BigDecimal getTze() {
		return tze;
	}
	public void setTze(BigDecimal tze) {
		this.tze = tze;
	}
	public BigDecimal getTzbl() {
		return tzbl;
	}
	public void setTzbl(BigDecimal tzbl) {
		this.tzbl = tzbl;
	}
	public String getSfsjkz() {
		return sfsjkz;
	}
	public void setSfsjkz(String sfsjkz) {
		this.sfsjkz = sfsjkz;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
