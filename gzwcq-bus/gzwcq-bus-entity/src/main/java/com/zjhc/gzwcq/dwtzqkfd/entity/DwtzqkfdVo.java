package com.zjhc.gzwcq.dwtzqkfd.entity;


import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_dwtzqkfd <br/>
 *         描述：对外投资情况浮动 <br/>
 */
public class DwtzqkfdVo extends Dwtzqkfd {

	private static final long serialVersionUID = 18L;

	private List<DwtzqkfdVo> dwtzqkfdList;
	private String bdlxStr;//标的类型
	private String sfsjkzStr;//是否实际控制

	private List<String> sshyList;// 所属行业集合
	private String sshyStr;// 所属行业
	private String addressStr;// 注册地或所在地

	public DwtzqkfdVo() {
		super();
	}

  	public DwtzqkfdVo(String id) {
  		super();
  		this.id = id;
	}

	public List<DwtzqkfdVo> getDwtzqkfdList() {
		return dwtzqkfdList;
	}

	public void setDwtzqkfdList(List<DwtzqkfdVo> dwtzqkfdList) {
		this.dwtzqkfdList = dwtzqkfdList;
	}

	public String getBdlxStr() {
		return bdlxStr;
	}

	public void setBdlxStr(String bdlxStr) {
		this.bdlxStr = bdlxStr;
	}

	public String getSfsjkzStr() {
		return sfsjkzStr;
	}

	public void setSfsjkzStr(String sfsjkzStr) {
		this.sfsjkzStr = sfsjkzStr;
	}

	public List<String> getSshyList() {
		return sshyList;
	}

	public void setSshyList(List<String> sshyList) {
		this.sshyList = sshyList;
	}

	public String getSshyStr() {
		if (sshyList != null && !sshyList.isEmpty()){
			StringBuilder builder = new StringBuilder();
			sshyList.forEach(h -> {
				builder.append(h).append(",");
			});
			return builder.substring(0,builder.length()-1);
		}
		return "";
	}

	public void setSshyStr(String sshyStr) {
		if (sshyList != null && !sshyList.isEmpty()){
			StringBuilder builder = new StringBuilder();
			sshyList.forEach(h -> {
				builder.append(h).append(",");
			});
			this.sshyStr = builder.substring(0,builder.length()-1);
		}else {
			this.sshyStr = "";
		}
	}

	public String getAddressStr() {
		return addressStr;
	}

	public void setAddressStr(String addressStr) {
		this.addressStr = addressStr;
	}
}
