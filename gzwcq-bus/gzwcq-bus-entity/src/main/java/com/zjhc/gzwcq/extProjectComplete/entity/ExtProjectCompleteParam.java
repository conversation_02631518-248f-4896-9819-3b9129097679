package com.zjhc.gzwcq.extProjectComplete.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR> <br/>
 * 表名： cq_ext_project_complete <br/>
 * 描述：ExtProjectComplete查询类 <br/>
 */
@ApiModel(value = "ExtProjectComplete对象", description = "extProjectComplete")
public class ExtProjectCompleteParam extends ExtProjectComplete {

    private static final long serialVersionUID = 18L;

    @ApiParam(value = "查询页（和偏移量二选一）")
    private int pageNumber;

    @ApiParam(value = "每页数量")
    private int limit;

    @ApiParam(value = "当前偏移量（和查询页二选一）")
    private int offest;
    @ApiParam(value = "组织名称")
    private String organizationName;
    @ApiParam(value = "开始时间 yyyyMMdd")
    private String startTime;
    @ApiParam(value = "结束时间 yyyyMMdd")
    private String endTime;
    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffest() {
        return offest;
    }

    public void setOffest(int offest) {
        this.offest = offest;
    }
}
