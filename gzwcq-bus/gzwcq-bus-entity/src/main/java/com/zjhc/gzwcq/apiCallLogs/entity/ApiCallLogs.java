package com.zjhc.gzwcq.apiCallLogs.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： tbl_api_call_logs <br/>
 *         描述：tbl_api_call_logs <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiCallLogs implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键，自动递增")
	protected Long id;// 主键，自动递增
  	@ApiParam(value="类型 1-调用日志 2-被调用日志")
	protected Integer type;// 类型 1-调用日志 2-被调用日志
  	@ApiParam(value="客户端ID")
	protected String appId;// 客户端ID
  	@ApiParam(value="API名称或标识符")
	protected String apiName;// API名称或标识符
  	@ApiParam(value="HTTP请求方法（GET, POST等）")
	protected String requestMethod;// HTTP请求方法（GET, POST等）
  	@ApiParam(value="完整请求URL")
	protected String requestUrl;// 完整请求URL
  	@ApiParam(value="请求头信息（可选，视需求而定）")
	protected String requestHeaders;// 请求头信息（可选，视需求而定）
  	@ApiParam(value="入参")
	protected String request;// 入参
  	@ApiParam(value="响应状态码（如200, 404, 500等）")
	protected Integer responseStatusCode;// 响应状态码（如200, 404, 500等）
  	@ApiParam(value="出参")
	protected String response;// 出参
  	@ApiParam(value="执行时间，单位为毫秒")
	protected Long executionTimeMs;// 执行时间，单位为毫秒
  	@ApiParam(value="客户端IP地址")
	protected String clientIp;// 客户端IP地址
  	@ApiParam(value="请求结果 1-成功 2-失败")
	protected Integer status;// 请求结果 1-成功 2-失败
  	@ApiParam(value="错误信息")
	protected String errMsg;// 错误信息
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="最后更新时间")
	protected Date lastUpdateTime;// 最后更新时间

	public ApiCallLogs() {
		super();
	}
	
  	public ApiCallLogs(Long id) {
  		super();
  		this.id = id;
	}

	/***
	 * *
	 * @param type
	 * @param appId
	 * @param apiName
	 * @param requestMethod
	 * @param requestUrl
	 * @param requestHeaders
	 * @param request
	 * @param responseStatusCode
	 * @param response
	 * @param executionTimeMs
	 * @param clientIp
	 * @param status
	 * @param errMsg
	 */
	public ApiCallLogs(Integer type, String appId, String apiName, String requestMethod, String requestUrl, String requestHeaders, String request, Integer responseStatusCode, String response, Long executionTimeMs, String clientIp, Integer status, String errMsg) {
		this.type = type;
		this.appId = appId;
		this.apiName = apiName;
		this.requestMethod = requestMethod;
		this.requestUrl = requestUrl;
		this.requestHeaders = requestHeaders;
		this.request = request;
		this.responseStatusCode = responseStatusCode;
		this.response = response;
		this.executionTimeMs = executionTimeMs;
		this.clientIp = clientIp;
		this.status = status;
		this.errMsg = errMsg;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getApiName() {
		return apiName;
	}
	public void setApiName(String apiName) {
		this.apiName = apiName;
	}
	public String getRequestMethod() {
		return requestMethod;
	}
	public void setRequestMethod(String requestMethod) {
		this.requestMethod = requestMethod;
	}
	public String getRequestUrl() {
		return requestUrl;
	}
	public void setRequestUrl(String requestUrl) {
		this.requestUrl = requestUrl;
	}
	public String getRequestHeaders() {
		return requestHeaders;
	}
	public void setRequestHeaders(String requestHeaders) {
		this.requestHeaders = requestHeaders;
	}
	public String getRequest() {
		return request;
	}
	public void setRequest(String request) {
		this.request = request;
	}
	public Integer getResponseStatusCode() {
		return responseStatusCode;
	}
	public void setResponseStatusCode(Integer responseStatusCode) {
		this.responseStatusCode = responseStatusCode;
	}
	public String getResponse() {
		return response;
	}
	public void setResponse(String response) {
		this.response = response;
	}
	public Long getExecutionTimeMs() {
		return executionTimeMs;
	}
	public void setExecutionTimeMs(Long executionTimeMs) {
		this.executionTimeMs = executionTimeMs;
	}
	public String getClientIp() {
		return clientIp;
	}
	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getErrMsg() {
		return errMsg;
	}
	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
