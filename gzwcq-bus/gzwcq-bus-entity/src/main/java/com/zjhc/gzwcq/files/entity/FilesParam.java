package com.zjhc.gzwcq.files.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> <br/>
 *         表名： cq_files <br/>
 *         描述：Files查询类 <br/>
 */
@ApiModel(value="Files对象",description="files")
public class FilesParam extends Files{

	private static final long serialVersionUID = 18L;
  	
  	@ApiParam(value="查询页（和偏移量二选一）")
	private int pageNumber;
	
  	@ApiParam(value="每页数量")
	private int limit;
	
  	@ApiParam(value="当前偏移量（和查询页二选一）")
	private int offest;

	@ApiParam(value="开始日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	private Date inTimeStart;

	@ApiParam(value="结束日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	private Date inTimeEnd;

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public int getOffest() {
		return offest;
	}

	public void setOffest(int offest) {
		this.offest = offest;
	}
	public Date getInTimeStart() {
		return inTimeStart;
	}

	public void setInTimeStart(Date inTimeStart) {
		this.inTimeStart = inTimeStart;
	}

	public Date getInTimeEnd() {
		return inTimeEnd;
	}

	public void setInTimeEnd(Date inTimeEnd) {
		this.inTimeEnd = inTimeEnd;
	}
}
