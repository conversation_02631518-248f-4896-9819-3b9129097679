package com.zjhc.gzwcq.placard.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_placard <br/>
 *         描述：cq_placard <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Placard implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="标题")
	protected String title;// 标题
  	@ApiParam(value="描述")
	protected String descript;// 描述
  	@ApiParam(value="内容")
	protected String content;// 内容
  	@ApiParam(value="已读")
	protected String haveRead;// 已读人集合
  	@ApiParam(value="浏览次数")
	protected Integer lookTime;// 浏览次数
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Placard() {
		super();
	}
	
  	public Placard(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getDescript() {
		return descript;
	}
	public void setDescript(String descript) {
		this.descript = descript;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public Integer getLookTime() {
		return lookTime;
	}
	public void setLookTime(Integer lookTime) {
		this.lookTime = lookTime;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getHaveRead() {
		return haveRead;
	}

	public void setHaveRead(String haveRead) {
		this.haveRead = haveRead;
	}
}
