package com.zjhc.gzwcq.newHome.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/05:15:31:24
 **/
@ApiModel("国资委新首页数据统计返回")
@Data
public class BusinessAssessmentInfoVO {
    @ApiModelProperty("审核企业数")
    private String enterpriseCount;
    @ApiModelProperty("企业户数")
    private String companyCount;
    @ApiModelProperty("提交事项数")
    private String submitNum;
    @ApiModelProperty("审核事项数")
    private String auditNum;
    @ApiModelProperty("通过数")
    private String passNum;
    @ApiModelProperty("通过率")
    private String throughRate;
    @ApiModelProperty("退回数")
    private String rejectionNum;
    @ApiModelProperty("退回率")
    private String rejectionRate;
}
