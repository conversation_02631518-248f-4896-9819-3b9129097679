package com.zjhc.gzwcq.hrhcfd.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_hrhcfd <br/>
 *         描述：划入划出浮动 <br/>
 */
public class HrhcfdVo extends Hrhcfd {

	private static final long serialVersionUID = 18L;

	private List<HrhcfdVo> hrhcfdList;

	private String fd7WchzlbStr;// 无偿划转类别
	private String fd7HcfssgzjgjgStr;// 划出方所属国资监管机构
	private String fd7HrfssgzjgjgStr;// 划入方所属国资监管机构
	private String fd7HrfssgjczqyStr;// 划入方国家出资企业
	private String fd7HcfssgjczqyStr;// 划出方国家出资企业
	public HrhcfdVo() {
		super();
	}

  	public HrhcfdVo(String id) {
  		super();
  		this.id = id;
	}

	public String getFd7WchzlbStr() {
		return fd7WchzlbStr;
	}

	public void setFd7WchzlbStr(String fd7WchzlbStr) {
		this.fd7WchzlbStr = fd7WchzlbStr;
	}

	public String getFd7HcfssgzjgjgStr() {
		return fd7HcfssgzjgjgStr;
	}

	public void setFd7HcfssgzjgjgStr(String fd7HcfssgzjgjgStr) {
		this.fd7HcfssgzjgjgStr = fd7HcfssgzjgjgStr;
	}

	public String getFd7HrfssgzjgjgStr() {
		return fd7HrfssgzjgjgStr;
	}

	public void setFd7HrfssgzjgjgStr(String fd7HrfssgzjgjgStr) {
		this.fd7HrfssgzjgjgStr = fd7HrfssgzjgjgStr;
	}

	public List<HrhcfdVo> getHrhcfdList() {
		return hrhcfdList;
	}

	public void setHrhcfdList(List<HrhcfdVo> hrhcfdList) {
		this.hrhcfdList = hrhcfdList;
	}

	public String getFd7HrfssgjczqyStr() {
		return fd7HrfssgjczqyStr;
	}

	public void setFd7HrfssgjczqyStr(String fd7HrfssgjczqyStr) {
		this.fd7HrfssgjczqyStr = fd7HrfssgjczqyStr;
	}

	public String getFd7HcfssgjczqyStr() {
		return fd7HcfssgjczqyStr;
	}

	public void setFd7HcfssgjczqyStr(String fd7HcfssgjczqyStr) {
		this.fd7HcfssgjczqyStr = fd7HcfssgjczqyStr;
	}
}
