package com.zjhc.gzwcq.auditflowHistory.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： rg_auditflow_history <br/>
 *         描述：审核历史记录 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditflowHistory implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联流程实例ID")
	protected String businessInfoId;// 关联流程实例ID
  	@ApiParam(value="单位ID 同md_org表的主键")
	protected String afUnitid;// 单位ID 同md_org表的主键
  	@ApiParam(value="版本，同“CQ_打头”的其他表的datatime")
	protected String afDatatime;// 版本，同“CQ_打头”的其他表的datatime
  	@ApiParam(value="审核状态 ")
	protected String afProcesstype;// 审核状态
  	@ApiParam(value="审核人")
	protected String afProcessuserid;// 审核人
  	@ApiParam(value="审核人机构 md_org表的主键")
	protected String afProcessunitid;// 审核人机构 md_org表的主键
  	@ApiParam(value="处理时间，分别为审核时间或者上报时间")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	protected Date afProcessdate;// 处理时间，分别为审核时间或者上报时间
  	@ApiParam(value="审批意见")
	protected String afProcesscomment;// 审批意见
  	@ApiParam(value="账号登录名")
	protected String afProcessusername;// 账号登录名
  	@ApiParam(value="账号名称")
	protected String afProcessusertitle;// 账号名称
  	@ApiParam(value="操作机构")
	protected String afProcessunitcode;// 操作机构
  	@ApiParam(value="操作机构名称")
	protected String afProcessunittitle;// 操作机构名称
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间
	@ApiParam(value="审核分组（审核人/初审/复审）")
	protected String afProcessgroup; //审核分组（审核人/初审/复审）

	public AuditflowHistory() {
		super();
	}
	
  	public AuditflowHistory(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getBusinessInfoId() {
		return businessInfoId;
	}
	public void setBusinessInfoId(String businessInfoId) {
		this.businessInfoId = businessInfoId;
	}
	public String getAfUnitid() {
		return afUnitid;
	}
	public void setAfUnitid(String afUnitid) {
		this.afUnitid = afUnitid;
	}
	public String getAfDatatime() {
		return afDatatime;
	}
	public void setAfDatatime(String afDatatime) {
		this.afDatatime = afDatatime;
	}
	public String getAfProcesstype() {
		return afProcesstype;
	}
	public void setAfProcesstype(String afProcesstype) {
		this.afProcesstype = afProcesstype;
	}
	public String getAfProcessuserid() {
		return afProcessuserid;
	}
	public void setAfProcessuserid(String afProcessuserid) {
		this.afProcessuserid = afProcessuserid;
	}
	public String getAfProcessunitid() {
		return afProcessunitid;
	}
	public void setAfProcessunitid(String afProcessunitid) {
		this.afProcessunitid = afProcessunitid;
	}
	public Date getAfProcessdate() {
		return afProcessdate;
	}
	public void setAfProcessdate(Date afProcessdate) {
		this.afProcessdate = afProcessdate;
	}
	public String getAfProcesscomment() {
		return afProcesscomment;
	}
	public void setAfProcesscomment(String afProcesscomment) {
		this.afProcesscomment = afProcesscomment;
	}
	public String getAfProcessusername() {
		return afProcessusername;
	}
	public void setAfProcessusername(String afProcessusername) {
		this.afProcessusername = afProcessusername;
	}
	public String getAfProcessusertitle() {
		return afProcessusertitle;
	}
	public void setAfProcessusertitle(String afProcessusertitle) {
		this.afProcessusertitle = afProcessusertitle;
	}
	public String getAfProcessunitcode() {
		return afProcessunitcode;
	}
	public void setAfProcessunitcode(String afProcessunitcode) {
		this.afProcessunitcode = afProcessunitcode;
	}
	public String getAfProcessunittitle() {
		return afProcessunittitle;
	}
	public void setAfProcessunittitle(String afProcessunittitle) {
		this.afProcessunittitle = afProcessunittitle;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getAfProcessgroup() {
		return afProcessgroup;
	}

	public void setAfProcessgroup(String afProcessgroup) {
		this.afProcessgroup = afProcessgroup;
	}
}
