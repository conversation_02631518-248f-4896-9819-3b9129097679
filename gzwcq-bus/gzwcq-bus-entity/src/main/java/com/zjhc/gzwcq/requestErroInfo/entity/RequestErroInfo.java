package com.zjhc.gzwcq.requestErroInfo.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR> <br/>
 *         表名： api_request_erro_info <br/>
 *         描述：api_request_erro_info <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestErroInfo implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="id")
	protected Long id;// id
  	@ApiParam(value="对应请求的id")
	protected Long apiRequestInfoId;// 对应请求的id
  	@ApiParam(value="企业id")
	protected String orgId;// 企业id
  	@ApiParam(value="企业名称")
	protected String qymc;// 企业名称
  	@ApiParam(value="统一信用编码")
	protected String xybm;// 统一信用编码
  	@ApiParam(value="错误消息")
	protected String errorMessage;// 错误消息
  	@ApiParam(value="错误编号")
	protected String errorIndex;// 错误编号
  	@ApiParam(value="错误类型")
	protected String errorType;// 错误类型
  	@ApiParam(value="错误级别1：严重错误 2：重要错误 3：一般错误")
	protected String errorLevel;// 错误级别1：严重错误2：重要错误3：一般错误
  	@ApiParam(value="数据处理模式 0：保 1：排除")
	protected String processMode;// 数据处理模式 0：保1：排除

	public RequestErroInfo() {
		super();
	}
	
  	public RequestErroInfo(Long id) {
  		super();
  		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getApiRequestInfoId() {
		return apiRequestInfoId;
	}
	public void setApiRequestInfoId(Long apiRequestInfoId) {
		this.apiRequestInfoId = apiRequestInfoId;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getQymc() {
		return qymc;
	}
	public void setQymc(String qymc) {
		this.qymc = qymc;
	}
	public String getXybm() {
		return xybm;
	}
	public void setXybm(String xybm) {
		this.xybm = xybm;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	public String getErrorIndex() {
		return errorIndex;
	}
	public void setErrorIndex(String errorIndex) {
		this.errorIndex = errorIndex;
	}
	public String getErrorType() {
		return errorType;
	}
	public void setErrorType(String errorType) {
		this.errorType = errorType;
	}
	public String getErrorLevel() {
		return errorLevel;
	}
	public void setErrorLevel(String errorLevel) {
		this.errorLevel = errorLevel;
	}
	public String getProcessMode() {
		return processMode;
	}
	public void setProcessMode(String processMode) {
		this.processMode = processMode;
	}
}
