package com.zjhc.gzwcq.openApi;

import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import io.swagger.annotations.Api;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/29:10:28:48
 **/
@Api(tags = "返回表实体类", description = "返回所有基本表单+出资人+资本状态")
public class JbxxbCzrInfoVO {
    private JbxxbVo jbxxb;
    private List<CzfdVo> czTableData;
    private List<HhrqkfdVo> hhrqkfdList;

    public JbxxbCzrInfoVO(JbxxbVo jbxxb, List<CzfdVo> czTableData, List<HhrqkfdVo> hhrqkfdList) {
        this.jbxxb = jbxxb;
        this.czTableData = czTableData;
        this.hhrqkfdList = hhrqkfdList;
    }

    public JbxxbCzrInfoVO() {
    }

    public JbxxbVo getJbxxb() {
        return jbxxb;
    }

    public void setJbxxb(JbxxbVo jbxxb) {
        this.jbxxb = jbxxb;
    }

    public List<CzfdVo> getCzTableData() {
        return czTableData;
    }

    public void setCzTableData(List<CzfdVo> czTableData) {
        this.czTableData = czTableData;
    }

    public List<HhrqkfdVo> getHhrqkfdList() {
        return hhrqkfdList;
    }

    public void setHhrqkfdList(List<HhrqkfdVo> hhrqkfdList) {
        this.hhrqkfdList = hhrqkfdList;
    }
}
