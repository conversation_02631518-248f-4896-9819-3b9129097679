package com.zjhc.gzwcq.fhbzcfd.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_fhbzcfd <br/>
 *         描述：非货币资产浮动 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Fhbzcfd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期")
	protected String datatime;// 数据时期
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="出资方")
	protected String fd1Czf;// 出资方
  	@ApiParam(value="出资作价（万元）")
	protected BigDecimal fd1Czzj;// 出资作价（万元）
  	@ApiParam(value="评估值（万元）")
	protected BigDecimal fd1Pgz;// 评估值（万元）
  	@ApiParam(value="支付方")
	protected String fd1Zff;// 支付方
  	@ApiParam(value="支付作价（万元）")
	protected BigDecimal fd1Zfzj;// 支付作价（万元）
  	@ApiParam(value="减资方")
	protected String fd1Jzf;// 减资方
  	@ApiParam(value="减资作价(万元)")
	protected BigDecimal fd1Jzzj;// 减资作价(万元)
  	@ApiParam(value="备注")
	protected String fd1Bz;// 备注
  	@ApiParam(value="非货币资产类别")
	protected String fd1Hfbzclb;// 非货币资产类别
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Fhbzcfd() {
		super();
	}
	
  	public Fhbzcfd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public String getFd1Czf() {
		return fd1Czf;
	}
	public void setFd1Czf(String fd1Czf) {
		this.fd1Czf = fd1Czf;
	}
	public BigDecimal getFd1Czzj() {
		return fd1Czzj;
	}
	public void setFd1Czzj(BigDecimal fd1Czzj) {
		this.fd1Czzj = fd1Czzj;
	}
	public BigDecimal getFd1Pgz() {
		return fd1Pgz;
	}
	public void setFd1Pgz(BigDecimal fd1Pgz) {
		this.fd1Pgz = fd1Pgz;
	}
	public String getFd1Zff() {
		return fd1Zff;
	}
	public void setFd1Zff(String fd1Zff) {
		this.fd1Zff = fd1Zff;
	}
	public BigDecimal getFd1Zfzj() {
		return fd1Zfzj;
	}
	public void setFd1Zfzj(BigDecimal fd1Zfzj) {
		this.fd1Zfzj = fd1Zfzj;
	}
	public String getFd1Jzf() {
		return fd1Jzf;
	}
	public void setFd1Jzf(String fd1Jzf) {
		this.fd1Jzf = fd1Jzf;
	}
	public BigDecimal getFd1Jzzj() {
		return fd1Jzzj;
	}
	public void setFd1Jzzj(BigDecimal fd1Jzzj) {
		this.fd1Jzzj = fd1Jzzj;
	}
	public String getFd1Bz() {
		return fd1Bz;
	}
	public void setFd1Bz(String fd1Bz) {
		this.fd1Bz = fd1Bz;
	}
	public String getFd1Hfbzclb() {
		return fd1Hfbzclb;
	}
	public void setFd1Hfbzclb(String fd1Hfbzclb) {
		this.fd1Hfbzclb = fd1Hfbzclb;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
