package com.zjhc.gzwcq.hhrqkfd.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_hhrqkfd <br/>
 *         描述：合伙人情况浮动 <br/>
 */
public class HhrqkfdVo extends Hhrqkfd {

	private static final long serialVersionUID = 18L;

	private List<HhrqkfdVo> hhrqkfdList;
	private String typeStr;//合伙人类型
	private String categoryStr;//合伙人类别
	private String czfsStr;//出资方式

	public HhrqkfdVo() {
		super();
	}

  	public HhrqkfdVo(String id) {
  		super();
  		this.id = id;
	}

	public List<HhrqkfdVo> getHhrqkfdList() {
		return hhrqkfdList;
	}

	public void setHhrqkfdList(List<HhrqkfdVo> hhrqkfdList) {
		this.hhrqkfdList = hhrqkfdList;
	}

	public String getTypeStr() {
		return typeStr;
	}

	public void setTypeStr(String typeStr) {
		this.typeStr = typeStr;
	}

	public String getCategoryStr() {
		return categoryStr;
	}

	public void setCategoryStr(String categoryStr) {
		this.categoryStr = categoryStr;
	}

	public String getCzfsStr() {
		return czfsStr;
	}

	public void setCzfsStr(String czfsStr) {
		this.czfsStr = czfsStr;
	}
}
