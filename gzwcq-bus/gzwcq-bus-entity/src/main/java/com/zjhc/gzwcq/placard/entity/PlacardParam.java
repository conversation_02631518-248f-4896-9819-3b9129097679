package com.zjhc.gzwcq.placard.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> <br/>
 * 表名： cq_placard <br/>
 * 描述：Placard查询类 <br/>
 */
@ApiModel(value = "Placard对象", description = "placard")
public class PlacardParam extends Placard {

    private static final long serialVersionUID = 18L;

    @ApiParam(value = "查询页（和偏移量二选一）")
    private int pageNumber;

    @ApiParam(value = "每页数量")
    private int limit;

    @ApiParam(value = "当前偏移量（和查询页二选一）")
    private int offest;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginDate;//开始日期

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;//结束日期

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffest() {
        return offest;
    }

    public void setOffest(int offest) {
        this.offest = offest;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
