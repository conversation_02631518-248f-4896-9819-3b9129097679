package com.zjhc.gzwcq.zrsrfd.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_zrsrfd <br/>
 *         描述：转让受让浮动 <br/>
 */
public class ZrsrfdVo extends Zrsrfd {

	private static final long serialVersionUID = 18L;

	private List<ZrsrfdVo> zrsrfdList;

	private String fd8SrfxzStr;//受让方性质

    private String fd8ZrlbStr;//转让类别

	private String fd8ZrfssgzjgjgStr;//转让方所属国资监管机构

	private String fd8SrfssgzjgjgStr;//受让方所属国资监管机构

	public ZrsrfdVo() {
		super();
	}

  	public ZrsrfdVo(String id) {
  		super();
  		this.id = id;
	}

	public List<ZrsrfdVo> getZrsrfdList() {
		return zrsrfdList;
	}

	public void setZrsrfdList(List<ZrsrfdVo> zrsrfdList) {
		this.zrsrfdList = zrsrfdList;
	}

	public String getFd8SrfxzStr() {
		return fd8SrfxzStr;
	}

	public void setFd8SrfxzStr(String fd8SrfxzStr) {
		this.fd8SrfxzStr = fd8SrfxzStr;
	}

    public String getFd8ZrlbStr() {
        return fd8ZrlbStr;
    }

    public void setFd8ZrlbStr(String fd8ZrlbStr) {
        this.fd8ZrlbStr = fd8ZrlbStr;
    }

	public String getFd8ZrfssgzjgjgStr() {
		return fd8ZrfssgzjgjgStr;
	}

	public void setFd8ZrfssgzjgjgStr(String fd8ZrfssgzjgjgStr) {
		this.fd8ZrfssgzjgjgStr = fd8ZrfssgzjgjgStr;
	}

	public String getFd8SrfssgzjgjgStr() {
		return fd8SrfssgzjgjgStr;
	}

	public void setFd8SrfssgzjgjgStr(String fd8SrfssgzjgjgStr) {
		this.fd8SrfssgzjgjgStr = fd8SrfssgzjgjgStr;
	}
}
