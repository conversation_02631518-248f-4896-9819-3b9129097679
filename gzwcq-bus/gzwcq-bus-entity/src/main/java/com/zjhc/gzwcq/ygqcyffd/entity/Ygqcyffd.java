package com.zjhc.gzwcq.ygqcyffd.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ygqcyffd <br/>
 *         描述：原股权持有方浮动 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Ygqcyffd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期")
	protected String datatime;// 数据时期
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="原股权持有方名称")
	protected String fd2Ygqcyfmc;// 原股权持有方名称
  	@ApiParam(value="折股标准（元/股）")
	protected BigDecimal fd2Zgbz;// 折股标准（元/股）
  	@ApiParam(value="收购价格(万元)")
	protected BigDecimal fd2Sgjg;// 收购价格(万元)
  	@ApiParam(value="收购股权的审计净资产值（万元）")
	protected BigDecimal fd2Sjjzcz;// 收购股权的审计净资产值（万元）
  	@ApiParam(value="收购股权的评估净资产值（万元）")
	protected BigDecimal fd2Pgjzcz;// 收购股权的评估净资产值（万元）
  	@ApiParam(value="收购方式")
	protected String fd2Sgfs;// 收购方式
  	@ApiParam(value="作价依据")
	protected String fd2Zjyj;// 作价依据
  	@ApiParam(value="备注")
	protected String fd2Bz;// 备注
  	@ApiParam(value="原股权持有方性质")
	protected String fd2Ygqcyfxz;// 原股权持有方性质
  	@ApiParam(value="收购股权比例")
	protected BigDecimal fd2Sggqbl;// 收购股权比例
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Ygqcyffd() {
		super();
	}
	
  	public Ygqcyffd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public String getFd2Ygqcyfmc() {
		return fd2Ygqcyfmc;
	}
	public void setFd2Ygqcyfmc(String fd2Ygqcyfmc) {
		this.fd2Ygqcyfmc = fd2Ygqcyfmc;
	}
	public BigDecimal getFd2Zgbz() {
		return fd2Zgbz;
	}
	public void setFd2Zgbz(BigDecimal fd2Zgbz) {
		this.fd2Zgbz = fd2Zgbz;
	}
	public BigDecimal getFd2Sgjg() {
		return fd2Sgjg;
	}
	public void setFd2Sgjg(BigDecimal fd2Sgjg) {
		this.fd2Sgjg = fd2Sgjg;
	}
	public BigDecimal getFd2Sjjzcz() {
		return fd2Sjjzcz;
	}
	public void setFd2Sjjzcz(BigDecimal fd2Sjjzcz) {
		this.fd2Sjjzcz = fd2Sjjzcz;
	}
	public BigDecimal getFd2Pgjzcz() {
		return fd2Pgjzcz;
	}
	public void setFd2Pgjzcz(BigDecimal fd2Pgjzcz) {
		this.fd2Pgjzcz = fd2Pgjzcz;
	}
	public String getFd2Sgfs() {
		return fd2Sgfs;
	}
	public void setFd2Sgfs(String fd2Sgfs) {
		this.fd2Sgfs = fd2Sgfs;
	}
	public String getFd2Zjyj() {
		return fd2Zjyj;
	}
	public void setFd2Zjyj(String fd2Zjyj) {
		this.fd2Zjyj = fd2Zjyj;
	}
	public String getFd2Bz() {
		return fd2Bz;
	}
	public void setFd2Bz(String fd2Bz) {
		this.fd2Bz = fd2Bz;
	}
	public String getFd2Ygqcyfxz() {
		return fd2Ygqcyfxz;
	}
	public void setFd2Ygqcyfxz(String fd2Ygqcyfxz) {
		this.fd2Ygqcyfxz = fd2Ygqcyfxz;
	}
	public BigDecimal getFd2Sggqbl() {
		return fd2Sggqbl;
	}
	public void setFd2Sggqbl(BigDecimal fd2Sggqbl) {
		this.fd2Sggqbl = fd2Sggqbl;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
