package com.zjhc.gzwcq.businessInfo.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： rg_business_info <br/>
 *         描述：登记状态表/流程实例表 <br/>
 */
@ExcelIgnoreUnannotated
public class BusinessInfoVo extends BusinessInfo {

	private static final long serialVersionUID = 18L;

	private List<BusinessInfoVo> businessInfoList;

	public BusinessInfoVo() {
		super();
	}

  	public BusinessInfoVo(String id) {
  		super();
  		this.id = id;
	}

	public List<BusinessInfoVo> getBusinessInfoList() {
		return businessInfoList;
	}

	public void setBusinessInfoList(List<BusinessInfoVo> businessInfoList) {
		this.businessInfoList = businessInfoList;
	}

	@ExcelProperty(value = "企业代码",index = 1)
	private String orgCode; //组织编码
	@ExcelProperty(value = "企业名称",index = 0)
	private String orgName; //组织名称
	@ExcelProperty(value = "登记类型",index = 2)
	private String rgTypeName; //登记类型名称
	@ExcelProperty(value = "业务状态",index = 3)
	private String busStatus; //业务状态
	private String jbGJCZQY;
	private String jbGjczqyStr;
	private String jbCqdjqxStr; //产权登记情形

	private String jbZycqdjqx; //占有产权登记情形

	private String jbBdcqdjqx; //变动产权登记情形

	private String jbZxcqdjqx; //注销产权登记情形

	private String rgTimemarkStr; //时间快照 yyyy-MM-dd

	private boolean canDelete; //能否删除

	private String jbxxCreateUser;

	private String approvalUnitIds; //审批轨迹中所有参与的企业id,逗号隔开

	public String getJbGJCZQY() {
		return jbGJCZQY;
	}

	public void setJbGJCZQY(String jbGJCZQY) {
		this.jbGJCZQY = jbGJCZQY;
	}

	public String getJbGjczqyStr() {
		return jbGjczqyStr;
	}

	public void setJbGjczqyStr(String jbGjczqyStr) {
		this.jbGjczqyStr = jbGjczqyStr;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getRgTypeName() {
		return rgTypeName;
	}

	public void setRgTypeName(String rgTypeName) {
		this.rgTypeName = rgTypeName;
	}

	public String getBusStatus() {
		return busStatus;
	}

	public void setBusStatus(String busStatus) {
		this.busStatus = busStatus;
	}

	public String getJbCqdjqxStr() {
		return jbCqdjqxStr;
	}

	public void setJbCqdjqxStr(String jbCqdjqxStr) {
		this.jbCqdjqxStr = jbCqdjqxStr;
	}

	public String getJbZycqdjqx() {
		return jbZycqdjqx;
	}

	public void setJbZycqdjqx(String jbZycqdjqx) {
		this.jbZycqdjqx = jbZycqdjqx;
	}

	public String getJbBdcqdjqx() {
		return jbBdcqdjqx;
	}

	public void setJbBdcqdjqx(String jbBdcqdjqx) {
		this.jbBdcqdjqx = jbBdcqdjqx;
	}

	public String getJbZxcqdjqx() {
		return jbZxcqdjqx;
	}

	public void setJbZxcqdjqx(String jbZxcqdjqx) {
		this.jbZxcqdjqx = jbZxcqdjqx;
	}

	public String getRgTimemarkStr() {
		return rgTimemarkStr;
	}

	public void setRgTimemarkStr(String rgTimemarkStr) {
		this.rgTimemarkStr = rgTimemarkStr;
	}

	public boolean isCanDelete() {
		return canDelete;
	}

	public void setCanDelete(boolean canDelete) {
		this.canDelete = canDelete;
	}

	public String getJbxxCreateUser() {
		return jbxxCreateUser;
	}

	public void setJbxxCreateUser(String jbxxCreateUser) {
		this.jbxxCreateUser = jbxxCreateUser;
	}

	public String getApprovalUnitIds() {
		return approvalUnitIds;
	}

	public void setApprovalUnitIds(String approvalUnitIds) {
		this.approvalUnitIds = approvalUnitIds;
	}
}
