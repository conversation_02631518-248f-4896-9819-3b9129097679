package com.zjhc.gzwcq.requestResultInfo.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： api_request_result_info <br/>
 *         描述：api_request_result_info <br/>
 */
public class RequestResultInfoVo extends RequestResultInfo {

	private static final long serialVersionUID = 18L;

	private List<RequestResultInfoVo> requestResultInfoList;
	private String errorType;
	private String qymc;
	private String xybm;
	private String errorMessage;
	public RequestResultInfoVo() {
		super();
	}

	public String getErrorType() {
		return errorType;
	}

	public void setErrorType(String errorType) {
		this.errorType = errorType;
	}

	public String getQymc() {
		return qymc;
	}

	public void setQymc(String qymc) {
		this.qymc = qymc;
	}

	public String getXybm() {
		return xybm;
	}

	public void setXybm(String xybm) {
		this.xybm = xybm;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public RequestResultInfoVo(Long id) {
  		super();
  		this.id = id;
	}

	public List<RequestResultInfoVo> getRequestResultInfoList() {
		return requestResultInfoList;
	}

	public void setRequestResultInfoList(List<RequestResultInfoVo> requestResultInfoList) {
		this.requestResultInfoList = requestResultInfoList;
	}

}
