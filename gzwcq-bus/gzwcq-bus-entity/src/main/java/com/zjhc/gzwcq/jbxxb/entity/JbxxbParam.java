package com.zjhc.gzwcq.jbxxb.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <br/>
 * 表名： cq_jbxxb <br/>
 * 描述：Jbxxb查询类 <br/>
 */
@ApiModel(value = "Jbxxb对象", description = "jbxxb")
public class JbxxbParam extends Jbxxb {

    private static final long serialVersionUID = 18L;

    @ApiParam(value = "查询页（和偏移量二选一）")
    private int pageNumber;

    @ApiParam(value = "每页数量")
    private int limit;

    @ApiParam(value = "当前偏移量（和查询页二选一）")
    private int offest;

    @ApiParam(value = "数据权限")
    private Set<String> visibles;

    @ApiParam(value = "是否根据企业分组")
    private String isGroup;
    @ApiParam(value = "登记证2/登记表1")
    private String djType;
    @ApiParam(value = "登录用户id")
    private String userId;
    @ApiParam(value = "登录用户组织id")
    private String userOrgId;
    @ApiParam(value = "选中的企业id数组")
    private List<String> selectedOrganizationIds;

    public String getDjType() {
        return djType;
    }

    public void setDjType(String djType) {
        this.djType = djType;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffest() {
        return offest;
    }

    public void setOffest(int offest) {
        this.offest = offest;
    }

    public String getIsGroup() {
        return isGroup;
    }

    public void setIsGroup(String isGroup) {
        this.isGroup = isGroup;
    }

    public Set<String> getVisibles() {
        return visibles;
    }

    public void setVisibles(Set<String> visibles) {
        this.visibles = visibles;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserOrgId() {
        return userOrgId;
    }

    public void setUserOrgId(String userOrgId) {
        this.userOrgId = userOrgId;
    }

    public List<String> getSelectedOrganizationIds() {
        return selectedOrganizationIds;
    }

    public void setSelectedOrganizationIds(List<String> selectedOrganizationIds) {
        this.selectedOrganizationIds = selectedOrganizationIds;
    }
}
