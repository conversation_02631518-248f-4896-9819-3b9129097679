package com.zjhc.gzwcq.jbxxb;

public enum ZzxsEnum {
    /**
    * 11
     * 13
     * 15
     * 16
     * 21
     * 23
     * 24
     * 25
     * 33
     * 38
    *
     *
     *  有限责任公司
     * 股份有限公司
     * 中外合资企业
     * 中外合作企业
     * 全民所有制企业
     * 联营企业
     * 股份合作企业
     * 有限合伙企业
     * 事业单位
     * 其他
    */
    ONE("11", "有限责任公司"),
    TWO("13", "股份有限公司"),
    THREE("15", "中外合资企业"),
    FOUR("16", "中外合作企业"),
    FIVE("21", "全民所有制企业"),
    SIX("23", "联营企业"),
    SEVEN("24", "股份合作企业"),
    EIGHT("25", "有限合伙企业"),
    NINE("33", "事业单位"),
    TEN("38", "其他");

    private String value;
    private String text;

    ZzxsEnum(String value, String text){
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    /**
     * @param value
     * @return
     */
    public static ZzxsEnum getByValue(String value){
        for(ZzxsEnum x:values()){
            if(x.getValue().equals(value)){
                return x;
            }
        }
        return null;
    }

}
