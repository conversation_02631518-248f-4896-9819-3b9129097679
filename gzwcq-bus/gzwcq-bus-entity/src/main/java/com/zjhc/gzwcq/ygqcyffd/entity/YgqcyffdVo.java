package com.zjhc.gzwcq.ygqcyffd.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ygqcyffd <br/>
 *         描述：原股权持有方浮动 <br/>
 */
public class YgqcyffdVo extends Ygqcyffd {

	private static final long serialVersionUID = 18L;

	private List<YgqcyffdVo> ygqcyffdList;
	private String fd2YgqcyfxzStr;//原股权持有方性质
	private String fd2SgfsStr;//收购方式
	private String fd2ZjyjStr;//作价依据
	public YgqcyffdVo() {
		super();
	}

  	public YgqcyffdVo(String id) {
  		super();
  		this.id = id;
	}

	public List<YgqcyffdVo> getYgqcyffdList() {
		return ygqcyffdList;
	}

	public void setYgqcyffdList(List<YgqcyffdVo> ygqcyffdList) {
		this.ygqcyffdList = ygqcyffdList;
	}

	public String getFd2YgqcyfxzStr() {
		return fd2YgqcyfxzStr;
	}

	public void setFd2YgqcyfxzStr(String fd2YgqcyfxzStr) {
		this.fd2YgqcyfxzStr = fd2YgqcyfxzStr;
	}

	public String getFd2SgfsStr() {
		return fd2SgfsStr;
	}

	public void setFd2SgfsStr(String fd2SgfsStr) {
		this.fd2SgfsStr = fd2SgfsStr;
	}

	public String getFd2ZjyjStr() {
		return fd2ZjyjStr;
	}

	public void setFd2ZjyjStr(String fd2ZjyjStr) {
		this.fd2ZjyjStr = fd2ZjyjStr;
	}
}
