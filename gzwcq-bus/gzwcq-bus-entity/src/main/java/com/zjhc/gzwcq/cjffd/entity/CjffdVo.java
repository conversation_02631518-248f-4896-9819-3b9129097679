package com.zjhc.gzwcq.cjffd.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_cjffd <br/>
 *         描述：浮动 <br/>
 */
public class CjffdVo extends Cjffd {

	private static final long serialVersionUID = 18L;

	private List<CjffdVo> cjffdList;

	public CjffdVo() {
		super();
	}

  	public CjffdVo(String id) {
  		super();
  		this.id = id;
	}

	public List<CjffdVo> getCjffdList() {
		return cjffdList;
	}

	public void setCjffdList(List<CjffdVo> cjffdList) {
		this.cjffdList = cjffdList;
	}

	private String fd5CjfxzStr; //承接方性质str

	public String getFd5CjfxzStr() {
		return fd5CjfxzStr;
	}

	public void setFd5CjfxzStr(String fd5CjfxzStr) {
		this.fd5CjfxzStr = fd5CjfxzStr;
	}
}
