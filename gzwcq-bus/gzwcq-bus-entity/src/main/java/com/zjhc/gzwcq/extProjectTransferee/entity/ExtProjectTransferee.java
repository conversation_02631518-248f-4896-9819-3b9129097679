package com.zjhc.gzwcq.extProjectTransferee.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ext_project_transferee <br/>
 *         描述：项目受让方 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtProjectTransferee implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected Long id;// 主键
  	@ApiParam(value="项目类型")
	protected String xmlx;// 项目类型
  	@ApiParam(value="项目编号")
	protected String xmbh;// 项目编号
  	@ApiParam(value="项目名称")
	protected String xmmc;// 项目名称
  	@ApiParam(value="意向方证件号")
	protected String khzjhm;// 意向方证件号
  	@ApiParam(value="意向方全称")
	protected String khqc;// 意向方全称
  	@ApiParam(value="联系方式")
	protected String mobile;// 联系方式
  	@ApiParam(value="登记时间")
	protected String djsj;// 登记时间
  	@ApiParam(value="是否优先")
	protected Integer sfyx;// 是否优先
  	@ApiParam(value="报名状态")
	protected Integer zt;// 报名状态
  	@ApiParam(value="项目所需材料名称")
	protected String yxffj;// 项目所需材料名称
  	@ApiParam(value="意向方附件URL地址,多个逗号隔开")
	protected String fatt;// 意向方附件URL地址,多个逗号隔开
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public ExtProjectTransferee() {
		super();
	}
	
  	public ExtProjectTransferee(Long id) {
  		super();
  		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getXmlx() {
		return xmlx;
	}
	public void setXmlx(String xmlx) {
		this.xmlx = xmlx;
	}
	public String getXmbh() {
		return xmbh;
	}
	public void setXmbh(String xmbh) {
		this.xmbh = xmbh;
	}
	public String getXmmc() {
		return xmmc;
	}
	public void setXmmc(String xmmc) {
		this.xmmc = xmmc;
	}
	public String getKhzjhm() {
		return khzjhm;
	}
	public void setKhzjhm(String khzjhm) {
		this.khzjhm = khzjhm;
	}
	public String getKhqc() {
		return khqc;
	}
	public void setKhqc(String khqc) {
		this.khqc = khqc;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getDjsj() {
		return djsj;
	}
	public void setDjsj(String djsj) {
		this.djsj = djsj;
	}
	public Integer getSfyx() {
		return sfyx;
	}
	public void setSfyx(Integer sfyx) {
		this.sfyx = sfyx;
	}
	public Integer getZt() {
		return zt;
	}
	public void setZt(Integer zt) {
		this.zt = zt;
	}
	public String getYxffj() {
		return yxffj;
	}
	public void setYxffj(String yxffj) {
		this.yxffj = yxffj;
	}
	public String getFatt() {
		return fatt;
	}
	public void setFatt(String fatt) {
		this.fatt = fatt;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
