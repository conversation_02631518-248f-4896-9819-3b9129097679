package com.zjhc.gzwcq.newHome.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:54:16
 **/
@ApiModel("首页业务考核功能，请求")
@Data
public class BusinessAssessmentDTO {
    @ApiModelProperty("统计日期")
    private String dateTime;

    @ApiModelProperty("统计状态 1= 本企业 ，2=本企业及以下")
    private String dataStatus;
    @ApiParam(value = "查询页（和偏移量二选一）")
    private int pageNumber;

    @ApiParam(value = "每页数量")
    private int limit;

    @ApiModelProperty("统计数据的截止年月")
    private String yearMonth;
}
