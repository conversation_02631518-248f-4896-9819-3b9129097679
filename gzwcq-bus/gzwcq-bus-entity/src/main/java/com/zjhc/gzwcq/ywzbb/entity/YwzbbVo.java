package com.zjhc.gzwcq.ywzbb.entity;

import com.zjhc.gzwcq.attachment.entity.Attachment;
import io.swagger.annotations.ApiParam;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ywzbb <br/>
 *         描述：产权业务指标表 <br/>
 */
public class YwzbbVo extends Ywzbb {

	private static final long serialVersionUID = 18L;

	private List<YwzbbVo> ywzbbList;

	private List<Attachment> mainAttachments;//业务办理申请文件
	private String xxBxbfxzStr;//被吸并方性质
	private String xxBdqyxzStr;//被投资企业性质
	private String zlTzxyYwStr;//投资协议_有无
	private String zlJcwjYwStr;//经济行为决策文件_有无
	private String zlJzggYwStr;//减资公告_有无
	private String zlPcggYwStr;//破产公告_有无
	private String zlJzrsjbgYwStr;//基准日审计报告_有无
	private String zlQyzcYwStr;//企业章程_有无
	private String zlJcjgYwStr;//进场交易交割_有无
	private String zlSjbgYwStr;//审计报告_有无
	private String zlFhbpgbaYwStr;//非货币评估备案表_有无
	private String zlYyzzYwStr;//营业执照_有无
	private String zlPgbaYwStr;// 评估备案表_有无
	private String zlGszxzmYwStr;// 工商注销证明_有无
	private String zlGytdbaYwStr;// 国有土地管理部门备案_有无
	private String zlYzbgYwStr;// 验资报告_有无
	private String zlHbxysYwStr;// 合并协议书_有无
	private String zlZhxyYwStr;// 置换协议_有无
	private String zlGqzrYwStr;// 股权转让协议_有无
	private String zlWchzxyYwStr;// 无偿划转协议_有无
	private String zlBdpgbaYwStr;// 标的评估备案表_有无
	private String zlZjyqsjbgYwStr;// 最近一期审计报告_有无
	private String zlFlxysYwStr;// 分立协议书_有无
	private String zlZxggYwStr;// 注销公告_有无
	private String zlQsbgYwStr;// 清算报告_有无
	private String zlZhypgbabYwStr;// 置换一评估备案表_有无
	private String zlZhepgbabYwStr;// 置换二评估备案表_有无
	private String zlXbpgbabYwStr;// 吸并评估备案表_有无
	private String zlBxbpgbabYwStr;// 被吸并评估备案表_有无
	private String zlTjpgbabYwStr;// 投资评估备案表_有无
	private String zlSyzcczxyYwStr;// 剩余资产处置协议_有无
	private String zlZgdbdhjyYwStr;// 职工代表大会决议_有无
	private String zlGqszfawjYwStr;// 股权设置方案文件_有无
	private String zlGdqkdjbYwStr;// 股东情况登记表_有无

	private String zlZgdbdhjyYjStr;//职工代表大会决议_意见

	public YwzbbVo() {
		super();
	}

  	public YwzbbVo(String id) {
  		super();
  		this.id = id;
	}

	public String getXxBxbfxzStr() {
		return xxBxbfxzStr;
	}

	public void setXxBxbfxzStr(String xxBxbfxzStr) {
		this.xxBxbfxzStr = xxBxbfxzStr;
	}

	public List<YwzbbVo> getYwzbbList() {
		return ywzbbList;
	}

	public void setYwzbbList(List<YwzbbVo> ywzbbList) {
		this.ywzbbList = ywzbbList;
	}

	public List<Attachment> getMainAttachments() {
		return mainAttachments;
	}

	public void setMainAttachments(List<Attachment> mainAttachments) {
		this.mainAttachments = mainAttachments;
	}

	public String getZlTzxyYwStr() {
		return dicKeyToValue(zlTzxyYw);
	}

	public void setZlTzxyYwStr(String zlTzxyYwStr) {
		this.zlTzxyYwStr = dicKeyToValue(zlTzxyYw);
	}

	public String getZlJcwjYwStr() {
		return dicKeyToValue(zlJcwjYw);
	}

	public void setZlJcwjYwStr(String zlJcwjYwStr) {
		this.zlJcwjYwStr = dicKeyToValue(zlJcwjYw);
	}

	public String getZlJzggYwStr() {
		return dicKeyToValue(zlJzggYw);
	}

	public void setZlJzggYwStr(String zlJzggYwStr) {
		this.zlJzggYwStr = dicKeyToValue(zlJzggYw);
	}

	public String getZlPcggYwStr() {
		return dicKeyToValue(zlPcggYw);
	}

	public void setZlPcggYwStr(String zlPcggYwStr) {
		this.zlPcggYwStr = dicKeyToValue(zlPcggYw);
	}

	public String getZlJzrsjbgYwStr() {
		return dicKeyToValue(zlJzrsjbgYw);
	}

	public void setZlJzrsjbgYwStr(String zlJzrsjbgYwStr) {
		this.zlJzrsjbgYwStr = dicKeyToValue(zlJzrsjbgYw);
	}

	public String getZlQyzcYwStr() {
		return dicKeyToValue(zlQyzcYw);
	}

	public void setZlQyzcYwStr(String zlQyzcYwStr) {
		this.zlQyzcYwStr = dicKeyToValue(zlQyzcYw);
	}

	public String getZlJcjgYwStr() {
		return dicKeyToValue(zlJcjgYw);
	}

	public void setZlJcjgYwStr(String zlJcjgYwStr) {
		this.zlJcjgYwStr = dicKeyToValue(zlJcjgYw);
	}

	public String getZlSjbgYwStr() {
		return dicKeyToValue(zlSjbgYw);
	}

	public void setZlSjbgYwStr(String zlSjbgYwStr) {
		this.zlSjbgYwStr = dicKeyToValue(zlSjbgYw);
	}

	public String getZlFhbpgbaYwStr() {
		return dicKeyToValue(zlFhbpgbaYw);
	}

	public void setZlFhbpgbaYwStr(String zlFhbpgbaYwStr) {
		this.zlFhbpgbaYwStr = dicKeyToValue(zlFhbpgbaYw);
	}

	public String getZlYyzzYwStr() {
		return dicKeyToValue(zlYyzzYw);
	}

	public void setZlYyzzYwStr(String zlYyzzYwStr) {
		this.zlYyzzYwStr = dicKeyToValue(zlYyzzYw);
	}

	public String getZlPgbaYwStr() {
		return dicKeyToValue(zlPgbaYw);
	}

	public void setZlPgbaYwStr(String zlPgbaYwStr) {
		this.zlPgbaYwStr = dicKeyToValue(zlPgbaYw);
	}

	public String getZlGszxzmYwStr() {
		return dicKeyToValue(zlGszxzmYw);
	}

	public void setZlGszxzmYwStr(String zlGszxzmYwStr) {
		this.zlGszxzmYwStr = dicKeyToValue(zlGszxzmYw);
	}

	public String getZlGytdbaYwStr() {
		return dicKeyToValue(zlGytdbaYw);
	}

	public void setZlGytdbaYwStr(String zlGytdbaYwStr) {
		this.zlGytdbaYwStr = dicKeyToValue(zlGytdbaYw);
	}

	public String getZlYzbgYwStr() {
		return dicKeyToValue(zlYzbgYw);
	}

	public void setZlYzbgYwStr(String zlYzbgYwStr) {
		this.zlYzbgYwStr = dicKeyToValue(zlYzbgYw);
	}

	public String getZlHbxysYwStr() {
		return dicKeyToValue(zlHbxysYw);
	}

	public void setZlHbxysYwStr(String zlHbxysYwStr) {
		this.zlHbxysYwStr = dicKeyToValue(zlHbxysYw);
	}

	public String getZlZhxyYwStr() {
		return dicKeyToValue(zlZhxyYw);
	}

	public void setZlZhxyYwStr(String zlZhxyYwStr) {
		this.zlZhxyYwStr = dicKeyToValue(zlZhxyYw);
	}

	public String getZlGqzrYwStr() {
		return dicKeyToValue(zlGqzrYw);
	}

	public void setZlGqzrYwStr(String zlGqzrYwStr) {
		this.zlGqzrYwStr = dicKeyToValue(zlGqzrYw);
	}

	public String getZlWchzxyYwStr() {
		return dicKeyToValue(zlWchzxyYw);
	}

	public void setZlWchzxyYwStr(String zlWchzxyYwStr) {
		this.zlWchzxyYwStr = dicKeyToValue(zlWchzxyYw);
	}

	public String getZlBdpgbaYwStr() {
		return dicKeyToValue(zlBdpgbaYw);
	}

	public void setZlBdpgbaYwStr(String zlBdpgbaYwStr) {
		this.zlBdpgbaYwStr = dicKeyToValue(zlBdpgbaYw);
	}

	public String getZlZjyqsjbgYwStr() {
		return dicKeyToValue(zlZjyqsjbgYw);
	}

	public void setZlZjyqsjbgYwStr(String zlZjyqsjbgYwStr) {
		this.zlZjyqsjbgYwStr = dicKeyToValue(zlZjyqsjbgYw);
	}

	public String getZlFlxysYwStr() {
		return dicKeyToValue(zlFlxysYw);
	}

	public void setZlFlxysYwStr(String zlFlxysYwStr) {
		this.zlFlxysYwStr = dicKeyToValue(zlFlxysYw);
	}

	public String getZlZxggYwStr() {
		return dicKeyToValue(zlZxggYw);
	}

	public void setZlZxggYwStr(String zlZxggYwStr) {
		this.zlZxggYwStr = dicKeyToValue(zlZxggYw);
	}

	public String getZlQsbgYwStr() {
		return dicKeyToValue(zlQsbgYw);
	}

	public void setZlQsbgYwStr(String zlQsbgYwStr) {
		this.zlQsbgYwStr = dicKeyToValue(zlQsbgYw);
	}

	public String getZlZhypgbabYwStr() {
		return dicKeyToValue(zlZhypgbabYw);
	}

	public void setZlZhypgbabYwStr(String zlZhypgbabYwStr) {
		this.zlZhypgbabYwStr = dicKeyToValue(zlZhypgbabYw);
	}

	public String getZlZhepgbabYwStr() {
		return dicKeyToValue(zlZhepgbabYw);
	}

	public void setZlZhepgbabYwStr(String zlZhepgbabYwStr) {
		this.zlZhepgbabYwStr = dicKeyToValue(zlZhepgbabYw);
	}

	public String getZlXbpgbabYwStr() {
		return dicKeyToValue(zlXbpgbabYw);
	}

	public void setZlXbpgbabYwStr(String zlXbpgbabYwStr) {
		this.zlXbpgbabYwStr = dicKeyToValue(zlXbpgbabYw);
	}

	public String getZlBxbpgbabYwStr() {
		return dicKeyToValue(zlBxbpgbabYw);
	}

	public void setZlBxbpgbabYwStr(String zlBxbpgbabYwStr) {
		this.zlBxbpgbabYwStr = dicKeyToValue(zlBxbpgbabYw);
	}

	public String getZlTjpgbabYwStr() {
		return dicKeyToValue(zlTjpgbabYw);
	}

	public void setZlTjpgbabYwStr(String zlTjpgbabYwStr) {
		this.zlTjpgbabYwStr = dicKeyToValue(zlTjpgbabYw);
	}

	public String getZlSyzcczxyYwStr() {
		return dicKeyToValue(zlSyzcczxyYw);
	}

	public void setZlSyzcczxyYwStr(String zlSyzcczxyYwStr) {
		this.zlSyzcczxyYwStr = dicKeyToValue(zlSyzcczxyYw);
	}

	public String getZlZgdbdhjyYwStr() {
		return dicKeyToValue(zlZgdbdhjyYw);
	}

	public void setZlZgdbdhjyYwStr(String zlZgdbdhjyYwStr) {
		this.zlZgdbdhjyYwStr = dicKeyToValue(zlZgdbdhjyYw);
	}

	public String getZlGqszfawjYwStr() {
		return dicKeyToValue(zlGqszfawjYw);
	}

	public void setZlGqszfawjYwStr(String zlGqszfawjYwStr) {
		this.zlGqszfawjYwStr = dicKeyToValue(zlGqszfawjYw);
	}

	public String getZlGdqkdjbYwStr() {
		return dicKeyToValue(zlGdqkdjbYw);
	}

	public void setZlGdqkdjbYwStr(String zlGdqkdjbYwStr) {
		this.zlGdqkdjbYwStr = dicKeyToValue(zlGdqkdjbYw);
	}

	public String getXxBdqyxzStr() {
		return xxBdqyxzStr;
	}

	public void setXxBdqyxzStr(String xxBdqyxzStr) {
		this.xxBdqyxzStr = xxBdqyxzStr;
	}

	public String getZlZgdbdhjyYjStr() {
		return zlZgdbdhjyYjStr;
	}

	public void setZlZgdbdhjyYjStr(String zlZgdbdhjyYjStr) {
		this.zlZgdbdhjyYjStr = zlZgdbdhjyYjStr;
	}

	/**
	 * 有无字典key转换为value
	 * @param key 字典key
	 * @return 字典value
	 */
	private static String dicKeyToValue(String key){
		if ("1".equals(key)) return "有";
		else if ("2".equals(key)) return "无";
		else return "";
	}
}
