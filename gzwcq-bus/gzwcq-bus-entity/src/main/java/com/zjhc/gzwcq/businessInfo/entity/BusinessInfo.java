package com.zjhc.gzwcq.businessInfo.entity;

import java.util.Date;
import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： rg_business_info <br/>
 *         描述：登记状态表/流程实例表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@ExcelIgnoreUnannotated
public class BusinessInfo implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期 UNITID+DATATIME唯一")
	protected String datatime;// 数据时期 UNITID+DATATIME唯一
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="登记业务方案ID")
	protected String rgSolutionid;// 登记业务方案ID
  	@ApiParam(value="登记数据状态 0 待上报 1 待审核 2 审核通过 3 退回")
	protected String rgUnitstate;// 登记数据状态 0 待上报 1 待审核 2 审核通过 3 退回
  	@ApiParam(value="登记时间轮 快照时间选项。最后一次里程碑时间，比如发起变更时间，审核时间等，如果退回的,不修改时间")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	protected Date rgTimemark;// 登记时间轮 快照时间选项。最后一次里程碑时间，比如发起变更时间，审核时间等，如果退回的,不修改时间
  	@ApiParam(value="登记类型 1：变动登记 0：占有登记 3：注销登记")
	protected String rgType;// 登记类型 1：变动登记 0：占有登记 3：注销登记
  	@ApiParam(value="登记日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date rgDate;// 登记日期
  	@ApiParam(value="划转类型(关联自废弃表RG_TRANSFERTYPE，该字段可废弃，无实际应用)")
	protected String rgTransfertype;// 划转类型(关联自废弃表RG_TRANSFERTYPE，该字段可废弃，无实际应用)
  	@ApiParam(value="当前审核节点 待上报/待【xxx企业】审核/审核完成/...")
	@ExcelProperty(value = "当前审核节点",index = 4)
	protected String afCurrentNode;// 当前审核节点 待上报/待【xxx企业】审核/审核完成/...
  	@ApiParam(value="当前审核机构 组织ID")
	protected String afCurrentunitid;// 当前审核机构 组织ID
  	@ApiParam(value="当前审核层级 初审/二审")
	protected String afCurrentAuditLevel;// 当前审核层级 初审/二审
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间
	@ApiParam(value="是否自动预警生成，默认否")
	protected Boolean ifMonitorwarn; //是否自动预警生成，默认否
	@ApiParam(value="关联自动预警ID")
	protected String monitorwarnId; //关联自动预警ID
	@ApiParam(value="登记编码（六位区域码+审批通过的时间+5位流水号）")
	protected String registerCode; //登记编码（六位区域码+审批通过的时间+5位流水号）
	protected Boolean djStatus;

	public Boolean getDjStatus() {
		if (djStatus == null){
			this.djStatus = false;
		}
		return djStatus;
	}

	public void setDjStatus(Boolean djStatus) {
		this.djStatus = djStatus;
	}

	public String getRegisterCode() {
		return registerCode;
	}

	public void setRegisterCode(String registerCode) {
		this.registerCode = registerCode;
	}

	public BusinessInfo() {
		super();
	}
	
  	public BusinessInfo(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public String getRgSolutionid() {
		return rgSolutionid;
	}
	public void setRgSolutionid(String rgSolutionid) {
		this.rgSolutionid = rgSolutionid;
	}
	public String getRgUnitstate() {
		return rgUnitstate;
	}
	public void setRgUnitstate(String rgUnitstate) {
		this.rgUnitstate = rgUnitstate;
	}
	public Date getRgTimemark() {
		return rgTimemark;
	}
	public void setRgTimemark(Date rgTimemark) {
		this.rgTimemark = rgTimemark;
	}
	public String getRgType() {
		return rgType;
	}
	public void setRgType(String rgType) {
		this.rgType = rgType;
	}
	public Date getRgDate() {
		return rgDate;
	}
	public void setRgDate(Date rgDate) {
		this.rgDate = rgDate;
	}
	public String getRgTransfertype() {
		return rgTransfertype;
	}
	public void setRgTransfertype(String rgTransfertype) {
		this.rgTransfertype = rgTransfertype;
	}
	public String getAfCurrentNode() {
		return afCurrentNode;
	}
	public void setAfCurrentNode(String afCurrentNode) {
		this.afCurrentNode = afCurrentNode;
	}
	public String getAfCurrentunitid() {
		return afCurrentunitid;
	}
	public void setAfCurrentunitid(String afCurrentunitid) {
		this.afCurrentunitid = afCurrentunitid;
	}
	public String getAfCurrentAuditLevel() {
		return afCurrentAuditLevel;
	}
	public void setAfCurrentAuditLevel(String afCurrentAuditLevel) {
		this.afCurrentAuditLevel = afCurrentAuditLevel;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Boolean getIfMonitorwarn() {
		return ifMonitorwarn;
	}

	public void setIfMonitorwarn(Boolean ifMonitorwarn) {
		this.ifMonitorwarn = ifMonitorwarn;
	}

	public String getMonitorwarnId() {
		return monitorwarnId;
	}

	public void setMonitorwarnId(String monitorwarnId) {
		this.monitorwarnId = monitorwarnId;
	}
}
