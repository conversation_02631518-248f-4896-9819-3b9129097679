package com.zjhc.gzwcq.jbxxb.entity;

import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdVo;
import com.zjhc.gzwcq.cjffd.entity.CjffdVo;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkVo;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdVo;
import com.zjhc.gzwcq.hhqy.entity.HhqyVo;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdVo;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdVo;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdVo;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdVo;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdVo;
import io.swagger.annotations.ApiParam;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_jbxxb <br/>
 *         描述：产权基本信息表 <br/>
 */
public class JbxxbVo extends Jbxxb {

	private static final long serialVersionUID = 18L;

	private List<JbxxbVo> jbxxbList;

	public JbxxbVo() {
		super();
	}

  	public JbxxbVo(String id) {
  		super();
  		this.id = id;
	}
	protected String jbZyhy1Str;
	protected String jbZyhy2Str;
	protected String jbZyhy3Str;
	protected String jdZyczr;
	@ApiParam(value="是否上市公司")
	protected String jbSfssStr;
	@ApiParam(value="是否并表")
	protected String jbSfbbStr;
	@ApiParam(value="是否代管托管")
	protected String jbSftgqyStr;
	@ApiParam(value="是否存在休眠、停业、歇业")
	protected String jbSfczblztStr;
	@ApiParam(value="是否壳公司")
	protected String jbSfkzgsStr;
	@ApiParam(value = "企业管理级次")
	protected String jbQygljcStr;
	@ApiParam(value = "国资监管机构类型")
	protected String jbQzwjkjglxStr;
	@ApiParam(value = "国资监管机构明细")
	protected String jbGzwjkjgmxStr;
	@ApiParam(value = "认缴资本币种")
	protected String jbRjzbbzStr;
	protected boolean jbRevocationStatus;

	public boolean getJbRevocationStatus() {
		return jbRevocationStatus;
	}

	public void setJbRevocationStatus(boolean jbRevocationStatus) {
		this.jbRevocationStatus = jbRevocationStatus;
	}

	public String getJbRjzbbzStr() {
		return jbRjzbbzStr;
	}

	public void setJbRjzbbzStr(String jbRjzbbzStr) {
		this.jbRjzbbzStr = jbRjzbbzStr;
	}

	public String getJdZyczr() {
		return jdZyczr;
	}

	public void setJdZyczr(String jdZyczr) {
		this.jdZyczr = jdZyczr;
	}

	public List<JbxxbVo> getJbxxbList() {
		return jbxxbList;
	}

	public void setJbxxbList(List<JbxxbVo> jbxxbList) {
		this.jbxxbList = jbxxbList;
	}
	@ApiParam(value = "代持股情况浮动表")
	protected List<DcgqkVo> jbDcggkList;

	private String createUserStr;
	private String lastUpdateUserStr;
	private String jbJnjwStr;//境内、境外
	private String jbSfybgsStr;//是否已办工商(是、否、不涉及)
	private String jbZycqdjqxStr;//占有产权登记情形
	private String jbBdcqdjqxStr;//变动产权登记情形
	private String jbZxcqdjqxStr;//注销产权登记情形
	private String jbCqdjqxStr;//产权登记情形
	private String jbQylbStr;//企业类别
	private String jbZzxsStr;//组织形式
	private String jbQyjcStr;//企业级次
	private String jbJyzkStr;//经营状况
	private String jbZcdStr;//注册地(境内)
	private String jbZcdjwStr;//注册地(境外)
	private String jbGjczqyStr;//国家出资企业
	private String jbSsbmStr;//所属部门
	private String jbGzjgjgStr;//国资监管机构
	private List<String> jbZyhyList;//所属行业集合
	private List<String> jbZyhyList2;//所属行业集合
	private List<String> jbZyhyList3;//所属行业集合
	private List<String> jbZyhyList1;//所属行业集合
	private String jbZyhyStr;//所属行业字符串
	private String jbSfzyStr;//是否国家出资企业主业
	private String jbSfztjnStr;//是否境外转投境内
	private String jbSftsmdgsStr;//是否特殊目的公司
	private String jbZcmdStr;//注册目的
	private String jbSfczgrdcgStr;//是否存在个人代持股
	private String jbRelaStr;//与国家出资企业的关系
	private String jbHgqyStr;//是否混改企业
	private String jbSfyzStr;//工商登记信息与产权登记信息是否一致
	private List<CzfdVo> fdTableData;//出资人浮动列表
	private List<CgrfdVo> fd9TableData;//股权浮动列表
	private List<FhbzcfdVo> fd1TableData;//CQ_FHBZCFD表数据
	private List<XgzjgfdVo> fd3TableData;//CQ_XGZJGFD表数据
	private List<ZrsrfdVo> fd8TableData;//CQ_ZRSRFD表数据
	private List<HrhcfdVo> fd7TableData;//CQ_Hrhcfd表数据
	private List<YgqcyffdVo> fd2TableData;//CQ_ygqcyffdVo表数据
	private List<SyccfpfdVo> fd6TableData;//剩余财产分配浮动
	private List<CjffdVo> fd5TableData;//承接方浮动
	private String jbDjlxStr;//登记类型
	private String jbDjbh;//登记编号
	private String jbShztStr;//审核状态
	private String jbQylx;//企业类型(和businessNature一样)
	private String jbQylxStr;//企业类型字符串
	private YwzbbVo ywzbbVo;//关联的合规资料1
	private Ywzbb2Vo ywzbb2Vo;//关联的合规资料2
	private HhqyVo hhqyVo;//合伙企业
	private List<HhrqkfdVo> hhrqkfdList;
	private List<DwtzqkfdVo> dwtzqkfdList;
	private Boolean canEdit;//是否能够编辑
	private String rgType;//审核类型(0:占有,1:变动,3:注销)
	private Attachment gsAttachment;//工商登记资料
	private String jbQljhStr;//是否计划清理
	private String jbGjczqyCode; //国家出资企业编号
	private String jbQysbsbzxzStr;//企业申报数币种
	private String jbSdsbzxzStr;//审定数币种选择
	private String jbCzebzxzStr;// 出资额币种选择
	private String jbSjzczbbzxzStr;// 实缴注册资本币种选择
	private String jbRjzbbzxzStr;// 认缴资本币种选择
	private String jbZczbbzxzStr;// 注册资本币种选择
	private String jbZczbbzStr;//注册资本币种
	private String jbCzrzzjgStr;//出资人组织机构

	public String getJbGjczqyCode() {
		return jbGjczqyCode;
	}

	public List<String> getJbZyhyList1() {
		return jbZyhyList1;
	}

	public void setJbZyhyList1(List<String> jbZyhyList1) {
		this.jbZyhyList1 = jbZyhyList1;
	}

	public void setJbGjczqyCode(String jbGjczqyCode) {
		this.jbGjczqyCode = jbGjczqyCode;
	}

	private Boolean canDelete;//能否删除

	private Boolean canExportJbxx;//能否导出基本信息

	public String getJbSfssStr() {
		return jbSfssStr;
	}

	public void setJbSfssStr(String jbSfssStr) {
		this.jbSfssStr = jbSfssStr;
	}

	public String getJbSfbbStr() {
		return jbSfbbStr;
	}

	public void setJbSfbbStr(String jbSfbbStr) {
		this.jbSfbbStr = jbSfbbStr;
	}

	public String getJbSftgqyStr() {
		return jbSftgqyStr;
	}

	public void setJbSftgqyStr(String jbSftgqyStr) {
		this.jbSftgqyStr = jbSftgqyStr;
	}

	public String getJbSfczblztStr() {
		return jbSfczblztStr;
	}

	public String getJbZyhy1Str() {
		return jbZyhy1Str;
	}

	public void setJbZyhy1Str(String jbZyhy1Str) {
		this.jbZyhy1Str = jbZyhy1Str;
	}

	public String getJbZyhy2Str() {
		return jbZyhy2Str;
	}

	public void setJbZyhy2Str(String jbZyhy2Str) {
		this.jbZyhy2Str = jbZyhy2Str;
	}

	public String getJbZyhy3Str() {
		return jbZyhy3Str;
	}

	public void setJbZyhy3Str(String jbZyhy3Str) {
		this.jbZyhy3Str = jbZyhy3Str;
	}

	public void setJbSfczblztStr(String jbSfczblztStr) {
		this.jbSfczblztStr = jbSfczblztStr;
	}

	public String getJbSfkzgsStr() {
		return jbSfkzgsStr;
	}

	public void setJbSfkzgsStr(String jbSfkzgsStr) {
		this.jbSfkzgsStr = jbSfkzgsStr;
	}

	public String getJbQygljcStr() {
		return jbQygljcStr;
	}

	public void setJbQygljcStr(String jbQygljcStr) {
		this.jbQygljcStr = jbQygljcStr;
	}

	public String getJbGzwjkjgmxStr() {
		return jbGzwjkjgmxStr;
	}

	public void setJbGzwjkjgmxStr(String jbGzwjkjgmxStr) {
		this.jbGzwjkjgmxStr = jbGzwjkjgmxStr;
	}

	public String getJbQzwjkjglxStr() {
		return jbQzwjkjglxStr;
	}

	public void setJbQzwjkjglxStr(String jbQzwjkjglxStr) {
		this.jbQzwjkjglxStr = jbQzwjkjglxStr;
	}



	public List<String> getJbZyhyList2() {
		return jbZyhyList2;
	}

	public void setJbZyhyList2(List<String> jbZyhyList2) {
		this.jbZyhyList2 = jbZyhyList2;
	}

	public List<String> getJbZyhyList3() {
		return jbZyhyList3;
	}

	public void setJbZyhyList3(List<String> jbZyhyList3) {
		this.jbZyhyList3 = jbZyhyList3;
	}

	public String getJbQylxStr() {
		return "1".equals(businessNature) || businessNature == null? "公司制企业":"合伙企业";
	}

	public void setJbQylxStr(String jbQylxStr) {
		this.jbQylxStr = "1".equals(businessNature) || businessNature == null? "公司制企业":"合伙企业";
	}

	public List<DcgqkVo> getJbDcggkList() {
		return jbDcggkList;
	}

	public void setJbDcggkList(List<DcgqkVo> jbDcggkList) {
		this.jbDcggkList = jbDcggkList;
	}

	public String getJbShztStr() {
		return jbShztStr;
	}

	public void setJbShztStr(String jbShztStr) {
		this.jbShztStr = jbShztStr;
	}

	public String getJbDjlxStr() {
		if (jbZycqdjqx != null && !"".equals(jbZycqdjqx)){
			return "占有登记";
		}else if (jbBdcqdjqx != null && !"".equals(jbBdcqdjqx)){
			return "变动登记";
		}else if (jbZxcqdjqx != null && !"".equals(jbZxcqdjqx)){
			return "注销登记";
		}
		return "";
	}

	public void setJbDjlxStr(String jbDjlxStr) {
		if (jbZycqdjqx != null && !"".equals(jbZycqdjqx)){
			this.jbDjlxStr = "占有登记";
		}else if (jbBdcqdjqx != null && !"".equals(jbBdcqdjqx)){
			this.jbDjlxStr = "变动登记";
		}else if (jbZxcqdjqx != null && !"".equals(jbZxcqdjqx)){
			this.jbDjlxStr = "注销登记";
		}else {
			this.jbDjlxStr = "";
		}
	}

	public List<CzfdVo> getFdTableData() {
		return fdTableData;
	}

	public void setFdTableData(List<CzfdVo> fdTableData) {
		this.fdTableData = fdTableData;
	}

	public List<CgrfdVo> getFd9TableData() {
		return fd9TableData;
	}

	public void setFd9TableData(List<CgrfdVo> fd9TableData) {
		this.fd9TableData = fd9TableData;
	}

	public List<FhbzcfdVo> getFd1TableData() {
		return fd1TableData;
	}

	public void setFd1TableData(List<FhbzcfdVo> fd1TableData) {
		this.fd1TableData = fd1TableData;
	}

	public List<XgzjgfdVo> getFd3TableData() {
		return fd3TableData;
	}

	public void setFd3TableData(List<XgzjgfdVo> fd3TableData) {
		this.fd3TableData = fd3TableData;
	}

	public List<ZrsrfdVo> getFd8TableData() {
		return fd8TableData;
	}

	public void setFd8TableData(List<ZrsrfdVo> fd8TableData) {
		this.fd8TableData = fd8TableData;
	}

	public List<HrhcfdVo> getFd7TableData() {
		return fd7TableData;
	}

	public void setFd7TableData(List<HrhcfdVo> fd7TableData) {
		this.fd7TableData = fd7TableData;
	}

	public List<YgqcyffdVo> getFd2TableData() {
		return fd2TableData;
	}

	public void setFd2TableData(List<YgqcyffdVo> fd2TableData) {
		this.fd2TableData = fd2TableData;
	}

	public List<SyccfpfdVo> getFd6TableData() {
		return fd6TableData;
	}

	public void setFd6TableData(List<SyccfpfdVo> fd6TableData) {
		this.fd6TableData = fd6TableData;
	}

	public String getCreateUserStr() {
		return createUserStr;
	}

	public void setCreateUserStr(String createUserStr) {
		this.createUserStr = createUserStr;
	}

	public String getLastUpdateUserStr() {
		return lastUpdateUserStr;
	}

	public void setLastUpdateUserStr(String lastUpdateUserStr) {
		this.lastUpdateUserStr = lastUpdateUserStr;
	}

	public String getJbJnjwStr() {
		return jbJnjwStr;
	}

	public void setJbJnjwStr(String jbJnjwStr) {
		this.jbJnjwStr = jbJnjwStr;
	}

	public String getJbSfybgsStr() {
		return jbSfybgsStr;
	}

	public void setJbSfybgsStr(String jbSfybgsStr) {
		this.jbSfybgsStr = jbSfybgsStr;
	}

	public String getJbZycqdjqxStr() {
		return jbZycqdjqxStr;
	}

	public void setJbZycqdjqxStr(String jbZycqdjqxStr) {
		this.jbZycqdjqxStr = jbZycqdjqxStr;
	}

	public String getJbQylbStr() {
		return jbQylbStr;
	}

	public void setJbQylbStr(String jbQylbStr) {
		this.jbQylbStr = jbQylbStr;
	}

	public String getJbZzxsStr() {
		return jbZzxsStr;
	}

	public void setJbZzxsStr(String jbZzxsStr) {
		this.jbZzxsStr = jbZzxsStr;
	}

	public String getJbQyjcStr() {
		return jbQyjcStr;
	}

	public void setJbQyjcStr(String jbQyjcStr) {
		this.jbQyjcStr = jbQyjcStr;
	}

	public String getJbJyzkStr() {
		return jbJyzkStr;
	}

	public void setJbJyzkStr(String jbJyzkStr) {
		this.jbJyzkStr = jbJyzkStr;
	}

	public String getJbZcdStr() {
		return jbZcdStr;
	}

	public void setJbZcdStr(String jbZcdStr) {
		this.jbZcdStr = jbZcdStr;
	}

	public String getJbZcdjwStr() {
		return jbZcdjwStr;
	}

	public void setJbZcdjwStr(String jbZcdjwStr) {
		this.jbZcdjwStr = jbZcdjwStr;
	}

	public String getJbGjczqyStr() {
		return jbGjczqyStr;
	}

	public void setJbGjczqyStr(String jbGjczqyStr) {
		this.jbGjczqyStr = jbGjczqyStr;
	}

	public String getJbSsbmStr() {
		return jbSsbmStr;
	}

	public void setJbSsbmStr(String jbSsbmStr) {
		this.jbSsbmStr = jbSsbmStr;
	}

	public String getJbGzjgjgStr() {
		return jbGzjgjgStr;
	}

	public void setJbGzjgjgStr(String jbGzjgjgStr) {
		this.jbGzjgjgStr = jbGzjgjgStr;
	}

	public String getJbZyhyStr() {
		if (jbZyhyList != null && !jbZyhyList.isEmpty()){
			StringBuilder builder = new StringBuilder();
			jbZyhyList.forEach(hy -> {
				builder.append(hy).append(",");
			});
			return builder.substring(0,builder.length()-1);
		}
		return "";
	}

	public void setJbZyhyStr(String jbZyhyStr) {
		if (jbZyhyList != null && !jbZyhyList.isEmpty()){
			StringBuilder builder = new StringBuilder();
			jbZyhyList.forEach(hy -> {
				builder.append(hy).append(",");
			});
			this.jbZyhyStr = builder.substring(0,builder.length()-1);
		}else {
			this.jbZyhyStr = "";
		}
	}

	public String getJbSfzyStr() {
		return jbSfzyStr;
	}

	public void setJbSfzyStr(String jbSfzyStr) {
		this.jbSfzyStr = jbSfzyStr;
	}

	public String getJbSfztjnStr() {
		return jbSfztjnStr;
	}

	public void setJbSfztjnStr(String jbSfztjnStr) {
		this.jbSfztjnStr = jbSfztjnStr;
	}

	public String getJbSftsmdgsStr() {
		return jbSftsmdgsStr;
	}

	public void setJbSftsmdgsStr(String jbSftsmdgsStr) {
		this.jbSftsmdgsStr = jbSftsmdgsStr;
	}

	public String getJbZcmdStr() {
		return jbZcmdStr;
	}

	public void setJbZcmdStr(String jbZcmdStr) {
		this.jbZcmdStr = jbZcmdStr;
	}

	public String getJbSfczgrdcgStr() {
		return jbSfczgrdcgStr;
	}

	public void setJbSfczgrdcgStr(String jbSfczgrdcgStr) {
		this.jbSfczgrdcgStr = jbSfczgrdcgStr;
	}

	public String getJbRelaStr() {
		return jbRelaStr;
	}

	public void setJbRelaStr(String jbRelaStr) {
		this.jbRelaStr = jbRelaStr;
	}

	public String getJbHgqyStr() {
		return jbHgqyStr;
	}

	public void setJbHgqyStr(String jbHgqyStr) {
		this.jbHgqyStr = jbHgqyStr;
	}

	public String getJbSfyzStr() {
		return jbSfyzStr;
	}

	public void setJbSfyzStr(String jbSfyzStr) {
		this.jbSfyzStr = jbSfyzStr;
	}

	public YwzbbVo getYwzbbVo() {
		return ywzbbVo;
	}

	public void setYwzbbVo(YwzbbVo ywzbbVo) {
		this.ywzbbVo = ywzbbVo;
	}

	public Ywzbb2Vo getYwzbb2Vo() {
		return ywzbb2Vo;
	}

	public void setYwzbb2Vo(Ywzbb2Vo ywzbb2Vo) {
		this.ywzbb2Vo = ywzbb2Vo;
	}

	public List<String> getJbZyhyList() {
		return jbZyhyList;
	}

	public void setJbZyhyList(List<String> jbZyhyList) {
		this.jbZyhyList = jbZyhyList;
	}

	public String getJbBdcqdjqxStr() {
		return jbBdcqdjqxStr;
	}

	public void setJbBdcqdjqxStr(String jbBdcqdjqxStr) {
		this.jbBdcqdjqxStr = jbBdcqdjqxStr;
	}

	public String getJbZxcqdjqxStr() {
		return jbZxcqdjqxStr;
	}

	public void setJbZxcqdjqxStr(String jbZxcqdjqxStr) {
		this.jbZxcqdjqxStr = jbZxcqdjqxStr;
	}

	public HhqyVo getHhqyVo() {
		return hhqyVo;
	}

	public void setHhqyVo(HhqyVo hhqyVo) {
		this.hhqyVo = hhqyVo;
	}

	public List<HhrqkfdVo> getHhrqkfdList() {
		return hhrqkfdList;
	}

	public void setHhrqkfdList(List<HhrqkfdVo> hhrqkfdList) {
		this.hhrqkfdList = hhrqkfdList;
	}

	public List<DwtzqkfdVo> getDwtzqkfdList() {
		return dwtzqkfdList;
	}

	public void setDwtzqkfdList(List<DwtzqkfdVo> dwtzqkfdList) {
		this.dwtzqkfdList = dwtzqkfdList;
	}

	public Boolean getCanEdit() {
		return canEdit;
	}

	public void setCanEdit(Boolean canEdit) {
		this.canEdit = canEdit;
	}

	public String getRgType() {
		return rgType;
	}

	public void setRgType(String rgType) {
		this.rgType = rgType;
	}

	public Attachment getGsAttachment() {
		return gsAttachment;
	}

	public void setGsAttachment(Attachment gsAttachment) {
		this.gsAttachment = gsAttachment;
	}

	public String getJbQljhStr() {
		return jbQljhStr;
	}

	public void setJbQljhStr(String jbQljhStr) {
		this.jbQljhStr = jbQljhStr;
	}

	public String getJbQysbsbzxzStr() {
		return jbQysbsbzxzStr;
	}

	public void setJbQysbsbzxzStr(String jbQysbsbzxzStr) {
		this.jbQysbsbzxzStr = jbQysbsbzxzStr;
	}

	public String getJbSdsbzxzStr() {
		return jbSdsbzxzStr;
	}

	public void setJbSdsbzxzStr(String jbSdsbzxzStr) {
		this.jbSdsbzxzStr = jbSdsbzxzStr;
	}

	public String getJbCzebzxzStr() {
		return jbCzebzxzStr;
	}

	public void setJbCzebzxzStr(String jbCzebzxzStr) {
		this.jbCzebzxzStr = jbCzebzxzStr;
	}

	public String getJbSjzczbbzxzStr() {
		return jbSjzczbbzxzStr;
	}

	public void setJbSjzczbbzxzStr(String jbSjzczbbzxzStr) {
		this.jbSjzczbbzxzStr = jbSjzczbbzxzStr;
	}

	public String getJbRjzbbzxzStr() {
		return jbRjzbbzxzStr;
	}

	public void setJbRjzbbzxzStr(String jbRjzbbzxzStr) {
		this.jbRjzbbzxzStr = jbRjzbbzxzStr;
	}

	public String getJbZczbbzxzStr() {
		return jbZczbbzxzStr;
	}

	public void setJbZczbbzxzStr(String jbZczbbzxzStr) {
		this.jbZczbbzxzStr = jbZczbbzxzStr;
	}

	public String getJbZczbbzStr() {
		return jbZczbbzStr;
	}

	public void setJbZczbbzStr(String jbZczbbzStr) {
		this.jbZczbbzStr = jbZczbbzStr;
	}

	public List<CjffdVo> getFd5TableData() {
		return fd5TableData;
	}

	public void setFd5TableData(List<CjffdVo> fd5TableData) {
		this.fd5TableData = fd5TableData;
	}

	public String getJbCzrzzjgStr() {
		return jbCzrzzjgStr;
	}

	public void setJbCzrzzjgStr(String jbCzrzzjgStr) {
		this.jbCzrzzjgStr = jbCzrzzjgStr;
	}

	public String getJbQylx() {
		return businessNature;
	}

	public void setJbQylx(String jbQylx) {
		this.jbQylx = businessNature;
	}

	public String getJbDjbh() {
		return jbDjbh;
	}

	public void setJbDjbh(String jbDjbh) {
		this.jbDjbh = jbDjbh;
	}

	public Boolean getCanDelete() {
		return canDelete;
	}

	public void setCanDelete(Boolean canDelete) {
		this.canDelete = canDelete;
	}

	public String getJbCqdjqxStr() {
		return jbCqdjqxStr;
	}

	public void setJbCqdjqxStr(String jbCqdjqxStr) {
		this.jbCqdjqxStr = jbCqdjqxStr;
	}

	public Boolean getCanExportJbxx() {
		return canExportJbxx;
	}

	public void setCanExportJbxx(Boolean canExportJbxx) {
		this.canExportJbxx = canExportJbxx;
	}
}
