package com.zjhc.gzwcq.ywzbb2.entity;

import com.zjhc.gzwcq.attachment.entity.Attachment;
import io.swagger.annotations.ApiParam;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ywzbb_2 <br/>
 *         描述：产权业务指标表2 <br/>
 */
public class Ywzbb2Vo extends Ywzbb2 {

	private static final long serialVersionUID = 18L;

	private List<Ywzbb2Vo> ywzbb2List;
	private String xxYwfhbczStr;//有无非货币出资
	private String xxZhyssgzjgjgStr;//置换方（一）所属国资监管机构
	private String xxYwfhbzfsgjkStr;//有无非货币支付收购价款
	private String xxYwfhbjzStr;//有无非货币减资
	private String zlYxhztzsYwStr;//企业名称预先核准通知书_有无
	private String xxZhyssgjczqyStr;//置换方（一）所属国家出资企业
	private String xxZhessgzjgjgStr;//置换方（二）所属国资监管机构
	private String xxZhessgjczqyStr;//置换方（二）所属国家出资企业

	private List<Attachment> otherAttachments;//其他附件

	private List<Attachment> zlQsbgAttachment;// 清算报告
	private List<Attachment> zlFlxysAttachment;// 分立协议书
	private List<Attachment> zlGszxzmAttachment;// 工商注销证明
	private List<Attachment> zlZgdbdhjyAttachment;// 职工代表大会决议
	private List<Attachment> zlGqszfawjAttachment;// 股权设置方案文件
	private List<Attachment> zlZxggAttachment;// 注销公告
	private List<Attachment> zlHbxysAttachment;// 合并协议书
	private List<Attachment> zlJzrsjbgAttachment;// 基准日审计报告
	private List<Attachment> zlZhxyAttachment;// 置换协议
	private List<Attachment> zlFhbpgbaAttachment;// 非货币评估备案表
	private List<Attachment> zlZjyqsjbgAttachment;// 最近一期审计报告
	private List<Attachment> zlPgbaAttachment;// 评估备案表
	private List<Attachment> zlBdpgbaAttachment;// 标的评估备案表
	private List<Attachment> zlWchzxyAttachment;// 无偿划转协议
	private List<Attachment> zlJzggAttachment;// 减资公告
	private List<Attachment> zlYyzzAttachment;// 营业执照
	private List<Attachment> zlSjbgAttachment;// 审计报告
	private List<Attachment> zlYzbgAttachment;// 验资报告
	private List<Attachment> zlQyzcAttachment;// 企业章程
	private List<Attachment> zlGdqkdjbAttachment;// 股东情况登记表
	private List<Attachment> zlZhypgbabAttachment;// 置换一评估备案表
	private List<Attachment> zlSyzcczxyAttachment;// 剩余资产处置协议
	private List<Attachment> zlTjpgbabAttachment;// 投资评估备案表
	private List<Attachment> zlGqzrAttachment;// 股权转让协议
	private List<Attachment> zlZhepgbabAttachment;// 置换二评估备案表
	private List<Attachment> zlXbpgbabAttachment;// 吸并评估备案表
	private List<Attachment> zlTzxyAttachment;// 投资协议
	private List<Attachment> zlBxbpgbabAttachment;// 被吸并评估备案表
	private List<Attachment> zlGytdbaAttachment;// 国有土地管理部门备案
	private List<Attachment> zlJcjgAttachment;// 进场交易交割
	private List<Attachment> zlJcwjAttachment;// 经济行为决策文件
	private List<Attachment> zlPcggAttachment;// 破产公告
	private List<Attachment> zlYxhztzsAttachment;//预先核准通知书

	public String getXxYwfhbczStr() {
		return dicKeyToValue(xxYwfhbcz);
	}

	public void setXxYwfhbczStr(String xxYwfhbczStr) {
		this.xxYwfhbczStr = dicKeyToValue(xxYwfhbcz);
	}

	public Ywzbb2Vo() {
		super();
	}

  	public Ywzbb2Vo(String id) {
  		super();
  		this.id = id;
	}

	public String getXxZhyssgzjgjgStr() {
		return xxZhyssgzjgjgStr;
	}

	public List<Attachment> getZlYxhztzsAttachment() {
		return zlYxhztzsAttachment;
	}

	public void setZlYxhztzsAttachment(List<Attachment> zlYxhztzsAttachment) {
		this.zlYxhztzsAttachment = zlYxhztzsAttachment;
	}

	public void setXxZhyssgzjgjgStr(String xxZhyssgzjgjgStr) {
		this.xxZhyssgzjgjgStr = xxZhyssgzjgjgStr;
	}

	public String getXxZhyssgjczqyStr() {
		return xxZhyssgjczqyStr;
	}

	public String getXxZhessgzjgjgStr() {
		return xxZhessgzjgjgStr;
	}

	public void setXxZhessgzjgjgStr(String xxZhessgzjgjgStr) {
		this.xxZhessgzjgjgStr = xxZhessgzjgjgStr;
	}

	public String getXxZhessgjczqyStr() {
		return xxZhessgjczqyStr;
	}

	public void setXxZhessgjczqyStr(String xxZhessgjczqyStr) {
		this.xxZhessgjczqyStr = xxZhessgjczqyStr;
	}

	public void setXxZhyssgjczqyStr(String xxZhyssgjczqyStr) {
		this.xxZhyssgjczqyStr = xxZhyssgjczqyStr;
	}

	public List<Ywzbb2Vo> getYwzbb2List() {
		return ywzbb2List;
	}

	public void setYwzbb2List(List<Ywzbb2Vo> ywzbb2List) {
		this.ywzbb2List = ywzbb2List;
	}

	public List<Attachment> getOtherAttachments() {
		return otherAttachments;
	}

	public void setOtherAttachments(List<Attachment> otherAttachments) {
		this.otherAttachments = otherAttachments;
	}

	public String getXxYwfhbzfsgjkStr() {
		return dicKeyToValue(xxYwfhbzfsgjk);
	}

	public void setXxYwfhbzfsgjkStr(String xxYwfhbzfsgjkStr) {
		this.xxYwfhbzfsgjkStr = dicKeyToValue(xxYwfhbzfsgjk);
	}

	public String getXxYwfhbjzStr() {
		return dicKeyToValue(xxYwfhbjz);
	}

	public void setXxYwfhbjzStr(String xxYwfhbjzStr) {
		this.xxYwfhbjzStr = dicKeyToValue(xxYwfhbjz);
	}

	public String getZlYxhztzsYwStr() {
		return dicKeyToValue(zlYxhztzsYw);
	}

	public void setZlYxhztzsYwStr(String zlYxhztzsYwStr) {
		this.zlYxhztzsYwStr = dicKeyToValue(zlYxhztzsYw);
	}

	public List<Attachment> getZlQsbgAttachment() {
		return zlQsbgAttachment;
	}

	public void setZlQsbgAttachment(List<Attachment> zlQsbgAttachment) {
		this.zlQsbgAttachment = zlQsbgAttachment;
	}

	public List<Attachment> getZlFlxysAttachment() {
		return zlFlxysAttachment;
	}

	public void setZlFlxysAttachment(List<Attachment> zlFlxysAttachment) {
		this.zlFlxysAttachment = zlFlxysAttachment;
	}

	public List<Attachment> getZlGszxzmAttachment() {
		return zlGszxzmAttachment;
	}

	public void setZlGszxzmAttachment(List<Attachment> zlGszxzmAttachment) {
		this.zlGszxzmAttachment = zlGszxzmAttachment;
	}

	public List<Attachment> getZlZgdbdhjyAttachment() {
		return zlZgdbdhjyAttachment;
	}

	public void setZlZgdbdhjyAttachment(List<Attachment> zlZgdbdhjyAttachment) {
		this.zlZgdbdhjyAttachment = zlZgdbdhjyAttachment;
	}

	public List<Attachment> getZlGqszfawjAttachment() {
		return zlGqszfawjAttachment;
	}

	public void setZlGqszfawjAttachment(List<Attachment> zlGqszfawjAttachment) {
		this.zlGqszfawjAttachment = zlGqszfawjAttachment;
	}

	public List<Attachment> getZlZxggAttachment() {
		return zlZxggAttachment;
	}

	public void setZlZxggAttachment(List<Attachment> zlZxggAttachment) {
		this.zlZxggAttachment = zlZxggAttachment;
	}

	public List<Attachment> getZlHbxysAttachment() {
		return zlHbxysAttachment;
	}

	public void setZlHbxysAttachment(List<Attachment> zlHbxysAttachment) {
		this.zlHbxysAttachment = zlHbxysAttachment;
	}

	public List<Attachment> getZlJzrsjbgAttachment() {
		return zlJzrsjbgAttachment;
	}

	public void setZlJzrsjbgAttachment(List<Attachment> zlJzrsjbgAttachment) {
		this.zlJzrsjbgAttachment = zlJzrsjbgAttachment;
	}

	public List<Attachment> getZlZhxyAttachment() {
		return zlZhxyAttachment;
	}

	public void setZlZhxyAttachment(List<Attachment> zlZhxyAttachment) {
		this.zlZhxyAttachment = zlZhxyAttachment;
	}

	public List<Attachment> getZlFhbpgbaAttachment() {
		return zlFhbpgbaAttachment;
	}

	public void setZlFhbpgbaAttachment(List<Attachment> zlFhbpgbaAttachment) {
		this.zlFhbpgbaAttachment = zlFhbpgbaAttachment;
	}

	public List<Attachment> getZlZjyqsjbgAttachment() {
		return zlZjyqsjbgAttachment;
	}

	public void setZlZjyqsjbgAttachment(List<Attachment> zlZjyqsjbgAttachment) {
		this.zlZjyqsjbgAttachment = zlZjyqsjbgAttachment;
	}

	public List<Attachment> getZlPgbaAttachment() {
		return zlPgbaAttachment;
	}

	public void setZlPgbaAttachment(List<Attachment> zlPgbaAttachment) {
		this.zlPgbaAttachment = zlPgbaAttachment;
	}

	public List<Attachment> getZlBdpgbaAttachment() {
		return zlBdpgbaAttachment;
	}

	public void setZlBdpgbaAttachment(List<Attachment> zlBdpgbaAttachment) {
		this.zlBdpgbaAttachment = zlBdpgbaAttachment;
	}

	public List<Attachment> getZlWchzxyAttachment() {
		return zlWchzxyAttachment;
	}

	public void setZlWchzxyAttachment(List<Attachment> zlWchzxyAttachment) {
		this.zlWchzxyAttachment = zlWchzxyAttachment;
	}

	public List<Attachment> getZlJzggAttachment() {
		return zlJzggAttachment;
	}

	public void setZlJzggAttachment(List<Attachment> zlJzggAttachment) {
		this.zlJzggAttachment = zlJzggAttachment;
	}

	public List<Attachment> getZlYyzzAttachment() {
		return zlYyzzAttachment;
	}

	public void setZlYyzzAttachment(List<Attachment> zlYyzzAttachment) {
		this.zlYyzzAttachment = zlYyzzAttachment;
	}

	public List<Attachment> getZlSjbgAttachment() {
		return zlSjbgAttachment;
	}

	public void setZlSjbgAttachment(List<Attachment> zlSjbgAttachment) {
		this.zlSjbgAttachment = zlSjbgAttachment;
	}

	public List<Attachment> getZlYzbgAttachment() {
		return zlYzbgAttachment;
	}

	public void setZlYzbgAttachment(List<Attachment> zlYzbgAttachment) {
		this.zlYzbgAttachment = zlYzbgAttachment;
	}

	public List<Attachment> getZlQyzcAttachment() {
		return zlQyzcAttachment;
	}

	public void setZlQyzcAttachment(List<Attachment> zlQyzcAttachment) {
		this.zlQyzcAttachment = zlQyzcAttachment;
	}

	public List<Attachment> getZlGdqkdjbAttachment() {
		return zlGdqkdjbAttachment;
	}

	public void setZlGdqkdjbAttachment(List<Attachment> zlGdqkdjbAttachment) {
		this.zlGdqkdjbAttachment = zlGdqkdjbAttachment;
	}

	public List<Attachment> getZlZhypgbabAttachment() {
		return zlZhypgbabAttachment;
	}

	public void setZlZhypgbabAttachment(List<Attachment> zlZhypgbabAttachment) {
		this.zlZhypgbabAttachment = zlZhypgbabAttachment;
	}

	public List<Attachment> getZlSyzcczxyAttachment() {
		return zlSyzcczxyAttachment;
	}

	public void setZlSyzcczxyAttachment(List<Attachment> zlSyzcczxyAttachment) {
		this.zlSyzcczxyAttachment = zlSyzcczxyAttachment;
	}

	public List<Attachment> getZlTjpgbabAttachment() {
		return zlTjpgbabAttachment;
	}

	public void setZlTjpgbabAttachment(List<Attachment> zlTjpgbabAttachment) {
		this.zlTjpgbabAttachment = zlTjpgbabAttachment;
	}

	public List<Attachment> getZlGqzrAttachment() {
		return zlGqzrAttachment;
	}

	public void setZlGqzrAttachment(List<Attachment> zlGqzrAttachment) {
		this.zlGqzrAttachment = zlGqzrAttachment;
	}

	public List<Attachment> getZlZhepgbabAttachment() {
		return zlZhepgbabAttachment;
	}

	public void setZlZhepgbabAttachment(List<Attachment> zlZhepgbabAttachment) {
		this.zlZhepgbabAttachment = zlZhepgbabAttachment;
	}

	public List<Attachment> getZlXbpgbabAttachment() {
		return zlXbpgbabAttachment;
	}

	public void setZlXbpgbabAttachment(List<Attachment> zlXbpgbabAttachment) {
		this.zlXbpgbabAttachment = zlXbpgbabAttachment;
	}

	public List<Attachment> getZlTzxyAttachment() {
		return zlTzxyAttachment;
	}

	public void setZlTzxyAttachment(List<Attachment> zlTzxyAttachment) {
		this.zlTzxyAttachment = zlTzxyAttachment;
	}

	public List<Attachment> getZlBxbpgbabAttachment() {
		return zlBxbpgbabAttachment;
	}

	public void setZlBxbpgbabAttachment(List<Attachment> zlBxbpgbabAttachment) {
		this.zlBxbpgbabAttachment = zlBxbpgbabAttachment;
	}

	public List<Attachment> getZlGytdbaAttachment() {
		return zlGytdbaAttachment;
	}

	public void setZlGytdbaAttachment(List<Attachment> zlGytdbaAttachment) {
		this.zlGytdbaAttachment = zlGytdbaAttachment;
	}

	public List<Attachment> getZlJcjgAttachment() {
		return zlJcjgAttachment;
	}

	public void setZlJcjgAttachment(List<Attachment> zlJcjgAttachment) {
		this.zlJcjgAttachment = zlJcjgAttachment;
	}

	public List<Attachment> getZlJcwjAttachment() {
		return zlJcwjAttachment;
	}

	public void setZlJcwjAttachment(List<Attachment> zlJcwjAttachment) {
		this.zlJcwjAttachment = zlJcwjAttachment;
	}

	public List<Attachment> getZlPcggAttachment() {
		return zlPcggAttachment;
	}

	public void setZlPcggAttachment(List<Attachment> zlPcggAttachment) {
		this.zlPcggAttachment = zlPcggAttachment;
	}

	/**
	 * 有无字典key转换为value
	 * @param key 字典key
	 * @return 字典value
	 */
	private static String dicKeyToValue(String key){
		if ("1".equals(key)) return "有";
		else if ("2".equals(key)) return "无";
		else return "";
	}
}
