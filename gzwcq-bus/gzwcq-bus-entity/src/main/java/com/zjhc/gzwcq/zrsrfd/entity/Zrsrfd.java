package com.zjhc.gzwcq.zrsrfd.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_zrsrfd <br/>
 *         描述：转让受让浮动 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Zrsrfd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期")
	protected String datatime;// 数据时期
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="转让方名称")
	protected String fd8Zrfmc;// 转让方名称
  	@ApiParam(value="转让类别")
	protected String fd8Zrlb;// 转让类别
  	@ApiParam(value="受让股权审计净资产值（万元）")
	protected BigDecimal fd8Srgqsjjzcz;// 受让股权审计净资产值（万元）
  	@ApiParam(value="受让股权评估净资产值（万元）")
	protected BigDecimal fd8Srgqpgjzcz;// 受让股权评估净资产值（万元）
  	@ApiParam(value="成交价（万元）")
	protected BigDecimal fd8Cjj;// 成交价（万元）
  	@ApiParam(value="受让方名称")
	protected String fd8Srfmc;// 受让方名称
  	@ApiParam(value="受让方性质")
	protected String fd8Srfxz;// 受让方性质
  	@ApiParam(value="转让股权审计净资产值（万元）")
	protected BigDecimal fd8Zrgqsjjzcz;// 转让股权审计净资产值（万元）
  	@ApiParam(value="转让股权评估净资产值（万元）")
	protected BigDecimal fd8Zrgqpgjzcz;// 转让股权评估净资产值（万元）
  	@ApiParam(value="作价依据")
	protected String fd8Zjyj;// 作价依据
  	@ApiParam(value="备注")
	protected String fd8Bz;// 备注
  	@ApiParam(value="转让方所属国资监管机构")
	protected String fd8Zrfssgzjgjg;// 转让方所属国资监管机构
  	@ApiParam(value="受让方所属国资监管机构")
	protected String fd8Srfssgzjgjg;// 受让方所属国资监管机构
  	@ApiParam(value="转让股权比例")
	protected BigDecimal fd8Zrgqbl;// 转让股权比例
  	@ApiParam(value="受让股权比例")
	protected BigDecimal fd8Srgqbl;// 受让股权比例
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Zrsrfd() {
		super();
	}
	
  	public Zrsrfd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public String getFd8Zrfmc() {
		return fd8Zrfmc;
	}
	public void setFd8Zrfmc(String fd8Zrfmc) {
		this.fd8Zrfmc = fd8Zrfmc;
	}
	public String getFd8Zrlb() {
		return fd8Zrlb;
	}
	public void setFd8Zrlb(String fd8Zrlb) {
		this.fd8Zrlb = fd8Zrlb;
	}
	public BigDecimal getFd8Srgqsjjzcz() {
		return fd8Srgqsjjzcz;
	}
	public void setFd8Srgqsjjzcz(BigDecimal fd8Srgqsjjzcz) {
		this.fd8Srgqsjjzcz = fd8Srgqsjjzcz;
	}
	public BigDecimal getFd8Srgqpgjzcz() {
		return fd8Srgqpgjzcz;
	}
	public void setFd8Srgqpgjzcz(BigDecimal fd8Srgqpgjzcz) {
		this.fd8Srgqpgjzcz = fd8Srgqpgjzcz;
	}
	public BigDecimal getFd8Cjj() {
		return fd8Cjj;
	}
	public void setFd8Cjj(BigDecimal fd8Cjj) {
		this.fd8Cjj = fd8Cjj;
	}
	public String getFd8Srfmc() {
		return fd8Srfmc;
	}
	public void setFd8Srfmc(String fd8Srfmc) {
		this.fd8Srfmc = fd8Srfmc;
	}
	public String getFd8Srfxz() {
		return fd8Srfxz;
	}
	public void setFd8Srfxz(String fd8Srfxz) {
		this.fd8Srfxz = fd8Srfxz;
	}
	public BigDecimal getFd8Zrgqsjjzcz() {
		return fd8Zrgqsjjzcz;
	}
	public void setFd8Zrgqsjjzcz(BigDecimal fd8Zrgqsjjzcz) {
		this.fd8Zrgqsjjzcz = fd8Zrgqsjjzcz;
	}
	public BigDecimal getFd8Zrgqpgjzcz() {
		return fd8Zrgqpgjzcz;
	}
	public void setFd8Zrgqpgjzcz(BigDecimal fd8Zrgqpgjzcz) {
		this.fd8Zrgqpgjzcz = fd8Zrgqpgjzcz;
	}
	public String getFd8Zjyj() {
		return fd8Zjyj;
	}
	public void setFd8Zjyj(String fd8Zjyj) {
		this.fd8Zjyj = fd8Zjyj;
	}
	public String getFd8Bz() {
		return fd8Bz;
	}
	public void setFd8Bz(String fd8Bz) {
		this.fd8Bz = fd8Bz;
	}
	public String getFd8Zrfssgzjgjg() {
		return fd8Zrfssgzjgjg;
	}
	public void setFd8Zrfssgzjgjg(String fd8Zrfssgzjgjg) {
		this.fd8Zrfssgzjgjg = fd8Zrfssgzjgjg;
	}
	public String getFd8Srfssgzjgjg() {
		return fd8Srfssgzjgjg;
	}
	public void setFd8Srfssgzjgjg(String fd8Srfssgzjgjg) {
		this.fd8Srfssgzjgjg = fd8Srfssgzjgjg;
	}
	public BigDecimal getFd8Zrgqbl() {
		return fd8Zrgqbl;
	}
	public void setFd8Zrgqbl(BigDecimal fd8Zrgqbl) {
		this.fd8Zrgqbl = fd8Zrgqbl;
	}
	public BigDecimal getFd8Srgqbl() {
		return fd8Srgqbl;
	}
	public void setFd8Srgqbl(BigDecimal fd8Srgqbl) {
		this.fd8Srgqbl = fd8Srgqbl;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
