package com.zjhc.gzwcq.businessInfo.entity;

import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： rg_business_info <br/>
 *         描述：BusinessInfo查询类 <br/>
 */
@ApiModel(value="BusinessInfo对象",description="businessInfo")
public class BusinessInfoParam extends BusinessInfo{

	private static final long serialVersionUID = 18L;
  	
  	@ApiParam(value="查询页（和偏移量二选一）")
	private int pageNumber;
	
  	@ApiParam(value="每页数量")
	private int limit;
	
  	@ApiParam(value="当前偏移量（和查询页二选一）")
	private int offest;

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public int getOffest() {
		return offest;
	}

	public void setOffest(int offest) {
		this.offest = offest;
	}

	@ApiParam(value="上报基本信息数据")
	private Jbxxb jbxxb;

	public Jbxxb getJbxxb() {
		return jbxxb;
	}

	public void setJbxxb(Jbxxb jbxxb) {
		this.jbxxb = jbxxb;
	}
	@ApiParam(value="审核结果(1审核通过,2审核退回)")
	private String approvalResult;
	@ApiParam(value="审批意见")
	private String afProcesscomment;
	@ApiParam(value="审核工作台tab页类型(1待审核，2上级退回), 和3补录列表")
	private String todoType;
	@ApiParam(value="企业名称")
	private String jbQymc;
	@ApiParam(value="企业代码")
	private String jbZzjgdm;
	@ApiParam(value="发起类型(占有1，变动2，注销3)")
	private String submitType;
	@ApiParam(value="初审1/复审2")
	private String auditLevel;
	@ApiParam(value="用户id")
	private String userId;
	@ApiParam(value="待审核查询企业范围列表")
	private List<String> approvalUnitIds;

	public String getApprovalResult() {
		return approvalResult;
	}

	public void setApprovalResult(String approvalResult) {
		this.approvalResult = approvalResult;
	}

	public String getAfProcesscomment() {
		return afProcesscomment;
	}

	public void setAfProcesscomment(String afProcesscomment) {
		this.afProcesscomment = afProcesscomment;
	}

	public String getJbQymc() {
		return jbQymc;
	}

	public void setJbQymc(String jbQymc) {
		this.jbQymc = jbQymc;
	}

	public String getJbZzjgdm() {
		return jbZzjgdm;
	}

	public void setJbZzjgdm(String jbZzjgdm) {
		this.jbZzjgdm = jbZzjgdm;
	}

	public String getTodoType() {
		return todoType;
	}

	public void setTodoType(String todoType) {
		this.todoType = todoType;
	}

	public String getSubmitType() {
		return submitType;
	}

	public void setSubmitType(String submitType) {
		this.submitType = submitType;
	}

	public String getAuditLevel() {
		return auditLevel;
	}

	public void setAuditLevel(String auditLevel) {
		this.auditLevel = auditLevel;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public List<String> getApprovalUnitIds() {
		return approvalUnitIds;
	}

	public void setApprovalUnitIds(List<String> approvalUnitIds) {
		this.approvalUnitIds = approvalUnitIds;
	}
}
