package com.zjhc.gzwcq.xgzjgfd.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_xgzjgfd <br/>
 *         描述：新国资机构浮动 <br/>
 */
public class XgzjgfdVo extends Xgzjgfd {

	private static final long serialVersionUID = 18L;

	private List<XgzjgfdVo> xgzjgfdList;

	private String fd3XgzjgfdStr;//新国有控制出资人所属国资监管机构

	public XgzjgfdVo() {
		super();
	}

  	public XgzjgfdVo(String id) {
  		super();
  		this.id = id;
	}

	public List<XgzjgfdVo> getXgzjgfdList() {
		return xgzjgfdList;
	}

	public void setXgzjgfdList(List<XgzjgfdVo> xgzjgfdList) {
		this.xgzjgfdList = xgzjgfdList;
	}

	public String getFd3XgzjgfdStr() {
		return fd3XgzjgfdStr;
	}

	public void setFd3XgzjgfdStr(String fd3XgzjgfdStr) {
		this.fd3XgzjgfdStr = fd3XgzjgfdStr;
	}
}
