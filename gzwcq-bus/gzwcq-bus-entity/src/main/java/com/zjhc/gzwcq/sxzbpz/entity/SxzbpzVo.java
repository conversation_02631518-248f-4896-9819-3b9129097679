package com.zjhc.gzwcq.sxzbpz.entity;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <br/>
 *         表名： cq_sxzbpz <br/>
 *         描述：筛选指标配置 <br/>
 */
public class SxzbpzVo extends Sxzbpz {

	private static final long serialVersionUID = 18L;

	private List<SxzbpzVo> sxzbpzList;

	private Object dictionaryList;//对应字典映射

	private Object fieldValue;//字段值

	private List<String> situationStrs;//所属情形中文列表

	private Set<String> dicVals;//本级及下级字典值

	public SxzbpzVo() {
		super();
	}

  	public SxzbpzVo(String id) {
  		super();
  		this.id = id;
	}

	public List<SxzbpzVo> getSxzbpzList() {
		return sxzbpzList;
	}

	public void setSxzbpzList(List<SxzbpzVo> sxzbpzList) {
		this.sxzbpzList = sxzbpzList;
	}

	public Object getDictionaryList() {
		return dictionaryList;
	}

	public void setDictionaryList(Object dictionaryList) {
		this.dictionaryList = dictionaryList;
	}

	public Object getFieldValue() {
		return fieldValue;
	}

	public void setFieldValue(Object fieldValue) {
		this.fieldValue = fieldValue;
	}

	public List<String> getSituationStrs() {
		return situationStrs;
	}

	public void setSituationStrs(List<String> situationStrs) {
		this.situationStrs = situationStrs;
	}

	public Set<String> getDicVals() {
		return dicVals;
	}

	public void setDicVals(Set<String> dicVals) {
		this.dicVals = dicVals;
	}
}
