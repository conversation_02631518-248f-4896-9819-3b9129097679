package com.zjhc.gzwcq.requestResultInfo.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： api_request_result_info <br/>
 *         描述：api_request_result_info <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestResultInfo implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="id")
	protected Long id;// id
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="请求结束时间")
	protected Date requstTime;// 请求结束时间
  	@ApiParam(value="请求的app id")
	protected String requstAppId;// 请求的app id
  	@ApiParam(value="请求的密钥")
	protected String requstAppSecret;// 请求的密钥
  	@ApiParam(value="请求的url")
	protected String requstUrl;// 请求的url
  	@ApiParam(value="返回结果")
	protected String resultInfo;// 返回结果
  	@ApiParam(value="0 = 失败  1= 成功")
	protected String requstSuccess;// 0 = 失败  1= 成功
  	@ApiParam(value="文件id")
	protected String fileUploadId;// 文件id
  	@ApiParam(value="1 = 查询过   0 = 没有查询过")
	protected String selectStatus;// 1 = 查询过   0 = 没有查询过
  	@ApiParam(value="成功次数")
	protected String trueCont;// 成功次数
  	@ApiParam(value="失败次数")
	protected String falseCont;// 失败次数
  	@ApiParam(value="推送总的企业数")
	protected String totalCont;// 推送总的企业数

	public RequestResultInfo() {
		super();
	}
	
  	public RequestResultInfo(Long id) {
  		super();
  		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getRequstTime() {
		return requstTime;
	}
	public void setRequstTime(Date requstTime) {
		this.requstTime = requstTime;
	}
	public String getRequstAppId() {
		return requstAppId;
	}
	public void setRequstAppId(String requstAppId) {
		this.requstAppId = requstAppId;
	}
	public String getRequstAppSecret() {
		return requstAppSecret;
	}
	public void setRequstAppSecret(String requstAppSecret) {
		this.requstAppSecret = requstAppSecret;
	}
	public String getRequstUrl() {
		return requstUrl;
	}
	public void setRequstUrl(String requstUrl) {
		this.requstUrl = requstUrl;
	}
	public String getResultInfo() {
		return resultInfo;
	}
	public void setResultInfo(String resultInfo) {
		this.resultInfo = resultInfo;
	}
	public String getRequstSuccess() {
		return requstSuccess;
	}
	public void setRequstSuccess(String requstSuccess) {
		this.requstSuccess = requstSuccess;
	}
	public String getFileUploadId() {
		return fileUploadId;
	}
	public void setFileUploadId(String fileUploadId) {
		this.fileUploadId = fileUploadId;
	}
	public String getSelectStatus() {
		return selectStatus;
	}
	public void setSelectStatus(String selectStatus) {
		this.selectStatus = selectStatus;
	}
	public String getTrueCont() {
		return trueCont;
	}
	public void setTrueCont(String trueCont) {
		this.trueCont = trueCont;
	}
	public String getFalseCont() {
		return falseCont;
	}
	public void setFalseCont(String falseCont) {
		this.falseCont = falseCont;
	}
	public String getTotalCont() {
		return totalCont;
	}
	public void setTotalCont(String totalCont) {
		this.totalCont = totalCont;
	}
}
