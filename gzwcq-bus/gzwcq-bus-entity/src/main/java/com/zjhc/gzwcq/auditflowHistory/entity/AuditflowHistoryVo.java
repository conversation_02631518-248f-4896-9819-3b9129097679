package com.zjhc.gzwcq.auditflowHistory.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： rg_auditflow_history <br/>
 *         描述：审核历史记录 <br/>
 */
public class AuditflowHistoryVo extends AuditflowHistory {

	private static final long serialVersionUID = 18L;

	private List<AuditflowHistoryVo> auditflowHistoryList;

	public AuditflowHistoryVo() {
		super();
	}

  	public AuditflowHistoryVo(String id) {
  		super();
  		this.id = id;
	}

	public List<AuditflowHistoryVo> getAuditflowHistoryList() {
		return auditflowHistoryList;
	}

	public void setAuditflowHistoryList(List<AuditflowHistoryVo> auditflowHistoryList) {
		this.auditflowHistoryList = auditflowHistoryList;
	}

	private String approvalName; //审核人名称
	private String phone; //审核人手机号码
	private String approvalGroup; //审核分组
	private String approvalStatus; //审核状态中文
	private String businesstype; //处理组织监管机构字段(1监管机构)

	public String getApprovalName() {
		return approvalName;
	}

	public void setApprovalName(String approvalName) {
		this.approvalName = approvalName;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getApprovalGroup() {
		return approvalGroup;
	}

	public void setApprovalGroup(String approvalGroup) {
		this.approvalGroup = approvalGroup;
	}

	public String getApprovalStatus() {
		return approvalStatus;
	}

	public void setApprovalStatus(String approvalStatus) {
		this.approvalStatus = approvalStatus;
	}

	public String getBusinesstype() {
		return businesstype;
	}

	public void setBusinesstype(String businesstype) {
		this.businesstype = businesstype;
	}
}
