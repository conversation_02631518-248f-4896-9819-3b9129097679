package com.zjhc.gzwcq.ywzbb.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ywzbb <br/>
 *         描述：产权业务指标表 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Ywzbb implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期")
	protected String datatime;// 数据时期
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="评估净资产值（万元）")
	protected BigDecimal xxPgjzcz;// 评估净资产值（万元）
  	@ApiParam(value="审计净资产值(万元)")
	protected BigDecimal xxSjjzcz;// 审计净资产值(万元)
  	@ApiParam(value="发行价格(万元)")
	protected BigDecimal xxFxjg;// 发行价格(万元)
  	@ApiParam(value="经济行为决策文件_理由描述")
	protected String zlJcwjLy;// 经济行为决策文件_理由描述
  	@ApiParam(value="验资报告_中介机构名称")
	protected String zlYzbgZjjgmc;// 验资报告_中介机构名称
  	@ApiParam(value="验资报告_验资报告号")
	protected String zlYzbgYzbgh;// 验资报告_验资报告号
  	@ApiParam(value="验资报告_理由描述")
	protected String zlYzbgLy;// 验资报告_理由描述
  	@ApiParam(value="验资报告_报告出具日")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date zlYzbgYzcjr;// 验资报告_报告出具日
  	@ApiParam(value="企业章程_理由描述")
	protected String zlQyzcLy;// 企业章程_理由描述
  	@ApiParam(value="企业法人营业执照_理由描述")
	protected String zlYyzzLy;// 企业法人营业执照_理由描述
  	@ApiParam(value="评估备案表_理由描述")
	protected String zlPgbaLy;// 评估备案表_理由描述
  	@ApiParam(value="非货币评估备案表_理由描述")
	protected String zlFhbpgbaLy;// 非货币评估备案表_理由描述
  	@ApiParam(value="标的评估备案表_理由描述")
	protected String zlBdpgbaLy;// 标的评估备案表_理由描述
  	@ApiParam(value="国有土地管理部门备案_理由描述")
	protected String zlGytdbaLy;// 国有土地管理部门备案_理由描述
  	@ApiParam(value="进场交易交割_交易机构")
	protected String zlJcjgJyjg;// 进场交易交割_交易机构
  	@ApiParam(value="进场交易交割_交割单/鉴证书号")
	protected String zlJcjgJgd;// 进场交易交割_交割单/鉴证书号
  	@ApiParam(value="进场交易交割_出具日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date zlJcjgCjrq;// 进场交易交割_出具日期
  	@ApiParam(value="进场交易交割_理由描述")
	protected String zlJcjgLy;// 进场交易交割_理由描述
  	@ApiParam(value="审计报告_中介机构")
	protected String zlSjbgZjjg;// 审计报告_中介机构
  	@ApiParam(value="审计报告_报告号")
	protected String zlSjbgBgh;// 审计报告_报告号
  	@ApiParam(value="审计报告_出具日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date zlSjbgCjrq;// 审计报告_出具日期
  	@ApiParam(value="审计报告_理由描述")
	protected String zlSjbgLy;// 审计报告_理由描述
  	@ApiParam(value="股权转让协议_理由描述")
	protected String zlGqzrLy;// 股权转让协议_理由描述
  	@ApiParam(value="置换协议_理由描述")
	protected String zlZhxyLy;// 置换协议_理由描述
  	@ApiParam(value="合并协议书_理由描述")
	protected String zlHbxysLy;// 合并协议书_理由描述
  	@ApiParam(value="分立协议书_理由描述")
	protected String zlFlxysLy;// 分立协议书_理由描述
  	@ApiParam(value="无偿划转协议_理由描述")
	protected String zlWchzxyLy;// 无偿划转协议_理由描述
  	@ApiParam(value="基准日审计报告_中介机构名称")
	protected String zlJzrsjbgZjjgmc;// 基准日审计报告_中介机构名称
  	@ApiParam(value="基准日审计报告_验资报告号")
	protected String zlJzrsjbgYzbgh;// 基准日审计报告_验资报告号
  	@ApiParam(value="基准日审计报告_理由描述")
	protected String zlJzrsjbgLy;// 基准日审计报告_理由描述
  	@ApiParam(value="减资公告_媒体名称")
	protected String zlJzggMtmc;// 减资公告_媒体名称
  	@ApiParam(value="减资公告_公告日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date zlJzggGgrq;// 减资公告_公告日期
  	@ApiParam(value="减资公告_理由描述")
	protected String zlJzggLy;// 减资公告_理由描述
  	@ApiParam(value="承接方名称")
	protected String xxCjfmc;// 承接方名称
  	@ApiParam(value="司法作价(万元)")
	protected BigDecimal xxSfzj;// 司法作价(万元)
  	@ApiParam(value="最近一期审计报告_中介机构")
	protected String zlZjyqsjbgZjjg;// 最近一期审计报告_中介机构
  	@ApiParam(value="最近一期审计报告_报告号")
	protected String zlZjyqsjbgBgh;// 最近一期审计报告_报告号
  	@ApiParam(value="最近一期审计报告_理由描述")
	protected String zlZjyqsjbgLy;// 最近一期审计报告_理由描述
  	@ApiParam(value="投资协议_理由描述")
	protected String zlTzxyLy;// 投资协议_理由描述
  	@ApiParam(value="清算净资产值(万元)")
	protected BigDecimal xxQsjzcz;// 清算净资产值(万元)
  	@ApiParam(value="清算费用（万元）")
	protected BigDecimal xxQsfy;// 清算费用（万元）
  	@ApiParam(value="清偿债务（万元）")
	protected BigDecimal xxQczw;// 清偿债务（万元）
  	@ApiParam(value="破产财产总额（万元）")
	protected BigDecimal xxPcccze;// 破产财产总额（万元）
  	@ApiParam(value="破产费用和共益债务（万元）")
	protected BigDecimal xxPcfyhgyzw;// 破产费用和共益债务（万元）
  	@ApiParam(value="普通债务（万元）")
	protected BigDecimal xxPtzw;// 普通债务（万元）
  	@ApiParam(value="清算报告_中介机构名称")
	protected String zlQsbgZjjgmc;// 清算报告_中介机构名称
  	@ApiParam(value="清算报告_报告号")
	protected String zlQsbgBgh;// 清算报告_报告号
  	@ApiParam(value="清算报告_理由描述")
	protected String zlQsbgLy;// 清算报告_理由描述
  	@ApiParam(value="注销公告_媒体名称")
	protected String zlZxggMtmc;// 注销公告_媒体名称
  	@ApiParam(value="注销公告_公告日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date zlZxggGgrq;// 注销公告_公告日期
  	@ApiParam(value="注销公告_理由描述")
	protected String zlZxggLy;// 注销公告_理由描述
  	@ApiParam(value="工商注销证明_理由描述")
	protected String zlGszxzmLy;// 工商注销证明_理由描述
  	@ApiParam(value="破产公告_媒体名称")
	protected String zlPcggMtmc;// 破产公告_媒体名称
  	@ApiParam(value="破产公告_公告日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date zlPcggGgrq;// 破产公告_公告日期
  	@ApiParam(value="破产公告_理由描述")
	protected String zlPcggLy;// 破产公告_理由描述
  	@ApiParam(value="投资协议_有无")
	protected String zlTzxyYw;// 投资协议_有无
  	@ApiParam(value="经济行为决策文件_有无")
	protected String zlJcwjYw;// 经济行为决策文件_有无
  	@ApiParam(value="减资公告_有无")
	protected String zlJzggYw;// 减资公告_有无
  	@ApiParam(value="破产公告_有无")
	protected String zlPcggYw;// 破产公告_有无
  	@ApiParam(value="基准日审计报告_有无")
	protected String zlJzrsjbgYw;// 基准日审计报告_有无
  	@ApiParam(value="企业章程_有无")
	protected String zlQyzcYw;// 企业章程_有无
  	@ApiParam(value="进场交易交割_有无")
	protected String zlJcjgYw;// 进场交易交割_有无
  	@ApiParam(value="审计报告_有无")
	protected String zlSjbgYw;// 审计报告_有无
  	@ApiParam(value="非货币评估备案表_有无")
	protected String zlFhbpgbaYw;// 非货币评估备案表_有无
  	@ApiParam(value="营业执照_有无")
	protected String zlYyzzYw;// 营业执照_有无
  	@ApiParam(value="评估备案表_有无")
	protected String zlPgbaYw;// 评估备案表_有无
  	@ApiParam(value="工商注销证明_有无")
	protected String zlGszxzmYw;// 工商注销证明_有无
  	@ApiParam(value="国有土地管理部门备案_有无")
	protected String zlGytdbaYw;// 国有土地管理部门备案_有无
  	@ApiParam(value="验资报告_有无")
	protected String zlYzbgYw;// 验资报告_有无
  	@ApiParam(value="合并协议书_有无")
	protected String zlHbxysYw;// 合并协议书_有无
  	@ApiParam(value="置换协议_有无")
	protected String zlZhxyYw;// 置换协议_有无
  	@ApiParam(value="股权转让协议_有无")
	protected String zlGqzrYw;// 股权转让协议_有无
  	@ApiParam(value="无偿划转协议_有无")
	protected String zlWchzxyYw;// 无偿划转协议_有无
  	@ApiParam(value="标的评估备案表_有无")
	protected String zlBdpgbaYw;// 标的评估备案表_有无
  	@ApiParam(value="最近一期审计报告_有无")
	protected String zlZjyqsjbgYw;// 最近一期审计报告_有无
  	@ApiParam(value="分立协议书_有无")
	protected String zlFlxysYw;// 分立协议书_有无
  	@ApiParam(value="注销公告_有无")
	protected String zlZxggYw;// 注销公告_有无
  	@ApiParam(value="清算报告_有无")
	protected String zlQsbgYw;// 清算报告_有无
  	@ApiParam(value="标的企业性质")
	protected String xxBdqyxz;// 标的企业性质
  	@ApiParam(value="被吸并方性质")
	protected String xxBxbfxz;// 被吸并方性质
  	@ApiParam(value="解算原因")
	protected String xxJsyy;// 解算原因
  	@ApiParam(value="工商注销证明_注销日期")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date zlGszxzmZxrq;// 工商注销证明_注销日期
  	@ApiParam(value="经济行为决策文件_单位名称")
	protected String zlJcwjDwmc;// 经济行为决策文件_单位名称
  	@ApiParam(value="经济行为决策文件_文件名称")
	protected String zlJcwjWjmc;// 经济行为决策文件_文件名称
  	@ApiParam(value="经济行为决策文件_文件号")
	protected String zlJcwjWjh;// 经济行为决策文件_文件号
  	@ApiParam(value="评估备案表_中介机构名称")
	protected String zlPgbaZjjgmc;// 评估备案表_中介机构名称
  	@ApiParam(value="评估备案表_评估报告号")
	protected String zlPgbaPgbgh;// 评估备案表_评估报告号
  	@ApiParam(value="评估备案表_核准单位名称")
	protected String zlPgbaHzdwmc;// 评估备案表_核准单位名称
  	@ApiParam(value="评估备案表_核准文件号")
	protected String zlPgbaHzwjh;// 评估备案表_核准文件号
  	@ApiParam(value="非货币评估备案表_中介机构名称")
	protected String zlFhbpgbaZjjgmc;// 非货币评估备案表_中介机构名称
  	@ApiParam(value="非货币评估备案表_评估报告号")
	protected String zlFhbpgbaPgbgh;// 非货币评估备案表_评估报告号
  	@ApiParam(value="非货币评估备案表_核准单位名称")
	protected String zlFhbpgbaHzdwmc;// 非货币评估备案表_核准单位名称
  	@ApiParam(value="非货币评估备案表_核准文件号")
	protected String zlFhbpgbaHzwjh;// 非货币评估备案表_核准文件号
  	@ApiParam(value="标的评估备案表_中介机构名称")
	protected String zlBdpgbaZjjgmc;// 标的评估备案表_中介机构名称
  	@ApiParam(value="标的评估备案表_评估报告号")
	protected String zlBdpgbaPgbgh;// 标的评估备案表_评估报告号
  	@ApiParam(value="标的评估备案表_核准单位名称")
	protected String zlBdpgbaHzdwmc;// 标的评估备案表_核准单位名称
  	@ApiParam(value="标的评估备案表_核准文件号")
	protected String zlBdpgbaHzwjh;// 标的评估备案表_核准文件号
  	@ApiParam(value="国有土地管理部门备案_批准单位")
	protected String zlGytdbaPzdw;// 国有土地管理部门备案_批准单位
  	@ApiParam(value="国有土地管理部门备案_批准文号")
	protected String zlGytdbaPzwh;// 国有土地管理部门备案_批准文号
  	@ApiParam(value="业务办理申请文件")
	protected String zlYwblsqwj;// 业务办理申请文件
  	@ApiParam(value="置换一评估备案表_有无")
	protected String zlZhypgbabYw;// 置换一评估备案表_有无
  	@ApiParam(value="置换一评估备案表_中介机构名称")
	protected String zlZhypgbabZjjgmc;// 置换一评估备案表_中介机构名称
  	@ApiParam(value="置换一评估备案表_评估报告号")
	protected String zlZhypgbabPgbgh;// 置换一评估备案表_评估报告号
  	@ApiParam(value="置换一评估备案表_核准单位名称")
	protected String zlZhypgbabHzdwmc;// 置换一评估备案表_核准单位名称
  	@ApiParam(value="置换一评估备案表_核准文件号")
	protected String zlZhypgbabHzwjh;// 置换一评估备案表_核准文件号
  	@ApiParam(value="置换一评估备案表_理由描述")
	protected String zlZhypgbabLy;// 置换一评估备案表_理由描述
  	@ApiParam(value="置换二评估备案表_有无")
	protected String zlZhepgbabYw;// 置换二评估备案表_有无
  	@ApiParam(value="置换二评估备案表_中介机构名称")
	protected String zlZhepgbabZjjgmc;// 置换二评估备案表_中介机构名称
  	@ApiParam(value="置换二评估备案表_评估报告号")
	protected String zlZhepgbabPgbgh;// 置换二评估备案表_评估报告号
  	@ApiParam(value="置换二评估备案表_核准单位名称")
	protected String zlZhepgbabHzdwmc;// 置换二评估备案表_核准单位名称
  	@ApiParam(value="置换二评估备案表_核准文件号")
	protected String zlZhepgbabHzwjh;// 置换二评估备案表_核准文件号
  	@ApiParam(value="置换二评估备案表_理由描述")
	protected String zlZhepgbabLy;// 置换二评估备案表_理由描述
  	@ApiParam(value="吸并评估备案表_有无")
	protected String zlXbpgbabYw;// 吸并评估备案表_有无
  	@ApiParam(value="吸并评估备案表_中介机构名称")
	protected String zlXbpgbabZjjgmc;// 吸并评估备案表_中介机构名称
  	@ApiParam(value="吸并评估备案表_评估报告号")
	protected String zlXbpgbabPgbgh;// 吸并评估备案表_评估报告号
  	@ApiParam(value="吸并评估备案表_核准单位名称")
	protected String zlXbpgbabHzdwmc;// 吸并评估备案表_核准单位名称
  	@ApiParam(value="吸并评估备案表_核准文件号")
	protected String zlXbpgbabHzwjh;// 吸并评估备案表_核准文件号
  	@ApiParam(value="吸并评估备案表_理由描述")
	protected String zlXbpgbabLy;// 吸并评估备案表_理由描述
  	@ApiParam(value="被吸并评估备案表_有无")
	protected String zlBxbpgbabYw;// 被吸并评估备案表_有无
  	@ApiParam(value="被吸并评估备案表_中介机构名称")
	protected String zlBxbpgbabZjjgmc;// 被吸并评估备案表_中介机构名称
  	@ApiParam(value="被吸并评估备案表_评估报告号")
	protected String zlBxbpgbabPgbgh;// 被吸并评估备案表_评估报告号
  	@ApiParam(value="被吸并评估备案表_核准单位名称")
	protected String zlBxbpgbabHzdwmc;// 被吸并评估备案表_核准单位名称
  	@ApiParam(value="被吸并评估备案表_核准文件号")
	protected String zlBxbpgbabHzwjh;// 被吸并评估备案表_核准文件号
  	@ApiParam(value="被吸并评估备案表_理由描述")
	protected String zlBxbpgbabLy;// 被吸并评估备案表_理由描述
  	@ApiParam(value="投资评估备案表_有无")
	protected String zlTjpgbabYw;// 投资评估备案表_有无
  	@ApiParam(value="投资评估备案表_中介机构名称")
	protected String zlTjpgbabZjjgmc;// 投资评估备案表_中介机构名称
  	@ApiParam(value="投资并评估备案表_评估报告号")
	protected String zlTjpgbabPgbgh;// 投资并评估备案表_评估报告号
  	@ApiParam(value="投资评估备案表_核准单位名称")
	protected String zlTjpgbabHzdwmc;// 投资评估备案表_核准单位名称
  	@ApiParam(value="投资评估备案表_核准文件号")
	protected String zlTjpgbabHzwjh;// 投资评估备案表_核准文件号
  	@ApiParam(value="投资评估备案表_理由描述")
	protected String zlTjpgbabLy;// 投资评估备案表_理由描述
  	@ApiParam(value="剩余资产处置协议_有无")
	protected String zlSyzcczxyYw;// 剩余资产处置协议_有无
  	@ApiParam(value="剩余资产处置协议_理由描述")
	protected String zlSyzcczxyLy;// 剩余资产处置协议_理由描述
  	@ApiParam(value="职工代表大会决议_有无")
	protected String zlZgdbdhjyYw;// 职工代表大会决议_有无
  	@ApiParam(value="职工代表大会决议_意见")
	protected String zlZgdbdhjyYj;// 职工代表大会决议_意见
  	@ApiParam(value="职工代表大会决议_理由描述")
	protected String zlZgdbdhjyLy;// 职工代表大会决议_理由描述
  	@ApiParam(value="股权设置方案文件_有无")
	protected String zlGqszfawjYw;// 股权设置方案文件_有无
  	@ApiParam(value="股权设置方案文件_批准单位")
	protected String zlGqszfawjPzdw;// 股权设置方案文件_批准单位
  	@ApiParam(value="股权设置方案文件_批准文号")
	protected String zlGqszfawjPzwh;// 股权设置方案文件_批准文号
  	@ApiParam(value="股权设置方案文件_理由描述")
	protected String zlGqszfawjLy;// 股权设置方案文件_理由描述
  	@ApiParam(value="股东情况登记表_有无")
	protected String zlGdqkdjbYw;// 股东情况登记表_有无
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Ywzbb() {
		super();
	}
	
  	public Ywzbb(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public BigDecimal getXxPgjzcz() {
		return xxPgjzcz;
	}
	public void setXxPgjzcz(BigDecimal xxPgjzcz) {
		this.xxPgjzcz = xxPgjzcz;
	}
	public BigDecimal getXxSjjzcz() {
		return xxSjjzcz;
	}
	public void setXxSjjzcz(BigDecimal xxSjjzcz) {
		this.xxSjjzcz = xxSjjzcz;
	}
	public BigDecimal getXxFxjg() {
		return xxFxjg;
	}
	public void setXxFxjg(BigDecimal xxFxjg) {
		this.xxFxjg = xxFxjg;
	}
	public String getZlJcwjLy() {
		return zlJcwjLy;
	}
	public void setZlJcwjLy(String zlJcwjLy) {
		this.zlJcwjLy = zlJcwjLy;
	}
	public String getZlYzbgZjjgmc() {
		return zlYzbgZjjgmc;
	}
	public void setZlYzbgZjjgmc(String zlYzbgZjjgmc) {
		this.zlYzbgZjjgmc = zlYzbgZjjgmc;
	}
	public String getZlYzbgYzbgh() {
		return zlYzbgYzbgh;
	}
	public void setZlYzbgYzbgh(String zlYzbgYzbgh) {
		this.zlYzbgYzbgh = zlYzbgYzbgh;
	}
	public String getZlYzbgLy() {
		return zlYzbgLy;
	}
	public void setZlYzbgLy(String zlYzbgLy) {
		this.zlYzbgLy = zlYzbgLy;
	}
	public Date getZlYzbgYzcjr() {
		return zlYzbgYzcjr;
	}
	public void setZlYzbgYzcjr(Date zlYzbgYzcjr) {
		this.zlYzbgYzcjr = zlYzbgYzcjr;
	}
	public String getZlQyzcLy() {
		return zlQyzcLy;
	}
	public void setZlQyzcLy(String zlQyzcLy) {
		this.zlQyzcLy = zlQyzcLy;
	}
	public String getZlYyzzLy() {
		return zlYyzzLy;
	}
	public void setZlYyzzLy(String zlYyzzLy) {
		this.zlYyzzLy = zlYyzzLy;
	}
	public String getZlPgbaLy() {
		return zlPgbaLy;
	}
	public void setZlPgbaLy(String zlPgbaLy) {
		this.zlPgbaLy = zlPgbaLy;
	}
	public String getZlFhbpgbaLy() {
		return zlFhbpgbaLy;
	}
	public void setZlFhbpgbaLy(String zlFhbpgbaLy) {
		this.zlFhbpgbaLy = zlFhbpgbaLy;
	}
	public String getZlBdpgbaLy() {
		return zlBdpgbaLy;
	}
	public void setZlBdpgbaLy(String zlBdpgbaLy) {
		this.zlBdpgbaLy = zlBdpgbaLy;
	}
	public String getZlGytdbaLy() {
		return zlGytdbaLy;
	}
	public void setZlGytdbaLy(String zlGytdbaLy) {
		this.zlGytdbaLy = zlGytdbaLy;
	}
	public String getZlJcjgJyjg() {
		return zlJcjgJyjg;
	}
	public void setZlJcjgJyjg(String zlJcjgJyjg) {
		this.zlJcjgJyjg = zlJcjgJyjg;
	}
	public String getZlJcjgJgd() {
		return zlJcjgJgd;
	}
	public void setZlJcjgJgd(String zlJcjgJgd) {
		this.zlJcjgJgd = zlJcjgJgd;
	}
	public Date getZlJcjgCjrq() {
		return zlJcjgCjrq;
	}
	public void setZlJcjgCjrq(Date zlJcjgCjrq) {
		this.zlJcjgCjrq = zlJcjgCjrq;
	}
	public String getZlJcjgLy() {
		return zlJcjgLy;
	}
	public void setZlJcjgLy(String zlJcjgLy) {
		this.zlJcjgLy = zlJcjgLy;
	}
	public String getZlSjbgZjjg() {
		return zlSjbgZjjg;
	}
	public void setZlSjbgZjjg(String zlSjbgZjjg) {
		this.zlSjbgZjjg = zlSjbgZjjg;
	}
	public String getZlSjbgBgh() {
		return zlSjbgBgh;
	}
	public void setZlSjbgBgh(String zlSjbgBgh) {
		this.zlSjbgBgh = zlSjbgBgh;
	}
	public Date getZlSjbgCjrq() {
		return zlSjbgCjrq;
	}
	public void setZlSjbgCjrq(Date zlSjbgCjrq) {
		this.zlSjbgCjrq = zlSjbgCjrq;
	}
	public String getZlSjbgLy() {
		return zlSjbgLy;
	}
	public void setZlSjbgLy(String zlSjbgLy) {
		this.zlSjbgLy = zlSjbgLy;
	}
	public String getZlGqzrLy() {
		return zlGqzrLy;
	}
	public void setZlGqzrLy(String zlGqzrLy) {
		this.zlGqzrLy = zlGqzrLy;
	}
	public String getZlZhxyLy() {
		return zlZhxyLy;
	}
	public void setZlZhxyLy(String zlZhxyLy) {
		this.zlZhxyLy = zlZhxyLy;
	}
	public String getZlHbxysLy() {
		return zlHbxysLy;
	}
	public void setZlHbxysLy(String zlHbxysLy) {
		this.zlHbxysLy = zlHbxysLy;
	}
	public String getZlFlxysLy() {
		return zlFlxysLy;
	}
	public void setZlFlxysLy(String zlFlxysLy) {
		this.zlFlxysLy = zlFlxysLy;
	}
	public String getZlWchzxyLy() {
		return zlWchzxyLy;
	}
	public void setZlWchzxyLy(String zlWchzxyLy) {
		this.zlWchzxyLy = zlWchzxyLy;
	}
	public String getZlJzrsjbgZjjgmc() {
		return zlJzrsjbgZjjgmc;
	}
	public void setZlJzrsjbgZjjgmc(String zlJzrsjbgZjjgmc) {
		this.zlJzrsjbgZjjgmc = zlJzrsjbgZjjgmc;
	}
	public String getZlJzrsjbgYzbgh() {
		return zlJzrsjbgYzbgh;
	}
	public void setZlJzrsjbgYzbgh(String zlJzrsjbgYzbgh) {
		this.zlJzrsjbgYzbgh = zlJzrsjbgYzbgh;
	}
	public String getZlJzrsjbgLy() {
		return zlJzrsjbgLy;
	}
	public void setZlJzrsjbgLy(String zlJzrsjbgLy) {
		this.zlJzrsjbgLy = zlJzrsjbgLy;
	}
	public String getZlJzggMtmc() {
		return zlJzggMtmc;
	}
	public void setZlJzggMtmc(String zlJzggMtmc) {
		this.zlJzggMtmc = zlJzggMtmc;
	}
	public Date getZlJzggGgrq() {
		return zlJzggGgrq;
	}
	public void setZlJzggGgrq(Date zlJzggGgrq) {
		this.zlJzggGgrq = zlJzggGgrq;
	}
	public String getZlJzggLy() {
		return zlJzggLy;
	}
	public void setZlJzggLy(String zlJzggLy) {
		this.zlJzggLy = zlJzggLy;
	}
	public String getXxCjfmc() {
		return xxCjfmc;
	}
	public void setXxCjfmc(String xxCjfmc) {
		this.xxCjfmc = xxCjfmc;
	}
	public BigDecimal getXxSfzj() {
		return xxSfzj;
	}
	public void setXxSfzj(BigDecimal xxSfzj) {
		this.xxSfzj = xxSfzj;
	}
	public String getZlZjyqsjbgZjjg() {
		return zlZjyqsjbgZjjg;
	}
	public void setZlZjyqsjbgZjjg(String zlZjyqsjbgZjjg) {
		this.zlZjyqsjbgZjjg = zlZjyqsjbgZjjg;
	}
	public String getZlZjyqsjbgBgh() {
		return zlZjyqsjbgBgh;
	}
	public void setZlZjyqsjbgBgh(String zlZjyqsjbgBgh) {
		this.zlZjyqsjbgBgh = zlZjyqsjbgBgh;
	}
	public String getZlZjyqsjbgLy() {
		return zlZjyqsjbgLy;
	}
	public void setZlZjyqsjbgLy(String zlZjyqsjbgLy) {
		this.zlZjyqsjbgLy = zlZjyqsjbgLy;
	}
	public String getZlTzxyLy() {
		return zlTzxyLy;
	}
	public void setZlTzxyLy(String zlTzxyLy) {
		this.zlTzxyLy = zlTzxyLy;
	}
	public BigDecimal getXxQsjzcz() {
		return xxQsjzcz;
	}
	public void setXxQsjzcz(BigDecimal xxQsjzcz) {
		this.xxQsjzcz = xxQsjzcz;
	}
	public BigDecimal getXxQsfy() {
		return xxQsfy;
	}
	public void setXxQsfy(BigDecimal xxQsfy) {
		this.xxQsfy = xxQsfy;
	}
	public BigDecimal getXxQczw() {
		return xxQczw;
	}
	public void setXxQczw(BigDecimal xxQczw) {
		this.xxQczw = xxQczw;
	}
	public BigDecimal getXxPcccze() {
		return xxPcccze;
	}
	public void setXxPcccze(BigDecimal xxPcccze) {
		this.xxPcccze = xxPcccze;
	}
	public BigDecimal getXxPcfyhgyzw() {
		return xxPcfyhgyzw;
	}
	public void setXxPcfyhgyzw(BigDecimal xxPcfyhgyzw) {
		this.xxPcfyhgyzw = xxPcfyhgyzw;
	}
	public BigDecimal getXxPtzw() {
		return xxPtzw;
	}
	public void setXxPtzw(BigDecimal xxPtzw) {
		this.xxPtzw = xxPtzw;
	}
	public String getZlQsbgZjjgmc() {
		return zlQsbgZjjgmc;
	}
	public void setZlQsbgZjjgmc(String zlQsbgZjjgmc) {
		this.zlQsbgZjjgmc = zlQsbgZjjgmc;
	}
	public String getZlQsbgBgh() {
		return zlQsbgBgh;
	}
	public void setZlQsbgBgh(String zlQsbgBgh) {
		this.zlQsbgBgh = zlQsbgBgh;
	}
	public String getZlQsbgLy() {
		return zlQsbgLy;
	}
	public void setZlQsbgLy(String zlQsbgLy) {
		this.zlQsbgLy = zlQsbgLy;
	}
	public String getZlZxggMtmc() {
		return zlZxggMtmc;
	}
	public void setZlZxggMtmc(String zlZxggMtmc) {
		this.zlZxggMtmc = zlZxggMtmc;
	}
	public Date getZlZxggGgrq() {
		return zlZxggGgrq;
	}
	public void setZlZxggGgrq(Date zlZxggGgrq) {
		this.zlZxggGgrq = zlZxggGgrq;
	}
	public String getZlZxggLy() {
		return zlZxggLy;
	}
	public void setZlZxggLy(String zlZxggLy) {
		this.zlZxggLy = zlZxggLy;
	}
	public String getZlGszxzmLy() {
		return zlGszxzmLy;
	}
	public void setZlGszxzmLy(String zlGszxzmLy) {
		this.zlGszxzmLy = zlGszxzmLy;
	}
	public String getZlPcggMtmc() {
		return zlPcggMtmc;
	}
	public void setZlPcggMtmc(String zlPcggMtmc) {
		this.zlPcggMtmc = zlPcggMtmc;
	}
	public Date getZlPcggGgrq() {
		return zlPcggGgrq;
	}
	public void setZlPcggGgrq(Date zlPcggGgrq) {
		this.zlPcggGgrq = zlPcggGgrq;
	}
	public String getZlPcggLy() {
		return zlPcggLy;
	}
	public void setZlPcggLy(String zlPcggLy) {
		this.zlPcggLy = zlPcggLy;
	}
	public String getZlTzxyYw() {
		return zlTzxyYw;
	}
	public void setZlTzxyYw(String zlTzxyYw) {
		this.zlTzxyYw = zlTzxyYw;
	}
	public String getZlJcwjYw() {
		return zlJcwjYw;
	}
	public void setZlJcwjYw(String zlJcwjYw) {
		this.zlJcwjYw = zlJcwjYw;
	}
	public String getZlJzggYw() {
		return zlJzggYw;
	}
	public void setZlJzggYw(String zlJzggYw) {
		this.zlJzggYw = zlJzggYw;
	}
	public String getZlPcggYw() {
		return zlPcggYw;
	}
	public void setZlPcggYw(String zlPcggYw) {
		this.zlPcggYw = zlPcggYw;
	}
	public String getZlJzrsjbgYw() {
		return zlJzrsjbgYw;
	}
	public void setZlJzrsjbgYw(String zlJzrsjbgYw) {
		this.zlJzrsjbgYw = zlJzrsjbgYw;
	}
	public String getZlQyzcYw() {
		return zlQyzcYw;
	}
	public void setZlQyzcYw(String zlQyzcYw) {
		this.zlQyzcYw = zlQyzcYw;
	}
	public String getZlJcjgYw() {
		return zlJcjgYw;
	}
	public void setZlJcjgYw(String zlJcjgYw) {
		this.zlJcjgYw = zlJcjgYw;
	}
	public String getZlSjbgYw() {
		return zlSjbgYw;
	}
	public void setZlSjbgYw(String zlSjbgYw) {
		this.zlSjbgYw = zlSjbgYw;
	}
	public String getZlFhbpgbaYw() {
		return zlFhbpgbaYw;
	}
	public void setZlFhbpgbaYw(String zlFhbpgbaYw) {
		this.zlFhbpgbaYw = zlFhbpgbaYw;
	}
	public String getZlYyzzYw() {
		return zlYyzzYw;
	}
	public void setZlYyzzYw(String zlYyzzYw) {
		this.zlYyzzYw = zlYyzzYw;
	}
	public String getZlPgbaYw() {
		return zlPgbaYw;
	}
	public void setZlPgbaYw(String zlPgbaYw) {
		this.zlPgbaYw = zlPgbaYw;
	}
	public String getZlGszxzmYw() {
		return zlGszxzmYw;
	}
	public void setZlGszxzmYw(String zlGszxzmYw) {
		this.zlGszxzmYw = zlGszxzmYw;
	}
	public String getZlGytdbaYw() {
		return zlGytdbaYw;
	}
	public void setZlGytdbaYw(String zlGytdbaYw) {
		this.zlGytdbaYw = zlGytdbaYw;
	}
	public String getZlYzbgYw() {
		return zlYzbgYw;
	}
	public void setZlYzbgYw(String zlYzbgYw) {
		this.zlYzbgYw = zlYzbgYw;
	}
	public String getZlHbxysYw() {
		return zlHbxysYw;
	}
	public void setZlHbxysYw(String zlHbxysYw) {
		this.zlHbxysYw = zlHbxysYw;
	}
	public String getZlZhxyYw() {
		return zlZhxyYw;
	}
	public void setZlZhxyYw(String zlZhxyYw) {
		this.zlZhxyYw = zlZhxyYw;
	}
	public String getZlGqzrYw() {
		return zlGqzrYw;
	}
	public void setZlGqzrYw(String zlGqzrYw) {
		this.zlGqzrYw = zlGqzrYw;
	}
	public String getZlWchzxyYw() {
		return zlWchzxyYw;
	}
	public void setZlWchzxyYw(String zlWchzxyYw) {
		this.zlWchzxyYw = zlWchzxyYw;
	}
	public String getZlBdpgbaYw() {
		return zlBdpgbaYw;
	}
	public void setZlBdpgbaYw(String zlBdpgbaYw) {
		this.zlBdpgbaYw = zlBdpgbaYw;
	}
	public String getZlZjyqsjbgYw() {
		return zlZjyqsjbgYw;
	}
	public void setZlZjyqsjbgYw(String zlZjyqsjbgYw) {
		this.zlZjyqsjbgYw = zlZjyqsjbgYw;
	}
	public String getZlFlxysYw() {
		return zlFlxysYw;
	}
	public void setZlFlxysYw(String zlFlxysYw) {
		this.zlFlxysYw = zlFlxysYw;
	}
	public String getZlZxggYw() {
		return zlZxggYw;
	}
	public void setZlZxggYw(String zlZxggYw) {
		this.zlZxggYw = zlZxggYw;
	}
	public String getZlQsbgYw() {
		return zlQsbgYw;
	}
	public void setZlQsbgYw(String zlQsbgYw) {
		this.zlQsbgYw = zlQsbgYw;
	}
	public String getXxBdqyxz() {
		return xxBdqyxz;
	}
	public void setXxBdqyxz(String xxBdqyxz) {
		this.xxBdqyxz = xxBdqyxz;
	}
	public String getXxBxbfxz() {
		return xxBxbfxz;
	}
	public void setXxBxbfxz(String xxBxbfxz) {
		this.xxBxbfxz = xxBxbfxz;
	}
	public String getXxJsyy() {
		return xxJsyy;
	}
	public void setXxJsyy(String xxJsyy) {
		this.xxJsyy = xxJsyy;
	}
	public Date getZlGszxzmZxrq() {
		return zlGszxzmZxrq;
	}
	public void setZlGszxzmZxrq(Date zlGszxzmZxrq) {
		this.zlGszxzmZxrq = zlGszxzmZxrq;
	}
	public String getZlJcwjDwmc() {
		return zlJcwjDwmc;
	}
	public void setZlJcwjDwmc(String zlJcwjDwmc) {
		this.zlJcwjDwmc = zlJcwjDwmc;
	}
	public String getZlJcwjWjmc() {
		return zlJcwjWjmc;
	}
	public void setZlJcwjWjmc(String zlJcwjWjmc) {
		this.zlJcwjWjmc = zlJcwjWjmc;
	}
	public String getZlJcwjWjh() {
		return zlJcwjWjh;
	}
	public void setZlJcwjWjh(String zlJcwjWjh) {
		this.zlJcwjWjh = zlJcwjWjh;
	}
	public String getZlPgbaZjjgmc() {
		return zlPgbaZjjgmc;
	}
	public void setZlPgbaZjjgmc(String zlPgbaZjjgmc) {
		this.zlPgbaZjjgmc = zlPgbaZjjgmc;
	}
	public String getZlPgbaPgbgh() {
		return zlPgbaPgbgh;
	}
	public void setZlPgbaPgbgh(String zlPgbaPgbgh) {
		this.zlPgbaPgbgh = zlPgbaPgbgh;
	}
	public String getZlPgbaHzdwmc() {
		return zlPgbaHzdwmc;
	}
	public void setZlPgbaHzdwmc(String zlPgbaHzdwmc) {
		this.zlPgbaHzdwmc = zlPgbaHzdwmc;
	}
	public String getZlPgbaHzwjh() {
		return zlPgbaHzwjh;
	}
	public void setZlPgbaHzwjh(String zlPgbaHzwjh) {
		this.zlPgbaHzwjh = zlPgbaHzwjh;
	}
	public String getZlFhbpgbaZjjgmc() {
		return zlFhbpgbaZjjgmc;
	}
	public void setZlFhbpgbaZjjgmc(String zlFhbpgbaZjjgmc) {
		this.zlFhbpgbaZjjgmc = zlFhbpgbaZjjgmc;
	}
	public String getZlFhbpgbaPgbgh() {
		return zlFhbpgbaPgbgh;
	}
	public void setZlFhbpgbaPgbgh(String zlFhbpgbaPgbgh) {
		this.zlFhbpgbaPgbgh = zlFhbpgbaPgbgh;
	}
	public String getZlFhbpgbaHzdwmc() {
		return zlFhbpgbaHzdwmc;
	}
	public void setZlFhbpgbaHzdwmc(String zlFhbpgbaHzdwmc) {
		this.zlFhbpgbaHzdwmc = zlFhbpgbaHzdwmc;
	}
	public String getZlFhbpgbaHzwjh() {
		return zlFhbpgbaHzwjh;
	}
	public void setZlFhbpgbaHzwjh(String zlFhbpgbaHzwjh) {
		this.zlFhbpgbaHzwjh = zlFhbpgbaHzwjh;
	}
	public String getZlBdpgbaZjjgmc() {
		return zlBdpgbaZjjgmc;
	}
	public void setZlBdpgbaZjjgmc(String zlBdpgbaZjjgmc) {
		this.zlBdpgbaZjjgmc = zlBdpgbaZjjgmc;
	}
	public String getZlBdpgbaPgbgh() {
		return zlBdpgbaPgbgh;
	}
	public void setZlBdpgbaPgbgh(String zlBdpgbaPgbgh) {
		this.zlBdpgbaPgbgh = zlBdpgbaPgbgh;
	}
	public String getZlBdpgbaHzdwmc() {
		return zlBdpgbaHzdwmc;
	}
	public void setZlBdpgbaHzdwmc(String zlBdpgbaHzdwmc) {
		this.zlBdpgbaHzdwmc = zlBdpgbaHzdwmc;
	}
	public String getZlBdpgbaHzwjh() {
		return zlBdpgbaHzwjh;
	}
	public void setZlBdpgbaHzwjh(String zlBdpgbaHzwjh) {
		this.zlBdpgbaHzwjh = zlBdpgbaHzwjh;
	}
	public String getZlGytdbaPzdw() {
		return zlGytdbaPzdw;
	}
	public void setZlGytdbaPzdw(String zlGytdbaPzdw) {
		this.zlGytdbaPzdw = zlGytdbaPzdw;
	}
	public String getZlGytdbaPzwh() {
		return zlGytdbaPzwh;
	}
	public void setZlGytdbaPzwh(String zlGytdbaPzwh) {
		this.zlGytdbaPzwh = zlGytdbaPzwh;
	}
	public String getZlYwblsqwj() {
		return zlYwblsqwj;
	}
	public void setZlYwblsqwj(String zlYwblsqwj) {
		this.zlYwblsqwj = zlYwblsqwj;
	}
	public String getZlZhypgbabYw() {
		return zlZhypgbabYw;
	}
	public void setZlZhypgbabYw(String zlZhypgbabYw) {
		this.zlZhypgbabYw = zlZhypgbabYw;
	}
	public String getZlZhypgbabZjjgmc() {
		return zlZhypgbabZjjgmc;
	}
	public void setZlZhypgbabZjjgmc(String zlZhypgbabZjjgmc) {
		this.zlZhypgbabZjjgmc = zlZhypgbabZjjgmc;
	}
	public String getZlZhypgbabPgbgh() {
		return zlZhypgbabPgbgh;
	}
	public void setZlZhypgbabPgbgh(String zlZhypgbabPgbgh) {
		this.zlZhypgbabPgbgh = zlZhypgbabPgbgh;
	}
	public String getZlZhypgbabHzdwmc() {
		return zlZhypgbabHzdwmc;
	}
	public void setZlZhypgbabHzdwmc(String zlZhypgbabHzdwmc) {
		this.zlZhypgbabHzdwmc = zlZhypgbabHzdwmc;
	}
	public String getZlZhypgbabHzwjh() {
		return zlZhypgbabHzwjh;
	}
	public void setZlZhypgbabHzwjh(String zlZhypgbabHzwjh) {
		this.zlZhypgbabHzwjh = zlZhypgbabHzwjh;
	}
	public String getZlZhypgbabLy() {
		return zlZhypgbabLy;
	}
	public void setZlZhypgbabLy(String zlZhypgbabLy) {
		this.zlZhypgbabLy = zlZhypgbabLy;
	}
	public String getZlZhepgbabYw() {
		return zlZhepgbabYw;
	}
	public void setZlZhepgbabYw(String zlZhepgbabYw) {
		this.zlZhepgbabYw = zlZhepgbabYw;
	}
	public String getZlZhepgbabZjjgmc() {
		return zlZhepgbabZjjgmc;
	}
	public void setZlZhepgbabZjjgmc(String zlZhepgbabZjjgmc) {
		this.zlZhepgbabZjjgmc = zlZhepgbabZjjgmc;
	}
	public String getZlZhepgbabPgbgh() {
		return zlZhepgbabPgbgh;
	}
	public void setZlZhepgbabPgbgh(String zlZhepgbabPgbgh) {
		this.zlZhepgbabPgbgh = zlZhepgbabPgbgh;
	}
	public String getZlZhepgbabHzdwmc() {
		return zlZhepgbabHzdwmc;
	}
	public void setZlZhepgbabHzdwmc(String zlZhepgbabHzdwmc) {
		this.zlZhepgbabHzdwmc = zlZhepgbabHzdwmc;
	}
	public String getZlZhepgbabHzwjh() {
		return zlZhepgbabHzwjh;
	}
	public void setZlZhepgbabHzwjh(String zlZhepgbabHzwjh) {
		this.zlZhepgbabHzwjh = zlZhepgbabHzwjh;
	}
	public String getZlZhepgbabLy() {
		return zlZhepgbabLy;
	}
	public void setZlZhepgbabLy(String zlZhepgbabLy) {
		this.zlZhepgbabLy = zlZhepgbabLy;
	}
	public String getZlXbpgbabYw() {
		return zlXbpgbabYw;
	}
	public void setZlXbpgbabYw(String zlXbpgbabYw) {
		this.zlXbpgbabYw = zlXbpgbabYw;
	}
	public String getZlXbpgbabZjjgmc() {
		return zlXbpgbabZjjgmc;
	}
	public void setZlXbpgbabZjjgmc(String zlXbpgbabZjjgmc) {
		this.zlXbpgbabZjjgmc = zlXbpgbabZjjgmc;
	}
	public String getZlXbpgbabPgbgh() {
		return zlXbpgbabPgbgh;
	}
	public void setZlXbpgbabPgbgh(String zlXbpgbabPgbgh) {
		this.zlXbpgbabPgbgh = zlXbpgbabPgbgh;
	}
	public String getZlXbpgbabHzdwmc() {
		return zlXbpgbabHzdwmc;
	}
	public void setZlXbpgbabHzdwmc(String zlXbpgbabHzdwmc) {
		this.zlXbpgbabHzdwmc = zlXbpgbabHzdwmc;
	}
	public String getZlXbpgbabHzwjh() {
		return zlXbpgbabHzwjh;
	}
	public void setZlXbpgbabHzwjh(String zlXbpgbabHzwjh) {
		this.zlXbpgbabHzwjh = zlXbpgbabHzwjh;
	}
	public String getZlXbpgbabLy() {
		return zlXbpgbabLy;
	}
	public void setZlXbpgbabLy(String zlXbpgbabLy) {
		this.zlXbpgbabLy = zlXbpgbabLy;
	}
	public String getZlBxbpgbabYw() {
		return zlBxbpgbabYw;
	}
	public void setZlBxbpgbabYw(String zlBxbpgbabYw) {
		this.zlBxbpgbabYw = zlBxbpgbabYw;
	}
	public String getZlBxbpgbabZjjgmc() {
		return zlBxbpgbabZjjgmc;
	}
	public void setZlBxbpgbabZjjgmc(String zlBxbpgbabZjjgmc) {
		this.zlBxbpgbabZjjgmc = zlBxbpgbabZjjgmc;
	}
	public String getZlBxbpgbabPgbgh() {
		return zlBxbpgbabPgbgh;
	}
	public void setZlBxbpgbabPgbgh(String zlBxbpgbabPgbgh) {
		this.zlBxbpgbabPgbgh = zlBxbpgbabPgbgh;
	}
	public String getZlBxbpgbabHzdwmc() {
		return zlBxbpgbabHzdwmc;
	}
	public void setZlBxbpgbabHzdwmc(String zlBxbpgbabHzdwmc) {
		this.zlBxbpgbabHzdwmc = zlBxbpgbabHzdwmc;
	}
	public String getZlBxbpgbabHzwjh() {
		return zlBxbpgbabHzwjh;
	}
	public void setZlBxbpgbabHzwjh(String zlBxbpgbabHzwjh) {
		this.zlBxbpgbabHzwjh = zlBxbpgbabHzwjh;
	}
	public String getZlBxbpgbabLy() {
		return zlBxbpgbabLy;
	}
	public void setZlBxbpgbabLy(String zlBxbpgbabLy) {
		this.zlBxbpgbabLy = zlBxbpgbabLy;
	}
	public String getZlTjpgbabYw() {
		return zlTjpgbabYw;
	}
	public void setZlTjpgbabYw(String zlTjpgbabYw) {
		this.zlTjpgbabYw = zlTjpgbabYw;
	}
	public String getZlTjpgbabZjjgmc() {
		return zlTjpgbabZjjgmc;
	}
	public void setZlTjpgbabZjjgmc(String zlTjpgbabZjjgmc) {
		this.zlTjpgbabZjjgmc = zlTjpgbabZjjgmc;
	}
	public String getZlTjpgbabPgbgh() {
		return zlTjpgbabPgbgh;
	}
	public void setZlTjpgbabPgbgh(String zlTjpgbabPgbgh) {
		this.zlTjpgbabPgbgh = zlTjpgbabPgbgh;
	}
	public String getZlTjpgbabHzdwmc() {
		return zlTjpgbabHzdwmc;
	}
	public void setZlTjpgbabHzdwmc(String zlTjpgbabHzdwmc) {
		this.zlTjpgbabHzdwmc = zlTjpgbabHzdwmc;
	}
	public String getZlTjpgbabHzwjh() {
		return zlTjpgbabHzwjh;
	}
	public void setZlTjpgbabHzwjh(String zlTjpgbabHzwjh) {
		this.zlTjpgbabHzwjh = zlTjpgbabHzwjh;
	}
	public String getZlTjpgbabLy() {
		return zlTjpgbabLy;
	}
	public void setZlTjpgbabLy(String zlTjpgbabLy) {
		this.zlTjpgbabLy = zlTjpgbabLy;
	}
	public String getZlSyzcczxyYw() {
		return zlSyzcczxyYw;
	}
	public void setZlSyzcczxyYw(String zlSyzcczxyYw) {
		this.zlSyzcczxyYw = zlSyzcczxyYw;
	}
	public String getZlSyzcczxyLy() {
		return zlSyzcczxyLy;
	}
	public void setZlSyzcczxyLy(String zlSyzcczxyLy) {
		this.zlSyzcczxyLy = zlSyzcczxyLy;
	}
	public String getZlZgdbdhjyYw() {
		return zlZgdbdhjyYw;
	}
	public void setZlZgdbdhjyYw(String zlZgdbdhjyYw) {
		this.zlZgdbdhjyYw = zlZgdbdhjyYw;
	}
	public String getZlZgdbdhjyYj() {
		return zlZgdbdhjyYj;
	}
	public void setZlZgdbdhjyYj(String zlZgdbdhjyYj) {
		this.zlZgdbdhjyYj = zlZgdbdhjyYj;
	}
	public String getZlZgdbdhjyLy() {
		return zlZgdbdhjyLy;
	}
	public void setZlZgdbdhjyLy(String zlZgdbdhjyLy) {
		this.zlZgdbdhjyLy = zlZgdbdhjyLy;
	}
	public String getZlGqszfawjYw() {
		return zlGqszfawjYw;
	}
	public void setZlGqszfawjYw(String zlGqszfawjYw) {
		this.zlGqszfawjYw = zlGqszfawjYw;
	}
	public String getZlGqszfawjPzdw() {
		return zlGqszfawjPzdw;
	}
	public void setZlGqszfawjPzdw(String zlGqszfawjPzdw) {
		this.zlGqszfawjPzdw = zlGqszfawjPzdw;
	}
	public String getZlGqszfawjPzwh() {
		return zlGqszfawjPzwh;
	}
	public void setZlGqszfawjPzwh(String zlGqszfawjPzwh) {
		this.zlGqszfawjPzwh = zlGqszfawjPzwh;
	}
	public String getZlGqszfawjLy() {
		return zlGqszfawjLy;
	}
	public void setZlGqszfawjLy(String zlGqszfawjLy) {
		this.zlGqszfawjLy = zlGqszfawjLy;
	}
	public String getZlGdqkdjbYw() {
		return zlGdqkdjbYw;
	}
	public void setZlGdqkdjbYw(String zlGdqkdjbYw) {
		this.zlGdqkdjbYw = zlGdqkdjbYw;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
