package com.zjhc.gzwcq.ywzbb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/27:16:00:40
 **/
public class Ywzbb12VO implements Serializable {
    private static final long serialVersionUID = 18L;
    @ApiParam(value="主键")
    protected String id;// 主键
    @ApiParam(value="关联基本信息ID")
    protected String jbxxId;// 关联基本信息ID
    @ApiParam(value="单位ID")
    protected String unitid;// 单位ID
    @ApiParam(value="数据时期")
    protected String datatime;// 数据时期
    @ApiParam(value="浮动行顺序号")
    protected BigDecimal floatorder;// 浮动行顺序号
    @ApiParam(value="评估净资产值（万元）")
    protected BigDecimal xxPgjzcz;// 评估净资产值（万元）
    @ApiParam(value="审计净资产值(万元)")
    protected BigDecimal xxSjjzcz;// 审计净资产值(万元)
    @ApiParam(value="发行价格(万元)")
    protected BigDecimal xxFxjg;// 发行价格(万元)
    @ApiParam(value="经济行为决策文件_理由描述")
    protected String zlJcwjLy;// 经济行为决策文件_理由描述
    @ApiParam(value="验资报告_中介机构名称")
    protected String zlYzbgZjjgmc;// 验资报告_中介机构名称
    @ApiParam(value="验资报告_验资报告号")
    protected String zlYzbgYzbgh;// 验资报告_验资报告号
    @ApiParam(value="验资报告_理由描述")
    protected String zlYzbgLy;// 验资报告_理由描述
    @ApiParam(value="验资报告_报告出具日")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlYzbgYzcjr;// 验资报告_报告出具日
    @ApiParam(value="企业章程_理由描述")
    protected String zlQyzcLy;// 企业章程_理由描述
    @ApiParam(value="企业法人营业执照_理由描述")
    protected String zlYyzzLy;// 企业法人营业执照_理由描述
    @ApiParam(value="评估备案表_理由描述")
    protected String zlPgbaLy;// 评估备案表_理由描述
    @ApiParam(value="非货币评估备案表_理由描述")
    protected String zlFhbpgbaLy;// 非货币评估备案表_理由描述
    @ApiParam(value="标的评估备案表_理由描述")
    protected String zlBdpgbaLy;// 标的评估备案表_理由描述
    @ApiParam(value="国有土地管理部门备案_理由描述")
    protected String zlGytdbaLy;// 国有土地管理部门备案_理由描述
    @ApiParam(value="进场交易交割_交易机构")
    protected String zlJcjgJyjg;// 进场交易交割_交易机构
    @ApiParam(value="进场交易交割_交割单/鉴证书号")
    protected String zlJcjgJgd;// 进场交易交割_交割单/鉴证书号
    @ApiParam(value="进场交易交割_出具日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlJcjgCjrq;// 进场交易交割_出具日期
    @ApiParam(value="进场交易交割_理由描述")
    protected String zlJcjgLy;// 进场交易交割_理由描述
    @ApiParam(value="审计报告_中介机构")
    protected String zlSjbgZjjg;// 审计报告_中介机构
    @ApiParam(value="审计报告_报告号")
    protected String zlSjbgBgh;// 审计报告_报告号
    @ApiParam(value="审计报告_出具日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlSjbgCjrq;// 审计报告_出具日期
    @ApiParam(value="审计报告_理由描述")
    protected String zlSjbgLy;// 审计报告_理由描述
    @ApiParam(value="股权转让协议_理由描述")
    protected String zlGqzrLy;// 股权转让协议_理由描述
    @ApiParam(value="置换协议_理由描述")
    protected String zlZhxyLy;// 置换协议_理由描述
    @ApiParam(value="合并协议书_理由描述")
    protected String zlHbxysLy;// 合并协议书_理由描述
    @ApiParam(value="分立协议书_理由描述")
    protected String zlFlxysLy;// 分立协议书_理由描述
    @ApiParam(value="无偿划转协议_理由描述")
    protected String zlWchzxyLy;// 无偿划转协议_理由描述
    @ApiParam(value="基准日审计报告_中介机构名称")
    protected String zlJzrsjbgZjjgmc;// 基准日审计报告_中介机构名称
    @ApiParam(value="基准日审计报告_验资报告号")
    protected String zlJzrsjbgYzbgh;// 基准日审计报告_验资报告号
    @ApiParam(value="基准日审计报告_理由描述")
    protected String zlJzrsjbgLy;// 基准日审计报告_理由描述
    @ApiParam(value="减资公告_媒体名称")
    protected String zlJzggMtmc;// 减资公告_媒体名称
    @ApiParam(value="减资公告_公告日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlJzggGgrq;// 减资公告_公告日期
    @ApiParam(value="减资公告_理由描述")
    protected String zlJzggLy;// 减资公告_理由描述
    @ApiParam(value="承接方名称")
    protected String xxCjfmc;// 承接方名称
    @ApiParam(value="司法作价(万元)")
    protected BigDecimal xxSfzj;// 司法作价(万元)
    @ApiParam(value="最近一期审计报告_中介机构")
    protected String zlZjyqsjbgZjjg;// 最近一期审计报告_中介机构
    @ApiParam(value="最近一期审计报告_报告号")
    protected String zlZjyqsjbgBgh;// 最近一期审计报告_报告号
    @ApiParam(value="最近一期审计报告_理由描述")
    protected String zlZjyqsjbgLy;// 最近一期审计报告_理由描述
    @ApiParam(value="投资协议_理由描述")
    protected String zlTzxyLy;// 投资协议_理由描述
    @ApiParam(value="清算净资产值(万元)")
    protected BigDecimal xxQsjzcz;// 清算净资产值(万元)
    @ApiParam(value="清算费用（万元）")
    protected BigDecimal xxQsfy;// 清算费用（万元）
    @ApiParam(value="清偿债务（万元）")
    protected BigDecimal xxQczw;// 清偿债务（万元）
    @ApiParam(value="破产财产总额（万元）")
    protected BigDecimal xxPcccze;// 破产财产总额（万元）
    @ApiParam(value="破产费用和共益债务（万元）")
    protected BigDecimal xxPcfyhgyzw;// 破产费用和共益债务（万元）
    @ApiParam(value="普通债务（万元）")
    protected BigDecimal xxPtzw;// 普通债务（万元）
    @ApiParam(value="清算报告_中介机构名称")
    protected String zlQsbgZjjgmc;// 清算报告_中介机构名称
    @ApiParam(value="清算报告_报告号")
    protected String zlQsbgBgh;// 清算报告_报告号
    @ApiParam(value="清算报告_理由描述")
    protected String zlQsbgLy;// 清算报告_理由描述
    @ApiParam(value="注销公告_媒体名称")
    protected String zlZxggMtmc;// 注销公告_媒体名称
    @ApiParam(value="注销公告_公告日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlZxggGgrq;// 注销公告_公告日期
    @ApiParam(value="注销公告_理由描述")
    protected String zlZxggLy;// 注销公告_理由描述
    @ApiParam(value="工商注销证明_理由描述")
    protected String zlGszxzmLy;// 工商注销证明_理由描述
    @ApiParam(value="破产公告_媒体名称")
    protected String zlPcggMtmc;// 破产公告_媒体名称
    @ApiParam(value="破产公告_公告日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlPcggGgrq;// 破产公告_公告日期
    @ApiParam(value="破产公告_理由描述")
    protected String zlPcggLy;// 破产公告_理由描述
    @ApiParam(value="投资协议_有无")
    protected String zlTzxyYw;// 投资协议_有无
    @ApiParam(value="经济行为决策文件_有无")
    protected String zlJcwjYw;// 经济行为决策文件_有无
    @ApiParam(value="减资公告_有无")
    protected String zlJzggYw;// 减资公告_有无
    @ApiParam(value="破产公告_有无")
    protected String zlPcggYw;// 破产公告_有无
    @ApiParam(value="基准日审计报告_有无")
    protected String zlJzrsjbgYw;// 基准日审计报告_有无
    @ApiParam(value="企业章程_有无")
    protected String zlQyzcYw;// 企业章程_有无
    @ApiParam(value="进场交易交割_有无")
    protected String zlJcjgYw;// 进场交易交割_有无
    @ApiParam(value="审计报告_有无")
    protected String zlSjbgYw;// 审计报告_有无
    @ApiParam(value="非货币评估备案表_有无")
    protected String zlFhbpgbaYw;// 非货币评估备案表_有无
    @ApiParam(value="营业执照_有无")
    protected String zlYyzzYw;// 营业执照_有无
    @ApiParam(value="评估备案表_有无")
    protected String zlPgbaYw;// 评估备案表_有无
    @ApiParam(value="工商注销证明_有无")
    protected String zlGszxzmYw;// 工商注销证明_有无
    @ApiParam(value="国有土地管理部门备案_有无")
    protected String zlGytdbaYw;// 国有土地管理部门备案_有无
    @ApiParam(value="验资报告_有无")
    protected String zlYzbgYw;// 验资报告_有无
    @ApiParam(value="合并协议书_有无")
    protected String zlHbxysYw;// 合并协议书_有无
    @ApiParam(value="置换协议_有无")
    protected String zlZhxyYw;// 置换协议_有无
    @ApiParam(value="股权转让协议_有无")
    protected String zlGqzrYw;// 股权转让协议_有无
    @ApiParam(value="无偿划转协议_有无")
    protected String zlWchzxyYw;// 无偿划转协议_有无
    @ApiParam(value="标的评估备案表_有无")
    protected String zlBdpgbaYw;// 标的评估备案表_有无
    @ApiParam(value="最近一期审计报告_有无")
    protected String zlZjyqsjbgYw;// 最近一期审计报告_有无
    @ApiParam(value="分立协议书_有无")
    protected String zlFlxysYw;// 分立协议书_有无
    @ApiParam(value="注销公告_有无")
    protected String zlZxggYw;// 注销公告_有无
    @ApiParam(value="清算报告_有无")
    protected String zlQsbgYw;// 清算报告_有无
    @ApiParam(value="标的企业性质")
    protected String xxBdqyxz;// 标的企业性质
    @ApiParam(value="被吸并方性质")
    protected String xxBxbfxz;// 被吸并方性质
    @ApiParam(value="解算原因")
    protected String xxJsyy;// 解算原因
    @ApiParam(value="工商注销证明_注销日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    protected Date zlGszxzmZxrq;// 工商注销证明_注销日期
    @ApiParam(value="经济行为决策文件_单位名称")
    protected String zlJcwjDwmc;// 经济行为决策文件_单位名称
    @ApiParam(value="经济行为决策文件_文件名称")
    protected String zlJcwjWjmc;// 经济行为决策文件_文件名称
    @ApiParam(value="经济行为决策文件_文件号")
    protected String zlJcwjWjh;// 经济行为决策文件_文件号
    @ApiParam(value="评估备案表_中介机构名称")
    protected String zlPgbaZjjgmc;// 评估备案表_中介机构名称
    @ApiParam(value="评估备案表_评估报告号")
    protected String zlPgbaPgbgh;// 评估备案表_评估报告号
    @ApiParam(value="评估备案表_核准单位名称")
    protected String zlPgbaHzdwmc;// 评估备案表_核准单位名称
    @ApiParam(value="评估备案表_核准文件号")
    protected String zlPgbaHzwjh;// 评估备案表_核准文件号
    @ApiParam(value="非货币评估备案表_中介机构名称")
    protected String zlFhbpgbaZjjgmc;// 非货币评估备案表_中介机构名称
    @ApiParam(value="非货币评估备案表_评估报告号")
    protected String zlFhbpgbaPgbgh;// 非货币评估备案表_评估报告号
    @ApiParam(value="非货币评估备案表_核准单位名称")
    protected String zlFhbpgbaHzdwmc;// 非货币评估备案表_核准单位名称
    @ApiParam(value="非货币评估备案表_核准文件号")
    protected String zlFhbpgbaHzwjh;// 非货币评估备案表_核准文件号
    @ApiParam(value="标的评估备案表_中介机构名称")
    protected String zlBdpgbaZjjgmc;// 标的评估备案表_中介机构名称
    @ApiParam(value="标的评估备案表_评估报告号")
    protected String zlBdpgbaPgbgh;// 标的评估备案表_评估报告号
    @ApiParam(value="标的评估备案表_核准单位名称")
    protected String zlBdpgbaHzdwmc;// 标的评估备案表_核准单位名称
    @ApiParam(value="标的评估备案表_核准文件号")
    protected String zlBdpgbaHzwjh;// 标的评估备案表_核准文件号
    @ApiParam(value="国有土地管理部门备案_批准单位")
    protected String zlGytdbaPzdw;// 国有土地管理部门备案_批准单位
    @ApiParam(value="国有土地管理部门备案_批准文号")
    protected String zlGytdbaPzwh;// 国有土地管理部门备案_批准文号
    @ApiParam(value="业务办理申请文件")
    protected String zlYwblsqwj;// 业务办理申请文件
    @ApiParam(value="置换一评估备案表_有无")
    protected String zlZhypgbabYw;// 置换一评估备案表_有无
    @ApiParam(value="置换一评估备案表_中介机构名称")
    protected String zlZhypgbabZjjgmc;// 置换一评估备案表_中介机构名称
    @ApiParam(value="置换一评估备案表_评估报告号")
    protected String zlZhypgbabPgbgh;// 置换一评估备案表_评估报告号
    @ApiParam(value="置换一评估备案表_核准单位名称")
    protected String zlZhypgbabHzdwmc;// 置换一评估备案表_核准单位名称
    @ApiParam(value="置换一评估备案表_核准文件号")
    protected String zlZhypgbabHzwjh;// 置换一评估备案表_核准文件号
    @ApiParam(value="置换一评估备案表_理由描述")
    protected String zlZhypgbabLy;// 置换一评估备案表_理由描述
    @ApiParam(value="置换二评估备案表_有无")
    protected String zlZhepgbabYw;// 置换二评估备案表_有无
    @ApiParam(value="置换二评估备案表_中介机构名称")
    protected String zlZhepgbabZjjgmc;// 置换二评估备案表_中介机构名称
    @ApiParam(value="置换二评估备案表_评估报告号")
    protected String zlZhepgbabPgbgh;// 置换二评估备案表_评估报告号
    @ApiParam(value="置换二评估备案表_核准单位名称")
    protected String zlZhepgbabHzdwmc;// 置换二评估备案表_核准单位名称
    @ApiParam(value="置换二评估备案表_核准文件号")
    protected String zlZhepgbabHzwjh;// 置换二评估备案表_核准文件号
    @ApiParam(value="置换二评估备案表_理由描述")
    protected String zlZhepgbabLy;// 置换二评估备案表_理由描述
    @ApiParam(value="吸并评估备案表_有无")
    protected String zlXbpgbabYw;// 吸并评估备案表_有无
    @ApiParam(value="吸并评估备案表_中介机构名称")
    protected String zlXbpgbabZjjgmc;// 吸并评估备案表_中介机构名称
    @ApiParam(value="吸并评估备案表_评估报告号")
    protected String zlXbpgbabPgbgh;// 吸并评估备案表_评估报告号
    @ApiParam(value="吸并评估备案表_核准单位名称")
    protected String zlXbpgbabHzdwmc;// 吸并评估备案表_核准单位名称
    @ApiParam(value="吸并评估备案表_核准文件号")
    protected String zlXbpgbabHzwjh;// 吸并评估备案表_核准文件号
    @ApiParam(value="吸并评估备案表_理由描述")
    protected String zlXbpgbabLy;// 吸并评估备案表_理由描述
    @ApiParam(value="被吸并评估备案表_有无")
    protected String zlBxbpgbabYw;// 被吸并评估备案表_有无
    @ApiParam(value="被吸并评估备案表_中介机构名称")
    protected String zlBxbpgbabZjjgmc;// 被吸并评估备案表_中介机构名称
    @ApiParam(value="被吸并评估备案表_评估报告号")
    protected String zlBxbpgbabPgbgh;// 被吸并评估备案表_评估报告号
    @ApiParam(value="被吸并评估备案表_核准单位名称")
    protected String zlBxbpgbabHzdwmc;// 被吸并评估备案表_核准单位名称
    @ApiParam(value="被吸并评估备案表_核准文件号")
    protected String zlBxbpgbabHzwjh;// 被吸并评估备案表_核准文件号
    @ApiParam(value="被吸并评估备案表_理由描述")
    protected String zlBxbpgbabLy;// 被吸并评估备案表_理由描述
    @ApiParam(value="投资评估备案表_有无")
    protected String zlTjpgbabYw;// 投资评估备案表_有无
    @ApiParam(value="投资评估备案表_中介机构名称")
    protected String zlTjpgbabZjjgmc;// 投资评估备案表_中介机构名称
    @ApiParam(value="投资并评估备案表_评估报告号")
    protected String zlTjpgbabPgbgh;// 投资并评估备案表_评估报告号
    @ApiParam(value="投资评估备案表_核准单位名称")
    protected String zlTjpgbabHzdwmc;// 投资评估备案表_核准单位名称
    @ApiParam(value="投资评估备案表_核准文件号")
    protected String zlTjpgbabHzwjh;// 投资评估备案表_核准文件号
    @ApiParam(value="投资评估备案表_理由描述")
    protected String zlTjpgbabLy;// 投资评估备案表_理由描述
    @ApiParam(value="剩余资产处置协议_有无")
    protected String zlSyzcczxyYw;// 剩余资产处置协议_有无
    @ApiParam(value="剩余资产处置协议_理由描述")
    protected String zlSyzcczxyLy;// 剩余资产处置协议_理由描述
    @ApiParam(value="职工代表大会决议_有无")
    protected String zlZgdbdhjyYw;// 职工代表大会决议_有无
    @ApiParam(value="职工代表大会决议_意见")
    protected String zlZgdbdhjyYj;// 职工代表大会决议_意见
    @ApiParam(value="职工代表大会决议_理由描述")
    protected String zlZgdbdhjyLy;// 职工代表大会决议_理由描述
    @ApiParam(value="股权设置方案文件_有无")
    protected String zlGqszfawjYw;// 股权设置方案文件_有无
    @ApiParam(value="股权设置方案文件_批准单位")
    protected String zlGqszfawjPzdw;// 股权设置方案文件_批准单位
    @ApiParam(value="股权设置方案文件_批准文号")
    protected String zlGqszfawjPzwh;// 股权设置方案文件_批准文号
    @ApiParam(value="股权设置方案文件_理由描述")
    protected String zlGqszfawjLy;// 股权设置方案文件_理由描述
    @ApiParam(value="股东情况登记表_有无")
    protected String zlGdqkdjbYw;// 股东情况登记表_有无
    @ApiParam(value="股东情况登记表_理由描述")
    protected String zlGdqkdjbLy;// 股东情况登记表_理由描述
    @ApiParam(value="其他")
    protected String zlQt;// 其他
    @ApiParam(value="工商注销证明_工商部门名称")
    protected String zlGszxzmGsbmmc;// 工商注销证明_工商部门名称
    @ApiParam(value="有无非货币出资")
    protected String xxYwfhbcz;// 有无非货币出资
    @ApiParam(value="有无非货币支付收购价款")
    protected String xxYwfhbzfsgjk;// 有无非货币支付收购价款
    @ApiParam(value="标的企业评估净资产值(万元)")
    protected BigDecimal xxBdqypgjzcz;// 标的企业评估净资产值(万元)
    @ApiParam(value="备注")
    protected String xxBz;// 备注
    @ApiParam(value="标的企业审计净资产值(万元)")
    protected BigDecimal xxBdqysjjzcz;// 标的企业审计净资产值(万元)
    @ApiParam(value="置换方（一）企业名称")
    protected String xxZhyqymc;// 置换方（一）企业名称
    @ApiParam(value="置换方（一）所属国资监管机构")
    protected String xxZhyssgzjgjg;// 置换方（一）所属国资监管机构
    @ApiParam(value="置换方（一）所属国家出资企业")
    protected String xxZhyssgjczqy;// 置换方（一）所属国家出资企业
    @ApiParam(value="置换方（一）用于置换的资产评估值(万元)")
    protected BigDecimal xxZhyyyzhdzcpgz;// 置换方（一）用于置换的资产评估值(万元)
    @ApiParam(value="置换方（一）备注")
    protected String xxZhybz;// 置换方（一）备注
    @ApiParam(value="置换方（二）企业名称")
    protected String xxZheqymc;// 置换方（二）企业名称
    @ApiParam(value="置换方（二）所属国资监管机构")
    protected String xxZhessgzjgjg;// 置换方（二）所属国资监管机构
    @ApiParam(value="置换方（二）所属国家出资企业")
    protected String xxZhessgjczqy;// 置换方（二）所属国家出资企业
    @ApiParam(value="置换方（二）用于置换的资产评估值(万元)")
    protected BigDecimal xxZheyyzhdzcpgz;// 置换方（二）用于置换的资产评估值(万元)
    @ApiParam(value="置换方（二）备注")
    protected String xxZhebz;// 置换方（二）备注
    @ApiParam(value="安置费用总额（万元）")
    protected BigDecimal xxAzfyze;// 安置费用总额（万元）
    @ApiParam(value="核减（增）国有权益（万元）")
    protected BigDecimal xxGyqy;// 核减（增）国有权益（万元）
    @ApiParam(value="被吸并方名称")
    protected String xxBxbfmc;// 被吸并方名称
    @ApiParam(value="被吸并方评估净资产值(万元)")
    protected BigDecimal xxBxbfpgjzcz;// 被吸并方评估净资产值(万元)
    @ApiParam(value="吸并方评估净资产值（万元）")
    protected BigDecimal xxXbfpgjzcz;// 吸并方评估净资产值（万元）
    @ApiParam(value="被分立企业评估净资产值(万元)")
    protected BigDecimal xxBflqypgjzcz;// 被分立企业评估净资产值(万元)
    @ApiParam(value="存续企业评估净资产值（万元）")
    protected BigDecimal xxCxqypgjzcz;// 存续企业评估净资产值（万元）
    @ApiParam(value="存续企业折股标准（万元）")
    protected BigDecimal xxCxqyzgbz;// 存续企业折股标准（万元）
    @ApiParam(value="用于投资的股权评估值(万元)")
    protected BigDecimal xxYytzdgqpgz;// 用于投资的股权评估值(万元)
    @ApiParam(value="用于投资的股权作价（万元）")
    protected BigDecimal xxYytzdgqzj;// 用于投资的股权作价（万元）
    @ApiParam(value="剩余净资产处置收入（万元）")
    protected BigDecimal xxSyjzcczsr;// 剩余净资产处置收入（万元）
    @ApiParam(value="承接方所属国资监管机构")
    protected String xxCjfssgzjgjg;// 承接方所属国资监管机构
    @ApiParam(value="标的企业名称")
    protected String xxBdqymc;// 标的企业名称
    @ApiParam(value="标的企业所属国资监管机构")
    protected String xxBdqyssgzjgjg;// 标的企业所属国资监管机构
    @ApiParam(value="清算分配职工工资、社保和法定补偿（万元）")
    protected BigDecimal xxQsfpbc;// 清算分配职工工资、社保和法定补偿（万元）
    @ApiParam(value="清算分配所欠税款（万元）")
    protected BigDecimal xxQsfpsqsk;// 清算分配所欠税款（万元）
    @ApiParam(value="清算财产是否足以清偿债务")
    protected String xxQsccsfzyqczw;// 清算财产是否足以清偿债务
    @ApiParam(value="破产分配职工工资、社保和法定补偿（万元）")
    protected BigDecimal xxPcfpbc;// 破产分配职工工资、社保和法定补偿（万元）
    @ApiParam(value="破产分配所欠税款（万元）")
    protected BigDecimal xxPcfpsqsk;// 破产分配所欠税款（万元）
    @ApiParam(value="有无非货币减资")
    protected String xxYwfhbjz;// 有无非货币减资
    @ApiParam(value="出资作价（万元）合计")
    protected BigDecimal fd1Czzjhj;// 出资作价（万元）合计
    @ApiParam(value="评估值（万元）合计")
    protected BigDecimal fd1Pgzhj;// 评估值（万元）合计
    @ApiParam(value="支付作价（万元）合计")
    protected BigDecimal fd1Zfzjhj;// 支付作价（万元）合计
    @ApiParam(value="减资作价(万元)合计")
    protected BigDecimal fd1Jzzjhj;// 减资作价(万元)合计
    @ApiParam(value="收购价格(万元)合计")
    protected BigDecimal fd2Sgjghj;// 收购价格(万元)合计
    @ApiParam(value="收购股权的审计净资产值（万元）合计")
    protected BigDecimal fd2Sjjzczhj;// 收购股权的审计净资产值（万元）合计
    @ApiParam(value="收购股权的评估净资产值（万元）合计")
    protected BigDecimal fd2Pgjzczhj;// 收购股权的评估净资产值（万元）合计
    @ApiParam(value="转股数量合计")
    protected BigDecimal fd4Zgslhj;// 转股数量合计
    @ApiParam(value="司法作价（万元）合计")
    protected BigDecimal fd5Sfzjhj;// 司法作价（万元）合计
    @ApiParam(value="剩余财产分配结果（万元）合计")
    protected BigDecimal fd6Syccfpjghj;// 剩余财产分配结果（万元）合计
    @ApiParam(value="划转股权比例合计")
    protected BigDecimal fd7Hzgqblhj;// 划转股权比例合计
    @ApiParam(value="划转净资产值（万元）合计")
    protected BigDecimal fd7Hzjzczhj;// 划转净资产值（万元）合计
    @ApiParam(value="受让股权审计净资产值（万元）合计")
    protected BigDecimal fd8Srgqsjjzczhj;// 受让股权审计净资产值（万元）合计
    @ApiParam(value="受让股权评估净资产值（万元）合计")
    protected BigDecimal fd8Srgqpgjzczhj;// 受让股权评估净资产值（万元）合计
    @ApiParam(value="成交价（万元）合计")
    protected BigDecimal fd8Cjjhj;// 成交价（万元）合计
    @ApiParam(value="转让股权比例合计")
    protected BigDecimal fd8Zrgqblhj;// 转让股权比例合计
    @ApiParam(value="转让股权审计净资产值（万元）合计")
    protected BigDecimal fd8Zrgqsjjzczhj;// 转让股权审计净资产值（万元）合计
    @ApiParam(value="转让股权评估净资产值（万元）合计")
    protected BigDecimal fd8Zrgqpgjzczhj;// 转让股权评估净资产值（万元）合计
    @ApiParam(value="受让股权比例合计")
    protected BigDecimal fd8Srgqblhj;// 受让股权比例合计
    @ApiParam(value="收购股权比例合计")
    protected BigDecimal fd2Sggqblhj;// 收购股权比例合计
    @ApiParam(value="发行股数")
    protected BigDecimal xxFxgs;// 发行股数
    @ApiParam(value="其中：公开发行股数")
    protected BigDecimal xxGkfxgs;// 其中：公开发行股数
    @ApiParam(value="安置人员总数")
    protected BigDecimal xxAzryzs;// 安置人员总数
    @ApiParam(value="被吸并方净资产折合吸并方股权比例")
    protected BigDecimal xxBxbfjzczhxbfgqbl;// 被吸并方净资产折合吸并方股权比例
    @ApiParam(value="用于投资股权折合标的企业股权比例")
    protected BigDecimal xxYytzgqzhbdqygqbl;// 用于投资股权折合标的企业股权比例
    @ApiParam(value="附件")
    protected String zlFj;// 附件
    @ApiParam(value="标的企业评估净资产值(万元)_被增资")
    protected BigDecimal xxBdqypgjzczBzz;// 标的企业评估净资产值(万元)_被增资
    @ApiParam(value="标的企业评估净资产值(万元)_被减资")
    protected BigDecimal xxBdqypgjzczBjz;// 标的企业评估净资产值(万元)_被减资
    @ApiParam(value="标的企业评估净资产值(万元)_被改制")
    protected BigDecimal xxBdqypgjzczBgz;// 标的企业评估净资产值(万元)_被改制
    @ApiParam(value="标的企业审计净资产值(万元)_被改制")
    protected BigDecimal xxBdqysjjzczBgz;// 标的企业审计净资产值(万元)_被改制
    @ApiParam(value="折股标准(元/股)_新增股本")
    protected BigDecimal xxZgbzXzgb;// 折股标准(元/股)_新增股本
    @ApiParam(value="折股标准(元/股)_减少")
    protected BigDecimal xxZgbzJs;// 折股标准(元/股)_减少
    @ApiParam(value="折股标准(元/股)_投资新增")
    protected BigDecimal xxZgbzTzxz;// 折股标准(元/股)_投资新增
    @ApiParam(value="折股标准(元/股)_吸收合并")
    protected BigDecimal xxZgbzXshb;// 折股标准(元/股)_吸收合并
    @ApiParam(value="折股标准(元/股)_股权出资")
    protected BigDecimal xxZgbzGqcz;// 折股标准(元/股)_股权出资
    @ApiParam(value="企业名称预先核准通知书_有无")
    protected String zlYxhztzsYw;// 企业名称预先核准通知书_有无
    @ApiParam(value="企业名称预先核准通知书_理由描述")
    protected String zlYxhztzsLy;// 企业名称预先核准通知书_理由描述
    @ApiParam(value="企业名称预先核准通知书_核准单位")
    protected String zlYxhztzsHzdw;// 企业名称预先核准通知书_核准单位
    @ApiParam(value="提示性公告日前30个交易日的每日加权平均价格的算术平均值")
    protected BigDecimal jc30sspj;// 提示性公告日前30个交易日的每日加权平均价格的算术平均值
    @ApiParam(value="最近一个会计年度上市公司经审计的每股净资产值")
    protected BigDecimal jcMgjzcz;// 最近一个会计年度上市公司经审计的每股净资产值
    @ApiParam(value="减持股数")
    protected BigDecimal jcJcgs;// 减持股数
    @ApiParam(value="减持均价")
    protected BigDecimal jcJcjj;// 减持均价
    @ApiParam(value="清算报告")
    protected String zlQsbg;// 清算报告
    @ApiParam(value="分立协议书")
    protected String zlFlxys;// 分立协议书
    @ApiParam(value="工商注销证明")
    protected String zlGszxzm;// 工商注销证明
    @ApiParam(value="职工代表大会决议")
    protected String zlZgdbdhjy;// 职工代表大会决议
    @ApiParam(value="股权设置方案文件")
    protected String zlGqszfawj;// 股权设置方案文件
    @ApiParam(value="注销公告")
    protected String zlZxgg;// 注销公告
    @ApiParam(value="合并协议书")
    protected String zlHbxys;// 合并协议书
    @ApiParam(value="基准日审计报告")
    protected String zlJzrsjbg;// 基准日审计报告
    @ApiParam(value="置换协议")
    protected String zlZhxy;// 置换协议
    @ApiParam(value="非货币评估备案表")
    protected String zlFhbpgba;// 非货币评估备案表
    @ApiParam(value="最近一期审计报告")
    protected String zlZjyqsjbg;// 最近一期审计报告
    @ApiParam(value="评估备案表")
    protected String zlPgba;// 评估备案表
    @ApiParam(value="标的评估备案表")
    protected String zlBdpgba;// 标的评估备案表
    @ApiParam(value="无偿划转协议")
    protected String zlWchzxy;// 无偿划转协议
    @ApiParam(value="减资公告")
    protected String zlJzgg;// 减资公告
    @ApiParam(value="营业执照")
    protected String zlYyzz;// 营业执照
    @ApiParam(value="审计报告")
    protected String zlSjbg;// 审计报告
    @ApiParam(value="验资报告")
    protected String zlYzbg;// 验资报告
    @ApiParam(value="企业章程")
    protected String zlQyzc;// 企业章程
    @ApiParam(value="股东情况登记表")
    protected String zlGdqkdjb;// 股东情况登记表
    @ApiParam(value="置换一评估备案表")
    protected String zlZhypgbab;// 置换一评估备案表
    @ApiParam(value="剩余资产处置协议")
    protected String zlSyzcczxy;// 剩余资产处置协议
    @ApiParam(value="投资评估备案表")
    protected String zlTjpgbab;// 投资评估备案表
    @ApiParam(value="股权转让协议")
    protected String zlGqzr;// 股权转让协议
    @ApiParam(value="置换二评估备案表")
    protected String zlZhepgbab;// 置换二评估备案表
    @ApiParam(value="吸并评估备案表")
    protected String zlXbpgbab;// 吸并评估备案表
    @ApiParam(value="投资协议")
    protected String zlTzxy;// 投资协议
    @ApiParam(value="被吸并评估备案表")
    protected String zlBxbpgbab;// 被吸并评估备案表
    @ApiParam(value="国有土地管理部门备案")
    protected String zlGytdba;// 国有土地管理部门备案
    @ApiParam(value="进场交易交割")
    protected String zlJcjg;// 进场交易交割
    @ApiParam(value="经济行为决策文件")
    protected String zlJcwj;// 经济行为决策文件
    @ApiParam(value="破产公告")
    protected String zlPcgg;// 破产公告
    @ApiParam(value="预先核准通知书")
    protected String zlYxhztzs;// 预先核准通知书

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getJbxxId() {
        return jbxxId;
    }

    public void setJbxxId(String jbxxId) {
        this.jbxxId = jbxxId;
    }

    public String getUnitid() {
        return unitid;
    }

    public void setUnitid(String unitid) {
        this.unitid = unitid;
    }

    public String getDatatime() {
        return datatime;
    }

    public void setDatatime(String datatime) {
        this.datatime = datatime;
    }

    public BigDecimal getFloatorder() {
        return floatorder;
    }

    public void setFloatorder(BigDecimal floatorder) {
        this.floatorder = floatorder;
    }

    public BigDecimal getXxPgjzcz() {
        return xxPgjzcz;
    }

    public void setXxPgjzcz(BigDecimal xxPgjzcz) {
        this.xxPgjzcz = xxPgjzcz;
    }

    public BigDecimal getXxSjjzcz() {
        return xxSjjzcz;
    }

    public void setXxSjjzcz(BigDecimal xxSjjzcz) {
        this.xxSjjzcz = xxSjjzcz;
    }

    public BigDecimal getXxFxjg() {
        return xxFxjg;
    }

    public void setXxFxjg(BigDecimal xxFxjg) {
        this.xxFxjg = xxFxjg;
    }

    public String getZlJcwjLy() {
        return zlJcwjLy;
    }

    public void setZlJcwjLy(String zlJcwjLy) {
        this.zlJcwjLy = zlJcwjLy;
    }

    public String getZlYzbgZjjgmc() {
        return zlYzbgZjjgmc;
    }

    public void setZlYzbgZjjgmc(String zlYzbgZjjgmc) {
        this.zlYzbgZjjgmc = zlYzbgZjjgmc;
    }

    public String getZlYzbgYzbgh() {
        return zlYzbgYzbgh;
    }

    public void setZlYzbgYzbgh(String zlYzbgYzbgh) {
        this.zlYzbgYzbgh = zlYzbgYzbgh;
    }

    public String getZlYzbgLy() {
        return zlYzbgLy;
    }

    public void setZlYzbgLy(String zlYzbgLy) {
        this.zlYzbgLy = zlYzbgLy;
    }

    public Date getZlYzbgYzcjr() {
        return zlYzbgYzcjr;
    }

    public void setZlYzbgYzcjr(Date zlYzbgYzcjr) {
        this.zlYzbgYzcjr = zlYzbgYzcjr;
    }

    public String getZlQyzcLy() {
        return zlQyzcLy;
    }

    public void setZlQyzcLy(String zlQyzcLy) {
        this.zlQyzcLy = zlQyzcLy;
    }

    public String getZlYyzzLy() {
        return zlYyzzLy;
    }

    public void setZlYyzzLy(String zlYyzzLy) {
        this.zlYyzzLy = zlYyzzLy;
    }

    public String getZlPgbaLy() {
        return zlPgbaLy;
    }

    public void setZlPgbaLy(String zlPgbaLy) {
        this.zlPgbaLy = zlPgbaLy;
    }

    public String getZlFhbpgbaLy() {
        return zlFhbpgbaLy;
    }

    public void setZlFhbpgbaLy(String zlFhbpgbaLy) {
        this.zlFhbpgbaLy = zlFhbpgbaLy;
    }

    public String getZlBdpgbaLy() {
        return zlBdpgbaLy;
    }

    public void setZlBdpgbaLy(String zlBdpgbaLy) {
        this.zlBdpgbaLy = zlBdpgbaLy;
    }

    public String getZlGytdbaLy() {
        return zlGytdbaLy;
    }

    public void setZlGytdbaLy(String zlGytdbaLy) {
        this.zlGytdbaLy = zlGytdbaLy;
    }

    public String getZlJcjgJyjg() {
        return zlJcjgJyjg;
    }

    public void setZlJcjgJyjg(String zlJcjgJyjg) {
        this.zlJcjgJyjg = zlJcjgJyjg;
    }

    public String getZlJcjgJgd() {
        return zlJcjgJgd;
    }

    public void setZlJcjgJgd(String zlJcjgJgd) {
        this.zlJcjgJgd = zlJcjgJgd;
    }

    public Date getZlJcjgCjrq() {
        return zlJcjgCjrq;
    }

    public void setZlJcjgCjrq(Date zlJcjgCjrq) {
        this.zlJcjgCjrq = zlJcjgCjrq;
    }

    public String getZlJcjgLy() {
        return zlJcjgLy;
    }

    public void setZlJcjgLy(String zlJcjgLy) {
        this.zlJcjgLy = zlJcjgLy;
    }

    public String getZlSjbgZjjg() {
        return zlSjbgZjjg;
    }

    public void setZlSjbgZjjg(String zlSjbgZjjg) {
        this.zlSjbgZjjg = zlSjbgZjjg;
    }

    public String getZlSjbgBgh() {
        return zlSjbgBgh;
    }

    public void setZlSjbgBgh(String zlSjbgBgh) {
        this.zlSjbgBgh = zlSjbgBgh;
    }

    public Date getZlSjbgCjrq() {
        return zlSjbgCjrq;
    }

    public void setZlSjbgCjrq(Date zlSjbgCjrq) {
        this.zlSjbgCjrq = zlSjbgCjrq;
    }

    public String getZlSjbgLy() {
        return zlSjbgLy;
    }

    public void setZlSjbgLy(String zlSjbgLy) {
        this.zlSjbgLy = zlSjbgLy;
    }

    public String getZlGqzrLy() {
        return zlGqzrLy;
    }

    public void setZlGqzrLy(String zlGqzrLy) {
        this.zlGqzrLy = zlGqzrLy;
    }

    public String getZlZhxyLy() {
        return zlZhxyLy;
    }

    public void setZlZhxyLy(String zlZhxyLy) {
        this.zlZhxyLy = zlZhxyLy;
    }

    public String getZlHbxysLy() {
        return zlHbxysLy;
    }

    public void setZlHbxysLy(String zlHbxysLy) {
        this.zlHbxysLy = zlHbxysLy;
    }

    public String getZlFlxysLy() {
        return zlFlxysLy;
    }

    public void setZlFlxysLy(String zlFlxysLy) {
        this.zlFlxysLy = zlFlxysLy;
    }

    public String getZlWchzxyLy() {
        return zlWchzxyLy;
    }

    public void setZlWchzxyLy(String zlWchzxyLy) {
        this.zlWchzxyLy = zlWchzxyLy;
    }

    public String getZlJzrsjbgZjjgmc() {
        return zlJzrsjbgZjjgmc;
    }

    public void setZlJzrsjbgZjjgmc(String zlJzrsjbgZjjgmc) {
        this.zlJzrsjbgZjjgmc = zlJzrsjbgZjjgmc;
    }

    public String getZlJzrsjbgYzbgh() {
        return zlJzrsjbgYzbgh;
    }

    public void setZlJzrsjbgYzbgh(String zlJzrsjbgYzbgh) {
        this.zlJzrsjbgYzbgh = zlJzrsjbgYzbgh;
    }

    public String getZlJzrsjbgLy() {
        return zlJzrsjbgLy;
    }

    public void setZlJzrsjbgLy(String zlJzrsjbgLy) {
        this.zlJzrsjbgLy = zlJzrsjbgLy;
    }

    public String getZlJzggMtmc() {
        return zlJzggMtmc;
    }

    public void setZlJzggMtmc(String zlJzggMtmc) {
        this.zlJzggMtmc = zlJzggMtmc;
    }

    public Date getZlJzggGgrq() {
        return zlJzggGgrq;
    }

    public void setZlJzggGgrq(Date zlJzggGgrq) {
        this.zlJzggGgrq = zlJzggGgrq;
    }

    public String getZlJzggLy() {
        return zlJzggLy;
    }

    public void setZlJzggLy(String zlJzggLy) {
        this.zlJzggLy = zlJzggLy;
    }

    public String getXxCjfmc() {
        return xxCjfmc;
    }

    public void setXxCjfmc(String xxCjfmc) {
        this.xxCjfmc = xxCjfmc;
    }

    public BigDecimal getXxSfzj() {
        return xxSfzj;
    }

    public void setXxSfzj(BigDecimal xxSfzj) {
        this.xxSfzj = xxSfzj;
    }

    public String getZlZjyqsjbgZjjg() {
        return zlZjyqsjbgZjjg;
    }

    public void setZlZjyqsjbgZjjg(String zlZjyqsjbgZjjg) {
        this.zlZjyqsjbgZjjg = zlZjyqsjbgZjjg;
    }

    public String getZlZjyqsjbgBgh() {
        return zlZjyqsjbgBgh;
    }

    public void setZlZjyqsjbgBgh(String zlZjyqsjbgBgh) {
        this.zlZjyqsjbgBgh = zlZjyqsjbgBgh;
    }

    public String getZlZjyqsjbgLy() {
        return zlZjyqsjbgLy;
    }

    public void setZlZjyqsjbgLy(String zlZjyqsjbgLy) {
        this.zlZjyqsjbgLy = zlZjyqsjbgLy;
    }

    public String getZlTzxyLy() {
        return zlTzxyLy;
    }

    public void setZlTzxyLy(String zlTzxyLy) {
        this.zlTzxyLy = zlTzxyLy;
    }

    public BigDecimal getXxQsjzcz() {
        return xxQsjzcz;
    }

    public void setXxQsjzcz(BigDecimal xxQsjzcz) {
        this.xxQsjzcz = xxQsjzcz;
    }

    public BigDecimal getXxQsfy() {
        return xxQsfy;
    }

    public void setXxQsfy(BigDecimal xxQsfy) {
        this.xxQsfy = xxQsfy;
    }

    public BigDecimal getXxQczw() {
        return xxQczw;
    }

    public void setXxQczw(BigDecimal xxQczw) {
        this.xxQczw = xxQczw;
    }

    public BigDecimal getXxPcccze() {
        return xxPcccze;
    }

    public void setXxPcccze(BigDecimal xxPcccze) {
        this.xxPcccze = xxPcccze;
    }

    public BigDecimal getXxPcfyhgyzw() {
        return xxPcfyhgyzw;
    }

    public void setXxPcfyhgyzw(BigDecimal xxPcfyhgyzw) {
        this.xxPcfyhgyzw = xxPcfyhgyzw;
    }

    public BigDecimal getXxPtzw() {
        return xxPtzw;
    }

    public void setXxPtzw(BigDecimal xxPtzw) {
        this.xxPtzw = xxPtzw;
    }

    public String getZlQsbgZjjgmc() {
        return zlQsbgZjjgmc;
    }

    public void setZlQsbgZjjgmc(String zlQsbgZjjgmc) {
        this.zlQsbgZjjgmc = zlQsbgZjjgmc;
    }

    public String getZlQsbgBgh() {
        return zlQsbgBgh;
    }

    public void setZlQsbgBgh(String zlQsbgBgh) {
        this.zlQsbgBgh = zlQsbgBgh;
    }

    public String getZlQsbgLy() {
        return zlQsbgLy;
    }

    public void setZlQsbgLy(String zlQsbgLy) {
        this.zlQsbgLy = zlQsbgLy;
    }

    public String getZlZxggMtmc() {
        return zlZxggMtmc;
    }

    public void setZlZxggMtmc(String zlZxggMtmc) {
        this.zlZxggMtmc = zlZxggMtmc;
    }

    public Date getZlZxggGgrq() {
        return zlZxggGgrq;
    }

    public void setZlZxggGgrq(Date zlZxggGgrq) {
        this.zlZxggGgrq = zlZxggGgrq;
    }

    public String getZlZxggLy() {
        return zlZxggLy;
    }

    public void setZlZxggLy(String zlZxggLy) {
        this.zlZxggLy = zlZxggLy;
    }

    public String getZlGszxzmLy() {
        return zlGszxzmLy;
    }

    public void setZlGszxzmLy(String zlGszxzmLy) {
        this.zlGszxzmLy = zlGszxzmLy;
    }

    public String getZlPcggMtmc() {
        return zlPcggMtmc;
    }

    public void setZlPcggMtmc(String zlPcggMtmc) {
        this.zlPcggMtmc = zlPcggMtmc;
    }

    public Date getZlPcggGgrq() {
        return zlPcggGgrq;
    }

    public void setZlPcggGgrq(Date zlPcggGgrq) {
        this.zlPcggGgrq = zlPcggGgrq;
    }

    public String getZlPcggLy() {
        return zlPcggLy;
    }

    public void setZlPcggLy(String zlPcggLy) {
        this.zlPcggLy = zlPcggLy;
    }

    public String getZlTzxyYw() {
        return zlTzxyYw;
    }

    public void setZlTzxyYw(String zlTzxyYw) {
        this.zlTzxyYw = zlTzxyYw;
    }

    public String getZlJcwjYw() {
        return zlJcwjYw;
    }

    public void setZlJcwjYw(String zlJcwjYw) {
        this.zlJcwjYw = zlJcwjYw;
    }

    public String getZlJzggYw() {
        return zlJzggYw;
    }

    public void setZlJzggYw(String zlJzggYw) {
        this.zlJzggYw = zlJzggYw;
    }

    public String getZlPcggYw() {
        return zlPcggYw;
    }

    public void setZlPcggYw(String zlPcggYw) {
        this.zlPcggYw = zlPcggYw;
    }

    public String getZlJzrsjbgYw() {
        return zlJzrsjbgYw;
    }

    public void setZlJzrsjbgYw(String zlJzrsjbgYw) {
        this.zlJzrsjbgYw = zlJzrsjbgYw;
    }

    public String getZlQyzcYw() {
        return zlQyzcYw;
    }

    public void setZlQyzcYw(String zlQyzcYw) {
        this.zlQyzcYw = zlQyzcYw;
    }

    public String getZlJcjgYw() {
        return zlJcjgYw;
    }

    public void setZlJcjgYw(String zlJcjgYw) {
        this.zlJcjgYw = zlJcjgYw;
    }

    public String getZlSjbgYw() {
        return zlSjbgYw;
    }

    public void setZlSjbgYw(String zlSjbgYw) {
        this.zlSjbgYw = zlSjbgYw;
    }

    public String getZlFhbpgbaYw() {
        return zlFhbpgbaYw;
    }

    public void setZlFhbpgbaYw(String zlFhbpgbaYw) {
        this.zlFhbpgbaYw = zlFhbpgbaYw;
    }

    public String getZlYyzzYw() {
        return zlYyzzYw;
    }

    public void setZlYyzzYw(String zlYyzzYw) {
        this.zlYyzzYw = zlYyzzYw;
    }

    public String getZlPgbaYw() {
        return zlPgbaYw;
    }

    public void setZlPgbaYw(String zlPgbaYw) {
        this.zlPgbaYw = zlPgbaYw;
    }

    public String getZlGszxzmYw() {
        return zlGszxzmYw;
    }

    public void setZlGszxzmYw(String zlGszxzmYw) {
        this.zlGszxzmYw = zlGszxzmYw;
    }

    public String getZlGytdbaYw() {
        return zlGytdbaYw;
    }

    public void setZlGytdbaYw(String zlGytdbaYw) {
        this.zlGytdbaYw = zlGytdbaYw;
    }

    public String getZlYzbgYw() {
        return zlYzbgYw;
    }

    public void setZlYzbgYw(String zlYzbgYw) {
        this.zlYzbgYw = zlYzbgYw;
    }

    public String getZlHbxysYw() {
        return zlHbxysYw;
    }

    public void setZlHbxysYw(String zlHbxysYw) {
        this.zlHbxysYw = zlHbxysYw;
    }

    public String getZlZhxyYw() {
        return zlZhxyYw;
    }

    public void setZlZhxyYw(String zlZhxyYw) {
        this.zlZhxyYw = zlZhxyYw;
    }

    public String getZlGqzrYw() {
        return zlGqzrYw;
    }

    public void setZlGqzrYw(String zlGqzrYw) {
        this.zlGqzrYw = zlGqzrYw;
    }

    public String getZlWchzxyYw() {
        return zlWchzxyYw;
    }

    public void setZlWchzxyYw(String zlWchzxyYw) {
        this.zlWchzxyYw = zlWchzxyYw;
    }

    public String getZlBdpgbaYw() {
        return zlBdpgbaYw;
    }

    public void setZlBdpgbaYw(String zlBdpgbaYw) {
        this.zlBdpgbaYw = zlBdpgbaYw;
    }

    public String getZlZjyqsjbgYw() {
        return zlZjyqsjbgYw;
    }

    public void setZlZjyqsjbgYw(String zlZjyqsjbgYw) {
        this.zlZjyqsjbgYw = zlZjyqsjbgYw;
    }

    public String getZlFlxysYw() {
        return zlFlxysYw;
    }

    public void setZlFlxysYw(String zlFlxysYw) {
        this.zlFlxysYw = zlFlxysYw;
    }

    public String getZlZxggYw() {
        return zlZxggYw;
    }

    public void setZlZxggYw(String zlZxggYw) {
        this.zlZxggYw = zlZxggYw;
    }

    public String getZlQsbgYw() {
        return zlQsbgYw;
    }

    public void setZlQsbgYw(String zlQsbgYw) {
        this.zlQsbgYw = zlQsbgYw;
    }

    public String getXxBdqyxz() {
        return xxBdqyxz;
    }

    public void setXxBdqyxz(String xxBdqyxz) {
        this.xxBdqyxz = xxBdqyxz;
    }

    public String getXxBxbfxz() {
        return xxBxbfxz;
    }

    public void setXxBxbfxz(String xxBxbfxz) {
        this.xxBxbfxz = xxBxbfxz;
    }

    public String getXxJsyy() {
        return xxJsyy;
    }

    public void setXxJsyy(String xxJsyy) {
        this.xxJsyy = xxJsyy;
    }

    public Date getZlGszxzmZxrq() {
        return zlGszxzmZxrq;
    }

    public void setZlGszxzmZxrq(Date zlGszxzmZxrq) {
        this.zlGszxzmZxrq = zlGszxzmZxrq;
    }

    public String getZlJcwjDwmc() {
        return zlJcwjDwmc;
    }

    public void setZlJcwjDwmc(String zlJcwjDwmc) {
        this.zlJcwjDwmc = zlJcwjDwmc;
    }

    public String getZlJcwjWjmc() {
        return zlJcwjWjmc;
    }

    public void setZlJcwjWjmc(String zlJcwjWjmc) {
        this.zlJcwjWjmc = zlJcwjWjmc;
    }

    public String getZlJcwjWjh() {
        return zlJcwjWjh;
    }

    public void setZlJcwjWjh(String zlJcwjWjh) {
        this.zlJcwjWjh = zlJcwjWjh;
    }

    public String getZlPgbaZjjgmc() {
        return zlPgbaZjjgmc;
    }

    public void setZlPgbaZjjgmc(String zlPgbaZjjgmc) {
        this.zlPgbaZjjgmc = zlPgbaZjjgmc;
    }

    public String getZlPgbaPgbgh() {
        return zlPgbaPgbgh;
    }

    public void setZlPgbaPgbgh(String zlPgbaPgbgh) {
        this.zlPgbaPgbgh = zlPgbaPgbgh;
    }

    public String getZlPgbaHzdwmc() {
        return zlPgbaHzdwmc;
    }

    public void setZlPgbaHzdwmc(String zlPgbaHzdwmc) {
        this.zlPgbaHzdwmc = zlPgbaHzdwmc;
    }

    public String getZlPgbaHzwjh() {
        return zlPgbaHzwjh;
    }

    public void setZlPgbaHzwjh(String zlPgbaHzwjh) {
        this.zlPgbaHzwjh = zlPgbaHzwjh;
    }

    public String getZlFhbpgbaZjjgmc() {
        return zlFhbpgbaZjjgmc;
    }

    public void setZlFhbpgbaZjjgmc(String zlFhbpgbaZjjgmc) {
        this.zlFhbpgbaZjjgmc = zlFhbpgbaZjjgmc;
    }

    public String getZlFhbpgbaPgbgh() {
        return zlFhbpgbaPgbgh;
    }

    public void setZlFhbpgbaPgbgh(String zlFhbpgbaPgbgh) {
        this.zlFhbpgbaPgbgh = zlFhbpgbaPgbgh;
    }

    public String getZlFhbpgbaHzdwmc() {
        return zlFhbpgbaHzdwmc;
    }

    public void setZlFhbpgbaHzdwmc(String zlFhbpgbaHzdwmc) {
        this.zlFhbpgbaHzdwmc = zlFhbpgbaHzdwmc;
    }

    public String getZlFhbpgbaHzwjh() {
        return zlFhbpgbaHzwjh;
    }

    public void setZlFhbpgbaHzwjh(String zlFhbpgbaHzwjh) {
        this.zlFhbpgbaHzwjh = zlFhbpgbaHzwjh;
    }

    public String getZlBdpgbaZjjgmc() {
        return zlBdpgbaZjjgmc;
    }

    public void setZlBdpgbaZjjgmc(String zlBdpgbaZjjgmc) {
        this.zlBdpgbaZjjgmc = zlBdpgbaZjjgmc;
    }

    public String getZlBdpgbaPgbgh() {
        return zlBdpgbaPgbgh;
    }

    public void setZlBdpgbaPgbgh(String zlBdpgbaPgbgh) {
        this.zlBdpgbaPgbgh = zlBdpgbaPgbgh;
    }

    public String getZlBdpgbaHzdwmc() {
        return zlBdpgbaHzdwmc;
    }

    public void setZlBdpgbaHzdwmc(String zlBdpgbaHzdwmc) {
        this.zlBdpgbaHzdwmc = zlBdpgbaHzdwmc;
    }

    public String getZlBdpgbaHzwjh() {
        return zlBdpgbaHzwjh;
    }

    public void setZlBdpgbaHzwjh(String zlBdpgbaHzwjh) {
        this.zlBdpgbaHzwjh = zlBdpgbaHzwjh;
    }

    public String getZlGytdbaPzdw() {
        return zlGytdbaPzdw;
    }

    public void setZlGytdbaPzdw(String zlGytdbaPzdw) {
        this.zlGytdbaPzdw = zlGytdbaPzdw;
    }

    public String getZlGytdbaPzwh() {
        return zlGytdbaPzwh;
    }

    public void setZlGytdbaPzwh(String zlGytdbaPzwh) {
        this.zlGytdbaPzwh = zlGytdbaPzwh;
    }

    public String getZlYwblsqwj() {
        return zlYwblsqwj;
    }

    public void setZlYwblsqwj(String zlYwblsqwj) {
        this.zlYwblsqwj = zlYwblsqwj;
    }

    public String getZlZhypgbabYw() {
        return zlZhypgbabYw;
    }

    public void setZlZhypgbabYw(String zlZhypgbabYw) {
        this.zlZhypgbabYw = zlZhypgbabYw;
    }

    public String getZlZhypgbabZjjgmc() {
        return zlZhypgbabZjjgmc;
    }

    public void setZlZhypgbabZjjgmc(String zlZhypgbabZjjgmc) {
        this.zlZhypgbabZjjgmc = zlZhypgbabZjjgmc;
    }

    public String getZlZhypgbabPgbgh() {
        return zlZhypgbabPgbgh;
    }

    public void setZlZhypgbabPgbgh(String zlZhypgbabPgbgh) {
        this.zlZhypgbabPgbgh = zlZhypgbabPgbgh;
    }

    public String getZlZhypgbabHzdwmc() {
        return zlZhypgbabHzdwmc;
    }

    public void setZlZhypgbabHzdwmc(String zlZhypgbabHzdwmc) {
        this.zlZhypgbabHzdwmc = zlZhypgbabHzdwmc;
    }

    public String getZlZhypgbabHzwjh() {
        return zlZhypgbabHzwjh;
    }

    public void setZlZhypgbabHzwjh(String zlZhypgbabHzwjh) {
        this.zlZhypgbabHzwjh = zlZhypgbabHzwjh;
    }

    public String getZlZhypgbabLy() {
        return zlZhypgbabLy;
    }

    public void setZlZhypgbabLy(String zlZhypgbabLy) {
        this.zlZhypgbabLy = zlZhypgbabLy;
    }

    public String getZlZhepgbabYw() {
        return zlZhepgbabYw;
    }

    public void setZlZhepgbabYw(String zlZhepgbabYw) {
        this.zlZhepgbabYw = zlZhepgbabYw;
    }

    public String getZlZhepgbabZjjgmc() {
        return zlZhepgbabZjjgmc;
    }

    public void setZlZhepgbabZjjgmc(String zlZhepgbabZjjgmc) {
        this.zlZhepgbabZjjgmc = zlZhepgbabZjjgmc;
    }

    public String getZlZhepgbabPgbgh() {
        return zlZhepgbabPgbgh;
    }

    public void setZlZhepgbabPgbgh(String zlZhepgbabPgbgh) {
        this.zlZhepgbabPgbgh = zlZhepgbabPgbgh;
    }

    public String getZlZhepgbabHzdwmc() {
        return zlZhepgbabHzdwmc;
    }

    public void setZlZhepgbabHzdwmc(String zlZhepgbabHzdwmc) {
        this.zlZhepgbabHzdwmc = zlZhepgbabHzdwmc;
    }

    public String getZlZhepgbabHzwjh() {
        return zlZhepgbabHzwjh;
    }

    public void setZlZhepgbabHzwjh(String zlZhepgbabHzwjh) {
        this.zlZhepgbabHzwjh = zlZhepgbabHzwjh;
    }

    public String getZlZhepgbabLy() {
        return zlZhepgbabLy;
    }

    public void setZlZhepgbabLy(String zlZhepgbabLy) {
        this.zlZhepgbabLy = zlZhepgbabLy;
    }

    public String getZlXbpgbabYw() {
        return zlXbpgbabYw;
    }

    public void setZlXbpgbabYw(String zlXbpgbabYw) {
        this.zlXbpgbabYw = zlXbpgbabYw;
    }

    public String getZlXbpgbabZjjgmc() {
        return zlXbpgbabZjjgmc;
    }

    public void setZlXbpgbabZjjgmc(String zlXbpgbabZjjgmc) {
        this.zlXbpgbabZjjgmc = zlXbpgbabZjjgmc;
    }

    public String getZlXbpgbabPgbgh() {
        return zlXbpgbabPgbgh;
    }

    public void setZlXbpgbabPgbgh(String zlXbpgbabPgbgh) {
        this.zlXbpgbabPgbgh = zlXbpgbabPgbgh;
    }

    public String getZlXbpgbabHzdwmc() {
        return zlXbpgbabHzdwmc;
    }

    public void setZlXbpgbabHzdwmc(String zlXbpgbabHzdwmc) {
        this.zlXbpgbabHzdwmc = zlXbpgbabHzdwmc;
    }

    public String getZlXbpgbabHzwjh() {
        return zlXbpgbabHzwjh;
    }

    public void setZlXbpgbabHzwjh(String zlXbpgbabHzwjh) {
        this.zlXbpgbabHzwjh = zlXbpgbabHzwjh;
    }

    public String getZlXbpgbabLy() {
        return zlXbpgbabLy;
    }

    public void setZlXbpgbabLy(String zlXbpgbabLy) {
        this.zlXbpgbabLy = zlXbpgbabLy;
    }

    public String getZlBxbpgbabYw() {
        return zlBxbpgbabYw;
    }

    public void setZlBxbpgbabYw(String zlBxbpgbabYw) {
        this.zlBxbpgbabYw = zlBxbpgbabYw;
    }

    public String getZlBxbpgbabZjjgmc() {
        return zlBxbpgbabZjjgmc;
    }

    public void setZlBxbpgbabZjjgmc(String zlBxbpgbabZjjgmc) {
        this.zlBxbpgbabZjjgmc = zlBxbpgbabZjjgmc;
    }

    public String getZlBxbpgbabPgbgh() {
        return zlBxbpgbabPgbgh;
    }

    public void setZlBxbpgbabPgbgh(String zlBxbpgbabPgbgh) {
        this.zlBxbpgbabPgbgh = zlBxbpgbabPgbgh;
    }

    public String getZlBxbpgbabHzdwmc() {
        return zlBxbpgbabHzdwmc;
    }

    public void setZlBxbpgbabHzdwmc(String zlBxbpgbabHzdwmc) {
        this.zlBxbpgbabHzdwmc = zlBxbpgbabHzdwmc;
    }

    public String getZlBxbpgbabHzwjh() {
        return zlBxbpgbabHzwjh;
    }

    public void setZlBxbpgbabHzwjh(String zlBxbpgbabHzwjh) {
        this.zlBxbpgbabHzwjh = zlBxbpgbabHzwjh;
    }

    public String getZlBxbpgbabLy() {
        return zlBxbpgbabLy;
    }

    public void setZlBxbpgbabLy(String zlBxbpgbabLy) {
        this.zlBxbpgbabLy = zlBxbpgbabLy;
    }

    public String getZlTjpgbabYw() {
        return zlTjpgbabYw;
    }

    public void setZlTjpgbabYw(String zlTjpgbabYw) {
        this.zlTjpgbabYw = zlTjpgbabYw;
    }

    public String getZlTjpgbabZjjgmc() {
        return zlTjpgbabZjjgmc;
    }

    public void setZlTjpgbabZjjgmc(String zlTjpgbabZjjgmc) {
        this.zlTjpgbabZjjgmc = zlTjpgbabZjjgmc;
    }

    public String getZlTjpgbabPgbgh() {
        return zlTjpgbabPgbgh;
    }

    public void setZlTjpgbabPgbgh(String zlTjpgbabPgbgh) {
        this.zlTjpgbabPgbgh = zlTjpgbabPgbgh;
    }

    public String getZlTjpgbabHzdwmc() {
        return zlTjpgbabHzdwmc;
    }

    public void setZlTjpgbabHzdwmc(String zlTjpgbabHzdwmc) {
        this.zlTjpgbabHzdwmc = zlTjpgbabHzdwmc;
    }

    public String getZlTjpgbabHzwjh() {
        return zlTjpgbabHzwjh;
    }

    public void setZlTjpgbabHzwjh(String zlTjpgbabHzwjh) {
        this.zlTjpgbabHzwjh = zlTjpgbabHzwjh;
    }

    public String getZlTjpgbabLy() {
        return zlTjpgbabLy;
    }

    public void setZlTjpgbabLy(String zlTjpgbabLy) {
        this.zlTjpgbabLy = zlTjpgbabLy;
    }

    public String getZlSyzcczxyYw() {
        return zlSyzcczxyYw;
    }

    public void setZlSyzcczxyYw(String zlSyzcczxyYw) {
        this.zlSyzcczxyYw = zlSyzcczxyYw;
    }

    public String getZlSyzcczxyLy() {
        return zlSyzcczxyLy;
    }

    public void setZlSyzcczxyLy(String zlSyzcczxyLy) {
        this.zlSyzcczxyLy = zlSyzcczxyLy;
    }

    public String getZlZgdbdhjyYw() {
        return zlZgdbdhjyYw;
    }

    public void setZlZgdbdhjyYw(String zlZgdbdhjyYw) {
        this.zlZgdbdhjyYw = zlZgdbdhjyYw;
    }

    public String getZlZgdbdhjyYj() {
        return zlZgdbdhjyYj;
    }

    public void setZlZgdbdhjyYj(String zlZgdbdhjyYj) {
        this.zlZgdbdhjyYj = zlZgdbdhjyYj;
    }

    public String getZlZgdbdhjyLy() {
        return zlZgdbdhjyLy;
    }

    public void setZlZgdbdhjyLy(String zlZgdbdhjyLy) {
        this.zlZgdbdhjyLy = zlZgdbdhjyLy;
    }

    public String getZlGqszfawjYw() {
        return zlGqszfawjYw;
    }

    public void setZlGqszfawjYw(String zlGqszfawjYw) {
        this.zlGqszfawjYw = zlGqszfawjYw;
    }

    public String getZlGqszfawjPzdw() {
        return zlGqszfawjPzdw;
    }

    public void setZlGqszfawjPzdw(String zlGqszfawjPzdw) {
        this.zlGqszfawjPzdw = zlGqszfawjPzdw;
    }

    public String getZlGqszfawjPzwh() {
        return zlGqszfawjPzwh;
    }

    public void setZlGqszfawjPzwh(String zlGqszfawjPzwh) {
        this.zlGqszfawjPzwh = zlGqszfawjPzwh;
    }

    public String getZlGqszfawjLy() {
        return zlGqszfawjLy;
    }

    public void setZlGqszfawjLy(String zlGqszfawjLy) {
        this.zlGqszfawjLy = zlGqszfawjLy;
    }

    public String getZlGdqkdjbYw() {
        return zlGdqkdjbYw;
    }

    public void setZlGdqkdjbYw(String zlGdqkdjbYw) {
        this.zlGdqkdjbYw = zlGdqkdjbYw;
    }

    public String getZlGdqkdjbLy() {
        return zlGdqkdjbLy;
    }

    public void setZlGdqkdjbLy(String zlGdqkdjbLy) {
        this.zlGdqkdjbLy = zlGdqkdjbLy;
    }

    public String getZlQt() {
        return zlQt;
    }

    public void setZlQt(String zlQt) {
        this.zlQt = zlQt;
    }

    public String getZlGszxzmGsbmmc() {
        return zlGszxzmGsbmmc;
    }

    public void setZlGszxzmGsbmmc(String zlGszxzmGsbmmc) {
        this.zlGszxzmGsbmmc = zlGszxzmGsbmmc;
    }

    public String getXxYwfhbcz() {
        return xxYwfhbcz;
    }

    public void setXxYwfhbcz(String xxYwfhbcz) {
        this.xxYwfhbcz = xxYwfhbcz;
    }

    public String getXxYwfhbzfsgjk() {
        return xxYwfhbzfsgjk;
    }

    public void setXxYwfhbzfsgjk(String xxYwfhbzfsgjk) {
        this.xxYwfhbzfsgjk = xxYwfhbzfsgjk;
    }

    public BigDecimal getXxBdqypgjzcz() {
        return xxBdqypgjzcz;
    }

    public void setXxBdqypgjzcz(BigDecimal xxBdqypgjzcz) {
        this.xxBdqypgjzcz = xxBdqypgjzcz;
    }

    public String getXxBz() {
        return xxBz;
    }

    public void setXxBz(String xxBz) {
        this.xxBz = xxBz;
    }

    public BigDecimal getXxBdqysjjzcz() {
        return xxBdqysjjzcz;
    }

    public void setXxBdqysjjzcz(BigDecimal xxBdqysjjzcz) {
        this.xxBdqysjjzcz = xxBdqysjjzcz;
    }

    public String getXxZhyqymc() {
        return xxZhyqymc;
    }

    public void setXxZhyqymc(String xxZhyqymc) {
        this.xxZhyqymc = xxZhyqymc;
    }

    public String getXxZhyssgzjgjg() {
        return xxZhyssgzjgjg;
    }

    public void setXxZhyssgzjgjg(String xxZhyssgzjgjg) {
        this.xxZhyssgzjgjg = xxZhyssgzjgjg;
    }

    public String getXxZhyssgjczqy() {
        return xxZhyssgjczqy;
    }

    public void setXxZhyssgjczqy(String xxZhyssgjczqy) {
        this.xxZhyssgjczqy = xxZhyssgjczqy;
    }

    public BigDecimal getXxZhyyyzhdzcpgz() {
        return xxZhyyyzhdzcpgz;
    }

    public void setXxZhyyyzhdzcpgz(BigDecimal xxZhyyyzhdzcpgz) {
        this.xxZhyyyzhdzcpgz = xxZhyyyzhdzcpgz;
    }

    public String getXxZhybz() {
        return xxZhybz;
    }

    public void setXxZhybz(String xxZhybz) {
        this.xxZhybz = xxZhybz;
    }

    public String getXxZheqymc() {
        return xxZheqymc;
    }

    public void setXxZheqymc(String xxZheqymc) {
        this.xxZheqymc = xxZheqymc;
    }

    public String getXxZhessgzjgjg() {
        return xxZhessgzjgjg;
    }

    public void setXxZhessgzjgjg(String xxZhessgzjgjg) {
        this.xxZhessgzjgjg = xxZhessgzjgjg;
    }

    public String getXxZhessgjczqy() {
        return xxZhessgjczqy;
    }

    public void setXxZhessgjczqy(String xxZhessgjczqy) {
        this.xxZhessgjczqy = xxZhessgjczqy;
    }

    public BigDecimal getXxZheyyzhdzcpgz() {
        return xxZheyyzhdzcpgz;
    }

    public void setXxZheyyzhdzcpgz(BigDecimal xxZheyyzhdzcpgz) {
        this.xxZheyyzhdzcpgz = xxZheyyzhdzcpgz;
    }

    public String getXxZhebz() {
        return xxZhebz;
    }

    public void setXxZhebz(String xxZhebz) {
        this.xxZhebz = xxZhebz;
    }

    public BigDecimal getXxAzfyze() {
        return xxAzfyze;
    }

    public void setXxAzfyze(BigDecimal xxAzfyze) {
        this.xxAzfyze = xxAzfyze;
    }

    public BigDecimal getXxGyqy() {
        return xxGyqy;
    }

    public void setXxGyqy(BigDecimal xxGyqy) {
        this.xxGyqy = xxGyqy;
    }

    public String getXxBxbfmc() {
        return xxBxbfmc;
    }

    public void setXxBxbfmc(String xxBxbfmc) {
        this.xxBxbfmc = xxBxbfmc;
    }

    public BigDecimal getXxBxbfpgjzcz() {
        return xxBxbfpgjzcz;
    }

    public void setXxBxbfpgjzcz(BigDecimal xxBxbfpgjzcz) {
        this.xxBxbfpgjzcz = xxBxbfpgjzcz;
    }

    public BigDecimal getXxXbfpgjzcz() {
        return xxXbfpgjzcz;
    }

    public void setXxXbfpgjzcz(BigDecimal xxXbfpgjzcz) {
        this.xxXbfpgjzcz = xxXbfpgjzcz;
    }

    public BigDecimal getXxBflqypgjzcz() {
        return xxBflqypgjzcz;
    }

    public void setXxBflqypgjzcz(BigDecimal xxBflqypgjzcz) {
        this.xxBflqypgjzcz = xxBflqypgjzcz;
    }

    public BigDecimal getXxCxqypgjzcz() {
        return xxCxqypgjzcz;
    }

    public void setXxCxqypgjzcz(BigDecimal xxCxqypgjzcz) {
        this.xxCxqypgjzcz = xxCxqypgjzcz;
    }

    public BigDecimal getXxCxqyzgbz() {
        return xxCxqyzgbz;
    }

    public void setXxCxqyzgbz(BigDecimal xxCxqyzgbz) {
        this.xxCxqyzgbz = xxCxqyzgbz;
    }

    public BigDecimal getXxYytzdgqpgz() {
        return xxYytzdgqpgz;
    }

    public void setXxYytzdgqpgz(BigDecimal xxYytzdgqpgz) {
        this.xxYytzdgqpgz = xxYytzdgqpgz;
    }

    public BigDecimal getXxYytzdgqzj() {
        return xxYytzdgqzj;
    }

    public void setXxYytzdgqzj(BigDecimal xxYytzdgqzj) {
        this.xxYytzdgqzj = xxYytzdgqzj;
    }

    public BigDecimal getXxSyjzcczsr() {
        return xxSyjzcczsr;
    }

    public void setXxSyjzcczsr(BigDecimal xxSyjzcczsr) {
        this.xxSyjzcczsr = xxSyjzcczsr;
    }

    public String getXxCjfssgzjgjg() {
        return xxCjfssgzjgjg;
    }

    public void setXxCjfssgzjgjg(String xxCjfssgzjgjg) {
        this.xxCjfssgzjgjg = xxCjfssgzjgjg;
    }

    public String getXxBdqymc() {
        return xxBdqymc;
    }

    public void setXxBdqymc(String xxBdqymc) {
        this.xxBdqymc = xxBdqymc;
    }

    public String getXxBdqyssgzjgjg() {
        return xxBdqyssgzjgjg;
    }

    public void setXxBdqyssgzjgjg(String xxBdqyssgzjgjg) {
        this.xxBdqyssgzjgjg = xxBdqyssgzjgjg;
    }

    public BigDecimal getXxQsfpbc() {
        return xxQsfpbc;
    }

    public void setXxQsfpbc(BigDecimal xxQsfpbc) {
        this.xxQsfpbc = xxQsfpbc;
    }

    public BigDecimal getXxQsfpsqsk() {
        return xxQsfpsqsk;
    }

    public void setXxQsfpsqsk(BigDecimal xxQsfpsqsk) {
        this.xxQsfpsqsk = xxQsfpsqsk;
    }

    public String getXxQsccsfzyqczw() {
        return xxQsccsfzyqczw;
    }

    public void setXxQsccsfzyqczw(String xxQsccsfzyqczw) {
        this.xxQsccsfzyqczw = xxQsccsfzyqczw;
    }

    public BigDecimal getXxPcfpbc() {
        return xxPcfpbc;
    }

    public void setXxPcfpbc(BigDecimal xxPcfpbc) {
        this.xxPcfpbc = xxPcfpbc;
    }

    public BigDecimal getXxPcfpsqsk() {
        return xxPcfpsqsk;
    }

    public void setXxPcfpsqsk(BigDecimal xxPcfpsqsk) {
        this.xxPcfpsqsk = xxPcfpsqsk;
    }

    public String getXxYwfhbjz() {
        return xxYwfhbjz;
    }

    public void setXxYwfhbjz(String xxYwfhbjz) {
        this.xxYwfhbjz = xxYwfhbjz;
    }

    public BigDecimal getFd1Czzjhj() {
        return fd1Czzjhj;
    }

    public void setFd1Czzjhj(BigDecimal fd1Czzjhj) {
        this.fd1Czzjhj = fd1Czzjhj;
    }

    public BigDecimal getFd1Pgzhj() {
        return fd1Pgzhj;
    }

    public void setFd1Pgzhj(BigDecimal fd1Pgzhj) {
        this.fd1Pgzhj = fd1Pgzhj;
    }

    public BigDecimal getFd1Zfzjhj() {
        return fd1Zfzjhj;
    }

    public void setFd1Zfzjhj(BigDecimal fd1Zfzjhj) {
        this.fd1Zfzjhj = fd1Zfzjhj;
    }

    public BigDecimal getFd1Jzzjhj() {
        return fd1Jzzjhj;
    }

    public void setFd1Jzzjhj(BigDecimal fd1Jzzjhj) {
        this.fd1Jzzjhj = fd1Jzzjhj;
    }

    public BigDecimal getFd2Sgjghj() {
        return fd2Sgjghj;
    }

    public void setFd2Sgjghj(BigDecimal fd2Sgjghj) {
        this.fd2Sgjghj = fd2Sgjghj;
    }

    public BigDecimal getFd2Sjjzczhj() {
        return fd2Sjjzczhj;
    }

    public void setFd2Sjjzczhj(BigDecimal fd2Sjjzczhj) {
        this.fd2Sjjzczhj = fd2Sjjzczhj;
    }

    public BigDecimal getFd2Pgjzczhj() {
        return fd2Pgjzczhj;
    }

    public void setFd2Pgjzczhj(BigDecimal fd2Pgjzczhj) {
        this.fd2Pgjzczhj = fd2Pgjzczhj;
    }

    public BigDecimal getFd4Zgslhj() {
        return fd4Zgslhj;
    }

    public void setFd4Zgslhj(BigDecimal fd4Zgslhj) {
        this.fd4Zgslhj = fd4Zgslhj;
    }

    public BigDecimal getFd5Sfzjhj() {
        return fd5Sfzjhj;
    }

    public void setFd5Sfzjhj(BigDecimal fd5Sfzjhj) {
        this.fd5Sfzjhj = fd5Sfzjhj;
    }

    public BigDecimal getFd6Syccfpjghj() {
        return fd6Syccfpjghj;
    }

    public void setFd6Syccfpjghj(BigDecimal fd6Syccfpjghj) {
        this.fd6Syccfpjghj = fd6Syccfpjghj;
    }

    public BigDecimal getFd7Hzgqblhj() {
        return fd7Hzgqblhj;
    }

    public void setFd7Hzgqblhj(BigDecimal fd7Hzgqblhj) {
        this.fd7Hzgqblhj = fd7Hzgqblhj;
    }

    public BigDecimal getFd7Hzjzczhj() {
        return fd7Hzjzczhj;
    }

    public void setFd7Hzjzczhj(BigDecimal fd7Hzjzczhj) {
        this.fd7Hzjzczhj = fd7Hzjzczhj;
    }

    public BigDecimal getFd8Srgqsjjzczhj() {
        return fd8Srgqsjjzczhj;
    }

    public void setFd8Srgqsjjzczhj(BigDecimal fd8Srgqsjjzczhj) {
        this.fd8Srgqsjjzczhj = fd8Srgqsjjzczhj;
    }

    public BigDecimal getFd8Srgqpgjzczhj() {
        return fd8Srgqpgjzczhj;
    }

    public void setFd8Srgqpgjzczhj(BigDecimal fd8Srgqpgjzczhj) {
        this.fd8Srgqpgjzczhj = fd8Srgqpgjzczhj;
    }

    public BigDecimal getFd8Cjjhj() {
        return fd8Cjjhj;
    }

    public void setFd8Cjjhj(BigDecimal fd8Cjjhj) {
        this.fd8Cjjhj = fd8Cjjhj;
    }

    public BigDecimal getFd8Zrgqblhj() {
        return fd8Zrgqblhj;
    }

    public void setFd8Zrgqblhj(BigDecimal fd8Zrgqblhj) {
        this.fd8Zrgqblhj = fd8Zrgqblhj;
    }

    public BigDecimal getFd8Zrgqsjjzczhj() {
        return fd8Zrgqsjjzczhj;
    }

    public void setFd8Zrgqsjjzczhj(BigDecimal fd8Zrgqsjjzczhj) {
        this.fd8Zrgqsjjzczhj = fd8Zrgqsjjzczhj;
    }

    public BigDecimal getFd8Zrgqpgjzczhj() {
        return fd8Zrgqpgjzczhj;
    }

    public void setFd8Zrgqpgjzczhj(BigDecimal fd8Zrgqpgjzczhj) {
        this.fd8Zrgqpgjzczhj = fd8Zrgqpgjzczhj;
    }

    public BigDecimal getFd8Srgqblhj() {
        return fd8Srgqblhj;
    }

    public void setFd8Srgqblhj(BigDecimal fd8Srgqblhj) {
        this.fd8Srgqblhj = fd8Srgqblhj;
    }

    public BigDecimal getFd2Sggqblhj() {
        return fd2Sggqblhj;
    }

    public void setFd2Sggqblhj(BigDecimal fd2Sggqblhj) {
        this.fd2Sggqblhj = fd2Sggqblhj;
    }

    public BigDecimal getXxFxgs() {
        return xxFxgs;
    }

    public void setXxFxgs(BigDecimal xxFxgs) {
        this.xxFxgs = xxFxgs;
    }

    public BigDecimal getXxGkfxgs() {
        return xxGkfxgs;
    }

    public void setXxGkfxgs(BigDecimal xxGkfxgs) {
        this.xxGkfxgs = xxGkfxgs;
    }

    public BigDecimal getXxAzryzs() {
        return xxAzryzs;
    }

    public void setXxAzryzs(BigDecimal xxAzryzs) {
        this.xxAzryzs = xxAzryzs;
    }

    public BigDecimal getXxBxbfjzczhxbfgqbl() {
        return xxBxbfjzczhxbfgqbl;
    }

    public void setXxBxbfjzczhxbfgqbl(BigDecimal xxBxbfjzczhxbfgqbl) {
        this.xxBxbfjzczhxbfgqbl = xxBxbfjzczhxbfgqbl;
    }

    public BigDecimal getXxYytzgqzhbdqygqbl() {
        return xxYytzgqzhbdqygqbl;
    }

    public void setXxYytzgqzhbdqygqbl(BigDecimal xxYytzgqzhbdqygqbl) {
        this.xxYytzgqzhbdqygqbl = xxYytzgqzhbdqygqbl;
    }

    public String getZlFj() {
        return zlFj;
    }

    public void setZlFj(String zlFj) {
        this.zlFj = zlFj;
    }

    public BigDecimal getXxBdqypgjzczBzz() {
        return xxBdqypgjzczBzz;
    }

    public void setXxBdqypgjzczBzz(BigDecimal xxBdqypgjzczBzz) {
        this.xxBdqypgjzczBzz = xxBdqypgjzczBzz;
    }

    public BigDecimal getXxBdqypgjzczBjz() {
        return xxBdqypgjzczBjz;
    }

    public void setXxBdqypgjzczBjz(BigDecimal xxBdqypgjzczBjz) {
        this.xxBdqypgjzczBjz = xxBdqypgjzczBjz;
    }

    public BigDecimal getXxBdqypgjzczBgz() {
        return xxBdqypgjzczBgz;
    }

    public void setXxBdqypgjzczBgz(BigDecimal xxBdqypgjzczBgz) {
        this.xxBdqypgjzczBgz = xxBdqypgjzczBgz;
    }

    public BigDecimal getXxBdqysjjzczBgz() {
        return xxBdqysjjzczBgz;
    }

    public void setXxBdqysjjzczBgz(BigDecimal xxBdqysjjzczBgz) {
        this.xxBdqysjjzczBgz = xxBdqysjjzczBgz;
    }

    public BigDecimal getXxZgbzXzgb() {
        return xxZgbzXzgb;
    }

    public void setXxZgbzXzgb(BigDecimal xxZgbzXzgb) {
        this.xxZgbzXzgb = xxZgbzXzgb;
    }

    public BigDecimal getXxZgbzJs() {
        return xxZgbzJs;
    }

    public void setXxZgbzJs(BigDecimal xxZgbzJs) {
        this.xxZgbzJs = xxZgbzJs;
    }

    public BigDecimal getXxZgbzTzxz() {
        return xxZgbzTzxz;
    }

    public void setXxZgbzTzxz(BigDecimal xxZgbzTzxz) {
        this.xxZgbzTzxz = xxZgbzTzxz;
    }

    public BigDecimal getXxZgbzXshb() {
        return xxZgbzXshb;
    }

    public void setXxZgbzXshb(BigDecimal xxZgbzXshb) {
        this.xxZgbzXshb = xxZgbzXshb;
    }

    public BigDecimal getXxZgbzGqcz() {
        return xxZgbzGqcz;
    }

    public void setXxZgbzGqcz(BigDecimal xxZgbzGqcz) {
        this.xxZgbzGqcz = xxZgbzGqcz;
    }

    public String getZlYxhztzsYw() {
        return zlYxhztzsYw;
    }

    public void setZlYxhztzsYw(String zlYxhztzsYw) {
        this.zlYxhztzsYw = zlYxhztzsYw;
    }

    public String getZlYxhztzsLy() {
        return zlYxhztzsLy;
    }

    public void setZlYxhztzsLy(String zlYxhztzsLy) {
        this.zlYxhztzsLy = zlYxhztzsLy;
    }

    public String getZlYxhztzsHzdw() {
        return zlYxhztzsHzdw;
    }

    public void setZlYxhztzsHzdw(String zlYxhztzsHzdw) {
        this.zlYxhztzsHzdw = zlYxhztzsHzdw;
    }

    public BigDecimal getJc30sspj() {
        return jc30sspj;
    }

    public void setJc30sspj(BigDecimal jc30sspj) {
        this.jc30sspj = jc30sspj;
    }

    public BigDecimal getJcMgjzcz() {
        return jcMgjzcz;
    }

    public void setJcMgjzcz(BigDecimal jcMgjzcz) {
        this.jcMgjzcz = jcMgjzcz;
    }

    public BigDecimal getJcJcgs() {
        return jcJcgs;
    }

    public void setJcJcgs(BigDecimal jcJcgs) {
        this.jcJcgs = jcJcgs;
    }

    public BigDecimal getJcJcjj() {
        return jcJcjj;
    }

    public void setJcJcjj(BigDecimal jcJcjj) {
        this.jcJcjj = jcJcjj;
    }

    public String getZlQsbg() {
        return zlQsbg;
    }

    public void setZlQsbg(String zlQsbg) {
        this.zlQsbg = zlQsbg;
    }

    public String getZlFlxys() {
        return zlFlxys;
    }

    public void setZlFlxys(String zlFlxys) {
        this.zlFlxys = zlFlxys;
    }

    public String getZlGszxzm() {
        return zlGszxzm;
    }

    public void setZlGszxzm(String zlGszxzm) {
        this.zlGszxzm = zlGszxzm;
    }

    public String getZlZgdbdhjy() {
        return zlZgdbdhjy;
    }

    public void setZlZgdbdhjy(String zlZgdbdhjy) {
        this.zlZgdbdhjy = zlZgdbdhjy;
    }

    public String getZlGqszfawj() {
        return zlGqszfawj;
    }

    public void setZlGqszfawj(String zlGqszfawj) {
        this.zlGqszfawj = zlGqszfawj;
    }

    public String getZlZxgg() {
        return zlZxgg;
    }

    public void setZlZxgg(String zlZxgg) {
        this.zlZxgg = zlZxgg;
    }

    public String getZlHbxys() {
        return zlHbxys;
    }

    public void setZlHbxys(String zlHbxys) {
        this.zlHbxys = zlHbxys;
    }

    public String getZlJzrsjbg() {
        return zlJzrsjbg;
    }

    public void setZlJzrsjbg(String zlJzrsjbg) {
        this.zlJzrsjbg = zlJzrsjbg;
    }

    public String getZlZhxy() {
        return zlZhxy;
    }

    public void setZlZhxy(String zlZhxy) {
        this.zlZhxy = zlZhxy;
    }

    public String getZlFhbpgba() {
        return zlFhbpgba;
    }

    public void setZlFhbpgba(String zlFhbpgba) {
        this.zlFhbpgba = zlFhbpgba;
    }

    public String getZlZjyqsjbg() {
        return zlZjyqsjbg;
    }

    public void setZlZjyqsjbg(String zlZjyqsjbg) {
        this.zlZjyqsjbg = zlZjyqsjbg;
    }

    public String getZlPgba() {
        return zlPgba;
    }

    public void setZlPgba(String zlPgba) {
        this.zlPgba = zlPgba;
    }

    public String getZlBdpgba() {
        return zlBdpgba;
    }

    public void setZlBdpgba(String zlBdpgba) {
        this.zlBdpgba = zlBdpgba;
    }

    public String getZlWchzxy() {
        return zlWchzxy;
    }

    public void setZlWchzxy(String zlWchzxy) {
        this.zlWchzxy = zlWchzxy;
    }

    public String getZlJzgg() {
        return zlJzgg;
    }

    public void setZlJzgg(String zlJzgg) {
        this.zlJzgg = zlJzgg;
    }

    public String getZlYyzz() {
        return zlYyzz;
    }

    public void setZlYyzz(String zlYyzz) {
        this.zlYyzz = zlYyzz;
    }

    public String getZlSjbg() {
        return zlSjbg;
    }

    public void setZlSjbg(String zlSjbg) {
        this.zlSjbg = zlSjbg;
    }

    public String getZlYzbg() {
        return zlYzbg;
    }

    public void setZlYzbg(String zlYzbg) {
        this.zlYzbg = zlYzbg;
    }

    public String getZlQyzc() {
        return zlQyzc;
    }

    public void setZlQyzc(String zlQyzc) {
        this.zlQyzc = zlQyzc;
    }

    public String getZlGdqkdjb() {
        return zlGdqkdjb;
    }

    public void setZlGdqkdjb(String zlGdqkdjb) {
        this.zlGdqkdjb = zlGdqkdjb;
    }

    public String getZlZhypgbab() {
        return zlZhypgbab;
    }

    public void setZlZhypgbab(String zlZhypgbab) {
        this.zlZhypgbab = zlZhypgbab;
    }

    public String getZlSyzcczxy() {
        return zlSyzcczxy;
    }

    public void setZlSyzcczxy(String zlSyzcczxy) {
        this.zlSyzcczxy = zlSyzcczxy;
    }

    public String getZlTjpgbab() {
        return zlTjpgbab;
    }

    public void setZlTjpgbab(String zlTjpgbab) {
        this.zlTjpgbab = zlTjpgbab;
    }

    public String getZlGqzr() {
        return zlGqzr;
    }

    public void setZlGqzr(String zlGqzr) {
        this.zlGqzr = zlGqzr;
    }

    public String getZlZhepgbab() {
        return zlZhepgbab;
    }

    public void setZlZhepgbab(String zlZhepgbab) {
        this.zlZhepgbab = zlZhepgbab;
    }

    public String getZlXbpgbab() {
        return zlXbpgbab;
    }

    public void setZlXbpgbab(String zlXbpgbab) {
        this.zlXbpgbab = zlXbpgbab;
    }

    public String getZlTzxy() {
        return zlTzxy;
    }

    public void setZlTzxy(String zlTzxy) {
        this.zlTzxy = zlTzxy;
    }

    public String getZlBxbpgbab() {
        return zlBxbpgbab;
    }

    public void setZlBxbpgbab(String zlBxbpgbab) {
        this.zlBxbpgbab = zlBxbpgbab;
    }

    public String getZlGytdba() {
        return zlGytdba;
    }

    public void setZlGytdba(String zlGytdba) {
        this.zlGytdba = zlGytdba;
    }

    public String getZlJcjg() {
        return zlJcjg;
    }

    public void setZlJcjg(String zlJcjg) {
        this.zlJcjg = zlJcjg;
    }

    public String getZlJcwj() {
        return zlJcwj;
    }

    public void setZlJcwj(String zlJcwj) {
        this.zlJcwj = zlJcwj;
    }

    public String getZlPcgg() {
        return zlPcgg;
    }

    public void setZlPcgg(String zlPcgg) {
        this.zlPcgg = zlPcgg;
    }

    public String getZlYxhztzs() {
        return zlYxhztzs;
    }

    public void setZlYxhztzs(String zlYxhztzs) {
        this.zlYxhztzs = zlYxhztzs;
    }
}
