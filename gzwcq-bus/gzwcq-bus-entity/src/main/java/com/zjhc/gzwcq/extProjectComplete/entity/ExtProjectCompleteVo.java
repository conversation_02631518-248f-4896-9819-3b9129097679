package com.zjhc.gzwcq.extProjectComplete.entity;

import com.zjhc.gzwcq.extProjectComplete.enums.extProjectCompleteEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <br/>
 * 表名： cq_ext_project_complete <br/>
 * 描述：项目成交 <br/>
 */
public class ExtProjectCompleteVo extends ExtProjectComplete {

    private static final long serialVersionUID = 18L;
    private String khqc;
    private String xmlxName;
    private String zrfmc;

    public String getZrfmc() {
        return zrfmc;
    }

    public void setZrfmc(String zrfmc) {
        this.zrfmc = zrfmc;
    }

    @Override
    public void setXmlx(String xmlx) {
        String textName;
        if (StringUtils.isNotBlank(xmlx)) {
            textName = extProjectCompleteEnum.getTextName(xmlx);
            this.xmlxName = textName;
        }
        this.xmlx = xmlx;
    }

    public String getXmlxName() {
        String textName;
        if (StringUtils.isBlank(this.xmlxName)) {
            if (StringUtils.isNotBlank(this.xmlx)) {
                textName = extProjectCompleteEnum.getTextName(xmlx);
                this.xmlxName = textName;
            }
        }
        return xmlxName;
    }

    public void setXmlxName(String xmlxName) {
        this.xmlxName = xmlxName;
    }

    public String getKhqc() {
        return khqc;
    }

    public void setKhqc(String khqc) {
        this.khqc = khqc;
    }

    private List<ExtProjectCompleteVo> extProjectCompleteList;

    public ExtProjectCompleteVo() {
        super();
    }

    public ExtProjectCompleteVo(Long id) {
        super();
        this.id = id;
    }

    public List<ExtProjectCompleteVo> getExtProjectCompleteList() {
        return extProjectCompleteList;
    }

    public void setExtProjectCompleteList(List<ExtProjectCompleteVo> extProjectCompleteList) {
        this.extProjectCompleteList = extProjectCompleteList;
    }

}
