package com.zjhc.gzwcq.extProjectComplete.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_ext_project_complete <br/>
 *         描述：项目成交 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtProjectComplete implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected Long id;// 主键
  	@ApiParam(value="产权树ID")
	protected String organizationId;// 产权树ID
  	@ApiParam(value="项目类型")
	protected String xmlx;// 项目类型
  	@ApiParam(value="项目编号")
	protected String xmbh;// 项目编号
  	@ApiParam(value="项目名称")
	protected String xmmc;// 项目名称
  	@ApiParam(value="成交价格")
	protected String cjjg;// 成交价格
  	@ApiParam(value="成交金额（万元）")
	protected BigDecimal cjje;// 成交金额（万元）
  	@ApiParam(value="成交日期")
	protected String cjrq;// 成交日期
  	@ApiParam(value="成交时间")
	protected String cjsj;// 成交时间
  	@ApiParam(value="实际成交方式")
	protected String sjcjfs;// 实际成交方式
  	@ApiParam(value="受让方(承租方)名称")
	protected String srfmc;// 受让方(承租方)名称
  	@ApiParam(value="转让底价")
	protected String zrdj;// 转让底价
  	@ApiParam(value="评估价(万元)")
	protected BigDecimal pgj;// 评估价(万元)
  	@ApiParam(value="持股比例")
	protected BigDecimal cgbl;// 持股比例
  	@ApiParam(value="公共资源平台编号")
	protected String ggzyptbh;// 公共资源平台编号
  	@ApiParam(value="处理结果")
	protected String cljg;// 处理结果
  	@ApiParam(value="结果说明")
	protected String jgsm;// 结果说明
  	@ApiParam(value="成交租期")
	protected String zcczCjzq;// 成交租期
  	@ApiParam(value="不动产权证号")
	protected String bdcfczh;// 不动产权证号
  	@ApiParam(value="竞价结束时间")
	protected String jjjssj;// 竞价结束时间
  	@ApiParam(value="成交方联系方式")
	protected String srflxfs;// 成交方联系方式
  	@ApiParam(value="房产证号")
	protected String fczh;// 房产证号
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间
  	@ApiParam(value="状态 0-未读 1-已读")
	protected Integer status;// 状态 0-未读 1-已读

	public ExtProjectComplete() {
		super();
	}
	
  	public ExtProjectComplete(Long id) {
  		super();
  		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrganizationId() {
		return organizationId;
	}
	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId;
	}
	public String getXmlx() {
		return xmlx;
	}
	public void setXmlx(String xmlx) {
		this.xmlx = xmlx;
	}
	public String getXmbh() {
		return xmbh;
	}
	public void setXmbh(String xmbh) {
		this.xmbh = xmbh;
	}
	public String getXmmc() {
		return xmmc;
	}
	public void setXmmc(String xmmc) {
		this.xmmc = xmmc;
	}
	public String getCjjg() {
		return cjjg;
	}
	public void setCjjg(String cjjg) {
		this.cjjg = cjjg;
	}
	public BigDecimal getCjje() {
		return cjje;
	}
	public void setCjje(BigDecimal cjje) {
		this.cjje = cjje;
	}
	public String getCjrq() {
		return cjrq;
	}
	public void setCjrq(String cjrq) {
		this.cjrq = cjrq;
	}
	public String getCjsj() {
		return cjsj;
	}
	public void setCjsj(String cjsj) {
		this.cjsj = cjsj;
	}
	public String getSjcjfs() {
		return sjcjfs;
	}
	public void setSjcjfs(String sjcjfs) {
		this.sjcjfs = sjcjfs;
	}
	public String getSrfmc() {
		return srfmc;
	}
	public void setSrfmc(String srfmc) {
		this.srfmc = srfmc;
	}
	public String getZrdj() {
		return zrdj;
	}
	public void setZrdj(String zrdj) {
		this.zrdj = zrdj;
	}
	public BigDecimal getPgj() {
		return pgj;
	}
	public void setPgj(BigDecimal pgj) {
		this.pgj = pgj;
	}
	public BigDecimal getCgbl() {
		return cgbl;
	}
	public void setCgbl(BigDecimal cgbl) {
		this.cgbl = cgbl;
	}
	public String getGgzyptbh() {
		return ggzyptbh;
	}
	public void setGgzyptbh(String ggzyptbh) {
		this.ggzyptbh = ggzyptbh;
	}
	public String getCljg() {
		return cljg;
	}
	public void setCljg(String cljg) {
		this.cljg = cljg;
	}
	public String getJgsm() {
		return jgsm;
	}
	public void setJgsm(String jgsm) {
		this.jgsm = jgsm;
	}
	public String getZcczCjzq() {
		return zcczCjzq;
	}
	public void setZcczCjzq(String zcczCjzq) {
		this.zcczCjzq = zcczCjzq;
	}
	public String getBdcfczh() {
		return bdcfczh;
	}
	public void setBdcfczh(String bdcfczh) {
		this.bdcfczh = bdcfczh;
	}
	public String getJjjssj() {
		return jjjssj;
	}
	public void setJjjssj(String jjjssj) {
		this.jjjssj = jjjssj;
	}
	public String getSrflxfs() {
		return srflxfs;
	}
	public void setSrflxfs(String srflxfs) {
		this.srflxfs = srflxfs;
	}
	public String getFczh() {
		return fczh;
	}
	public void setFczh(String fczh) {
		this.fczh = fczh;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
}
