package com.zjhc.gzwcq.extProjectComplete.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/25:10:14:25
 **/
public enum extProjectCompleteEnum {
    /**
     * ZH	综合交易
     * SW	资产转让
     * GQ	产权转让
     * CG	企业采购
     * 1G	资产出租
     * 1C	企业增资
     */
    XMLX_ZH("ZH", "综合交易"),
    XMLX_SW("SW", "资产转让"),
    XMLX_GQ("GQ", "产权转让"),
    XMLX_CG("CG", "企业采购"),
    XMLX_1G("1G", "资产出租"),
    XMLX_1C("1C", "企业增资"),
    XMLX_DEFULT("-1", "企业类型不存在，请联系运维排查问题");
    private String code;
    private String name;
    public static Map<String, String> parMap;

    extProjectCompleteEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    static void initMap() {
        parMap = new HashMap<>();
        extProjectCompleteEnum[] values = extProjectCompleteEnum.values();
        for (extProjectCompleteEnum value : values) {
            parMap.put(value.getCode(), value.getName());
        }
    }

    public static String getTextName(String code) {
        if (Objects.isNull(parMap)) {
            initMap();
        }
        String s = parMap.get(code);
        if (Objects.isNull(s)) {
            //默认值
            s = extProjectCompleteEnum.XMLX_DEFULT.getName();
        }
        return s;
    }

    public static void setParMap(Map<String, String> parMap) {
        extProjectCompleteEnum.parMap = parMap;
    }

    public static Map<String, String> getParMap() {
        if (Objects.isNull(parMap)) {
            initMap();
        }
        return parMap;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
