package com.zjhc.gzwcq.czfd.entity;

import java.util.List;

/**
 * <AUTHOR> <br/>
 *         表名： cq_czfd <br/>
 *         描述：产权出资浮动行 <br/>
 */
public class CzfdVo extends Czfd {

	private static final long serialVersionUID = 18L;

	private List<CzfdVo> czfdList;
	private String fdCzrlbStr;//出资人类别
	private String fdCzfsStr;//出资方式
	private String  fdCzebzStr;
	private String fdSjzczbbzStr;
	private String fdRjzbbzStr;

	public String getFdCzebzStr() {
		return fdCzebzStr;
	}

	public void setFdCzebzStr(String fdCzebzStr) {
		this.fdCzebzStr = fdCzebzStr;
	}

	public String getFdSjzczbbzStr() {
		return fdSjzczbbzStr;
	}

	public void setFdSjzczbbzStr(String fdSjzczbbzStr) {
		this.fdSjzczbbzStr = fdSjzczbbzStr;
	}

	public String getFdRjzbbzStr() {
		return fdRjzbbzStr;
	}

	public void setFdRjzbbzStr(String fdRjzbbzStr) {
		this.fdRjzbbzStr = fdRjzbbzStr;
	}

	public CzfdVo() {
		super();
	}

  	public CzfdVo(String id) {
  		super();
  		this.id = id;
	}

	public List<CzfdVo> getCzfdList() {
		return czfdList;
	}

	public void setCzfdList(List<CzfdVo> czfdList) {
		this.czfdList = czfdList;
	}

	private String createUserStr;
	private String lastUpdateUserStr;

	public String getCreateUserStr() {
		return createUserStr;
	}

	public void setCreateUserStr(String createUserStr) {
		this.createUserStr = createUserStr;
	}

	public String getLastUpdateUserStr() {
		return lastUpdateUserStr;
	}

	public void setLastUpdateUserStr(String lastUpdateUserStr) {
		this.lastUpdateUserStr = lastUpdateUserStr;
	}

	public String getFdCzrlbStr() {
		return fdCzrlbStr;
	}

	public void setFdCzrlbStr(String fdCzrlbStr) {
		this.fdCzrlbStr = fdCzrlbStr;
	}

	public String getFdCzfsStr() {
		return fdCzfsStr;
	}

	public void setFdCzfsStr(String fdCzfsStr) {
		this.fdCzfsStr = fdCzfsStr;
	}
}
