package com.zjhc.gzwcq.hrhcfd.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR> <br/>
 *         表名： cq_hrhcfd <br/>
 *         描述：划入划出浮动 <br/>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Hrhcfd implements Serializable {

	private static final long serialVersionUID = 18L;
  	@ApiParam(value="主键")
	protected String id;// 主键
  	@ApiParam(value="关联基本信息ID")
	protected String jbxxId;// 关联基本信息ID
  	@ApiParam(value="单位ID")
	protected String unitid;// 单位ID
  	@ApiParam(value="数据时期")
	protected String datatime;// 数据时期
  	@ApiParam(value="浮动行顺序号")
	protected BigDecimal floatorder;// 浮动行顺序号
  	@ApiParam(value="划出方名称")
	protected String fd7Hcfmc;// 划出方名称
  	@ApiParam(value="无偿划转类别")
	protected String fd7Wchzlb;// 无偿划转类别
  	@ApiParam(value="划转基准日")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	protected Date fd7Hzjzr;// 划转基准日
  	@ApiParam(value="划转净资产值（万元）")
	protected BigDecimal fd7Hzjzcz;// 划转净资产值（万元）
  	@ApiParam(value="划出方所属国家出资企业")
	protected String fd7Hcfssgjczqy;// 划出方所属国家出资企业`
  	@ApiParam(value="划转标的企业名称")
	protected String fd7Hzbdqymc;// 划转标的企业名称
  	@ApiParam(value="划入方名称")
	protected String fd7Hrfmc;// 划入方名称
  	@ApiParam(value="划入方所属国家出资企业")
	protected String fd7Hrfssgjczqy;// 划入方所属国家出资企业
  	@ApiParam(value="备注")
	protected String fd7Bz;// 备注
  	@ApiParam(value="划出方所属国资监管机构")
	protected String fd7Hcfssgzjgjg;// 划出方所属国资监管机构
  	@ApiParam(value="划入方所属国资监管机构")
	protected String fd7Hrfssgzjgjg;// 划入方所属国资监管机构
  	@ApiParam(value="划转股权比例")
	protected BigDecimal fd7Hzgqbl;// 划转股权比例
  	@ApiParam(value="划入方名称_注销")
	protected String fd7HrfmcZx;// 划入方名称_注销
  	@ApiParam(value="划入方所属国资监管机构_注销")
	protected String fd7HrfssgzjgjgZx;// 划入方所属国资监管机构_注销
  	@ApiParam(value="创建人")
	protected String createUser;// 创建人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="创建时间")
	protected Date createTime;// 创建时间
  	@ApiParam(value="更新人")
	protected String lastUpdateUser;// 更新人
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
  	@ApiParam(value="更新时间")
	protected Date lastUpdateTime;// 更新时间

	public Hrhcfd() {
		super();
	}
	
  	public Hrhcfd(String id) {
  		super();
  		this.id = id;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getJbxxId() {
		return jbxxId;
	}
	public void setJbxxId(String jbxxId) {
		this.jbxxId = jbxxId;
	}
	public String getUnitid() {
		return unitid;
	}
	public void setUnitid(String unitid) {
		this.unitid = unitid;
	}
	public String getDatatime() {
		return datatime;
	}
	public void setDatatime(String datatime) {
		this.datatime = datatime;
	}
	public BigDecimal getFloatorder() {
		return floatorder;
	}
	public void setFloatorder(BigDecimal floatorder) {
		this.floatorder = floatorder;
	}
	public String getFd7Hcfmc() {
		return fd7Hcfmc;
	}
	public void setFd7Hcfmc(String fd7Hcfmc) {
		this.fd7Hcfmc = fd7Hcfmc;
	}
	public String getFd7Wchzlb() {
		return fd7Wchzlb;
	}
	public void setFd7Wchzlb(String fd7Wchzlb) {
		this.fd7Wchzlb = fd7Wchzlb;
	}
	public Date getFd7Hzjzr() {
		return fd7Hzjzr;
	}
	public void setFd7Hzjzr(Date fd7Hzjzr) {
		this.fd7Hzjzr = fd7Hzjzr;
	}
	public BigDecimal getFd7Hzjzcz() {
		return fd7Hzjzcz;
	}
	public void setFd7Hzjzcz(BigDecimal fd7Hzjzcz) {
		this.fd7Hzjzcz = fd7Hzjzcz;
	}
	public String getFd7Hcfssgjczqy() {
		return fd7Hcfssgjczqy;
	}
	public void setFd7Hcfssgjczqy(String fd7Hcfssgjczqy) {
		this.fd7Hcfssgjczqy = fd7Hcfssgjczqy;
	}
	public String getFd7Hzbdqymc() {
		return fd7Hzbdqymc;
	}
	public void setFd7Hzbdqymc(String fd7Hzbdqymc) {
		this.fd7Hzbdqymc = fd7Hzbdqymc;
	}
	public String getFd7Hrfmc() {
		return fd7Hrfmc;
	}
	public void setFd7Hrfmc(String fd7Hrfmc) {
		this.fd7Hrfmc = fd7Hrfmc;
	}
	public String getFd7Hrfssgjczqy() {
		return fd7Hrfssgjczqy;
	}
	public void setFd7Hrfssgjczqy(String fd7Hrfssgjczqy) {
		this.fd7Hrfssgjczqy = fd7Hrfssgjczqy;
	}
	public String getFd7Bz() {
		return fd7Bz;
	}
	public void setFd7Bz(String fd7Bz) {
		this.fd7Bz = fd7Bz;
	}
	public String getFd7Hcfssgzjgjg() {
		return fd7Hcfssgzjgjg;
	}
	public void setFd7Hcfssgzjgjg(String fd7Hcfssgzjgjg) {
		this.fd7Hcfssgzjgjg = fd7Hcfssgzjgjg;
	}
	public String getFd7Hrfssgzjgjg() {
		return fd7Hrfssgzjgjg;
	}
	public void setFd7Hrfssgzjgjg(String fd7Hrfssgzjgjg) {
		this.fd7Hrfssgzjgjg = fd7Hrfssgzjgjg;
	}
	public BigDecimal getFd7Hzgqbl() {
		return fd7Hzgqbl;
	}
	public void setFd7Hzgqbl(BigDecimal fd7Hzgqbl) {
		this.fd7Hzgqbl = fd7Hzgqbl;
	}
	public String getFd7HrfmcZx() {
		return fd7HrfmcZx;
	}
	public void setFd7HrfmcZx(String fd7HrfmcZx) {
		this.fd7HrfmcZx = fd7HrfmcZx;
	}
	public String getFd7HrfssgzjgjgZx() {
		return fd7HrfssgzjgjgZx;
	}
	public void setFd7HrfssgzjgjgZx(String fd7HrfssgzjgjgZx) {
		this.fd7HrfssgzjgjgZx = fd7HrfssgzjgjgZx;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getLastUpdateUser() {
		return lastUpdateUser;
	}
	public void setLastUpdateUser(String lastUpdateUser) {
		this.lastUpdateUser = lastUpdateUser;
	}
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
