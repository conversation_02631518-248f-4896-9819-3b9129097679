package com.zjhc.gzwcq.attachment.api;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.iAdmin.ftp.util.FtpPoolHelper;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.entity.AttachmentParam;
import com.zjhc.gzwcq.attachment.entity.AttachmentVo;
import com.zjhc.gzwcq.attachment.service.api.IAttachmentService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value="/attachmentRemoteApi")
@Api(value="attachment接口文档",tags="附件表")
public class AttachmentRemoteApi {
  
  	@Autowired
	private IAttachmentService attachmentService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Attachment
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<AttachmentVo> queryByPage(@RequestBody AttachmentParam attachmentParam) {
        BootstrapTableModel<AttachmentVo> model = new BootstrapTableModel<AttachmentVo>();
		model.setRows(attachmentService.queryAttachmentByPage(attachmentParam));
		model.setTotal(attachmentService.queryTotalAttachments(attachmentParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	public void insert(@RequestBody Attachment attachment){
    	attachmentService.insert(attachment);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
    @RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	attachmentService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
  	@RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	attachmentService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	public void updateIgnoreNull(@RequestBody Attachment attachment){
    	attachmentService.updateIgnoreNull(attachment);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	@RequestMapping(value="/update",method=RequestMethod.POST)
  	public void update(@RequestBody Attachment attachment){
    	attachmentService.update(attachment);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
  	@RequestMapping(value="/selectAttachmentByPrimaryKey",method=RequestMethod.POST)
	public Attachment selectAttachmentByPrimaryKey(@RequestBody Attachment attachment){
  		return attachmentService.selectAttachmentByPrimaryKey(attachment);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	public List<Attachment> selectForList(@RequestBody Attachment attachment){
    	return attachmentService.selectForList(attachment);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	public boolean validateUniqueParam(@RequestBody Attachment attachment){
    	return attachmentService.validateUniqueParam(attachment);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	public void saveOne(@RequestBody Attachment attachment){
    	attachmentService.saveOne(attachment);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	public void multipleSaveAndEdit(@RequestBody Attachment[] objs){
    	attachmentService.multipleSaveAndEdit(objs);
    };
	/**
	 * 上传文件
	 */
    @ApiOperation(value="上传文件")
    @RequestMapping(value="/uploadFile",method=RequestMethod.POST,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public Attachment uploadFile(@RequestPart("file") MultipartFile file){
		return attachmentService.uploadFile(file);
	}

	/**
	 * 下载文件
	 */
    @ApiOperation(value="下载文件")
	@RequestMapping(value="/download",method=RequestMethod.GET)
	public void download(HttpServletResponse response,@RequestParam("ftpPath") String ftpPath){
		InputStream fileInputStream =FtpPoolHelper.getInputStreamByName(ftpPath);
		OutputStream outStream;
		try {
			outStream = response.getOutputStream();

			byte[] bytes = new byte[1024];
			int len = 0;
			while ((len = fileInputStream.read(bytes)) != -1) {
				outStream.write(bytes, 0, len);
			}
			fileInputStream.close();
			outStream.close();
			outStream.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}