<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.auditflowHistory.mapper.IAuditflowHistoryMapper">

	<resultMap type="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="business_info_id" property="businessInfoId"/>
		<result column="af_unitid" property="afUnitid"/>
		<result column="af_datatime" property="afDatatime"/>
		<result column="af_processtype" property="afProcesstype"/>
		<result column="af_processuserid" property="afProcessuserid"/>
		<result column="af_processunitid" property="afProcessunitid"/>
		<result column="af_processdate" property="afProcessdate"/>
		<result column="af_processcomment" property="afProcesscomment"/>
		<result column="af_processusername" property="afProcessusername"/>
		<result column="af_processusertitle" property="afProcessusertitle"/>
		<result column="af_processunitcode" property="afProcessunitcode"/>
		<result column="af_processunittitle" property="afProcessunittitle"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="af_processgroup" property="afProcessgroup"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		business_info_id, 
		af_unitid, 
		af_datatime, 
		af_processtype, 
		af_processuserid, 
		af_processunitid, 
		af_processdate, 
		af_processcomment, 
		af_processusername, 
		af_processusertitle, 
		af_processunitcode, 
		af_processunittitle, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time,
		af_processgroup
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.business_info_id, 
		t.af_unitid, 
		t.af_datatime, 
		t.af_processtype, 
		t.af_processuserid, 
		t.af_processunitid, 
		t.af_processdate, 
		t.af_processcomment, 
		t.af_processusername, 
		t.af_processusertitle, 
		t.af_processunitcode, 
		t.af_processunittitle, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time,
		t.af_processgroup
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{businessInfoId}, 
		#{afUnitid}, 
		#{afDatatime}, 
		#{afProcesstype}, 
		#{afProcessuserid}, 
		#{afProcessunitid}, 
		#{afProcessdate}, 
		#{afProcesscomment}, 
		#{afProcessusername}, 
		#{afProcessusertitle}, 
		#{afProcessunitcode}, 
		#{afProcessunittitle}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime},
		#{afProcessgroup}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="businessInfoId != null and businessInfoId != ''">
			and t.business_info_id = #{businessInfoId}
		</if>
		<if test="afUnitid != null and afUnitid != ''">
			and t.af_unitid = #{afUnitid}
		</if>
		<if test="afDatatime != null and afDatatime != ''">
			and t.af_datatime = #{afDatatime}
		</if>
		<if test="afProcesstype != null and afProcesstype != ''">
			and t.af_processtype = #{afProcesstype}
		</if>
		<if test="afProcessuserid != null and afProcessuserid != ''">
			and t.af_processuserid = #{afProcessuserid}
		</if>
		<if test="afProcessunitid != null and afProcessunitid != ''">
			and t.af_processunitid = #{afProcessunitid}
		</if>
		<if test="afProcessdate != null">
			and t.af_processdate = #{afProcessdate}
		</if>
		<if test="afProcesscomment != null and afProcesscomment != ''">
			and t.af_processcomment = #{afProcesscomment}
		</if>
		<if test="afProcessusername != null and afProcessusername != ''">
			and t.af_processusername = #{afProcessusername}
		</if>
		<if test="afProcessusertitle != null and afProcessusertitle != ''">
			and t.af_processusertitle = #{afProcessusertitle}
		</if>
		<if test="afProcessunitcode != null and afProcessunitcode != ''">
			and t.af_processunitcode = #{afProcessunitcode}
		</if>
		<if test="afProcessunittitle != null and afProcessunittitle != ''">
			and t.af_processunittitle = #{afProcessunittitle}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="afProcessgroup != null and afProcessgroup != ''">
			and t.af_processgroup = #{afProcessgroup}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into rg_auditflow_history (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update rg_auditflow_history set isDeleted = 'Y' where
		id in
		<foreach collection="auditflowHistorys" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update rg_auditflow_history set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from rg_auditflow_history  where
		id in
		<foreach collection="auditflowHistorys" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from rg_auditflow_history  where id = #{id}
	</delete>
	
	<select id="selectAuditflowHistoryByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from rg_auditflow_history
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update rg_auditflow_history
		<set>
			<if test="businessInfoId != null">
				business_info_id=#{businessInfoId},
			</if>
			<if test="afUnitid != null">
				af_unitid=#{afUnitid},
			</if>
			<if test="afDatatime != null">
				af_datatime=#{afDatatime},
			</if>
			<if test="afProcesstype != null">
				af_processtype=#{afProcesstype},
			</if>
			<if test="afProcessuserid != null">
				af_processuserid=#{afProcessuserid},
			</if>
			<if test="afProcessunitid != null">
				af_processunitid=#{afProcessunitid},
			</if>
			<if test="afProcessdate != null">
				af_processdate=#{afProcessdate},
			</if>
			<if test="afProcesscomment != null">
				af_processcomment=#{afProcesscomment},
			</if>
			<if test="afProcessusername != null">
				af_processusername=#{afProcessusername},
			</if>
			<if test="afProcessusertitle != null">
				af_processusertitle=#{afProcessusertitle},
			</if>
			<if test="afProcessunitcode != null">
				af_processunitcode=#{afProcessunitcode},
			</if>
			<if test="afProcessunittitle != null">
				af_processunittitle=#{afProcessunittitle},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime},
			</if>
			<if test="afProcessgroup != null">
				af_processgroup = #{afProcessgroup}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update rg_auditflow_history
		<set>
			business_info_id=#{businessInfoId},
			af_unitid=#{afUnitid},
			af_datatime=#{afDatatime},
			af_processtype=#{afProcesstype},
			af_processuserid=#{afProcessuserid},
			af_processunitid=#{afProcessunitid},
			af_processdate=#{afProcessdate},
			af_processcomment=#{afProcesscomment},
			af_processusername=#{afProcessusername},
			af_processusertitle=#{afProcessusertitle},
			af_processunitcode=#{afProcessunitcode},
			af_processunittitle=#{afProcessunittitle},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime},
			af_processgroup = #{afProcessgroup}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>, su.NAME as approvalName,
				case when t.af_processusertitle is not null then su.PHONE else '' end as phone,
		       t.af_processgroup as approvalGroup,
				sys.text as approvalStatus
		from
			rg_auditflow_history t
			left join sys_users su on t.AF_PROCESSUSERID = su.USER_ID
			left join rg_business_info rbi on t.BUSINESS_INFO_ID = rbi.ID
			left join (select * from sys_dictionary where type_code = 'APPROVAL_STATUS') sys on t.AF_PROCESSTYPE = sys.val
		where 1 = 1
		<include refid="whereSql" />
		order by create_time
	</select>	

	<select id="queryTotalAuditflowHistorys" parameterType="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryParam" resultType="java.lang.Long">
		select
			count(ID)
		from rg_auditflow_history t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryAuditflowHistoryForList" parameterType="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			rg_auditflow_history t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from rg_auditflow_history t
		where t.id != #{id}
			<if test="businessInfoId != null and businessInfoId != ''">
				and t.business_info_id = #{businessInfoId}
			</if>
			<if test="afUnitid != null and afUnitid != ''">
				and t.af_unitid = #{afUnitid}
			</if>
			<if test="afDatatime != null and afDatatime != ''">
				and t.af_datatime = #{afDatatime}
			</if>
			<if test="afProcesstype != null and afProcesstype != ''">
				and t.af_processtype = #{afProcesstype}
			</if>
			<if test="afProcessuserid != null and afProcessuserid != ''">
				and t.af_processuserid = #{afProcessuserid}
			</if>
			<if test="afProcessunitid != null and afProcessunitid != ''">
				and t.af_processunitid = #{afProcessunitid}
			</if>
			<if test="afProcessdate != null">
				and t.af_processdate = #{afProcessdate}
			</if>
			<if test="afProcesscomment != null and afProcesscomment != ''">
				and t.af_processcomment = #{afProcesscomment}
			</if>
			<if test="afProcessusername != null and afProcessusername != ''">
				and t.af_processusername = #{afProcessusername}
			</if>
			<if test="afProcessusertitle != null and afProcessusertitle != ''">
				and t.af_processusertitle = #{afProcessusertitle}
			</if>
			<if test="afProcessunitcode != null and afProcessunitcode != ''">
				and t.af_processunitcode = #{afProcessunitcode}
			</if>
			<if test="afProcessunittitle != null and afProcessunittitle != ''">
				and t.af_processunittitle = #{afProcessunittitle}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
			<if test="afProcessgroup != null and afProcessgroup != ''">
				and t.af_processgroup = #{afProcessgroup}
			</if>
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from rg_auditflow_history where business_info_id = (select id from rg_business_info where jbxx_id = #{jbxxId} limit 1)
	</delete>
</mapper>