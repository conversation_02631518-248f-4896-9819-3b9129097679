package com.zjhc.gzwcq.sxzbpz.service.impl;

import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.github.pagehelper.PageHelper;
import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import com.zjhc.gzwcq.sxzbpz.mapper.ISxzbpzMapper;
import com.zjhc.gzwcq.sxzbpz.service.api.ISxzbpzService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SxzbpzServiceImpl implements ISxzbpzService {

    @Autowired
    private ISxzbpzMapper sxzbpzMapper;
    @Autowired
    private DictCacheStrategy dictCacheStrategy;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(Sxzbpz sxzbpz) {
        sxzbpzMapper.insert(sxzbpz);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKeys(Map<String, Object> map) {
        sxzbpzMapper.deleteByPrimaryKeys(map);
    }

    /**
     * 删除一个对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKey(String id) {
        sxzbpzMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIgnoreNull(Sxzbpz sxzbpz) {
        sxzbpzMapper.updateIgnoreNull(sxzbpz);
    }

    /**
     * 更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Sxzbpz sxzbpz) {
        sxzbpzMapper.update(sxzbpz);
    }

    @Override
    public List<SxzbpzVo> querySxzbpzByPage(SxzbpzParam sxzbpzParam) {
        //分页
        PageHelper.startPage(sxzbpzParam.getPageNumber(), sxzbpzParam.getLimit(), false);
        return sxzbpzMapper.querySxzbpzForList(sxzbpzParam);
    }


    @Override
    public SxzbpzVo selectSxzbpzByPrimaryKey(Sxzbpz Sxzbpz) {
        SxzbpzVo sxzbpzVo = sxzbpzMapper.selectSxzbpzByPrimaryKey(Sxzbpz);
        if (sxzbpzVo != null){
            String situations = sxzbpzVo.getSituations();
            if (StringUtils.isNotBlank(situations)){
                String[] split = situations.split(",");
                List<String> situationStrs = new ArrayList<>(split.length);
                for (String s : split) {
                    String djqx = dictCacheStrategy.getTextByVal("ZYCQDJQX", s);
                    if (StringUtils.isNotBlank(djqx)){
                        situationStrs.add(djqx);
                    }else {
                        djqx = dictCacheStrategy.getTextByVal("BDCQDJQX", s);
                        if (StringUtils.isNotBlank(djqx)){
                            situationStrs.add(djqx);
                        }else {
                            situationStrs.add(dictCacheStrategy.getTextByVal("ZXCQDJQX", s));
                        }
                    }

                }
                sxzbpzVo.setSituationStrs(situationStrs);
            }
        }
        return sxzbpzVo;
    }

    @Override
    public long queryTotalSxzbpzs(SxzbpzParam sxzbpzParam) {
        return sxzbpzMapper.queryTotalSxzbpzs(sxzbpzParam);
    }

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    @Override
    public List<Sxzbpz> selectForList(Sxzbpz sxzbpz) {
        return sxzbpzMapper.selectForList(sxzbpz);
    }

    /**
     * 数据唯一性验证
     */
    @Override
    public boolean validateUniqueParam(Sxzbpz sxzbpz) {
        return sxzbpzMapper.selectForUnique(sxzbpz).size() == 0;
    }

    /**
     * 保存单个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOne(Sxzbpz sxzbpz) {
        if (StringUtils.isBlank(sxzbpz.getId())) {
            this.insert(sxzbpz);
        } else {
            this.updateIgnoreNull(sxzbpz);
        }
    }

    /**
     * 保存多个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void multipleSaveAndEdit(Sxzbpz[] objs) {
        for (Sxzbpz sxzbpz : objs) {
            this.saveOne(sxzbpz);
        }
    }

    /**
     * 按条件获取业务相关表的所有字段,按表名分组展示
     */
    @Override
    public List<SxzbpzVo> loadFields(SxzbpzParam param) {
        List<SxzbpzVo> tables = sxzbpzMapper.selectTables(param);
        List<SxzbpzVo> sxzbpzs = this.querySxzbpzByPage(param);
        this.getDicTreeFromCache(sxzbpzs);
        for (SxzbpzVo vo : tables) {
            //方便前端展示,将表名也放到字段名中
            vo.setFieldName(vo.getTableName());
            vo.setFieldNameCn(vo.getTableNameCn());
            vo.setSxzbpzList(sxzbpzs.stream().filter(sx ->
                            Objects.equals(vo.getTableName(), sx.getTableName()))
                    .collect(Collectors.toList()));
        }
        return tables;
    }

    /**
     * 批量保存
     */
    @Override
    public void saveBatch(String ids, Sxzbpz sxzbpz) {
        String[] sxzIds = ids.split(",");
        sxzbpzMapper.saveBatch(sxzIds,sxzbpz);
    }

    /**
     * 获取经济行为分析指标,按指标类型分组
     */
    @Override
    public List<SxzbpzVo> loadByIndexType(SxzbpzParam param) {
        param.setEconomicBehaviorAnalysis(1);
        List<SxzbpzVo> types = sxzbpzMapper.selectByIndexType(param);
        //查询基本信息指标和所选择的情形对应的指标
        List<SxzbpzVo> sxzbpzVos = sxzbpzMapper.selectFieldsByParam(param);
        this.getDicTreeFromCache(sxzbpzVos);
        if (CollectionUtils.isNotEmpty(types)){
            Iterator<SxzbpzVo> typesIterator = types.iterator();
            while (typesIterator.hasNext()){
                SxzbpzVo next = typesIterator.next();
                next.setFieldNameCn(dictCacheStrategy.getTextByVal("INDEX_TYPE",next.getIndexType()));
                next.setSxzbpzList(sxzbpzVos.stream().filter(sx ->
                                Objects.equals(next.getIndexType(), sx.getIndexType()))
                        .collect(Collectors.toList()));
                //如果某个指标类型中没有任何字段,该指标不展示
                if (CollectionUtils.isEmpty(next.getSxzbpzList())){
                    typesIterator.remove();
                }
            }
        }
        return types;
    }

    /**
     * 按字段中文名搜索
     */
    @Override
    public List<SxzbpzVo> loadByFieldNameCn(Sxzbpz sxzbpz) {
        return sxzbpzMapper.loadByFieldNameCn(sxzbpz);
    }

    /**
     * 从缓存中获取字段为字典类型的字典树
     */
    private void getDicTreeFromCache(List<SxzbpzVo> vos){
        if (CollectionUtils.isNotEmpty(vos)){
            for (SxzbpzVo vo : vos) {
                //如果是字典类型,将字典树获取出来
                if (Constants.FIELD_TYPE_DIC.equals(vo.getType())){
                    DictionaryVo cache = dictCacheStrategy.getCache(vo.getDicType());
                    if (cache != null){
                        vo.setDictionaryList(cache.getDictionaryList());
                    }
                }
            }
        }
    }
}
