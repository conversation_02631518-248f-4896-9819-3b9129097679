package com.zjhc.gzwcq.xgzjgfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.xgzjgfd.client.XgzjgfdFeignClient;
import com.zjhc.gzwcq.xgzjgfd.service.api.IXgzjgfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdParam;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/xgzjgfdRemoteApi")
@Api(value="xgzjgfd接口文档",tags="新国资机构浮动")
public class XgzjgfdRemoteApi implements XgzjgfdFeignClient {
  
  	@Autowired
	private IXgzjgfdService xgzjgfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Xgzjgfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<XgzjgfdVo> queryByPage(@RequestBody XgzjgfdParam xgzjgfdParam) {
        BootstrapTableModel<XgzjgfdVo> model = new BootstrapTableModel<XgzjgfdVo>();
		model.setRows(xgzjgfdService.queryXgzjgfdByPage(xgzjgfdParam));
		model.setTotal(xgzjgfdService.queryTotalXgzjgfds(xgzjgfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Xgzjgfd xgzjgfd){
    	xgzjgfdService.insert(xgzjgfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	xgzjgfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	xgzjgfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Xgzjgfd xgzjgfd){
    	xgzjgfdService.updateIgnoreNull(xgzjgfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Xgzjgfd xgzjgfd){
    	xgzjgfdService.update(xgzjgfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Xgzjgfd selectXgzjgfdByPrimaryKey(@RequestBody Xgzjgfd xgzjgfd){
  		return xgzjgfdService.selectXgzjgfdByPrimaryKey(xgzjgfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Xgzjgfd> selectForList(@RequestBody Xgzjgfd xgzjgfd){
    	return xgzjgfdService.selectForList(xgzjgfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Xgzjgfd xgzjgfd){
    	return xgzjgfdService.validateUniqueParam(xgzjgfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Xgzjgfd xgzjgfd){
    	xgzjgfdService.saveOne(xgzjgfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Xgzjgfd[] objs){
    	xgzjgfdService.multipleSaveAndEdit(objs);
    };
	
}