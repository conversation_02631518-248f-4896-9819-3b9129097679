package com.zjhc.gzwcq.sxzbpz.mapper;

import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ISxzbpzMapper {

    /*保存对象*/
    void insert(Sxzbpz sxzbpz);

    //物理删除
    void deleteByPrimaryKeys(Map<String, Object> map);

    void deleteByPrimaryKey(@Param("id") String id);

    //逻辑删除
    void logicDeleteByPrimaryKeys(Map<String, Object> map);

    void logicDeleteByPrimaryKey(Map<String, Object> map);

    /*根据非空属性更新对象信息*/
    void updateIgnoreNull(Sxzbpz sxzbpz);

    /**
     * 更新
     */
    void update(Sxzbpz sxzbpz);

    /*分页查询对象*/
    List<SxzbpzVo> querySxzbpzForList(SxzbpzParam sxzbpzParam);

    /*数据总量查询*/
    long queryTotalSxzbpzs(SxzbpzParam sxzbpzParam);

    /*根据主键查询对象*/
    SxzbpzVo selectSxzbpzByPrimaryKey(Sxzbpz sxzbpz);

    /*根据部分属性对象查询全部结果，不分页*/
    List<Sxzbpz> selectForList(Sxzbpz sxzbpz);

    /**
     * 数据唯一性验证
     */
    List<Sxzbpz> selectForUnique(Sxzbpz sxzbpz);

    /**
     * 条件获取相关表数据
     */
    List<SxzbpzVo> selectTables(Sxzbpz sxzbpz);

    void saveBatch(@Param("sxzIds") String[] sxzIds, @Param("sxzbpz") Sxzbpz sxzbpz);

    /**
     * 获取所有经济行为分析指标,按指标类型分组
     */
    List<SxzbpzVo> selectByIndexType(SxzbpzParam param);

    /**
     * 获取满足情形的经济行为分析指标
     */
    List<SxzbpzVo> selectFieldsByParam(SxzbpzParam param);

    /**
     * 按字段中文名搜索
     */
    List<SxzbpzVo> loadByFieldNameCn(Sxzbpz sxzbpz);
}