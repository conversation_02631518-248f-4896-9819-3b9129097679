package com.zjhc.gzwcq.ygqcyffd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.ygqcyffd.mapper.IYgqcyffdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdVo;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdParam;
import com.zjhc.gzwcq.ygqcyffd.service.api.IYgqcyffdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class YgqcyffdServiceImpl implements IYgqcyffdService {
	
	@Autowired
	private IYgqcyffdMapper ygqcyffdMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Ygqcyffd ygqcyffd){
		ygqcyffd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		ygqcyffd.setCreateTime(new Date());//创建时间
		ygqcyffd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ygqcyffd.setLastUpdateTime(new Date());//更新时间
		ygqcyffdMapper.insert(ygqcyffd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		ygqcyffdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		ygqcyffdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Ygqcyffd ygqcyffd){
		ygqcyffd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ygqcyffd.setLastUpdateTime(new Date());//更新时间
		ygqcyffdMapper.updateIgnoreNull(ygqcyffd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Ygqcyffd ygqcyffd){
		ygqcyffd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ygqcyffd.setLastUpdateTime(new Date());//更新时间
		ygqcyffdMapper.update(ygqcyffd);
	}
	
	public List<YgqcyffdVo> queryYgqcyffdByPage(YgqcyffdParam ygqcyffdParam) {
      	//分页
      	PageHelper.startPage(ygqcyffdParam.getPageNumber(),ygqcyffdParam.getLimit(),false);
		return ygqcyffdMapper.queryYgqcyffdForList(ygqcyffdParam);
	}
	

	public YgqcyffdVo selectYgqcyffdByPrimaryKey(Ygqcyffd Ygqcyffd) {
		return ygqcyffdMapper.selectYgqcyffdByPrimaryKey(Ygqcyffd);
	}
	
	public long queryTotalYgqcyffds(YgqcyffdParam ygqcyffdParam) {
		return ygqcyffdMapper.queryTotalYgqcyffds(ygqcyffdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Ygqcyffd> selectForList(Ygqcyffd ygqcyffd){
		return ygqcyffdMapper.selectForList(ygqcyffd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Ygqcyffd ygqcyffd) {
		return ygqcyffdMapper.selectForUnique(ygqcyffd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Ygqcyffd ygqcyffd) {
		if(StringUtils.isBlank(ygqcyffd.getId())) {
			this.insert(ygqcyffd);
		}else {
			this.updateIgnoreNull(ygqcyffd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Ygqcyffd[] objs) {
		for(Ygqcyffd ygqcyffd : objs) {
			this.saveOne(ygqcyffd);
		}
	}
	/**
	 * 根据基本信息表id查数据
	 */
	@Override
	public List<YgqcyffdVo> selectByJbxxId(String id) {
		return ygqcyffdMapper.selectByJbxxId(id);
	}

	@Override
	public void deleteByJbxxId(String jbxxId) {
		ygqcyffdMapper.deleteByJbxxId(jbxxId);
	}
}
