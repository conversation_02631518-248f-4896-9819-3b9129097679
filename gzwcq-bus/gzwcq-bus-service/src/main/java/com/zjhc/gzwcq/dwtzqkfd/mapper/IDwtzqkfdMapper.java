package com.zjhc.gzwcq.dwtzqkfd.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdParam;
import org.apache.ibatis.annotations.Param;

public interface IDwtzqkfdMapper {
	
	/*保存对象*/
	void insert(Dwtzqkfd dwtzqkfd);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Dwtzqkfd dwtzqkfd);
	
	/**更新*/
	void update(Dwtzqkfd dwtzqkfd);
	
	/*分页查询对象*/
	List<DwtzqkfdVo> queryDwtzqkfdForList(DwtzqkfdParam dwtzqkfdParam);
	
	/*数据总量查询*/
	long queryTotalDwtzqkfds(DwtzqkfdParam dwtzqkfdParam);
	
	/*根据主键查询对象*/
	Dwtzqkfd selectDwtzqkfdByPrimaryKey(Dwtzqkfd dwtzqkfd);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Dwtzqkfd> selectForList(Dwtzqkfd dwtzqkfd);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Dwtzqkfd> selectForUnique(Dwtzqkfd dwtzqkfd);

    List<DwtzqkfdVo> selectByJbxxbId(String jbxxId);

    void deleteByJbxxId(String jbxxId);

	List<Dwtzqkfd> selectAllByJbxxId(@Param("jbxxId")String jbxxId);
}