package com.zjhc.gzwcq.placard.service.api;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.placard.entity.Placard;
import com.zjhc.gzwcq.placard.entity.PlacardVo;
import com.zjhc.gzwcq.placard.entity.PlacardParam;

public interface IPlacardService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Placard placard);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Placard placard);
	
	/**
	* 更新
	*/
	void update(Placard placard);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<PlacardVo> queryPlacardByPage(PlacardParam placardParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalPlacards(PlacardParam placardParam);
  
	
	/**
	 *通过ID查询数据
	 */
	PlacardVo selectPlacardByPrimaryKey(Placard placard);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Placard> selectForList(Placard placard);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Placard placard);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Placard placard);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Placard[] objs);

	void updateLookTimeAndRead(PlacardVo placard);

	/**
	 * 消息数量
	 */
	HashMap<String, Long> messageTotal(PlacardVo placard);
}