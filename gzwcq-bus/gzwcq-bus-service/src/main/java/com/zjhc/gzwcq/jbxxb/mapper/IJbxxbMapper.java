package com.zjhc.gzwcq.jbxxb.mapper;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbParam;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import com.zjhc.gzwcq.openApi.JbxxbInfo;
import com.zjhc.gzwcq.screen.entity.TimelinessRate;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IJbxxbMapper {

    /*保存对象*/
    void insert(Jbxxb jbxxb);

    //物理删除
    void deleteByPrimaryKeys(Map<String, Object> map);

    void deleteByPrimaryKey(@Param("id") String id);

    //逻辑删除
    void logicDeleteByPrimaryKeys(Map<String, Object> map);

    void logicDeleteByPrimaryKey(Map<String, Object> map);

    /*根据非空属性更新对象信息*/
    void updateIgnoreNull(Jbxxb jbxxb);

    /**
     * 更新
     */
    void update(Jbxxb jbxxb);

    /*分页查询对象*/
    List<JbxxbVo> queryJbxxbForList(JbxxbParam jbxxbParam);

    /*数据总量查询*/
    long queryTotalJbxxbs(JbxxbParam jbxxbParam);

    /*根据主键查询对象*/
    JbxxbVo selectJbxxbByPrimaryKey(Jbxxb jbxxb);

    /*根据部分属性对象查询全部结果，不分页*/
    List<Jbxxb> selectForList(Jbxxb jbxxb);

    List<JbxxbInfo> getJbxxbOrCzrDetail(@Param("orgId") String orgId, @Param("appOrgId") String appOrgId);
    Long getJbxxbOrCzrDetailTotal(@Param("orgId") String orgId, @Param("appOrgId") String appOrgId);

    /**
     * 数据唯一性验证
     */
    long selectForUnique(Jbxxb jbxxb);

    /**
     * 分页查询未办工商列表
     */
    List<JbxxbVo> selectWbgsByPage(@Param("jbxxbParam") JbxxbParam jbxxbParam,
                                   @Param("auditHostingList") Set<String> auditHostingList);

    /**
     * 根据组织id获取其最新的状态数据
     */
    JbxxbVo loadRecentApprovedByUnitId(Jbxxb jbxxb);

    /**
     * 根据组织id获取其最新的状态数据 因为2024.4.17 杨月提的bug 只能针对这一个接口处理
     */
    Map<String,String> loadRecentApprovedByUnitIdNew(Jbxxb jbxxb);

    /**
     * 根据企业id查询审核未通过的数据条数
     */
    int countUnapprovedByUnitId(Jbxxb jbxxb);

    /**
     * 如果保存时没有工商登记资料,将工商登记资料置空
     */
    void updateGsdjzlToNull(String id);

    /**
     * 判断企业是否存在下级
     */
    long hasSubordinate(Jbxxb jbxxb);

    /*分页查询对象*/
    List<JbxxbVo> queryDjForList(JbxxbParam jbxxbParam);

    /*数据总量查询*/
    long queryTotalDj(JbxxbParam jbxxbParam);

    /**
     * 统计当前登录用户
     *
     * @param auditHostingList
     * @return
     */
    int selectWbgsNumByLoginUser(@Param("auditHostingList") List<String> auditHostingList);

    /**
     * 按组织形式统计某企业的可见企业数量
     */
    List<Map<String, Object>> companyNumByZzxs(@Param("auditHostingList") List<String> auditHostingList);
    /**
    * @description: TODO 按组织形式统计某企业的可见企业数量
    * @author: hhy
    * @date: 2025/7/25 17:02
    * @param auditHostingList 参数说明
    * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
    */
    List<Map<String, Object>> companyNumByZzxsNew(@Param("auditHostingList") List<String> auditHostingList);

    List<DictionaryVo> loadZzxs();
    /**
     * 按季度及企业类别统计某企业的可见企业数量
     */
    List<Map<String, Object>> companyNumBySeason(@Param("auditHostingList") List<String> auditHostingList,
                                                 @Param("seasonLastDay") String seasonLastDay);

    /**
     * 查询及时率列表
     *
     * @param type  1本企业，2本级及以下企业
     * @param orgId 登陆人组织id
     * @return
     */
    List<TimelinessRate> queryTimelinessRateList(@Param("type") String type, @Param("orgId") String orgId, @Param("year") String year);

    /**
     * 查询第一次提交到国资委的时间
     *
     * @param businessInfoIds
     * @return
     */
    List<TimelinessRate> queryAllApprovalDate(@Param("list") List<String> businessInfoIds);

    /**
     * 查询退回率提交次数
     *
     * @param type  1本企业，2本级及以下企业
     * @param orgId 登陆人组织id
     * @return
     */
    List<String> queryAllSubmitList(@Param("type") String type, @Param("orgId") String orgId, @Param("year") String year);

    /**
     * 查询退回率退回次数
     *
     * @param businessInfoIds
     * @param year
     * @return
     */
    int queryAllReturnList(@Param("list") List<String> businessInfoIds, @Param("year") String year);

    int queryAllWaitList(@Param("list") List<String> businessInfoIds, @Param("year") String year);

    /**
     * 查询是否有经过国资委审核的企业数量
     *
     * @param jbxxId
     * @return
     */
    List<AuditflowHistoryVo> selectHasGzwApprovalCount(@Param("jbxxId") String jbxxId);

    /**
     * 根据组织id获取其最新的状态数据
     */
    List<JbxxbVo> loadRecentApprovedByOpenApi(@Param("orgId") List<String> orgId);

    /**
     * 根据组织id 查询最新的一条数据
     *
     * @param orgId
     * @return
     */
    Jbxxb selectRecentOneDataByorgId(@Param("orgId") String orgId);
}