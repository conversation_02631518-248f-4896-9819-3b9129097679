package com.zjhc.gzwcq.fhbzcfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.fhbzcfd.mapper.IFhbzcfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdVo;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdParam;
import com.zjhc.gzwcq.fhbzcfd.service.api.IFhbzcfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class FhbzcfdServiceImpl implements IFhbzcfdService {
	
	@Autowired
	private IFhbzcfdMapper fhbzcfdMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Fhbzcfd fhbzcfd){
		fhbzcfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		fhbzcfd.setCreateTime(new Date());//创建时间
		fhbzcfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		fhbzcfd.setLastUpdateTime(new Date());//更新时间
		fhbzcfdMapper.insert(fhbzcfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		fhbzcfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		fhbzcfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Fhbzcfd fhbzcfd){
		fhbzcfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		fhbzcfd.setLastUpdateTime(new Date());//更新时间
		fhbzcfdMapper.updateIgnoreNull(fhbzcfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Fhbzcfd fhbzcfd){
		fhbzcfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		fhbzcfd.setLastUpdateTime(new Date());//更新时间
		fhbzcfdMapper.update(fhbzcfd);
	}
	
	public List<FhbzcfdVo> queryFhbzcfdByPage(FhbzcfdParam fhbzcfdParam) {
      	//分页
      	PageHelper.startPage(fhbzcfdParam.getPageNumber(),fhbzcfdParam.getLimit(),false);
		return fhbzcfdMapper.queryFhbzcfdForList(fhbzcfdParam);
	}
	

	public FhbzcfdVo selectFhbzcfdByPrimaryKey(Fhbzcfd Fhbzcfd) {
		return fhbzcfdMapper.selectFhbzcfdByPrimaryKey(Fhbzcfd);
	}
	
	public long queryTotalFhbzcfds(FhbzcfdParam fhbzcfdParam) {
		return fhbzcfdMapper.queryTotalFhbzcfds(fhbzcfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Fhbzcfd> selectForList(Fhbzcfd fhbzcfd){
		return fhbzcfdMapper.selectForList(fhbzcfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Fhbzcfd fhbzcfd) {
		return fhbzcfdMapper.selectForUnique(fhbzcfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Fhbzcfd fhbzcfd) {
		if(StringUtils.isBlank(fhbzcfd.getId())) {
			this.insert(fhbzcfd);
		}else {
			this.updateIgnoreNull(fhbzcfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Fhbzcfd[] objs) {
		for(Fhbzcfd fhbzcfd : objs) {
			this.saveOne(fhbzcfd);
		}
	}
	/**
	 * 根据基本信息表id查数据
	 * @param id
	 * @return
	 */
	@Override
	public List<FhbzcfdVo> selectByJbxxId(String id) {
		return fhbzcfdMapper.selectByJbxxId(id);
	}

	@Override
	public void deleteByJbxxId(String jbxxId) {
		fhbzcfdMapper.deleteByJbxxId(jbxxId);
	}
}
