<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.businessInfo.mapper.IBusinessInfoMapper">

	<resultMap type="com.zjhc.gzwcq.businessInfo.entity.BusinessInfo" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="rg_solutionid" property="rgSolutionid"/>
		<result column="rg_unitstate" property="rgUnitstate"/>
		<result column="rg_timemark" property="rgTimemark"/>
		<result column="rg_type" property="rgType"/>
		<result column="rg_date" property="rgDate"/>
		<result column="rg_transfertype" property="rgTransfertype"/>
		<result column="af_current_node" property="afCurrentNode"/>
		<result column="af_currentunitid" property="afCurrentunitid"/>
		<result column="af_current_audit_level" property="afCurrentAuditLevel"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="if_monitorwarn" property="ifMonitorwarn"/>
		<result column="monitorwarn_id" property="monitorwarnId"/>
		<result column="register_code" property="registerCode"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		rg_solutionid, 
		rg_unitstate, 
		rg_timemark, 
		rg_type, 
		rg_date, 
		rg_transfertype, 
		af_current_node, 
		af_currentunitid, 
		af_current_audit_level, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time,
		if_monitorwarn,
		monitorwarn_id,
		register_code,
		djStatus
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.rg_solutionid, 
		t.rg_unitstate, 
		t.rg_timemark, 
		t.rg_type, 
		t.rg_date, 
		t.rg_transfertype, 
		t.af_current_node, 
		t.af_currentunitid, 
		t.af_current_audit_level, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time,
		t.if_monitorwarn,
		t.monitorwarn_id,
		t.register_code,
		t.djStatus
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{rgSolutionid}, 
		#{rgUnitstate}, 
		#{rgTimemark}, 
		#{rgType}, 
		#{rgDate}, 
		#{rgTransfertype}, 
		#{afCurrentNode}, 
		#{afCurrentunitid}, 
		#{afCurrentAuditLevel}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime},
		#{ifMonitorwarn},
		#{monitorwarnId},
		#{registerCode},
		#{djStatus}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="rgSolutionid != null and rgSolutionid != ''">
			and t.rg_solutionid = #{rgSolutionid}
		</if>
		<if test="rgUnitstate != null and rgUnitstate != ''">
			and t.rg_unitstate = #{rgUnitstate}
		</if>
		<if test="rgTimemark != null">
			and t.rg_timemark = #{rgTimemark}
		</if>
		<if test="rgType != null and rgType != ''">
			and t.rg_type = #{rgType}
		</if>
		<if test="rgDate != null">
			and t.rg_date = #{rgDate}
		</if>
		<if test="rgTransfertype != null and rgTransfertype != ''">
			and t.rg_transfertype = #{rgTransfertype}
		</if>
		<if test="afCurrentNode != null and afCurrentNode != ''">
			and t.af_current_node = #{afCurrentNode}
		</if>
		<if test="afCurrentunitid != null and afCurrentunitid != ''">
			and t.af_currentunitid = #{afCurrentunitid}
		</if>
		<if test="afCurrentAuditLevel != null and afCurrentAuditLevel != ''">
			and t.af_current_audit_level = #{afCurrentAuditLevel}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="ifMonitorwarn != null">
			and t.if_monitorwarn = #{ifMonitorwarn}
		</if>
		<if test="monitorwarnId != null and monitorwarnId != ''">
			and t.monitorwarn_id = #{monitorwarnId}
		</if>
		<if test="registerCode != null and registerCode != ''">
			and t.register_code = #{registerCode}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.businessInfo.entity.BusinessInfo" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into rg_business_info (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update rg_business_info set isDeleted = 'Y' where
		id in
		<foreach collection="businessInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update rg_business_info set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from rg_business_info  where
		id in
		<foreach collection="businessInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from rg_business_info  where id = #{id}
	</delete>
	
	<select id="selectBusinessInfoByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from rg_business_info
		where id=#{id}
	</select>

	<select id="selectBusinessInfoByPrimaryKeyByJbxxbId" resultMap="baseResultMapExt">
		select
		<include refid="columns"/>
		from rg_business_info
		where jbxx_id=#{jbxxbId}
	</select>
	<update id="updateIgnoreNull">
		update rg_business_info
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="rgSolutionid != null">
				rg_solutionid=#{rgSolutionid},
			</if>
			<if test="rgUnitstate != null">
				rg_unitstate=#{rgUnitstate},
			</if>
			<if test="rgTimemark != null">
				rg_timemark=#{rgTimemark},
			</if>
			<if test="rgType != null">
				rg_type=#{rgType},
			</if>
			<if test="rgDate != null">
				rg_date=#{rgDate},
			</if>
			<if test="rgTransfertype != null">
				rg_transfertype=#{rgTransfertype},
			</if>
			<if test="afCurrentNode != null">
				af_current_node=#{afCurrentNode},
			</if>
			<if test="afCurrentunitid != null">
				af_currentunitid=#{afCurrentunitid},
			</if>
			<if test="afCurrentAuditLevel != null">
				af_current_audit_level=#{afCurrentAuditLevel},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime},
			</if>
			<if test="ifMonitorwarn != null">
				if_monitorwarn = #{ifMonitorwarn},
			</if>
			<if test="monitorwarnId != null">
				monitorwarn_id = #{monitorwarnId},
			</if>
			<if test="registerCode != null">
				register_code = #{registerCode},
			</if>
		    <if test="djStatus != null">
				djStatus = #{djStatus}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update rg_business_info
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			rg_solutionid=#{rgSolutionid},
			rg_unitstate=#{rgUnitstate},
			rg_timemark=#{rgTimemark},
			rg_type=#{rgType},
			rg_date=#{rgDate},
			rg_transfertype=#{rgTransfertype},
			af_current_node=#{afCurrentNode},
			af_currentunitid=#{afCurrentunitid},
			af_current_audit_level=#{afCurrentAuditLevel},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime},
			if_monitorwarn = #{ifMonitorwarn},
			monitorwarn_id = #{monitorwarnId},
			register_code = #{registerCode}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.businessInfo.entity.BusinessInfo" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			rg_business_info t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalBusinessInfos" parameterType="com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam" resultType="java.lang.Long">
		select
			count(ID)
		from rg_business_info t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryBusinessInfoForList" parameterType="com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,sd1.text as rgTypeName
		from
			rg_business_info t
			left join (select * from sys_dictionary where type_code = 'RG_TYPE') sd1 on t.RG_TYPE = sd1.val
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.businessInfo.entity.BusinessInfo" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from rg_business_info t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="rgSolutionid != null and rgSolutionid != ''">
				and t.rg_solutionid = #{rgSolutionid}
			</if>
			<if test="rgUnitstate != null and rgUnitstate != ''">
				and t.rg_unitstate = #{rgUnitstate}
			</if>
			<if test="rgTimemark != null">
				and t.rg_timemark = #{rgTimemark}
			</if>
			<if test="rgType != null and rgType != ''">
				and t.rg_type = #{rgType}
			</if>
			<if test="rgDate != null">
				and t.rg_date = #{rgDate}
			</if>
			<if test="rgTransfertype != null and rgTransfertype != ''">
				and t.rg_transfertype = #{rgTransfertype}
			</if>
			<if test="afCurrentNode != null and afCurrentNode != ''">
				and t.af_current_node = #{afCurrentNode}
			</if>
			<if test="afCurrentunitid != null and afCurrentunitid != ''">
				and t.af_currentunitid = #{afCurrentunitid}
			</if>
			<if test="afCurrentAuditLevel != null and afCurrentAuditLevel != ''">
				and t.af_current_audit_level = #{afCurrentAuditLevel}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
			<if test="ifMonitorwarn != null">
				and t.if_monitorwarn = #{ifMonitorwarn}
			</if>
			<if test="monitorwarnId != null and monitorwarnId != ''">
				and t.monitorwarn_id = #{monitorwarnId}
			</if>
			<if test="registerCode != null and registerCode != ''">
				and t.register_code = #{registerCode}
			</if>
	</select>
	
	<select id="findNoAllApprovalBus" resultType="Integer">
		<!--select count(a.id)
		from rg_business_info a
		left join view_cq_jbxxb_noDelete b on a.JBXX_ID = b.ID
		where b.id != #{jbxxId}
		      and b.unitid = #{unitid} and RG_UNITSTATE != '2'-->
		SELECT count(1) FROM view_cq_jbxxb_noDelete cj
			JOIN rg_business_info rbi ON cj.id = rbi.JBXX_ID
		WHERE cj.id != #{jbxxId} and cj.UNITID = #{unitid}
		  AND (cj.JB_SHZT != '4' or rbi.RG_UNITSTATE != '2')
	</select>

	<select id="selectTodoList"
			resultMap="baseResultMapExt">
		select b.*,so.ORGANIZATION_CODE as orgCode,so.ORGANIZATION_NAME as orgName,
		sd1.text as rgTypeName,
		case when ${todoType} = '1' then '待审核' else '上级退回' end as busStatus
		from rg_auditflow_history a
		left join rg_business_info b on a.BUSINESS_INFO_ID = b.ID
		left join sys_organization so on b.UNITID = so.ORGANIZATION_ID
		left join (select * from sys_dictionary where type_code = 'RG_TYPE') sd1 on b.RG_TYPE = sd1.val
		where 1=1
-- 		  and a.AF_PROCESSDATE is not null
		<choose>
			<when test="todoType == 1">
				and a.AF_PROCESSTYPE = '6'
			</when>
			<when test="todoType == 2">
				and a.AF_PROCESSTYPE = '5' and b.RG_UNITSTATE = '3'
			</when>
		</choose>
		and a.AF_PROCESSUNITID in
		<foreach collection="auditHostingList" item="item" separator="," index="index" open="(" close=")">
			#{item}
		</foreach>
	</select>

	<select id="todoOrReturnList"
			resultMap="baseResultMapExt">
		select
		b.id,
		b.jbxx_id,
		<!--unitid应取基本信息的unitid-->
		qj.unitid unitid,
		b.datatime,
		b.floatorder,
		b.rg_solutionid,
		b.rg_unitstate,
		b.rg_timemark,
		b.rg_type,
		b.rg_date,
		b.rg_transfertype,
		b.af_current_node,
		b.af_currentunitid,
		b.af_current_audit_level,
		b.create_user,
		b.create_time,
		b.last_update_user,
		b.last_update_time,
		b.if_monitorwarn,
		b.monitorwarn_id,
		b.register_code,qj.JB_ZZJGDM as orgCode,qj.JB_QYMC as orgName,qj.jb_zycqdjqx jbZycqdjqx,qj.jb_bdcqdjqx jbBdcqdjqx,qj.jb_zxcqdjqx jbZxcqdjqx,
		sd1.text as rgTypeName,
		case when ${param.todoType} = '1' then '待审核' else '上级退回' end as busStatus,
		qj.create_user jbxxCreateUser,
		qj.JB_GJCZQY as jbGjczqy
		<!-- ,
		(select GROUP_CONCAT(uu.AF_PROCESSUNITID)
		from rg_auditflow_history uu
		where uu.BUSINESS_INFO_ID = a.BUSINESS_INFO_ID
		GROUP BY uu.BUSINESS_INFO_ID) as approvalUnitIds -->
		from rg_auditflow_history a
		left join rg_business_info b on a.BUSINESS_INFO_ID = b.ID
		left join view_cq_jbxxb_noDelete qj on b.JBXX_ID = qj.id
		left join (select val,text from sys_dictionary where type_id = (select id from sys_dictionary where type_code = 'RG_TYPE')) sd1 on b.RG_TYPE = sd1.val
		where qj.id is not null
		and qj.id not in (select distinct JBXX_ID from cq_monitorwarn where JBXX_ID is not null)
		<if test="param.approvalUnitIds != null and param.approvalUnitIds.size()>0">
			and b.UNITID in
			<foreach collection="param.approvalUnitIds" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
-- 		  and a.AF_PROCESSDATE is not null
		<if test="param.jbQymc != null and param.jbQymc != ''">
			and qj.JB_QYMC like concat('%',#{param.jbQymc},'%')
		</if>
		<if test="param.jbZzjgdm != null and param.jbZzjgdm != ''">
			and qj.JB_ZZJGDM like concat('%',#{param.jbZzjgdm},'%')
		</if>
		<choose>
			<!--1:审核通过,2:审核退回,3:上报,5:待上报,6:待审核-->
			<when test="param.todoType == 1">
				and a.AF_PROCESSTYPE = '6'
				and b.RG_UNITSTATE != '2'
				<!-- 审核人员需要区分初审复审 -->
				<choose>
					<!--  初审人员跟复审人员  -->
					<when test="param.auditLevel == 1">
						and b.AF_CURRENT_AUDIT_LEVEL = '初审'
					</when>
					<when test="param.auditLevel == 2">
						and b.AF_CURRENT_AUDIT_LEVEL = '复审'
					</when>
				</choose>
			</when>
			<when test="param.todoType == 2">
				and a.AF_PROCESSTYPE = '5' and b.RG_UNITSTATE = '3'
				and a.AF_PROCESSUSERID = #{param.userId}
			</when>
		</choose>
		and a.AF_PROCESSUNITID in
		<foreach collection="auditHostingList" item="item" separator="," index="index" open="(" close=")">
			#{item}
		</foreach>
		<!--order by a.AF_PROCESSDATE desc-->
		order by b.create_time
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from rg_business_info where jbxx_id = #{jbxxId}
	</delete>

	<select id="allApprovedAndNowList" parameterType="com.zjhc.gzwcq.businessInfo.entity.BusinessInfo" resultMap="baseResultMapExt">
		SELECT <include refid="columnsAlias"/>,
		CASE WHEN t.rg_type = 0 THEN '占有登记'
			 WHEN t.rg_type = 1 THEN '变动登记'
			 WHEN t.rg_type = 3 THEN '注销登记'
		END AS rgTypeName
		FROM rg_business_info t
		JOIN view_cq_jbxxb_noDelete cj ON t.jbxx_id = cj.id
		<where>
			(cj.JB_SHZT = '4' and t.unitid = #{unitid} and t.RG_UNITSTATE = #{rgUnitstate})
			or t.jbxx_id = #{jbxxId}
		</where>
		ORDER BY t.RG_DATE
	</select>

	<select id="loginUserTodoOrReturnNum" resultType="java.lang.Integer">
		select count(1)
		from
		(
		select b.id
		from rg_auditflow_history a
		left join rg_business_info b on a.BUSINESS_INFO_ID = b.ID
		left join view_cq_jbxxb_noDelete qj on b.JBXX_ID = qj.id
		where qj.id is not null
		and qj.id not in (select distinct JBXX_ID from cq_monitorwarn where JBXX_ID is not null)
		<choose>
			<!--1:审核通过,2:审核退回,3:上报,5:待上报,6:待审核-->
			<when test="param.todoType == 1">
				and a.AF_PROCESSTYPE = '6'
				and b.RG_UNITSTATE != '2'
				<!-- 审核人员需要区分初审复审 -->
				<choose>
					<!--  初审人员跟复审人员  -->
					<when test="param.auditLevel == 1">
						and b.AF_CURRENT_AUDIT_LEVEL = '初审'
					</when>
					<when test="param.auditLevel == 2">
						and b.AF_CURRENT_AUDIT_LEVEL = '复审'
					</when>
				</choose>
			</when>
			<when test="param.todoType == 2">
				and a.AF_PROCESSTYPE = '5' and b.RG_UNITSTATE = '3'
				and a.AF_PROCESSUSERID = #{param.userId}
			</when>
		</choose>
		and a.AF_PROCESSUNITID in
		<foreach collection="auditHostingList" item="item" separator="," index="index" open="(" close=")">
			#{item}
		</foreach>
		group by b.id
		) z
	</select>

	<select id="getPassData"  parameterType="com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam" resultMap="baseResultMapExt">
		select b.id,
		b.jbxx_id,
		<!--unitid应取基本信息的unitid-->
		qj.unitid unitid,
		b.rg_type,
		b.af_current_node,
		b.create_time,
		t.AF_PROCESSTYPE busStatus,
        qj.JB_ZZJGDM as orgCode,qj.JB_QYMC as orgName,qj.jb_zycqdjqx jbZycqdjqx,qj.jb_bdcqdjqx jbBdcqdjqx,qj.jb_zxcqdjqx jbZxcqdjqx,
		sd1.text as rgTypeName
		from rg_auditflow_history  t
		join  (select id,max(CREATE_TIME) from rg_auditflow_history  where AF_PROCESSUSERID = #{userId} Group By BUSINESS_INFO_ID,AF_PROCESSUSERID) tem on t.id = tem.ID
		left join rg_business_info b on t.BUSINESS_INFO_ID = b.ID
		left join view_cq_jbxxb_noDelete qj on b.JBXX_ID = qj.id
		left join (select val,text from sys_dictionary where type_id = (select id from sys_dictionary where type_code = 'RG_TYPE')) sd1 on b.RG_TYPE = sd1.val
		where qj.id is not null and t.AF_PROCESSTYPE in('1','2')
		<if test="jbQymc != null and jbQymc != ''">
			and qj.JB_QYMC like concat('%',#{jbQymc},'%')
		</if>
		<if test="jbZzjgdm != null and jbZzjgdm != ''">
			and qj.JB_ZZJGDM like concat('%',#{jbZzjgdm},'%')
		</if>
		<if test="approvalUnitIds != null and approvalUnitIds.size()>0">
			and b.UNITID in
			<foreach collection="approvalUnitIds" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>
</mapper>