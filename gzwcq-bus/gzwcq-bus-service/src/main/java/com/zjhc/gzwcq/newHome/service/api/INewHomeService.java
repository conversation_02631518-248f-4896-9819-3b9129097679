package com.zjhc.gzwcq.newHome.service.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.newHome.dto.BusinessAssessmentDTO;
import com.zjhc.gzwcq.newHome.vo.BusinessTransactionVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:43:20
 **/
public interface INewHomeService {

    ResponseEnvelope selectUserStatus();

    ResponseEnvelope selectBusinessAssessment(BusinessAssessmentDTO dto);

    ResponseEnvelope selectBusinessAssessmentInfo(BusinessAssessmentDTO dto);

    BootstrapTableModel<BusinessTransactionVO> selectHandleMattersInfo(BusinessAssessmentDTO dto);
}
