package com.zjhc.gzwcq.dcgqk.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkVo;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkParam;
import org.apache.ibatis.annotations.Param;

public interface IDcgqkMapper {
	
	/*保存对象*/
	void insert(Dcgqk dcgqk);

	List<DcgqkVo> selectAllByJbxxId(@Param("jbxxId")String jbxxId);

	void deletebyJbxxbId(@Param("id") String id);

	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Dcgqk dcgqk);
	
	/**更新*/
	void update(Dcgqk dcgqk);
	
	/*分页查询对象*/
	List<DcgqkVo> queryDcgqkForList(DcgqkParam dcgqkParam);
	
	/*数据总量查询*/
	long queryTotalDcgqks(DcgqkParam dcgqkParam);
	
	/*根据主键查询对象*/
	Dcgqk selectDcgqkByPrimaryKey(Dcgqk dcgqk);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Dcgqk> selectForList(Dcgqk dcgqk);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Dcgqk> selectForUnique(Dcgqk dcgqk);
	
}