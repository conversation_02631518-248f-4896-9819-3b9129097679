package com.zjhc.gzwcq.extProjectComplete.api;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.extProjectComplete.client.ExtProjectCompleteFeignClient;
import com.zjhc.gzwcq.extProjectComplete.service.api.IExtProjectCompleteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectComplete;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteParam;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value = "/extProjectCompleteRemoteApi")
@Api(value = "extProjectComplete接口文档", tags = "项目成交")
public class ExtProjectCompleteRemoteApi implements ExtProjectCompleteFeignClient {

    @Autowired
    private IExtProjectCompleteService extProjectCompleteService;

    /**
     * 分页查询列表
     *
     * @param BootstrapTableModel
     * @param ExtProjectComplete
     * @return String
     * @Title: load
     * <AUTHOR>
    @ApiOperation(value = "分页查询列表")
    public BootstrapTableModel<ExtProjectCompleteVo> queryByPage(@RequestBody ExtProjectCompleteParam extProjectCompleteParam) {
        BootstrapTableModel<ExtProjectCompleteVo> model = new BootstrapTableModel<ExtProjectCompleteVo>();
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        extProjectCompleteParam.setOrganizationId(user.getOrganization_id());
        model.setRows(extProjectCompleteService.queryExtProjectCompleteByPage(extProjectCompleteParam));
        model.setTotal(extProjectCompleteService.queryTotalExtProjectCompletes(extProjectCompleteParam));
        return model;
    }

    /**
     * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
     */
    @ApiOperation(value = "新增")
    public void insert(@RequestBody ExtProjectComplete extProjectComplete) {
        extProjectCompleteService.insert(extProjectComplete);
    }

    ;

    /**
     * 按对象中的主键进行删除，
     */
    @ApiOperation(value = "批量删除")
    public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map) {
        extProjectCompleteService.deleteByPrimaryKeys(map);
    }

    ;

    /**
     * 按对象中的主键进行删除，
     */
    @ApiOperation(value = "单条删除")
    public void deleteByPrimaryKey(@RequestParam("id") String id) {
        extProjectCompleteService.deleteByPrimaryKey(id);
    }

    ;

    /**
     * 按对象中的主键进行所有非空属性的修改
     */
    @ApiOperation(value = "更新忽略空字段")
    public void updateIgnoreNull(@RequestBody ExtProjectComplete extProjectComplete) {
        extProjectCompleteService.updateIgnoreNull(extProjectComplete);
    }

    ;

    /**
     * 更新
     */
    @ApiOperation(value = "更新全部字段")
    public void update(@RequestBody ExtProjectComplete extProjectComplete) {
        extProjectCompleteService.update(extProjectComplete);
    }

    ;

    /**
     * 通过ID查询数据
     */
    @ApiOperation(value = "根据主键查询")
    public ExtProjectComplete selectExtProjectCompleteByPrimaryKey(@RequestBody ExtProjectComplete extProjectComplete) {
        return extProjectCompleteService.selectExtProjectCompleteByPrimaryKey(extProjectComplete);
    }

    ;

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    @ApiOperation(value = "查询列表")
    public List<ExtProjectComplete> selectForList(@RequestBody ExtProjectComplete extProjectComplete) {
        return extProjectCompleteService.selectForList(extProjectComplete);
    }

    ;

    /**
     * 数据唯一性验证
     * <P>代码生成，必要时可以使用
     */
    @ApiOperation(value = "唯一性校验")
    public boolean validateUniqueParam(@RequestBody ExtProjectComplete extProjectComplete) {
        return extProjectCompleteService.validateUniqueParam(extProjectComplete);
    }

    ;

    /**
     * 保存单个对象
     * <P>存在则更新，不存在则新增
     */
    @ApiOperation(value = "插入更新")
    public void saveOne(@RequestBody ExtProjectComplete extProjectComplete) {
        extProjectCompleteService.saveOne(extProjectComplete);
    }

    ;

    /**
     * 保存多个对象
     */
    @ApiOperation(value = "批量插入更新")
    public void multipleSaveAndEdit(@RequestBody ExtProjectComplete[] objs) {
        extProjectCompleteService.multipleSaveAndEdit(objs);
    }

    ;

    @ApiOperation(value = "批量插入更新状态")
    public ResponseEnvelope updateStatus(@RequestParam("ids") String ids) {
        return extProjectCompleteService.updateStatus(ids);
    }

    @RequestMapping(value = "/unreadNumber", method = RequestMethod.POST)
    public ResponseEnvelope unreadNumber() {
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        return extProjectCompleteService.unreadNumber(user);
    }
}