package com.zjhc.gzwcq.ywzbb.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb12VO;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbParam;
import org.apache.ibatis.annotations.Param;

public interface IYwzbbMapper {
	
	/*保存对象*/
	void insert(Ywzbb ywzbb);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Ywzbb ywzbb);
	
	/**更新*/
	void update(Ywzbb ywzbb);
	
	/*分页查询对象*/
	List<YwzbbVo> queryYwzbbForList(YwzbbParam ywzbbParam);
	
	/*数据总量查询*/
	long queryTotalYwzbbs(YwzbbParam ywzbbParam);
	
	/*根据主键查询对象*/
	YwzbbVo selectYwzbbByPrimaryKey(Ywzbb ywzbb);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Ywzbb> selectForList(Ywzbb ywzbb);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Ywzbb> selectForUnique(Ywzbb ywzbb);

	/**
	 * 根据jbxxId查询
	 */
    YwzbbVo selectByJbxxId(String jbxxId);

    void deleteByJbxxId(String jbxxId);

	List<Ywzbb12VO> selectAllByJbxxId(@Param("jbxxId") String jbxxId);
}