package com.zjhc.gzwcq.auditflowHistory.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryParam;
import org.apache.ibatis.annotations.Param;

public interface IAuditflowHistoryMapper {
	
	/*保存对象*/
	void insert(AuditflowHistory auditflowHistory);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(AuditflowHistory auditflowHistory);
	
	/**更新*/
	void update(AuditflowHistory auditflowHistory);
	
	/*分页查询对象*/
	List<AuditflowHistoryVo> queryAuditflowHistoryForList(AuditflowHistoryParam auditflowHistoryParam);
	
	/*数据总量查询*/
	long queryTotalAuditflowHistorys(AuditflowHistoryParam auditflowHistoryParam);
	
	/*根据主键查询对象*/
	AuditflowHistory selectAuditflowHistoryByPrimaryKey(AuditflowHistory auditflowHistory);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<AuditflowHistoryVo> selectForList(AuditflowHistory auditflowHistory);
	
	/**
	 * 数据唯一性验证
	 * */
	List<AuditflowHistory> selectForUnique(AuditflowHistory auditflowHistory);

    void deleteByJbxxId(String jbxxId);
}