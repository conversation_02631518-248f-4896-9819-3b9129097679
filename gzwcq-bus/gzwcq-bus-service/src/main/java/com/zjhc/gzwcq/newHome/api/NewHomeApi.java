package com.zjhc.gzwcq.newHome.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.newHome.client.NewHomeFeignClient;
import com.zjhc.gzwcq.newHome.dto.BusinessAssessmentDTO;
import com.zjhc.gzwcq.newHome.service.api.INewHomeService;
import com.zjhc.gzwcq.newHome.vo.BusinessTransactionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:42:34
 **/
@RestController
@RequestMapping("newHomeApi")
@Api(tags = "新首页面", description = "新首页面")
public class NewHomeApi implements NewHomeFeignClient {
    @Autowired
    private INewHomeService service;

    @Override
    @ApiOperation(value = "查询用户状态")
    public ResponseEnvelope selectUserStatus() {
        return service.selectUserStatus();
    }

    @Override
    @ApiOperation(value = "查询业务考核数据")
    public ResponseEnvelope selectBusinessAssessment(BusinessAssessmentDTO dto) {
        return service.selectBusinessAssessment(dto);
    }

    @Override
    @ApiOperation(value = "查询一行数据块")
    public ResponseEnvelope selectBusinessAssessmentInfo(BusinessAssessmentDTO dto) {
        return service.selectBusinessAssessmentInfo(dto);
    }

    @Override
    @ApiOperation(value = "一级企业办理事项情况")
    public BootstrapTableModel<BusinessTransactionVO> selectHandleMattersInfo(BusinessAssessmentDTO dto) {
        return service.selectHandleMattersInfo(dto);
    }
}
