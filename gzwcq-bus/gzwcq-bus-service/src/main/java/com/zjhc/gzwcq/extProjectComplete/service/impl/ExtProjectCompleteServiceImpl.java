package com.zjhc.gzwcq.extProjectComplete.service.impl;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Map;
import java.util.List;
import java.util.Date;
import java.util.stream.Collectors;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.extProjectComplete.mapper.IExtProjectCompleteMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectComplete;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteVo;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteParam;
import com.zjhc.gzwcq.extProjectComplete.service.api.IExtProjectCompleteService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class ExtProjectCompleteServiceImpl implements IExtProjectCompleteService {

    @Autowired
    private IExtProjectCompleteMapper extProjectCompleteMapper;


    @Transactional(rollbackFor = Exception.class)
    public void insert(ExtProjectComplete extProjectComplete) {
        extProjectComplete.setCreateTime(new Date());//创建时间
        extProjectComplete.setLastUpdateTime(new Date());//更新时间
        extProjectCompleteMapper.insert(extProjectComplete);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKeys(Map<String, Object> map) {
        extProjectCompleteMapper.deleteByPrimaryKeys(map);
    }

    /**
     * 删除一个对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKey(String id) {
        extProjectCompleteMapper.deleteByPrimaryKey(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateIgnoreNull(ExtProjectComplete extProjectComplete) {
        extProjectComplete.setLastUpdateTime(new Date());//更新时间
        extProjectCompleteMapper.updateIgnoreNull(extProjectComplete);
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(ExtProjectComplete extProjectComplete) {
        extProjectComplete.setLastUpdateTime(new Date());//更新时间
        extProjectCompleteMapper.update(extProjectComplete);
    }

    public List<ExtProjectCompleteVo> queryExtProjectCompleteByPage(ExtProjectCompleteParam extProjectCompleteParam) {
        //分页
        PageHelper.startPage(extProjectCompleteParam.getPageNumber(), extProjectCompleteParam.getLimit(), false);
        return extProjectCompleteMapper.queryExtProjectCompleteForList(extProjectCompleteParam);
    }


    public ExtProjectComplete selectExtProjectCompleteByPrimaryKey(ExtProjectComplete ExtProjectComplete) {
        return extProjectCompleteMapper.selectExtProjectCompleteByPrimaryKey(ExtProjectComplete);
    }

    public long queryTotalExtProjectCompletes(ExtProjectCompleteParam extProjectCompleteParam) {
        long completes = extProjectCompleteMapper.queryTotalExtProjectCompletes(extProjectCompleteParam);
        return completes;
    }

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    public List<ExtProjectComplete> selectForList(ExtProjectComplete extProjectComplete) {
        return extProjectCompleteMapper.selectForList(extProjectComplete);
    }

    /**
     * 数据唯一性验证
     */
    @Override
    public boolean validateUniqueParam(ExtProjectComplete extProjectComplete) {
        return extProjectCompleteMapper.selectForUnique(extProjectComplete).size() == 0;
    }

    /**
     * 保存单个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOne(ExtProjectComplete extProjectComplete) {
        if (extProjectComplete.getId() == null) {
            this.insert(extProjectComplete);
        } else {
            this.updateIgnoreNull(extProjectComplete);
        }
    }

    /**
     * 保存多个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void multipleSaveAndEdit(ExtProjectComplete[] objs) {
        for (ExtProjectComplete extProjectComplete : objs) {
            this.saveOne(extProjectComplete);
        }
    }

    @Override
    public ResponseEnvelope updateStatus(String ids) {
        String[] split = ids.split(",");
        List<ExtProjectComplete> collect = Arrays.stream(split).parallel().map(iteam -> {
            ExtProjectComplete extProjectComplete = new ExtProjectComplete();
            extProjectComplete.setId(Long.valueOf(iteam));
            extProjectComplete.setStatus(1);
            return extProjectComplete;
        }).collect(Collectors.toList());
        for (ExtProjectComplete extProjectComplete : collect) {
            this.updateIgnoreNull(extProjectComplete);
        }
        return new ResponseEnvelope();
    }

    @Override
    public ResponseEnvelope unreadNumber(SysUser user) {
        ResponseEnvelope responseEnvelope = new ResponseEnvelope();
        String organization_id = user.getOrganization_id();
        Long number = extProjectCompleteMapper.unreadNumber(organization_id);
        responseEnvelope.setResult(number);
        return responseEnvelope;
    }
}
