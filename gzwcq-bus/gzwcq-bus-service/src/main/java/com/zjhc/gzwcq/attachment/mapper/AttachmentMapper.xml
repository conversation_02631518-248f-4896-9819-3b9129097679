<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.attachment.mapper.IAttachmentMapper">

	<resultMap type="com.zjhc.gzwcq.attachment.entity.Attachment" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="file_name" property="fileName"/>
		<result column="last_file_name" property="lastFileName"/>
		<result column="file_type" property="fileType"/>
		<result column="ftp_file_path" property="ftpFilePath"/>
		<result column="org_id" property="orgId"/>
		<result column="remark" property="remark"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.attachment.entity.AttachmentVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		file_name, 
		last_file_name, 
		file_type, 
		ftp_file_path, 
		org_id, 
		remark, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.file_name, 
		t.last_file_name, 
		t.file_type, 
		t.ftp_file_path, 
		t.org_id, 
		t.remark, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{fileName}, 
		#{lastFileName}, 
		#{fileType}, 
		#{ftpFilePath}, 
		#{orgId}, 
		#{remark}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="fileName != null and fileName != ''">
			and t.file_name = #{fileName}
		</if>
		<if test="lastFileName != null and lastFileName != ''">
			and t.last_file_name = #{lastFileName}
		</if>
		<if test="fileType != null and fileType != ''">
			and t.file_type = #{fileType}
		</if>
		<if test="ftpFilePath != null and ftpFilePath != ''">
			and t.ftp_file_path = #{ftpFilePath}
		</if>
		<if test="orgId != null">
			and t.org_id = #{orgId}
		</if>
		<if test="remark != null and remark != ''">
			and t.remark = #{remark}
		</if>
		<if test="createUser != null">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.attachment.entity.Attachment" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into sys_attachment (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update sys_attachment set isDeleted = 'Y' where
		id in
		<foreach collection="attachments" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update sys_attachment set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from sys_attachment  where
		id in
		<foreach collection="attachments" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from sys_attachment  where id = #{id}
	</delete>
	
	<select id="selectAttachmentByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from sys_attachment
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update sys_attachment
		<set>
			<if test="fileName != null">
				file_name=#{fileName},
			</if>
			<if test="lastFileName != null">
				last_file_name=#{lastFileName},
			</if>
			<if test="fileType != null">
				file_type=#{fileType},
			</if>
			<if test="ftpFilePath != null">
				ftp_file_path=#{ftpFilePath},
			</if>
			<if test="orgId != null">
				org_id=#{orgId},
			</if>
			<if test="remark != null">
				remark=#{remark},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update sys_attachment
		<set>
			file_name=#{fileName},
			last_file_name=#{lastFileName},
			file_type=#{fileType},
			ftp_file_path=#{ftpFilePath},
			org_id=#{orgId},
			remark=#{remark},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.attachment.entity.Attachment" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			sys_attachment t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalAttachments" parameterType="com.zjhc.gzwcq.attachment.entity.AttachmentParam" resultType="java.lang.Long">
		select
			count(id)
		from sys_attachment t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryAttachmentForList" parameterType="com.zjhc.gzwcq.attachment.entity.AttachmentParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			sys_attachment t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.attachment.entity.Attachment" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from sys_attachment t
		where t.id != #{id}
			<if test="fileName != null and fileName != ''">
				and t.file_name = #{fileName}
			</if>
			<if test="lastFileName != null and lastFileName != ''">
				and t.last_file_name = #{lastFileName}
			</if>
			<if test="fileType != null and fileType != ''">
				and t.file_type = #{fileType}
			</if>
			<if test="ftpFilePath != null and ftpFilePath != ''">
				and t.ftp_file_path = #{ftpFilePath}
			</if>
			<if test="orgId != null">
				and t.org_id = #{orgId}
			</if>
			<if test="remark != null and remark != ''">
				and t.remark = #{remark}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>
	<select id="selectAllById" resultType="string">
		SELECT
			sys_attachment.ftp_file_path
		FROM
			`sys_attachment`
		WHERE
			sys_attachment.id = #{id}
	</select>

	<select id="selectOrdIdById" resultType="string">
		SELECT
			sys_attachment.org_id
		FROM
			`sys_attachment`
		WHERE
			sys_attachment.id = #{id}
	</select>
</mapper>