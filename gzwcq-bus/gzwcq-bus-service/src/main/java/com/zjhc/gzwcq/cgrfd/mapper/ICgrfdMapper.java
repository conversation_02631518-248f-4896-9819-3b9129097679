package com.zjhc.gzwcq.cgrfd.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdVo;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdParam;
import org.apache.ibatis.annotations.Param;

public interface ICgrfdMapper {
	
	/*保存对象*/
	void insert(Cgrfd cgrfd);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Cgrfd cgrfd);
	
	/**更新*/
	void update(Cgrfd cgrfd);
	
	/*分页查询对象*/
	List<CgrfdVo> queryCgrfdForList(CgrfdParam cgrfdParam);
	
	/*数据总量查询*/
	long queryTotalCgrfds(CgrfdParam cgrfdParam);
	
	/*根据主键查询对象*/
	Cgrfd selectCgrfdByPrimaryKey(Cgrfd cgrfd);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Cgrfd> selectForList(Cgrfd cgrfd);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Cgrfd> selectForUnique(Cgrfd cgrfd);

	/**
	 * 根据jbxxId关联持股人信息
	 */
	List<CgrfdVo> selectByJbxxId(String jbxxId);

    void deleteByJbxxId(String jbxxId);

	List<Cgrfd> selectAllByJbxxId(@Param("jbxxId")String jbxxId);
}