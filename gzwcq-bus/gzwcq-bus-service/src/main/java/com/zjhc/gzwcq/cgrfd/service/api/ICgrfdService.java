package com.zjhc.gzwcq.cgrfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdVo;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdParam;

public interface ICgrfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Cgrfd cgrfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Cgrfd cgrfd);
	
	/**
	* 更新
	*/
	void update(Cgrfd cgrfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<CgrfdVo> queryCgrfdByPage(CgrfdParam cgrfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalCgrfds(CgrfdParam cgrfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Cgrfd selectCgrfdByPrimaryKey(Cgrfd cgrfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Cgrfd> selectForList(Cgrfd cgrfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Cgrfd cgrfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Cgrfd cgrfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Cgrfd[] objs);

    void deleteByJbxxId(String jbxxId);
}