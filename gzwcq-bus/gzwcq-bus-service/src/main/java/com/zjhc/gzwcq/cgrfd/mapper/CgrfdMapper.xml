<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.cgrfd.mapper.ICgrfdMapper">

	<resultMap type="com.zjhc.gzwcq.cgrfd.entity.Cgrfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="fd9_cgrmc" property="fd9Cgrmc"/>
		<result column="fd9_sjczr" property="fd9Sjczr"/>
		<result column="fd9_info" property="fd9Info"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.cgrfd.entity.CgrfdVo" extends="baseResultMap">
		<result property="createUserStr" column="createUserStr"/>
		<result property="lastUpdateUserStr" column="lastUpdateUserStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		fd9_cgrmc, 
		fd9_sjczr, 
		fd9_info, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.fd9_cgrmc, 
		t.fd9_sjczr, 
		t.fd9_info, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{fd9Cgrmc}, 
		#{fd9Sjczr}, 
		#{fd9Info}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="fd9Cgrmc != null and fd9Cgrmc != ''">
			and t.fd9_cgrmc = #{fd9Cgrmc}
		</if>
		<if test="fd9Sjczr != null and fd9Sjczr != ''">
			and t.fd9_sjczr = #{fd9Sjczr}
		</if>
		<if test="fd9Info != null and fd9Info != ''">
			and t.fd9_info = #{fd9Info}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.cgrfd.entity.Cgrfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_cgrfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_cgrfd set isDeleted = 'Y' where
		id in
		<foreach collection="cgrfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_cgrfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_cgrfd  where
		id in
		<foreach collection="cgrfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_cgrfd  where id = #{id}
	</delete>
	
	<select id="selectCgrfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_cgrfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_cgrfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="fd9Cgrmc != null">
				fd9_cgrmc=#{fd9Cgrmc},
			</if>
			<if test="fd9Sjczr != null">
				fd9_sjczr=#{fd9Sjczr},
			</if>
			<if test="fd9Info != null">
				fd9_info=#{fd9Info},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_cgrfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			fd9_cgrmc=#{fd9Cgrmc},
			fd9_sjczr=#{fd9Sjczr},
			fd9_info=#{fd9Info},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.cgrfd.entity.Cgrfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr
		from
			cq_cgrfd t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalCgrfds" parameterType="com.zjhc.gzwcq.cgrfd.entity.CgrfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_cgrfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryCgrfdForList" parameterType="com.zjhc.gzwcq.cgrfd.entity.CgrfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr
		from
			cq_cgrfd t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.cgrfd.entity.Cgrfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_cgrfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="fd9Cgrmc != null and fd9Cgrmc != ''">
				and t.fd9_cgrmc = #{fd9Cgrmc}
			</if>
			<if test="fd9Sjczr != null and fd9Sjczr != ''">
				and t.fd9_sjczr = #{fd9Sjczr}
			</if>
			<if test="fd9Info != null and fd9Info != ''">
				and t.fd9_info = #{fd9Info}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxId" parameterType="java.lang.String" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr
		from
		cq_cgrfd t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		where jbxx_id = #{jbxxId}
		order by t.FLOATORDER
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_cgrfd where jbxx_id = #{jbxxId}
	</delete>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			cg.FD9_SJCZR,
			cg.FD9_CGRMC,
			cg.FD9_INFO
		FROM `cq_cgrfd` cg
		WHERE
			cg.JBXX_ID = #{jbxxId}
	</select>
</mapper>