<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.czfd.mapper.ICzfdMapper">

	<resultMap type="com.zjhc.gzwcq.czfd.entity.Czfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="fd_czrmc" property="fdCzrmc"/>
		<result column="fd_cze" property="fdCze"/>
		<result column="fd_sjzcj" property="fdSjzcj"/>
		<result column="fd_gqbl" property="fdGqbl"/>
		<result column="fd_czrzzjgdm" property="fdCzrzzjgdm"/>
		<result column="fd_czebz" property="fdCzebz"/>
		<result column="fd_sjzczbbz" property="fdSjzczbbz"/>
		<result column="fd_czrlb" property="fdCzrlb"/>
		<result column="fd_rjzb" property="fdRjzb"/>
		<result column="fd_rjzbbz" property="fdRjzbbz"/>
		<result column="fd_czfs" property="fdCzfs"/>
		<result column="fd_jfqx" property="fdJfqx"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.czfd.entity.CzfdVo" extends="baseResultMap">
		<result property="createUserStr" column="createUserStr"/>
		<result property="lastUpdateUserStr" column="lastUpdateUserStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		fd_czrmc, 
		fd_cze, 
		fd_sjzcj, 
		fd_gqbl, 
		fd_czrzzjgdm, 
		fd_czebz, 
		fd_sjzczbbz, 
		fd_czrlb, 
		fd_rjzb, 
		fd_rjzbbz,
		fd_czfs,
		fd_jfqx,
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.fd_czrmc, 
		t.fd_cze, 
		t.fd_sjzcj, 
		t.fd_gqbl, 
		t.fd_czrzzjgdm, 
		t.fd_czebz, 
		t.fd_sjzczbbz, 
		t.fd_czrlb, 
		t.fd_rjzb, 
		t.fd_rjzbbz,
		t.fd_czfs,
		t.fd_jfqx,
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{fdCzrmc}, 
		#{fdCze}, 
		#{fdSjzcj}, 
		#{fdGqbl}, 
		#{fdCzrzzjgdm}, 
		#{fdCzebz}, 
		#{fdSjzczbbz}, 
		#{fdCzrlb}, 
		#{fdRjzb}, 
		#{fdRjzbbz},
		#{fdCzfs},
		#{fdJfqx},
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="fdCzrmc != null and fdCzrmc != ''">
			and t.fd_czrmc = #{fdCzrmc}
		</if>
		<if test="fdCze != null">
			and t.fd_cze = #{fdCze}
		</if>
		<if test="fdSjzcj != null">
			and t.fd_sjzcj = #{fdSjzcj}
		</if>
		<if test="fdGqbl != null">
			and t.fd_gqbl = #{fdGqbl}
		</if>
		<if test="fdCzrzzjgdm != null and fdCzrzzjgdm != ''">
			and t.fd_czrzzjgdm = #{fdCzrzzjgdm}
		</if>
		<if test="fdCzebz != null">
			and t.fd_czebz = #{fdCzebz}
		</if>
		<if test="fdSjzczbbz != null">
			and t.fd_sjzczbbz = #{fdSjzczbbz}
		</if>
		<if test="fdCzrlb != null and fdCzrlb != ''">
			and t.fd_czrlb = #{fdCzrlb}
		</if>
		<if test="fdRjzb != null">
			and t.fd_rjzb = #{fdRjzb}
		</if>
		<if test="fdRjzbbz != null">
			and t.fd_rjzbbz = #{fdRjzbbz}
		</if>
		<if test="fdCzfs != null and fdCzfs != ''">
			and t.fd_czfs = #{fdCzfs}
		</if>
		<if test="fdJfqx != null">
			and t.fd_jfqx = #{fdJfqx}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.czfd.entity.Czfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_czfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_czfd set isDeleted = 'Y' where
		id in
		<foreach collection="czfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_czfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_czfd  where
		id in
		<foreach collection="czfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_czfd  where id = #{id}
	</delete>
	
	<select id="selectCzfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_czfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_czfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="fdCzrmc != null">
				fd_czrmc=#{fdCzrmc},
			</if>
			<if test="fdCze != null">
				fd_cze=#{fdCze},
			</if>
			<if test="fdSjzcj != null">
				fd_sjzcj=#{fdSjzcj},
			</if>
			<if test="fdGqbl != null">
				fd_gqbl=#{fdGqbl},
			</if>
			<if test="fdCzrzzjgdm != null">
				fd_czrzzjgdm=#{fdCzrzzjgdm},
			</if>
			<if test="fdCzebz != null">
				fd_czebz=#{fdCzebz},
			</if>
			<if test="fdSjzczbbz != null">
				fd_sjzczbbz=#{fdSjzczbbz},
			</if>
			<if test="fdCzrlb != null">
				fd_czrlb=#{fdCzrlb},
			</if>
			<if test="fdRjzb != null">
				fd_rjzb=#{fdRjzb},
			</if>
			<if test="fdRjzbbz != null">
				fd_rjzbbz=#{fdRjzbbz},
			</if>
			<if test="fdCzfs != null and fdCzfs != ''">
				fd_czfs = #{fdCzfs},
			</if>
			<if test="fdJfqx != null">
				fd_jfqx = #{fdJfqx},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_czfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			fd_czrmc=#{fdCzrmc},
			fd_cze=#{fdCze},
			fd_sjzcj=#{fdSjzcj},
			fd_gqbl=#{fdGqbl},
			fd_czrzzjgdm=#{fdCzrzzjgdm},
			fd_czebz=#{fdCzebz},
			fd_sjzczbbz=#{fdSjzczbbz},
			fd_czrlb=#{fdCzrlb},
			fd_rjzb=#{fdRjzb},
			fd_rjzbbz=#{fdRjzbbz},
			fd_czfs = #{fdCzfs},
			fd_jfqx = #{fdJfqx},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.czfd.entity.Czfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_czfd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalCzfds" parameterType="com.zjhc.gzwcq.czfd.entity.CzfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_czfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryCzfdForList" parameterType="com.zjhc.gzwcq.czfd.entity.CzfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_czfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.czfd.entity.Czfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_czfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="fdCzrmc != null and fdCzrmc != ''">
				and t.fd_czrmc = #{fdCzrmc}
			</if>
			<if test="fdCze != null and fdCze != ''">
				and t.fd_cze = #{fdCze}
			</if>
			<if test="fdSjzcj != null and fdSjzcj != ''">
				and t.fd_sjzcj = #{fdSjzcj}
			</if>
			<if test="fdGqbl != null and fdGqbl != ''">
				and t.fd_gqbl = #{fdGqbl}
			</if>
			<if test="fdCzrzzjgdm != null and fdCzrzzjgdm != ''">
				and t.fd_czrzzjgdm = #{fdCzrzzjgdm}
			</if>
			<if test="fdCzebz != null and fdCzebz != ''">
				and t.fd_czebz = #{fdCzebz}
			</if>
			<if test="fdSjzczbbz != null and fdSjzczbbz != ''">
				and t.fd_sjzczbbz = #{fdSjzczbbz}
			</if>
			<if test="fdCzrlb != null and fdCzrlb != ''">
				and t.fd_czrlb = #{fdCzrlb}
			</if>
			<if test="fdRjzb != null and fdRjzb != ''">
				and t.fd_rjzb = #{fdRjzb}
			</if>
			<if test="fdRjzbbz != null and fdRjzbbz != ''">
				and t.fd_rjzbbz = #{fdRjzbbz}
			</if>
			<if test="fdCzfs != null and fdCzfs != ''">
				and t.fd_czfs = #{fdCzfs}
			</if>
			<if test="fdJfqx != null">
				and t.fd_jfqx = #{fdJfqx}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxId" parameterType="java.lang.String" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr,
		       sd1.text fdCzrlbStr,sd2.text fdCzfsStr
		from
		cq_czfd t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		left join sys_dictionary sd1 on sd1.val = t.fd_czrlb
			and sd1.type_id = (select id from sys_dictionary where type_code = 'CZRLB')
		left join sys_dictionary sd2 on sd2.val = t.fd_czfs
		    and sd2.type_id = (select id from sys_dictionary where type_code = 'CZFSPT')
		where jbxx_id = #{jbxxId}
		order by t.FLOATORDER
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_czfd where jbxx_id = #{jbxxId}
	</delete>
	<sql id="selectAllByJbxxIdVO">
		t.fd_czrmc,
		t.fd_cze,
		t.fd_sjzcj,
		t.fd_gqbl,
		t.fd_czrzzjgdm,
		t.fd_czebz,
		t.fd_sjzczbbz,
		t.fd_czrlb,
		t.fd_rjzb,
		t.fd_rjzbbz,
		t.fd_czfs,
		t.fd_jfqx
	</sql>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			<include refid="selectAllByJbxxIdVO"/>
		FROM
			`cq_czfd` t
		WHERE
			t.JBXX_ID = #{jbxxId}
	</select>
</mapper>