<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.jbxxb.mapper.IJbxxbMapper">

	<resultMap type="com.zjhc.gzwcq.jbxxb.entity.Jbxxb" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="jb_zczb" property="jbZczb"/>
		<result column="jb_gjsbs" property="jbGjsbs"/>
		<result column="jb_gjsds" property="jbGjsds"/>
		<result column="jb_gyfrsbs" property="jbGyfrsbs"/>
		<result column="jb_gyfrsds" property="jbGyfrsds"/>
		<result column="jb_gyjdkgsbs" property="jbGyjdkgsbs"/>
		<result column="jb_gyjdkgsds" property="jbGyjdkgsds"/>
		<result column="jb_gysjkzsbs" property="jbGysjkzsbs"/>
		<result column="jb_gysjkzsds" property="jbGysjkzsds"/>
		<result column="jb_qtqysbs" property="jbQtqysbs"/>
		<result column="jb_qtsds" property="jbQtsds"/>
		<result column="jb_hjqysbs" property="jbHjqysbs"/>
		<result column="jb_hjsds" property="jbHjsds"/>
		<result column="jb_sjblrq" property="jbSjblrq"/>
		<result column="jb_hjcze" property="jbHjcze"/>
		<result column="jb_hjsjzcj" property="jbHjsjzcj"/>
		<result column="jb_hjbl" property="jbHjbl"/>
		<result column="jb_qymc" property="jbQymc"/>
		<result column="jb_zzjgdm" property="jbZzjgdm"/>
		<result column="jb_zcmd" property="jbZcmd"/>
		<result column="jb_cgrxm" property="jbCgrxm"/>
		<result column="jb_jhqlsj" property="jbJhqlsj"/>
		<result column="jb_sjczr" property="jbSjczr"/>
		<result column="jb_czrzzjgdm" property="jbCzrzzjgdm"/>
		<result column="jb_hjsjzcjbz" property="jbHjsjzcjbz"/>
		<result column="jb_gjczsdsbz" property="jbGjczsdsbz"/>
		<result column="jb_gjczqysbsbz" property="jbGjczqysbsbz"/>
		<result column="jb_gyfrczsbsbz" property="jbGyfrczsbsbz"/>
		<result column="jb_gyfrczsdsbz" property="jbGyfrczsdsbz"/>
		<result column="jb_gyjdkgfrsbsbz" property="jbGyjdkgfrsbsbz"/>
		<result column="jb_gyjdkgfrsdsbz" property="jbGyjdkgfrsdsbz"/>
		<result column="jb_gysjkzfrsbsbz" property="jbGysjkzfrsbsbz"/>
		<result column="jb_gysjkzfrsdsbz" property="jbGysjkzfrsdsbz"/>
		<result column="jb_qtsbsbz" property="jbQtsbsbz"/>
		<result column="jb_qtqysdsbz" property="jbQtqysdsbz"/>
		<result column="jb_hjqysbsbz" property="jbHjqysbsbz"/>
		<result column="jb_hjsdsbz" property="jbHjsdsbz"/>
		<result column="jb_shzt" property="jbShzt"/>
		<result column="jb_sshy" property="jbSshy"/>
		<result column="jb_sfzy" property="jbSfzy"/>
		<result column="jb_zzxs" property="jbZzxs"/>
		<result column="jb_qylb" property="jbQylb"/>
		<result column="jb_qyjc" property="jbQyjc"/>
		<result column="jb_ssbm" property="jbSsbm"/>
		<result column="jb_jyzk" property="jbJyzk"/>
		<result column="jb_sftsmdgs" property="jbSftsmdgs"/>
		<result column="jb_sfczgrdcg" property="jbSfczgrdcg"/>
		<result column="jb_gjczqy" property="jbGjczqy"/>
		<result column="jb_qysbsbzxz" property="jbQysbsbzxz"/>
		<result column="jb_sdsbzxz" property="jbSdsbzxz"/>
		<result column="jb_jnjw" property="jbJnjw"/>
		<result column="jb_zycqdjqx" property="jbZycqdjqx"/>
		<result column="jb_bdcqdjqx" property="jbBdcqdjqx"/>
		<result column="jb_zxcqdjqx" property="jbZxcqdjqx"/>
		<result column="jb_gzjgjg" property="jbGzjgjg"/>
		<result column="jb_zcd" property="jbZcd"/>
		<result column="jb_gsdjrq" property="jbGsdjrq"/>
		<result column="jb_zcrq" property="jbZcrq"/>
		<result column="jb_sfybgs" property="jbSfybgs"/>
		<result column="jb_gsblzk" property="jbGsblzk"/>
		<result column="jb_gsdjxgzl" property="jbGsdjxgzl"/>
		<result column="jb_sfyz" property="jbSfyz"/>
		<result column="jb_byzly" property="jbByzly"/>
		<result column="jb_zyhy" property="jbZyhy"/>
		<result column="jb_zcdjw" property="jbZcdjw"/>
		<result column="jb_hjcjebz" property="jbHjcjebz"/>
		<result column="jb_czebzxz" property="jbCzebzxz"/>
		<result column="jb_sjzczbbzxz" property="jbSjzczbbzxz"/>
		<result column="jb_blqshzt" property="jbBlqshzt"/>
		<result column="jb_sfztjn" property="jbSfztjn"/>
		<result column="jb_zczbbz" property="jbZczbbz"/>
		<result column="jb_zczbjw" property="jbZczbjw"/>
		<result column="jb_sfzdyjsj" property="jbSfzdyjsj"/>
		<result column="jb_sfsjscdm" property="jbSfsjscdm"/>
		<result column="jb_xzqy" property="jbXzqy"/>
		<result column="jb_shtgrq" property="jbShtgrq"/>
		<result column="jb_dylsh" property="jbDylsh"/>
		<result column="jb_ssgzjgjg" property="jbSsgzjgjg"/>
		<result column="jb_qljh" property="jbQljh"/>
		<result column="jb_hjrjzb" property="jbHjrjzb"/>
		<result column="jb_rjzbbzxz" property="jbRjzbbzxz"/>
		<result column="jb_hjrjzbbz" property="jbHjrjzbbz"/>
		<result column="jb_zczbbzxz" property="jbZczbbzxz"/>
		<result column="jb_gykgcz" property="jbGykgcz"/>
		<result column="jb_gykgczsbs" property="jbGykgczsbs"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="business_nature" property="businessNature"/>
		<result column="jb_gyzb" property="jbGyzb"/>
		<result column="jb_rela" property="jbRela"/>
		<result column="jb_hgqy" property="jbHgqy"/>
		<result column="jb_czrzzjgid" property="jbCzrzzjgid"/>
		<result column="jb_deleted" property="jbDeleted"/>
		<result column="jb_sf_ss" property="jbSfss"/>
		<result column="jb_sf_bb" property="jbSfbb"/>
		<result column="jb_qyjycs" property="jbQyjycs"/>
		<result column="jb_sf_tgqy" property="jbSftgqy"/>
		<result column="jb_sf_czblzt" property="jbSfczblzt"/>
		<result column="jb_sf_kzgs" property="jbSfkzgs"/>
		<result column="jb_zyhy2" property="jbZyhy2"/>
		<result column="jb_zyhy3" property="jbZyhy3"/>
		<result column="jb_qygljc" property="jbQygljc"/>
		<result column="jb_qyslzt" property="jbQyslzt"/>
		<result column="jb_qzwjkjglx" property="jbQzwjkjglx"/>
		<result column="jb_gzwjkjgmx" property="jbGzwjkjgmx"/>
		<result column="jb_yyzzzc" property="jbYyzzzc"/>
		<result column="jb_zyhy1" property="jbZyhy1"/>
		<result column="jb_rjzb" property="jbRjzb"/>
		<result column="jb_rjzbbz" property="jbRjzbbz"/>
		<result column="jb_data_status" property="jbDataStatus"/>
		<result column="jb_myqysbsbz" property="jbMyqysbsbz"/>
		<result column="jb_myqysbs" property="jbMyqysbs"/>
		<result column="jb_wzqysbsbz" property="jbWzqysbsbz"/>
		<result column="jb_wzqysbs" property="jbWzqysbs"/>
		<result column="jb_zrrsbsbz" property="jbZrrsbsbz"/>
		<result column="jb_zrrsbs" property="jbZrrsbs"/>
	</resultMap>

	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.jbxxb.entity.JbxxbVo" extends="baseResultMap">
		<result property="createUserStr" column="createUserStr"/>
		<result property="lastUpdateUserStr" column="lastUpdateUserStr"/>
		<result property="jbJnjwStr" column="jbJnjwStr"/>
		<result property="jbSfybgsStr" column="jbSfybgsStr"/>
		<result property="jbZycqdjqxStr" column="jbZycqdjqxStr"/>
		<result property="jbBdcqdjqxStr" column="jbBdcqdjqxStr"/>
		<result property="jbZxcqdjqxStr" column="jbZxcqdjqxStr"/>
		<result property="jbQylbStr" column="jbQylbStr"/>
		<result property="jbZzxsStr" column="jbZzxsStr"/>
		<result property="jbQyjcStr" column="jbQyjcStr"/>
		<result property="jbJyzkStr" column="jbJyzkStr"/>
		<result property="jbZcdStr" column="jbZcdStr"/>
		<result property="jbZcdjwStr" column="jbZcdjwStr"/>
		<result property="jbGjczqyStr" column="jbGjczqyStr"/>
		<result property="jbSsbmStr" column="jbSsbmStr"/>
		<result property="jbGzjgjgStr" column="jbGzjgjgStr"/>
		<result property="jbSfzyStr" column="jbSfzyStr"/>
		<result property="jbSfztjnStr" column="jbSfztjnStr"/>
		<result property="jbSftsmdgsStr" column="jbSftsmdgsStr"/>
		<result property="jbZcmdStr" column="jbZcmdStr"/>
		<result property="jbSfczgrdcgStr" column="jbSfczgrdcgStr"/>
		<result property="jbRelaStr" column="jbRelaStr"/>
		<result property="jbHgqyStr" column="jbHgqyStr"/>
		<result property="jbSfyzStr" column="jbSfyzStr"/>
		<result property="rgType" column="rgType"/>
		<result property="jbQljhStr" column="jbQljhStr"/>
		<result property="jbQysbsbzxzStr" column="jbQysbsbzxzStr"/>
		<result property="jbSdsbzxzStr" column="jbSdsbzxzStr"/>
		<result property="jbCzebzxzStr" column="jbCzebzxzStr"/>
		<result property="jbSjzczbbzxzStr" column="jbSjzczbbzxzStr"/>
		<result property="jbRjzbbzxzStr" column="jbRjzbbzxzStr"/>
		<result property="jbZczbbzxzStr" column="jbZczbbzxzStr"/>
		<result property="jbZczbbzStr" column="jbZczbbzStr"/>
		<result property="jdZyczr" column="jdZyczr"/>
	</resultMap>

	<resultMap id="resultMapWithZyhy" type="com.zjhc.gzwcq.jbxxb.entity.JbxxbVo" extends="baseResultMapExt">
		<collection property="jbZyhyList" ofType="java.lang.String">
			<result column="jbZyhyList"/>
		</collection>
		<collection property="jbZyhyList2" ofType="java.lang.String">
			<result column="jbZyhyList2"/>
		</collection>
		<collection property="jbZyhyList3" ofType="java.lang.String">
			<result column="jbZyhyList3"/>
		</collection>
		<collection property="jbZyhyList1" ofType="java.lang.String">
			<result column="jbZyhyList1"/>
		</collection>
	</resultMap>

	<sql id="columns">
		id,
		unitid,
		datatime,
		floatorder,
		jb_zczb,
		jb_gjsbs,
		jb_gjsds,
		jb_gyfrsbs,
		jb_gyfrsds,
		jb_gyjdkgsbs,
		jb_gyjdkgsds,
		jb_gysjkzsbs,
		jb_gysjkzsds,
		jb_qtqysbs,
		jb_qtsds,
		jb_hjqysbs,
		jb_hjsds,
		jb_sjblrq,
		jb_hjcze,
		jb_hjsjzcj,
		jb_hjbl,
		jb_qymc,
		jb_zzjgdm,
		jb_zcmd,
		jb_cgrxm,
		jb_jhqlsj,
		jb_sjczr,
		jb_czrzzjgdm,
		jb_hjsjzcjbz,
		jb_gjczsdsbz,
		jb_gjczqysbsbz,
		jb_gyfrczsbsbz,
		jb_gyfrczsdsbz,
		jb_gyjdkgfrsbsbz,
		jb_gyjdkgfrsdsbz,
		jb_gysjkzfrsbsbz,
		jb_gysjkzfrsdsbz,
		jb_qtsbsbz,
		jb_qtqysdsbz,
		jb_hjqysbsbz,
		jb_hjsdsbz,
		jb_shzt,
		jb_sshy,
		jb_sfzy,
		jb_zzxs,
		jb_qylb,
		jb_qyjc,
		jb_ssbm,
		jb_jyzk,
		jb_sftsmdgs,
		jb_sfczgrdcg,
		jb_gjczqy,
		jb_qysbsbzxz,
		jb_sdsbzxz,
		jb_jnjw,
		jb_zycqdjqx,
		jb_bdcqdjqx,
		jb_zxcqdjqx,
		jb_gzjgjg,
		jb_zcd,
		jb_gsdjrq,
		jb_zcrq,
		jb_sfybgs,
		jb_gsblzk,
		jb_gsdjxgzl,
		jb_sfyz,
		jb_byzly,
		jb_zyhy,
		jb_zcdjw,
		jb_hjcjebz,
		jb_czebzxz,
		jb_sjzczbbzxz,
		jb_blqshzt,
		jb_sfztjn,
		jb_zczbbz,
		jb_zczbjw,
		jb_sfzdyjsj,
		jb_sfsjscdm,
		jb_xzqy,
		jb_shtgrq,
		jb_dylsh,
		jb_ssgzjgjg,
		jb_qljh,
		jb_hjrjzb,
		jb_rjzbbzxz,
		jb_hjrjzbbz,
		jb_zczbbzxz,
		jb_gykgcz,
		jb_gykgczsbs,
		create_user,
		create_time,
		last_update_user,
		last_update_time,
		business_nature,
		jb_gyzb,
		jb_rela,
		jb_hgqy,
		jb_czrzzjgid,
		jb_deleted,
		jb_sf_ss,
		jb_sf_bb,
		jb_qyjycs,
		jb_sf_tgqy,
		jb_sf_czblzt,
		jb_sf_kzgs,
		jb_zyhy2,
		 jb_ZYHY3,
		jb_qygljc,
		jb_qyslzt,
		jb_qzwjkjglx,
		jb_gzwjkjgmx,
		jb_yyzzzc,
		jb_zyhy1,
		jb_rjzbbz,
		jb_rjzb,
		jb_data_status,
		jb_myqysbsbz,
  		jb_myqysbs,
  		jb_wzqysbsbz,
  		jb_wzqysbs,
  		jb_zrrsbsbz,
  		jb_zrrsbs
	</sql>

	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id,
		t.unitid,
		t.datatime,
		t.floatorder,
		t.jb_zczb,
		t.jb_gjsbs,
		t.jb_gjsds,
		t.jb_gyfrsbs,
		t.jb_gyfrsds,
		t.jb_gyjdkgsbs,
		t.jb_gyjdkgsds,
		t.jb_gysjkzsbs,
		t.jb_gysjkzsds,
		t.jb_qtqysbs,
		t.jb_qtsds,
		t.jb_hjqysbs,
		t.jb_hjsds,
		t.jb_sjblrq,
		t.jb_hjcze,
		t.jb_hjsjzcj,
		t.jb_hjbl,
		t.jb_qymc,
		t.jb_zzjgdm,
		t.jb_zcmd,
		t.jb_cgrxm,
		t.jb_jhqlsj,
		t.jb_sjczr,
		t.jb_czrzzjgdm,
		t.jb_hjsjzcjbz,
		t.jb_gjczsdsbz,
		t.jb_gjczqysbsbz,
		t.jb_gyfrczsbsbz,
		t.jb_gyfrczsdsbz,
		t.jb_gyjdkgfrsbsbz,
		t.jb_gyjdkgfrsdsbz,
		t.jb_gysjkzfrsbsbz,
		t.jb_gysjkzfrsdsbz,
		t.jb_qtsbsbz,
		t.jb_qtqysdsbz,
		t.jb_hjqysbsbz,
		t.jb_hjsdsbz,
		t.jb_shzt,
		t.jb_sshy,
		t.jb_sfzy,
		t.jb_zzxs,
		t.jb_qylb,
		t.jb_qyjc,
		t.jb_ssbm,
		t.jb_jyzk,
		t.jb_sftsmdgs,
		t.jb_sfczgrdcg,
		t.jb_gjczqy,
		t.jb_qysbsbzxz,
		t.jb_sdsbzxz,
		t.jb_jnjw,
		t.jb_zycqdjqx,
		t.jb_bdcqdjqx,
		t.jb_zxcqdjqx,
		t.jb_gzjgjg,
		t.jb_zcd,
		t.jb_gsdjrq,
		t.jb_zcrq,
		t.jb_sfybgs,
		t.jb_gsblzk,
		t.jb_gsdjxgzl,
		t.jb_sfyz,
		t.jb_byzly,
		t.jb_zyhy,
		t.jb_zcdjw,
		t.jb_hjcjebz,
		t.jb_czebzxz,
		t.jb_sjzczbbzxz,
		t.jb_blqshzt,
		t.jb_sfztjn,
		t.jb_zczbbz,
		t.jb_zczbjw,
		t.jb_sfzdyjsj,
		t.jb_sfsjscdm,
		t.jb_xzqy,
		t.jb_shtgrq,
		t.jb_dylsh,
		t.jb_ssgzjgjg,
		t.jb_qljh,
		t.jb_hjrjzb,
		t.jb_rjzbbzxz,
		t.jb_hjrjzbbz,
		t.jb_zczbbzxz,
		t.jb_gykgcz,
		t.jb_gykgczsbs,
		t.create_user,
		t.create_time,
		t.last_update_user,
		t.last_update_time,
		t.business_nature,
		t.jb_gyzb,
		t.jb_rela,
		t.jb_hgqy,
		t.jb_czrzzjgid,
		t.jb_deleted,
		t.jb_sf_ss,
		t.jb_sf_bb,
		t.jb_qyjycs,
		t.jb_sf_tgqy,
		t.jb_sf_czblzt,
		t.jb_sf_kzgs,
		t.jb_zyhy2,
		 t.jb_ZYHY3,
		t.jb_qygljc,
		t.jb_qyslzt,
		t.jb_qzwjkjglx,
		t.jb_gzwjkjgmx,
		t.jb_yyzzzc,
		t.jb_zyhy1,
		t.jb_rjzbbz,
		t.jb_rjzb,
		t.jb_data_status,
		t.jb_myqysbsbz,
  		t.jb_myqysbs,
  		t.jb_wzqysbsbz,
  		t.jb_wzqysbs,
  		t.jb_zrrsbsbz,
  		t.jb_zrrsbs
	</sql>

	<!--带别名的列2-->
	<sql id="columnsAlias2">
		t.id,
		t.unitid,
		t.datatime,
		t.floatorder,
		ifnull(t.jb_zczb,hh.hh_rjcze) jb_zczb,
		t.jb_gjsbs,
		t.jb_gjsds,
		t.jb_gyfrsbs,
		t.jb_gyfrsds,
		t.jb_gyjdkgsbs,
		t.jb_gyjdkgsds,
		t.jb_gysjkzsbs,
		t.jb_gysjkzsds,
		t.jb_qtqysbs,
		t.jb_qtsds,
		t.jb_hjqysbs,
		t.jb_hjsds,
		t.jb_sjblrq,
		t.jb_hjcze,
		t.jb_hjsjzcj,
		t.jb_hjbl,
		t.jb_qymc,
		t.jb_zzjgdm,
		t.jb_zcmd,
		t.jb_cgrxm,
		t.jb_jhqlsj,
		t.jb_sjczr,
		t.jb_czrzzjgdm,
		t.jb_hjsjzcjbz,
		t.jb_gjczsdsbz,
		t.jb_gjczqysbsbz,
		t.jb_gyfrczsbsbz,
		t.jb_gyfrczsdsbz,
		t.jb_gyjdkgfrsbsbz,
		t.jb_gyjdkgfrsdsbz,
		t.jb_gysjkzfrsbsbz,
		t.jb_gysjkzfrsdsbz,
		t.jb_qtsbsbz,
		t.jb_qtqysdsbz,
		t.jb_hjqysbsbz,
		t.jb_hjsdsbz,
		t.jb_shzt,
		t.jb_sshy,
		t.jb_sfzy,
		t.jb_zzxs,
		t.jb_qylb,
		t.jb_qyjc,
		t.jb_ssbm,
		t.jb_jyzk,
		t.jb_sftsmdgs,
		t.jb_sfczgrdcg,
		t.jb_gjczqy,
		t.jb_qysbsbzxz,
		t.jb_sdsbzxz,
		t.jb_jnjw,
		t.jb_zycqdjqx,
		t.jb_bdcqdjqx,
		t.jb_zxcqdjqx,
		t.jb_gzjgjg,
		t.jb_zcd,
		t.jb_gsdjrq,
		t.jb_zcrq,
		t.jb_sfybgs,
		t.jb_gsblzk,
		t.jb_gsdjxgzl,
		t.jb_sfyz,
		t.jb_byzly,
		t.jb_zyhy,
		t.jb_zcdjw,
		t.jb_hjcjebz,
		t.jb_czebzxz,
		t.jb_sjzczbbzxz,
		t.jb_blqshzt,
		t.jb_sfztjn,
		t.jb_zczbbz,
		t.jb_zczbjw,
		t.jb_sfzdyjsj,
		t.jb_sfsjscdm,
		t.jb_xzqy,
		t.jb_shtgrq,
		t.jb_dylsh,
		t.jb_ssgzjgjg,
		t.jb_qljh,
		t.jb_hjrjzb,
		t.jb_rjzbbzxz,
		t.jb_hjrjzbbz,
		t.jb_zczbbzxz,
		t.jb_gykgcz,
		t.jb_gykgczsbs,
		t.create_user,
		t.create_time,
		t.last_update_user,
		t.last_update_time,
		t.business_nature,
		t.jb_gyzb,
		t.jb_rela,
		t.jb_hgqy,
		t.jb_czrzzjgid,
		t.jb_deleted,
		t.jb_data_status,
		t.jb_myqysbsbz,
  		t.jb_myqysbs,
  		t.jb_wzqysbsbz,
  		t.jb_wzqysbs,
  		t.jb_zrrsbsbz,
  		t.jb_zrrsbs
	</sql>

	<sql id="vals">
		#{id},
		#{unitid},
		#{datatime},
		#{floatorder},
		#{jbZczb},
		#{jbGjsbs},
		#{jbGjsds},
		#{jbGyfrsbs},
		#{jbGyfrsds},
		#{jbGyjdkgsbs},
		#{jbGyjdkgsds},
		#{jbGysjkzsbs},
		#{jbGysjkzsds},
		#{jbQtqysbs},
		#{jbQtsds},
		#{jbHjqysbs},
		#{jbHjsds},
		#{jbSjblrq},
		#{jbHjcze},
		#{jbHjsjzcj},
		#{jbHjbl},
		#{jbQymc},
		#{jbZzjgdm},
		#{jbZcmd},
		#{jbCgrxm},
		#{jbJhqlsj},
		#{jbSjczr},
		#{jbCzrzzjgdm},
		#{jbHjsjzcjbz},
		#{jbGjczsdsbz},
		#{jbGjczqysbsbz},
		#{jbGyfrczsbsbz},
		#{jbGyfrczsdsbz},
		#{jbGyjdkgfrsbsbz},
		#{jbGyjdkgfrsdsbz},
		#{jbGysjkzfrsbsbz},
		#{jbGysjkzfrsdsbz},
		#{jbQtsbsbz},
		#{jbQtqysdsbz},
		#{jbHjqysbsbz},
		#{jbHjsdsbz},
		#{jbShzt},
		#{jbSshy},
		#{jbSfzy},
		#{jbZzxs},
		#{jbQylb},
		#{jbQyjc},
		#{jbSsbm},
		#{jbJyzk},
		#{jbSftsmdgs},
		#{jbSfczgrdcg},
		#{jbGjczqy},
		#{jbQysbsbzxz},
		#{jbSdsbzxz},
		#{jbJnjw},
		#{jbZycqdjqx},
		#{jbBdcqdjqx},
		#{jbZxcqdjqx},
		#{jbGzjgjg},
		#{jbZcd},
		#{jbGsdjrq},
		#{jbZcrq},
		#{jbSfybgs},
		#{jbGsblzk},
		#{jbGsdjxgzl},
		#{jbSfyz},
		#{jbByzly},
		#{jbZyhy},
		#{jbZcdjw},
		#{jbHjcjebz},
		#{jbCzebzxz},
		#{jbSjzczbbzxz},
		#{jbBlqshzt},
		#{jbSfztjn},
		#{jbZczbbz},
		#{jbZczbjw},
		#{jbSfzdyjsj},
		#{jbSfsjscdm},
		#{jbXzqy},
		#{jbShtgrq},
		#{jbDylsh},
		#{jbSsgzjgjg},
		#{jbQljh},
		#{jbHjrjzb},
		#{jbRjzbbzxz},
		#{jbHjrjzbbz},
		#{jbZczbbzxz},
		#{jbGykgcz},
		#{jbGykgczsbs},
		#{createUser},
		#{createTime},
		#{lastUpdateUser},
		#{lastUpdateTime},
		#{businessNature},
		#{jbGyzb},
		#{jbRela},
		#{jbHgqy},
		#{jbCzrzzjgid},
		#{jbDeleted},
		#{jbSfss},
		#{jbSfbb},
		#{jbQyjycs},
		#{jbSftgqy},
		#{jbSfczblzt},
		#{jbSfkzgs},
		#{jbZyhy2},
		#{jbZyhy3},
		#{jbQygljc},
		#{jbQyslzt},
		#{jbQzwjkjglx},
		#{jbGzwjkjgmx},
		#{jbYyzzzc},
		#{jbZyhy1},
		#{jbRjzbbz},
		#{jbRjzb},
		#{jbDataStatus},
		#{jbMyqysbsbz},
		#{jbMyqysbs},
		#{jbWzqysbsbz},
		#{jbWzqysbs},
		#{jbZrrsbsbz},
		#{jbZrrsbs}
	</sql>

	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="jbZczb != null">
			and t.jb_zczb = #{jbZczb}
		</if>
		<if test="jbGjsbs != null">
			and t.jb_gjsbs = #{jbGjsbs}
		</if>
		<if test="jbGjsds != null">
			and t.jb_gjsds = #{jbGjsds}
		</if>
		<if test="jbGyfrsbs != null">
			and t.jb_gyfrsbs = #{jbGyfrsbs}
		</if>
		<if test="jbGyfrsds != null">
			and t.jb_gyfrsds = #{jbGyfrsds}
		</if>
		<if test="jbGyjdkgsbs != null">
			and t.jb_gyjdkgsbs = #{jbGyjdkgsbs}
		</if>
		<if test="jbGyjdkgsds != null">
			and t.jb_gyjdkgsds = #{jbGyjdkgsds}
		</if>
		<if test="jbGysjkzsbs != null">
			and t.jb_gysjkzsbs = #{jbGysjkzsbs}
		</if>
		<if test="jbGysjkzsds != null">
			and t.jb_gysjkzsds = #{jbGysjkzsds}
		</if>
		<if test="jbQtqysbs != null">
			and t.jb_qtqysbs = #{jbQtqysbs}
		</if>
		<if test="jbQtsds != null">
			and t.jb_qtsds = #{jbQtsds}
		</if>
		<if test="jbHjqysbs != null">
			and t.jb_hjqysbs = #{jbHjqysbs}
		</if>
		<if test="jbHjsds != null">
			and t.jb_hjsds = #{jbHjsds}
		</if>
		<if test="jbSjblrq != null">
			and t.jb_sjblrq = #{jbSjblrq}
		</if>
		<if test="jbHjcze != null">
			and t.jb_hjcze = #{jbHjcze}
		</if>
		<if test="jbHjsjzcj != null">
			and t.jb_hjsjzcj = #{jbHjsjzcj}
		</if>
		<if test="jbHjbl != null">
			and t.jb_hjbl = #{jbHjbl}
		</if>
		<if test="jbQymc != null and jbQymc != ''">
			and t.jb_qymc = #{jbQymc}
		</if>
		<if test="jbZzjgdm != null and jbZzjgdm != ''">
			and t.jb_zzjgdm = #{jbZzjgdm}
		</if>
		<if test="jbZcmd != null and jbZcmd != ''">
			and t.jb_zcmd = #{jbZcmd}
		</if>
		<if test="jbCgrxm != null and jbCgrxm != ''">
			and t.jb_cgrxm = #{jbCgrxm}
		</if>
		<if test="jbJhqlsj != null">
			and t.jb_jhqlsj = #{jbJhqlsj}
		</if>
		<if test="jbSjczr != null and jbSjczr != ''">
			and t.jb_sjczr = #{jbSjczr}
		</if>
		<if test="jbCzrzzjgdm != null and jbCzrzzjgdm != ''">
			and t.jb_czrzzjgdm = #{jbCzrzzjgdm}
		</if>
		<if test="jbHjsjzcjbz != null">
			and t.jb_hjsjzcjbz = #{jbHjsjzcjbz}
		</if>
		<if test="jbGjczsdsbz != null">
			and t.jb_gjczsdsbz = #{jbGjczsdsbz}
		</if>
		<if test="jbGjczqysbsbz != null">
			and t.jb_gjczqysbsbz = #{jbGjczqysbsbz}
		</if>
		<if test="jbGyfrczsbsbz != null">
			and t.jb_gyfrczsbsbz = #{jbGyfrczsbsbz}
		</if>
		<if test="jbGyfrczsdsbz != null">
			and t.jb_gyfrczsdsbz = #{jbGyfrczsdsbz}
		</if>
		<if test="jbGyjdkgfrsbsbz != null">
			and t.jb_gyjdkgfrsbsbz = #{jbGyjdkgfrsbsbz}
		</if>
		<if test="jbGyjdkgfrsdsbz != null">
			and t.jb_gyjdkgfrsdsbz = #{jbGyjdkgfrsdsbz}
		</if>
		<if test="jbGysjkzfrsbsbz != null">
			and t.jb_gysjkzfrsbsbz = #{jbGysjkzfrsbsbz}
		</if>
		<if test="jbGysjkzfrsdsbz != null">
			and t.jb_gysjkzfrsdsbz = #{jbGysjkzfrsdsbz}
		</if>
		<if test="jbQtsbsbz != null">
			and t.jb_qtsbsbz = #{jbQtsbsbz}
		</if>
		<if test="jbQtqysdsbz != null">
			and t.jb_qtqysdsbz = #{jbQtqysdsbz}
		</if>
		<if test="jbHjqysbsbz != null">
			and t.jb_hjqysbsbz = #{jbHjqysbsbz}
		</if>
		<if test="jbHjsdsbz != null">
			and t.jb_hjsdsbz = #{jbHjsdsbz}
		</if>
		<if test="jbShzt != null">
			and t.jb_shzt = #{jbShzt}
		</if>
		<if test="jbSshy != null and jbSshy != ''">
			and t.jb_sshy = #{jbSshy}
		</if>
		<if test="jbSfzy != null and jbSfzy != ''">
			and t.jb_sfzy = #{jbSfzy}
		</if>
		<if test="jbZzxs != null and jbZzxs != ''">
			and t.jb_zzxs = #{jbZzxs}
		</if>
		<if test="jbQylb != null and jbQylb != ''">
			and t.jb_qylb = #{jbQylb}
		</if>
		<if test="jbQyjc != null and jbQyjc != ''">
			and t.jb_qyjc = #{jbQyjc}
		</if>
		<if test="jbSsbm != null and jbSsbm != ''">
			and t.jb_ssbm = #{jbSsbm}
		</if>
		<if test="jbJyzk != null and jbJyzk != ''">
			and t.jb_jyzk = #{jbJyzk}
		</if>
		<if test="jbSftsmdgs != null and jbSftsmdgs != ''">
			and t.jb_sftsmdgs = #{jbSftsmdgs}
		</if>
		<if test="jbSfczgrdcg != null and jbSfczgrdcg != ''">
			and t.jb_sfczgrdcg = #{jbSfczgrdcg}
		</if>
		<if test="jbGjczqy != null and jbGjczqy != ''">
			and t.jb_gjczqy = #{jbGjczqy}
		</if>
		<if test="jbQysbsbzxz != null and jbQysbsbzxz != ''">
			and t.jb_qysbsbzxz = #{jbQysbsbzxz}
		</if>
		<if test="jbSdsbzxz != null and jbSdsbzxz != ''">
			and t.jb_sdsbzxz = #{jbSdsbzxz}
		</if>
		<if test="jbJnjw != null and jbJnjw != ''">
			and t.jb_jnjw = #{jbJnjw}
		</if>
		<if test="jbZycqdjqx != null and jbZycqdjqx != ''">
			and t.jb_zycqdjqx = #{jbZycqdjqx}
		</if>
		<if test="jbBdcqdjqx != null and jbBdcqdjqx != ''">
			and t.jb_bdcqdjqx = #{jbBdcqdjqx}
		</if>
		<if test="jbZxcqdjqx != null and jbZxcqdjqx != ''">
			and t.jb_zxcqdjqx = #{jbZxcqdjqx}
		</if>
		<if test="jbGzjgjg != null and jbGzjgjg != ''">
			and t.jb_gzjgjg = #{jbGzjgjg}
		</if>
		<if test="jbZcd != null and jbZcd != ''">
			and t.jb_zcd = #{jbZcd}
		</if>
		<if test="jbGsdjrq != null">
			and t.jb_gsdjrq = #{jbGsdjrq}
		</if>
		<if test="jbZcrq != null">
			and t.jb_zcrq = #{jbZcrq}
		</if>
		<if test="jbSfybgs != null and jbSfybgs != ''">
			and t.jb_sfybgs = #{jbSfybgs}
		</if>
		<if test="jbGsblzk != null and jbGsblzk != ''">
			and t.jb_gsblzk = #{jbGsblzk}
		</if>
		<if test="jbGsdjxgzl != null and jbGsdjxgzl != ''">
			and t.jb_gsdjxgzl = #{jbGsdjxgzl}
		</if>
		<if test="jbSfyz != null and jbSfyz != ''">
			and t.jb_sfyz = #{jbSfyz}
		</if>
		<if test="jbByzly != null and jbByzly != ''">
			and t.jb_byzly = #{jbByzly}
		</if>
		<if test="jbZyhy != null and jbZyhy != ''">
			and t.jb_zyhy = #{jbZyhy}
		</if>
		<if test="jbZcdjw != null and jbZcdjw != ''">
			and t.jb_zcdjw = #{jbZcdjw}
		</if>
		<if test="jbHjcjebz != null">
			and t.jb_hjcjebz = #{jbHjcjebz}
		</if>
		<if test="jbCzebzxz != null and jbCzebzxz != ''">
			and t.jb_czebzxz = #{jbCzebzxz}
		</if>
		<if test="jbSjzczbbzxz != null and jbSjzczbbzxz != ''">
			and t.jb_sjzczbbzxz = #{jbSjzczbbzxz}
		</if>
		<if test="jbBlqshzt != null">
			and t.jb_blqshzt = #{jbBlqshzt}
		</if>
		<if test="jbSfztjn != null and jbSfztjn != ''">
			and t.jb_sfztjn = #{jbSfztjn}
		</if>
		<if test="jbZczbbz != null and jbZczbbz != ''">
			and t.jb_zczbbz = #{jbZczbbz}
		</if>
		<if test="jbZczbjw != null">
			and t.jb_zczbjw = #{jbZczbjw}
		</if>
		<if test="jbSfzdyjsj != null and jbSfzdyjsj != ''">
			and t.jb_sfzdyjsj = #{jbSfzdyjsj}
		</if>
		<if test="jbSfsjscdm != null and jbSfsjscdm != ''">
			and t.jb_sfsjscdm = #{jbSfsjscdm}
		</if>
		<if test="jbXzqy != null and jbXzqy != ''">
			and t.jb_xzqy = #{jbXzqy}
		</if>
		<if test="jbShtgrq != null and jbShtgrq != ''">
			and t.jb_shtgrq = #{jbShtgrq}
		</if>
		<if test="jbDylsh != null and jbDylsh != ''">
			and t.jb_dylsh = #{jbDylsh}
		</if>
		<if test="jbSsgzjgjg != null and jbSsgzjgjg != ''">
			and t.jb_ssgzjgjg = #{jbSsgzjgjg}
		</if>
		<if test="jbQljh != null and jbQljh != ''">
			and t.jb_qljh = #{jbQljh}
		</if>
		<if test="jbHjrjzb != null">
			and t.jb_hjrjzb = #{jbHjrjzb}
		</if>
		<if test="jbRjzbbzxz != null and jbRjzbbzxz != ''">
			and t.jb_rjzbbzxz = #{jbRjzbbzxz}
		</if>
		<if test="jbHjrjzbbz != null">
			and t.jb_hjrjzbbz = #{jbHjrjzbbz}
		</if>
		<if test="jbZczbbzxz != null and jbZczbbzxz != ''">
			and t.jb_zczbbzxz = #{jbZczbbzxz}
		</if>
		<if test="jbGykgcz != null">
			and t.jb_gykgcz = #{jbGykgcz}
		</if>
		<if test="jbGykgczsbs != null">
			and t.jb_gykgczsbs = #{jbGykgczsbs}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="businessNature != null and businessNature != ''">
			and t.business_nature = #{businessNature}
		</if>
		<if test="jbGyzb != null">
			and t.jb_gyzb = #{jbGyzb}
		</if>
		<if test="jbRela != null and jbRela != ''">
			and t.jb_rela = #{jbRela}
		</if>
		<if test="jbHgqy != null and jbHgqy != ''">
			and t.jb_hgqy = #{jbHgqy}
		</if>
		<if test="jbCzrzzjgid != null and jbCzrzzjgid != ''">
			and t.jb_czrzzjgid = #{jbCzrzzjgid}
		</if>
		<if test="jbDeleted != null and jbDeleted != ''">
			and t.jb_deleted = #{jbDeleted}
		</if>
		<if test="jbDataStatus != null and jbDataStatus != ''">
			and t.jb_data_status = #{jbDataStatus}
		</if>
	</sql>

	<sql id="whereSql2">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="jbZczb != null">
			and t.jb_zczb = #{jbZczb}
		</if>
		<if test="jbGjsbs != null">
			and t.jb_gjsbs = #{jbGjsbs}
		</if>
		<if test="jbGjsds != null">
			and t.jb_gjsds = #{jbGjsds}
		</if>
		<if test="jbGyfrsbs != null">
			and t.jb_gyfrsbs = #{jbGyfrsbs}
		</if>
		<if test="jbGyfrsds != null">
			and t.jb_gyfrsds = #{jbGyfrsds}
		</if>
		<if test="jbGyjdkgsbs != null">
			and t.jb_gyjdkgsbs = #{jbGyjdkgsbs}
		</if>
		<if test="jbGyjdkgsds != null">
			and t.jb_gyjdkgsds = #{jbGyjdkgsds}
		</if>
		<if test="jbGysjkzsbs != null">
			and t.jb_gysjkzsbs = #{jbGysjkzsbs}
		</if>
		<if test="jbGysjkzsds != null">
			and t.jb_gysjkzsds = #{jbGysjkzsds}
		</if>
		<if test="jbQtqysbs != null">
			and t.jb_qtqysbs = #{jbQtqysbs}
		</if>
		<if test="jbQtsds != null">
			and t.jb_qtsds = #{jbQtsds}
		</if>
		<if test="jbHjqysbs != null">
			and t.jb_hjqysbs = #{jbHjqysbs}
		</if>
		<if test="jbHjsds != null">
			and t.jb_hjsds = #{jbHjsds}
		</if>
		<if test="jbSjblrq != null">
			and t.jb_sjblrq = #{jbSjblrq}
		</if>
		<if test="jbHjcze != null">
			and t.jb_hjcze = #{jbHjcze}
		</if>
		<if test="jbHjsjzcj != null">
			and t.jb_hjsjzcj = #{jbHjsjzcj}
		</if>
		<if test="jbHjbl != null">
			and t.jb_hjbl = #{jbHjbl}
		</if>
		<if test="jbQymc != null and jbQymc != ''">
			and t.jb_qymc like concat('%',#{jbQymc},'%')
		</if>
		<if test="jbZzjgdm != null and jbZzjgdm != ''">
			and t.jb_zzjgdm like concat('%',#{jbZzjgdm},'%')
		</if>
		<if test="jbZcmd != null and jbZcmd != ''">
			and t.jb_zcmd = #{jbZcmd}
		</if>
		<if test="jbCgrxm != null and jbCgrxm != ''">
			and t.jb_cgrxm = #{jbCgrxm}
		</if>
		<if test="jbJhqlsj != null">
			and t.jb_jhqlsj = #{jbJhqlsj}
		</if>
		<if test="jbSjczr != null and jbSjczr != ''">
			and t.jb_sjczr = #{jbSjczr}
		</if>
		<if test="jbCzrzzjgdm != null and jbCzrzzjgdm != ''">
			and t.jb_czrzzjgdm = #{jbCzrzzjgdm}
		</if>
		<if test="jbHjsjzcjbz != null">
			and t.jb_hjsjzcjbz = #{jbHjsjzcjbz}
		</if>
		<if test="jbGjczsdsbz != null">
			and t.jb_gjczsdsbz = #{jbGjczsdsbz}
		</if>
		<if test="jbGjczqysbsbz != null">
			and t.jb_gjczqysbsbz = #{jbGjczqysbsbz}
		</if>
		<if test="jbGyfrczsbsbz != null">
			and t.jb_gyfrczsbsbz = #{jbGyfrczsbsbz}
		</if>
		<if test="jbGyfrczsdsbz != null">
			and t.jb_gyfrczsdsbz = #{jbGyfrczsdsbz}
		</if>
		<if test="jbGyjdkgfrsbsbz != null">
			and t.jb_gyjdkgfrsbsbz = #{jbGyjdkgfrsbsbz}
		</if>
		<if test="jbGyjdkgfrsdsbz != null">
			and t.jb_gyjdkgfrsdsbz = #{jbGyjdkgfrsdsbz}
		</if>
		<if test="jbGysjkzfrsbsbz != null">
			and t.jb_gysjkzfrsbsbz = #{jbGysjkzfrsbsbz}
		</if>
		<if test="jbGysjkzfrsdsbz != null">
			and t.jb_gysjkzfrsdsbz = #{jbGysjkzfrsdsbz}
		</if>
		<if test="jbQtsbsbz != null">
			and t.jb_qtsbsbz = #{jbQtsbsbz}
		</if>
		<if test="jbQtqysdsbz != null">
			and t.jb_qtqysdsbz = #{jbQtqysdsbz}
		</if>
		<if test="jbHjqysbsbz != null">
			and t.jb_hjqysbsbz = #{jbHjqysbsbz}
		</if>
		<if test="jbHjsdsbz != null">
			and t.jb_hjsdsbz = #{jbHjsdsbz}
		</if>
		<if test="jbShzt != null">
			and t.jb_shzt = #{jbShzt}
		</if>
		<if test="jbSshy != null and jbSshy != ''">
			and t.jb_sshy = #{jbSshy}
		</if>
		<if test="jbSfzy != null and jbSfzy != ''">
			and t.jb_sfzy = #{jbSfzy}
		</if>
		<if test="jbZzxs != null and jbZzxs != ''">
			and t.jb_zzxs = #{jbZzxs}
		</if>
		<if test="jbQylb != null and jbQylb != ''">
			and t.jb_qylb = #{jbQylb}
		</if>
		<if test="jbQyjc != null and jbQyjc != ''">
			and t.jb_qyjc = #{jbQyjc}
		</if>
		<if test="jbSsbm != null and jbSsbm != ''">
			and t.jb_ssbm = #{jbSsbm}
		</if>
		<if test="jbJyzk != null and jbJyzk != ''">
			and t.jb_jyzk = #{jbJyzk}
		</if>
		<if test="jbSftsmdgs != null and jbSftsmdgs != ''">
			and t.jb_sftsmdgs = #{jbSftsmdgs}
		</if>
		<if test="jbSfczgrdcg != null and jbSfczgrdcg != ''">
			and t.jb_sfczgrdcg = #{jbSfczgrdcg}
		</if>
		<if test="jbGjczqy != null and jbGjczqy != ''">
			and t.jb_gjczqy = #{jbGjczqy}
		</if>
		<if test="jbQysbsbzxz != null and jbQysbsbzxz != ''">
			and t.jb_qysbsbzxz = #{jbQysbsbzxz}
		</if>
		<if test="jbSdsbzxz != null and jbSdsbzxz != ''">
			and t.jb_sdsbzxz = #{jbSdsbzxz}
		</if>
		<if test="jbJnjw != null and jbJnjw != ''">
			and t.jb_jnjw = #{jbJnjw}
		</if>
		<if test="jbZycqdjqx != null and jbZycqdjqx != ''">
			and t.jb_zycqdjqx = #{jbZycqdjqx}
		</if>
		<if test="jbBdcqdjqx != null and jbBdcqdjqx != ''">
			and t.jb_bdcqdjqx = #{jbBdcqdjqx}
		</if>
		<if test="jbZxcqdjqx != null and jbZxcqdjqx != ''">
			and t.jb_zxcqdjqx = #{jbZxcqdjqx}
		</if>
		<if test="jbGzjgjg != null and jbGzjgjg != ''">
			and t.jb_gzjgjg = #{jbGzjgjg}
		</if>
		<if test="jbZcd != null and jbZcd != ''">
			and t.jb_zcd = #{jbZcd}
		</if>
		<if test="jbGsdjrq != null">
			and t.jb_gsdjrq = #{jbGsdjrq}
		</if>
		<if test="jbZcrq != null">
			and t.jb_zcrq = #{jbZcrq}
		</if>
		<if test="jbSfybgs != null and jbSfybgs != ''">
			and t.jb_sfybgs = #{jbSfybgs}
		</if>
		<if test="jbGsblzk != null and jbGsblzk != ''">
			and t.jb_gsblzk = #{jbGsblzk}
		</if>
		<if test="jbGsdjxgzl != null and jbGsdjxgzl != ''">
			and t.jb_gsdjxgzl = #{jbGsdjxgzl}
		</if>
		<if test="jbSfyz != null and jbSfyz != ''">
			and t.jb_sfyz = #{jbSfyz}
		</if>
		<if test="jbByzly != null and jbByzly != ''">
			and t.jb_byzly = #{jbByzly}
		</if>
		<if test="jbZyhy != null and jbZyhy != ''">
			and t.jb_zyhy = #{jbZyhy}
		</if>
		<if test="jbZcdjw != null and jbZcdjw != ''">
			and t.jb_zcdjw = #{jbZcdjw}
		</if>
		<if test="jbHjcjebz != null">
			and t.jb_hjcjebz = #{jbHjcjebz}
		</if>
		<if test="jbCzebzxz != null and jbCzebzxz != ''">
			and t.jb_czebzxz = #{jbCzebzxz}
		</if>
		<if test="jbSjzczbbzxz != null and jbSjzczbbzxz != ''">
			and t.jb_sjzczbbzxz = #{jbSjzczbbzxz}
		</if>
		<if test="jbBlqshzt != null">
			and t.jb_blqshzt = #{jbBlqshzt}
		</if>
		<if test="jbSfztjn != null and jbSfztjn != ''">
			and t.jb_sfztjn = #{jbSfztjn}
		</if>
		<if test="jbZczbbz != null and jbZczbbz != ''">
			and t.jb_zczbbz = #{jbZczbbz}
		</if>
		<if test="jbZczbjw != null">
			and t.jb_zczbjw = #{jbZczbjw}
		</if>
		<if test="jbSfzdyjsj != null and jbSfzdyjsj != ''">
			and t.jb_sfzdyjsj = #{jbSfzdyjsj}
		</if>
		<if test="jbSfsjscdm != null and jbSfsjscdm != ''">
			and t.jb_sfsjscdm = #{jbSfsjscdm}
		</if>
		<if test="jbXzqy != null and jbXzqy != ''">
			and t.jb_xzqy = #{jbXzqy}
		</if>
		<if test="jbShtgrq != null and jbShtgrq != ''">
			and t.jb_shtgrq = #{jbShtgrq}
		</if>
		<if test="jbDylsh != null and jbDylsh != ''">
			and t.jb_dylsh = #{jbDylsh}
		</if>
		<if test="jbSsgzjgjg != null and jbSsgzjgjg != ''">
			and t.jb_ssgzjgjg = #{jbSsgzjgjg}
		</if>
		<if test="jbQljh != null and jbQljh != ''">
			and t.jb_qljh = #{jbQljh}
		</if>
		<if test="jbHjrjzb != null">
			and t.jb_hjrjzb = #{jbHjrjzb}
		</if>
		<if test="jbRjzbbzxz != null and jbRjzbbzxz != ''">
			and t.jb_rjzbbzxz = #{jbRjzbbzxz}
		</if>
		<if test="jbHjrjzbbz != null">
			and t.jb_hjrjzbbz = #{jbHjrjzbbz}
		</if>
		<if test="jbZczbbzxz != null and jbZczbbzxz != ''">
			and t.jb_zczbbzxz = #{jbZczbbzxz}
		</if>
		<if test="jbGykgcz != null">
			and t.jb_gykgcz = #{jbGykgcz}
		</if>
		<if test="jbGykgczsbs != null">
			and t.jb_gykgczsbs = #{jbGykgczsbs}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="businessNature != null and businessNature != ''">
			<choose>
				<when test="businessNature == '2'.toString()">
					and t.business_nature = #{businessNature}
				</when>
				<otherwise>
					and (t.business_nature = #{businessNature} or t.business_nature is null or t.business_nature = '')
				</otherwise>
			</choose>
		</if>
		<if test="jbGyzb != null">
			and t.jb_gyzb = #{jbGyzb}
		</if>
		<if test="jbRela != null and jbRela != ''">
			and t.jb_rela = #{jbRela}
		</if>
		<if test="jbHgqy != null and jbHgqy != ''">
			and t.jb_hgqy = #{jbHgqy}
		</if>
		<if test="jbCzrzzjgid != null and jbCzrzzjgid != ''">
			and t.jb_czrzzjgid = #{jbCzrzzjgid}
		</if>
		<if test="jbDeleted != null and jbDeleted != ''">
			and t.jb_deleted = #{jbDeleted}
		</if>
		<if test="jbDataStatus != null and jbDataStatus != ''">
			and t.jb_data_status = #{jbDataStatus}
		</if>
	</sql>



	<insert id="insert" parameterType="com.zjhc.gzwcq.jbxxb.entity.Jbxxb" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
			select replace(uuid(), '-', '') as id from dual
		</selectKey>
		insert into cq_jbxxb (<include refid="columns" />)
		values (<include refid="vals" />)
	</insert>

	<!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_jbxxb set jb_deleted = 'Y' where
		id in
		<foreach collection="jbxxbs" open="(" close=")" separator="," item="id">
			#{id}
		</foreach>
	</update>

	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_jbxxb set jb_deleted = 'Y' where id = #{id}
	</update>

	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_jbxxb  where
		id in
		<foreach collection="jbxxbs" open="(" close=")" separator="," item="id">
			#{id}
		</foreach>
	</delete>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_jbxxb  where id = #{id}
	</delete>

	<select id="selectJbxxbByPrimaryKey" resultMap="resultMapWithZyhy">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr,
		sd1.text jbJnjwStr,sd2.text jbSfybgsStr,sd3.text jbZycqdjqxStr,sd4.text jbQylbStr,
		sd5.text jbZzxsStr,sd6.text jbQyjcStr,sd7.text jbJyzkStr,sd8.text jbZcdStr,
		sd9.text jbZcdjwStr,sd10.text jbGjczqyStr,sd11.text jbSsbmStr,sd12.text jbGzjgjgStr,
		sd13.text jbZyhyList,sd35.text jbZyhyList2, sd36.text jbZyhyList3 ,sd37.text jbZyhyList1
		       ,sd14.text jbSfzyStr,sd15.text jbSfztjnStr,sd16.text jbSftsmdgsStr,
		sd17.text jbZcmdStr,sd18.text jbSfczgrdcgStr,sd19.text jbRelaStr,sd20.text jbHgqyStr,
		sd21.text jbSfyzStr,sd22.text jbBdcqdjqxStr,sd23.text jbZxcqdjqxStr,rbi.rg_type rgType,sd24.text jbQljhStr,
		sd25.text jbQysbsbzxzStr,sd26.text jbSdsbzxzStr,sd27.text jbCzebzxzStr,sd28.text jbSjzczbbzxzStr,
		sd29.text jbRjzbbzxzStr,sd30.text jbZczbbzxzStr,sd31.text jbZczbbzStr, o.ORGANIZATION_NAME as jdZyczr
		from view_cq_jbxxb_noDelete t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		left join rg_business_info rbi on rbi.jbxx_id=t.id
		left join sys_dictionary sd1 on sd1.val=t.jb_jnjw and sd1.type_id=(select id from sys_dictionary where type_code='JNJW')
		left join sys_dictionary sd2 on sd2.val=t.jb_sfybgs and sd2.type_id=(select id from sys_dictionary where type_code='SFYBGS')
		left join sys_dictionary sd3 on sd3.val=t.jb_zycqdjqx and sd3.type_id=(select id from sys_dictionary where type_code='ZYCQDJQX')
		left join sys_dictionary sd4 on sd4.val=t.jb_qylb and sd4.type_id=(select id from sys_dictionary where type_code='QYLB')
		left join sys_dictionary sd5 on sd5.val=t.jb_zzxs and sd5.type_id=(select id from sys_dictionary where type_code='ZZXS')
		left join sys_dictionary sd6 on sd6.val=t.jb_qyjc and sd6.type_id=(select id from sys_dictionary where type_code='QYJC')
		left join sys_dictionary sd7 on sd7.val=t.jb_jyzk and sd7.type_id=(select id from sys_dictionary where type_code='JYZK')
		left join sys_dictionary sd8 on sd8.val=t.jb_zcd and sd8.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'ZCD_OLD' else 'ZCD' end))
		left join sys_dictionary sd9 on sd9.val=t.jb_zcdjw and sd9.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'ZCDJW_OLD' else 'ZCDJW' end))
		left join sys_dictionary sd10 on sd10.val=t.jb_gjczqy and sd10.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		left join sys_dictionary sd11 on sd11.val=t.jb_ssbm and sd11.type_id=(select id from sys_dictionary where type_code='SSBM')
		left join sys_dictionary sd12 on sd12.val=t.jb_gzjgjg and sd12.type_id=(select id from sys_dictionary where type_code='GZJGJG')
		left join sys_dictionary sd13 on FIND_IN_SET(sd13.val,t.jb_zyhy) and sd13.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd35 on FIND_IN_SET(sd35.val,t.jb_zyhy2) and sd35.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd36 on FIND_IN_SET(sd36.val,t.jb_zyhy3) and sd36.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd37 on FIND_IN_SET(sd37.val,t.jb_zyhy1) and sd37.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd14 on sd14.val=t.jb_sfzy and sd14.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd15 on sd15.val=t.jb_sfztjn and sd15.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd16 on sd16.val=t.jb_sftsmdgs and sd16.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd17 on sd17.val=t.jb_zcmd and sd17.type_id=(select id from sys_dictionary where type_code='ZCMD')
		left join sys_dictionary sd18 on sd18.val=t.jb_sfczgrdcg and sd18.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd19 on sd19.val=t.jb_rela and sd19.type_id=(select id from sys_dictionary where type_code='RELA')
		left join sys_dictionary sd20 on sd20.val=t.jb_hgqy and sd20.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd21 on sd21.val=t.jb_sfyz and sd21.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd22 on sd22.val=t.jb_bdcqdjqx and sd22.type_id=(select id from sys_dictionary where type_code='BDCQDJQX')
		left join sys_dictionary sd23 on sd23.val=t.jb_zxcqdjqx and sd23.type_id=(select id from sys_dictionary where type_code='ZXCQDJQX')
		left join sys_dictionary sd24 on sd24.val=t.jb_qljh and sd24.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd25 on sd25.val=t.jb_qysbsbzxz and sd25.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd26 on sd26.val=t.jb_sdsbzxz and sd26.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd27 on sd27.val=t.jb_czebzxz and sd27.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd28 on sd28.val=t.jb_sjzczbbzxz and sd28.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd29 on sd29.val=t.jb_rjzbbzxz and sd29.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd30 on sd30.val=t.jb_zczbbzxz and sd30.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd31 on sd31.val=t.jb_zczbbz and sd31.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_organization o on o.ORGANIZATION_ID = t.jb_czrzzjgid
		where t.id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_jbxxb
		<set>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="jbZczb != null">
				jb_zczb=#{jbZczb},
			</if>
			<if test="jbGjsbs != null">
				jb_gjsbs=#{jbGjsbs},
			</if>
			<if test="jbGjsds != null">
				jb_gjsds=#{jbGjsds},
			</if>
			<if test="jbGyfrsbs != null">
				jb_gyfrsbs=#{jbGyfrsbs},
			</if>
			<if test="jbGyfrsds != null">
				jb_gyfrsds=#{jbGyfrsds},
			</if>
			<if test="jbGyjdkgsbs != null">
				jb_gyjdkgsbs=#{jbGyjdkgsbs},
			</if>
			<if test="jbGyjdkgsds != null">
				jb_gyjdkgsds=#{jbGyjdkgsds},
			</if>
			<if test="jbGysjkzsbs != null">
				jb_gysjkzsbs=#{jbGysjkzsbs},
			</if>
			<if test="jbGysjkzsds != null">
				jb_gysjkzsds=#{jbGysjkzsds},
			</if>
			<if test="jbQtqysbs != null">
				jb_qtqysbs=#{jbQtqysbs},
			</if>
			<if test="jbQtsds != null">
				jb_qtsds=#{jbQtsds},
			</if>
			<if test="jbHjqysbs != null">
				jb_hjqysbs=#{jbHjqysbs},
			</if>
			<if test="jbHjsds != null">
				jb_hjsds=#{jbHjsds},
			</if>
			<if test="jbSjblrq != null">
				jb_sjblrq=#{jbSjblrq},
			</if>
			<if test="jbHjcze != null">
				jb_hjcze=#{jbHjcze},
			</if>
			<if test="jbHjsjzcj != null">
				jb_hjsjzcj=#{jbHjsjzcj},
			</if>
			<if test="jbHjbl != null">
				jb_hjbl=#{jbHjbl},
			</if>
			<if test="jbQymc != null">
				jb_qymc=#{jbQymc},
			</if>
			<if test="jbZzjgdm != null">
				jb_zzjgdm=#{jbZzjgdm},
			</if>
			<if test="jbZcmd != null">
				jb_zcmd=#{jbZcmd},
			</if>
			<if test="jbCgrxm != null">
				jb_cgrxm=#{jbCgrxm},
			</if>
			<if test="jbJhqlsj != null">
				jb_jhqlsj=#{jbJhqlsj},
			</if>
			<if test="jbSjczr != null">
				jb_sjczr=#{jbSjczr},
			</if>
			<if test="jbCzrzzjgdm != null">
				jb_czrzzjgdm=#{jbCzrzzjgdm},
			</if>
			<if test="jbHjsjzcjbz != null">
				jb_hjsjzcjbz=#{jbHjsjzcjbz},
			</if>
			<if test="jbGjczsdsbz != null">
				jb_gjczsdsbz=#{jbGjczsdsbz},
			</if>
			<if test="jbGjczqysbsbz != null">
				jb_gjczqysbsbz=#{jbGjczqysbsbz},
			</if>
			<if test="jbGyfrczsbsbz != null">
				jb_gyfrczsbsbz=#{jbGyfrczsbsbz},
			</if>
			<if test="jbGyfrczsdsbz != null">
				jb_gyfrczsdsbz=#{jbGyfrczsdsbz},
			</if>
			<if test="jbGyjdkgfrsbsbz != null">
				jb_gyjdkgfrsbsbz=#{jbGyjdkgfrsbsbz},
			</if>
			<if test="jbGyjdkgfrsdsbz != null">
				jb_gyjdkgfrsdsbz=#{jbGyjdkgfrsdsbz},
			</if>
			<if test="jbGysjkzfrsbsbz != null">
				jb_gysjkzfrsbsbz=#{jbGysjkzfrsbsbz},
			</if>
			<if test="jbGysjkzfrsdsbz != null">
				jb_gysjkzfrsdsbz=#{jbGysjkzfrsdsbz},
			</if>
			<if test="jbQtsbsbz != null">
				jb_qtsbsbz=#{jbQtsbsbz},
			</if>
			<if test="jbQtqysdsbz != null">
				jb_qtqysdsbz=#{jbQtqysdsbz},
			</if>
			<if test="jbHjqysbsbz != null">
				jb_hjqysbsbz=#{jbHjqysbsbz},
			</if>
			<if test="jbHjsdsbz != null">
				jb_hjsdsbz=#{jbHjsdsbz},
			</if>
			<if test="jbShzt != null">
				jb_shzt=#{jbShzt},
			</if>
			<if test="jbSshy != null">
				jb_sshy=#{jbSshy},
			</if>
			<if test="jbSfzy != null">
				jb_sfzy=#{jbSfzy},
			</if>
			<if test="jbZzxs != null">
				jb_zzxs=#{jbZzxs},
			</if>
			<if test="jbQylb != null">
				jb_qylb=#{jbQylb},
			</if>
			<if test="jbQyjc != null">
				jb_qyjc=#{jbQyjc},
			</if>
			<if test="jbSsbm != null">
				jb_ssbm=#{jbSsbm},
			</if>
			<if test="jbJyzk != null">
				jb_jyzk=#{jbJyzk},
			</if>
		    <if test="jbSftsmdgs != null">
				jb_sftsmdgs=#{jbSftsmdgs},
			</if>
			<if test="jbSfczgrdcg != null">
				jb_sfczgrdcg=#{jbSfczgrdcg},
			</if>
			<if test="jbGjczqy != null">
				jb_gjczqy=#{jbGjczqy},
			</if>
			<if test="jbQysbsbzxz != null">
				jb_qysbsbzxz=#{jbQysbsbzxz},
			</if>
			<if test="jbSdsbzxz != null">
				jb_sdsbzxz=#{jbSdsbzxz},
			</if>
			<if test="jbJnjw != null">
				jb_jnjw=#{jbJnjw},
			</if>
			<if test="jbZycqdjqx != null">
				jb_zycqdjqx=#{jbZycqdjqx},
			</if>
			<if test="jbBdcqdjqx != null">
				jb_bdcqdjqx=#{jbBdcqdjqx},
			</if>
			<if test="jbZxcqdjqx != null">
				jb_zxcqdjqx=#{jbZxcqdjqx},
			</if>
			<if test="jbGzjgjg != null">
				jb_gzjgjg=#{jbGzjgjg},
			</if>
			<if test="jbZcd != null">
				jb_zcd=#{jbZcd},
			</if>
			<if test="jbGsdjrq != null">
				jb_gsdjrq=#{jbGsdjrq},
			</if>
			<if test="jbZcrq != null">
				jb_zcrq=#{jbZcrq},
			</if>
			<if test="jbSfybgs != null">
				jb_sfybgs=#{jbSfybgs},
			</if>
			<if test="jbGsblzk != null">
				jb_gsblzk=#{jbGsblzk},
			</if>
			<if test="jbGsdjxgzl != null">
				jb_gsdjxgzl=#{jbGsdjxgzl},
			</if>
			<if test="jbSfyz != null">
				jb_sfyz=#{jbSfyz},
			</if>
			<if test="jbByzly != null">
				jb_byzly=#{jbByzly},
			</if>
			<if test="jbZyhy != null">
				jb_zyhy=#{jbZyhy},
			</if>
			<if test="jbZcdjw != null">
				jb_zcdjw=#{jbZcdjw},
			</if>
			<if test="jbHjcjebz != null">
				jb_hjcjebz=#{jbHjcjebz},
			</if>
			<if test="jbCzebzxz != null">
				jb_czebzxz=#{jbCzebzxz},
			</if>
			<if test="jbSjzczbbzxz != null">
				jb_sjzczbbzxz=#{jbSjzczbbzxz},
			</if>
			<if test="jbBlqshzt != null">
				jb_blqshzt=#{jbBlqshzt},
			</if>
			<if test="jbSfztjn != null">
				jb_sfztjn=#{jbSfztjn},
			</if>
			<if test="jbZczbbz != null">
				jb_zczbbz=#{jbZczbbz},
			</if>
			<if test="jbZczbjw != null">
				jb_zczbjw=#{jbZczbjw},
			</if>
			<if test="jbSfzdyjsj != null">
				jb_sfzdyjsj=#{jbSfzdyjsj},
			</if>
			<if test="jbSfsjscdm != null">
				jb_sfsjscdm=#{jbSfsjscdm},
			</if>
			<if test="jbXzqy != null">
				jb_xzqy=#{jbXzqy},
			</if>
			<if test="jbShtgrq != null">
				jb_shtgrq=#{jbShtgrq},
			</if>
			<if test="jbDylsh != null">
				jb_dylsh=#{jbDylsh},
			</if>
			<if test="jbSsgzjgjg != null">
				jb_ssgzjgjg=#{jbSsgzjgjg},
			</if>
			<if test="jbQljh != null">
				jb_qljh=#{jbQljh},
			</if>
			<if test="jbHjrjzb != null">
				jb_hjrjzb=#{jbHjrjzb},
			</if>
			<if test="jbRjzbbzxz != null">
				jb_rjzbbzxz=#{jbRjzbbzxz},
			</if>
			<if test="jbHjrjzbbz != null">
				jb_hjrjzbbz=#{jbHjrjzbbz},
			</if>
			<if test="jbZczbbzxz != null">
				jb_zczbbzxz=#{jbZczbbzxz},
			</if>
			<if test="jbGykgcz != null">
				jb_gykgcz=#{jbGykgcz},
			</if>
			<if test="jbGykgczsbs != null">
				jb_gykgczsbs=#{jbGykgczsbs},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime},
			</if>
			<if test="businessNature != null">
				business_nature=#{businessNature},
			</if>
			<if test="jbGyzb != null">
				jb_gyzb=#{jbGyzb},
			</if>
			<if test="jbRela != null">
				jb_rela=#{jbRela},
			</if>
			<if test="jbHgqy != null">
				jb_hgqy=#{jbHgqy},
			</if>
			<if test="jbCzrzzjgid != null and jbCzrzzjgid != ''">
				jb_czrzzjgid = #{jbCzrzzjgid},
			</if>
			<if test="jbDeleted != null">
				jb_deleted = #{jbDeleted},
			</if>
			<if test="jbSfss != null and jbSfss != ''">
				jb_sf_ss= #{jbSfss},
			</if>
			<if test="jbSfbb != null and jbSfbb != ''">
				jb_sf_bb = #{jbSfbb},
			</if>
			<if test="jbQyjycs != null and jbQyjycs != ''">
				jb_qyjycs = #{jbQyjycs},
			</if>
			<if test="jbSftgqy != null and jbSftgqy != ''">
				jb_sf_tgqy  = #{jbSftgqy},
			</if>
			<if test="jbSfczblzt != null and jbSfczblzt != ''">
				jb_sf_czblzt = #{jbSfczblzt},
			</if>
			<if test="jbSfkzgs != null and jbSfkzgs != ''">
				jb_sf_kzgs = #{jbSfkzgs},
			</if>
			<if test="jbZyhy2 != null and jbZyhy2 != ''">
				jb_zyhy2 = #{jbZyhy2},
			</if>
			<if test="jbZyhy3 != null and jbZyhy3 != ''">
				jb_zyhy3 = #{jbZyhy3},
			</if>
		    <if test="jbZyhy1 != null and jbZyhy1 != ''">
				jb_zyhy1 = #{jbZyhy1},
			</if>
			<if test="jbQygljc != null and jbQygljc != ''">
				jb_qygljc = #{jbQygljc},
			</if>
			<if test="jbQyslzt != null and jbQyslzt != ''">
				jb_qyslzt = #{jbQyslzt},
			</if>
			<if test="jbQzwjkjglx != null and jbQzwjkjglx != ''">
				jb_qzwjkjglx = #{jbQzwjkjglx},
			</if>
			<if test="jbGzwjkjgmx != null and jbGzwjkjgmx != ''">
				jb_gzwjkjgmx = #{jbGzwjkjgmx},
			</if>
			<if test="jbYyzzzc != null and jbYyzzzc != ''">
				jb_yyzzzc = #{jbYyzzzc},
			</if>
		    <if test="jbRjzbbz != null">
				jb_rjzbbz = #{jbRjzbbz},
			</if>
			<if test="jbRjzb != null">
				jb_rjzb = #{jbRjzb},
			</if>
			<if test="jbDataStatus != null">
				jb_data_status = #{jbDataStatus},
			</if>
			<if test="jbMyqysbsbz != null">
				jb_myqysbsbz = #{jbMyqysbsbz},
			</if>
			<if test="jbMyqysbs != null">
				jb_myqysbs = #{jbMyqysbs},
			</if>
			<if test="jbWzqysbsbz != null">
				jb_wzqysbsbz = #{jbWzqysbsbz},
			</if>
			<if test="jbWzqysbs != null">
				jb_wzqysbs = #{jbWzqysbs},
			</if>
			<if test="jbZrrsbsbz != null">
				jb_zrrsbsbz = #{jbZrrsbsbz},
			</if>
			<if test="jbZrrsbs != null">
				jb_zrrsbs = #{jbZrrsbs}
			</if>
		</set>
		where id=#{id}
	</update>

	<!-- 更新 -->
	<update id="update">
		update cq_jbxxb
		<set>
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			jb_zczb=#{jbZczb},
			jb_gjsbs=#{jbGjsbs},
			jb_gjsds=#{jbGjsds},
			jb_gyfrsbs=#{jbGyfrsbs},
			jb_gyfrsds=#{jbGyfrsds},
			jb_gyjdkgsbs=#{jbGyjdkgsbs},
			jb_gyjdkgsds=#{jbGyjdkgsds},
			jb_gysjkzsbs=#{jbGysjkzsbs},
			jb_gysjkzsds=#{jbGysjkzsds},
			jb_qtqysbs=#{jbQtqysbs},
			jb_qtsds=#{jbQtsds},
			jb_hjqysbs=#{jbHjqysbs},
			jb_hjsds=#{jbHjsds},
			jb_sjblrq=#{jbSjblrq},
			jb_hjcze=#{jbHjcze},
			jb_hjsjzcj=#{jbHjsjzcj},
			jb_hjbl=#{jbHjbl},
			jb_qymc=#{jbQymc},
			jb_zzjgdm=#{jbZzjgdm},
			jb_zcmd=#{jbZcmd},
			jb_cgrxm=#{jbCgrxm},
			jb_jhqlsj=#{jbJhqlsj},
			jb_sjczr=#{jbSjczr},
			jb_czrzzjgdm=#{jbCzrzzjgdm},
			jb_hjsjzcjbz=#{jbHjsjzcjbz},
			jb_gjczsdsbz=#{jbGjczsdsbz},
			jb_gjczqysbsbz=#{jbGjczqysbsbz},
			jb_gyfrczsbsbz=#{jbGyfrczsbsbz},
			jb_gyfrczsdsbz=#{jbGyfrczsdsbz},
			jb_gyjdkgfrsbsbz=#{jbGyjdkgfrsbsbz},
			jb_gyjdkgfrsdsbz=#{jbGyjdkgfrsdsbz},
			jb_gysjkzfrsbsbz=#{jbGysjkzfrsbsbz},
			jb_gysjkzfrsdsbz=#{jbGysjkzfrsdsbz},
			jb_qtsbsbz=#{jbQtsbsbz},
			jb_qtqysdsbz=#{jbQtqysdsbz},
			jb_hjqysbsbz=#{jbHjqysbsbz},
			jb_hjsdsbz=#{jbHjsdsbz},
			jb_shzt=#{jbShzt},
			jb_sshy=#{jbSshy},
			jb_sfzy=#{jbSfzy},
			jb_zzxs=#{jbZzxs},
			jb_qylb=#{jbQylb},
			jb_qyjc=#{jbQyjc},
			jb_ssbm=#{jbSsbm},
			jb_jyzk=#{jbJyzk},
			jb_sftsmdgs=#{jbSftsmdgs},
			jb_sfczgrdcg=#{jbSfczgrdcg},
			jb_gjczqy=#{jbGjczqy},
			jb_qysbsbzxz=#{jbQysbsbzxz},
			jb_sdsbzxz=#{jbSdsbzxz},
			jb_jnjw=#{jbJnjw},
			jb_zycqdjqx=#{jbZycqdjqx},
			jb_bdcqdjqx=#{jbBdcqdjqx},
			jb_zxcqdjqx=#{jbZxcqdjqx},
			jb_gzjgjg=#{jbGzjgjg},
			jb_zcd=#{jbZcd},
			jb_gsdjrq=#{jbGsdjrq},
			jb_zcrq=#{jbZcrq},
			jb_sfybgs=#{jbSfybgs},
			jb_gsblzk=#{jbGsblzk},
			jb_gsdjxgzl=#{jbGsdjxgzl},
			jb_sfyz=#{jbSfyz},
			jb_byzly=#{jbByzly},
			jb_zyhy=#{jbZyhy},
			jb_zcdjw=#{jbZcdjw},
			jb_hjcjebz=#{jbHjcjebz},
			jb_czebzxz=#{jbCzebzxz},
			jb_sjzczbbzxz=#{jbSjzczbbzxz},
			jb_blqshzt=#{jbBlqshzt},
			jb_sfztjn=#{jbSfztjn},
			jb_zczbbz=#{jbZczbbz},
			jb_zczbjw=#{jbZczbjw},
			jb_sfzdyjsj=#{jbSfzdyjsj},
			jb_sfsjscdm=#{jbSfsjscdm},
			jb_xzqy=#{jbXzqy},
			jb_shtgrq=#{jbShtgrq},
			jb_dylsh=#{jbDylsh},
			jb_ssgzjgjg=#{jbSsgzjgjg},
			jb_qljh=#{jbQljh},
			jb_hjrjzb=#{jbHjrjzb},
			jb_rjzbbzxz=#{jbRjzbbzxz},
			jb_hjrjzbbz=#{jbHjrjzbbz},
			jb_zczbbzxz=#{jbZczbbzxz},
			jb_gykgcz=#{jbGykgcz},
			jb_gykgczsbs=#{jbGykgczsbs},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime},
			business_nature=#{businessNature},
			jb_gyzb=#{jbGyzb},
			jb_rela=#{jbRela},
			jb_hgqy=#{jbHgqy},
			jb_czrzzjgid = #{jbCzrzzjgid},
			jb_deleted = #{jbDeleted},
			jb_data_status = #{jbDataStatus},
			jb_myqysbsbz = #{jbMyqysbsbz},
			jb_myqysbs = #{jbMyqysbs},
			jb_wzqysbsbz = #{jbWzqysbsbz},
			jb_wzqysbs = #{jbWzqysbs},
			jb_zrrsbsbz = #{jbZrrsbsbz},
			jb_zrrsbs = #{jbZrrsbs}
		</set>
		where id=#{id}
	</update>


	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.jbxxb.entity.Jbxxb" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
		view_cq_jbxxb_noDelete t
		where 1 = 1
		<include refid="whereSql" />
	</select>

	<select id="getJbxxbOrCzrDetail" resultType="com.zjhc.gzwcq.openApi.JbxxbInfo">
		SELECT
		       jb.id ,
			jb.UNITID as organizationId,
		    jb.BUSINESS_NATURE as businessNature,
			jb.JB_QYMC as jbQymc,
			jb.JB_ZZJGDM as jbZzjgdm,
			dic25.text as jbZcmd,
			jb.JB_CGRXM as jbCgrxm,
			jb.jb_qyslzt as jbQyslzt,
			dic.text as jbGzjgjg,
			dic2.text as jbGzwjkjgmx,
			gjcz.text as jbGjczqymc,
			SUBSTRING_INDEX(gjcz.val,'_',-1) as jbGjczqy,
			dic3.text as jbYgjczqygx ,
			dic4.text as jbQylb,
			if(jb.JB_HGQY != 1,'否',if(jb.JB_HGQY is not null,'是',null))  jbSfhgqy,
			dic5.text jbZzxs,
			if(jb.jb_sf_ss != 1,'否',if(jb.jb_sf_ss is not null,'是',null)) jbSfss,
			if(jb.jb_sf_bb != 1,'否',if(jb.jb_sf_bb is not null,'是',null)) as jbSfbb,
			dic6.text jbQycjc,
			dic7.text jbQyqjc,
			jb.JB_ZCRQ jbZclrq,
			jb.JB_GSDJRQ jbGsdjrq,
			jb.jb_yyzzzc jbYyzzdz	,
			dic8.text  jbZcd,
			jb.JB_CZRZZJGDM jbZczyhxzjb,
			zycz.ORGANIZATION_NAME  jbZczy,
			IF(jb.BUSINESS_NATURE != 2,
			   dic9.text,
			   dic21.text
				) as jbZczbz,

			IF(jb.BUSINESS_NATURE != 2,
			   IFNULL(jb.JB_RJZB,jb.JB_ZCZBJW)
				,
			   jb.HH_RJCZE
				) as jbZczb,
			IF(jb.BUSINESS_NATURE != 2,
			   jb.JB_ZCZB
				,jb.HH_RJCZERMB

				) as jbZczbrmb,
			jb.JB_GYZB jbGzzb,
			dic10.text jbSshy,
			if(jb.JB_SFZY != 1,'否',if(jb.JB_SFZY is not null,'是',null)) jbIsMainBusiness,
			dic11.text jbZyhy1,
			dic12.text jbZyhy2,
			dic13.text jbZyhy3,
			dic14.text jbJyzk,
			if(jb.jb_sf_tgqy != 1,'否',if(jb.jb_sf_tgqy is not null,'是',null)) jbSfdtgt,
			if(jb.jb_sf_czblzt!= 1,'否',if(jb.jb_sf_czblzt is not null,'是',null)) jbIsSuspend,
			dic15.text jbJNJW,
			if(jb.JB_SFZTJN != 1,'否',if(jb.JB_SFZTJN is not null,'是',null)) jbIsJwToJn,
			dic16.text jbIsBgs,
			IFNULL(dic17.text,IFNULL(dic18.text,dic19.text)) jbCqdjqx,
			jb.HH_QX jbHhqx,
			dic20.text jbZyjycs,
			jb.HH_ZXSWHHR jbZxswhhr,
			jb.HH_ZXSWHHR_CODE jbZxswhhrCode,
			dic22.text as jbSszbbz,
			jb.HH_SJCZE as jbSszb,
			jb.HH_SJCZERMB as jbSszbrmb,
			if(jb.HH_SFSMTZJJ = 1 ,'是',if(jb.HH_SFSMTZJJ is not null,'否',null)) jbIsSmr,
			if(jb.JB_QLJH != 1,'否',if(jb.JB_QLJH is not null,'是',null)) jbIsPlanClear,
			jb.JB_JHQLSJ jbPlanClearTime,
			if(jb.jb_sf_kzgs!= 1,'否',if(jb.jb_sf_kzgs is not null,'是',null)) jbIsKg,
			if(jb.JB_SFCZGRDCG!= 1,'否',if(jb.JB_SFCZGRDCG is not null,'是',null)) jbIsPersonalCg,
			if(jb.JB_SFTSMDGS!= 1,'否',if(jb.JB_SFTSMDGS is not null,'是',null)) jbSftsmdgs
		FROM
			(
				SELECT
					jb.id,
					jb.RG_TIMEMARK
				FROM
					(
						SELECT
							info.JBXX_ID as id,
							info.RG_TIMEMARK,
							info.UNITID
						FROM
							(
								SELECT
									o.ORGANIZATION_ID
								FROM
									(
										SELECT
											o.ORGANIZATION_ID
										FROM
											sys_organization o
										WHERE
											o.isdeleted = 'N'
										  AND
											o.ORGANIZATION_ID = #{appOrgId}
									) oid
										JOIN sys_organization o ON FIND_IN_SET( oid.ORGANIZATION_ID, o.PARENTS )
								WHERE
								      1=1
								  <if test="orgId != '' and orgId != null">
									  and o.ORGANIZATION_ID = #{orgId}
								  </if>

								  AND o.isdeleted = 'N'
							) o
								JOIN rg_business_info info  on info.UNITID = o.ORGANIZATION_ID
						WHERE
							info.RG_UNITSTATE = 2
						ORDER BY
							info.RG_TIMEMARK desc
						LIMIT 1000000000
					) jb
				GROUP BY
					jb.UNITID
			)jbo JOIN  view_cq_jbxxb jb on jb.ID = jbo.id


				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='GZWJKJGLX')) dic
						   on dic.val = jb.jb_qzwjkjglx

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='GZJGJG')) dic2
						   on dic2.val = jb.jb_gzwjkjgmx

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='GJCZQY')) gjcz
						   on gjcz.val  = jb.JB_GJCZQY

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='RELA')) dic3
						   on dic3.val = jb.JB_RELA

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='QYLB')) dic4
						   on dic4.val = jb.jb_qylb

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='ZZXS')) dic5
						   on dic5.val = jb.jb_ZZXS

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='QYJC')) dic6
						   on dic6.val = jb.JB_QYJC

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='QYGLJC')) dic7
						   on dic7.val = jb.jb_qygljc

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='ZCD')) dic8
						   on dic8.val = jb.jb_zcd

				 LEFT JOIN sys_organization zycz on zycz.ORGANIZATION_ID = jb.JB_CZRZZJGID

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='BZXZ')) dic9
						   on dic9.val = IF(jb.JB_RJZBBZ is null or jb.JB_RJZBBZ = '',IF(jb.jb_Zczbbz is not null and jb.jb_Zczbbz != '' ,jb.jb_Zczbbz,null),jb.JB_RJZBBZ)

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic10
						   on dic10.val = jb.JB_ZYHY

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic11
						   on dic11.val = jb.JB_ZYHY1

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic12
						   on dic12.val = jb.JB_ZYHY2

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic13
						   on dic13.val = jb.JB_ZYHY3

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='JYZK')) dic14
						   on dic14.val = jb.jb_JYZK

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='JNJW')) dic15
						   on dic15.val = jb.JB_JNJW

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='SFYBGS')) dic16
						   on dic16.val = jb.JB_SFYBGS

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='ZYCQDJQX')) dic17
						   on dic17.val = jb.JB_ZYCQDJQX

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='BDCQDJQX')) dic18
						   on dic18.val = jb.JB_BDCQDJQX

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='ZXCQDJQX')) dic19
						   on dic19.val = jb.JB_ZXCQDJQX

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='ZCD')) dic20
						   on dic20.val = jb.HH_ZYJYCS

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='RJCZEBC')) dic21
						   on dic21.val = jb.HH_RJCZEBZ

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='RJCZEBC')) dic22
						   on dic22.val = jb.HH_SJCZEBZ

				 left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
																		  (SELECT id FROM sys_dictionary WHERE type_code ='ZCMD')) dic25
						   on dic25.val = jb.JB_ZCMD

	</select>
	<select id="getJbxxbOrCzrDetailTotal" resultType="java.lang.Long">
		SELECT
		count(1)
		FROM
		(
		SELECT
		jb.id,
		jb.RG_TIMEMARK
		FROM
		(
		SELECT
		info.JBXX_ID as id,
		info.RG_TIMEMARK,
		info.UNITID
		FROM
		(
		SELECT
		o.ORGANIZATION_ID
		FROM
		(
		SELECT
		o.ORGANIZATION_ID
		FROM
		sys_organization o
		WHERE
		o.isdeleted = 'N'
		AND
		o.ORGANIZATION_ID = #{appOrgId}
		) oid
		JOIN sys_organization o ON FIND_IN_SET( oid.ORGANIZATION_ID, o.PARENTS )
		WHERE
		1=1
		<if test="orgId != '' and orgId != null">
			and o.ORGANIZATION_ID = #{orgId}
		</if>

		AND o.isdeleted = 'N'
		) o
		JOIN rg_business_info info  on info.UNITID = o.ORGANIZATION_ID
		WHERE
		info.RG_UNITSTATE = 2
		ORDER BY
		info.RG_TIMEMARK desc
		LIMIT 1000000000
		) jb
		GROUP BY
		jb.UNITID
		)jbo JOIN  view_cq_jbxxb jb on jb.ID = jbo.id


		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='GZWJKJGLX')) dic
		on dic.val = jb.jb_qzwjkjglx

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='GZJGJG')) dic2
		on dic2.val = jb.jb_gzwjkjgmx

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='GJCZQY')) gjcz
		on gjcz.val  = jb.JB_GJCZQY

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='RELA')) dic3
		on dic3.val = jb.JB_RELA

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='QYLB')) dic4
		on dic4.val = jb.jb_qylb

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='ZZXS')) dic5
		on dic5.val = jb.jb_ZZXS

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='QYJC')) dic6
		on dic6.val = jb.JB_QYJC

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='QYGLJC')) dic7
		on dic7.val = jb.jb_qygljc

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='ZCD')) dic8
		on dic8.val = jb.jb_zcd

		LEFT JOIN sys_organization zycz on zycz.ORGANIZATION_ID = jb.JB_CZRZZJGID

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='BZXZ')) dic9
		on dic9.val = IF(jb.JB_RJZBBZ is null or jb.JB_RJZBBZ = '',IF(jb.jb_Zczbbz is not null and jb.jb_Zczbbz != '' ,jb.jb_Zczbbz,null),jb.JB_RJZBBZ)

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic10
		on dic10.val = jb.JB_ZYHY

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic11
		on dic11.val = jb.JB_ZYHY1

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic12
		on dic12.val = jb.JB_ZYHY2

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='INDUSTRY_CLASSIFICATION_TREE')) dic13
		on dic13.val = jb.JB_ZYHY3

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='JYZK')) dic14
		on dic14.val = jb.jb_JYZK

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='JNJW')) dic15
		on dic15.val = jb.JB_JNJW

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='SFYBGS')) dic16
		on dic16.val = jb.JB_SFYBGS

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='ZYCQDJQX')) dic17
		on dic17.val = jb.JB_ZYCQDJQX

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='BDCQDJQX')) dic18
		on dic18.val = jb.JB_BDCQDJQX

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='ZXCQDJQX')) dic19
		on dic19.val = jb.JB_ZXCQDJQX

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='ZCD')) dic20
		on dic20.val = jb.HH_ZYJYCS

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='RJCZEBC')) dic21
		on dic21.val = jb.HH_RJCZEBZ

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='RJCZEBC')) dic22
		on dic22.val = jb.HH_SJCZEBZ

		left JOIN (SELECT text,val FROM sys_dictionary dic WHERE dic.type_id =
		(SELECT id FROM sys_dictionary WHERE type_code ='ZCMD')) dic25
		on dic25.val = jb.JB_ZCMD

	</select>
	<select id="queryTotalJbxxbs" parameterType="com.zjhc.gzwcq.jbxxb.entity.JbxxbParam" resultType="java.lang.Long">
		select
		count(z.ID)
		from (
		select id from
		view_cq_jbxxb_noDelete t
		where 1=1
		<include refid="whereSql2" />
		and ((t.jb_zycqdjqx is null or t.jb_zycqdjqx is not null and t.jb_shzt = '4')
		and t.unitid in (select t1.ORGANIZATION_ID from (
		<foreach collection="visibles" item="item" separator="union" index="index" open="(" close=")">
			(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
		</foreach>)as t1)
		<if test="userId != null and userId != ''">
			or t.create_user = #{userId}
		</if>
		<!--占有未通过的使用出资人id,通过的使用自己的unitId-->
		or t.jb_zycqdjqx is not null and t.jb_shzt !='4' and t.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
		<foreach collection="visibles" item="item" separator="union" index="index" open="(" close=")">
			(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
		</foreach>) as t2))

		<!--查询的组织筛选-->
		<if test="selectedOrganizationIds != null and !selectedOrganizationIds.isEmpty() and selectedOrganizationIds.size()>0">
			and (((t.jb_zycqdjqx is null or t.jb_zycqdjqx is not null and t.jb_shzt = '4')
			and t.unitid in
			<foreach collection="selectedOrganizationIds" open="(" close=")" separator="," item="sid">
				#{sid}
			</foreach>)
			<!--占有未通过的使用出资人id,通过的使用自己的unitId-->
			or (t.jb_zycqdjqx is not null and t.jb_shzt !='4' and t.jb_czrzzjgid in
			<foreach collection="selectedOrganizationIds" open="(" close=")" separator="," item="sid">
				#{sid}
			</foreach>)
			)
		</if>
		) z
	</select>

	<!-- 列表页查询 -->
	<select id="queryJbxxbForList" parameterType="com.zjhc.gzwcq.jbxxb.entity.JbxxbParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias2"/>,sd1.text jbSfybgsStr,sd2.text jbShztStr
		from
		view_cq_jbxxb_noDelete t
		left join cq_hhqy hh on t.id = hh.jbxx_id
		left join sys_dictionary sd1 on sd1.val=t.jb_sfybgs and sd1.type_id=(select id from sys_dictionary where type_code='SFYBGS')
		left join sys_dictionary sd2 on sd2.val=t.jb_shzt and sd2.type_id=(select id from sys_dictionary where type_code='JBXX_REVIEW_STATUS')
		where 1=1
		<include refid="whereSql2" />
		and ((t.jb_zycqdjqx is null or t.jb_zycqdjqx is not null and t.jb_shzt = '4')
		and t.unitid in (select t1.ORGANIZATION_ID from (
		<foreach collection="visibles" item="item" separator="union" index="index" open="(" close=")">
			(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
		</foreach>)as t1)
		<if test="userId != null and userId != ''">
			or t.create_user = #{userId}
		</if>
		<!--占有未通过的使用出资人id,通过的使用自己的unitId-->
		or t.jb_zycqdjqx is not null and t.jb_shzt !='4' and t.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
		<foreach collection="visibles" item="item" separator="union" index="index" open="(" close=")">
			(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
		</foreach>) as t2))

		<!--查询的组织筛选-->
		<if test="selectedOrganizationIds != null and !selectedOrganizationIds.isEmpty() and selectedOrganizationIds.size()>0">
			and (((t.jb_zycqdjqx is null or t.jb_zycqdjqx is not null and t.jb_shzt = '4')
			and t.unitid in
			<foreach collection="selectedOrganizationIds" open="(" close=")" separator="," item="sid">
				#{sid}
			</foreach>)
			<!--占有未通过的使用出资人id,通过的使用自己的unitId-->
			or (t.jb_zycqdjqx is not null and t.jb_shzt !='4' and t.jb_czrzzjgid in
			<foreach collection="selectedOrganizationIds" open="(" close=")" separator="," item="sid">
				#{sid}
			</foreach>)
			)
		</if>
		order by t.create_time desc
	</select>

	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.jbxxb.entity.Jbxxb" resultType="java.lang.Long">
		select
		(
		(select count(id)
		from view_cq_jbxxb_noDelete
		<!--只与审核中和补录的数据比较-->
		where JB_SHZT in ('3','9')
		<if test="unitid != null and unitid != ''">
			and unitid != #{unitid}
		</if>
		<if test="id != null and id != ''">
			and id != #{id}
		</if>
		and JB_ZZJGDM = #{jbZzjgdm}
		and JB_SSGZJGJG = #{jbSsgzjgjg})
		+
		(SELECT count(ORGANIZATION_ID)
		FROM
		sys_organization
		WHERE
		isdeleted = 'N'
		AND ORGANIZATION_CODE = concat(#{jbSsgzjgjg}, '_', #{jbZzjgdm})
		<if test="unitid != null and unitid != ''">
			and ORGANIZATION_ID != #{unitid}
		</if>)
		)
		<!--
         (select count(id) from view_cq_jbxxb_noDelete t
         <where>
             ((JB_ZXCQDJQX is null) or (JB_ZXCQDJQX is not null and JB_SHZT != '4'))
             <if test="unitid != null and unitid != ''">
                 and t.unitid != #{unitid}
             </if>
             <if test="jbZzjgdm != null and jbZzjgdm != ''">
                 and t.jb_zzjgdm = #{jbZzjgdm}
             </if>
         </where>)
         +
         (select count(organization_id) from sys_organization
         <where>
             isdeleted = 'N'
             <if test="unitid != null and unitid != ''">
                 and organization_id != #{unitid}
             </if>
             <if test="jbZzjgdm != null and jbZzjgdm != ''">
                 and organization_code like concat('%/_',#{jbZzjgdm}) escape '/'
             </if>
         </where>)-->
	</select>

	<select id="selectWbgsByPage" parameterType="com.zjhc.gzwcq.jbxxb.entity.JbxxbParam" resultMap="baseResultMapExt">
		SELECT t.id,t.unitid,t.JB_QYMC JB_QYMC,t.jb_zzjgdm jb_zzjgdm,t.CREATE_TIME
		FROM
		(SELECT unitid, max(RG_DATE) maxTime FROM rg_business_info GROUP BY UNITID) temp
		JOIN rg_business_info rbi ON rbi.UNITID = temp.UNITID AND temp.maxTime = rbi.RG_DATE
		JOIN view_cq_jbxxb_noDelete t ON t.id = rbi.JBXX_ID
		<where>
			rbi.RG_UNITSTATE = '9'
			<if test="jbxxbParam.jbShzt != null">
				and t.jb_shzt = '9'
			</if>
			<if test="jbxxbParam.jbQymc != null and jbxxbParam.jbQymc != ''">
				and t.jb_qymc like concat('%',#{jbxxbParam.jbQymc},'%')
			</if>
			<if test="jbxxbParam.jbZzjgdm != null and jbxxbParam.jbZzjgdm != ''">
				and t.jb_zzjgdm like concat('%',#{jbxxbParam.jbZzjgdm},'%')
			</if>
			and (t.jb_zycqdjqx is null and t.unitid in (select t1.ORGANIZATION_ID from (
			<foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
				(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
			</foreach>)as t1)
			or t.jb_zycqdjqx is not null and t.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
			<foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
				(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
			</foreach>) as t2))
		</where>
	</select>

	<select id="loadRecentApprovedByUnitId" parameterType="com.zjhc.gzwcq.jbxxb.entity.Jbxxb" resultMap="resultMapWithZyhy">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr,
		sd1.text jbJnjwStr,sd2.text jbSfybgsStr,sd3.text jbZycqdjqxStr,sd4.text jbQylbStr,
		sd5.text jbZzxsStr,sd6.text jbQyjcStr,sd7.text jbJyzkStr,sd8.text jbZcdStr,
		sd9.text jbZcdjwStr,sd10.text jbGjczqyStr,sd11.text jbSsbmStr,sd12.text jbGzjgjgStr,
		sd13.text jbZyhyList,sd35.text jbZyhyList2, sd36.text jbZyhyList3 ,sd37.text jbZyhyList1
		,sd14.text jbSfzyStr,sd15.text jbSfztjnStr,sd16.text jbSftsmdgsStr,
		sd17.text jbZcmdStr,sd18.text jbSfczgrdcgStr,sd19.text jbRelaStr,sd20.text jbHgqyStr,
		sd21.text jbSfyzStr,sd22.text jbBdcqdjqxStr,sd23.text jbZxcqdjqxStr,rbi.rg_type rgType,sd24.text jbQljhStr,
		sd25.text jbQysbsbzxzStr,sd26.text jbSdsbzxzStr,sd27.text jbCzebzxzStr,sd28.text jbSjzczbbzxzStr,
		sd29.text jbRjzbbzxzStr,sd30.text jbZczbbzxzStr,sd31.text jbZczbbzStr, o.ORGANIZATION_NAME as jdZyczr
		from view_cq_jbxxb_noDelete t
		left join sys_users su1 on su1.user_id = t.create_user
		left join rg_business_info rbi on rbi.jbxx_id=t.id
		left join sys_users su2 on su2.user_id = t.last_update_user
		left join sys_dictionary sd1 on sd1.val=t.jb_jnjw and sd1.type_id=(select id from sys_dictionary where type_code='JNJW')
		left join sys_dictionary sd2 on sd2.val=t.jb_sfybgs and sd2.type_id=(select id from sys_dictionary where type_code='SFYBGS')
		left join sys_dictionary sd3 on sd3.val=t.jb_zycqdjqx and sd3.type_id=(select id from sys_dictionary where type_code='ZYCQDJQX')
		left join sys_dictionary sd4 on sd4.val=t.jb_qylb and sd4.type_id=(select id from sys_dictionary where type_code='QYLB')
		left join sys_dictionary sd5 on sd5.val=t.jb_zzxs and sd5.type_id=(select id from sys_dictionary where type_code='ZZXS')
		left join sys_dictionary sd6 on sd6.val=t.jb_qyjc and sd6.type_id=(select id from sys_dictionary where type_code='QYJC')
		left join sys_dictionary sd7 on sd7.val=t.jb_jyzk and sd7.type_id=(select id from sys_dictionary where type_code='JYZK')
		left join sys_dictionary sd8 on sd8.val=t.jb_zcd and sd8.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'ZCD_OLD' else 'ZCD' end))
		left join sys_dictionary sd9 on sd9.val=t.jb_zcdjw and sd9.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'ZCDJW_OLD' else 'ZCDJW' end))
		left join sys_dictionary sd10 on sd10.val=t.jb_gjczqy and sd10.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		left join sys_dictionary sd11 on sd11.val=t.jb_ssbm and sd11.type_id=(select id from sys_dictionary where type_code='SSBM')
		left join sys_dictionary sd12 on sd12.val=t.jb_gzjgjg and sd12.type_id=(select id from sys_dictionary where type_code='GZJGJG')
		left join sys_dictionary sd13 on FIND_IN_SET(sd13.val,t.jb_zyhy) and sd13.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd35 on FIND_IN_SET(sd35.val,t.jb_zyhy2) and sd35.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd36 on FIND_IN_SET(sd36.val,t.jb_zyhy3) and sd36.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd14 on sd14.val=t.jb_sfzy and sd14.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd37 on FIND_IN_SET(sd37.val,t.jb_zyhy1) and sd37.type_id=(select id from sys_dictionary where type_code=
		    (case when t.JB_DATA_STATUS = 'old' then 'INDUSTRY_CLASSIFICATION_TREE_OLD' else 'INDUSTRY_CLASSIFICATION_TREE' end))
		left join sys_dictionary sd15 on sd15.val=t.jb_sfztjn and sd15.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd16 on sd16.val=t.jb_sftsmdgs and sd16.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd17 on sd17.val=t.jb_zcmd and sd17.type_id=(select id from sys_dictionary where type_code='ZCMD')
		left join sys_dictionary sd18 on sd18.val=t.jb_sfczgrdcg and sd18.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd19 on sd19.val=t.jb_rela and sd19.type_id=(select id from sys_dictionary where type_code='RELA')
		left join sys_dictionary sd20 on sd20.val=t.jb_hgqy and sd20.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd21 on sd21.val=t.jb_sfyz and sd21.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd22 on sd22.val=t.jb_bdcqdjqx and sd22.type_id=(select id from sys_dictionary where type_code='BDCQDJQX')
		left join sys_dictionary sd23 on sd23.val=t.jb_zxcqdjqx and sd23.type_id=(select id from sys_dictionary where type_code='ZXCQDJQX')
		left join sys_dictionary sd24 on sd24.val=t.jb_qljh and sd24.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd25 on sd25.val=t.jb_qysbsbzxz and sd25.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd26 on sd26.val=t.jb_sdsbzxz and sd26.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd27 on sd27.val=t.jb_czebzxz and sd27.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd28 on sd28.val=t.jb_sjzczbbzxz and sd28.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd29 on sd29.val=t.jb_rjzbbzxz and sd29.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd30 on sd30.val=t.jb_zczbbzxz and sd30.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd31 on sd31.val=t.jb_zczbbz and sd31.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_organization o on o.ORGANIZATION_ID = t.jb_czrzzjgid
		where t.UNITID = #{unitid} and t.jb_shzt = '4' and rbi.RG_UNITSTATE = '2'
		<if test="id != null and id != ''">
			and t.id != #{id}
			and rbi.RG_DATE &lt; (select rg_date from rg_business_info where jbxx_id = #{id} order by rg_date limit 1)
		</if>
		order by rbi.rg_date desc
		limit 1
		<!--where t.id = (SELECT JBXX_ID FROM rg_business_info where RG_UNITSTATE = '2' and UNITID = #{unitid}
					  <if test="id != null and id != ''">
						 and rg_date &lt; (select rg_date from rg_business_info where jbxx_id = #{id} order by rg_date desc limit 1)
					  </if>
					  order by rg_date desc limit 1)-->
	</select>
	<select id="loadRecentApprovedByUnitIdNew" parameterType="com.zjhc.gzwcq.jbxxb.entity.Jbxxb" resultType="hashmap">
		select
		GROUP_CONCAT(sd13.text ORDER BY sd13.id) jbZyhyList,
		GROUP_CONCAT(sd35.text ORDER BY sd35.id) jbZyhyList2,
		GROUP_CONCAT(sd36.text ORDER BY sd36.id) jbZyhyList3,
		GROUP_CONCAT(sd37.text ORDER BY sd37.id) jbZyhyList1
		from view_cq_jbxxb_noDelete t
		left join sys_dictionary sd37 on FIND_IN_SET(sd37.val,t.jb_zyhy1) and sd37.type_id=(select id from sys_dictionary where type_code='INDUSTRY_CLASSIFICATION_TREE')

		left join sys_dictionary sd35 on FIND_IN_SET(sd35.val,t.jb_zyhy2) and sd35.type_id=(select id from sys_dictionary where type_code='INDUSTRY_CLASSIFICATION_TREE')
		left join sys_dictionary sd36 on FIND_IN_SET(sd36.val,t.jb_zyhy3) and sd36.type_id=(select id from sys_dictionary where type_code='INDUSTRY_CLASSIFICATION_TREE')
		left join sys_users su1 on su1.user_id = t.create_user
		left join rg_business_info rbi on rbi.jbxx_id=t.id
		left join sys_users su2 on su2.user_id = t.last_update_user
		left join sys_dictionary sd1 on sd1.val=t.jb_jnjw and sd1.type_id=(select id from sys_dictionary where type_code='JNJW')
		left join sys_dictionary sd2 on sd2.val=t.jb_sfybgs and sd2.type_id=(select id from sys_dictionary where type_code='SFYBGS')
		left join sys_dictionary sd3 on sd3.val=t.jb_zycqdjqx and sd3.type_id=(select id from sys_dictionary where type_code='ZYCQDJQX')
		left join sys_dictionary sd4 on sd4.val=t.jb_qylb and sd4.type_id=(select id from sys_dictionary where type_code='QYLB')
		left join sys_dictionary sd5 on sd5.val=t.jb_zzxs and sd5.type_id=(select id from sys_dictionary where type_code='ZZXS')
		left join sys_dictionary sd6 on sd6.val=t.jb_qyjc and sd6.type_id=(select id from sys_dictionary where type_code='QYJC')
		left join sys_dictionary sd7 on sd7.val=t.jb_jyzk and sd7.type_id=(select id from sys_dictionary where type_code='JYZK')
		left join sys_dictionary sd8 on sd8.val=t.jb_zcd and sd8.type_id=(select id from sys_dictionary where type_code='ZCD')
		left join sys_dictionary sd9 on sd9.val=t.jb_zcdjw and sd9.type_id=(select id from sys_dictionary where type_code='ZCDJW')
		left join sys_dictionary sd10 on sd10.val=t.jb_gjczqy and sd10.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		left join sys_dictionary sd11 on sd11.val=t.jb_ssbm and sd11.type_id=(select id from sys_dictionary where type_code='SSBM')
		left join sys_dictionary sd12 on sd12.val=t.jb_gzjgjg and sd12.type_id=(select id from sys_dictionary where type_code='GZJGJG')
		left join sys_dictionary sd13 on FIND_IN_SET(sd13.val,t.jb_zyhy) and sd13.type_id=(select id from sys_dictionary where type_code='INDUSTRY_CLASSIFICATION_TREE')
		left join sys_dictionary sd14 on sd14.val=t.jb_sfzy and sd14.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd15 on sd15.val=t.jb_sfztjn and sd15.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd16 on sd16.val=t.jb_sftsmdgs and sd16.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd17 on sd17.val=t.jb_zcmd and sd17.type_id=(select id from sys_dictionary where type_code='ZCMD')
		left join sys_dictionary sd18 on sd18.val=t.jb_sfczgrdcg and sd18.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd19 on sd19.val=t.jb_rela and sd19.type_id=(select id from sys_dictionary where type_code='RELA')
		left join sys_dictionary sd20 on sd20.val=t.jb_hgqy and sd20.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd21 on sd21.val=t.jb_sfyz and sd21.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd22 on sd22.val=t.jb_bdcqdjqx and sd22.type_id=(select id from sys_dictionary where type_code='BDCQDJQX')
		left join sys_dictionary sd23 on sd23.val=t.jb_zxcqdjqx and sd23.type_id=(select id from sys_dictionary where type_code='ZXCQDJQX')
		left join sys_dictionary sd24 on sd24.val=t.jb_qljh and sd24.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd25 on sd25.val=t.jb_qysbsbzxz and sd25.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd26 on sd26.val=t.jb_sdsbzxz and sd26.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd27 on sd27.val=t.jb_czebzxz and sd27.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd28 on sd28.val=t.jb_sjzczbbzxz and sd28.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd29 on sd29.val=t.jb_rjzbbzxz and sd29.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd30 on sd30.val=t.jb_zczbbzxz and sd30.type_id=(select id from sys_dictionary where type_code='BZXZ')
		left join sys_dictionary sd31 on sd31.val=t.jb_zczbbz and sd31.type_id=(select id from sys_dictionary where type_code='BZXZ')
		where t.UNITID = #{unitid} and t.jb_shzt = '4' and rbi.RG_UNITSTATE = '2'
		<if test="id != null and id != ''">
			and t.id != #{id}
			and rbi.RG_DATE &lt; (select rg_date from rg_business_info where jbxx_id = #{id} order by rg_date limit 1)
		</if>
		GROUP BY
		t.id
		order by rbi.rg_date desc
		limit 1
	</select>

	<select id="countUnapprovedByUnitId" resultType="java.lang.Integer" parameterType="com.zjhc.gzwcq.jbxxb.entity.Jbxxb">
		SELECT count(1) FROM view_cq_jbxxb_noDelete cj
								 JOIN rg_business_info rbi ON cj.id = rbi.JBXX_ID
		WHERE cj.UNITID = #{unitid}
		  AND (cj.JB_SHZT != #{jbShzt} or rbi.RG_UNITSTATE != '2')
	</select>

	<update id="updateGsdjzlToNull" parameterType="java.lang.String">
		update cq_jbxxb set jb_gsdjxgzl = null where id = #{id}
	</update>

	<select id="hasSubordinate" resultType="java.lang.Long" parameterType="com.zjhc.gzwcq.jbxxb.entity.Jbxxb">
		SELECT count(1) FROM sys_organization where PARENT_ID = #{unitid} and isdeleted = 'N'
	</select>

	<select id="queryTotalDj" parameterType="com.zjhc.gzwcq.jbxxb.entity.JbxxbParam" resultType="java.lang.Long">
		select
		count(z.ID)
		from (
		select t.id from
		view_cq_jbxxb_noDelete t
		JOIN rg_business_info cj ON t.id = cj.jbxx_id
		left join sys_organization org on t.UNITID = org.ORGANIZATION_ID
		where 1=1 and org.PARENTS like concat(concat('%',#{userOrgId}),'%')
		and t.JB_SHZT = '4' and cj.RG_UNITSTATE = '2'
		<include refid="whereSql2" />
		<choose>
			<!-- 登记表查2级及以下企业 -->
			<when test="djType != null and djType == '1'.toString()">
				and t.JB_QYJC is not null and t.JB_QYJC != '01'
			</when>
			<!-- 登记表查1级企业 -->
			<when test="djType != null and djType == '2'.toString()">
				and t.JB_QYJC = '01'
			</when>
		</choose>
		<if test="isGroup != null and isGroup == 'Y'.toString()">
			group by t.JB_ZZJGDM
		</if>
		) z
	</select>

	<!-- 列表页查询 -->
	<select id="queryDjForList" parameterType="com.zjhc.gzwcq.jbxxb.entity.JbxxbParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,sd1.text jbSfybgsStr,sd2.text jbShztStr
		from
		(select * from view_cq_jbxxb_noDelete ORDER BY CREATE_TIME desc limit 10000000) t
		JOIN rg_business_info cj ON t.id = cj.jbxx_id
		left join sys_organization org on t.UNITID = org.ORGANIZATION_ID
		left join sys_dictionary sd1 on sd1.val=t.jb_sfybgs and sd1.type_id=(select id from sys_dictionary where type_code='SFYBGS')
		left join sys_dictionary sd2 on sd2.val=t.jb_shzt and sd2.type_id=(select id from sys_dictionary where type_code='JBXX_REVIEW_STATUS')
		where 1=1 and org.PARENTS like concat(concat('%',#{userOrgId}),'%')
		and t.JB_SHZT = '4' and cj.RG_UNITSTATE = '2'
		<include refid="whereSql2" />
		<choose>
			<!-- 登记表查2级及以下企业 -->
			<when test="djType != null and djType == '1'.toString()">
				and t.JB_QYJC is not null and t.JB_QYJC != '01'
			</when>
			<!-- 登记表查1级企业 -->
			<when test="djType != null and djType == '2'.toString()">
				and t.JB_QYJC = '01'
			</when>
		</choose>
		<if test="isGroup != null and isGroup == 'Y'.toString()">
			group by t.JB_ZZJGDM
		</if>
		order by t.create_time desc
	</select>

	<select id="selectWbgsNumByLoginUser" resultType="java.lang.Integer">
		SELECT count(1)
		FROM
		(SELECT unitid, max(RG_DATE) maxTime FROM rg_business_info GROUP BY UNITID) temp
		JOIN rg_business_info rbi ON rbi.UNITID = temp.UNITID AND temp.maxTime = rbi.RG_DATE
		JOIN view_cq_jbxxb_noDelete t ON t.id = rbi.JBXX_ID
		<where>
			t.id is not null
			and rbi.RG_UNITSTATE = '9' and t.jb_shzt = '9'
			and (t.jb_zycqdjqx is null and t.unitid in (select t1.ORGANIZATION_ID from (
			<foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
				(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
			</foreach>)as t1)
			or t.jb_zycqdjqx is not null and t.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
			<foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
				(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
			</foreach>) as t2))
		</where>
	</select>

	<select id="companyNumByZzxs" resultType="java.util.Map">
		<!--		SELECT
                cj.jb_zzxs val,sd1.text text,count(t.ORGANIZATION_ID) num,0 rate
                FROM
                sys_organization t
                join (select UNITID,max(RG_TIMEMARK) mdate from rg_business_info group by unitid) temp
                on temp.UNITID = t.ORGANIZATION_ID
                JOIN rg_business_info rbi on rbi.RG_TIMEMARK = temp.mdate and rbi.UNITID = temp.UNITID
                JOIN view_cq_jbxxb_noDelete cj ON cj.id = rbi.JBXX_ID
                join sys_dictionary sd1 on sd1.val = cj.jb_zzxs
                join sys_dictionary sd2 on sd1.type_id = sd2.id
                where t.isdeleted = 'N' and cj.JB_SHZT = '4' and rbi.RG_UNITSTATE = '2'
                and rbi.rg_type in ('0','1')
                and cj.JB_ZXCQDJQX is null and jb_zzxs is not null and sd2.type_code='ZZXS'
                and cj.jb_zzxs != '10' and cj.jb_zzxs != '20' and cj.jb_zzxs != '30'
                and t.ORGANIZATION_ID in
                    (select t1.ORGANIZATION_ID from (
                    <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                        (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                    </foreach>)as t1)
                group by cj.jb_zzxs,sd1.text-->
		select cj.jb_zzxs val,sd1.text text,count(distinct cj.UNITID) num,0 rate
		from view_cq_jbxxb_noDelete cj join (SELECT
		tt.UNITID,
		max( t.RG_TIMEMARK ) maxdate
		FROM
		rg_business_info t join view_cq_jbxxb_noDelete tt on t.JBXX_ID = tt.id
		WHERE
		tt.JB_SHZT = '4'
		and t.RG_UNITSTATE = '2'
		AND t.UNITID NOT IN (
		SELECT
		t1.UNITID
		FROM
		rg_business_info t
		JOIN view_cq_jbxxb_noDelete t1 ON t.jbxx_id = t1.id
		WHERE
		t.rg_type = '3'
		AND t1.JB_SHZT = '4'
		)
		GROUP BY
		tt.UNITID) tt
		on cj.UNITID = tt.UNITID
		join rg_business_info ttt on cj.id = ttt.JBXX_ID and ttt.RG_TIMEMARK = tt.maxdate
		join sys_dictionary sd1 on sd1.val = cj.jb_zzxs
		join sys_dictionary sd2 on sd1.type_id = sd2.id
		where cj.JB_SHZt = '4'
		and sd2.type_code='ZZXS'
		<!--and cj.jb_zzxs != '10' and cj.jb_zzxs != '20' and cj.jb_zzxs != '30'-->
		and cj.unitid IN
		(select t1.ORGANIZATION_ID from (
		<foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
			(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
		</foreach>)as t1)
		group by cj.jb_zzxs,sd1.text
		order by num desc
	</select>

	<select id="companyNumByZzxsNew" resultType="java.util.Map">
		SELECT
			jb.JB_ZZXS as zzxs,COUNT(1) num
		FROM
			(
				SELECT
					jb.ID,jb.RG_TIMEMARK,jb.JB_ZZXS
				FROM(
						SELECT
							jb.ID,inf.RG_TIMEMARK,jb.UNITID,jb.JB_ZZXS
						FROM
		(select t1.ORGANIZATION_ID from (
		<foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
			(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS) and isdeleted = 'N')
		</foreach>
		    )as t1
		    ) o JOIN
							rg_business_info inf on inf.UNITID = o.ORGANIZATION_ID
											   JOIN
							cq_jbxxb jb on jb.ID = inf.JBXX_ID
						WHERE
							jb.JB_SHZT = 4
						  AND
							inf.RG_UNITSTATE = 2
						ORDER BY
							inf.RG_TIMEMARK desc
						LIMIT 10000000000000000000
					) jb
				GROUP BY
					jb.UNITID
			) jb
		WHERE
			(jb.JB_ZZXS is not null and jb.JB_ZZXS != '')
		and
			jb.JB_ZZXS in(
				SELECT
				val
				FROM
				sys_dictionary dic
				WHERE
				dic.type_id IN(
				SELECT
				dic.id
				FROM
				sys_dictionary dic
				WHERE
				dic.type_code = 'ZZXS'
				)
				and dic.disabled is null
		)
		GROUP BY
			jb.JB_ZZXS
	</select>
	<select id="loadZzxs" resultType="com.boot.IAdmin.dict.model.DictionaryVo">
		SELECT
			val, text
		FROM
			sys_dictionary dic
		WHERE
				dic.type_id IN(
				SELECT
					dic.id
				FROM
					sys_dictionary dic
				WHERE
					dic.type_code = 'ZZXS'
			)
		  and dic.disabled is null
	</select>
	<select id="companyNumBySeason" resultType="java.util.Map">
		<!--		SELECT
                cj.jb_qylb val,sd1.text text,count(t.ORGANIZATION_ID) num,0 season
                FROM
                sys_organization t
                join (select UNITID,max(RG_TIMEMARK) mdate from rg_business_info group by unitid
                        having date_format(max(RG_TIMEMARK),'%Y%m%d') &lt;= #{seasonLastDay}) temp
                on temp.UNITID = t.ORGANIZATION_ID
                JOIN rg_business_info rbi on rbi.RG_TIMEMARK = temp.mdate and rbi.UNITID = temp.UNITID
                JOIN view_cq_jbxxb_noDelete cj ON cj.id = rbi.JBXX_ID
                join sys_dictionary sd1 on sd1.val = cj.jb_qylb
                join sys_dictionary sd2 on sd1.type_id = sd2.id
                where t.isdeleted = 'N' and cj.JB_SHZT = '4' and rbi.RG_UNITSTATE = '2'
                and cj.JB_ZXCQDJQX is null and rbi.rg_type in ('0','1')
                  and sd2.type_code='QYLB'
                and t.ORGANIZATION_ID in
                (select t1.ORGANIZATION_ID from (
                <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                    (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                </foreach>)as t1)
                group by cj.jb_qylb,sd1.text-->
		select cj.jb_qylb val,sd1.text text,count(distinct cj.UNITID) num,QUARTER(#{seasonLastDay}) season from view_cq_jbxxb_noDelete cj join (SELECT
		tt.UNITID,
		max( t.RG_TIMEMARK ) maxdate
		FROM
		rg_business_info t join view_cq_jbxxb_noDelete tt on t.JBXX_ID = tt.id
		WHERE
		date_format( t.RG_TIMEMARK, '%Y%m%d' ) &lt;= #{seasonLastDay}
		and tt.JB_SHZT = '4'
		and t.RG_UNITSTATE = '2'
		AND t.UNITID NOT IN (
		SELECT
		t1.UNITID
		FROM
		rg_business_info t
		JOIN view_cq_jbxxb_noDelete t1 ON t.jbxx_id = t1.id
		WHERE
		date_format( t.RG_TIMEMARK, '%Y%m%d' ) &lt;= #{seasonLastDay}
		AND t.rg_type = '3'
		AND t1.JB_SHZT = '4'
		)
		GROUP BY
		tt.UNITID) tt
		on  cj.UNITID = tt.UNITID
		join rg_business_info ttt on cj.id = ttt.JBXX_ID and ttt.RG_TIMEMARK = tt.maxdate
		join sys_dictionary sd1 on sd1.val = cj.jb_qylb
		join sys_dictionary sd2 on sd1.type_id = sd2.id
		where cj.JB_SHZt = '4'
		and sd2.type_code='QYLB'
		and cj.unitid IN
		(select t1.ORGANIZATION_ID from (
		<foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
			(select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
		</foreach>)as t1)
		group by cj.jb_qylb,sd1.text
	</select>

	<select id="queryTimelinessRateList" resultType="com.zjhc.gzwcq.screen.entity.TimelinessRate">
		select a.BUSINESS_INFO_ID as businessInfoId,
		c.JB_SFYBGS as jbSfybgs, c.jb_gsdjrq as jbGsdjrq
		from (
		select n.* from (
		select a.* from rg_auditflow_history a
		LEFT JOIN sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where a.AF_PROCESSTYPE = '3'
		<choose>
			<when test="type != null and type == '2'.toString()">
				and FIND_IN_SET(#{orgId},d.PARENTS)
			</when>
			<otherwise>
				and a.AF_PROCESSUNITID = #{orgId}
			</otherwise>
		</choose>
		<choose>
			<when test="year != null and year != ''">
				and YEAR(a.CREATE_TIME) = #{year}
			</when>
			<otherwise>
				and YEAR(a.CREATE_TIME) = YEAR(NOW())
			</otherwise>
		</choose>
		ORDER BY CREATE_TIME limit 10000000
		) n GROUP BY n.BUSINESS_INFO_ID
		) a
		LEFT JOIN rg_business_info b on a.BUSINESS_INFO_ID = b.id
		left JOIN view_cq_jbxxb_noDelete c on b.JBXX_ID = c.id
		LEFT JOIN sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where c.BUSINESS_NATURE = '1'
	</select>

	<select id="queryAllApprovalDate" resultType="com.zjhc.gzwcq.screen.entity.TimelinessRate">
		select z.BUSINESS_INFO_ID as businessInfoId,
		z.CREATE_TIME as afProcessDate
		from(
		select a.* from rg_auditflow_history a
		LEFT JOIN sys_organization b on a.AF_PROCESSUNITID = b.ORGANIZATION_ID
		where b.BUSINESSTYPE = '1'
		and a.BUSINESS_INFO_ID in
		<foreach collection="list" separator="," open="(" close=")" index="i" item="item">
			#{item}
		</foreach>
		ORDER BY a.CREATE_TIME limit 1000000) z
		GROUP BY z.BUSINESS_INFO_ID
	</select>

	<select id="queryAllSubmitList" resultType="String">
		select a.BUSINESS_INFO_ID,d.PARENTS
		from rg_auditflow_history a
		LEFT JOIN rg_business_info b on a.BUSINESS_INFO_ID = b.id
		left JOIN cq_jbxxb c on b.JBXX_ID = c.id
		LEFT JOIN sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where a.AF_PROCESSTYPE = '3'
		and c.BUSINESS_NATURE = '1'
		<choose>
			<when test="year != null and year != ''">
				and YEAR(a.CREATE_TIME) = #{year}
			</when>
			<otherwise>
				and YEAR(a.CREATE_TIME) = YEAR(NOW())
			</otherwise>
		</choose>
		<choose>
			<when test="type != null and type == '2'.toString()">
				and FIND_IN_SET(#{orgId},d.PARENTS)
			</when>
			<otherwise>
				and a.AF_PROCESSUNITID = #{orgId}
			</otherwise>
		</choose>
	</select>

	<select id="queryAllReturnList" resultType="Integer">
		select count(1)
		from rg_auditflow_history a
		left join sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where a.AF_PROCESSTYPE = '2'
		and d.BUSINESSTYPE = '1'
		and a.BUSINESS_INFO_ID in
		<foreach collection="list" separator="," open="(" close=")" index="i" item="item">
			#{item}
		</foreach>
		<choose>
			<when test="year != null and year != ''">
				and YEAR(a.CREATE_TIME) = #{year}
			</when>
			<otherwise>
				and YEAR(a.CREATE_TIME) = YEAR(NOW())
			</otherwise>
		</choose>
	</select>
	<select id="queryAllWaitList" resultType="Integer">
		select count(1)
		from rg_auditflow_history a
		left join sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where a.AF_PROCESSTYPE = '6'
		and d.BUSINESSTYPE = '1'
		and a.BUSINESS_INFO_ID in
		<foreach collection="list" separator="," open="(" close=")" index="i" item="item">
			#{item}
		</foreach>
		<choose>
			<when test="year != null and year != ''">
				and YEAR(a.CREATE_TIME) = #{year}
			</when>
			<otherwise>
				and YEAR(a.CREATE_TIME) = YEAR(NOW())
			</otherwise>
		</choose>
	</select>


	<select id="selectHasGzwApprovalCount" resultType="com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo">
		select t2.*,t3.BUSINESSTYPE as businesstype
		from view_cq_jbxxb_noDelete t
				 left join rg_business_info t1 on t.id = t1.JBXX_ID
				 left join rg_auditflow_history t2 on t1.id = t2.BUSINESS_INFO_ID
				 left join sys_organization t3 on t2.AF_PROCESSUNITID = t3.ORGANIZATION_ID
		where t.id = #{jbxxId}
		order by t2.CREATE_TIME
	</select>

	<select id="loadRecentApprovedByOpenApi" resultMap="resultMapWithZyhy">
		select t.*,'' createUserStr,'' lastUpdateUserStr, '' as jbZyhyList
		from
		(
		select <include refid="columnsAliasForOpenApi"/>
		from view_cq_jbxxb a
		left join rg_business_info rbi on a.id = rbi.jbxx_id
		where a.jb_shzt = '4'
		and a.UNITID in
		<foreach collection="orgId" item="orgIds" close=")" separator="," open="(" >
			#{orgIds}
		</foreach>
		order by rbi.rg_date desc limit 1000000
		) t
		group by t.UNITID
	</select>

	<sql id="columnsAliasForOpenApi">
		a.id,
		a.unitid,
		a.datatime,
		a.floatorder,
		a.jb_zczb,
		a.jb_gjsbs,
		a.jb_gjsds,
		a.jb_gyfrsbs,
		a.jb_gyfrsds,
		a.jb_gyjdkgsbs,
		a.jb_gyjdkgsds,
		a.jb_gysjkzsbs,
		a.jb_gysjkzsds,
		a.jb_qtqysbs,
		a.jb_qtsds,
		a.jb_hjqysbs,
		a.jb_hjsds,
		a.jb_sjblrq,
		a.jb_hjcze,
		a.jb_hjsjzcj,
		a.jb_hjbl,
		a.jb_qymc,
		a.jb_zzjgdm,
		a.jb_zcmd,
		a.jb_cgrxm,
		a.jb_jhqlsj,
		a.jb_sjczr,
		a.jb_czrzzjgdm,
		a.jb_hjsjzcjbz,
		a.jb_gjczsdsbz,
		a.jb_gjczqysbsbz,
		a.jb_gyfrczsbsbz,
		a.jb_gyfrczsdsbz,
		a.jb_gyjdkgfrsbsbz,
		a.jb_gyjdkgfrsdsbz,
		a.jb_gysjkzfrsbsbz,
		a.jb_gysjkzfrsdsbz,
		a.jb_qtsbsbz,
		a.jb_qtqysdsbz,
		a.jb_hjqysbsbz,
		a.jb_hjsdsbz,
		a.jb_shzt,
		a.jb_sfzy,
		a.jb_zzxs,
		a.jb_qylb,
		a.jb_qyjc,
		a.jb_ssbm,
		a.jb_jyzk,
		a.jb_sftsmdgs,
		a.jb_sfczgrdcg,
		a.jb_gjczqy,
		a.jb_qysbsbzxz,
		a.jb_sdsbzxz,
		a.jb_jnjw,
		a.jb_gzjgjg,
		a.jb_zcd,
		a.jb_gsdjrq,
		a.jb_zcrq,
		a.jb_sfybgs,
		a.jb_gsblzk,
		a.jb_zyhy,
		a.jb_zcdjw,
		a.jb_hjcjebz,
		a.jb_czebzxz,
		a.jb_sjzczbbzxz,
		a.jb_sfztjn,
		a.jb_zczbbz,
		a.jb_zczbjw,
		a.jb_xzqy,
		a.jb_shtgrq,
		a.jb_dylsh,
		a.jb_ssgzjgjg,
		a.jb_hjrjzb,
		a.jb_rjzbbzxz,
		a.jb_hjrjzbbz,
		a.jb_zczbbzxz,
		a.jb_gykgcz,
		a.jb_gykgczsbs,
		a.business_nature,
		a.jb_gyzb,
		a.jb_rela,
		a.jb_hgqy,
		a.jb_czrzzjgid
	</sql>
	<sql id="selectRecentOneDataByorgIdSql">
		cqjb.id,
		cqjb.unitid,
		cqjb.jb_zczb,
		cqjb.jb_gjsbs,
		cqjb.jb_gjsds,
		cqjb.jb_gyfrsbs,
		cqjb.jb_gyfrsds,
		cqjb.jb_gyjdkgsbs,
		cqjb.jb_gyjdkgsds,
		cqjb.jb_gysjkzsbs,
		cqjb.jb_gysjkzsds,
		cqjb.jb_qtqysbs,
		cqjb.jb_qtsds,
		cqjb.jb_hjqysbs,
		cqjb.jb_hjsds,
		cqjb.jb_sjblrq,
		cqjb.jb_hjcze,
		cqjb.jb_hjsjzcj,
		cqjb.jb_hjbl,
		cqjb.jb_qymc,
		cqjb.jb_zzjgdm,
		cqjb.jb_zcmd,
		cqjb.jb_cgrxm,
		cqjb.jb_jhqlsj,
		cqjb.jb_sjczr,
		cqjb.jb_czrzzjgdm,
		cqjb.jb_hjsjzcjbz,
		cqjb.jb_gjczsdsbz,
		cqjb.jb_gjczqysbsbz,
		cqjb.jb_gyfrczsbsbz,
		cqjb.jb_gyfrczsdsbz,
		cqjb.jb_gyjdkgfrsbsbz,
		cqjb.jb_gyjdkgfrsdsbz,
		cqjb.jb_gysjkzfrsbsbz,
		cqjb.jb_gysjkzfrsdsbz,
		cqjb.jb_qtsbsbz,
		cqjb.jb_qtqysdsbz,
		cqjb.jb_hjqysbsbz,
		cqjb.jb_hjsdsbz,
		cqjb.jb_sfzy,
		cqjb.jb_zzxs,
		cqjb.jb_qylb,
		cqjb.jb_qyjc,
		cqjb.jb_ssbm,
		cqjb.jb_jyzk,
		cqjb.jb_sftsmdgs,
		cqjb.jb_sfczgrdcg,
		cqjb.jb_gjczqy,
		cqjb.jb_qysbsbzxz,
		cqjb.jb_sdsbzxz,
		cqjb.jb_jnjw,
		cqjb.jb_zycqdjqx,
		cqjb.jb_bdcqdjqx,
		cqjb.jb_zxcqdjqx,
		cqjb.jb_gzjgjg,
		cqjb.jb_zcd,
		cqjb.jb_gsdjrq,
		cqjb.jb_zcrq,
		cqjb.jb_sfybgs,
		cqjb.jb_gsblzk,
		cqjb.jb_gsdjxgzl,
		cqjb.jb_sfyz,
		cqjb.jb_byzly,
		cqjb.jb_zyhy,
		cqjb.jb_zcdjw,
		cqjb.jb_hjcjebz,
		cqjb.jb_czebzxz,
		cqjb.jb_sjzczbbzxz,
		cqjb.jb_sfztjn,
		cqjb.jb_zczbbz,
		cqjb.jb_zczbjw,
		cqjb.jb_sfzdyjsj,
		cqjb.jb_sfsjscdm,
		cqjb.jb_xzqy,
		cqjb.jb_shtgrq,
		cqjb.jb_dylsh,
		cqjb.jb_ssgzjgjg,
		cqjb.jb_qljh,
		cqjb.jb_hjrjzb,
		cqjb.jb_rjzbbzxz,
		cqjb.jb_hjrjzbbz,
		cqjb.jb_zczbbzxz,
		cqjb.jb_gykgcz,
		cqjb.jb_gykgczsbs,
		cqjb.create_time,
		cqjb.business_nature,
		cqjb.jb_gyzb,
		cqjb.jb_rela,
		cqjb.jb_hgqy,
		cqjb.jb_czrzzjgid
	</sql>
	<select id="selectRecentOneDataByorgId" resultMap="baseResultMap">
		SELECT
		<include refid="selectRecentOneDataByorgIdSql"/>
		FROM
		cq_jbxxb cqjb
		JOIN
		(
		SELECT
		cjb.ID
		FROM
		`sys_organization` so
		JOIN
		`cq_jbxxb`  cjb  on cjb.UNITID = so.ORGANIZATION_ID
		JOIN
		`rg_business_info` rbi on rbi.JBXX_ID = cjb.ID
		WHERE
		so.ORGANIZATION_ID = #{orgId}
		AND
		rbi.RG_UNITSTATE = '2'
		AND
		cjb.JB_DELETED = 'N'
		AND
		cjb.JB_SHZT = '4'
		ORDER BY
		rbi.RG_TIMEMARK desc
		LIMIT 1
		) as iscjbb
		ON
		iscjbb.ID = cqjb.ID
	</select>
</mapper>