<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.requestResultInfo.mapper.IRequestResultInfoMapper">

	<resultMap type="com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfo" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="requst_time" property="requstTime"/>
		<result column="requst_app_id" property="requstAppId"/>
		<result column="requst_app_secret" property="requstAppSecret"/>
		<result column="requst_url" property="requstUrl"/>
		<result column="result_info" property="resultInfo"/>
		<result column="requst_success" property="requstSuccess"/>
		<result column="file_upload_id" property="fileUploadId"/>
		<result column="select_status" property="selectStatus"/>
		<result column="true_cont" property="trueCont"/>
		<result column="false_cont" property="falseCont"/>
		<result column="total_cont" property="totalCont"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoVo" extends="baseResultMap">
		<result column="error_message" property="errorMessage"/>
		<result column="error_type" property="errorType"/>
	</resultMap>

	<sql id="columns">
		id, 
		requst_time, 
		requst_app_id, 
		requst_app_secret, 
		requst_url, 
		result_info, 
		requst_success, 
		file_upload_id, 
		select_status, 
		true_cont, 
		false_cont, 
		total_cont
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.requst_time, 
		t.requst_app_id, 
		t.requst_app_secret, 
		t.requst_url, 
		t.result_info, 
		t.requst_success, 
		t.file_upload_id, 
		t.select_status, 
		t.true_cont, 
		t.false_cont, 
		t.total_cont
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{requstTime}, 
		#{requstAppId}, 
		#{requstAppSecret}, 
		#{requstUrl}, 
		#{resultInfo}, 
		#{requstSuccess}, 
		#{fileUploadId}, 
		#{selectStatus}, 
		#{trueCont}, 
		#{falseCont}, 
		#{totalCont}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null">
			and t.id = #{id}
		</if>
		<if test="requstTime != null">
			and t.requst_time = #{requstTime}
		</if>
		<if test="requstAppId != null and requstAppId != ''">
			and t.requst_app_id = #{requstAppId}
		</if>
		<if test="requstAppSecret != null and requstAppSecret != ''">
			and t.requst_app_secret = #{requstAppSecret}
		</if>
		<if test="requstUrl != null and requstUrl != ''">
			and t.requst_url = #{requstUrl}
		</if>
		<if test="resultInfo != null and resultInfo != ''">
			and t.result_info = #{resultInfo}
		</if>
		<if test="requstSuccess != null and requstSuccess != ''">
			and t.requst_success = #{requstSuccess}
		</if>
		<if test="fileUploadId != null and fileUploadId != ''">
			and t.file_upload_id = #{fileUploadId}
		</if>
		<if test="selectStatus != null and selectStatus != ''">
			and t.select_status = #{selectStatus}
		</if>
		<if test="trueCont != null and trueCont != ''">
			and t.true_cont = #{trueCont}
		</if>
		<if test="falseCont != null and falseCont != ''">
			and t.false_cont = #{falseCont}
		</if>
		<if test="totalCont != null and totalCont != ''">
			and t.total_cont = #{totalCont}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfo" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into api_request_result_info (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update api_request_result_info set isDeleted = 'Y' where
		id in
		<foreach collection="requestResultInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update api_request_result_info set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from api_request_result_info  where
		id in
		<foreach collection="requestResultInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from api_request_result_info  where id = #{id}
	</delete>
	
	<select id="selectRequestResultInfoByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from api_request_result_info
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update api_request_result_info
		<set>
			<if test="requstTime != null">
				requst_time=#{requstTime},
			</if>
			<if test="requstAppId != null">
				requst_app_id=#{requstAppId},
			</if>
			<if test="requstAppSecret != null">
				requst_app_secret=#{requstAppSecret},
			</if>
			<if test="requstUrl != null">
				requst_url=#{requstUrl},
			</if>
			<if test="resultInfo != null">
				result_info=#{resultInfo},
			</if>
			<if test="requstSuccess != null">
				requst_success=#{requstSuccess},
			</if>
			<if test="fileUploadId != null">
				file_upload_id=#{fileUploadId},
			</if>
			<if test="selectStatus != null">
				select_status=#{selectStatus},
			</if>
			<if test="trueCont != null">
				true_cont=#{trueCont},
			</if>
			<if test="falseCont != null">
				false_cont=#{falseCont},
			</if>
			<if test="totalCont != null">
				total_cont=#{totalCont}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update api_request_result_info
		<set>
			requst_time=#{requstTime},
			requst_app_id=#{requstAppId},
			requst_app_secret=#{requstAppSecret},
			requst_url=#{requstUrl},
			result_info=#{resultInfo},
			requst_success=#{requstSuccess},
			file_upload_id=#{fileUploadId},
			select_status=#{selectStatus},
			true_cont=#{trueCont},
			false_cont=#{falseCont},
			total_cont=#{totalCont}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfo" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			api_request_result_info t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalRequestResultInfos" parameterType="com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoParam" resultType="java.lang.Long">
		SELECT
			count(1)
		FROM
			(
				select
					t.id,
					t.requst_time
				from
					api_request_result_info t
				where
					t.requst_success = 1
				ORDER BY
					requst_time DESC LIMIT 1
			)info
				JOIN api_request_erro_info err on err.api_request_info_id = info.id
		where
			err.xybm = #{xybm}
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryRequestResultInfoForList" parameterType="com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoParam" resultMap="baseResultMapExt">
		SELECT
			err.id,
			info.requst_time,
			err.error_type,
			err.qymc,
			err.xybm,
			err.error_message
		FROM
			(
				select
					t.id,
					t.requst_time
				from
					api_request_result_info t
				where
					t.requst_success = 1
				ORDER BY
					requst_time DESC LIMIT 1
			)info
				JOIN api_request_erro_info err on err.api_request_info_id = info.id
			where
				err.xybm = #{xybm}
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfo" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from api_request_result_info t
		where t.id != #{id}
			<if test="requstTime != null">
				and t.requst_time = #{requstTime}
			</if>
			<if test="requstAppId != null and requstAppId != ''">
				and t.requst_app_id = #{requstAppId}
			</if>
			<if test="requstAppSecret != null and requstAppSecret != ''">
				and t.requst_app_secret = #{requstAppSecret}
			</if>
			<if test="requstUrl != null and requstUrl != ''">
				and t.requst_url = #{requstUrl}
			</if>
			<if test="resultInfo != null and resultInfo != ''">
				and t.result_info = #{resultInfo}
			</if>
			<if test="requstSuccess != null and requstSuccess != ''">
				and t.requst_success = #{requstSuccess}
			</if>
			<if test="fileUploadId != null and fileUploadId != ''">
				and t.file_upload_id = #{fileUploadId}
			</if>
			<if test="selectStatus != null and selectStatus != ''">
				and t.select_status = #{selectStatus}
			</if>
			<if test="trueCont != null and trueCont != ''">
				and t.true_cont = #{trueCont}
			</if>
			<if test="falseCont != null and falseCont != ''">
				and t.false_cont = #{falseCont}
			</if>
			<if test="totalCont != null and totalCont != ''">
				and t.total_cont = #{totalCont}
			</if>
	</select>

</mapper>