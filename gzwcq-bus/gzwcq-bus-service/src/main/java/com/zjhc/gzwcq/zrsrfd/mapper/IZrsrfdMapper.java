package com.zjhc.gzwcq.zrsrfd.mapper;

import java.util.Map;
import java.util.List;

import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdVo;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdParam;
import org.apache.ibatis.annotations.Param;

public interface IZrsrfdMapper {

    /*保存对象*/
    void insert(Zrsrfd zrsrfd);

    //物理删除
    void deleteByPrimaryKeys(Map<String, Object> map);

    void deleteByPrimaryKey(@Param("id") String id);

    //逻辑删除
    void logicDeleteByPrimaryKeys(Map<String, Object> map);

    void logicDeleteByPrimaryKey(Map<String, Object> map);

    /*根据非空属性更新对象信息*/
    void updateIgnoreNull(Zrsrfd zrsrfd);

    /**
     * 更新
     */
    void update(Zrsrfd zrsrfd);

    /*分页查询对象*/
    List<ZrsrfdVo> queryZrsrfdForList(ZrsrfdParam zrsrfdParam);

    /*数据总量查询*/
    long queryTotalZrsrfds(ZrsrfdParam zrsrfdParam);

    /*根据主键查询对象*/
    ZrsrfdVo selectZrsrfdByPrimaryKey(Zrsrfd zrsrfd);

    /*根据部分属性对象查询全部结果，不分页*/
    List<Zrsrfd> selectForList(Zrsrfd zrsrfd);

    /**
     * 数据唯一性验证
     */
    List<Zrsrfd> selectForUnique(Zrsrfd zrsrfd);

    /**
     * 根据基本信息表id查数据
     */
    List<ZrsrfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);

    List<Zrsrfd> selectAllByJbxxId(@Param("jbxxId") String jbxxId);
}