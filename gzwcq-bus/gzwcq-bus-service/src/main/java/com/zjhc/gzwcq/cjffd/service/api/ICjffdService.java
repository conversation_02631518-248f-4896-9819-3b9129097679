package com.zjhc.gzwcq.cjffd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.cjffd.entity.Cjffd;
import com.zjhc.gzwcq.cjffd.entity.CjffdVo;
import com.zjhc.gzwcq.cjffd.entity.CjffdParam;

public interface ICjffdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Cjffd cjffd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Cjffd cjffd);
	
	/**
	* 更新
	*/
	void update(Cjffd cjffd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<CjffdVo> queryCjffdByPage(CjffdParam cjffdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalCjffds(CjffdParam cjffdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Cjffd selectCjffdByPrimaryKey(Cjffd cjffd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Cjffd> selectForList(Cjffd cjffd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Cjffd cjffd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Cjffd cjffd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Cjffd[] objs);

    void deleteByJbxxId(String jbxxId);

    List<CjffdVo> selectByJbxxId(String jbxxId);
}