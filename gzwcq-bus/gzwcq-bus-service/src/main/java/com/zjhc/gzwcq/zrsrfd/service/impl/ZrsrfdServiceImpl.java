package com.zjhc.gzwcq.zrsrfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.zrsrfd.mapper.IZrsrfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdVo;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdParam;
import com.zjhc.gzwcq.zrsrfd.service.api.IZrsrfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class ZrsrfdServiceImpl implements IZrsrfdService {
	
	@Autowired
	private IZrsrfdMapper zrsrfdMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		zrsrfdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Zrsrfd zrsrfd){
		zrsrfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		zrsrfd.setCreateTime(new Date());//创建时间
		zrsrfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		zrsrfd.setLastUpdateTime(new Date());//更新时间
		zrsrfdMapper.insert(zrsrfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		zrsrfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		zrsrfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Zrsrfd zrsrfd){
		zrsrfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		zrsrfd.setLastUpdateTime(new Date());//更新时间
		zrsrfdMapper.updateIgnoreNull(zrsrfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Zrsrfd zrsrfd){
		zrsrfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		zrsrfd.setLastUpdateTime(new Date());//更新时间
		zrsrfdMapper.update(zrsrfd);
	}
	
	public List<ZrsrfdVo> queryZrsrfdByPage(ZrsrfdParam zrsrfdParam) {
      	//分页
      	PageHelper.startPage(zrsrfdParam.getPageNumber(),zrsrfdParam.getLimit(),false);
		return zrsrfdMapper.queryZrsrfdForList(zrsrfdParam);
	}
	

	public ZrsrfdVo selectZrsrfdByPrimaryKey(Zrsrfd Zrsrfd) {
		return zrsrfdMapper.selectZrsrfdByPrimaryKey(Zrsrfd);
	}
	
	public long queryTotalZrsrfds(ZrsrfdParam zrsrfdParam) {
		return zrsrfdMapper.queryTotalZrsrfds(zrsrfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Zrsrfd> selectForList(Zrsrfd zrsrfd){
		return zrsrfdMapper.selectForList(zrsrfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Zrsrfd zrsrfd) {
		return zrsrfdMapper.selectForUnique(zrsrfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Zrsrfd zrsrfd) {
		if(StringUtils.isBlank(zrsrfd.getId())) {
			this.insert(zrsrfd);
		}else {
			this.updateIgnoreNull(zrsrfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Zrsrfd[] objs) {
		for(Zrsrfd zrsrfd : objs) {
			this.saveOne(zrsrfd);
		}
	}

	/**
	 * 根据基本信息表id查数据
	 */
	@Override
	public List<ZrsrfdVo> selectByJbxxId(String id) {
		return zrsrfdMapper.selectByJbxxId(id);
	}
}
