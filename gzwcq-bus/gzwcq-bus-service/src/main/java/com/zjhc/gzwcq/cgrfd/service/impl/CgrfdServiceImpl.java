package com.zjhc.gzwcq.cgrfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.cgrfd.mapper.ICgrfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdVo;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdParam;
import com.zjhc.gzwcq.cgrfd.service.api.ICgrfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class CgrfdServiceImpl implements ICgrfdService {
	
	@Autowired
	private ICgrfdMapper cgrfdMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		cgrfdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Cgrfd cgrfd){
		cgrfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		cgrfd.setCreateTime(new Date());//创建时间
		cgrfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		cgrfd.setLastUpdateTime(new Date());//更新时间
		cgrfdMapper.insert(cgrfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		cgrfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		cgrfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Cgrfd cgrfd){
		cgrfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		cgrfd.setLastUpdateTime(new Date());//更新时间
		cgrfdMapper.updateIgnoreNull(cgrfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Cgrfd cgrfd){
		cgrfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		cgrfd.setLastUpdateTime(new Date());//更新时间
		cgrfdMapper.update(cgrfd);
	}
	
	public List<CgrfdVo> queryCgrfdByPage(CgrfdParam cgrfdParam) {
      	//分页
      	PageHelper.startPage(cgrfdParam.getPageNumber(),cgrfdParam.getLimit(),false);
		return cgrfdMapper.queryCgrfdForList(cgrfdParam);
	}
	

	public Cgrfd selectCgrfdByPrimaryKey(Cgrfd Cgrfd) {
		return cgrfdMapper.selectCgrfdByPrimaryKey(Cgrfd);
	}
	
	public long queryTotalCgrfds(CgrfdParam cgrfdParam) {
		return cgrfdMapper.queryTotalCgrfds(cgrfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Cgrfd> selectForList(Cgrfd cgrfd){
		return cgrfdMapper.selectForList(cgrfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Cgrfd cgrfd) {
		return cgrfdMapper.selectForUnique(cgrfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Cgrfd cgrfd) {
		if(StringUtils.isBlank(cgrfd.getId())) {
			this.insert(cgrfd);
		}else {
			this.updateIgnoreNull(cgrfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Cgrfd[] objs) {
		for(Cgrfd cgrfd : objs) {
			this.saveOne(cgrfd);
		}
	}
}
