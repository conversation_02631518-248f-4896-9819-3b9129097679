package com.zjhc.gzwcq.monitorwarn.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import org.apache.ibatis.annotations.Param;

public interface IMonitorwarnMapper {
	
	/*保存对象*/
	void insert(Monitorwarn monitorwarn);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Monitorwarn monitorwarn);
	
	/**更新*/
	void update(Monitorwarn monitorwarn);
	
	/*分页查询对象*/
	List<MonitorwarnVo> queryMonitorwarnForList(MonitorwarnParam monitorwarnParam);
	
	/*数据总量查询*/
	long queryTotalMonitorwarns(MonitorwarnParam monitorwarnParam);
	
	/*根据主键查询对象*/
	Monitorwarn selectMonitorwarnByPrimaryKey(Monitorwarn monitorwarn);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Monitorwarn> selectForList(Monitorwarn monitorwarn);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Monitorwarn> selectForUnique(Monitorwarn monitorwarn);

	/**
	 * 查询预警审核列表数据
	 * @param param
	 * @return
	 */
	List<MonitorwarnVo> selectWarnReviewList(MonitorwarnParam param);

	/**
	 * 统计预警审核数量
	 */
	int selectWarnAuditNum(MonitorwarnParam monitorwarnParam);
}