package com.zjhc.gzwcq.cjffd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.cjffd.client.CjffdFeignClient;
import com.zjhc.gzwcq.cjffd.service.api.ICjffdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.cjffd.entity.Cjffd;
import com.zjhc.gzwcq.cjffd.entity.CjffdParam;
import com.zjhc.gzwcq.cjffd.entity.CjffdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/cjffdRemoteApi")
@Api(value="cjffd接口文档",tags="浮动")
public class CjffdRemoteApi implements CjffdFeignClient {
  
  	@Autowired
	private ICjffdService cjffdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Cjffd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<CjffdVo> queryByPage(@RequestBody CjffdParam cjffdParam) {
        BootstrapTableModel<CjffdVo> model = new BootstrapTableModel<CjffdVo>();
		model.setRows(cjffdService.queryCjffdByPage(cjffdParam));
		model.setTotal(cjffdService.queryTotalCjffds(cjffdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Cjffd cjffd){
    	cjffdService.insert(cjffd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	cjffdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	cjffdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Cjffd cjffd){
    	cjffdService.updateIgnoreNull(cjffd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Cjffd cjffd){
    	cjffdService.update(cjffd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Cjffd selectCjffdByPrimaryKey(@RequestBody Cjffd cjffd){
  		return cjffdService.selectCjffdByPrimaryKey(cjffd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Cjffd> selectForList(@RequestBody Cjffd cjffd){
    	return cjffdService.selectForList(cjffd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Cjffd cjffd){
    	return cjffdService.validateUniqueParam(cjffd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Cjffd cjffd){
    	cjffdService.saveOne(cjffd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Cjffd[] objs){
    	cjffdService.multipleSaveAndEdit(objs);
    };
	
}