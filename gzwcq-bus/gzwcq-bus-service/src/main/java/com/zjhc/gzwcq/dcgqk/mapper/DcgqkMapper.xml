<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.dcgqk.mapper.IDcgqkMapper">

	<resultMap type="com.zjhc.gzwcq.dcgqk.entity.Dcgqk" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="company_name" property="companyName"/>
		<result column="czr_name" property="czrName"/>
		<result column="czr_area" property="czrArea"/>
		<result column="czr_code" property="czrCode"/>
		<result column="sj_czr" property="sjCzr"/>
		<result column="reason" property="reason"/>
		<result column="dc_rate" property="dcRate"/>
		<result column="measures" property="measures"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.dcgqk.entity.DcgqkVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		company_name, 
		czr_name, 
		czr_area, 
		czr_code, 
		sj_czr, 
		reason, 
		dc_rate, 
		measures, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.company_name, 
		t.czr_name, 
		t.czr_area, 
		t.czr_code, 
		t.sj_czr, 
		t.reason, 
		t.dc_rate, 
		t.measures, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{companyName}, 
		#{czrName}, 
		#{czrArea}, 
		#{czrCode}, 
		#{sjCzr}, 
		#{reason}, 
		#{dcRate}, 
		#{measures}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="companyName != null and companyName != ''">
			and t.company_name = #{companyName}
		</if>
		<if test="czrName != null and czrName != ''">
			and t.czr_name = #{czrName}
		</if>
		<if test="czrArea != null and czrArea != ''">
			and t.czr_area = #{czrArea}
		</if>
		<if test="czrCode != null and czrCode != ''">
			and t.czr_code = #{czrCode}
		</if>
		<if test="sjCzr != null and sjCzr != ''">
			and t.sj_czr = #{sjCzr}
		</if>
		<if test="reason != null and reason != ''">
			and t.reason = #{reason}
		</if>
		<if test="dcRate != null">
			and t.dc_rate = #{dcRate}
		</if>
		<if test="measures != null and measures != ''">
			and t.measures = #{measures}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.dcgqk.entity.Dcgqk" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_dcgqk (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_dcgqk set isDeleted = 'Y' where
		id in
		<foreach collection="dcgqks" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_dcgqk set isDeleted = 'Y' where id = #{id}
	</update>
	<select id="selectAllByJbxxId" resultMap="baseResultMapExt">
		select
		<include refid="columns"/>
		from
		cq_dcgqk
		where
		 jbxx_id = #{jbxxId}
	</select>

	<delete id="deletebyJbxxbId">
		delete
			 from cq_dcgqk  where
		jbxx_id = #{id}
	</delete>
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_dcgqk  where
		id in
		<foreach collection="dcgqks" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_dcgqk  where id = #{id}
	</delete>
	
	<select id="selectDcgqkByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_dcgqk
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_dcgqk
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="companyName != null">
				company_name=#{companyName},
			</if>
			<if test="czrName != null">
				czr_name=#{czrName},
			</if>
			<if test="czrArea != null">
				czr_area=#{czrArea},
			</if>
			<if test="czrCode != null">
				czr_code=#{czrCode},
			</if>
			<if test="sjCzr != null">
				sj_czr=#{sjCzr},
			</if>
			<if test="reason != null">
				reason=#{reason},
			</if>
			<if test="dcRate != null">
				dc_rate=#{dcRate},
			</if>
			<if test="measures != null">
				measures=#{measures},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_dcgqk
		<set>
			jbxx_id=#{jbxxId},
			company_name=#{companyName},
			czr_name=#{czrName},
			czr_area=#{czrArea},
			czr_code=#{czrCode},
			sj_czr=#{sjCzr},
			reason=#{reason},
			dc_rate=#{dcRate},
			measures=#{measures},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.dcgqk.entity.Dcgqk" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_dcgqk t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalDcgqks" parameterType="com.zjhc.gzwcq.dcgqk.entity.DcgqkParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_dcgqk t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryDcgqkForList" parameterType="com.zjhc.gzwcq.dcgqk.entity.DcgqkParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_dcgqk t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.dcgqk.entity.Dcgqk" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_dcgqk t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="companyName != null and companyName != ''">
				and t.company_name = #{companyName}
			</if>
			<if test="czrName != null and czrName != ''">
				and t.czr_name = #{czrName}
			</if>
			<if test="czrArea != null and czrArea != ''">
				and t.czr_area = #{czrArea}
			</if>
			<if test="czrCode != null and czrCode != ''">
				and t.czr_code = #{czrCode}
			</if>
			<if test="sjCzr != null and sjCzr != ''">
				and t.sj_czr = #{sjCzr}
			</if>
			<if test="reason != null and reason != ''">
				and t.reason = #{reason}
			</if>
			<if test="dcRate != null and dcRate != ''">
				and t.dc_rate = #{dcRate}
			</if>
			<if test="measures != null and measures != ''">
				and t.measures = #{measures}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

</mapper>