package com.zjhc.gzwcq.sxzbpz.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.sxzbpz.client.SxzbpzFeignClient;
import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import com.zjhc.gzwcq.sxzbpz.service.api.ISxzbpzService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value="/sxzbpzRemoteApi")
@Api(value="sxzbpz接口文档",tags="筛选指标配置")
public class SxzbpzRemoteApi implements SxzbpzFeignClient {
  
  	@Autowired
	private ISxzbpzService sxzbpzService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Sxzbpz
	 * @return String
	 */
  	@Override
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<SxzbpzVo> queryByPage(@RequestBody SxzbpzParam sxzbpzParam) {
        BootstrapTableModel<SxzbpzVo> model = new BootstrapTableModel<SxzbpzVo>();
		model.setRows(sxzbpzService.querySxzbpzByPage(sxzbpzParam));
		model.setTotal(sxzbpzService.queryTotalSxzbpzs(sxzbpzParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	@Override
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Sxzbpz sxzbpz){
    	sxzbpzService.insert(sxzbpz);
    }

	/**
	 * 按对象中的主键进行删除，
	 */
	@Override
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	sxzbpzService.deleteByPrimaryKeys(map);
    }
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	@Override
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	sxzbpzService.deleteByPrimaryKey(id);
    }
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	@Override
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Sxzbpz sxzbpz){
    	sxzbpzService.updateIgnoreNull(sxzbpz);
    }
	
	/**
	* 更新
	*/
	@Override
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Sxzbpz sxzbpz){
    	sxzbpzService.update(sxzbpz);
    }
	
	/**
	 *通过ID查询数据
	 */
	@Override
  	@ApiOperation(value="根据主键查询")
	public SxzbpzVo selectSxzbpzByPrimaryKey(@RequestBody Sxzbpz sxzbpz){
  		return sxzbpzService.selectSxzbpzByPrimaryKey(sxzbpz);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@Override
	@ApiOperation(value="查询列表")
	public List<Sxzbpz> selectForList(@RequestBody Sxzbpz sxzbpz){
    	return sxzbpzService.selectForList(sxzbpz);
    }
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@Override
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Sxzbpz sxzbpz){
    	return sxzbpzService.validateUniqueParam(sxzbpz);
    }
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@Override
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Sxzbpz sxzbpz){
    	sxzbpzService.saveOne(sxzbpz);
    }
	
	/**
	* 保存多个对象
	*/
	@Override
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Sxzbpz[] objs){
    	sxzbpzService.multipleSaveAndEdit(objs);
    }

	@Override
	@ApiOperation(value="按条件获取业务相关表的所有字段,按表名分组展示")
	public List<SxzbpzVo> loadFields(@RequestBody SxzbpzParam param) {
		return sxzbpzService.loadFields(param);
	};

	@Override
	@ApiOperation(value="批量保存")
	public void saveBatch(@RequestParam("ids") String ids,@RequestBody Sxzbpz sxzbpz) {
		sxzbpzService.saveBatch(ids,sxzbpz);
	}

	@Override
	@ApiOperation(value="获取所有经济行为分析指标,按指标类型分组")
	public List<SxzbpzVo> loadByIndexType(@RequestBody SxzbpzParam param) {
		return sxzbpzService.loadByIndexType(param);
	}

	@Override
	@ApiOperation(value="按字段中文名搜索")
	public List<SxzbpzVo> loadByFieldNameCn(@RequestBody Sxzbpz sxzbpz) {
		return sxzbpzService.loadByFieldNameCn(sxzbpz);
	}
}