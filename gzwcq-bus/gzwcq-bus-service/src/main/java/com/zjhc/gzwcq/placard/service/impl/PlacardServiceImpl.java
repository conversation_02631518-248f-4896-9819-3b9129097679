package com.zjhc.gzwcq.placard.service.impl;

import java.util.*;

import com.zjhc.gzwcq.extProjectComplete.mapper.IExtProjectCompleteMapper;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import com.zjhc.gzwcq.monitorwarn.mapper.IMonitorwarnMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.placard.mapper.IPlacardMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.placard.entity.Placard;
import com.zjhc.gzwcq.placard.entity.PlacardVo;
import com.zjhc.gzwcq.placard.entity.PlacardParam;
import com.zjhc.gzwcq.placard.service.api.IPlacardService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class PlacardServiceImpl implements IPlacardService {
	
	@Autowired
	private IPlacardMapper placardMapper;
	@Autowired
	private IMonitorwarnMapper monitorwarnMapper;
  	@Autowired
	private IExtProjectCompleteMapper iExtProjectCompleteMapper;
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Placard placard){
		placard.setLookTime(0);//查阅次数默认为0
		placard.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		placard.setCreateTime(new Date());//创建时间
		placard.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		placard.setLastUpdateTime(new Date());//更新时间
		placardMapper.insert(placard);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		placardMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		placardMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Placard placard){
		placard.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		placard.setLastUpdateTime(new Date());//更新时间
		placardMapper.updateIgnoreNull(placard);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Placard placard){
		placard.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		placard.setLastUpdateTime(new Date());//更新时间
		placardMapper.update(placard);
	}
	
	public List<PlacardVo> queryPlacardByPage(PlacardParam placardParam) {
      	//分页
      	PageHelper.startPage(placardParam.getPageNumber(),placardParam.getLimit(),false);
		String user_id = ((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id();
		List<PlacardVo> placardVos = placardMapper.queryPlacardForList(placardParam);
		placardVos.stream().forEach(attr->{
			attr.setReadStatus(Constants.NO);
			if(StringUtils.isNotBlank(attr.getHaveRead())&&attr.getHaveRead().contains(user_id)){
				attr.setReadStatus(Constants.YES);
			}
		});
		return placardVos;
	}
	

	public PlacardVo selectPlacardByPrimaryKey(Placard Placard) {
		return placardMapper.selectPlacardByPrimaryKey(Placard);
	}
	
	public long queryTotalPlacards(PlacardParam placardParam) {
		return placardMapper.queryTotalPlacards(placardParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Placard> selectForList(Placard placard){
		return placardMapper.selectForList(placard);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Placard placard) {
		return placardMapper.selectForUnique(placard).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Placard placard) {
		if(StringUtils.isBlank(placard.getId())) {
			this.insert(placard);
		}else {
			this.updateIgnoreNull(placard);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Placard[] objs) {
		for(Placard placard : objs) {
			this.saveOne(placard);
		}
	}

	@Override
	public void updateLookTimeAndRead(PlacardVo placard) {
		placard.setLookTime(placard.getLookTime()+1);
		String user_id = ((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id();
		String haveRead = placard.getHaveRead();
		if (StringUtils.isBlank(haveRead)){
			placard.setHaveRead(user_id);
		} else if (!haveRead.contains(user_id)) {
			String[] split = haveRead.split(",");
			List<String> list = Arrays.asList(split);
			List<String> target = new ArrayList<String>();
			target.addAll(list);
			target.add(user_id);
			String join = String.join(",", target);
			placard.setHaveRead(join);
		}
		placardMapper.updateIgnoreNull(placard);
	}

	/**
	 * 消息数量
	 */
	@Override
	public HashMap<String, Long> messageTotal(PlacardVo placard) {
		HashMap hashMap = new HashMap<String, Long>(3);
		SysUser user = (SysUser)SpringSecurityUserTools.instance().getUser(null);
		String user_id = user.getUser_id();
		String organization_id = user.getOrganization_id();
		//公告数量
		List<PlacardVo> placardVos = placardMapper.queryPlacardForList(null);
		Long count1 = placardVos.stream().filter(attr -> StringUtils.isBlank(attr.getHaveRead())||!attr.getHaveRead().contains(user_id)).count();
		hashMap.put("placardMessageTotal",count1);
		//预警数量
		MonitorwarnParam monitorwarnParam = new MonitorwarnParam();
		monitorwarnParam.setUnitid(user.getOrganization_id());
		monitorwarnParam.setChangeCategory("2");
		List<MonitorwarnVo> monitorwarnVos = monitorwarnMapper.queryMonitorwarnForList(monitorwarnParam);
		Long count2 = monitorwarnVos.stream().filter(attr -> StringUtils.isBlank(attr.getHaveRead())||!attr.getHaveRead().contains(user_id)).count();
		hashMap.put("warnMessageTotal",count2);
		//项目成交数量
		Long number = iExtProjectCompleteMapper.unreadNumber(organization_id);
		hashMap.put("projectCompleteTotal",number);
		//总数量
		hashMap.put("allMessageTotal",count1+count2+number);
		return hashMap;
	}
}
