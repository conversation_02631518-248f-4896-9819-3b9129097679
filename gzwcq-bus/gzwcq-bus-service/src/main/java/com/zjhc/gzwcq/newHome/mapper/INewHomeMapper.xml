<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.newHome.mapper.INewHomeMapper">
    <select id="selectUserStatus" resultType="com.boot.iAdmin.access.model.user.SysUser">
        SELECT
            u.USER_ID as user_id,
            u.`NAME` as `name`,
            o.ORGANIZATION_ID as organization_id
        FROM
            sys_users u JOIN sys_organization o on o.ORGANIZATION_ID = u.ORGANIZATION_ID
                AND o.ORGANIZATION_NAME  = '浙江省人民政府国有资产监督管理委员会'
        WHERE
            u.USER_ID = #{userId}
          AND
            u.isdeleted ='N'
    </select>
    <select id="selectBusinessAssessment" resultType="com.zjhc.gzwcq.newHome.vo.DataBusinessAssessmentVO" timeout="60">
        SELECT
        ifnull(
        ifnull(SUM( info.passNum ),0) +
        ifnull(SUM( info.backspaceNum ),0)+
        ifnull(SUM(info.approvalPending),0)
        ,
        0) AS countNum,
        COUNT(DISTINCT info.ID)+  SUM(info.sums) as count,
        ifnull(SUM( info.passNum ),0) AS passNum,
        ifnull(SUM( info.backspaceNum ),0) AS backspaceNum,
        ifnull(SUM( info.noInTimeNum ),0) AS noInTimeNum,
        ifnull(SUM( info.inTimeNum ),0) AS inTimeNum ,
        ifnull(SUM(info.approvalPending),0) as approvalPending
        FROM
        (
        SELECT
        info.ID ,
        info.UNITID,
        IF(
        ((info.RG_UNITSTATE = '1' ) and (MAX(h.AF_PROCESSTYPE ) = '6' )),1,0) AS approvalPending,
        count(IF(( h.AF_PROCESSGROUP = '复审' and h.AF_PROCESSTYPE = '1'),1,null)) as passNum,
        COUNT(IF(h.AF_PROCESSTYPE = '2',1,null)) AS backspaceNum,
        IF(
        (info.RG_UNITSTATE != '2' AND COUNT(IF((h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1'), 1, NULL)) > 0)
        OR COUNT(IF((h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1'), 1, NULL)) > 1,
        IF(
        MIN(h.CREATE_TIME) > o.JB_GSDJRQ

        AND MIN(IF(hs.create_time &lt; h.CREATE_TIME, h.CREATE_TIME, NULL))  > o.JB_GSDJRQ,
        2,
        IF(
        MIN(h.CREATE_TIME) > o.JB_GSDJRQ
        OR MIN(IF(h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1', h.CREATE_TIME, NULL)) > o.JB_GSDJRQ,
        1,
        0
        )
        ),
        IF(MIN(h.CREATE_TIME) > o.JB_GSDJRQ, 1, 0)
        )
            AS noInTimeNum,

        IF(
        (info.RG_UNITSTATE != '2' AND COUNT(IF((h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1'), 1, NULL)) > 0)
        OR COUNT(IF((h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1'), 1, NULL)) > 1,
        IF(
        MIN(h.CREATE_TIME) &lt; o.JB_GSDJRQ

        AND MIN(IF(hs.create_time &lt; h.CREATE_TIME, h.CREATE_TIME, NULL))  &lt; o.JB_GSDJRQ,
        2,
        IF(
        MIN(h.CREATE_TIME) &lt; o.JB_GSDJRQ or
        MIN(IF(hs.create_time &lt; h.CREATE_TIME, h.CREATE_TIME, NULL))  &lt; o.JB_GSDJRQ,
        1,
        0
        )
        ),
        IF(MIN(h.CREATE_TIME) &lt; o.JB_GSDJRQ, 1, 0)
        )
            as inTimeNum,
        IF(
        (info.RG_UNITSTATE != '2' AND COUNT(IF((h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1'), 1, NULL)) > 0)
        OR COUNT(IF((h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1'), 1, NULL)) > 1,
            if(isnull(MIN(IF(hs.create_time &lt; h.CREATE_TIME, h.CREATE_TIME, NULL))),0,1 )
            ,0) sums


        <!--   round(  COUNT( IF
           (h.`status`= 1, 1, null))/2) AS noInTimeNum,
           floor(COUNT( IF
           ( h.`status` = 0, 1, null ))/2) AS inTimeNum
           IF(min(h.CREATE_TIME) > adddate( o.JB_GSDJRQ , INTERVAL 30 DAY ),1,0) as noInTimeNum ,

           IF(min(h.CREATE_TIME) &lt; adddate( o.JB_GSDJRQ , INTERVAL 30 DAY ),1,0) as  inTimeNum
           -->
        FROM
        (
        SELECT
        h.BUSINESS_INFO_ID,
        h.CREATE_TIME,
        h.AF_PROCESSDATE,
        h.AF_PROCESSTYPE,
       <!-- CASE

        WHEN
        IF(ISNULL(h.AF_PROCESSDATE),
        adddate( h.CREATE_TIME, INTERVAL 30 DAY ) >= NOW(),
        adddate( h.CREATE_TIME, INTERVAL 30 DAY ) >= h.AF_PROCESSDATE)
        THEN
        0
        WHEN IF(ISNULL(h.AF_PROCESSDATE),
        adddate( h.CREATE_TIME, INTERVAL 30 DAY ) &lt; NOW()
        ,adddate( h.CREATE_TIME, INTERVAL 30 DAY ) &lt; h.AF_PROCESSDATE)
        THEN
        1
        END AS `status`, -->
        h.AF_PROCESSUNITTITLE,
        h.AF_PROCESSGROUP,
        h.AF_PROCESSUNITID
        FROM
        `rg_auditflow_history` h
        JOIN  sys_organization o on h.AF_PROCESSUNITID = o.ORGANIZATION_ID
            <if test="status == 0">
                and o.ORGANIZATION_ID in( '39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F')
            </if>
        <if test="status == 1 ">
                and o.BUSINESSTYPE = '1'
        </if>



        WHERE
        DATE_FORMAT( h.CREATE_TIME, '%Y' ) = #{dateTime}


        ) h
        JOIN rg_business_info info ON info.ID = h.BUSINESS_INFO_ID
        JOIN (
                SELECT
                jb.id, if( isnull(jb.JB_GSDJRQ),now(),jb.JB_GSDJRQ) as JB_GSDJRQ
                FROM
                <if test="status == 1 and status != '' and status != null">
                    sys_organization o
                    JOIN ( SELECT jb.ID, jb.JB_CZRZZJGID , jb.JB_GSDJRQ FROM cq_jbxxb jb  ) jb ON jb.JB_CZRZZJGID = o.ORGANIZATION_ID
                    WHERE
                    <if test="dataStatus == 1 and dataStatus != '' and dataStatus != null and status == 1">
                        o.ORGANIZATION_ID = #{orId}
                    </if>
                    <if test="dataStatus == 2 and dataStatus != '' and dataStatus != null and status == 1">
                        FIND_IN_SET(#{orId}, o.PARENTS)
                    </if>
                </if>

            <if test="status == 0">
                (
                <if test="dataStatus == 2 and dataStatus != '' and dataStatus != null and status == 0">
                SELECT o.ORGANIZATION_ID, o.ORGANIZATION_NAME FROM sys_organization o WHERE FIND_IN_SET(#{orId},o.PARENTS)

                UNION
                SELECT o.ORGANIZATION_ID, o.ORGANIZATION_NAME FROM sys_organization o WHERE
                FIND_IN_SET('53129A8420000021177AD54D2465FF2F', o.PARENTS)
                </if>
        <if test="dataStatus == 1 and dataStatus != '' and dataStatus != null and status == 0">
            SELECT o.ORGANIZATION_ID, o.ORGANIZATION_NAME FROM sys_organization o WHERE  o.ORGANIZATION_ID = #{orId}
        </if>
                )  o
                JOIN ( SELECT jb.ID, jb.JB_CZRZZJGID , jb.JB_GSDJRQ FROM cq_jbxxb jb  ) jb ON jb.JB_CZRZZJGID = o.ORGANIZATION_ID
            </if>

        ) o ON o.id = info.JBXX_ID
        left JOIN
        (
        SELECT
        h.BUSINESS_INFO_ID,
        h.AF_PROCESSUNITID,
        MIN(IF(h.AF_PROCESSGROUP = '复审' AND h.AF_PROCESSTYPE = '1', h.CREATE_TIME, NULL)) AS create_time
        FROM
        rg_auditflow_history h
        where
        DATE_FORMAT( h.CREATE_TIME, '%Y' ) = #{dateTime}
        GROUP BY
        h.BUSINESS_INFO_ID ,
        h.AF_PROCESSUNITID
        )hs on h.BUSINESS_INFO_ID = hs.BUSINESS_INFO_ID and h.AF_PROCESSUNITID = hs.AF_PROCESSUNITID
        GROUP BY
        info.ID
        ) info
    </select>
    <select id="selectBusinessAssessmentInfo" resultType="com.zjhc.gzwcq.newHome.vo.BusinessAssessmentInfoTotalDto">
        select sum(count) count,sum(rollbackNum) rejectionNum,sum(passNum) passNum,sum(rollbackNum+passNum) auditNum,(select count(DISTINCT UNITID) from rg_business_info where id in (select DISTINCT BUSINESS_INFO_ID from rg_auditflow_history where AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A'
        <choose>
            <when test="yearMonth != null">
                and DATE_FORMAT(CREATE_TIME,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
            </when>
            <otherwise>
                and DATE_FORMAT(CREATE_TIME,'%Y')=#{dateTime}
            </otherwise>
        </choose>
        )) orgNum from(
            select * from(
            select
            ttt.orgid ordId,
            ttt.orgName name,
            <choose>
                <when test="yearMonth != null">
                    sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and AF_PROCESSGROUP = '初审' and DATE_FORMAT(ttt.CREATE_TIME,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m'), 1, 0)) count,
                    sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') and AF_PROCESSTYPE = '2', 1, 0)) rollbackNum,
                    sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and DATE_FORMAT(ttt.CREATE_TIME,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') and (AF_PROCESSTYPE = '6' or DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') > DATE_FORMAT(#{yearMonth},'%Y-%m')), 1, 0)) pendingNum,
                    sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and AF_PROCESSGROUP = '复审' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') and AF_PROCESSTYPE = '1', 1, 0)) passNum
                </when>
                <otherwise>
                    sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and AF_PROCESSGROUP = '初审' and DATE_FORMAT(ttt.CREATE_TIME,'%Y')= #{dateTime}, 1, 0)) count,
                    sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y')= #{dateTime} and AF_PROCESSTYPE = '2', 1, 0)) rollbackNum,
                    sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and DATE_FORMAT(ttt.CREATE_TIME,'%Y')= #{dateTime} and (AF_PROCESSTYPE = '6' or DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y') > #{dateTime}), 1, 0)) pendingNum,
                    sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and AF_PROCESSGROUP = '复审' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y')= #{dateTime} and AF_PROCESSTYPE = '1', 1, 0)) passNum
                </otherwise>
            </choose>
            from (
            select DISTINCT
            tt.orgid,tt.orgName,t.*
            FROM
            rg_auditflow_history t join (
            select tt.BUSINESS_INFO_ID,ttt.AF_PROCESSUNITID orgid,ttt.AF_PROCESSUNITTITLE orgName from (select BUSINESS_INFO_ID,GROUP_CONCAT(AF_PROCESSUNITID) locus from rg_auditflow_history where BUSINESS_INFO_ID in (SELECT DISTINCT BUSINESS_INFO_ID  FROM rg_auditflow_history WHERE AF_PROCESSUNITID in (
            SELECT
            o.ORGANIZATION_ID
            FROM
            sys_organization o
            WHERE
            FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',o.PARENTS)  and o.isdeleted = 'N'

            AND o.BUSINESS_LEVEL = '01' UNION
            SELECT
            o.ORGANIZATION_ID
            FROM
            sys_organization o
            WHERE
            FIND_IN_SET( '53129A8420000021177AD54D2465FF2F', o.PARENTS )
            AND o.isdeleted = 'N'
            AND o.BUSINESS_LEVEL = '01'
            ) AND
            <choose>
                <when test="yearMonth != null">
                    DATE_FORMAT(AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
                </when>
                <otherwise>
                    DATE_FORMAT(AF_PROCESSDATE,'%Y')= #{dateTime}
                </otherwise>
            </choose>
            )
            group by BUSINESS_INFO_ID
            ) tt join (SELECT DISTINCT BUSINESS_INFO_ID,AF_PROCESSUNITID,AF_PROCESSUNITTITLE FROM rg_auditflow_history WHERE AF_PROCESSUNITID in (SELECT
            o.ORGANIZATION_ID
            FROM
            sys_organization o
            WHERE
            FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',o.PARENTS)  and o.isdeleted = 'N'
            AND o.BUSINESS_LEVEL = '01' UNION
            SELECT
            o.ORGANIZATION_ID
            FROM
            sys_organization o
            WHERE
            FIND_IN_SET( '53129A8420000021177AD54D2465FF2F', o.PARENTS )
            AND o.isdeleted = 'N'
            AND o.BUSINESS_LEVEL = '01' ) AND
            <choose>
                <when test="yearMonth != null">
                    DATE_FORMAT(AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
                </when>
                <otherwise>
                    DATE_FORMAT(AF_PROCESSDATE,'%Y')= #{dateTime}
                </otherwise>
            </choose>
            ) ttt on tt.BUSINESS_INFO_ID = ttt.BUSINESS_INFO_ID
            where FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',locus) or FIND_IN_SET('53129A8420000021177AD54D2465FF2F',locus)
            ) tt on t.BUSINESS_INFO_ID = tt.BUSINESS_INFO_ID) ttt where ttt.BUSINESS_INFO_ID in (select id from rg_business_info where jbxx_id in (select id from cq_jbxxb))
            group by ttt.orgid
            ) tttt
            ORDER BY tttt.count desc
            ) t4
    </select>
    <select id="selectBusinessAssessmentInfoNew" resultType="com.zjhc.gzwcq.newHome.vo.DataBusinessAssessmentInfoVO">
        SELECT
            info.ID as id,
            IF(COUNT(IF(ss.AF_PROCESSTYPE = '2',1,null)) > 0  or IF(info.RG_UNITSTATE = '2',1,0) > 0 ,info.UNITID,null)  as ordId,
            IF(
                    ((info.RG_UNITSTATE = '1' ) and (MAX(ss.AF_PROCESSTYPE ) = '6' )), 1, 0 )  as approvalPending,

            count(IF(( ss.AF_PROCESSGROUP = '复审' and ss.AF_PROCESSTYPE = '1'),1,null)) as passNum,
            COUNT(IF(ss.AF_PROCESSTYPE = '2',1,null)) AS backspaceNum
        FROM
            (
                SELECT * FROM rg_business_info info WHERE DATE_FORMAT( info.CREATE_TIME, '%Y' ) = #{dateTime}
            ) info
                JOIN rg_auditflow_history h  on h.BUSINESS_INFO_ID = info.id
                join
            (
                SELECT
                    o.ORGANIZATION_ID,
                    o.ORGANIZATION_NAME
                FROM
                    sys_organization o
                WHERE
                    FIND_IN_SET( #{orId}, o.PARENTS )
                  AND o.isdeleted = 'N'
                  AND o.BUSINESS_LEVEL = '01' UNION
                SELECT
                    o.ORGANIZATION_ID,
                    o.ORGANIZATION_NAME
                FROM
                    sys_organization o
                WHERE
                    FIND_IN_SET( '53129A8420000021177AD54D2465FF2F', o.PARENTS )
                  AND o.isdeleted = 'N'
                  AND o.BUSINESS_LEVEL = '01'
            ) o
            ON h.AF_UNITID = o.ORGANIZATION_ID
                or ( h.AF_PROCESSUNITID = o.ORGANIZATION_ID )
                JOIN
            rg_auditflow_history ss ON ss.BUSINESS_INFO_ID = info.id and ss.AF_PROCESSUNITID IN ( '39DC82B5C0000021A568D4D612672F5A', '53129A8420000021177AD54D2465FF2F' )
        GROUP BY
            o.ORGANIZATION_ID
    </select>
    <select id="selectHandleMattersInfo" resultType="com.zjhc.gzwcq.newHome.vo.DataBusinessTransactionVO">
        select * from(
             select
                 ttt.orgid ordId,
                 ttt.orgName name,
                 <choose>
                     <when test="yearMonth != null">
                         sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and AF_PROCESSGROUP = '初审' and DATE_FORMAT(ttt.CREATE_TIME,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m'), 1, 0)) count,
                         sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') and AF_PROCESSTYPE = '2', 1, 0)) rollbackNum,
                         sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and DATE_FORMAT(ttt.CREATE_TIME,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') and (AF_PROCESSTYPE = '6' or DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') > DATE_FORMAT(#{yearMonth},'%Y-%m')), 1, 0)) pendingNum,
                         sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and AF_PROCESSGROUP = '复审' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') and AF_PROCESSTYPE = '1', 1, 0)) passNum
                     </when>
                     <otherwise>
                         sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and AF_PROCESSGROUP = '初审' and DATE_FORMAT(ttt.CREATE_TIME,'%Y')= #{dateTime}, 1, 0)) count,
                         sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y')= #{dateTime} and AF_PROCESSTYPE = '2', 1, 0)) rollbackNum,
                         sum(if(AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') and DATE_FORMAT(ttt.CREATE_TIME,'%Y')= #{dateTime} and (AF_PROCESSTYPE = '6' or DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y') > #{dateTime}), 1, 0)) pendingNum,
                         sum(if(AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' and AF_PROCESSGROUP = '复审' and DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y')= #{dateTime} and AF_PROCESSTYPE = '1', 1, 0)) passNum
                     </otherwise>
                 </choose>
             from (
                 select DISTINCT
                 tt.orgid,tt.orgName,t.*
                 FROM
                 rg_auditflow_history t join (
                 select tt.BUSINESS_INFO_ID,ttt.AF_PROCESSUNITID orgid,ttt.AF_PROCESSUNITTITLE orgName from (select BUSINESS_INFO_ID,GROUP_CONCAT(AF_PROCESSUNITID) locus from rg_auditflow_history where BUSINESS_INFO_ID in (SELECT DISTINCT BUSINESS_INFO_ID  FROM rg_auditflow_history WHERE AF_PROCESSUNITID in (
                 SELECT
                 o.ORGANIZATION_ID
                 FROM
                 sys_organization o
                 WHERE
                 FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',o.PARENTS)  and o.isdeleted = 'N'

                 AND o.BUSINESS_LEVEL = '01' UNION
                 SELECT
                 o.ORGANIZATION_ID
                 FROM
                 sys_organization o
                 WHERE
                 FIND_IN_SET( '53129A8420000021177AD54D2465FF2F', o.PARENTS )
                 AND o.isdeleted = 'N'
                 AND o.BUSINESS_LEVEL = '01'
                 ) AND
                 <choose>
                     <when test="yearMonth != null">
                         DATE_FORMAT(AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
                     </when>
                     <otherwise>
                         DATE_FORMAT(AF_PROCESSDATE,'%Y')= #{dateTime}
                     </otherwise>
                 </choose>
                 )
                 group by BUSINESS_INFO_ID
                 ) tt join (SELECT DISTINCT BUSINESS_INFO_ID,AF_PROCESSUNITID,AF_PROCESSUNITTITLE FROM rg_auditflow_history WHERE AF_PROCESSUNITID in (SELECT
                 o.ORGANIZATION_ID
                 FROM
                 sys_organization o
                 WHERE
                 FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',o.PARENTS)  and o.isdeleted = 'N'
                 AND o.BUSINESS_LEVEL = '01' UNION
                 SELECT
                 o.ORGANIZATION_ID
                 FROM
                 sys_organization o
                 WHERE
                 FIND_IN_SET( '53129A8420000021177AD54D2465FF2F', o.PARENTS )
                 AND o.isdeleted = 'N'
                 AND o.BUSINESS_LEVEL = '01' ) AND
                 <choose>
                     <when test="yearMonth != null">
                         DATE_FORMAT(AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
                     </when>
                     <otherwise>
                         DATE_FORMAT(AF_PROCESSDATE,'%Y')= #{dateTime}
                     </otherwise>
                 </choose>
                 ) ttt on tt.BUSINESS_INFO_ID = ttt.BUSINESS_INFO_ID
                 where FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',locus) or FIND_IN_SET('53129A8420000021177AD54D2465FF2F',locus)
                 ) tt on t.BUSINESS_INFO_ID = tt.BUSINESS_INFO_ID) ttt where ttt.BUSINESS_INFO_ID in (select id from rg_business_info where jbxx_id in (select id from cq_jbxxb))
             group by ttt.orgid
         ) tttt
        ORDER BY tttt.count desc
    </select>

    <!-- 查询企业户数 参考 companyNumBySeason-->
    <!-- <AUTHOR> -->
    <select id="selectCompanyCount" resultType="java.lang.Integer">
        select count(distinct cj.UNITID)
        from view_cq_jbxxb_noDelete cj
        join (
            -- 找出时间段内注册的审核完成的企业
            SELECT
                tt.UNITID,
                max(t.RG_TIMEMARK) maxdate
            FROM
                rg_business_info t
                join view_cq_jbxxb_noDelete tt on t.JBXX_ID = tt.id
            WHERE
                date_format(t.RG_TIMEMARK, '%Y%m') &lt;= date_format(#{yearMonth}, '%Y%m')
                and tt.JB_SHZT = '4'
                and t.RG_UNITSTATE = '2'
        
                -- 排除注销的企业
                AND t.UNITID NOT IN (
                    SELECT
                        t1.UNITID
                    FROM
                        rg_business_info t
                        JOIN view_cq_jbxxb_noDelete t1 ON t.jbxx_id = t1.id
                    WHERE
                        date_format(t.RG_TIMEMARK, '%Y%m') &lt;= date_format(#{yearMonth}, '%Y%m')
                        AND t.rg_type = '3'
                        AND t1.JB_SHZT = '4'
                )
            GROUP BY
                tt.UNITID
        ) tt on cj.UNITID = tt.UNITID
        join rg_business_info ttt on cj.id = ttt.JBXX_ID and ttt.RG_TIMEMARK = tt.maxdate
        where
            cj.JB_SHZT = '4'
            and cj.unitid IN (
                select ORGANIZATION_ID
                from sys_organization
                where FIND_IN_SET(#{orgId}, PARENTS)
                <foreach collection="orgIdList" item="oid">
                    or find_in_set(#{oid}, PARENTS)
                </foreach>
            )
    </select>
    
    <select id="selectHandleMattersInfoV2" resultType="com.zjhc.gzwcq.newHome.vo.DataBusinessTransactionVO">
        SELECT 
            ttt.orgid AS ordId,
            ttt.orgName AS name,
            
            <choose>
                <when test="yearMonth != null">
                    SUM(IF(ttt.AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') AND ttt.AF_PROCESSGROUP = '初审' AND DATE_FORMAT(ttt.CREATE_TIME,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m'), 1, 0)) AS count,
                    SUM(IF(ttt.AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' AND DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') AND ttt.AF_PROCESSTYPE = '2', 1, 0)) AS rollbackNum,
                    SUM(IF(ttt.AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') AND DATE_FORMAT(ttt.CREATE_TIME,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') AND (ttt.AF_PROCESSTYPE = '6' OR DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') > DATE_FORMAT(#{yearMonth},'%Y-%m')), 1, 0)) AS pendingNum,
                    SUM(IF(ttt.AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' AND ttt.AF_PROCESSGROUP = '复审' AND DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m') AND ttt.AF_PROCESSTYPE = '1', 1, 0)) AS passNum
                </when>
                <otherwise>
                    SUM(IF(ttt.AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') AND ttt.AF_PROCESSGROUP = '初审' AND DATE_FORMAT(ttt.CREATE_TIME,'%Y') = #{dateTime}, 1, 0)) AS count,
                    SUM(IF(ttt.AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' AND DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y') = #{dateTime} AND ttt.AF_PROCESSTYPE = '2', 1, 0)) AS rollbackNum,
                    SUM(IF(ttt.AF_PROCESSUNITID IN ('39DC82B5C0000021A568D4D612672F5A','53129A8420000021177AD54D2465FF2F') AND DATE_FORMAT(ttt.CREATE_TIME,'%Y') = #{dateTime} AND (ttt.AF_PROCESSTYPE = '6' OR DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y') > #{dateTime}), 1, 0)) AS pendingNum,
                    SUM(IF(ttt.AF_PROCESSUNITID = '39DC82B5C0000021A568D4D612672F5A' AND ttt.AF_PROCESSGROUP = '复审' AND DATE_FORMAT(ttt.AF_PROCESSDATE,'%Y') = #{dateTime} AND ttt.AF_PROCESSTYPE = '1', 1, 0)) AS passNum
                </otherwise>
            </choose>

        FROM (
            SELECT 
                DISTINCT tt.BUSINESS_INFO_ID,
                t.AF_PROCESSUNITID,
                t.AF_PROCESSGROUP,
                t.AF_PROCESSDATE,
                t.AF_PROCESSTYPE,
                t.CREATE_TIME,
                ttt.AF_PROCESSUNITID AS orgid,
                ttt.AF_PROCESSUNITTITLE AS orgName
            FROM rg_auditflow_history t
        
            -- 用于过滤，过滤出同时被浙江省国资委和其一级机构审核过的业务
            JOIN (
                SELECT 
                    t1.BUSINESS_INFO_ID,
                    t2.AF_PROCESSUNITID,
                    t2.AF_PROCESSUNITTITLE
                FROM 
        
                -- 查询被一级机构审核过的业务,以及业务对应的所有审核机构
                (
                    SELECT 
                        BUSINESS_INFO_ID,
                        GROUP_CONCAT(AF_PROCESSUNITID) AS locus
                    FROM rg_auditflow_history
                    WHERE BUSINESS_INFO_ID IN (
                        SELECT DISTINCT BUSINESS_INFO_ID
                        FROM rg_auditflow_history
                        WHERE AF_PROCESSUNITID IN (
                            SELECT ORGANIZATION_ID
                            FROM sys_organization
                            WHERE isdeleted = 'N'
                              AND BUSINESS_LEVEL = '01'
                              AND (
                                  FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A', PARENTS)
                                  OR FIND_IN_SET('53129A8420000021177AD54D2465FF2F', PARENTS)
                              )
                        )
                        AND <choose>
                            <when test="yearMonth != null">
                                DATE_FORMAT(AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
                            </when>
                            <otherwise>
                                DATE_FORMAT(AF_PROCESSDATE,'%Y') = #{dateTime}
                            </otherwise>
                        </choose>
                    )
                    GROUP BY BUSINESS_INFO_ID
                ) t1
        
                -- 查询出具体业务归属哪个一级机构
                JOIN (
                    SELECT DISTINCT BUSINESS_INFO_ID, AF_PROCESSUNITID, AF_PROCESSUNITTITLE
                    FROM rg_auditflow_history
                    WHERE AF_PROCESSUNITID IN (
                        SELECT ORGANIZATION_ID
                        FROM sys_organization
                        WHERE isdeleted = 'N'
                          AND BUSINESS_LEVEL = '01'
                          AND (
                              FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A', PARENTS)
                              OR FIND_IN_SET('53129A8420000021177AD54D2465FF2F', PARENTS)
                          )
                    )
                    AND <choose>
                        <when test="yearMonth != null">
                            DATE_FORMAT(AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
                        </when>
                        <otherwise>
                            DATE_FORMAT(AF_PROCESSDATE,'%Y') = #{dateTime}
                        </otherwise>
                    </choose>
                ) t2 ON t1.BUSINESS_INFO_ID = t2.BUSINESS_INFO_ID
                
                -- 业务又必须被浙江省国资委或浙江省国资委代管审核过
                WHERE FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A', t1.locus) > 0
                   OR FIND_IN_SET('53129A8420000021177AD54D2465FF2F', t1.locus) > 0
            ) tt ON t.BUSINESS_INFO_ID = tt.BUSINESS_INFO_ID
        
            -- 找到业务对应的一级审批机构信息
            JOIN (
                SELECT DISTINCT BUSINESS_INFO_ID, AF_PROCESSUNITID, AF_PROCESSUNITTITLE
                FROM rg_auditflow_history
                WHERE AF_PROCESSUNITID IN (
                    SELECT ORGANIZATION_ID
                    FROM sys_organization
                    WHERE isdeleted = 'N'
                      AND BUSINESS_LEVEL = '01'
                      AND (
                          FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A', PARENTS)
                          OR FIND_IN_SET('53129A8420000021177AD54D2465FF2F', PARENTS)
                      )
                )
                AND <choose>
                    <when test="yearMonth != null">
                        DATE_FORMAT(AF_PROCESSDATE,'%Y-%m') &lt;= DATE_FORMAT(#{yearMonth},'%Y-%m')
                    </when>
                    <otherwise>
                        DATE_FORMAT(AF_PROCESSDATE,'%Y') = #{dateTime}
                    </otherwise>
                </choose>
            ) ttt ON tt.BUSINESS_INFO_ID = ttt.BUSINESS_INFO_ID
            WHERE t.BUSINESS_INFO_ID IN (
                SELECT id
                FROM rg_business_info
                WHERE jbxx_id IN (SELECT id FROM cq_jbxxb)
            )
        ) ttt
        GROUP BY ttt.orgid
        ORDER BY count DESC
    </select>
</mapper>
