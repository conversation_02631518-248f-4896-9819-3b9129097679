<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.ywzbb2.mapper.IYwzbb2Mapper">

	<resultMap type="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="zl_gdqkdjb_ly" property="zlGdqkdjbLy"/>
		<result column="zl_qt" property="zlQt"/>
		<result column="zl_gszxzm_gsbmmc" property="zlGszxzmGsbmmc"/>
		<result column="xx_ywfhbcz" property="xxYwfhbcz"/>
		<result column="xx_ywfhbzfsgjk" property="xxYwfhbzfsgjk"/>
		<result column="xx_bdqypgjzcz" property="xxBdqypgjzcz"/>
		<result column="xx_bz" property="xxBz"/>
		<result column="xx_bdqysjjzcz" property="xxBdqysjjzcz"/>
		<result column="xx_zhyqymc" property="xxZhyqymc"/>
		<result column="xx_zhyssgzjgjg" property="xxZhyssgzjgjg"/>
		<result column="xx_zhyssgjczqy" property="xxZhyssgjczqy"/>
		<result column="xx_zhyyyzhdzcpgz" property="xxZhyyyzhdzcpgz"/>
		<result column="xx_zhybz" property="xxZhybz"/>
		<result column="xx_zheqymc" property="xxZheqymc"/>
		<result column="xx_zhessgzjgjg" property="xxZhessgzjgjg"/>
		<result column="xx_zhessgjczqy" property="xxZhessgjczqy"/>
		<result column="xx_zheyyzhdzcpgz" property="xxZheyyzhdzcpgz"/>
		<result column="xx_zhebz" property="xxZhebz"/>
		<result column="xx_azfyze" property="xxAzfyze"/>
		<result column="xx_gyqy" property="xxGyqy"/>
		<result column="xx_bxbfmc" property="xxBxbfmc"/>
		<result column="xx_bxbfpgjzcz" property="xxBxbfpgjzcz"/>
		<result column="xx_xbfpgjzcz" property="xxXbfpgjzcz"/>
		<result column="xx_bflqypgjzcz" property="xxBflqypgjzcz"/>
		<result column="xx_cxqypgjzcz" property="xxCxqypgjzcz"/>
		<result column="xx_cxqyzgbz" property="xxCxqyzgbz"/>
		<result column="xx_yytzdgqpgz" property="xxYytzdgqpgz"/>
		<result column="xx_yytzdgqzj" property="xxYytzdgqzj"/>
		<result column="xx_syjzcczsr" property="xxSyjzcczsr"/>
		<result column="xx_cjfssgzjgjg" property="xxCjfssgzjgjg"/>
		<result column="xx_bdqymc" property="xxBdqymc"/>
		<result column="xx_bdqyssgzjgjg" property="xxBdqyssgzjgjg"/>
		<result column="xx_qsfpbc" property="xxQsfpbc"/>
		<result column="xx_qsfpsqsk" property="xxQsfpsqsk"/>
		<result column="xx_qsccsfzyqczw" property="xxQsccsfzyqczw"/>
		<result column="xx_pcfpbc" property="xxPcfpbc"/>
		<result column="xx_pcfpsqsk" property="xxPcfpsqsk"/>
		<result column="xx_ywfhbjz" property="xxYwfhbjz"/>
		<result column="fd1_czzjhj" property="fd1Czzjhj"/>
		<result column="fd1_pgzhj" property="fd1Pgzhj"/>
		<result column="fd1_zfzjhj" property="fd1Zfzjhj"/>
		<result column="fd1_jzzjhj" property="fd1Jzzjhj"/>
		<result column="fd2_sgjghj" property="fd2Sgjghj"/>
		<result column="fd2_sjjzczhj" property="fd2Sjjzczhj"/>
		<result column="fd2_pgjzczhj" property="fd2Pgjzczhj"/>
		<result column="fd4_zgslhj" property="fd4Zgslhj"/>
		<result column="fd5_sfzjhj" property="fd5Sfzjhj"/>
		<result column="fd6_syccfpjghj" property="fd6Syccfpjghj"/>
		<result column="fd7_hzgqblhj" property="fd7Hzgqblhj"/>
		<result column="fd7_hzjzczhj" property="fd7Hzjzczhj"/>
		<result column="fd8_srgqsjjzczhj" property="fd8Srgqsjjzczhj"/>
		<result column="fd8_srgqpgjzczhj" property="fd8Srgqpgjzczhj"/>
		<result column="fd8_cjjhj" property="fd8Cjjhj"/>
		<result column="fd8_zrgqblhj" property="fd8Zrgqblhj"/>
		<result column="fd8_zrgqsjjzczhj" property="fd8Zrgqsjjzczhj"/>
		<result column="fd8_zrgqpgjzczhj" property="fd8Zrgqpgjzczhj"/>
		<result column="fd8_srgqblhj" property="fd8Srgqblhj"/>
		<result column="fd2_sggqblhj" property="fd2Sggqblhj"/>
		<result column="xx_fxgs" property="xxFxgs"/>
		<result column="xx_gkfxgs" property="xxGkfxgs"/>
		<result column="xx_azryzs" property="xxAzryzs"/>
		<result column="xx_bxbfjzczhxbfgqbl" property="xxBxbfjzczhxbfgqbl"/>
		<result column="xx_yytzgqzhbdqygqbl" property="xxYytzgqzhbdqygqbl"/>
		<result column="zl_fj" property="zlFj"/>
		<result column="xx_bdqypgjzcz_bzz" property="xxBdqypgjzczBzz"/>
		<result column="xx_bdqypgjzcz_bjz" property="xxBdqypgjzczBjz"/>
		<result column="xx_bdqypgjzcz_bgz" property="xxBdqypgjzczBgz"/>
		<result column="xx_bdqysjjzcz_bgz" property="xxBdqysjjzczBgz"/>
		<result column="xx_zgbz_xzgb" property="xxZgbzXzgb"/>
		<result column="xx_zgbz_js" property="xxZgbzJs"/>
		<result column="xx_zgbz_tzxz" property="xxZgbzTzxz"/>
		<result column="xx_zgbz_xshb" property="xxZgbzXshb"/>
		<result column="xx_zgbz_gqcz" property="xxZgbzGqcz"/>
		<result column="zl_yxhztzs_yw" property="zlYxhztzsYw"/>
		<result column="zl_yxhztzs_ly" property="zlYxhztzsLy"/>
		<result column="zl_yxhztzs_hzdw" property="zlYxhztzsHzdw"/>
		<result column="jc_30sspj" property="jc30sspj"/>
		<result column="jc_mgjzcz" property="jcMgjzcz"/>
		<result column="jc_jcgs" property="jcJcgs"/>
		<result column="jc_jcjj" property="jcJcjj"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="zl_qsbg" property="zlQsbg"/>
		<result column="zl_flxys" property="zlFlxys"/>
		<result column="zl_gszxzm" property="zlGszxzm"/>
		<result column="zl_zgdbdhjy" property="zlZgdbdhjy"/>
		<result column="zl_gqszfawj" property="zlGqszfawj"/>
		<result column="zl_zxgg" property="zlZxgg"/>
		<result column="zl_hbxys" property="zlHbxys"/>
		<result column="zl_jzrsjbg" property="zlJzrsjbg"/>
		<result column="zl_zhxy" property="zlZhxy"/>
		<result column="zl_fhbpgba" property="zlFhbpgba"/>
		<result column="zl_zjyqsjbg" property="zlZjyqsjbg"/>
		<result column="zl_pgba" property="zlPgba"/>
		<result column="zl_bdpgba" property="zlBdpgba"/>
		<result column="zl_wchzxy" property="zlWchzxy"/>
		<result column="zl_jzgg" property="zlJzgg"/>
		<result column="zl_yyzz" property="zlYyzz"/>
		<result column="zl_sjbg" property="zlSjbg"/>
		<result column="zl_yzbg" property="zlYzbg"/>
		<result column="zl_qyzc" property="zlQyzc"/>
		<result column="zl_gdqkdjb" property="zlGdqkdjb"/>
		<result column="zl_zhypgbab" property="zlZhypgbab"/>
		<result column="zl_syzcczxy" property="zlSyzcczxy"/>
		<result column="zl_tjpgbab" property="zlTjpgbab"/>
		<result column="zl_gqzr" property="zlGqzr"/>
		<result column="zl_zhepgbab" property="zlZhepgbab"/>
		<result column="zl_xbpgbab" property="zlXbpgbab"/>
		<result column="zl_tzxy" property="zlTzxy"/>
		<result column="zl_bxbpgbab" property="zlBxbpgbab"/>
		<result column="zl_gytdba" property="zlGytdba"/>
		<result column="zl_jcjg" property="zlJcjg"/>
		<result column="zl_jcwj" property="zlJcwj"/>
		<result column="zl_pcgg" property="zlPcgg"/>
		<result column="zl_yxhztzs" property="zlYxhztzs"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo" extends="baseResultMap">
		<result column="xxZhyssgzjgjgStr" property="xxZhyssgzjgjgStr"/>
		<result column="xxZhyssgjczqyStr" property="xxZhyssgjczqyStr"/>
		<result column="xxZhessgzjgjgStr" property="xxZhessgzjgjgStr"/>
		<result column="xxZhessgjczqyStr" property="xxZhessgjczqyStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		zl_gdqkdjb_ly, 
		zl_qt, 
		zl_gszxzm_gsbmmc, 
		xx_ywfhbcz, 
		xx_ywfhbzfsgjk, 
		xx_bdqypgjzcz, 
		xx_bz, 
		xx_bdqysjjzcz, 
		xx_zhyqymc, 
		xx_zhyssgzjgjg, 
		xx_zhyssgjczqy, 
		xx_zhyyyzhdzcpgz, 
		xx_zhybz, 
		xx_zheqymc, 
		xx_zhessgzjgjg, 
		xx_zhessgjczqy, 
		xx_zheyyzhdzcpgz, 
		xx_zhebz, 
		xx_azfyze, 
		xx_gyqy, 
		xx_bxbfmc, 
		xx_bxbfpgjzcz, 
		xx_xbfpgjzcz, 
		xx_bflqypgjzcz, 
		xx_cxqypgjzcz, 
		xx_cxqyzgbz, 
		xx_yytzdgqpgz, 
		xx_yytzdgqzj, 
		xx_syjzcczsr, 
		xx_cjfssgzjgjg, 
		xx_bdqymc, 
		xx_bdqyssgzjgjg, 
		xx_qsfpbc, 
		xx_qsfpsqsk, 
		xx_qsccsfzyqczw, 
		xx_pcfpbc, 
		xx_pcfpsqsk, 
		xx_ywfhbjz, 
		fd1_czzjhj, 
		fd1_pgzhj, 
		fd1_zfzjhj, 
		fd1_jzzjhj, 
		fd2_sgjghj, 
		fd2_sjjzczhj, 
		fd2_pgjzczhj, 
		fd4_zgslhj, 
		fd5_sfzjhj, 
		fd6_syccfpjghj, 
		fd7_hzgqblhj, 
		fd7_hzjzczhj, 
		fd8_srgqsjjzczhj, 
		fd8_srgqpgjzczhj, 
		fd8_cjjhj, 
		fd8_zrgqblhj, 
		fd8_zrgqsjjzczhj, 
		fd8_zrgqpgjzczhj, 
		fd8_srgqblhj, 
		fd2_sggqblhj, 
		xx_fxgs, 
		xx_gkfxgs, 
		xx_azryzs, 
		xx_bxbfjzczhxbfgqbl, 
		xx_yytzgqzhbdqygqbl, 
		zl_fj, 
		xx_bdqypgjzcz_bzz, 
		xx_bdqypgjzcz_bjz, 
		xx_bdqypgjzcz_bgz, 
		xx_bdqysjjzcz_bgz, 
		xx_zgbz_xzgb, 
		xx_zgbz_js, 
		xx_zgbz_tzxz, 
		xx_zgbz_xshb, 
		xx_zgbz_gqcz, 
		zl_yxhztzs_yw, 
		zl_yxhztzs_ly, 
		zl_yxhztzs_hzdw, 
		jc_30sspj, 
		jc_mgjzcz, 
		jc_jcgs, 
		jc_jcjj, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time, 
		zl_qsbg, 
		zl_flxys, 
		zl_gszxzm, 
		zl_zgdbdhjy, 
		zl_gqszfawj, 
		zl_zxgg, 
		zl_hbxys, 
		zl_jzrsjbg, 
		zl_zhxy, 
		zl_fhbpgba, 
		zl_zjyqsjbg, 
		zl_pgba, 
		zl_bdpgba, 
		zl_wchzxy, 
		zl_jzgg, 
		zl_yyzz, 
		zl_sjbg, 
		zl_yzbg, 
		zl_qyzc, 
		zl_gdqkdjb, 
		zl_zhypgbab, 
		zl_syzcczxy, 
		zl_tjpgbab, 
		zl_gqzr, 
		zl_zhepgbab, 
		zl_xbpgbab, 
		zl_tzxy, 
		zl_bxbpgbab, 
		zl_gytdba, 
		zl_jcjg, 
		zl_jcwj, 
		zl_pcgg,
		zl_yxhztzs
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.zl_gdqkdjb_ly, 
		t.zl_qt, 
		t.zl_gszxzm_gsbmmc, 
		t.xx_ywfhbcz, 
		t.xx_ywfhbzfsgjk, 
		t.xx_bdqypgjzcz, 
		t.xx_bz, 
		t.xx_bdqysjjzcz, 
		t.xx_zhyqymc, 
		t.xx_zhyssgzjgjg, 
		t.xx_zhyssgjczqy, 
		t.xx_zhyyyzhdzcpgz, 
		t.xx_zhybz, 
		t.xx_zheqymc, 
		t.xx_zhessgzjgjg, 
		t.xx_zhessgjczqy, 
		t.xx_zheyyzhdzcpgz, 
		t.xx_zhebz, 
		t.xx_azfyze, 
		t.xx_gyqy, 
		t.xx_bxbfmc, 
		t.xx_bxbfpgjzcz, 
		t.xx_xbfpgjzcz, 
		t.xx_bflqypgjzcz, 
		t.xx_cxqypgjzcz, 
		t.xx_cxqyzgbz, 
		t.xx_yytzdgqpgz, 
		t.xx_yytzdgqzj, 
		t.xx_syjzcczsr, 
		t.xx_cjfssgzjgjg, 
		t.xx_bdqymc, 
		t.xx_bdqyssgzjgjg, 
		t.xx_qsfpbc, 
		t.xx_qsfpsqsk, 
		t.xx_qsccsfzyqczw, 
		t.xx_pcfpbc, 
		t.xx_pcfpsqsk, 
		t.xx_ywfhbjz, 
		t.fd1_czzjhj, 
		t.fd1_pgzhj, 
		t.fd1_zfzjhj, 
		t.fd1_jzzjhj, 
		t.fd2_sgjghj, 
		t.fd2_sjjzczhj, 
		t.fd2_pgjzczhj, 
		t.fd4_zgslhj, 
		t.fd5_sfzjhj, 
		t.fd6_syccfpjghj, 
		t.fd7_hzgqblhj, 
		t.fd7_hzjzczhj, 
		t.fd8_srgqsjjzczhj, 
		t.fd8_srgqpgjzczhj, 
		t.fd8_cjjhj, 
		t.fd8_zrgqblhj, 
		t.fd8_zrgqsjjzczhj, 
		t.fd8_zrgqpgjzczhj, 
		t.fd8_srgqblhj, 
		t.fd2_sggqblhj, 
		t.xx_fxgs, 
		t.xx_gkfxgs, 
		t.xx_azryzs, 
		t.xx_bxbfjzczhxbfgqbl, 
		t.xx_yytzgqzhbdqygqbl, 
		t.zl_fj, 
		t.xx_bdqypgjzcz_bzz, 
		t.xx_bdqypgjzcz_bjz, 
		t.xx_bdqypgjzcz_bgz, 
		t.xx_bdqysjjzcz_bgz, 
		t.xx_zgbz_xzgb, 
		t.xx_zgbz_js, 
		t.xx_zgbz_tzxz, 
		t.xx_zgbz_xshb, 
		t.xx_zgbz_gqcz, 
		t.zl_yxhztzs_yw, 
		t.zl_yxhztzs_ly, 
		t.zl_yxhztzs_hzdw, 
		t.jc_30sspj, 
		t.jc_mgjzcz, 
		t.jc_jcgs, 
		t.jc_jcjj, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time, 
		t.zl_qsbg, 
		t.zl_flxys, 
		t.zl_gszxzm, 
		t.zl_zgdbdhjy, 
		t.zl_gqszfawj, 
		t.zl_zxgg, 
		t.zl_hbxys, 
		t.zl_jzrsjbg, 
		t.zl_zhxy, 
		t.zl_fhbpgba, 
		t.zl_zjyqsjbg, 
		t.zl_pgba, 
		t.zl_bdpgba, 
		t.zl_wchzxy, 
		t.zl_jzgg, 
		t.zl_yyzz, 
		t.zl_sjbg, 
		t.zl_yzbg, 
		t.zl_qyzc, 
		t.zl_gdqkdjb, 
		t.zl_zhypgbab, 
		t.zl_syzcczxy, 
		t.zl_tjpgbab, 
		t.zl_gqzr, 
		t.zl_zhepgbab, 
		t.zl_xbpgbab, 
		t.zl_tzxy, 
		t.zl_bxbpgbab, 
		t.zl_gytdba, 
		t.zl_jcjg, 
		t.zl_jcwj, 
		t.zl_pcgg,
		t.zl_yxhztzs
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{zlGdqkdjbLy}, 
		#{zlQt}, 
		#{zlGszxzmGsbmmc}, 
		#{xxYwfhbcz}, 
		#{xxYwfhbzfsgjk}, 
		#{xxBdqypgjzcz}, 
		#{xxBz}, 
		#{xxBdqysjjzcz}, 
		#{xxZhyqymc}, 
		#{xxZhyssgzjgjg}, 
		#{xxZhyssgjczqy}, 
		#{xxZhyyyzhdzcpgz}, 
		#{xxZhybz}, 
		#{xxZheqymc}, 
		#{xxZhessgzjgjg}, 
		#{xxZhessgjczqy}, 
		#{xxZheyyzhdzcpgz}, 
		#{xxZhebz}, 
		#{xxAzfyze}, 
		#{xxGyqy}, 
		#{xxBxbfmc}, 
		#{xxBxbfpgjzcz}, 
		#{xxXbfpgjzcz}, 
		#{xxBflqypgjzcz}, 
		#{xxCxqypgjzcz}, 
		#{xxCxqyzgbz}, 
		#{xxYytzdgqpgz}, 
		#{xxYytzdgqzj}, 
		#{xxSyjzcczsr}, 
		#{xxCjfssgzjgjg}, 
		#{xxBdqymc}, 
		#{xxBdqyssgzjgjg}, 
		#{xxQsfpbc}, 
		#{xxQsfpsqsk}, 
		#{xxQsccsfzyqczw}, 
		#{xxPcfpbc}, 
		#{xxPcfpsqsk}, 
		#{xxYwfhbjz}, 
		#{fd1Czzjhj}, 
		#{fd1Pgzhj}, 
		#{fd1Zfzjhj}, 
		#{fd1Jzzjhj}, 
		#{fd2Sgjghj}, 
		#{fd2Sjjzczhj}, 
		#{fd2Pgjzczhj}, 
		#{fd4Zgslhj}, 
		#{fd5Sfzjhj}, 
		#{fd6Syccfpjghj}, 
		#{fd7Hzgqblhj}, 
		#{fd7Hzjzczhj}, 
		#{fd8Srgqsjjzczhj}, 
		#{fd8Srgqpgjzczhj}, 
		#{fd8Cjjhj}, 
		#{fd8Zrgqblhj}, 
		#{fd8Zrgqsjjzczhj}, 
		#{fd8Zrgqpgjzczhj}, 
		#{fd8Srgqblhj}, 
		#{fd2Sggqblhj}, 
		#{xxFxgs}, 
		#{xxGkfxgs}, 
		#{xxAzryzs}, 
		#{xxBxbfjzczhxbfgqbl}, 
		#{xxYytzgqzhbdqygqbl}, 
		#{zlFj}, 
		#{xxBdqypgjzczBzz}, 
		#{xxBdqypgjzczBjz}, 
		#{xxBdqypgjzczBgz}, 
		#{xxBdqysjjzczBgz}, 
		#{xxZgbzXzgb}, 
		#{xxZgbzJs}, 
		#{xxZgbzTzxz}, 
		#{xxZgbzXshb}, 
		#{xxZgbzGqcz}, 
		#{zlYxhztzsYw}, 
		#{zlYxhztzsLy}, 
		#{zlYxhztzsHzdw}, 
		#{jc30sspj}, 
		#{jcMgjzcz}, 
		#{jcJcgs}, 
		#{jcJcjj}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}, 
		#{zlQsbg}, 
		#{zlFlxys}, 
		#{zlGszxzm}, 
		#{zlZgdbdhjy}, 
		#{zlGqszfawj}, 
		#{zlZxgg}, 
		#{zlHbxys}, 
		#{zlJzrsjbg}, 
		#{zlZhxy}, 
		#{zlFhbpgba}, 
		#{zlZjyqsjbg}, 
		#{zlPgba}, 
		#{zlBdpgba}, 
		#{zlWchzxy}, 
		#{zlJzgg}, 
		#{zlYyzz}, 
		#{zlSjbg}, 
		#{zlYzbg}, 
		#{zlQyzc}, 
		#{zlGdqkdjb}, 
		#{zlZhypgbab}, 
		#{zlSyzcczxy}, 
		#{zlTjpgbab}, 
		#{zlGqzr}, 
		#{zlZhepgbab}, 
		#{zlXbpgbab}, 
		#{zlTzxy}, 
		#{zlBxbpgbab}, 
		#{zlGytdba}, 
		#{zlJcjg}, 
		#{zlJcwj}, 
		#{zlPcgg},
		#{zlYxhztzs}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="zlGdqkdjbLy != null and zlGdqkdjbLy != ''">
			and t.zl_gdqkdjb_ly = #{zlGdqkdjbLy}
		</if>
		<if test="zlQt != null and zlQt != ''">
			and t.zl_qt = #{zlQt}
		</if>
		<if test="zlGszxzmGsbmmc != null and zlGszxzmGsbmmc != ''">
			and t.zl_gszxzm_gsbmmc = #{zlGszxzmGsbmmc}
		</if>
		<if test="xxYwfhbcz != null and xxYwfhbcz != ''">
			and t.xx_ywfhbcz = #{xxYwfhbcz}
		</if>
		<if test="xxYwfhbzfsgjk != null and xxYwfhbzfsgjk != ''">
			and t.xx_ywfhbzfsgjk = #{xxYwfhbzfsgjk}
		</if>
		<if test="xxBdqypgjzcz != null">
			and t.xx_bdqypgjzcz = #{xxBdqypgjzcz}
		</if>
		<if test="xxBz != null and xxBz != ''">
			and t.xx_bz = #{xxBz}
		</if>
		<if test="xxBdqysjjzcz != null">
			and t.xx_bdqysjjzcz = #{xxBdqysjjzcz}
		</if>
		<if test="xxZhyqymc != null and xxZhyqymc != ''">
			and t.xx_zhyqymc = #{xxZhyqymc}
		</if>
		<if test="xxZhyssgzjgjg != null and xxZhyssgzjgjg != ''">
			and t.xx_zhyssgzjgjg = #{xxZhyssgzjgjg}
		</if>
		<if test="xxZhyssgjczqy != null and xxZhyssgjczqy != ''">
			and t.xx_zhyssgjczqy = #{xxZhyssgjczqy}
		</if>
		<if test="xxZhyyyzhdzcpgz != null">
			and t.xx_zhyyyzhdzcpgz = #{xxZhyyyzhdzcpgz}
		</if>
		<if test="xxZhybz != null and xxZhybz != ''">
			and t.xx_zhybz = #{xxZhybz}
		</if>
		<if test="xxZheqymc != null and xxZheqymc != ''">
			and t.xx_zheqymc = #{xxZheqymc}
		</if>
		<if test="xxZhessgzjgjg != null and xxZhessgzjgjg != ''">
			and t.xx_zhessgzjgjg = #{xxZhessgzjgjg}
		</if>
		<if test="xxZhessgjczqy != null and xxZhessgjczqy != ''">
			and t.xx_zhessgjczqy = #{xxZhessgjczqy}
		</if>
		<if test="xxZheyyzhdzcpgz != null">
			and t.xx_zheyyzhdzcpgz = #{xxZheyyzhdzcpgz}
		</if>
		<if test="xxZhebz != null and xxZhebz != ''">
			and t.xx_zhebz = #{xxZhebz}
		</if>
		<if test="xxAzfyze != null">
			and t.xx_azfyze = #{xxAzfyze}
		</if>
		<if test="xxGyqy != null">
			and t.xx_gyqy = #{xxGyqy}
		</if>
		<if test="xxBxbfmc != null and xxBxbfmc != ''">
			and t.xx_bxbfmc = #{xxBxbfmc}
		</if>
		<if test="xxBxbfpgjzcz != null">
			and t.xx_bxbfpgjzcz = #{xxBxbfpgjzcz}
		</if>
		<if test="xxXbfpgjzcz != null">
			and t.xx_xbfpgjzcz = #{xxXbfpgjzcz}
		</if>
		<if test="xxBflqypgjzcz != null">
			and t.xx_bflqypgjzcz = #{xxBflqypgjzcz}
		</if>
		<if test="xxCxqypgjzcz != null">
			and t.xx_cxqypgjzcz = #{xxCxqypgjzcz}
		</if>
		<if test="xxCxqyzgbz != null">
			and t.xx_cxqyzgbz = #{xxCxqyzgbz}
		</if>
		<if test="xxYytzdgqpgz != null">
			and t.xx_yytzdgqpgz = #{xxYytzdgqpgz}
		</if>
		<if test="xxYytzdgqzj != null">
			and t.xx_yytzdgqzj = #{xxYytzdgqzj}
		</if>
		<if test="xxSyjzcczsr != null">
			and t.xx_syjzcczsr = #{xxSyjzcczsr}
		</if>
		<if test="xxCjfssgzjgjg != null and xxCjfssgzjgjg != ''">
			and t.xx_cjfssgzjgjg = #{xxCjfssgzjgjg}
		</if>
		<if test="xxBdqymc != null and xxBdqymc != ''">
			and t.xx_bdqymc = #{xxBdqymc}
		</if>
		<if test="xxBdqyssgzjgjg != null and xxBdqyssgzjgjg != ''">
			and t.xx_bdqyssgzjgjg = #{xxBdqyssgzjgjg}
		</if>
		<if test="xxQsfpbc != null">
			and t.xx_qsfpbc = #{xxQsfpbc}
		</if>
		<if test="xxQsfpsqsk != null">
			and t.xx_qsfpsqsk = #{xxQsfpsqsk}
		</if>
		<if test="xxQsccsfzyqczw != null and xxQsccsfzyqczw != ''">
			and t.xx_qsccsfzyqczw = #{xxQsccsfzyqczw}
		</if>
		<if test="xxPcfpbc != null">
			and t.xx_pcfpbc = #{xxPcfpbc}
		</if>
		<if test="xxPcfpsqsk != null">
			and t.xx_pcfpsqsk = #{xxPcfpsqsk}
		</if>
		<if test="xxYwfhbjz != null and xxYwfhbjz != ''">
			and t.xx_ywfhbjz = #{xxYwfhbjz}
		</if>
		<if test="fd1Czzjhj != null">
			and t.fd1_czzjhj = #{fd1Czzjhj}
		</if>
		<if test="fd1Pgzhj != null">
			and t.fd1_pgzhj = #{fd1Pgzhj}
		</if>
		<if test="fd1Zfzjhj != null">
			and t.fd1_zfzjhj = #{fd1Zfzjhj}
		</if>
		<if test="fd1Jzzjhj != null">
			and t.fd1_jzzjhj = #{fd1Jzzjhj}
		</if>
		<if test="fd2Sgjghj != null">
			and t.fd2_sgjghj = #{fd2Sgjghj}
		</if>
		<if test="fd2Sjjzczhj != null">
			and t.fd2_sjjzczhj = #{fd2Sjjzczhj}
		</if>
		<if test="fd2Pgjzczhj != null">
			and t.fd2_pgjzczhj = #{fd2Pgjzczhj}
		</if>
		<if test="fd4Zgslhj != null">
			and t.fd4_zgslhj = #{fd4Zgslhj}
		</if>
		<if test="fd5Sfzjhj != null">
			and t.fd5_sfzjhj = #{fd5Sfzjhj}
		</if>
		<if test="fd6Syccfpjghj != null">
			and t.fd6_syccfpjghj = #{fd6Syccfpjghj}
		</if>
		<if test="fd7Hzgqblhj != null">
			and t.fd7_hzgqblhj = #{fd7Hzgqblhj}
		</if>
		<if test="fd7Hzjzczhj != null">
			and t.fd7_hzjzczhj = #{fd7Hzjzczhj}
		</if>
		<if test="fd8Srgqsjjzczhj != null">
			and t.fd8_srgqsjjzczhj = #{fd8Srgqsjjzczhj}
		</if>
		<if test="fd8Srgqpgjzczhj != null">
			and t.fd8_srgqpgjzczhj = #{fd8Srgqpgjzczhj}
		</if>
		<if test="fd8Cjjhj != null">
			and t.fd8_cjjhj = #{fd8Cjjhj}
		</if>
		<if test="fd8Zrgqblhj != null">
			and t.fd8_zrgqblhj = #{fd8Zrgqblhj}
		</if>
		<if test="fd8Zrgqsjjzczhj != null">
			and t.fd8_zrgqsjjzczhj = #{fd8Zrgqsjjzczhj}
		</if>
		<if test="fd8Zrgqpgjzczhj != null">
			and t.fd8_zrgqpgjzczhj = #{fd8Zrgqpgjzczhj}
		</if>
		<if test="fd8Srgqblhj != null">
			and t.fd8_srgqblhj = #{fd8Srgqblhj}
		</if>
		<if test="fd2Sggqblhj != null">
			and t.fd2_sggqblhj = #{fd2Sggqblhj}
		</if>
		<if test="xxFxgs != null">
			and t.xx_fxgs = #{xxFxgs}
		</if>
		<if test="xxGkfxgs != null">
			and t.xx_gkfxgs = #{xxGkfxgs}
		</if>
		<if test="xxAzryzs != null">
			and t.xx_azryzs = #{xxAzryzs}
		</if>
		<if test="xxBxbfjzczhxbfgqbl != null">
			and t.xx_bxbfjzczhxbfgqbl = #{xxBxbfjzczhxbfgqbl}
		</if>
		<if test="xxYytzgqzhbdqygqbl != null">
			and t.xx_yytzgqzhbdqygqbl = #{xxYytzgqzhbdqygqbl}
		</if>
		<if test="zlFj != null and zlFj != ''">
			and t.zl_fj = #{zlFj}
		</if>
		<if test="xxBdqypgjzczBzz != null">
			and t.xx_bdqypgjzcz_bzz = #{xxBdqypgjzczBzz}
		</if>
		<if test="xxBdqypgjzczBjz != null">
			and t.xx_bdqypgjzcz_bjz = #{xxBdqypgjzczBjz}
		</if>
		<if test="xxBdqypgjzczBgz != null">
			and t.xx_bdqypgjzcz_bgz = #{xxBdqypgjzczBgz}
		</if>
		<if test="xxBdqysjjzczBgz != null">
			and t.xx_bdqysjjzcz_bgz = #{xxBdqysjjzczBgz}
		</if>
		<if test="xxZgbzXzgb != null">
			and t.xx_zgbz_xzgb = #{xxZgbzXzgb}
		</if>
		<if test="xxZgbzJs != null">
			and t.xx_zgbz_js = #{xxZgbzJs}
		</if>
		<if test="xxZgbzTzxz != null">
			and t.xx_zgbz_tzxz = #{xxZgbzTzxz}
		</if>
		<if test="xxZgbzXshb != null">
			and t.xx_zgbz_xshb = #{xxZgbzXshb}
		</if>
		<if test="xxZgbzGqcz != null">
			and t.xx_zgbz_gqcz = #{xxZgbzGqcz}
		</if>
		<if test="zlYxhztzsYw != null and zlYxhztzsYw != ''">
			and t.zl_yxhztzs_yw = #{zlYxhztzsYw}
		</if>
		<if test="zlYxhztzsLy != null and zlYxhztzsLy != ''">
			and t.zl_yxhztzs_ly = #{zlYxhztzsLy}
		</if>
		<if test="zlYxhztzsHzdw != null and zlYxhztzsHzdw != ''">
			and t.zl_yxhztzs_hzdw = #{zlYxhztzsHzdw}
		</if>
		<if test="jc30sspj != null">
			and t.jc_30sspj = #{jc30sspj}
		</if>
		<if test="jcMgjzcz != null">
			and t.jc_mgjzcz = #{jcMgjzcz}
		</if>
		<if test="jcJcgs != null">
			and t.jc_jcgs = #{jcJcgs}
		</if>
		<if test="jcJcjj != null">
			and t.jc_jcjj = #{jcJcjj}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="zlQsbg != null and zlQsbg != ''">
			and t.zl_qsbg = #{zlQsbg}
		</if>
		<if test="zlFlxys != null and zlFlxys != ''">
			and t.zl_flxys = #{zlFlxys}
		</if>
		<if test="zlGszxzm != null and zlGszxzm != ''">
			and t.zl_gszxzm = #{zlGszxzm}
		</if>
		<if test="zlZgdbdhjy != null and zlZgdbdhjy != ''">
			and t.zl_zgdbdhjy = #{zlZgdbdhjy}
		</if>
		<if test="zlGqszfawj != null and zlGqszfawj != ''">
			and t.zl_gqszfawj = #{zlGqszfawj}
		</if>
		<if test="zlZxgg != null and zlZxgg != ''">
			and t.zl_zxgg = #{zlZxgg}
		</if>
		<if test="zlHbxys != null and zlHbxys != ''">
			and t.zl_hbxys = #{zlHbxys}
		</if>
		<if test="zlJzrsjbg != null and zlJzrsjbg != ''">
			and t.zl_jzrsjbg = #{zlJzrsjbg}
		</if>
		<if test="zlZhxy != null and zlZhxy != ''">
			and t.zl_zhxy = #{zlZhxy}
		</if>
		<if test="zlFhbpgba != null and zlFhbpgba != ''">
			and t.zl_fhbpgba = #{zlFhbpgba}
		</if>
		<if test="zlZjyqsjbg != null and zlZjyqsjbg != ''">
			and t.zl_zjyqsjbg = #{zlZjyqsjbg}
		</if>
		<if test="zlPgba != null and zlPgba != ''">
			and t.zl_pgba = #{zlPgba}
		</if>
		<if test="zlBdpgba != null and zlBdpgba != ''">
			and t.zl_bdpgba = #{zlBdpgba}
		</if>
		<if test="zlWchzxy != null and zlWchzxy != ''">
			and t.zl_wchzxy = #{zlWchzxy}
		</if>
		<if test="zlJzgg != null and zlJzgg != ''">
			and t.zl_jzgg = #{zlJzgg}
		</if>
		<if test="zlYyzz != null and zlYyzz != ''">
			and t.zl_yyzz = #{zlYyzz}
		</if>
		<if test="zlSjbg != null and zlSjbg != ''">
			and t.zl_sjbg = #{zlSjbg}
		</if>
		<if test="zlYzbg != null and zlYzbg != ''">
			and t.zl_yzbg = #{zlYzbg}
		</if>
		<if test="zlQyzc != null and zlQyzc != ''">
			and t.zl_qyzc = #{zlQyzc}
		</if>
		<if test="zlGdqkdjb != null and zlGdqkdjb != ''">
			and t.zl_gdqkdjb = #{zlGdqkdjb}
		</if>
		<if test="zlZhypgbab != null and zlZhypgbab != ''">
			and t.zl_zhypgbab = #{zlZhypgbab}
		</if>
		<if test="zlSyzcczxy != null and zlSyzcczxy != ''">
			and t.zl_syzcczxy = #{zlSyzcczxy}
		</if>
		<if test="zlTjpgbab != null and zlTjpgbab != ''">
			and t.zl_tjpgbab = #{zlTjpgbab}
		</if>
		<if test="zlGqzr != null and zlGqzr != ''">
			and t.zl_gqzr = #{zlGqzr}
		</if>
		<if test="zlZhepgbab != null and zlZhepgbab != ''">
			and t.zl_zhepgbab = #{zlZhepgbab}
		</if>
		<if test="zlXbpgbab != null and zlXbpgbab != ''">
			and t.zl_xbpgbab = #{zlXbpgbab}
		</if>
		<if test="zlTzxy != null and zlTzxy != ''">
			and t.zl_tzxy = #{zlTzxy}
		</if>
		<if test="zlBxbpgbab != null and zlBxbpgbab != ''">
			and t.zl_bxbpgbab = #{zlBxbpgbab}
		</if>
		<if test="zlGytdba != null and zlGytdba != ''">
			and t.zl_gytdba = #{zlGytdba}
		</if>
		<if test="zlJcjg != null and zlJcjg != ''">
			and t.zl_jcjg = #{zlJcjg}
		</if>
		<if test="zlJcwj != null and zlJcwj != ''">
			and t.zl_jcwj = #{zlJcwj}
		</if>
		<if test="zlPcgg != null and zlPcgg != ''">
			and t.zl_pcgg = #{zlPcgg}
		</if>
		<if test="zlYxhztzs != null and zlYxhztzs != ''">
			and t.zl_yxhztzs = #{zlYxhztzs}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_ywzbb_2 (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_ywzbb_2 set isDeleted = 'Y' where
		id in
		<foreach collection="ywzbb2s" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_ywzbb_2 set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_ywzbb_2  where
		id in
		<foreach collection="ywzbb2s" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_ywzbb_2  where id = #{id}
	</delete>
	
	<select id="selectYwzbb2ByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_ywzbb_2
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_ywzbb_2
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="zlGdqkdjbLy != null">
				zl_gdqkdjb_ly=#{zlGdqkdjbLy},
			</if>
			<if test="zlQt != null">
				zl_qt=#{zlQt},
			</if>
			<if test="zlGszxzmGsbmmc != null">
				zl_gszxzm_gsbmmc=#{zlGszxzmGsbmmc},
			</if>
			<if test="xxYwfhbcz != null">
				xx_ywfhbcz=#{xxYwfhbcz},
			</if>
			<if test="xxYwfhbzfsgjk != null">
				xx_ywfhbzfsgjk=#{xxYwfhbzfsgjk},
			</if>
			<if test="xxBdqypgjzcz != null">
				xx_bdqypgjzcz=#{xxBdqypgjzcz},
			</if>
			<if test="xxBz != null">
				xx_bz=#{xxBz},
			</if>
			<if test="xxBdqysjjzcz != null">
				xx_bdqysjjzcz=#{xxBdqysjjzcz},
			</if>
			<if test="xxZhyqymc != null">
				xx_zhyqymc=#{xxZhyqymc},
			</if>
			<if test="xxZhyssgzjgjg != null">
				xx_zhyssgzjgjg=#{xxZhyssgzjgjg},
			</if>
			<if test="xxZhyssgjczqy != null">
				xx_zhyssgjczqy=#{xxZhyssgjczqy},
			</if>
			<if test="xxZhyyyzhdzcpgz != null">
				xx_zhyyyzhdzcpgz=#{xxZhyyyzhdzcpgz},
			</if>
			<if test="xxZhybz != null">
				xx_zhybz=#{xxZhybz},
			</if>
			<if test="xxZheqymc != null">
				xx_zheqymc=#{xxZheqymc},
			</if>
			<if test="xxZhessgzjgjg != null">
				xx_zhessgzjgjg=#{xxZhessgzjgjg},
			</if>
			<if test="xxZhessgjczqy != null">
				xx_zhessgjczqy=#{xxZhessgjczqy},
			</if>
			<if test="xxZheyyzhdzcpgz != null">
				xx_zheyyzhdzcpgz=#{xxZheyyzhdzcpgz},
			</if>
			<if test="xxZhebz != null">
				xx_zhebz=#{xxZhebz},
			</if>
			<if test="xxAzfyze != null">
				xx_azfyze=#{xxAzfyze},
			</if>
			<if test="xxGyqy != null">
				xx_gyqy=#{xxGyqy},
			</if>
			<if test="xxBxbfmc != null">
				xx_bxbfmc=#{xxBxbfmc},
			</if>
			<if test="xxBxbfpgjzcz != null">
				xx_bxbfpgjzcz=#{xxBxbfpgjzcz},
			</if>
			<if test="xxXbfpgjzcz != null">
				xx_xbfpgjzcz=#{xxXbfpgjzcz},
			</if>
			<if test="xxBflqypgjzcz != null">
				xx_bflqypgjzcz=#{xxBflqypgjzcz},
			</if>
			<if test="xxCxqypgjzcz != null">
				xx_cxqypgjzcz=#{xxCxqypgjzcz},
			</if>
			<if test="xxCxqyzgbz != null">
				xx_cxqyzgbz=#{xxCxqyzgbz},
			</if>
			<if test="xxYytzdgqpgz != null">
				xx_yytzdgqpgz=#{xxYytzdgqpgz},
			</if>
			<if test="xxYytzdgqzj != null">
				xx_yytzdgqzj=#{xxYytzdgqzj},
			</if>
			<if test="xxSyjzcczsr != null">
				xx_syjzcczsr=#{xxSyjzcczsr},
			</if>
			<if test="xxCjfssgzjgjg != null">
				xx_cjfssgzjgjg=#{xxCjfssgzjgjg},
			</if>
			<if test="xxBdqymc != null">
				xx_bdqymc=#{xxBdqymc},
			</if>
			<if test="xxBdqyssgzjgjg != null">
				xx_bdqyssgzjgjg=#{xxBdqyssgzjgjg},
			</if>
			<if test="xxQsfpbc != null">
				xx_qsfpbc=#{xxQsfpbc},
			</if>
			<if test="xxQsfpsqsk != null">
				xx_qsfpsqsk=#{xxQsfpsqsk},
			</if>
			<if test="xxQsccsfzyqczw != null">
				xx_qsccsfzyqczw=#{xxQsccsfzyqczw},
			</if>
			<if test="xxPcfpbc != null">
				xx_pcfpbc=#{xxPcfpbc},
			</if>
			<if test="xxPcfpsqsk != null">
				xx_pcfpsqsk=#{xxPcfpsqsk},
			</if>
			<if test="xxYwfhbjz != null">
				xx_ywfhbjz=#{xxYwfhbjz},
			</if>
			<if test="fd1Czzjhj != null">
				fd1_czzjhj=#{fd1Czzjhj},
			</if>
			<if test="fd1Pgzhj != null">
				fd1_pgzhj=#{fd1Pgzhj},
			</if>
			<if test="fd1Zfzjhj != null">
				fd1_zfzjhj=#{fd1Zfzjhj},
			</if>
			<if test="fd1Jzzjhj != null">
				fd1_jzzjhj=#{fd1Jzzjhj},
			</if>
			<if test="fd2Sgjghj != null">
				fd2_sgjghj=#{fd2Sgjghj},
			</if>
			<if test="fd2Sjjzczhj != null">
				fd2_sjjzczhj=#{fd2Sjjzczhj},
			</if>
			<if test="fd2Pgjzczhj != null">
				fd2_pgjzczhj=#{fd2Pgjzczhj},
			</if>
			<if test="fd4Zgslhj != null">
				fd4_zgslhj=#{fd4Zgslhj},
			</if>
			<if test="fd5Sfzjhj != null">
				fd5_sfzjhj=#{fd5Sfzjhj},
			</if>
			<if test="fd6Syccfpjghj != null">
				fd6_syccfpjghj=#{fd6Syccfpjghj},
			</if>
			<if test="fd7Hzgqblhj != null">
				fd7_hzgqblhj=#{fd7Hzgqblhj},
			</if>
			<if test="fd7Hzjzczhj != null">
				fd7_hzjzczhj=#{fd7Hzjzczhj},
			</if>
			<if test="fd8Srgqsjjzczhj != null">
				fd8_srgqsjjzczhj=#{fd8Srgqsjjzczhj},
			</if>
			<if test="fd8Srgqpgjzczhj != null">
				fd8_srgqpgjzczhj=#{fd8Srgqpgjzczhj},
			</if>
			<if test="fd8Cjjhj != null">
				fd8_cjjhj=#{fd8Cjjhj},
			</if>
			<if test="fd8Zrgqblhj != null">
				fd8_zrgqblhj=#{fd8Zrgqblhj},
			</if>
			<if test="fd8Zrgqsjjzczhj != null">
				fd8_zrgqsjjzczhj=#{fd8Zrgqsjjzczhj},
			</if>
			<if test="fd8Zrgqpgjzczhj != null">
				fd8_zrgqpgjzczhj=#{fd8Zrgqpgjzczhj},
			</if>
			<if test="fd8Srgqblhj != null">
				fd8_srgqblhj=#{fd8Srgqblhj},
			</if>
			<if test="fd2Sggqblhj != null">
				fd2_sggqblhj=#{fd2Sggqblhj},
			</if>
			<if test="xxFxgs != null">
				xx_fxgs=#{xxFxgs},
			</if>
			<if test="xxGkfxgs != null">
				xx_gkfxgs=#{xxGkfxgs},
			</if>
			<if test="xxAzryzs != null">
				xx_azryzs=#{xxAzryzs},
			</if>
			<if test="xxBxbfjzczhxbfgqbl != null">
				xx_bxbfjzczhxbfgqbl=#{xxBxbfjzczhxbfgqbl},
			</if>
			<if test="xxYytzgqzhbdqygqbl != null">
				xx_yytzgqzhbdqygqbl=#{xxYytzgqzhbdqygqbl},
			</if>
			<if test="zlFj != null">
				zl_fj=#{zlFj},
			</if>
			<if test="xxBdqypgjzczBzz != null">
				xx_bdqypgjzcz_bzz=#{xxBdqypgjzczBzz},
			</if>
			<if test="xxBdqypgjzczBjz != null">
				xx_bdqypgjzcz_bjz=#{xxBdqypgjzczBjz},
			</if>
			<if test="xxBdqypgjzczBgz != null">
				xx_bdqypgjzcz_bgz=#{xxBdqypgjzczBgz},
			</if>
			<if test="xxBdqysjjzczBgz != null">
				xx_bdqysjjzcz_bgz=#{xxBdqysjjzczBgz},
			</if>
			<if test="xxZgbzXzgb != null">
				xx_zgbz_xzgb=#{xxZgbzXzgb},
			</if>
			<if test="xxZgbzJs != null">
				xx_zgbz_js=#{xxZgbzJs},
			</if>
			<if test="xxZgbzTzxz != null">
				xx_zgbz_tzxz=#{xxZgbzTzxz},
			</if>
			<if test="xxZgbzXshb != null">
				xx_zgbz_xshb=#{xxZgbzXshb},
			</if>
			<if test="xxZgbzGqcz != null">
				xx_zgbz_gqcz=#{xxZgbzGqcz},
			</if>
			<if test="zlYxhztzsYw != null">
				zl_yxhztzs_yw=#{zlYxhztzsYw},
			</if>
			<if test="zlYxhztzsLy != null">
				zl_yxhztzs_ly=#{zlYxhztzsLy},
			</if>
			<if test="zlYxhztzsHzdw != null">
				zl_yxhztzs_hzdw=#{zlYxhztzsHzdw},
			</if>
			<if test="jc30sspj != null">
				jc_30sspj=#{jc30sspj},
			</if>
			<if test="jcMgjzcz != null">
				jc_mgjzcz=#{jcMgjzcz},
			</if>
			<if test="jcJcgs != null">
				jc_jcgs=#{jcJcgs},
			</if>
			<if test="jcJcjj != null">
				jc_jcjj=#{jcJcjj},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime},
			</if>
			<if test="zlQsbg != null">
				zl_qsbg=#{zlQsbg},
			</if>
			<if test="zlFlxys != null">
				zl_flxys=#{zlFlxys},
			</if>
			<if test="zlGszxzm != null">
				zl_gszxzm=#{zlGszxzm},
			</if>
			<if test="zlZgdbdhjy != null">
				zl_zgdbdhjy=#{zlZgdbdhjy},
			</if>
			<if test="zlGqszfawj != null">
				zl_gqszfawj=#{zlGqszfawj},
			</if>
			<if test="zlZxgg != null">
				zl_zxgg=#{zlZxgg},
			</if>
			<if test="zlHbxys != null">
				zl_hbxys=#{zlHbxys},
			</if>
			<if test="zlJzrsjbg != null">
				zl_jzrsjbg=#{zlJzrsjbg},
			</if>
			<if test="zlZhxy != null">
				zl_zhxy=#{zlZhxy},
			</if>
			<if test="zlFhbpgba != null">
				zl_fhbpgba=#{zlFhbpgba},
			</if>
			<if test="zlZjyqsjbg != null">
				zl_zjyqsjbg=#{zlZjyqsjbg},
			</if>
			<if test="zlPgba != null">
				zl_pgba=#{zlPgba},
			</if>
			<if test="zlBdpgba != null">
				zl_bdpgba=#{zlBdpgba},
			</if>
			<if test="zlWchzxy != null">
				zl_wchzxy=#{zlWchzxy},
			</if>
			<if test="zlJzgg != null">
				zl_jzgg=#{zlJzgg},
			</if>
			<if test="zlYyzz != null">
				zl_yyzz=#{zlYyzz},
			</if>
			<if test="zlSjbg != null">
				zl_sjbg=#{zlSjbg},
			</if>
			<if test="zlYzbg != null">
				zl_yzbg=#{zlYzbg},
			</if>
			<if test="zlQyzc != null">
				zl_qyzc=#{zlQyzc},
			</if>
			<if test="zlGdqkdjb != null">
				zl_gdqkdjb=#{zlGdqkdjb},
			</if>
			<if test="zlZhypgbab != null">
				zl_zhypgbab=#{zlZhypgbab},
			</if>
			<if test="zlSyzcczxy != null">
				zl_syzcczxy=#{zlSyzcczxy},
			</if>
			<if test="zlTjpgbab != null">
				zl_tjpgbab=#{zlTjpgbab},
			</if>
			<if test="zlGqzr != null">
				zl_gqzr=#{zlGqzr},
			</if>
			<if test="zlZhepgbab != null">
				zl_zhepgbab=#{zlZhepgbab},
			</if>
			<if test="zlXbpgbab != null">
				zl_xbpgbab=#{zlXbpgbab},
			</if>
			<if test="zlTzxy != null">
				zl_tzxy=#{zlTzxy},
			</if>
			<if test="zlBxbpgbab != null">
				zl_bxbpgbab=#{zlBxbpgbab},
			</if>
			<if test="zlGytdba != null">
				zl_gytdba=#{zlGytdba},
			</if>
			<if test="zlJcjg != null">
				zl_jcjg=#{zlJcjg},
			</if>
			<if test="zlJcwj != null">
				zl_jcwj=#{zlJcwj},
			</if>
			<if test="zlPcgg != null">
				zl_pcgg=#{zlPcgg}
			</if>
			<if test="zlYxhztzs != null">
				zl_yxhztzs=#{zlYxhztzs}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_ywzbb_2
		<set>
			jbxx_id=#{jbxxId},
			zl_gdqkdjb_ly=#{zlGdqkdjbLy},
			zl_qt=#{zlQt},
			zl_gszxzm_gsbmmc=#{zlGszxzmGsbmmc},
			xx_ywfhbcz=#{xxYwfhbcz},
			xx_ywfhbzfsgjk=#{xxYwfhbzfsgjk},
			xx_bdqypgjzcz=#{xxBdqypgjzcz},
			xx_bz=#{xxBz},
			xx_bdqysjjzcz=#{xxBdqysjjzcz},
			xx_zhyqymc=#{xxZhyqymc},
			xx_zhyssgzjgjg=#{xxZhyssgzjgjg},
			xx_zhyssgjczqy=#{xxZhyssgjczqy},
			xx_zhyyyzhdzcpgz=#{xxZhyyyzhdzcpgz},
			xx_zhybz=#{xxZhybz},
			xx_zheqymc=#{xxZheqymc},
			xx_zhessgzjgjg=#{xxZhessgzjgjg},
			xx_zhessgjczqy=#{xxZhessgjczqy},
			xx_zheyyzhdzcpgz=#{xxZheyyzhdzcpgz},
			xx_zhebz=#{xxZhebz},
			xx_azfyze=#{xxAzfyze},
			xx_gyqy=#{xxGyqy},
			xx_bxbfmc=#{xxBxbfmc},
			xx_bxbfpgjzcz=#{xxBxbfpgjzcz},
			xx_xbfpgjzcz=#{xxXbfpgjzcz},
			xx_bflqypgjzcz=#{xxBflqypgjzcz},
			xx_cxqypgjzcz=#{xxCxqypgjzcz},
			xx_cxqyzgbz=#{xxCxqyzgbz},
			xx_yytzdgqpgz=#{xxYytzdgqpgz},
			xx_yytzdgqzj=#{xxYytzdgqzj},
			xx_syjzcczsr=#{xxSyjzcczsr},
			xx_cjfssgzjgjg=#{xxCjfssgzjgjg},
			xx_bdqymc=#{xxBdqymc},
			xx_bdqyssgzjgjg=#{xxBdqyssgzjgjg},
			xx_qsfpbc=#{xxQsfpbc},
			xx_qsfpsqsk=#{xxQsfpsqsk},
			xx_qsccsfzyqczw=#{xxQsccsfzyqczw},
			xx_pcfpbc=#{xxPcfpbc},
			xx_pcfpsqsk=#{xxPcfpsqsk},
			xx_ywfhbjz=#{xxYwfhbjz},
			fd1_czzjhj=#{fd1Czzjhj},
			fd1_pgzhj=#{fd1Pgzhj},
			fd1_zfzjhj=#{fd1Zfzjhj},
			fd1_jzzjhj=#{fd1Jzzjhj},
			fd2_sgjghj=#{fd2Sgjghj},
			fd2_sjjzczhj=#{fd2Sjjzczhj},
			fd2_pgjzczhj=#{fd2Pgjzczhj},
			fd4_zgslhj=#{fd4Zgslhj},
			fd5_sfzjhj=#{fd5Sfzjhj},
			fd6_syccfpjghj=#{fd6Syccfpjghj},
			fd7_hzgqblhj=#{fd7Hzgqblhj},
			fd7_hzjzczhj=#{fd7Hzjzczhj},
			fd8_srgqsjjzczhj=#{fd8Srgqsjjzczhj},
			fd8_srgqpgjzczhj=#{fd8Srgqpgjzczhj},
			fd8_cjjhj=#{fd8Cjjhj},
			fd8_zrgqblhj=#{fd8Zrgqblhj},
			fd8_zrgqsjjzczhj=#{fd8Zrgqsjjzczhj},
			fd8_zrgqpgjzczhj=#{fd8Zrgqpgjzczhj},
			fd8_srgqblhj=#{fd8Srgqblhj},
			fd2_sggqblhj=#{fd2Sggqblhj},
			xx_fxgs=#{xxFxgs},
			xx_gkfxgs=#{xxGkfxgs},
			xx_azryzs=#{xxAzryzs},
			xx_bxbfjzczhxbfgqbl=#{xxBxbfjzczhxbfgqbl},
			xx_yytzgqzhbdqygqbl=#{xxYytzgqzhbdqygqbl},
			zl_fj=#{zlFj},
			xx_bdqypgjzcz_bzz=#{xxBdqypgjzczBzz},
			xx_bdqypgjzcz_bjz=#{xxBdqypgjzczBjz},
			xx_bdqypgjzcz_bgz=#{xxBdqypgjzczBgz},
			xx_bdqysjjzcz_bgz=#{xxBdqysjjzczBgz},
			xx_zgbz_xzgb=#{xxZgbzXzgb},
			xx_zgbz_js=#{xxZgbzJs},
			xx_zgbz_tzxz=#{xxZgbzTzxz},
			xx_zgbz_xshb=#{xxZgbzXshb},
			xx_zgbz_gqcz=#{xxZgbzGqcz},
			zl_yxhztzs_yw=#{zlYxhztzsYw},
			zl_yxhztzs_ly=#{zlYxhztzsLy},
			zl_yxhztzs_hzdw=#{zlYxhztzsHzdw},
			jc_30sspj=#{jc30sspj},
			jc_mgjzcz=#{jcMgjzcz},
			jc_jcgs=#{jcJcgs},
			jc_jcjj=#{jcJcjj},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime},
			zl_qsbg=#{zlQsbg},
			zl_flxys=#{zlFlxys},
			zl_gszxzm=#{zlGszxzm},
			zl_zgdbdhjy=#{zlZgdbdhjy},
			zl_gqszfawj=#{zlGqszfawj},
			zl_zxgg=#{zlZxgg},
			zl_hbxys=#{zlHbxys},
			zl_jzrsjbg=#{zlJzrsjbg},
			zl_zhxy=#{zlZhxy},
			zl_fhbpgba=#{zlFhbpgba},
			zl_zjyqsjbg=#{zlZjyqsjbg},
			zl_pgba=#{zlPgba},
			zl_bdpgba=#{zlBdpgba},
			zl_wchzxy=#{zlWchzxy},
			zl_jzgg=#{zlJzgg},
			zl_yyzz=#{zlYyzz},
			zl_sjbg=#{zlSjbg},
			zl_yzbg=#{zlYzbg},
			zl_qyzc=#{zlQyzc},
			zl_gdqkdjb=#{zlGdqkdjb},
			zl_zhypgbab=#{zlZhypgbab},
			zl_syzcczxy=#{zlSyzcczxy},
			zl_tjpgbab=#{zlTjpgbab},
			zl_gqzr=#{zlGqzr},
			zl_zhepgbab=#{zlZhepgbab},
			zl_xbpgbab=#{zlXbpgbab},
			zl_tzxy=#{zlTzxy},
			zl_bxbpgbab=#{zlBxbpgbab},
			zl_gytdba=#{zlGytdba},
			zl_jcjg=#{zlJcjg},
			zl_jcwj=#{zlJcwj},
			zl_pcgg=#{zlPcgg},
			zl_yxhztzs=#{zlYxhztzs}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_ywzbb_2 t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalYwzbb2s" parameterType="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Param" resultType="java.lang.Long">
		select
			count(ID)
		from cq_ywzbb_2 t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryYwzbb2ForList" parameterType="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Param" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_ywzbb_2 t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_ywzbb_2 t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="zlGdqkdjbLy != null and zlGdqkdjbLy != ''">
				and t.zl_gdqkdjb_ly = #{zlGdqkdjbLy}
			</if>
			<if test="zlQt != null and zlQt != ''">
				and t.zl_qt = #{zlQt}
			</if>
			<if test="zlGszxzmGsbmmc != null and zlGszxzmGsbmmc != ''">
				and t.zl_gszxzm_gsbmmc = #{zlGszxzmGsbmmc}
			</if>
			<if test="xxYwfhbcz != null and xxYwfhbcz != ''">
				and t.xx_ywfhbcz = #{xxYwfhbcz}
			</if>
			<if test="xxYwfhbzfsgjk != null and xxYwfhbzfsgjk != ''">
				and t.xx_ywfhbzfsgjk = #{xxYwfhbzfsgjk}
			</if>
			<if test="xxBdqypgjzcz != null and xxBdqypgjzcz != ''">
				and t.xx_bdqypgjzcz = #{xxBdqypgjzcz}
			</if>
			<if test="xxBz != null and xxBz != ''">
				and t.xx_bz = #{xxBz}
			</if>
			<if test="xxBdqysjjzcz != null and xxBdqysjjzcz != ''">
				and t.xx_bdqysjjzcz = #{xxBdqysjjzcz}
			</if>
			<if test="xxZhyqymc != null and xxZhyqymc != ''">
				and t.xx_zhyqymc = #{xxZhyqymc}
			</if>
			<if test="xxZhyssgzjgjg != null and xxZhyssgzjgjg != ''">
				and t.xx_zhyssgzjgjg = #{xxZhyssgzjgjg}
			</if>
			<if test="xxZhyssgjczqy != null and xxZhyssgjczqy != ''">
				and t.xx_zhyssgjczqy = #{xxZhyssgjczqy}
			</if>
			<if test="xxZhyyyzhdzcpgz != null and xxZhyyyzhdzcpgz != ''">
				and t.xx_zhyyyzhdzcpgz = #{xxZhyyyzhdzcpgz}
			</if>
			<if test="xxZhybz != null and xxZhybz != ''">
				and t.xx_zhybz = #{xxZhybz}
			</if>
			<if test="xxZheqymc != null and xxZheqymc != ''">
				and t.xx_zheqymc = #{xxZheqymc}
			</if>
			<if test="xxZhessgzjgjg != null and xxZhessgzjgjg != ''">
				and t.xx_zhessgzjgjg = #{xxZhessgzjgjg}
			</if>
			<if test="xxZhessgjczqy != null and xxZhessgjczqy != ''">
				and t.xx_zhessgjczqy = #{xxZhessgjczqy}
			</if>
			<if test="xxZheyyzhdzcpgz != null and xxZheyyzhdzcpgz != ''">
				and t.xx_zheyyzhdzcpgz = #{xxZheyyzhdzcpgz}
			</if>
			<if test="xxZhebz != null and xxZhebz != ''">
				and t.xx_zhebz = #{xxZhebz}
			</if>
			<if test="xxAzfyze != null and xxAzfyze != ''">
				and t.xx_azfyze = #{xxAzfyze}
			</if>
			<if test="xxGyqy != null and xxGyqy != ''">
				and t.xx_gyqy = #{xxGyqy}
			</if>
			<if test="xxBxbfmc != null and xxBxbfmc != ''">
				and t.xx_bxbfmc = #{xxBxbfmc}
			</if>
			<if test="xxBxbfpgjzcz != null and xxBxbfpgjzcz != ''">
				and t.xx_bxbfpgjzcz = #{xxBxbfpgjzcz}
			</if>
			<if test="xxXbfpgjzcz != null and xxXbfpgjzcz != ''">
				and t.xx_xbfpgjzcz = #{xxXbfpgjzcz}
			</if>
			<if test="xxBflqypgjzcz != null and xxBflqypgjzcz != ''">
				and t.xx_bflqypgjzcz = #{xxBflqypgjzcz}
			</if>
			<if test="xxCxqypgjzcz != null and xxCxqypgjzcz != ''">
				and t.xx_cxqypgjzcz = #{xxCxqypgjzcz}
			</if>
			<if test="xxCxqyzgbz != null and xxCxqyzgbz != ''">
				and t.xx_cxqyzgbz = #{xxCxqyzgbz}
			</if>
			<if test="xxYytzdgqpgz != null and xxYytzdgqpgz != ''">
				and t.xx_yytzdgqpgz = #{xxYytzdgqpgz}
			</if>
			<if test="xxYytzdgqzj != null and xxYytzdgqzj != ''">
				and t.xx_yytzdgqzj = #{xxYytzdgqzj}
			</if>
			<if test="xxSyjzcczsr != null and xxSyjzcczsr != ''">
				and t.xx_syjzcczsr = #{xxSyjzcczsr}
			</if>
			<if test="xxCjfssgzjgjg != null and xxCjfssgzjgjg != ''">
				and t.xx_cjfssgzjgjg = #{xxCjfssgzjgjg}
			</if>
			<if test="xxBdqymc != null and xxBdqymc != ''">
				and t.xx_bdqymc = #{xxBdqymc}
			</if>
			<if test="xxBdqyssgzjgjg != null and xxBdqyssgzjgjg != ''">
				and t.xx_bdqyssgzjgjg = #{xxBdqyssgzjgjg}
			</if>
			<if test="xxQsfpbc != null and xxQsfpbc != ''">
				and t.xx_qsfpbc = #{xxQsfpbc}
			</if>
			<if test="xxQsfpsqsk != null and xxQsfpsqsk != ''">
				and t.xx_qsfpsqsk = #{xxQsfpsqsk}
			</if>
			<if test="xxQsccsfzyqczw != null and xxQsccsfzyqczw != ''">
				and t.xx_qsccsfzyqczw = #{xxQsccsfzyqczw}
			</if>
			<if test="xxPcfpbc != null and xxPcfpbc != ''">
				and t.xx_pcfpbc = #{xxPcfpbc}
			</if>
			<if test="xxPcfpsqsk != null and xxPcfpsqsk != ''">
				and t.xx_pcfpsqsk = #{xxPcfpsqsk}
			</if>
			<if test="xxYwfhbjz != null and xxYwfhbjz != ''">
				and t.xx_ywfhbjz = #{xxYwfhbjz}
			</if>
			<if test="fd1Czzjhj != null and fd1Czzjhj != ''">
				and t.fd1_czzjhj = #{fd1Czzjhj}
			</if>
			<if test="fd1Pgzhj != null and fd1Pgzhj != ''">
				and t.fd1_pgzhj = #{fd1Pgzhj}
			</if>
			<if test="fd1Zfzjhj != null and fd1Zfzjhj != ''">
				and t.fd1_zfzjhj = #{fd1Zfzjhj}
			</if>
			<if test="fd1Jzzjhj != null and fd1Jzzjhj != ''">
				and t.fd1_jzzjhj = #{fd1Jzzjhj}
			</if>
			<if test="fd2Sgjghj != null and fd2Sgjghj != ''">
				and t.fd2_sgjghj = #{fd2Sgjghj}
			</if>
			<if test="fd2Sjjzczhj != null and fd2Sjjzczhj != ''">
				and t.fd2_sjjzczhj = #{fd2Sjjzczhj}
			</if>
			<if test="fd2Pgjzczhj != null and fd2Pgjzczhj != ''">
				and t.fd2_pgjzczhj = #{fd2Pgjzczhj}
			</if>
			<if test="fd4Zgslhj != null and fd4Zgslhj != ''">
				and t.fd4_zgslhj = #{fd4Zgslhj}
			</if>
			<if test="fd5Sfzjhj != null and fd5Sfzjhj != ''">
				and t.fd5_sfzjhj = #{fd5Sfzjhj}
			</if>
			<if test="fd6Syccfpjghj != null and fd6Syccfpjghj != ''">
				and t.fd6_syccfpjghj = #{fd6Syccfpjghj}
			</if>
			<if test="fd7Hzgqblhj != null and fd7Hzgqblhj != ''">
				and t.fd7_hzgqblhj = #{fd7Hzgqblhj}
			</if>
			<if test="fd7Hzjzczhj != null and fd7Hzjzczhj != ''">
				and t.fd7_hzjzczhj = #{fd7Hzjzczhj}
			</if>
			<if test="fd8Srgqsjjzczhj != null and fd8Srgqsjjzczhj != ''">
				and t.fd8_srgqsjjzczhj = #{fd8Srgqsjjzczhj}
			</if>
			<if test="fd8Srgqpgjzczhj != null and fd8Srgqpgjzczhj != ''">
				and t.fd8_srgqpgjzczhj = #{fd8Srgqpgjzczhj}
			</if>
			<if test="fd8Cjjhj != null and fd8Cjjhj != ''">
				and t.fd8_cjjhj = #{fd8Cjjhj}
			</if>
			<if test="fd8Zrgqblhj != null and fd8Zrgqblhj != ''">
				and t.fd8_zrgqblhj = #{fd8Zrgqblhj}
			</if>
			<if test="fd8Zrgqsjjzczhj != null and fd8Zrgqsjjzczhj != ''">
				and t.fd8_zrgqsjjzczhj = #{fd8Zrgqsjjzczhj}
			</if>
			<if test="fd8Zrgqpgjzczhj != null and fd8Zrgqpgjzczhj != ''">
				and t.fd8_zrgqpgjzczhj = #{fd8Zrgqpgjzczhj}
			</if>
			<if test="fd8Srgqblhj != null and fd8Srgqblhj != ''">
				and t.fd8_srgqblhj = #{fd8Srgqblhj}
			</if>
			<if test="fd2Sggqblhj != null and fd2Sggqblhj != ''">
				and t.fd2_sggqblhj = #{fd2Sggqblhj}
			</if>
			<if test="xxFxgs != null and xxFxgs != ''">
				and t.xx_fxgs = #{xxFxgs}
			</if>
			<if test="xxGkfxgs != null and xxGkfxgs != ''">
				and t.xx_gkfxgs = #{xxGkfxgs}
			</if>
			<if test="xxAzryzs != null and xxAzryzs != ''">
				and t.xx_azryzs = #{xxAzryzs}
			</if>
			<if test="xxBxbfjzczhxbfgqbl != null and xxBxbfjzczhxbfgqbl != ''">
				and t.xx_bxbfjzczhxbfgqbl = #{xxBxbfjzczhxbfgqbl}
			</if>
			<if test="xxYytzgqzhbdqygqbl != null and xxYytzgqzhbdqygqbl != ''">
				and t.xx_yytzgqzhbdqygqbl = #{xxYytzgqzhbdqygqbl}
			</if>
			<if test="zlFj != null and zlFj != ''">
				and t.zl_fj = #{zlFj}
			</if>
			<if test="xxBdqypgjzczBzz != null and xxBdqypgjzczBzz != ''">
				and t.xx_bdqypgjzcz_bzz = #{xxBdqypgjzczBzz}
			</if>
			<if test="xxBdqypgjzczBjz != null and xxBdqypgjzczBjz != ''">
				and t.xx_bdqypgjzcz_bjz = #{xxBdqypgjzczBjz}
			</if>
			<if test="xxBdqypgjzczBgz != null and xxBdqypgjzczBgz != ''">
				and t.xx_bdqypgjzcz_bgz = #{xxBdqypgjzczBgz}
			</if>
			<if test="xxBdqysjjzczBgz != null and xxBdqysjjzczBgz != ''">
				and t.xx_bdqysjjzcz_bgz = #{xxBdqysjjzczBgz}
			</if>
			<if test="xxZgbzXzgb != null and xxZgbzXzgb != ''">
				and t.xx_zgbz_xzgb = #{xxZgbzXzgb}
			</if>
			<if test="xxZgbzJs != null and xxZgbzJs != ''">
				and t.xx_zgbz_js = #{xxZgbzJs}
			</if>
			<if test="xxZgbzTzxz != null and xxZgbzTzxz != ''">
				and t.xx_zgbz_tzxz = #{xxZgbzTzxz}
			</if>
			<if test="xxZgbzXshb != null and xxZgbzXshb != ''">
				and t.xx_zgbz_xshb = #{xxZgbzXshb}
			</if>
			<if test="xxZgbzGqcz != null and xxZgbzGqcz != ''">
				and t.xx_zgbz_gqcz = #{xxZgbzGqcz}
			</if>
			<if test="zlYxhztzsYw != null and zlYxhztzsYw != ''">
				and t.zl_yxhztzs_yw = #{zlYxhztzsYw}
			</if>
			<if test="zlYxhztzsLy != null and zlYxhztzsLy != ''">
				and t.zl_yxhztzs_ly = #{zlYxhztzsLy}
			</if>
			<if test="zlYxhztzsHzdw != null and zlYxhztzsHzdw != ''">
				and t.zl_yxhztzs_hzdw = #{zlYxhztzsHzdw}
			</if>
			<if test="jc30sspj != null and jc30sspj != ''">
				and t.jc_30sspj = #{jc30sspj}
			</if>
			<if test="jcMgjzcz != null and jcMgjzcz != ''">
				and t.jc_mgjzcz = #{jcMgjzcz}
			</if>
			<if test="jcJcgs != null and jcJcgs != ''">
				and t.jc_jcgs = #{jcJcgs}
			</if>
			<if test="jcJcjj != null and jcJcjj != ''">
				and t.jc_jcjj = #{jcJcjj}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
			<if test="zlQsbg != null and zlQsbg != ''">
				and t.zl_qsbg = #{zlQsbg}
			</if>
			<if test="zlFlxys != null and zlFlxys != ''">
				and t.zl_flxys = #{zlFlxys}
			</if>
			<if test="zlGszxzm != null and zlGszxzm != ''">
				and t.zl_gszxzm = #{zlGszxzm}
			</if>
			<if test="zlZgdbdhjy != null and zlZgdbdhjy != ''">
				and t.zl_zgdbdhjy = #{zlZgdbdhjy}
			</if>
			<if test="zlGqszfawj != null and zlGqszfawj != ''">
				and t.zl_gqszfawj = #{zlGqszfawj}
			</if>
			<if test="zlZxgg != null and zlZxgg != ''">
				and t.zl_zxgg = #{zlZxgg}
			</if>
			<if test="zlHbxys != null and zlHbxys != ''">
				and t.zl_hbxys = #{zlHbxys}
			</if>
			<if test="zlJzrsjbg != null and zlJzrsjbg != ''">
				and t.zl_jzrsjbg = #{zlJzrsjbg}
			</if>
			<if test="zlZhxy != null and zlZhxy != ''">
				and t.zl_zhxy = #{zlZhxy}
			</if>
			<if test="zlFhbpgba != null and zlFhbpgba != ''">
				and t.zl_fhbpgba = #{zlFhbpgba}
			</if>
			<if test="zlZjyqsjbg != null and zlZjyqsjbg != ''">
				and t.zl_zjyqsjbg = #{zlZjyqsjbg}
			</if>
			<if test="zlPgba != null and zlPgba != ''">
				and t.zl_pgba = #{zlPgba}
			</if>
			<if test="zlBdpgba != null and zlBdpgba != ''">
				and t.zl_bdpgba = #{zlBdpgba}
			</if>
			<if test="zlWchzxy != null and zlWchzxy != ''">
				and t.zl_wchzxy = #{zlWchzxy}
			</if>
			<if test="zlJzgg != null and zlJzgg != ''">
				and t.zl_jzgg = #{zlJzgg}
			</if>
			<if test="zlYyzz != null and zlYyzz != ''">
				and t.zl_yyzz = #{zlYyzz}
			</if>
			<if test="zlSjbg != null and zlSjbg != ''">
				and t.zl_sjbg = #{zlSjbg}
			</if>
			<if test="zlYzbg != null and zlYzbg != ''">
				and t.zl_yzbg = #{zlYzbg}
			</if>
			<if test="zlQyzc != null and zlQyzc != ''">
				and t.zl_qyzc = #{zlQyzc}
			</if>
			<if test="zlGdqkdjb != null and zlGdqkdjb != ''">
				and t.zl_gdqkdjb = #{zlGdqkdjb}
			</if>
			<if test="zlZhypgbab != null and zlZhypgbab != ''">
				and t.zl_zhypgbab = #{zlZhypgbab}
			</if>
			<if test="zlSyzcczxy != null and zlSyzcczxy != ''">
				and t.zl_syzcczxy = #{zlSyzcczxy}
			</if>
			<if test="zlTjpgbab != null and zlTjpgbab != ''">
				and t.zl_tjpgbab = #{zlTjpgbab}
			</if>
			<if test="zlGqzr != null and zlGqzr != ''">
				and t.zl_gqzr = #{zlGqzr}
			</if>
			<if test="zlZhepgbab != null and zlZhepgbab != ''">
				and t.zl_zhepgbab = #{zlZhepgbab}
			</if>
			<if test="zlXbpgbab != null and zlXbpgbab != ''">
				and t.zl_xbpgbab = #{zlXbpgbab}
			</if>
			<if test="zlTzxy != null and zlTzxy != ''">
				and t.zl_tzxy = #{zlTzxy}
			</if>
			<if test="zlBxbpgbab != null and zlBxbpgbab != ''">
				and t.zl_bxbpgbab = #{zlBxbpgbab}
			</if>
			<if test="zlGytdba != null and zlGytdba != ''">
				and t.zl_gytdba = #{zlGytdba}
			</if>
			<if test="zlJcjg != null and zlJcjg != ''">
				and t.zl_jcjg = #{zlJcjg}
			</if>
			<if test="zlJcwj != null and zlJcwj != ''">
				and t.zl_jcwj = #{zlJcwj}
			</if>
			<if test="zlPcgg != null and zlPcgg != ''">
				and t.zl_pcgg = #{zlPcgg}
			</if>
			<if test="zlYxhztzs != null and zlYxhztzs != ''">
				and t.zl_yxhztzs = #{zlYxhztzs}
			</if>
	</select>

	<resultMap id="resultMapWithAttachment" type="com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo" extends="baseResultMapExt">
		<collection property="otherAttachments" ofType="com.zjhc.gzwcq.attachment.entity.Attachment">
			<id column="a_id" property="id"/>
			<result column="a_file_name" property="fileName"/>
			<result column="a_last_file_name" property="lastFileName"/>
			<result column="a_file_type" property="fileType"/>
			<result column="a_ftp_file_path" property="ftpFilePath"/>
			<result column="a_org_id" property="orgId"/>
			<result column="a_remark" property="remark"/>
			<result column="a_create_user" property="createUser"/>
			<result column="a_create_time" property="createTime"/>
			<result column="a_last_update_user" property="lastUpdateUser"/>
			<result column="a_last_update_time" property="lastUpdateTime"/>
		</collection>
	</resultMap>
	<select id="selectByJbxxId" parameterType="java.lang.String" resultMap="resultMapWithAttachment">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr,
		sa.id a_id,sa.file_name a_file_name,sa.last_file_name a_last_file_name,
		sa.file_type a_file_type,sa.ftp_file_path a_ftp_file_path,sa.org_id a_org_id,
		sa.remark a_remark,sa.create_user a_create_user,sa.create_time a_create_time,
		sa.last_update_user a_last_update_user,sa.last_update_time a_last_update_time,sd1.text xxYwfhbczStr,
		ifnull(sd2.text,t.XX_ZHYSSGZJGJG) xxZhyssgzjgjgStr,ifnull(sd3.text,t.XX_ZHESSGZJGJG) xxZhessgzjgjgStr,sd4.text xxZhyssgjczqyStr,sd5.text xxZhessgjczqyStr
		from
		cq_ywzbb_2 t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		left join sys_attachment sa on sa.id = t.zl_fj
		left join sys_dictionary sd1 on sd1.val=t.XX_YWFHBCZ and sd1.type_id=(select id from sys_dictionary where type_code='YW')
		left join sys_dictionary sd2 on sd2.val=t.XX_ZHYSSGZJGJG and sd2.type_id=(select id from sys_dictionary where type_code='GZJGJGFD')
		left join sys_dictionary sd3 on sd3.val=t.XX_ZHESSGZJGJG and sd3.type_id=(select id from sys_dictionary where type_code='GZJGJGFD')
		left join sys_dictionary sd4 on sd4.val=t.XX_ZHYSSGJCZQY and sd4.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		left join sys_dictionary sd5 on sd5.val=t.XX_ZHESSGJCZQY and sd5.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		where t.jbxx_id = #{jbxxId}
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_ywzbb_2 where jbxx_id = #{jbxxId}
	</delete>
</mapper>