package com.zjhc.gzwcq.apiCallLogs.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogsVo;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogsParam;

public interface IApiCallLogsService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(ApiCallLogs apiCallLogs);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(ApiCallLogs apiCallLogs);
	
	/**
	* 更新
	*/
	void update(ApiCallLogs apiCallLogs);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<ApiCallLogsVo> queryApiCallLogsByPage(ApiCallLogsParam apiCallLogsParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalApiCallLogss(ApiCallLogsParam apiCallLogsParam);
  
	
	/**
	 *通过ID查询数据
	 */
	ApiCallLogs selectApiCallLogsByPrimaryKey(ApiCallLogs apiCallLogs);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<ApiCallLogs> selectForList(ApiCallLogs apiCallLogs);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(ApiCallLogs apiCallLogs);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(ApiCallLogs apiCallLogs);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(ApiCallLogs[] objs);
	
}