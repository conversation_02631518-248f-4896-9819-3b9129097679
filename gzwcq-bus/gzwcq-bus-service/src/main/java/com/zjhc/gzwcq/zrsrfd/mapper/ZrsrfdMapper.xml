<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.zrsrfd.mapper.IZrsrfdMapper">

	<resultMap type="com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="fd8_zrfmc" property="fd8Zrfmc"/>
		<result column="fd8_zrlb" property="fd8Zrlb"/>
		<result column="fd8_srgqsjjzcz" property="fd8Srgqsjjzcz"/>
		<result column="fd8_srgqpgjzcz" property="fd8Srgqpgjzcz"/>
		<result column="fd8_cjj" property="fd8Cjj"/>
		<result column="fd8_srfmc" property="fd8Srfmc"/>
		<result column="fd8_srfxz" property="fd8Srfxz"/>
		<result column="fd8_zrgqsjjzcz" property="fd8Zrgqsjjzcz"/>
		<result column="fd8_zrgqpgjzcz" property="fd8Zrgqpgjzcz"/>
		<result column="fd8_zjyj" property="fd8Zjyj"/>
		<result column="fd8_bz" property="fd8Bz"/>
		<result column="fd8_zrfssgzjgjg" property="fd8Zrfssgzjgjg"/>
		<result column="fd8_srfssgzjgjg" property="fd8Srfssgzjgjg"/>
		<result column="fd8_zrgqbl" property="fd8Zrgqbl"/>
		<result column="fd8_srgqbl" property="fd8Srgqbl"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdVo" extends="baseResultMap">
		<result column="fd8SrfxzStr" property="fd8SrfxzStr"/>
		<result column="fd8ZrlbStr" property="fd8ZrlbStr"/>
		<result column="fd8ZrfssgzjgjgStr" property="fd8ZrfssgzjgjgStr"/>
		<result column="fd8SrfssgzjgjgStr" property="fd8SrfssgzjgjgStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		fd8_zrfmc, 
		fd8_zrlb, 
		fd8_srgqsjjzcz, 
		fd8_srgqpgjzcz, 
		fd8_cjj, 
		fd8_srfmc, 
		fd8_srfxz, 
		fd8_zrgqsjjzcz, 
		fd8_zrgqpgjzcz, 
		fd8_zjyj, 
		fd8_bz, 
		fd8_zrfssgzjgjg, 
		fd8_srfssgzjgjg, 
		fd8_zrgqbl, 
		fd8_srgqbl, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.fd8_zrfmc, 
		t.fd8_zrlb, 
		t.fd8_srgqsjjzcz, 
		t.fd8_srgqpgjzcz, 
		t.fd8_cjj, 
		t.fd8_srfmc, 
		t.fd8_srfxz, 
		t.fd8_zrgqsjjzcz, 
		t.fd8_zrgqpgjzcz, 
		t.fd8_zjyj, 
		t.fd8_bz, 
		t.fd8_zrfssgzjgjg, 
		t.fd8_srfssgzjgjg, 
		t.fd8_zrgqbl, 
		t.fd8_srgqbl, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{fd8Zrfmc}, 
		#{fd8Zrlb}, 
		#{fd8Srgqsjjzcz}, 
		#{fd8Srgqpgjzcz}, 
		#{fd8Cjj}, 
		#{fd8Srfmc}, 
		#{fd8Srfxz}, 
		#{fd8Zrgqsjjzcz}, 
		#{fd8Zrgqpgjzcz}, 
		#{fd8Zjyj}, 
		#{fd8Bz}, 
		#{fd8Zrfssgzjgjg}, 
		#{fd8Srfssgzjgjg}, 
		#{fd8Zrgqbl}, 
		#{fd8Srgqbl}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="fd8Zrfmc != null and fd8Zrfmc != ''">
			and t.fd8_zrfmc = #{fd8Zrfmc}
		</if>
		<if test="fd8Zrlb != null and fd8Zrlb != ''">
			and t.fd8_zrlb = #{fd8Zrlb}
		</if>
		<if test="fd8Srgqsjjzcz != null">
			and t.fd8_srgqsjjzcz = #{fd8Srgqsjjzcz}
		</if>
		<if test="fd8Srgqpgjzcz != null">
			and t.fd8_srgqpgjzcz = #{fd8Srgqpgjzcz}
		</if>
		<if test="fd8Cjj != null">
			and t.fd8_cjj = #{fd8Cjj}
		</if>
		<if test="fd8Srfmc != null and fd8Srfmc != ''">
			and t.fd8_srfmc = #{fd8Srfmc}
		</if>
		<if test="fd8Srfxz != null and fd8Srfxz != ''">
			and t.fd8_srfxz = #{fd8Srfxz}
		</if>
		<if test="fd8Zrgqsjjzcz != null">
			and t.fd8_zrgqsjjzcz = #{fd8Zrgqsjjzcz}
		</if>
		<if test="fd8Zrgqpgjzcz != null">
			and t.fd8_zrgqpgjzcz = #{fd8Zrgqpgjzcz}
		</if>
		<if test="fd8Zjyj != null and fd8Zjyj != ''">
			and t.fd8_zjyj = #{fd8Zjyj}
		</if>
		<if test="fd8Bz != null and fd8Bz != ''">
			and t.fd8_bz = #{fd8Bz}
		</if>
		<if test="fd8Zrfssgzjgjg != null and fd8Zrfssgzjgjg != ''">
			and t.fd8_zrfssgzjgjg = #{fd8Zrfssgzjgjg}
		</if>
		<if test="fd8Srfssgzjgjg != null and fd8Srfssgzjgjg != ''">
			and t.fd8_srfssgzjgjg = #{fd8Srfssgzjgjg}
		</if>
		<if test="fd8Zrgqbl != null">
			and t.fd8_zrgqbl = #{fd8Zrgqbl}
		</if>
		<if test="fd8Srgqbl != null">
			and t.fd8_srgqbl = #{fd8Srgqbl}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_zrsrfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_zrsrfd set isDeleted = 'Y' where
		id in
		<foreach collection="zrsrfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_zrsrfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_zrsrfd  where
		id in
		<foreach collection="zrsrfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_zrsrfd  where id = #{id}
	</delete>
	
	<select id="selectZrsrfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_zrsrfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_zrsrfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="fd8Zrfmc != null">
				fd8_zrfmc=#{fd8Zrfmc},
			</if>
			<if test="fd8Zrlb != null">
				fd8_zrlb=#{fd8Zrlb},
			</if>
			<if test="fd8Srgqsjjzcz != null">
				fd8_srgqsjjzcz=#{fd8Srgqsjjzcz},
			</if>
			<if test="fd8Srgqpgjzcz != null">
				fd8_srgqpgjzcz=#{fd8Srgqpgjzcz},
			</if>
			<if test="fd8Cjj != null">
				fd8_cjj=#{fd8Cjj},
			</if>
			<if test="fd8Srfmc != null">
				fd8_srfmc=#{fd8Srfmc},
			</if>
			<if test="fd8Srfxz != null">
				fd8_srfxz=#{fd8Srfxz},
			</if>
			<if test="fd8Zrgqsjjzcz != null">
				fd8_zrgqsjjzcz=#{fd8Zrgqsjjzcz},
			</if>
			<if test="fd8Zrgqpgjzcz != null">
				fd8_zrgqpgjzcz=#{fd8Zrgqpgjzcz},
			</if>
			<if test="fd8Zjyj != null">
				fd8_zjyj=#{fd8Zjyj},
			</if>
			<if test="fd8Bz != null">
				fd8_bz=#{fd8Bz},
			</if>
			<if test="fd8Zrfssgzjgjg != null">
				fd8_zrfssgzjgjg=#{fd8Zrfssgzjgjg},
			</if>
			<if test="fd8Srfssgzjgjg != null">
				fd8_srfssgzjgjg=#{fd8Srfssgzjgjg},
			</if>
			<if test="fd8Zrgqbl != null">
				fd8_zrgqbl=#{fd8Zrgqbl},
			</if>
			<if test="fd8Srgqbl != null">
				fd8_srgqbl=#{fd8Srgqbl},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_zrsrfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			fd8_zrfmc=#{fd8Zrfmc},
			fd8_zrlb=#{fd8Zrlb},
			fd8_srgqsjjzcz=#{fd8Srgqsjjzcz},
			fd8_srgqpgjzcz=#{fd8Srgqpgjzcz},
			fd8_cjj=#{fd8Cjj},
			fd8_srfmc=#{fd8Srfmc},
			fd8_srfxz=#{fd8Srfxz},
			fd8_zrgqsjjzcz=#{fd8Zrgqsjjzcz},
			fd8_zrgqpgjzcz=#{fd8Zrgqpgjzcz},
			fd8_zjyj=#{fd8Zjyj},
			fd8_bz=#{fd8Bz},
			fd8_zrfssgzjgjg=#{fd8Zrfssgzjgjg},
			fd8_srfssgzjgjg=#{fd8Srfssgzjgjg},
			fd8_zrgqbl=#{fd8Zrgqbl},
			fd8_srgqbl=#{fd8Srgqbl},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_zrsrfd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalZrsrfds" parameterType="com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_zrsrfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryZrsrfdForList" parameterType="com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_zrsrfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_zrsrfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="fd8Zrfmc != null and fd8Zrfmc != ''">
				and t.fd8_zrfmc = #{fd8Zrfmc}
			</if>
			<if test="fd8Zrlb != null and fd8Zrlb != ''">
				and t.fd8_zrlb = #{fd8Zrlb}
			</if>
			<if test="fd8Srgqsjjzcz != null and fd8Srgqsjjzcz != ''">
				and t.fd8_srgqsjjzcz = #{fd8Srgqsjjzcz}
			</if>
			<if test="fd8Srgqpgjzcz != null and fd8Srgqpgjzcz != ''">
				and t.fd8_srgqpgjzcz = #{fd8Srgqpgjzcz}
			</if>
			<if test="fd8Cjj != null and fd8Cjj != ''">
				and t.fd8_cjj = #{fd8Cjj}
			</if>
			<if test="fd8Srfmc != null and fd8Srfmc != ''">
				and t.fd8_srfmc = #{fd8Srfmc}
			</if>
			<if test="fd8Srfxz != null and fd8Srfxz != ''">
				and t.fd8_srfxz = #{fd8Srfxz}
			</if>
			<if test="fd8Zrgqsjjzcz != null and fd8Zrgqsjjzcz != ''">
				and t.fd8_zrgqsjjzcz = #{fd8Zrgqsjjzcz}
			</if>
			<if test="fd8Zrgqpgjzcz != null and fd8Zrgqpgjzcz != ''">
				and t.fd8_zrgqpgjzcz = #{fd8Zrgqpgjzcz}
			</if>
			<if test="fd8Zjyj != null and fd8Zjyj != ''">
				and t.fd8_zjyj = #{fd8Zjyj}
			</if>
			<if test="fd8Bz != null and fd8Bz != ''">
				and t.fd8_bz = #{fd8Bz}
			</if>
			<if test="fd8Zrfssgzjgjg != null and fd8Zrfssgzjgjg != ''">
				and t.fd8_zrfssgzjgjg = #{fd8Zrfssgzjgjg}
			</if>
			<if test="fd8Srfssgzjgjg != null and fd8Srfssgzjgjg != ''">
				and t.fd8_srfssgzjgjg = #{fd8Srfssgzjgjg}
			</if>
			<if test="fd8Zrgqbl != null and fd8Zrgqbl != ''">
				and t.fd8_zrgqbl = #{fd8Zrgqbl}
			</if>
			<if test="fd8Srgqbl != null and fd8Srgqbl != ''">
				and t.fd8_srgqbl = #{fd8Srgqbl}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxId" resultMap="baseResultMapExt">
		select  <include refid="columnsAlias"/>,sd1.text fd8SrfxzStr,
		sd2.text fd8ZrlbStr,sd3.text fd8ZrfssgzjgjgStr,sd4.text fd8SrfssgzjgjgStr
		from cq_zrsrfd t
		left join sys_dictionary sd1 on sd1.val=t.FD8_SRFXZ and sd1.type_id=(select id from sys_dictionary where type_code='SRFXZ')
		left join sys_dictionary sd2 on sd2.val=t.FD8_ZRLB and sd2.type_id=(select id from sys_dictionary where type_code='ZRLB')
		left join sys_dictionary sd3 on sd3.val=t.FD8_ZRFSSGZJGJG and sd3.type_id=(select id from sys_dictionary where type_code='GZJGJGFD')
		left join sys_dictionary sd4 on sd4.val=t.FD8_SRFSSGZJGJG and sd4.type_id=(select id from sys_dictionary where type_code='GZJGJGFD')
		where t.JBXX_ID = #{id}
    </select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_zrsrfd where jbxx_id = #{jbxxId}
	</delete>
	<sql id="selectAllByJbxxIdVo">
		fd8_zrfmc, 
		fd8_zrlb, 
		fd8_srgqsjjzcz, 
		fd8_srgqpgjzcz, 
		fd8_cjj, 
		fd8_srfmc, 
		fd8_srfxz, 
		fd8_zrgqsjjzcz, 
		fd8_zrgqpgjzcz, 
		fd8_zjyj, 
		fd8_bz, 
		fd8_zrfssgzjgjg, 
		fd8_srfssgzjgjg, 
		fd8_zrgqbl, 
		fd8_srgqbl
	</sql>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			<include refid="selectAllByJbxxIdVo"/>
		FROM
			`cq_zrsrfd` zrsr

		WHERE
			JBXX_ID = #{jbxxId}
	</select>
</mapper>