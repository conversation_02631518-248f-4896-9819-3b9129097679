package com.zjhc.gzwcq.requestErroInfo.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfo;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoVo;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoParam;
import org.apache.ibatis.annotations.Param;

public interface IRequestErroInfoMapper {
	
	/*保存对象*/
	void insert(RequestErroInfo requestErroInfo);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(RequestErroInfo requestErroInfo);
	
	/**更新*/
	void update(RequestErroInfo requestErroInfo);
	
	/*分页查询对象*/
	List<RequestErroInfoVo> queryRequestErroInfoForList(RequestErroInfoParam requestErroInfoParam);
	
	/*数据总量查询*/
	long queryTotalRequestErroInfos(RequestErroInfoParam requestErroInfoParam);
	
	/*根据主键查询对象*/
	RequestErroInfoVo selectRequestErroInfoByPrimaryKey(RequestErroInfo requestErroInfo);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<RequestErroInfo> selectForList(RequestErroInfo requestErroInfo);
	
	/**
	 * 数据唯一性验证
	 * */
	List<RequestErroInfo> selectForUnique(RequestErroInfo requestErroInfo);
	
}