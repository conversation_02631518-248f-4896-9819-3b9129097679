<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.dwtzqkfd.mapper.IDwtzqkfdMapper">

	<resultMap type="com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="floatorder" property="floatorder"/>
		<result column="bdlx" property="bdlx"/>
		<result column="bdmc" property="bdmc"/>
		<result column="code" property="code"/>
		<result column="sshy" property="sshy"/>
		<result column="address" property="address"/>
		<result column="tze" property="tze"/>
		<result column="tzbl" property="tzbl"/>
		<result column="sfsjkz" property="sfsjkz"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo" extends="baseResultMap">
		<result column="bdlxStr" property="bdlxStr"/>
		<result column="sfsjkzStr" property="sfsjkzStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		floatorder, 
		bdlx, 
		bdmc, 
		code, 
		sshy, 
		address, 
		tze, 
		tzbl, 
		sfsjkz, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.floatorder, 
		t.bdlx, 
		t.bdmc, 
		t.code, 
		t.sshy, 
		t.address, 
		t.tze, 
		t.tzbl, 
		t.sfsjkz, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{floatorder}, 
		#{bdlx}, 
		#{bdmc}, 
		#{code}, 
		#{sshy}, 
		#{address}, 
		#{tze}, 
		#{tzbl}, 
		#{sfsjkz}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="bdlx != null and bdlx != ''">
			and t.bdlx = #{bdlx}
		</if>
		<if test="bdmc != null and bdmc != ''">
			and t.bdmc = #{bdmc}
		</if>
		<if test="code != null and code != ''">
			and t.code = #{code}
		</if>
		<if test="sshy != null and sshy != ''">
			and t.sshy = #{sshy}
		</if>
		<if test="address != null and address != ''">
			and t.address = #{address}
		</if>
		<if test="tze != null">
			and t.tze = #{tze}
		</if>
		<if test="tzbl != null">
			and t.tzbl = #{tzbl}
		</if>
		<if test="sfsjkz != null and sfsjkz != ''">
			and t.sfsjkz = #{sfsjkz}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_dwtzqkfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_dwtzqkfd set isDeleted = 'Y' where
		id in
		<foreach collection="dwtzqkfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_dwtzqkfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_dwtzqkfd  where
		id in
		<foreach collection="dwtzqkfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_dwtzqkfd  where id = #{id}
	</delete>
	
	<select id="selectDwtzqkfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_dwtzqkfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_dwtzqkfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="bdlx != null">
				bdlx=#{bdlx},
			</if>
			<if test="bdmc != null">
				bdmc=#{bdmc},
			</if>
			<if test="code != null">
				code=#{code},
			</if>
			<if test="sshy != null">
				sshy=#{sshy},
			</if>
			<if test="address != null">
				address=#{address},
			</if>
			<if test="tze != null">
				tze=#{tze},
			</if>
			<if test="tzbl != null">
				tzbl=#{tzbl},
			</if>
			<if test="sfsjkz != null">
				sfsjkz=#{sfsjkz},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_dwtzqkfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			floatorder=#{floatorder},
			bdlx=#{bdlx},
			bdmc=#{bdmc},
			code=#{code},
			sshy=#{sshy},
			address=#{address},
			tze=#{tze},
			tzbl=#{tzbl},
			sfsjkz=#{sfsjkz},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_dwtzqkfd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalDwtzqkfds" parameterType="com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_dwtzqkfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryDwtzqkfdForList" parameterType="com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_dwtzqkfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_dwtzqkfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="floatorder != null">
				and t.floatorder = #{floatorder}
			</if>
			<if test="bdlx != null and bdlx != ''">
				and t.bdlx = #{bdlx}
			</if>
			<if test="bdmc != null and bdmc != ''">
				and t.bdmc = #{bdmc}
			</if>
			<if test="code != null and code != ''">
				and t.code = #{code}
			</if>
			<if test="sshy != null and sshy != ''">
				and t.sshy = #{sshy}
			</if>
			<if test="address != null and address != ''">
				and t.address = #{address}
			</if>
			<if test="tze != null and tze != ''">
				and t.tze = #{tze}
			</if>
			<if test="tzbl != null and tzbl != ''">
				and t.tzbl = #{tzbl}
			</if>
			<if test="sfsjkz != null and sfsjkz != ''">
				and t.sfsjkz = #{sfsjkz}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<resultMap id="resultMapWithSshy" type="com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo" extends="baseResultMapExt">
		<collection property="sshyList" ofType="java.lang.String">
			<constructor>
				<arg column="sshyList"/>
			</constructor>
		</collection>
	</resultMap>
	<select id="selectByJbxxbId" parameterType="java.lang.String" resultMap="resultMapWithSshy">
		select
		<include refid="columnsAlias"/>,sd1.text bdlxStr,sd2.text sfsjkzStr,IF(b.JB_DATA_STATUS = 'new',sd3.text,sd4.text) sshyList
		from
		cq_dwtzqkfd t join cq_jbxxb b on b.id = t.JBXX_ID
		left join sys_dictionary sd1 on sd1.val=t.bdlx and sd1.type_id=(select id from sys_dictionary where type_code='BDLX')
		left join sys_dictionary sd2 on sd2.val=t.sfsjkz and sd2.type_id=(select id from sys_dictionary where type_code='YesNo')
		left join sys_dictionary sd3 on FIND_IN_SET(sd3.val,t.SSHY) and sd3.type_id=(select id from sys_dictionary where type_code='INDUSTRY_CLASSIFICATION_TREE')
		left join sys_dictionary sd4 on FIND_IN_SET(sd4.val,t.SSHY) and sd4.type_id=(select id from sys_dictionary where type_code='INDUSTRY_CLASSIFICATION_TREE_OLD')
		where t.jbxx_id = #{jbxxId}
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_dwtzqkfd where jbxx_id = #{jbxxId}
	</delete>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			bdlx,
			bdmc,
			code,
			sshy,
			address,
			tze,
			tzbl,
			sfsjkz
		FROM
			`cq_dwtzqkfd`
		WHERE
			JBXX_ID=#{jbxxId}
	</select>
</mapper>