package com.zjhc.gzwcq.monitorwarn.service.impl;

import java.math.BigDecimal;
import java.util.*;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.utils.StringUtil;
import com.boot.IAdmin.common.utils.UUIDGenerator;
import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.IAdmin.dict.service.api.IDictionaryService;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.service.impl.OrganizationService;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.mapper.IBusinessInfoMapper;
import com.zjhc.gzwcq.businessInfo.service.api.IBusinessInfoService;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdVo;
import com.zjhc.gzwcq.cgrfd.service.api.ICgrfdService;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.czfd.service.api.ICzfdService;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo;
import com.zjhc.gzwcq.hhqy.entity.HhqyVo;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import com.zjhc.gzwcq.jbxxb.service.api.IJbxxbService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.monitorwarn.mapper.IMonitorwarnMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import com.zjhc.gzwcq.monitorwarn.service.api.IMonitorwarnService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class MonitorwarnServiceImpl implements IMonitorwarnService {
	
	@Autowired
	private IMonitorwarnMapper monitorwarnMapper;
	@Autowired
	private IJbxxbService jbxxbService;
	@Autowired
	private OrganizationMapper organizationMapper;
	@Autowired
	private OrganizationService organizationService;
	@Autowired
	private DictCacheStrategy dictCacheStrategy;
	@Autowired
	private IDictionaryService dictionaryService;
	@Autowired
	private ICgrfdService cgrfdService;
	@Autowired
	private ICzfdService czfdService;
	@Autowired
	private IBusinessInfoService businessInfoService;
	@Autowired
	private IBusinessInfoMapper businessInfoMapper;

	private static final String QYLB = "QYLB";  //企业类别
	private static final String CZRLB = "CZRLB";  //企业类别
	private static final String ZZXS = "ZZXS"; //组织形式

	private static final String YJDL = "YJDL"; //预警大类
	private static final String BDLX = "YJ_BDLX"; //变动类型
	private static final String BDZT = "BDZT"; //变动状态
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Monitorwarn monitorwarn){
		monitorwarn.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		monitorwarn.setCreateTime(new Date());//创建时间
		monitorwarn.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		monitorwarn.setLastUpdateTime(new Date());//更新时间
		monitorwarnMapper.insert(monitorwarn);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		monitorwarnMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		monitorwarnMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Monitorwarn monitorwarn){
		monitorwarn.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		monitorwarn.setLastUpdateTime(new Date());//更新时间
		monitorwarnMapper.updateIgnoreNull(monitorwarn);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Monitorwarn monitorwarn){
		monitorwarn.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		monitorwarn.setLastUpdateTime(new Date());//更新时间
		monitorwarnMapper.update(monitorwarn);
	}
	
	public List<MonitorwarnVo> queryMonitorwarnByPage(MonitorwarnParam monitorwarnParam) {
		SysUser user = (SysUser)SpringSecurityUserTools.instance().getUser(null);
		monitorwarnParam.setUnitid(user.getOrganization_id());
      	//分页
      	PageHelper.startPage(monitorwarnParam.getPageNumber(),monitorwarnParam.getLimit(),false);
		List<MonitorwarnVo> vos = monitorwarnMapper.queryMonitorwarnForList(monitorwarnParam);
		for(MonitorwarnVo vo : vos){
			if(StringUtils.isNotEmpty(vo.getChangeCategory())){
				vo.setChangeCategoryStr(dictCacheStrategy.getTextByVal(YJDL,vo.getChangeCategory()));
			}
			if(StringUtils.isNotEmpty(vo.getChangeType())){
				vo.setChangeTypeStr(dictCacheStrategy.getTextByVal(BDLX,vo.getChangeType()));
			}
			if(StringUtils.isNotEmpty(vo.getChangeStatus())){
				vo.setChangeStatusStr(dictCacheStrategy.getTextByVal(BDZT,vo.getChangeStatus()));
			}
			//已读未读
			vo.setReadStatus(Constants.NO);
			if(StringUtils.isNotBlank(vo.getHaveRead())&&vo.getHaveRead().contains(user.getUser_id())){
				vo.setReadStatus(Constants.YES);
			}
		}
		return vos;
	}
	

	public Monitorwarn selectMonitorwarnByPrimaryKey(Monitorwarn Monitorwarn) {
		return monitorwarnMapper.selectMonitorwarnByPrimaryKey(Monitorwarn);
	}
	
	public long queryTotalMonitorwarns(MonitorwarnParam monitorwarnParam) {
		return monitorwarnMapper.queryTotalMonitorwarns(monitorwarnParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Monitorwarn> selectForList(Monitorwarn monitorwarn){
		return monitorwarnMapper.selectForList(monitorwarn);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Monitorwarn monitorwarn) {
		return monitorwarnMapper.selectForUnique(monitorwarn).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Monitorwarn monitorwarn) {
		if(StringUtils.isBlank(monitorwarn.getId())) {
			this.insert(monitorwarn);
		}else {
			this.updateIgnoreNull(monitorwarn);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Monitorwarn[] objs) {
		for(Monitorwarn monitorwarn : objs) {
			this.saveOne(monitorwarn);
		}
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
	public void monitorWarn(Jbxxb jbxxb){
		//查询已审核通过的最新的企业基本信息
		jbxxb.setId(null);
		JbxxbVo jbxxbVo = jbxxbService.loadRecentApprovedByUnitId(jbxxb);
		if(jbxxbVo != null){
			if(Constants.QYLX_HHQY.equals(jbxxbVo.getBusinessNature())) { //合伙企业不进行预警
				return;
			}
			//查询以本企业作为出资企业的企业列表
			List<SysOrganization> investedOrgList = organizationMapper.getInvestedOrgList(jbxxbVo.getJbZzjgdm());
			//查询当前组织所有下级组织
			List<SysOrganization> childOrgList = organizationMapper.selectAllChildOrgInfoList(jbxxbVo.getUnitid());
			//对比本企业四个字段是否有修改 1企业名称 2企业级次  3 组织形式  4企业类别
			if(!jbxxb.getJbQymc().equals(jbxxbVo.getJbQymc())
					|| !jbxxb.getJbQylb().equals(jbxxbVo.getJbQylb())){  //变动类型： 出资人信息(出资人企业名称或者出资人类别变动)
				//企业名称有变更，查找已本企业作为投资企业的所有企业，进行自动预警
				//自动预警
				//企业类别对应关系
				String jbqylb = "";
				switch (jbxxb.getJbQylb()){
					case "1":
						jbqylb = "2";   //国有出资人
						break;
					case "2":
						jbqylb = "3";
						break;
					case "3":
						jbqylb = "4";
						break;
					case "4":
						jbqylb = "5";  //其他
						break;
					default:
						jbqylb = "5";
						break;
				}
				String qylb = dictCacheStrategy.getTextByVal(CZRLB,jbqylb);
				String reason = "本单位出资人名称应变成“"+jbxxb.getJbQymc()+"”,出资人类别应变成“"+qylb+"”。\n" +
						"请点击下边的“自动预警处理”按钮自动完成该预警的处理，或在“变动产权登记”功能中对该单位进行变动登记，将相应出资人名称更改成“"+jbxxb.getJbQymc()+"”,出资人类别更改成“"+qylb+"”。\n" +
						"注：需将功能参数“是否显示办理按钮”设置为“显示”。";

				for(SysOrganization sysOrganization : investedOrgList){
					//先查询当前组织是否还有未处理的出资人信息预警，有的话修改预警，没有则新增
					Monitorwarn noOpMonitor = new Monitorwarn();
					noOpMonitor.setUnitid(sysOrganization.getOrganization_id());
					noOpMonitor.setChangeStatus(Constants.BDZT_0);
					noOpMonitor.setChangeType(Constants.BDLX_CZRXX);
					List<Monitorwarn> noOpMonitorList =  monitorwarnMapper.selectForList(noOpMonitor);
					if(CollectionUtils.isNotEmpty(noOpMonitorList)){
						noOpMonitor = noOpMonitorList.get(0);
					}
					if(StringUtils.isNotEmpty(noOpMonitor.getId())) { //有还未处理的出资人信息预警
						noOpMonitor.setFromUnitid(jbxxbVo.getUnitid()); //引起预警企业Id
						noOpMonitor.setChangeCzrCode(jbxxbVo.getJbZzjgdm()); //引起预警企业代码
						noOpMonitor.setChangeReason(reason);  //变动原因
						this.updateIgnoreNull(noOpMonitor);
					}else{
						//新增预警信息
						Monitorwarn monitorwarn = new Monitorwarn();
//						monitorwarn.setJbxxId();  //当发起流程后，再设置关联基本信息id
						monitorwarn.setUnitid(sysOrganization.getOrganization_id()); //当前预警企业Id
						monitorwarn.setFromUnitid(jbxxbVo.getUnitid()); //引起预警企业Id
						monitorwarn.setChangeCategory(Constants.YJDL_ZD); //预警大类  自动
						monitorwarn.setChangeType(Constants.BDLX_CZRXX); //预警类型  出资人信息
						monitorwarn.setChangeQyjcOld(sysOrganization.getBusiness_level()); //变动前企业级次
						monitorwarn.setChangeQyjcNew(sysOrganization.getBusiness_level()); //变动后企业级次
						monitorwarn.setChangeCzrCode(jbxxbVo.getJbZzjgdm()); //引起预警企业代码
						monitorwarn.setChangeStatus(Constants.BDZT_0); //变动状态
						monitorwarn.setChangeReason(reason);  //变动原因
						this.insert(monitorwarn);
					}
				}
			}
			if(!jbxxb.getJbQyjc().equals(jbxxbVo.getJbQyjc())){ //变动类型： 企业级次
				//企业级次有变更，查找本企业下的所有下属企业，进行自动预警
				//自动预警

				int changeQyjc = Integer.parseInt(jbxxb.getJbQyjc()) - Integer.parseInt(jbxxbVo.getJbQyjc());
				for(SysOrganization sysOrganization : childOrgList){
					if(StringUtils.isEmpty(sysOrganization.getBusiness_level())){
						continue;
					}
					String tmpQyjc = sysOrganization.getBusiness_level();
					//先查询当前组织是否还有未处理的企业级次预警，有的话修改预警，没有则新增
					Monitorwarn noOpMonitor = new Monitorwarn();
					noOpMonitor.setUnitid(sysOrganization.getOrganization_id());
					noOpMonitor.setChangeStatus(Constants.BDZT_0);
					noOpMonitor.setChangeType(Constants.BDLX_QYJC);
					List<Monitorwarn> noOpMonitorList =  monitorwarnMapper.selectForList(noOpMonitor);
					if(CollectionUtils.isNotEmpty(noOpMonitorList)){
						noOpMonitor = noOpMonitorList.get(0);
						tmpQyjc = noOpMonitor.getChangeQyjcNew();
					}
					String reason = "上级单位的企业级次为“"+jbxxb.getJbQyjc()+"级”。\n" +
							"本单位的级次应变动成“0"+(Integer.parseInt(tmpQyjc)+changeQyjc)+"级”。\n" +
							"请点击下边的“自动预警处理”按钮自动完成该预警的处理，或在“变动产权登记”功能中对该单位进行变动登记，将相应的级次更改成“0"
							+(Integer.parseInt(sysOrganization.getBusiness_level())+changeQyjc)+"级”。\n" +
							"注：需将功能参数“是否显示办理按钮”设置为“显示”。";
					if(StringUtils.isNotEmpty(noOpMonitor.getId())){ //有还未处理的企业级次预警
						noOpMonitor.setChangeReason(reason);  //变动原因
						noOpMonitor.setChangeQyjcNew("0"+String.valueOf((Integer.parseInt(tmpQyjc)+changeQyjc))); //变动后企业级次
						this.updateIgnoreNull(noOpMonitor);
					}else{
						//新增预警信息
						Monitorwarn monitorwarn = new Monitorwarn();
//					monitorwarn.setJbxxId();  //当发起流程后，再设置关联基本信息id
						monitorwarn.setUnitid(sysOrganization.getOrganization_id()); //当前预警企业Id
						monitorwarn.setFromUnitid(jbxxbVo.getUnitid()); //引起预警企业Id
						monitorwarn.setChangeCategory(Constants.YJDL_ZD); //预警大类  自动
						monitorwarn.setChangeType(Constants.BDLX_QYJC); //预警类型  企业级次
						monitorwarn.setChangeQyjcOld(sysOrganization.getBusiness_level()); //变动前企业级次
						monitorwarn.setChangeQyjcNew("0"+String.valueOf((Integer.parseInt(tmpQyjc)+changeQyjc))); //变动后企业级次
						monitorwarn.setChangeCzrCode(jbxxbVo.getJbZzjgdm()); //引起预警企业代码
						monitorwarn.setChangeStatus(Constants.BDZT_0); //变动状态
						monitorwarn.setChangeReason(reason);  //变动原因
						this.insert(monitorwarn);
					}
				}
			}
			if(!jbxxb.getJbQylb().equals(jbxxbVo.getJbQylb())){ //变动类型： 企业类别
				//企业类别有变更，查找本企业下的所有下属企业，进行半自动预警
				//半自动预警
				//todo 企业类别类型不匹配会爆null
				Dictionary dic = dictCacheStrategy.getByTypeCodeAndVal(QYLB,jbxxb.getJbQylb());

				String reason = "上级单位的企业类别为“"+dic.getText()+"”。\n" +
						"本单位的企业类别应变动成“"+dic.getText()+"”。\n" +
						"请点击下边的“半自动预警处理”按钮跳转到变动登记页面，按正常审核流程完成变动登记，或在“变动产权登记”功能中对该单位进行变动登记，将相应的企业类别更改成“"
						+dic.getText()+"”。\n" +
						"注：需将功能参数“是否显示办理按钮”设置为“显示”。";

				for(SysOrganization sysOrganization : childOrgList){
					//先查询当前组织是否还有未处理的企业类别预警，有的话修改预警，没有则新增
					Monitorwarn noOpMonitor = new Monitorwarn();
					noOpMonitor.setUnitid(sysOrganization.getOrganization_id());
					noOpMonitor.setChangeStatus(Constants.BDZT_0);
					noOpMonitor.setChangeType(Constants.BDLX_QYLB);
					List<Monitorwarn> noOpMonitorList =  monitorwarnMapper.selectForList(noOpMonitor);
					if(CollectionUtils.isNotEmpty(noOpMonitorList)){
						noOpMonitor = noOpMonitorList.get(0);
					}
					if(StringUtils.isNotEmpty(noOpMonitor.getId())) { //有还未处理的企业类别预警
						noOpMonitor.setFromUnitid(jbxxbVo.getUnitid()); //引起预警企业Id
						noOpMonitor.setChangeCzrCode(jbxxbVo.getJbZzjgdm()); //引起预警企业代码
						noOpMonitor.setChangeReason(reason);  //变动原因
						this.updateIgnoreNull(noOpMonitor);
					}else{
						//新增预警信息
						Monitorwarn monitorwarn = new Monitorwarn();
//						monitorwarn.setJbxxId();  //当发起流程后，再设置关联基本信息id
						monitorwarn.setUnitid(sysOrganization.getOrganization_id()); //当前预警企业Id
						monitorwarn.setFromUnitid(jbxxbVo.getUnitid()); //引起预警企业Id
						monitorwarn.setChangeCategory(Constants.YJDL_BZD); //预警大类  半自动
						monitorwarn.setChangeType(Constants.BDLX_QYLB); //预警类型  企业类别
						monitorwarn.setChangeQyjcOld(sysOrganization.getBusiness_level()); //变动前企业级次
						monitorwarn.setChangeQyjcNew(sysOrganization.getBusiness_level()); //变动后企业级次
						monitorwarn.setChangeCzrCode(jbxxbVo.getJbZzjgdm()); //引起预警企业代码
						monitorwarn.setChangeStatus(Constants.BDZT_0); //变动状态
						monitorwarn.setChangeReason(reason);  //变动原因
						this.insert(monitorwarn);
					}
				}
			}
			if(!jbxxb.getJbZzxs().equals(jbxxbVo.getJbZzxs())){ //变动类型： 组织形式
				//企业组织形式有变更，查找本企业下的所有下属企业，进行半自动预警
				//半自动预警
				Dictionary param = new Dictionary();
				param.setType_id(36L);
				List<DictionaryVo> dictionaryVoList = dictionaryService.selectForList(param);
				Optional<DictionaryVo> op = dictionaryVoList.stream().filter(d -> d.getVal().equals(jbxxb.getJbZzxs())).findFirst();
				Dictionary parent = new Dictionary();
				parent.setId(op.get().getParent());
				parent = dictionaryService.queryDictionaryById(parent);
				String reason = "上级单位的组织形式为“"+op.get().getText()+"”。\n" +
						"本单位的组织形式应变动成“"+parent.getText()+"”。\n" +
						"请点击下边的“半自动预警处理”按钮跳转到变动登记页面，按正常审核流程完成变动登记，或在“变动产权登记”功能中对该单位进行变动登记，将相应的组织形式更改成“"
						+parent.getText()+"”。\n" +
						"注：需将功能参数“是否显示办理按钮”设置为“显示”。";

				for(SysOrganization sysOrganization : childOrgList){
					//先查询当前组织是否还有未处理的组织形式预警，有的话修改预警，没有则新增
					Monitorwarn noOpMonitor = new Monitorwarn();
					noOpMonitor.setUnitid(sysOrganization.getOrganization_id());
					noOpMonitor.setChangeStatus(Constants.BDZT_0);
					noOpMonitor.setChangeType(Constants.BDLX_ZZXS);
					List<Monitorwarn> noOpMonitorList =  monitorwarnMapper.selectForList(noOpMonitor);
					if(CollectionUtils.isNotEmpty(noOpMonitorList)){
						noOpMonitor = noOpMonitorList.get(0);
					}
					if(StringUtils.isNotEmpty(noOpMonitor.getId())) { //有还未处理的组织形式预警
						noOpMonitor.setFromUnitid(jbxxbVo.getUnitid()); //引起预警企业Id
						noOpMonitor.setChangeCzrCode(jbxxbVo.getJbZzjgdm()); //引起预警企业代码
						noOpMonitor.setChangeReason(reason);  //变动原因
						this.updateIgnoreNull(noOpMonitor);
					}else{
						//新增预警信息
						Monitorwarn monitorwarn = new Monitorwarn();
//						monitorwarn.setJbxxId();  //当发起流程后，再设置关联基本信息id
						monitorwarn.setUnitid(sysOrganization.getOrganization_id()); //当前预警企业Id
						monitorwarn.setFromUnitid(jbxxbVo.getUnitid()); //引起预警企业Id
						monitorwarn.setChangeCategory(Constants.YJDL_BZD); //预警大类  半自动
						monitorwarn.setChangeType(Constants.BDLX_ZZXS); //预警类型  组织形式
						monitorwarn.setChangeQyjcOld(sysOrganization.getBusiness_level()); //变动前企业级次
						monitorwarn.setChangeQyjcNew(sysOrganization.getBusiness_level()); //变动后企业级次
						monitorwarn.setChangeCzrCode(jbxxbVo.getJbZzjgdm()); //引起预警企业代码
						monitorwarn.setChangeStatus(Constants.BDZT_0); //变动状态
						monitorwarn.setChangeReason(reason);  //变动原因
						this.insert(monitorwarn);
					}
				}

			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String submitWarnReview(Monitorwarn monitorwarn){
		//查询预警数据
		monitorwarn = monitorwarnMapper.selectMonitorwarnByPrimaryKey(monitorwarn);
		//判断是否已处理
		if(Constants.BDZT_1.equals(monitorwarn.getChangeStatus())){
			return "该预警已处理，不能重复处理！";
		}
		//查找预警企业审核过的最新数据
		//查找基本信息表关联的表(需要复制的关联表)
		//国有资本+境内：CQ_JBXXB+ CQ_CZFD
		//国有资本+境外：CQ_JBXXB+CQ_CGRFD+CQ_CZFD
		//合伙企业：CQ_JBXXB+CQ_HHRQKFD+CQ_DWTZQKFD+CQ_HHQY  (合伙人企业没有预警)
		Jbxxb jbxxb = new Jbxxb();
		jbxxb.setUnitid(monitorwarn.getUnitid());
		JbxxbVo jbxxbVo = jbxxbService.loadRecentApprovedByUnitId(jbxxb); //里面包含了所有关联表信息

		//复制一份新的基本信息表数据(将id置空，然后新增)
		jbxxbVo.setId(null);
		//修改自动预警相应需要修改的字段
		if(Constants.BDLX_CZRXX.equals(monitorwarn.getChangeType())){ //出资人信息变动
			jbxxbVo.setJbBdcqdjqx("20500");  //变动情形-单纯原出资人名称或性质改变
			//查询变动出资人最新信息
			Jbxxb jbxxbCzr = new Jbxxb();
			jbxxbCzr.setUnitid(monitorwarn.getFromUnitid());
			JbxxbVo jbxxbCzrVo = jbxxbService.loadRecentApprovedByUnitId(jbxxbCzr);
			 //修改出资人名称类别
			for(CzfdVo czfd : jbxxbVo.getFdTableData()){
				if(StringUtils.isNotBlank(czfd.getFdCzrzzjgdm()) && StringUtils.isNotBlank(jbxxbCzrVo.getJbZzjgdm())
						&& czfd.getFdCzrzzjgdm().equals(jbxxbCzrVo.getJbZzjgdm())){
					czfd.setFdCzrmc(jbxxbCzrVo.getJbQymc());
					//企业类别对应关系
					String jbqylb = "";
					switch (jbxxbCzrVo.getJbQylb()){
						case "1":
							jbqylb = "2";
							break;
						case "2":
							jbqylb = "3";
							break;
						case "3":
							jbqylb = "4";
							break;
						case "4":
							jbqylb = "5";
							break;
						default:
							jbqylb = "5";
							break;
					}
					czfd.setFdCzrlb(jbqylb);
				}
			}
		}else if(Constants.BDLX_QYJC.equals(monitorwarn.getChangeType())){ //企业级次变动
			jbxxbVo.setJbBdcqdjqx("20400");  //变动情形-因上级出资人级次变动导致的级次变动
			//修改企业级次
			jbxxbVo.setJbQyjc(monitorwarn.getChangeQyjcNew());
		}

		//复制基本信息表及其关联表
		jbxxbVo.setJbSfybgs(Constants.JBXX_SFYBGS_UN); //预警的变动固定不涉及 工商
		jbxxbVo.setDatatime(UUIDGenerator.generate());
		jbxxbService.insert(jbxxbVo);
		//应当用unitId来判断是否有在途单
		if (businessInfoMapper.findNoAllApprovalBus(jbxxbVo.getId(),jbxxbVo.getUnitid()) > 0){
			jbxxbService.deleteByPrimaryKey(jbxxbVo.getId());
			return "企业还存在未审核通过的在途单，不可以再次上报!";
		}
		if(Constants.JNJW_1.equals(jbxxbVo.getJbJnjw())){ //境内
			for(CzfdVo czfdVo : jbxxbVo.getFdTableData()){
				czfdVo.setId(null);
				czfdVo.setJbxxId(jbxxbVo.getId());
				czfdService.insert(czfdVo);
			}
		}else if(Constants.JNJW_2.equals(jbxxbVo.getJbJnjw())){ //境外
			for(CzfdVo czfdVo : jbxxbVo.getFdTableData()){
				czfdVo.setId(null);
				czfdVo.setJbxxId(jbxxbVo.getId());
				czfdService.insert(czfdVo);
			}
			for(CgrfdVo cgrfdVo : jbxxbVo.getFd9TableData()){
				cgrfdVo.setId(null);
				cgrfdVo.setJbxxId(jbxxbVo.getId());
				cgrfdService.insert(cgrfdVo);
			}
		}

		//生成登记表数据
		BusinessInfo businessInfo = new BusinessInfoParam();
		businessInfo.setJbxxId(jbxxbVo.getId());
		businessInfo.setUnitid(jbxxbVo.getUnitid());
		businessInfo.setDatatime(jbxxbVo.getDatatime());
		businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_1); //默认未待上报
		//登记类型 1：变动登记 0：占有登记 3：注销登记
		businessInfo.setRgType(Constants.RG_TYPE_BD);  //固定变动
		businessInfo.setRgDate(new Date());
		businessInfo.setAfCurrentNode("待上报");
		businessInfo.setFloatorder(BigDecimal.ONE);
		businessInfo.setRgSolutionid("1");
		businessInfo.setIfMonitorwarn(true);
		businessInfo.setMonitorwarnId(monitorwarn.getId());
		businessInfo.setRgTimemark(new Date());
		businessInfoService.saveOne(businessInfo);

		//发起审核流程
		BusinessInfoParam param = new BusinessInfoParam();
		param.setSubmitType("2"); //预警得审核类型默认为变动
		param.setJbxxId(jbxxbVo.getId());
		String ex = businessInfoService.submitReviewStep2(param);
		if(StringUtils.isNotEmpty(ex)){
			businessInfoService.deleteByPrimaryKey(businessInfo.getId());
			jbxxbService.deleteByPrimaryKey(jbxxbVo.getId());
			return ex;
		}

		//将预警数据改为已处理
		monitorwarn.setChangeStatus(Constants.BDZT_1);
		monitorwarn.setJbxxId(jbxxbVo.getId());  //复制得基本信息表id
		this.updateIgnoreNull(monitorwarn);
		return "";
	}

	@Override
	public BootstrapTableModel<MonitorwarnVo> selectWarnReviewList(MonitorwarnParam param){
		BootstrapTableModel<MonitorwarnVo> re = new BootstrapTableModel<>();
		SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		//查询当前登录人组织被托管的组织列表
		List<String> auditHostingList = organizationService.dataPermissionsForApproval();
		param.setAuditHostingList(auditHostingList);
		SysOrganization org = organizationService.getOrgByOrgId(user.getOrganization_id());
		//当企业为1级或者行政机构时才需要设置初审和复审
//		if(StringUtils.equals("01", org.getBusiness_level()) || StringUtils.equals("1", org.getBusinesstype())) {
//			if(StringUtils.isNotEmpty(user.getAudit_level())){
//				param.setAuditLevel(user.getAudit_level());
//			}
//		}
		if(StringUtils.isNotEmpty(user.getAudit_level())){
			param.setAuditLevel(user.getAudit_level());
		}
		List<MonitorwarnVo> vos = monitorwarnMapper.selectWarnReviewList(param);
		re.setTotal(vos.size());
		int start = (param.getPageNumber()-1)*param.getLimit();
		int end = param.getPageNumber()*param.getLimit() > vos.size() ? vos.size() : param.getPageNumber()*param.getLimit();
		List<MonitorwarnVo> rows = vos.subList(start,end);
		for(MonitorwarnVo vo : rows){
			if(StringUtils.isNotEmpty(vo.getChangeCategory())){
				vo.setChangeCategoryStr(dictCacheStrategy.getTextByVal(YJDL,vo.getChangeCategory()));
			}
			if(StringUtils.isNotEmpty(vo.getChangeType())){
				vo.setChangeTypeStr(dictCacheStrategy.getTextByVal(BDLX,vo.getChangeType()));
			}
			if(StringUtils.isNotEmpty(vo.getChangeStatus())){
				vo.setChangeStatusStr(dictCacheStrategy.getTextByVal(BDZT,vo.getChangeStatus()));
			}
		}
		re.setRows(rows);
		return re;
	}

	/**
	 * 预警读消息
	 *
	 * @param monitorwarn
	 */
	@Override
	public void readMessage(Monitorwarn monitorwarn) {
		String haveRead = monitorwarn.getHaveRead();
		String user_id = ((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id();
		if (StringUtils.isBlank(haveRead)){
			monitorwarn.setHaveRead(user_id);
		} else if (!haveRead.contains(user_id)) {
			String[] split = haveRead.split(",");
			List<String> list = Arrays.asList(split);
			List<String> target = new ArrayList<String>();
			target.addAll(list);
			target.add(user_id);
			String join = String.join(",", target);
			monitorwarn.setHaveRead(join);
		}
		Monitorwarn monitorwarn1 = new Monitorwarn();
		monitorwarn1.setHaveRead(monitorwarn.getHaveRead());
		monitorwarn1.setId(monitorwarn.getId());
		monitorwarnMapper.updateIgnoreNull(monitorwarn1);
	}
}
