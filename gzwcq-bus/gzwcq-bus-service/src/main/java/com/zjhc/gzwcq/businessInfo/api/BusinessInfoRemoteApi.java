package com.zjhc.gzwcq.businessInfo.api;

import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.businessInfo.client.BusinessInfoFeignClient;
import com.zjhc.gzwcq.businessInfo.service.api.IBusinessInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/businessInfoRemoteApi")
@Api(value="businessInfo接口文档",tags="登记状态表/流程实例表")
public class BusinessInfoRemoteApi implements BusinessInfoFeignClient {
  
  	@Autowired
	private IBusinessInfoService businessInfoService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param BusinessInfo
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<BusinessInfoVo> queryByPage(@RequestBody BusinessInfoParam businessInfoParam) {
        BootstrapTableModel<BusinessInfoVo> model = new BootstrapTableModel<BusinessInfoVo>();
		model.setRows(businessInfoService.queryBusinessInfoByPage(businessInfoParam));
		model.setTotal(businessInfoService.queryTotalBusinessInfos(businessInfoParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody BusinessInfo businessInfo){
    	businessInfoService.insert(businessInfo);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	businessInfoService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	businessInfoService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody BusinessInfo businessInfo){
    	businessInfoService.updateIgnoreNull(businessInfo);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody BusinessInfo businessInfo){
    	businessInfoService.update(businessInfo);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public BusinessInfo selectBusinessInfoByPrimaryKey(@RequestBody BusinessInfo businessInfo){
  		return businessInfoService.selectBusinessInfoByPrimaryKey(businessInfo);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<BusinessInfo> selectForList(@RequestBody BusinessInfo businessInfo){
    	return businessInfoService.selectForList(businessInfo);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody BusinessInfo businessInfo){
    	return businessInfoService.validateUniqueParam(businessInfo);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody BusinessInfo businessInfo){
    	businessInfoService.saveOne(businessInfo);
    }

	@Override
	public void multipleSaveAndEdit(BusinessInfo[] objs) {
		businessInfoService.multipleSaveAndEdit(objs);
	};
	
	/**
	* 获取企业等级历史记录列表
	*/
	@ApiOperation(value="获取企业等级历史记录列表")
	public List<BusinessInfoVo> loadHistoryList(@RequestBody BusinessInfoParam param){
    	return businessInfoService.loadHistoryList(param);
    };

	/**
	 * 提交审核
	 * 需要携带等级类型字段(rgType;// 登记类型 1：变动登记 0：占有登记 3：注销登记)
	 */
	@ApiOperation(value="提交审核")
	public String submitReview(@RequestBody FormVo formVo){
		return businessInfoService.submitReview(formVo);
	};

	/**
	 * 审核
	 */
	@ApiOperation(value="审核")
	public void review(@RequestBody BusinessInfoParam param){
		businessInfoService.review(param);
	}
	@ApiOperation(value="回退审核")
	@Override
	public ResponseEnvelope recallReview(@RequestBody BusinessInfoParam param) {
		return businessInfoService.recallReview(param);
	}
	@ApiOperation(value="查询审核状态")
	@Override
	public ResponseEnvelope selectRecallReview(@RequestBody BusinessInfoParam param) {
		return businessInfoService.selectRecallReview(param);
	}

	;
	/**
	 * 审核轨迹列表查询
	 */
	@ApiOperation(value="审核轨迹列表查询")
	public List<AuditflowHistoryVo> reviewHistoryList(@RequestBody BusinessInfoParam param){
		return businessInfoService.reviewHistoryList(param);
	}
	/**
	 * 待办（待审核/退回）列表查询
	 */
	@ApiOperation(value="待办（待审核/退回）列表查询")
	public List<BusinessInfoVo> todoList(@RequestBody BusinessInfoParam param){
		return businessInfoService.todoList(param);
	}

	/**
	 * 待办（待审核/退回）列表查询(分页+筛选条件)
	 */
	@ApiOperation(value="待办（待审核/退回）列表查询(分页+筛选条件)")
	public BootstrapTableModel<BusinessInfoVo> todoOrReturnList(@RequestBody BusinessInfoParam param) {
		return businessInfoService.todoOrReturnList(param);
	}

	@ApiOperation(value="当前企业所有审核通过的历史列表和当前这条记录")
	public BootstrapTableModel<BusinessInfoVo> allApprovedAndNowList(@RequestBody BusinessInfo param) {
		return businessInfoService.allApprovedAndNowList(param);
	}

	@Override
	@ApiOperation(value="工商登记资料补录上报")
	public String suppleSubmitReview(@RequestBody Jbxxb jbxxb) {
		return businessInfoService.suppleSubmitReview(jbxxb);
	}

	/**
	 * 业务审核 已审核 待列表查询(分页+筛选条件)
	 *
	 * @param param
	 */
	@Override
	@ApiOperation(value="业务审核 已审核")
	public BootstrapTableModel<BusinessInfoVo> getPassData(@RequestBody BusinessInfoParam param) {
		return businessInfoService.getPassData(param);
	}
}