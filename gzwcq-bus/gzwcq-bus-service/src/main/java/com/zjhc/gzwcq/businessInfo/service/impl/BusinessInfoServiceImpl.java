package com.zjhc.gzwcq.businessInfo.service.impl;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;
import com.boot.iAdmin.access.service.impl.OrganizationService;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.boot.iAdmin.redis.service.RedisSerialNumberService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.auditflowHistory.mapper.IAuditflowHistoryMapper;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.zjhc.gzwcq.businessInfo.mapper.IBusinessInfoMapper;
import com.zjhc.gzwcq.businessInfo.service.api.IBusinessInfoService;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import com.zjhc.gzwcq.jbxxb.mapper.IJbxxbMapper;
import com.zjhc.gzwcq.jbxxb.service.api.IJbxxbService;
import com.zjhc.gzwcq.monitorwarn.service.api.IMonitorwarnService;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import com.zjhc.gzwcq.ywzbb.service.api.IYwzbbService;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo;
import com.zjhc.gzwcq.ywzbb2.service.api.IYwzbb2Service;
import com.zjhc.gzwcq.ywzbb2.service.impl.Ywzbb2ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Service
public class BusinessInfoServiceImpl implements IBusinessInfoService {
	
	@Autowired
	private IBusinessInfoMapper businessInfoMapper;

	@Autowired
	private OrganizationMapper organizationMapper;

	@Autowired
	private OrganizationService organizationService;

	@Autowired
	private IAuditflowHistoryMapper auditflowHistoryMapper;

	@Autowired
	private IJbxxbService jbxxbService;
	@Autowired
	private IMonitorwarnService monitorwarnService;
	@Autowired
	private SysUserInfoMapper userInfoMapper;
	@Autowired
	private RedisSerialNumberService redisSerialNumberService;
	@Autowired
	private IYwzbb2Service ywzbb2Service;
	@Autowired
	private IYwzbbService ywzbbService;
	@Autowired
	private DictCacheStrategy dictCacheStrategy;
	public static final String APPROVAL_STATUS = "APPROVAL_STATUS";//登记类型

	@Autowired
	private TmpBusinessInfoForAddRecordServiceImpl tmpBusinessInfoForAddRecordServiceImpl;
	@Resource
	private IJbxxbMapper jbxxbMapper;
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(BusinessInfo businessInfo){
		businessInfo.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		businessInfo.setCreateTime(new Date());//创建时间
		businessInfo.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		businessInfo.setLastUpdateTime(new Date());//更新时间
		businessInfoMapper.insert(businessInfo);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		businessInfoMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		businessInfoMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(BusinessInfo businessInfo){
		businessInfo.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		businessInfo.setLastUpdateTime(new Date());//更新时间
		businessInfoMapper.updateIgnoreNull(businessInfo);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(BusinessInfo businessInfo){
		businessInfo.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		businessInfo.setLastUpdateTime(new Date());//更新时间
		businessInfoMapper.update(businessInfo);
	}
	
	public List<BusinessInfoVo> queryBusinessInfoByPage(BusinessInfoParam businessInfoParam) {
      	//分页
      	PageHelper.startPage(businessInfoParam.getPageNumber(),businessInfoParam.getLimit(),false);
		return businessInfoMapper.queryBusinessInfoForList(businessInfoParam);
	}
	

	public BusinessInfo selectBusinessInfoByPrimaryKey(BusinessInfo BusinessInfo) {
		return businessInfoMapper.selectBusinessInfoByPrimaryKey(BusinessInfo);
	}
	
	public long queryTotalBusinessInfos(BusinessInfoParam businessInfoParam) {
		return businessInfoMapper.queryTotalBusinessInfos(businessInfoParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<BusinessInfo> selectForList(BusinessInfo businessInfo){
		return businessInfoMapper.selectForList(businessInfo);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(BusinessInfo businessInfo) {
		return businessInfoMapper.selectForUnique(businessInfo).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(BusinessInfo businessInfo) {
		if(StringUtils.isBlank(businessInfo.getId())) {
			this.insert(businessInfo);
		}else {
			this.updateIgnoreNull(businessInfo);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(BusinessInfo[] objs) {
		for(BusinessInfo businessInfo : objs) {
			this.saveOne(businessInfo);
		}
	}

	@Override
	public List<BusinessInfoVo> loadHistoryList(BusinessInfoParam param){
		return businessInfoMapper.queryBusinessInfoForList(param);
	}

	/**
	 * 1.先进行基本信息保存
	 * 2.保存成功后再使用jbxxId进行上报
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public String submitReview(FormVo formVo) {
		if (formVo != null){
			//判断上报人的组织及以下是否包含被上报的组织(或其出资人,占有情形)
			SysUser submitUser = (SysUser)SpringSecurityUserTools.instance().getUser(null);

			//组织及以下+审批托管
			Set<String> childrenAndAuditIds = organizationService.getChildrenAndAuditIdsById(submitUser.getOrganization_id());
			//占有登记需要使用其出资人id判断,此时它还没有自己的id
			if (Constants.RG_TYPE_ZY.equals(formVo.getRgType())){
				if (!childrenAndAuditIds.contains(formVo.getJbCzrzzjgid()) && !childrenAndAuditIds.contains(formVo.getHhCzqyId())){
					return "占有登记必须由其主要出资企业(及其上级或托管组织)发起!";
				}
			}else {
				//变动+注销登记,使用自己的id
				if (!childrenAndAuditIds.contains(formVo.getUnitid())){
					return "变动/注销登记必须由登记企业(及其上级或托管组织)发起!";
				}
			}
			String jbxxbId;
			//step1:保存
			if (Constants.QYLX_HHQY.equals(formVo.getBusinessNature())){
				//合伙企业
				jbxxbId = jbxxbService.savePartnership(formVo);
			}else {
				//国有企业
				jbxxbId = jbxxbService.saveGovernmentCapital(formVo);
			}
			//step2:上报
			BusinessInfoParam param = new BusinessInfoParam();
			param.setJbxxId(jbxxbId);
			return this.submitReviewStep2(param);
		}
		return "parameter can not be null!";
	}

	/**
	 * 工商登记资料补录上报
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public String suppleSubmitReview(Jbxxb jbxxb) {
		if (jbxxb != null && StringUtils.isNotBlank(jbxxb.getId())){
			//step1:保存基本信息
			jbxxbService.updateIgnoreNull(jbxxb);
			String id = jbxxb.getId();
			Ywzbb2Vo ywzbb2Vo = ywzbb2Service.selectByJbxxId(id);
			YwzbbVo ywzbbVo = ywzbbService.selectByJbxxId(id);
			String zlQyzc = jbxxb.getZlQyzc();
			if (Objects.nonNull(ywzbb2Vo) && StringUtils.isNotBlank(zlQyzc)){
				Ywzbb2Vo dto = new Ywzbb2Vo();
				dto.setZlQyzc(zlQyzc);
				dto.setId(ywzbb2Vo.getId());
				ywzbb2Service.updateIgnoreNull(dto);
			}
			if (Objects.nonNull(ywzbbVo) && (StringUtils.isNotBlank(jbxxb.getZlQyzcLy()) || StringUtils.isNotBlank(jbxxb.getZlQyzcYw()))){
				YwzbbVo dto = new YwzbbVo();
				dto.setId(ywzbbVo.getId());
				dto.setZlQyzcLy(jbxxb.getZlQyzcLy());
				dto.setZlQyzcYw(jbxxb.getZlQyzcYw());
				ywzbbService.updateIgnoreNull(dto);
			}
			//step2:上报
			BusinessInfoParam param = new BusinessInfoParam();
			param.setJbxxId(jbxxb.getId());
			return this.submitReviewStep2(param);
		}
		return "参数异常!";
	}

	/**
	 * 根据已保存的jbxxId进行上报
	 * 为submitReview方法的第二步骤
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
	public String submitReviewStep2(BusinessInfoParam param){
		//首先页面进行保存，再把基本信息id给我
		Jbxxb jbxxb = new Jbxxb();
		jbxxb.setId(param.getJbxxId());
		jbxxb = jbxxbService.selectJbxxbByPrimaryKey(jbxxb);

		//判断企业是否还有(待上报，待审核，退回等)未审核通过的登记数据，一个企业同时只能有一个审批流程
		//企业编码为空时，不校验  (排除当前流程的jbxxId,一个企业可以对应多个流程实例,但一个基本信息只能对应一个流程实例)
		/*if(StringUtils.isNotEmpty(jbxxb.getJbZzjgdm()) && businessInfoMapper.findNoAllApprovalBus(jbxxb.getId(),jbxxb.getJbZzjgdm()) > 0){
			throw new RuntimeException("企业还存在未审核通过的在途单，不可以再次上报!");
		}*/
		//应当用unitId来判断是否有在途单
		if (businessInfoMapper.findNoAllApprovalBus(jbxxb.getId(),jbxxb.getUnitid()) > 0){
			return "企业还存在未审核通过的在途单，不可以再次上报!";
		}

		Date nowDate = new Date();
		SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		//查询操作人组织信息
		SysOrganization org = organizationMapper.getOrgByOrgId(user.getOrganization_id());
		//查询企业上级组织信息
//		SysOrganization parentUnit = null;

		//查询关联的登记表数据
		BusinessInfo businessInfo = new BusinessInfo();
		businessInfo.setJbxxId(jbxxb.getId());
		List<BusinessInfo> infoList = businessInfoMapper.selectForList(businessInfo);
		if(CollectionUtils.isEmpty(infoList)){
			throw new RuntimeException("操作失败.");
		}
		businessInfo = infoList.get(0); //基本信息表数据与登记表数据一对一关系

//		if(Constants.RG_TYPE_ZY.equals(businessInfo.getRgType())){ //占有
//			//主要出资企业组织机构id，上级组织 (取上级组织作为初始审批组织节点)
//			parentUnit = organizationMapper.getOrgByOrgId(jbxxb.getJbCzrzzjgid());
//		}else{  //变动或注销
//			//从自己组织开始
//			parentUnit = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
//		}
		//查询是否已有审核记录，判断是否是第一次上报
		AuditflowHistory auditflowHistory = new AuditflowHistory();
		auditflowHistory.setBusinessInfoId(businessInfo.getId());
		List<AuditflowHistoryVo> historyList = auditflowHistoryMapper.selectForList(auditflowHistory);
		if(CollectionUtils.isNotEmpty(historyList)){ //退回
			//不是第一次上报，需要校验是否退回或者补录
			if(!businessInfo.getRgUnitstate().equals(Constants.REVIEW_STATUS_4)
					&&
					!businessInfo.getRgUnitstate().equals(Constants.REVIEW_STATUS_9)
			){
				//当前不是首次上报，且不是退回或者补录状态，数据问题，无法再次上报
				throw new RuntimeException("操作失败,当前数据状态无法上报");
			}
			//修改操作时间
			AuditflowHistory lastHistory = historyList.get(historyList.size()-1);
			lastHistory.setAfProcessdate(nowDate);
			lastHistory.setAfProcesstype(Constants.APPROVAL_STATUS_1);
			lastHistory.setAfProcessuserid(user.getUser_id());
			lastHistory.setAfProcessunitid(user.getOrganization_id());
			lastHistory.setAfProcessusername(user.getUsername());
			lastHistory.setAfProcessusertitle(user.getName());
			lastHistory.setAfProcessunitcode(org.getOrganization_code());
			lastHistory.setAfProcessunittitle(org.getOrganization_name());
			lastHistory.setAfProcessgroup(Constants.REVIEW_FQZ);//上报
			auditflowHistoryMapper.updateIgnoreNull(lastHistory);

			//登记表状态由退回改为审核中
			businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_2);
		 }else{ //首次上报
			//新增上报审批流程记录
			AuditflowHistory firstHistory = new AuditflowHistory();
			firstHistory.setBusinessInfoId(businessInfo.getId());
			firstHistory.setAfUnitid(businessInfo.getUnitid());
			firstHistory.setAfDatatime(businessInfo.getDatatime());
			firstHistory.setAfProcesstype(Constants.APPROVAL_STATUS_1);
			firstHistory.setAfProcessuserid(user.getUser_id());
			firstHistory.setAfProcessunitid(user.getOrganization_id());
			firstHistory.setAfProcessdate(nowDate);
			firstHistory.setAfProcessusername(user.getUsername());
			firstHistory.setAfProcessusertitle(user.getName());
			firstHistory.setAfProcessunitcode(org.getOrganization_code());
			firstHistory.setAfProcessunittitle(org.getOrganization_name());
			firstHistory.setCreateUser(user.getUser_id());
			firstHistory.setCreateTime(nowDate);
			firstHistory.setAfProcessgroup(Constants.REVIEW_FQZ);//上报
			auditflowHistoryMapper.insert(firstHistory);
			businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_2);
		}
		//如果父级企业已经是1级企业，则先自己进行初审跟复审
		//不是一级的,且不是行政机构的，往上找上级组织审批节点
		SysOrganization nextReviewNode = org;
		if(StringUtils.isBlank(nextReviewNode.getBusiness_level()) && !"1".equals(nextReviewNode.getBusinesstype())) {
			//查找下一个审批节点,父级节点可能为虚拟节点(组织企业级次为空)，则需要继续往上找组织
			nextReviewNode = organizationService.findNextReviewNode(nextReviewNode);
		}
		/*if(StringUtils.isNotEmpty(parentUnit.getBusiness_level()) && !parentUnit.getBusiness_level().equals("01")
				&& !"1".equals(parentUnit.getBusinesstype())) {
			//查找下一个审批节点,父级节点可能为虚拟节点(组织企业级次为空)，则需要继续往上找组织
			nextReviewNode = organizationService.findNextReviewNode(parentUnit);
		}*/
//		Integer level = null;
//		if(nextReviewNode != null && StringUtils.isNotEmpty(nextReviewNode.getBusiness_level())){
//			level = Integer.parseInt(nextReviewNode.getBusiness_level());
//		}
		//判断当前提交人是否为审核企业的初审人员
		//如果当前提交人员不是审核企业的初审人员，则从初审开始走流程
		//如果当前提交人员是审核企业的初审人员，则从复审开始走流程
		String afProcessgroup = getStartLevel(nextReviewNode, user);

		AuditflowHistory secondHistory = new AuditflowHistory();
		secondHistory.setBusinessInfoId(businessInfo.getId());
		secondHistory.setAfUnitid(businessInfo.getUnitid());
		secondHistory.setAfDatatime(businessInfo.getDatatime());
		secondHistory.setAfProcesstype(Constants.APPROVAL_STATUS_2);
		secondHistory.setAfProcessunitid(nextReviewNode.getOrganization_id()); //审核机构id
		secondHistory.setCreateUser(user.getUser_id());
		secondHistory.setCreateTime(new Date(nowDate.getTime()+1000));//+1秒
		secondHistory.setAfProcessunitcode(nextReviewNode.getOrganization_code());
		secondHistory.setAfProcessunittitle(nextReviewNode.getOrganization_name());
		secondHistory.setAfProcessgroup(afProcessgroup);
		auditflowHistoryMapper.insert(secondHistory);

		//更新登记表数据
		businessInfo.setAfCurrentNode("待【"+nextReviewNode.getOrganization_name()+"企业】" + afProcessgroup);
		businessInfo.setAfCurrentAuditLevel(afProcessgroup);
		businessInfo.setAfCurrentunitid(nextReviewNode.getOrganization_id());

		//审批企业是1级企业，则先自己进行初审跟复审
		/*if("01".equals(nextReviewNode.getBusiness_level())){
			businessInfo.setAfCurrentAuditLevel(Constants.REVIEW_FIRST_TRIAL);
		}*/
		businessInfoMapper.updateIgnoreNull(businessInfo);
		//修改基本信息状态为审核中
		jbxxb.setJbShzt(Integer.parseInt(Constants.JBXX_REVIEW_STATUS_1));
		if(Constants.RG_TYPE_BD.equals(param.getRgType()) || Constants.RG_TYPE_ZX.equals(param.getRgType())){
			jbxxb.setJbQyslzt(Constants.GSDJYBLZT); //补录后修改为产权工商均已设立登记
		}
		jbxxbService.updateIgnoreNull(jbxxb);
		return "";
	}

	/**
	 * 传入审批组织和上报人，判断先初审还是复审
	 * @param nextReviewNode
	 * @param user
	 * @return
	 */
	private String getStartLevel(SysOrganization nextReviewNode,SysUser user){
		//判断当前提交人是否为审核企业的初审人员
		//如果当前提交人员不是审核企业的初审人员，则从初审开始走流程
		//如果当前提交人员是审核企业的初审人员，则从复审开始走流程
		String afProcessgroup = Constants.REVIEW_FIRST_TRIAL;  //默认从初审开始走
		if(nextReviewNode != null && user != null){
			SysUser userGroup = new SysUser();
			userGroup.setOrganization_id(nextReviewNode.getOrganization_id());
			userGroup.setIsdeleted(Constants.NO_DELETED);
			userGroup.setAudit_level(Constants.REVIEW_FIRST_TRIAL_1); //初审人员
			List<SysUser> userGroupList = userInfoMapper.selectForList(userGroup);
			if(CollectionUtils.isNotEmpty(userGroupList) && user != null){
				Optional<SysUser> op =userGroupList.stream().filter(u -> u.getUser_id().equals(user.getUser_id()))
						.findFirst();
				if(op.isPresent()){
					afProcessgroup = Constants.REVIEW_SECEND_TRIAL;
				}
			}
		}
		return afProcessgroup;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void review(BusinessInfoParam param){
		//支持批量审批
		String[] jbxxbIds = param.getJbxxId().split(",");
		for(String jbxxbId : jbxxbIds){
			//查询登记表信息
			BusinessInfo businessInfo = new BusinessInfo();
			businessInfo.setJbxxId(jbxxbId);
			List<BusinessInfo> infoList = businessInfoMapper.selectForList(businessInfo);
			if(CollectionUtils.isEmpty(infoList)){
				continue;
			}
			businessInfo = infoList.get(0);

			Date nowDate = new Date();
			SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			//查询基本信息
			Jbxxb jbxxb = new JbxxbVo();
			jbxxb.setId(jbxxbId);
			//todo jbxxb中的企业类别 可能为藏数据或者配置的不对
			jbxxb = jbxxbService.selectJbxxbByPrimaryKey(jbxxb);

			//国务院国资委新增字段补录 审核流程 - 只需一个人审核即可
			if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())
					&& Constants.GWY_GZW_ZDBL_QX.equals(jbxxb.getJbBdcqdjqx())){
				tmpBusinessInfoForAddRecordServiceImpl.review(param, businessInfo, jbxxb, user);
				continue;
			}

			//查询审核人组织信息
			SysOrganization userOrg = organizationMapper.getOrgByOrgId(user.getOrganization_id());
			//查找当前审核节点
			SysOrganization currOrg = organizationMapper.getOrgByOrgId(businessInfo.getAfCurrentunitid());
			//查询变动企业组织信息
			SysOrganization unit = organizationMapper.getOrgByOrgId(businessInfo.getUnitid());
			if(null == unit){
				//占有企业取查基本信息，带过来
				unit = new SysOrganization();
				unit.setOrganization_id(jbxxb.getUnitid()); //组织id
				unit.setOrganization_code(jbxxb.getJbZzjgdm()); //组织机构代码
				unit.setOrganization_name(jbxxb.getJbQymc()); //组织名称
				unit.setBusiness_nature(jbxxb.getBusinessNature()); //企业性质(2合伙企业，其他为国有)
			}
			//查询上报人企业信息

			//查询审核历史列表
			AuditflowHistory auditflowHistory = new AuditflowHistory();
			auditflowHistory.setBusinessInfoId(businessInfo.getId());
			List<AuditflowHistoryVo> historyList = auditflowHistoryMapper.selectForList(auditflowHistory);
			AuditflowHistory lastHistory = historyList.get(historyList.size()-1);
			//查询上报企业
			auditflowHistory.setAfProcesstype(Constants.APPROVAL_STATUS_1);
			List<AuditflowHistoryVo> submitAuditFlowHistoryList = auditflowHistoryMapper.selectForList(auditflowHistory);
			AuditflowHistoryVo submitAuditFlowHistory = submitAuditFlowHistoryList.get(0);
			SysOrganization submitOrg = organizationMapper.getOrgByOrgId(submitAuditFlowHistory.getAfProcessunitid());
			//上报人
			SysUserVo submitUser = userInfoMapper.getUserInfoByUserId(submitAuditFlowHistory.getAfProcessuserid());
			String auditLevel = null;//审核层级中文(存到business_info表)
			//补充审核节点信息
			lastHistory.setAfProcesstype(param.getApprovalResult());
			lastHistory.setAfProcessuserid(user.getUser_id());
			lastHistory.setAfProcessunitid(userOrg.getOrganization_id());
			lastHistory.setAfProcessdate(nowDate);
			lastHistory.setAfProcesscomment(param.getAfProcesscomment());
			lastHistory.setAfProcessusername(user.getUsername());
			lastHistory.setAfProcessusertitle(user.getName());
			lastHistory.setAfProcessunitcode(userOrg.getOrganization_code());
			lastHistory.setAfProcessunittitle(userOrg.getOrganization_name());
			lastHistory.setLastUpdateTime(nowDate);
			lastHistory.setLastUpdateUser(user.getUser_id());
			auditflowHistoryMapper.updateIgnoreNull(lastHistory);

			String reviewTrial = "";
			AuditflowHistory nextHistory = new AuditflowHistory();
			SysOrganization nextReviewNode = null;
			String approvalStr = "";
			String APPROVAL_STATUS = Constants.APPROVAL_STATUS_2, //审批历史表下一个审核数据状态默认待审核，退回时改为待上报
					REVIEW_STATUS=Constants.REVIEW_STATUS_2; //登记表状态每次审核默认审核中，当退回才改为退回
			//默认没到工商补录阶段 用印懒加载审批人与审批流
			boolean DJ_STATUS = businessInfo.getDjStatus();
			if(param.getApprovalResult().equals(Constants.APPROVAL_STATUS_3)){ //审核通过
				businessInfo.setRgTimemark(nowDate); //里程牌时间快照
				//查找下一节点,
				//当前已经是国资委审核，并且为复审,则整个审核流程结束
				if(StringUtils.isNotEmpty(currOrg.getBusinesstype()) && currOrg.getBusinesstype().equals("1") || this.ifFlowIsEnd(currOrg,businessInfo)){
					if(Constants.REVIEW_SECEND_TRIAL.equals(businessInfo.getAfCurrentAuditLevel())){ //当前为复审，结束
						//判断占有跟变动是否已办工商选的否，则需要补录，选的是则直接审批结束
						if(
//								!"3".equals(businessInfo.getRgType()) &&
								Constants.JBXX_SFYBGS_N.equals(jbxxb.getJbSfybgs())){
							APPROVAL_STATUS = Constants.APPROVAL_STATUS_9; //下一条审批历史状态为待工商登记资料补录
							REVIEW_STATUS = Constants.REVIEW_STATUS_9; //登记表状态改为待工商登记资料补录
							nextReviewNode = submitOrg; //下一节点就是上报企业组织
							approvalStr = "待工商登记资料补录";
							DJ_STATUS = true;
							//基本信息表状态改为待工商登记资料补录
							this.updateJbxxbStatus(jbxxbId,Integer.parseInt(Constants.JBXX_REVIEW_STATUS_9));
//							jbxxb.setJbShzt(Integer.parseInt(Constants.JBXX_REVIEW_STATUS_9));
//							jbxxbService.update(jbxxb);
						}else{
							//自动预警判断,只有变动才需要
							if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())){
								Jbxxb monitorJbxxb = new Jbxxb();
								BeanUtils.copyProperties(jbxxb,monitorJbxxb);
								monitorwarnService.monitorWarn(monitorJbxxb);
							}
							String sixCode = ""; //六位区域码
							if(Constants.RG_TYPE_ZY.equals(businessInfo.getRgType())){ //占有审核后
								//审核通过后，企业上组织树
								//主要出资企业组织机构id，上级组织 (取上级组织作为初始审批组织节点)
								SysOrganization parentOrg = organizationMapper.getOrgByOrgId(jbxxb.getJbCzrzzjgid());
								sixCode = parentOrg.getSsgzjgjg();
								SysOrganization newOrg = new SysOrganization();
								newOrg.setOrganization_id(jbxxb.getUnitid());
								newOrg.setOrganization_name(jbxxb.getJbQymc());
								newOrg.setOrganization_desc(jbxxb.getJbQymc());
								newOrg.setOrganization_code(parentOrg.getSsgzjgjg() + "_" + jbxxb.getJbZzjgdm());
								newOrg.setParent_id(parentOrg.getOrganization_id());
								newOrg.setParent_code(parentOrg.getOrganization_code());
								newOrg.setParents(parentOrg.getParents()+","+jbxxb.getUnitid());
								newOrg.setCreate_time(new Date());
								newOrg.setCreate_user(user.getUser_id());
								newOrg.setCreate_unit(submitOrg.getOrganization_id());
								newOrg.setSyncode(jbxxb.getJbZzjgdm());
								newOrg.setSsgzjgjg(parentOrg.getSsgzjgjg());
								newOrg.setBusiness_level(jbxxb.getJbQyjc());
								newOrg.setIsdeleted(Constants.NO_DELETED);
								newOrg.setBusiness_nature(jbxxb.getBusinessNature());
								//生成组织同级排序
								Double priority = organizationMapper.getLastPriority(parentOrg.getOrganization_id());
								newOrg.setPriority(String.valueOf((priority == null ? 0d : priority)+1));
								organizationMapper.insertOrg(newOrg);
								Jbxxb jbxxb1 = new Jbxxb(jbxxbId);
								jbxxb1.setJbQyslzt("产权工商均已设立登记");
								jbxxbService.updateIgnoreNull(jbxxb1);
							}else if(Constants.RG_TYPE_ZX.equals(businessInfo.getRgType())){ //注销审核后，将组织从组织树删除
								SysOrganization thisOrg = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
								sixCode = thisOrg.getSsgzjgjg();
								SysOrganization organization = new SysOrganization();
								organization.setOrganization_id(jbxxb.getUnitid());
								organizationMapper.deleteOrganization(organization);
							}else if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())){ //变动审核后，判断父级组织是否改变
								//修改企业信息
								SysOrganization rootorg = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
								sixCode = rootorg.getSsgzjgjg();
								rootorg.setOrganization_name(jbxxb.getJbQymc());
								rootorg.setOrganization_desc(jbxxb.getJbQymc());
								rootorg.setBusiness_level(jbxxb.getJbQyjc());
								rootorg.setOrganization_code(sixCode + "_" + jbxxb.getJbZzjgdm());
								rootorg.setSyncode(jbxxb.getJbZzjgdm());
								organizationMapper.updateIgnoreNull(rootorg);

								reviewPassWhenUpdate(jbxxb);
							}
							businessInfo.setRegisterCode(redisSerialNumberService.getSerialNumberByForFixedLength(sixCode,null,5)); //登记编号
							businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_3);
							businessInfo.setAfCurrentNode("审核完成");
							businessInfo.setAfCurrentunitid("");  //当前审核节点组织id置空
							businessInfo.setAfCurrentAuditLevel(""); //当前审核层级置空
							updateIgnoreNull(businessInfo);
							//基本信息表状态改为审核通过
							this.updateJbxxbStatus(jbxxbId, Integer.parseInt(Constants.JBXX_REVIEW_STATUS_2));
//							jbxxbService.update(jbxxb);
							continue;
						}
					}else { //继续国资委复审
						reviewTrial = Constants.REVIEW_SECEND_TRIAL;
						nextReviewNode = currOrg;
						approvalStr = "待【"+nextReviewNode.getOrganization_name()+"企业】" + Constants.REVIEW_SECEND_TRIAL;
						businessInfo.setAfCurrentAuditLevel(Constants.REVIEW_SECEND_TRIAL);
					}
				}
				//如果已经判断为补录状态，则不需要再判断了
//				if(REVIEW_STATUS.equals(Constants.REVIEW_STATUS_9)){
					// 当前已经是1级企业节点，初审时，下一节点为复审,
					// 当前已经是1级企业节点，复审时，下一节点为国资委,
					// 当前不是1级节点，找下一节点
					else if(StringUtils.isNotEmpty(currOrg.getBusiness_level()) && currOrg.getBusiness_level().equals("01")){
						if(businessInfo.getAfCurrentAuditLevel().equals(Constants.REVIEW_FIRST_TRIAL)){ //初审->复审
							reviewTrial = Constants.REVIEW_SECEND_TRIAL;
							nextReviewNode = currOrg;
							approvalStr = "待【"+nextReviewNode.getOrganization_name()+"企业】" + Constants.REVIEW_SECEND_TRIAL;
							businessInfo.setAfCurrentAuditLevel(Constants.REVIEW_SECEND_TRIAL);
						}else if(StringUtils.isEmpty(unit.getBusiness_nature()) || unit.getBusiness_nature().equals(Constants.QYLX_GYQY)){  //复审->国资委, 合伙企业则不需要国资委审核
							nextReviewNode = organizationService.findNextReviewNode(currOrg); //找父级组织，即国资委(可能不对，1级组织父级不一定就是国资委)
							reviewTrial = Constants.REVIEW_FIRST_TRIAL;
							businessInfo.setAfCurrentAuditLevel(Constants.REVIEW_FIRST_TRIAL);
							approvalStr = "待【"+nextReviewNode.getOrganization_name()+"企业】" + Constants.REVIEW_FIRST_TRIAL;
						}else{ //合伙企业则不需要国资委审核
							//判断占有跟变动是否已办工商选的否，则需要补录，选的是则直接审批结束
							if(
//									!"3".equals(businessInfo.getRgType()) &&
									Constants.JBXX_SFYBGS_N.equals(jbxxb.getJbSfybgs())){
								APPROVAL_STATUS = Constants.APPROVAL_STATUS_9; //下一条审批历史状态为待工商登记资料补录
								REVIEW_STATUS = Constants.REVIEW_STATUS_9; //登记表状态改为待工商登记资料补录
								nextReviewNode = submitOrg; //下一节点就是上报企业组织
								approvalStr = "待工商登记资料补录";
								reviewTrial = Constants.REVIEW_FQZ;//上报
								DJ_STATUS = true;
								//基本信息表状态改为待工商登记资料补录
//								jbxxb.setJbShzt(Integer.parseInt(Constants.JBXX_REVIEW_STATUS_9));
//								jbxxbService.update(jbxxb);
								this.updateJbxxbStatus(jbxxbId,Integer.parseInt(Constants.JBXX_REVIEW_STATUS_9));
							}else{
								//自动预警判断,只有变动才需要
								if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())){
									Jbxxb monitorJbxxb = new Jbxxb();
									BeanUtils.copyProperties(jbxxb,monitorJbxxb);
									monitorwarnService.monitorWarn(monitorJbxxb);
								}
								String sixCode = ""; //六位区域码
								if(Constants.RG_TYPE_ZY.equals(businessInfo.getRgType())) { //占有审核后
									//审核通过后，企业上组织树
									//主要出资企业组织机构id，上级组织 (取上级组织作为初始审批组织节点)
									SysOrganization parentOrg = organizationMapper.getOrgByOrgId(jbxxb.getJbCzrzzjgid());
									sixCode = parentOrg.getSsgzjgjg();
									SysOrganization newOrg = new SysOrganization();
									newOrg.setOrganization_id(jbxxb.getUnitid());
									newOrg.setOrganization_name(jbxxb.getJbQymc());
									newOrg.setOrganization_desc(jbxxb.getJbQymc());
									newOrg.setOrganization_code(parentOrg.getSsgzjgjg() + "_" + jbxxb.getJbZzjgdm());
									newOrg.setParent_id(parentOrg.getOrganization_id());
									newOrg.setParent_code(parentOrg.getOrganization_code());
									newOrg.setParents(parentOrg.getParents() + "," + jbxxb.getUnitid());
									newOrg.setCreate_time(new Date());
									newOrg.setCreate_user(user.getUser_id());
									newOrg.setCreate_unit(submitOrg.getOrganization_id());
									newOrg.setSyncode(jbxxb.getJbZzjgdm());
									newOrg.setSsgzjgjg(parentOrg.getSsgzjgjg());
									newOrg.setBusiness_level(jbxxb.getJbQyjc());
									newOrg.setBusiness_nature(jbxxb.getBusinessNature());
									//生成组织同级排序
									Double priority = organizationMapper.getLastPriority(parentOrg.getOrganization_id());
									priority = priority == null? 0:priority;
									newOrg.setPriority(String.valueOf(priority+1));
									newOrg.setIsdeleted(Constants.NO_DELETED);
									organizationMapper.insertOrg(newOrg);
								}else if(Constants.RG_TYPE_ZX.equals(businessInfo.getRgType())){ //注销审核后，将组织从组织树删除
									SysOrganization thisOrg = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
									sixCode = thisOrg.getSsgzjgjg();
									SysOrganization organization = new SysOrganization();
									organization.setOrganization_id(jbxxb.getUnitid());
									organizationMapper.deleteOrganization(organization);
								}else if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())){ //变动审核后，判断父级组织是否改变
									//修改企业信息
									SysOrganization rootorg = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
									sixCode = rootorg.getSsgzjgjg();
									rootorg.setOrganization_name(jbxxb.getJbQymc());
									rootorg.setOrganization_desc(jbxxb.getJbQymc());
									rootorg.setBusiness_level(jbxxb.getJbQyjc());
									rootorg.setOrganization_code(sixCode + "_" + jbxxb.getJbZzjgdm());
									rootorg.setSyncode(jbxxb.getJbZzjgdm());
									organizationMapper.updateIgnoreNull(rootorg);

									reviewPassWhenUpdate(jbxxb);
								}
								businessInfo.setRegisterCode(redisSerialNumberService.getSerialNumberByForFixedLength(sixCode,null,5)); //登记编号
								businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_3);
								businessInfo.setAfCurrentNode("审核完成");
								businessInfo.setAfCurrentunitid("");  //当前审核节点组织id置空
								businessInfo.setAfCurrentAuditLevel(""); //当前审核层级置空
								businessInfo.setDjStatus(DJ_STATUS);
								updateIgnoreNull(businessInfo);

								//基本信息表状态改为审核通过
//								jbxxb.setJbShzt(Integer.parseInt(Constants.JBXX_REVIEW_STATUS_2));
//								jbxxbService.update(jbxxb);
								this.updateJbxxbStatus(jbxxbId, Integer.parseInt(Constants.JBXX_REVIEW_STATUS_2));
								continue;
							}
						}
					}else{ //当前审核节点不是1级组织，继续往上找, 初审到复审，复审到下级初审

						if(businessInfo.getAfCurrentAuditLevel().equals(Constants.REVIEW_FIRST_TRIAL)){ //初审->复审
							reviewTrial = Constants.REVIEW_SECEND_TRIAL;
							nextReviewNode = currOrg;
							approvalStr = "待【"+nextReviewNode.getOrganization_name()+"企业】" + Constants.REVIEW_SECEND_TRIAL;
							businessInfo.setAfCurrentAuditLevel(Constants.REVIEW_SECEND_TRIAL);
						}else
//						if(StringUtils.isEmpty(unit.getBusiness_nature()) || unit.getBusiness_nature().equals(Constants.QYLX_GYQY))
							{  //复审->国资委, 合伙企业则不需要国资委审核
							nextReviewNode = organizationService.findNextReviewNode(currOrg); //找父级组织
							reviewTrial = Constants.REVIEW_FIRST_TRIAL;
							businessInfo.setAfCurrentAuditLevel(Constants.REVIEW_FIRST_TRIAL);
							approvalStr = "待【"+nextReviewNode.getOrganization_name()+"企业】" + Constants.REVIEW_FIRST_TRIAL;
						}
					}
//				}

			}else if(param.getApprovalResult().equals(Constants.APPROVAL_STATUS_4)){ //审核退回
				//判断当前提交人是否为审核企业的初审人员
				//如果当前提交人员不是审核企业的初审人员，则从初审开始走流程
				//如果当前提交人员是审核企业的初审人员，则从复审开始走流程
				//初始审批企业节点
				AuditflowHistory startReviewHistory = historyList.get(1);
				SysOrganization startReviewOrg = organizationMapper.getOrgByOrgId(startReviewHistory.getAfProcessunitid());
				String afProcessgroup = getStartLevel(startReviewOrg, submitUser);

				APPROVAL_STATUS = Constants.APPROVAL_STATUS_5; //下一条审批历史状态为待上报
				REVIEW_STATUS = Constants.REVIEW_STATUS_4; //登记表状态改为退回
				reviewTrial = Constants.REVIEW_FQZ;//上报
				nextReviewNode = submitOrg; //下一节点就是操作上报企业组织
				approvalStr = "待上报";
				//将上报的审核层级置空
				businessInfo.setAfCurrentAuditLevel(null);
				//指定发起人id
				nextHistory.setAfProcessuserid(submitUser.getUser_id());
				//基本信息表改为流程未发起
				/*jbxxb.setJbShzt(Integer.parseInt(Constants.JBXX_REVIEW_STATUS_3));
				jbxxbService.update(jbxxb);*/
			}else{
				throw new RuntimeException("审核结果为非法值，请确认！");
			}

			nextHistory.setBusinessInfoId(businessInfo.getId());
			nextHistory.setAfUnitid(businessInfo.getUnitid());
			nextHistory.setAfDatatime(businessInfo.getDatatime());
			nextHistory.setAfProcesstype(APPROVAL_STATUS);
			nextHistory.setAfProcessunitid(nextReviewNode.getOrganization_id());
			nextHistory.setAfProcessunitcode(nextReviewNode.getOrganization_code());
			nextHistory.setAfProcessunittitle(nextReviewNode.getOrganization_name());
			nextHistory.setCreateUser(user.getUser_id());
			nextHistory.setCreateTime(nowDate);
			nextHistory.setAfProcessgroup(StringUtils.isNotEmpty(reviewTrial) ? reviewTrial : Constants.REVIEW_AUDITOR);
			auditflowHistoryMapper.insert(nextHistory);

			//更新登记表状态
			businessInfo.setAfCurrentunitid(nextReviewNode.getOrganization_id());
			businessInfo.setRgUnitstate(REVIEW_STATUS);
			businessInfo.setAfCurrentNode(approvalStr);
//			businessInfo.setAfCurrentAuditLevel(auditLevel);
			businessInfo.setDjStatus(DJ_STATUS);
			this.updateIgnoreNull(businessInfo);
		}
	}

	/**
	 * 判断当前审批应不应该结束
	 * @param currOrg
	 * @return
	 */
	private boolean ifFlowIsEnd(SysOrganization currOrg,BusinessInfo businessInfo){
		boolean djStatus = businessInfo.getDjStatus();
		SysOrganization nextReviewNode = organizationService.findNextReviewNode(currOrg);
		if (StringUtils.equals(nextReviewNode.getBusinesstype(),"1") && djStatus){
			return true;
		}else {
			return false;
		}
	}
	@Override
	public ResponseEnvelope recallReview(BusinessInfoParam param) {
		SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
		String user_id = user.getUser_id();
		ResponseEnvelope vo = new ResponseEnvelope();
		String jbxxId = param.getJbxxId();
		Jbxxb jbxxb = new Jbxxb(jbxxId);
		JbxxbVo jbxxbVo = jbxxbService.selectJbxxbByPrimaryKey(jbxxb);
		BusinessInfo businessInfo = businessInfoMapper.selectBusinessInfoByPrimaryKeyByJbxxbId(jbxxId);
		String rgUnitstate = businessInfo.getRgUnitstate();
		Integer jbShzt = jbxxbVo.getJbShzt();
		if (Objects.nonNull(jbShzt) && jbShzt ==4){
			vo.setMessage("当前表单审核已通过无法撤回");
			vo.setSuccess(false);
			return vo;
		}
		if (StringUtils.isNotBlank(rgUnitstate) && !StringUtils.equals("9",rgUnitstate)){
			vo.setMessage("当前表单审核不属于工商登记 无法撤回");
			vo.setSuccess(false);
			return vo;
		}
		if (StringUtils.isNotBlank(user_id) && !StringUtils.equals(businessInfo.getCreateUser(),user_id)){
			vo.setMessage("你不是流程发起人 无法撤回");
			vo.setSuccess(false);
			return vo;
		}
		this.deleteByJbxxId(jbxxId);
//		设置为待上报
		jbxxb.setJbShzt(8);
		jbxxbService.updateIgnoreNull(jbxxb);
		businessInfo.setJbxxId(jbxxb.getId());
		businessInfo.setUnitid(jbxxb.getUnitid());
		businessInfo.setDatatime(String.valueOf(System.currentTimeMillis()));
		businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_1); //默认未待上报
		//登记类型 1：变动登记 0：占有登记 3：注销登记
		businessInfo.setRgDate(new Date());
		businessInfo.setAfCurrentNode("待上报");
		businessInfo.setFloatorder(BigDecimal.ONE);
		businessInfo.setRgSolutionid("1");
		businessInfo.setRgTimemark(new Date());
		businessInfo.setCreateTime(new Date());
		businessInfo.setLastUpdateTime(new Date());
		businessInfo.setDjStatus(false);
		this.saveOne(businessInfo);
		return vo;
	}

	@Override
	public ResponseEnvelope selectRecallReview(BusinessInfoParam param) {
		SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
		String user_id = user.getUser_id();
		ResponseEnvelope vo = new ResponseEnvelope();
		String jbxxId = param.getJbxxId();
		Jbxxb jbxxb = new Jbxxb(jbxxId);
		JbxxbVo jbxxbVo = jbxxbMapper.selectJbxxbByPrimaryKey(jbxxb);
		BusinessInfo businessInfo = businessInfoMapper.selectBusinessInfoByPrimaryKeyByJbxxbId(jbxxId);
		if (Objects.isNull(jbxxbVo) || Objects.isNull(businessInfo)){
			return vo;
		}
		String rgUnitstate = businessInfo.getRgUnitstate();
		Integer jbShzt = jbxxbVo.getJbShzt();
		if (Objects.nonNull(jbShzt) && jbShzt ==4){
			vo.setMessage("当前表单审核已通过无法撤回");
			vo.setState(false);
			return vo;
		}
		if (StringUtils.isNotBlank(rgUnitstate) && !StringUtils.equals("9",rgUnitstate)){
			vo.setMessage("当前表单审核不属于工商登记 无法撤回");
			vo.setState(false);
			return vo;
		}
		if (StringUtils.isNotBlank(user_id) && !StringUtils.equals(businessInfo.getCreateUser(),user_id)){
			vo.setMessage("你不是流程发起人 无法撤回");
			vo.setState(false);
			return vo;
		}
		return vo;
	}

	//更新审核状态
	private void updateJbxxbStatus(String jbxxbId, int shzt) {
		Jbxxb jb = new Jbxxb();
		jb.setId(jbxxbId);
		jb.setJbShzt(shzt);
		jbxxbService.updateIgnoreNull(jb);
	}

	/**
	 * 变动审核通过后业务
	 */
	private void reviewPassWhenUpdate(Jbxxb jbxxb){
		//获取上版本的基本信息
		Jbxxb jbxxbNew = new Jbxxb();
		jbxxbNew.setUnitid(jbxxb.getUnitid());
		JbxxbVo jbxxbVo = jbxxbService.loadRecentApprovedByUnitId(jbxxbNew);
		if(!Objects.equals(jbxxbVo.getJbCzrzzjgid(),jbxxb.getJbCzrzzjgid())){
			//查询新的父级组织
			SysOrganization orgParentNew = organizationMapper.getOrgByOrgId(jbxxb.getJbCzrzzjgid());
			//查询本组织
			SysOrganization rootorg = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
			rootorg.setParents(orgParentNew.getParents()+","+rootorg.getOrganization_id());
			rootorg.setParent_id(orgParentNew.getOrganization_id());
			organizationMapper.updateIgnoreNull(rootorg);

			reviewPassWhenUpdateDo(rootorg,orgParentNew);
		}
	}

	private void reviewPassWhenUpdateDo(SysOrganization rootorg,SysOrganization orgParentNew){
		rootorg.setParents(orgParentNew.getParents()+","+rootorg.getOrganization_id());
		organizationMapper.updateIgnoreNull(rootorg);
		//查询子组织列表
		SysOrganization param = new SysOrganization();
		param.setParent_id(rootorg.getOrganization_id());
		List<SysOrganization> childOrgList = organizationMapper.selectForList(param);
		if(CollectionUtils.isEmpty(childOrgList)){
			return;
		}
		for(SysOrganization child : childOrgList){
			reviewPassWhenUpdateDo(child,rootorg);
		}

	}

	@Override
	public List<AuditflowHistoryVo> reviewHistoryList(BusinessInfoParam param){
		List<AuditflowHistoryVo> re = new ArrayList<>();
		//查询基本信息
		Jbxxb jbxxb = new Jbxxb();
		jbxxb.setId(param.getJbxxId());
		jbxxb = jbxxbService.selectJbxxbByPrimaryKey(jbxxb);
		//未上报(填报中)的没有审核轨迹
		if (Constants.JBXX_REVIEW_STATUS_3.equals(String.valueOf(jbxxb.getJbShzt()))){
			return Collections.emptyList();
		}
		//查询审核历史表列表
		BusinessInfo businessInfo = new BusinessInfo();
		businessInfo.setJbxxId(param.getJbxxId());
		List<BusinessInfo> infoList = businessInfoMapper.selectForList(businessInfo);
		businessInfo = infoList.get(0);
		AuditflowHistory auditflowHistory = new AuditflowHistory();
		auditflowHistory.setBusinessInfoId(businessInfo.getId());
		List<AuditflowHistoryVo> historyList = auditflowHistoryMapper.selectForList(auditflowHistory);

		List<AuditflowHistoryVo> parentOrgList = new ArrayList<>(); //审核组织列表
		boolean djStatus = businessInfo.getDjStatus();
		//审核通过的不进行拼接
		//国务院国资委新增字段补录 审核流程 - 只需一个人审核即可
		if(!Constants.JBXX_REVIEW_STATUS_2.equals(String.valueOf(jbxxb.getJbShzt())) ||
				(!Constants.RG_TYPE_BD.equals(businessInfo.getRgType())
				|| !Constants.GWY_GZW_ZDBL_QX.equals(jbxxb.getJbBdcqdjqx()))){

			//查询从变动企业开始的整个流程
			SysOrganization unit = organizationMapper.getOrgByOrgId(businessInfo.getUnitid()); //变动企业组织信息
			if(null == unit){
				//占有企业取查基本信息，带过来
				unit = new SysOrganization();
				unit.setOrganization_id(jbxxb.getUnitid()); //组织id
				unit.setOrganization_code(jbxxb.getJbZzjgdm()); //组织机构代码
				unit.setOrganization_name(jbxxb.getJbQymc()); //组织名称
				unit.setBusiness_nature(jbxxb.getBusinessNature()); //企业性质(2合伙企业，其他为国有)
			}
			SysOrganization startUnit = null;  //流程开始组织
			if(Constants.RG_TYPE_ZY.equals(businessInfo.getRgType())){ //占有
				//主要出资企业组织机构id，上级组织 (取上级组织作为初始审批组织节点)
				startUnit = organizationMapper.getOrgByOrgId(jbxxb.getJbCzrzzjgid());
			}else{  //变动或注销
				//从自己组织开始
				startUnit = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
			}
			//企业 占有/变动/注销 数据创建人企业
			SysUser startUser = userInfoMapper.getUserInfoByUserId(jbxxb.getCreateUser());
			SysOrganization startUserUnit;
			//历史数据创建人有可能为空
			if (startUser != null){
				startUserUnit = organizationMapper.getOrgByOrgId(startUser.getOrganization_id());
			}else {
				startUserUnit = new SysOrganization("");
				startUserUnit.setOrganization_code("");
				startUserUnit.setOrganization_name("");
				startUserUnit.setParents("");
			}

			String[] parents = null;

			//判空
			if(startUserUnit != null && StringUtils.isNotBlank(startUserUnit.getParents())){
				parents = startUserUnit.getParents().split(",");
			}


			AuditflowHistoryVo firstHis = new AuditflowHistoryVo();
			firstHis.setBusinessInfoId(businessInfo.getId());
			firstHis.setAfUnitid(businessInfo.getUnitid());
			firstHis.setAfDatatime(businessInfo.getDatatime());
			firstHis.setAfProcessunitid(startUserUnit.getOrganization_id());
			firstHis.setAfProcessunitcode(startUserUnit.getOrganization_code());
			firstHis.setAfProcessunittitle(startUserUnit.getOrganization_name());
			firstHis.setAfProcesstype(Constants.APPROVAL_STATUS_1);
			firstHis.setApprovalGroup(Constants.REVIEW_AUDITOR);
			parentOrgList.add(firstHis);
			Integer total = 0;
			SysOrganization old= null;
			int number = 0;
			if(parents != null){
				for(int i = parents.length-1; i >= 0; i--){ //从流程开始组织，往上查找审批节点


					SysOrganization next = organizationMapper.getOrgByOrgId(parents[i]);
					if (!Objects.isNull(old)&& StringUtils.equals(next.getOrganization_id(), old.getOrganization_id())){
						//数量加1
						total ++;
					}else {
						//重置计数
						total = 0;
					}
					if (total >= 5){
						String msg = "企业异常企业id为：" + old.getOrganization_id()+"异常的企业jbxxbId为："+jbxxb.getId()+"异常企业名称"+old.getOrganization_name();
						throw new RuntimeException(msg);
					}

//				int number = i;
//				if (i > 0){
//					number -= 1;
//				}
//				SysOrganization nextNext = organizationMapper.getOrgByOrgId(parents[number]);
					old = next;
//				if (StringUtils.isNotEmpty(next.getBusinesstype()) && next.getBusinesstype().equals("1") && djStatus){
//					number ++;
//				}
					//todo 新加的需求 不到国资委行政机构
					String businesstype = next.getBusinesstype();
					if (StringUtils.isNotEmpty(businesstype) && businesstype.equals("1") && djStatus){
						break;
					}
					if(StringUtils.isNotEmpty(next.getBusinesstype()) && next.getBusinesstype().equals("1")){ //已经到了国资委行政机构，不再往下审核
						if(StringUtils.isEmpty(unit.getBusiness_nature()) || unit.getBusiness_nature().equals(Constants.QYLX_GYQY)){
							//国有企业加进去
							AuditflowHistoryVo his = new AuditflowHistoryVo();
							his.setBusinessInfoId(businessInfo.getId());
							his.setAfUnitid(businessInfo.getUnitid());
							his.setAfDatatime(businessInfo.getDatatime());
							his.setAfProcessunitid(next.getOrganization_id());
							his.setAfProcessunitcode(next.getOrganization_code());
							his.setAfProcessunittitle(next.getOrganization_name());
							his.setAfProcesstype(Constants.APPROVAL_STATUS_2);
							String reviewTrial = "";
							//取上一条审核记录，判断审批组是审核人，还是初审，还是复审
							AuditflowHistoryVo lastOrg = parentOrgList.get(parentOrgList.size()-1);
							if(Constants.REVIEW_SECEND_TRIAL.equals(lastOrg.getApprovalGroup())){ //审核人
								reviewTrial = Constants.REVIEW_FIRST_TRIAL;
								//当为初审时，下一级审核还是当前企业
								i++;
							}else { //审核人
								reviewTrial = Constants.REVIEW_SECEND_TRIAL;
							}
							his.setApprovalGroup(reviewTrial);
							parentOrgList.add(his);

							if(Constants.REVIEW_SECEND_TRIAL.equals(reviewTrial)){
								break;
							}
						}
					}else
					if(StringUtils.isNotEmpty(next.getBusiness_level())){ //组织层级为空的是虚拟组织，略过
						AuditflowHistoryVo his = new AuditflowHistoryVo();
						his.setBusinessInfoId(businessInfo.getId());
						his.setAfUnitid(businessInfo.getUnitid());
						his.setAfDatatime(businessInfo.getDatatime());
						his.setAfProcessunitid(next.getOrganization_id());
						his.setAfProcessunitcode(next.getOrganization_code());
						his.setAfProcessunittitle(next.getOrganization_name());
						his.setAfProcesstype(Constants.APPROVAL_STATUS_2);

						String reviewTrial = Constants.REVIEW_AUDITOR;
						//取上一条审核记录，判断审批组是审核人，还是初审，还是复审
						if(Constants.REVIEW_AUDITOR.equals(parentOrgList.get(parentOrgList.size()-1).getApprovalGroup())){ //审核人
							//判断初始是从初审还是复审开始的
							//判断当前提交人是否为审核企业的初审人员
							//如果当前提交人员不是审核企业的初审人员，则从初审开始走流程
							//如果当前提交人员是审核企业的初审人员，则从复审开始走流程
							//初始审批企业节点
							AuditflowHistory startReviewHistory = historyList.get(1);
							SysOrganization startReviewOrg = organizationMapper.getOrgByOrgId(startReviewHistory.getAfProcessunitid());
							AuditflowHistoryVo submitAuditFlowHistory = historyList.get(0);
							//上报人
							SysUserVo submitUser = userInfoMapper.getUserInfoByUserId(submitAuditFlowHistory.getAfProcessuserid());
							reviewTrial = getStartLevel(startReviewOrg, submitUser);
							//当为初审时，下一级审核还是当前企业
							if(Constants.REVIEW_FIRST_TRIAL.equals(reviewTrial)){
								i++;
							}
						}else {
							if(Constants.REVIEW_FIRST_TRIAL.equals(parentOrgList.get(parentOrgList.size()-1).getApprovalGroup())){ //初审
								reviewTrial = Constants.REVIEW_SECEND_TRIAL;
							}else if(Constants.REVIEW_SECEND_TRIAL.equals(parentOrgList.get(parentOrgList.size()-1).getApprovalGroup())){
								reviewTrial = Constants.REVIEW_FIRST_TRIAL;
								i++;
							}
						}

						his.setApprovalGroup(reviewTrial);
						parentOrgList.add(his);

						if(StringUtils.isNotEmpty(unit.getBusiness_nature()) && unit.getBusiness_nature().equals(Constants.QYLX_HHQY)){ //国有企业往国资委，合伙企业到1级
							if(next.getBusiness_level().equals("01") && Constants.REVIEW_SECEND_TRIAL.equals(reviewTrial)){ //已经到了1级组织，判断是否为复审，复审不往下审核
								break;
							}
						}

					}

				}
			}
		}
		re.addAll(historyList);
		Integer jbShzt = jbxxb.getJbShzt();
		jbShzt = Objects.isNull(jbShzt) ? -1:jbShzt;
		//审核通过后不需要拼接待审批的轨迹
		if (jbShzt != 4) {
			//拼接流程列表
			if (CollectionUtils.isNotEmpty(historyList) && CollectionUtils.isNotEmpty(parentOrgList)) { //流程已发起
				parentOrgList.remove(0);//流程已发起，删除发起人步骤
				//最后一条审批历史数据
				AuditflowHistoryVo lastHis = historyList.get(historyList.size() - 1);
				Optional<AuditflowHistoryVo> pl = null;
				if (StringUtils.isNotEmpty(lastHis.getApprovalGroup()) && !Constants.REVIEW_AUDITOR.equals(lastHis.getApprovalGroup())) {
					pl = parentOrgList.stream().filter(p -> p.getAfProcessunitid().equals(lastHis.getAfProcessunitid())
									&& p.getApprovalGroup().equals(lastHis.getApprovalGroup()))
							.findFirst();
				} else {
					pl = parentOrgList.stream().filter(p -> p.getAfProcessunitid().equals(lastHis.getAfProcessunitid()))
							.findFirst();
				}
				if (pl.isPresent()) {
					int i = parentOrgList.indexOf(pl.get());
					re.addAll(parentOrgList.subList(i + 1, parentOrgList.size()));
				} else {
					re.addAll(parentOrgList);
				}
			} else { //流程未发起
				re.addAll(parentOrgList);
			}
		}
		//parentOrgList审核状态转中文
		for(AuditflowHistoryVo parentOrg : re){
			parentOrg.setApprovalStatus(dictCacheStrategy.getTextByVal(APPROVAL_STATUS,parentOrg.getAfProcesstype()));
		}
		return re;
	}

	@Override
	public List<BusinessInfoVo> todoList(BusinessInfoParam param){
		SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		String userOrgId = user.getOrganization_id(); //当前登录人组织id，用于查询待自己组织审核的单子
		//查询当前登录人组织被托管的组织列表
		List<String> auditHostingList = organizationService.dataPermissionsForApproval();
		//查询待 auditHostingList 中的组织审批,或退回待上报的流程列表
		return businessInfoMapper.selectTodoList(param.getTodoType(),auditHostingList);
	}

	/**
	 * 待办（待审核/退回）列表查询(分页+筛选条件)
	 */
	@Override
	public BootstrapTableModel<BusinessInfoVo> todoOrReturnList(BusinessInfoParam param) {
		BootstrapTableModel<BusinessInfoVo> model = new BootstrapTableModel<>();
		SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		String userOrgId = user.getOrganization_id(); //当前登录人组织id，用于查询待自己组织审核的单子
		SysOrganization org = organizationService.getOrgByOrgId(userOrgId);
		//当企业为1级或者行政机构时才需要设置初审和复审
//		if(StringUtils.equals("01", org.getBusiness_level()) || StringUtils.equals("1", org.getBusinesstype())) {
			if(StringUtils.isNotEmpty(user.getAudit_level())){
				param.setAuditLevel(user.getAudit_level());
			}
//		}
		param.setUserId(user.getUser_id()); //设置用户id，用户精准查询退回到登陆人的审核单
		//查询当前登录人组织被托管的组织列表
		List<String> auditHostingList = organizationService.dataPermissionsForApproval();
		//分页查询待 auditHostingList 中的组织审批,或退回待上报的流程列表
		PageHelper.startPage(param.getPageNumber(),param.getLimit());
		List<BusinessInfoVo> businessInfoVos = businessInfoMapper.todoOrReturnList(param, auditHostingList);
		//如果是待审核列表，判断是否有查询范围
//		if("1".equals(param.getTodoType()) && CollectionUtils.isNotEmpty(param.getApprovalUnitIds())){
//			//将不在查询范围的数据剔除
//			Iterator<BusinessInfoVo> it = businessInfoVos.iterator();
//			while(it.hasNext()){
//				BusinessInfoVo infoVo = it.next();
//				String[] approvalUnitIds = infoVo.getApprovalUnitIds().split(",");
//				boolean has = false;
//				for(String unitId : approvalUnitIds){
//					if(param.getApprovalUnitIds().contains(unitId)){
//						has = true;
//						break;
//					}
//				}
//				if(!has){ //审核企业中没有一个在查询范围内的，不进行展示
//					it.remove();
//				}
//			}
//		}
		businessInfoVos.forEach(attr->{
			//上级退回的可以删除,权限与填报中删除一致
			if ("2".equals(param.getTodoType())){
				SysUserVo createUser = userInfoMapper.getUserInfoByUserId(attr.getJbxxCreateUser());
				if (createUser != null && StringUtils.isBlank(createUser.getAudit_level())){
					createUser.setAudit_level(null);
				}
				if (StringUtils.isBlank(user.getAudit_level())){
					user.setAudit_level(null);
				}
				if (createUser != null && Objects.equals(userOrgId,createUser.getOrganization_id())
						&& Objects.equals(user.getType(),createUser.getType())
						&& Objects.equals(createUser.getAudit_level(),user.getAudit_level())){
					attr.setCanDelete(true);
				}
			}
			//产权登记情形
			String ZYCQDJQX = dictCacheStrategy.getTextByVal(Constants.ZYCQDJQX, attr.getJbZycqdjqx());
			String ZXCQDJQX = dictCacheStrategy.getTextByVal(Constants.ZXCQDJQX, attr.getJbZxcqdjqx());
			String BDCQDJQX = dictCacheStrategy.getTextByVal(Constants.BDCQDJQX, attr.getJbBdcqdjqx());
			String gjczqy = dictCacheStrategy.getTextByVal("GJCZQY", attr.getJbGJCZQY());
			String jbCqdjqxStr = ZYCQDJQX == null ? (ZXCQDJQX == null ? (BDCQDJQX == null ? "" : BDCQDJQX) : ZXCQDJQX) : ZYCQDJQX;
			attr.setJbCqdjqxStr(jbCqdjqxStr);
			attr.setJbGjczqyStr(gjczqy);
		});
		PageInfo<BusinessInfoVo> pageInfo = new PageInfo<>(businessInfoVos);
		model.setRows(pageInfo.getList());
		model.setTotal(pageInfo.getTotal());
		return model;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByJbxxId(String jbxxId) {
		//先删除审核历史记录
		auditflowHistoryMapper.deleteByJbxxId(jbxxId);
//		businessInfoMapper.deleteByJbxxId(jbxxId);
	}

	@Override
	public BootstrapTableModel<BusinessInfoVo> allApprovedAndNowList(BusinessInfo param){
		BootstrapTableModel<BusinessInfoVo> model = new BootstrapTableModel<>();
		List<BusinessInfoVo> vos = businessInfoMapper.allApprovedAndNowList(param);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		for(BusinessInfoVo vo :vos){
			//处理时间快照，转为yyyy-MM-dd
			vo.setRgTimemarkStr(vo.getRgTimemark() == null ? "" : sdf.format(vo.getRgTimemark()));
		}
		model.setRows(vos);
		model.setTotal(vos.size());
		return model;
	}


	@Override
	public BootstrapTableModel<BusinessInfoVo> getPassData(BusinessInfoParam param) {
		BootstrapTableModel<BusinessInfoVo> model = new BootstrapTableModel<>();
		SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		param.setUserId(user.getUser_id());
		List<BusinessInfoVo> businessInfoVos = businessInfoMapper.getPassData(param);
		businessInfoVos.forEach(attr->{
			//产权登记情形
			String ZYCQDJQX = dictCacheStrategy.getTextByVal(Constants.ZYCQDJQX, attr.getJbZycqdjqx());
			String ZXCQDJQX = dictCacheStrategy.getTextByVal(Constants.ZXCQDJQX, attr.getJbZxcqdjqx());
			String BDCQDJQX = dictCacheStrategy.getTextByVal(Constants.BDCQDJQX, attr.getJbBdcqdjqx());
			String jbCqdjqxStr = ZYCQDJQX == null ? (ZXCQDJQX == null ? (BDCQDJQX == null ? "" : BDCQDJQX) : ZXCQDJQX) : ZYCQDJQX;
			attr.setJbCqdjqxStr(jbCqdjqxStr);
			//业务状态
			String approvalStatus = dictCacheStrategy.getTextByVal("APPROVAL_STATUS", attr.getBusStatus());
			attr.setBusStatus(approvalStatus);
		});
		PageInfo<BusinessInfoVo> pageInfo = new PageInfo<>(businessInfoVos);
		model.setRows(pageInfo.getList());
		model.setTotal(pageInfo.getTotal());
		return model;
	}



}
