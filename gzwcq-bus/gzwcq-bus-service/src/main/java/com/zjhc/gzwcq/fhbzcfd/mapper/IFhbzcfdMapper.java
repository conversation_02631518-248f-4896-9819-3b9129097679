package com.zjhc.gzwcq.fhbzcfd.mapper;

import java.util.Map;
import java.util.List;

import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdVo;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdParam;
import org.apache.ibatis.annotations.Param;

public interface IFhbzcfdMapper {

    /*保存对象*/
    void insert(Fhbzcfd fhbzcfd);

    //物理删除
    void deleteByPrimaryKeys(Map<String, Object> map);

    void deleteByPrimaryKey(@Param("id") String id);

    //逻辑删除
    void logicDeleteByPrimaryKeys(Map<String, Object> map);

    void logicDeleteByPrimaryKey(Map<String, Object> map);

    /*根据非空属性更新对象信息*/
    void updateIgnoreNull(Fhbzcfd fhbzcfd);

    /**
     * 更新
     */
    void update(Fhbzcfd fhbzcfd);

    /*分页查询对象*/
    List<FhbzcfdVo> queryFhbzcfdForList(FhbzcfdParam fhbzcfdParam);

    /*数据总量查询*/
    long queryTotalFhbzcfds(FhbzcfdParam fhbzcfdParam);

    /*根据主键查询对象*/
    FhbzcfdVo selectFhbzcfdByPrimaryKey(Fhbzcfd fhbzcfd);

    /*根据部分属性对象查询全部结果，不分页*/
    List<Fhbzcfd> selectForList(Fhbzcfd fhbzcfd);

    /**
     * 数据唯一性验证
     */
    List<Fhbzcfd> selectForUnique(Fhbzcfd fhbzcfd);

    /**
     * 根据基本信息表id查数据
     *
     * @param id
     * @return
     */
    List<FhbzcfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);

    List<Fhbzcfd>  selectAllByJbxxId(@Param("jbxxId") String jbxxId);
}