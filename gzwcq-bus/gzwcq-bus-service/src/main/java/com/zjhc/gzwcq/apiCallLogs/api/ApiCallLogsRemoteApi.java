package com.zjhc.gzwcq.apiCallLogs.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.apiCallLogs.client.ApiCallLogsFeignClient;
import com.zjhc.gzwcq.apiCallLogs.service.api.IApiCallLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogsParam;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogsVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/apiCallLogsRemoteApi")
@Api(value="apiCallLogs接口文档",tags="tbl_api_call_logs")
public class ApiCallLogsRemoteApi implements ApiCallLogsFeignClient {
  
  	@Autowired
	private IApiCallLogsService apiCallLogsService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param ApiCallLogs
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<ApiCallLogsVo> queryByPage(@RequestBody ApiCallLogsParam apiCallLogsParam) {
        BootstrapTableModel<ApiCallLogsVo> model = new BootstrapTableModel<ApiCallLogsVo>();
		model.setRows(apiCallLogsService.queryApiCallLogsByPage(apiCallLogsParam));
		model.setTotal(apiCallLogsService.queryTotalApiCallLogss(apiCallLogsParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody ApiCallLogs apiCallLogs){
    	apiCallLogsService.insert(apiCallLogs);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	apiCallLogsService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	apiCallLogsService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody ApiCallLogs apiCallLogs){
    	apiCallLogsService.updateIgnoreNull(apiCallLogs);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody ApiCallLogs apiCallLogs){
    	apiCallLogsService.update(apiCallLogs);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public ApiCallLogs selectApiCallLogsByPrimaryKey(@RequestBody ApiCallLogs apiCallLogs){
  		return apiCallLogsService.selectApiCallLogsByPrimaryKey(apiCallLogs);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<ApiCallLogs> selectForList(@RequestBody ApiCallLogs apiCallLogs){
    	return apiCallLogsService.selectForList(apiCallLogs);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody ApiCallLogs apiCallLogs){
    	return apiCallLogsService.validateUniqueParam(apiCallLogs);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody ApiCallLogs apiCallLogs){
    	apiCallLogsService.saveOne(apiCallLogs);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody ApiCallLogs[] objs){
    	apiCallLogsService.multipleSaveAndEdit(objs);
    };
	
}