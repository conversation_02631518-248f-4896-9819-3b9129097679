package com.zjhc.gzwcq.requestErroInfo.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.requestErroInfo.mapper.IRequestErroInfoMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfo;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoVo;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoParam;
import com.zjhc.gzwcq.requestErroInfo.service.api.IRequestErroInfoService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class RequestErroInfoServiceImpl implements IRequestErroInfoService {
	
	@Autowired
	private IRequestErroInfoMapper requestErroInfoMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(RequestErroInfo requestErroInfo){
		requestErroInfo.setOrgId(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getCurrOrgId());//获取当前用户登录组织
		requestErroInfoMapper.insert(requestErroInfo);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		requestErroInfoMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		requestErroInfoMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(RequestErroInfo requestErroInfo){
		requestErroInfoMapper.updateIgnoreNull(requestErroInfo);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(RequestErroInfo requestErroInfo){
		requestErroInfoMapper.update(requestErroInfo);
	}
	
	public List<RequestErroInfoVo> queryRequestErroInfoByPage(RequestErroInfoParam requestErroInfoParam) {
      	//分页
      	PageHelper.startPage(requestErroInfoParam.getPageNumber(),requestErroInfoParam.getLimit(),false);
		return requestErroInfoMapper.queryRequestErroInfoForList(requestErroInfoParam);
	}
	

	public RequestErroInfoVo selectRequestErroInfoByPrimaryKey(RequestErroInfo RequestErroInfo) {
		return requestErroInfoMapper.selectRequestErroInfoByPrimaryKey(RequestErroInfo);
	}
	
	public long queryTotalRequestErroInfos(RequestErroInfoParam requestErroInfoParam) {
		return requestErroInfoMapper.queryTotalRequestErroInfos(requestErroInfoParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<RequestErroInfo> selectForList(RequestErroInfo requestErroInfo){
		return requestErroInfoMapper.selectForList(requestErroInfo);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(RequestErroInfo requestErroInfo) {
		return requestErroInfoMapper.selectForUnique(requestErroInfo).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(RequestErroInfo requestErroInfo) {
      	if(requestErroInfo.getId() == null) {
			this.insert(requestErroInfo);
		}else {
			this.updateIgnoreNull(requestErroInfo);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(RequestErroInfo[] objs) {
		for(RequestErroInfo requestErroInfo : objs) {
			this.saveOne(requestErroInfo);
		}
	}
}
