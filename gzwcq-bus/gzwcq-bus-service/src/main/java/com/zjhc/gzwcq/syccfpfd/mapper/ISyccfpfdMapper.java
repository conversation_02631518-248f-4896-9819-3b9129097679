package com.zjhc.gzwcq.syccfpfd.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdVo;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdParam;
import org.apache.ibatis.annotations.Param;

public interface ISyccfpfdMapper {
	
	/*保存对象*/
	void insert(Syccfpfd syccfpfd);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Syccfpfd syccfpfd);
	
	/**更新*/
	void update(Syccfpfd syccfpfd);
	
	/*分页查询对象*/
	List<SyccfpfdVo> querySyccfpfdForList(SyccfpfdParam syccfpfdParam);
	
	/*数据总量查询*/
	long queryTotalSyccfpfds(SyccfpfdParam syccfpfdParam);
	
	/*根据主键查询对象*/
	Syccfpfd selectSyccfpfdByPrimaryKey(Syccfpfd syccfpfd);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Syccfpfd> selectForList(Syccfpfd syccfpfd);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Syccfpfd> selectForUnique(Syccfpfd syccfpfd);

    void deleteByJbxxId(String jbxxId);

    List<SyccfpfdVo> selectByJbxxId(String jbxxId);

	List<Syccfpfd> selectAllByJbxxId(@Param("jbxxId")String jbxxId);
}