package com.zjhc.gzwcq.requestResultInfo.api;

import com.boot.IAdmin.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.requestResultInfo.client.RequestResultInfoFeignClient;
import com.zjhc.gzwcq.requestResultInfo.service.api.IRequestResultInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfo;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoParam;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/requestResultInfoRemoteApi")
@Api(value="requestResultInfo接口文档",tags="api_request_result_info")
public class RequestResultInfoRemoteApi implements RequestResultInfoFeignClient {
  
  	@Autowired
	private IRequestResultInfoService requestResultInfoService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param RequestResultInfo
	 * @return String
	 */
  	@Override
	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<RequestResultInfoVo> queryByPage(@RequestBody RequestResultInfoParam requestResultInfoParam) {
        BootstrapTableModel<RequestResultInfoVo> model = new BootstrapTableModel<RequestResultInfoVo>();
		model.setRows(requestResultInfoService.queryRequestResultInfoByPage(requestResultInfoParam));
		model.setTotal(requestResultInfoService.queryTotalRequestResultInfos(requestResultInfoParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody RequestResultInfo requestResultInfo){
    	requestResultInfoService.insert(requestResultInfo);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	requestResultInfoService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	requestResultInfoService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody RequestResultInfo requestResultInfo){
    	requestResultInfoService.updateIgnoreNull(requestResultInfo);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody RequestResultInfo requestResultInfo){
    	requestResultInfoService.update(requestResultInfo);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public RequestResultInfo selectRequestResultInfoByPrimaryKey(@RequestBody RequestResultInfo requestResultInfo){
  		return requestResultInfoService.selectRequestResultInfoByPrimaryKey(requestResultInfo);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<RequestResultInfo> selectForList(@RequestBody RequestResultInfo requestResultInfo){
    	return requestResultInfoService.selectForList(requestResultInfo);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody RequestResultInfo requestResultInfo){
    	return requestResultInfoService.validateUniqueParam(requestResultInfo);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody RequestResultInfo requestResultInfo){
    	requestResultInfoService.saveOne(requestResultInfo);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody RequestResultInfo[] objs){
    	requestResultInfoService.multipleSaveAndEdit(objs);
    };
	
}