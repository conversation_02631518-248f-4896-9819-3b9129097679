<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.xgzjgfd.mapper.IXgzjgfdMapper">

	<resultMap type="com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="fd3_bz" property="fd3Bz"/>
		<result column="fd3_xgzjgfd" property="fd3Xgzjgfd"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdVo" extends="baseResultMap">
		<result column="fd3XgzjgfdStr" property="fd3XgzjgfdStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		fd3_bz, 
		fd3_xgzjgfd, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.fd3_bz, 
		t.fd3_xgzjgfd, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{fd3Bz}, 
		#{fd3Xgzjgfd}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="fd3Bz != null and fd3Bz != ''">
			and t.fd3_bz = #{fd3Bz}
		</if>
		<if test="fd3Xgzjgfd != null and fd3Xgzjgfd != ''">
			and t.fd3_xgzjgfd = #{fd3Xgzjgfd}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_xgzjgfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_xgzjgfd set isDeleted = 'Y' where
		id in
		<foreach collection="xgzjgfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_xgzjgfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_xgzjgfd  where
		id in
		<foreach collection="xgzjgfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_xgzjgfd  where id = #{id}
	</delete>
	
	<select id="selectXgzjgfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_xgzjgfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_xgzjgfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="fd3Bz != null">
				fd3_bz=#{fd3Bz},
			</if>
			<if test="fd3Xgzjgfd != null">
				fd3_xgzjgfd=#{fd3Xgzjgfd},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_xgzjgfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			fd3_bz=#{fd3Bz},
			fd3_xgzjgfd=#{fd3Xgzjgfd},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_xgzjgfd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalXgzjgfds" parameterType="com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_xgzjgfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryXgzjgfdForList" parameterType="com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_xgzjgfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_xgzjgfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="fd3Bz != null and fd3Bz != ''">
				and t.fd3_bz = #{fd3Bz}
			</if>
			<if test="fd3Xgzjgfd != null and fd3Xgzjgfd != ''">
				and t.fd3_xgzjgfd = #{fd3Xgzjgfd}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxId" resultMap="baseResultMapExt">
		select  <include refid="columnsAlias"/>,sd1.text fd3XgzjgfdStr from cq_xgzjgfd t
		left join sys_dictionary sd1 on sd1.val=t.FD3_XGZJGFD and sd1.type_id=(select id from sys_dictionary where type_code='GZJGJGFD')
		where t.JBXX_ID = #{id}
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_xgzjgfd where jbxx_id = #{jbxxId}
	</delete>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			cq_xgzjgfd.FD3_XGZJGFD,
			cq_xgzjgfd.FD3_BZ
		FROM
			`cq_xgzjgfd`
		WHERE
			JBXX_ID =#{jbxxId}
	</select>
</mapper>