<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.files.mapper.IFilesMapper">

	<resultMap type="com.zjhc.gzwcq.files.entity.Files" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="title" property="title"/>
		<result column="descript" property="descript"/>
		<result column="look_time" property="lookTime"/>
		<result column="attachment_id" property="attachmentId"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.files.entity.FilesVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		title, 
		descript, 
		look_time, 
		attachment_id, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.title, 
		t.descript, 
		t.look_time, 
		t.attachment_id, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{title}, 
		#{descript}, 
		#{lookTime}, 
		#{attachmentId}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="title != null and title != ''">
			and t.title like concat('%',#{title},'%')
		</if>
		<if test="descript != null and descript != ''">
			and t.descript = #{descript}
		</if>
		<if test="lookTime != null">
			and t.look_time = #{lookTime}
		</if>
		<if test="attachmentId != null and attachmentId != ''">
			and t.attachment_id = #{attachmentId}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.files.entity.Files" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_files (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_files set isDeleted = 'Y' where
		id in
		<foreach collection="filess" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_files set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_files  where
		id in
		<foreach collection="filess" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_files  where id = #{id}
	</delete>
	
	<select id="selectFilesByPrimaryKey" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,su.NAME createUserStr
		from cq_files t left join sys_users su on t.create_user = su.USER_ID
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_files
		<set>
			<if test="title != null">
				title=#{title},
			</if>
			<if test="descript != null">
				descript=#{descript},
			</if>
			<if test="lookTime != null">
				look_time=#{lookTime},
			</if>
			<if test="attachmentId != null">
				attachment_id=#{attachmentId},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_files
		<set>
			title=#{title},
			descript=#{descript},
			look_time=#{lookTime},
			attachment_id=#{attachmentId},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.files.entity.Files" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_files t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalFiless" parameterType="com.zjhc.gzwcq.files.entity.FilesParam" resultType="java.lang.Long">
		select
			count(id)
		from cq_files t
		where 1=1
		<include refid="whereSql" />
		<if test="inTimeStart!=null and inTimeEnd!=null">
			and DATE_FORMAT(t.create_time,'%Y-%m-%d') between DATE_FORMAT(#{inTimeStart},'%Y-%m-%d') and DATE_FORMAT(#{inTimeEnd},'%Y-%m-%d')
		</if>
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryFilesForList" parameterType="com.zjhc.gzwcq.files.entity.FilesParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,su.NAME createUserStr
		from
			cq_files t left join sys_users su on t.create_user = su.USER_ID
		where 1=1
		<include refid="whereSql" />
		<if test="inTimeStart!=null and inTimeEnd!=null">
			and DATE_FORMAT(t.create_time,'%Y-%m-%d') between DATE_FORMAT(#{inTimeStart},'%Y-%m-%d') and DATE_FORMAT(#{inTimeEnd},'%Y-%m-%d')
		</if>
		order by t.create_time desc
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.files.entity.Files" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_files t
		where t.id != #{id}
			<if test="title != null and title != ''">
				and t.title = #{title}
			</if>
			<if test="descript != null and descript != ''">
				and t.descript = #{descript}
			</if>
			<if test="lookTime != null">
				and t.look_time = #{lookTime}
			</if>
			<if test="attachmentId != null and attachmentId != ''">
				and t.attachment_id = #{attachmentId}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

</mapper>