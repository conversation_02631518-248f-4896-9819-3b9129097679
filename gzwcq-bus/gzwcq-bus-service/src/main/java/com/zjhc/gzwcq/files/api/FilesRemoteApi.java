package com.zjhc.gzwcq.files.api;

import feign.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zjhc.gzwcq.files.client.FilesFeignClient;
import com.zjhc.gzwcq.files.service.api.IFilesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.*;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.files.entity.Files;
import com.zjhc.gzwcq.files.entity.FilesParam;
import com.zjhc.gzwcq.files.entity.FilesVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value="/filesRemoteApi")
@Api(value="files接口文档",tags="cq_files")
public class FilesRemoteApi implements FilesFeignClient {
  
  	@Autowired
	private IFilesService filesService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Files
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<FilesVo> queryByPage(@RequestBody FilesParam filesParam) {
        BootstrapTableModel<FilesVo> model = new BootstrapTableModel<FilesVo>();
		model.setRows(filesService.queryFilesByPage(filesParam));
		model.setTotal(filesService.queryTotalFiless(filesParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Files files){
    	filesService.insert(files);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	filesService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	filesService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Files files){
    	filesService.updateIgnoreNull(files);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Files files){
    	filesService.update(files);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public FilesVo selectFilesByPrimaryKey(@RequestBody Files files){
  		return filesService.selectFilesByPrimaryKey(files);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Files> selectForList(@RequestBody Files files){
    	return filesService.selectForList(files);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Files files){
    	return filesService.validateUniqueParam(files);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Files files){
    	filesService.saveOne(files);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Files[] objs){
    	filesService.multipleSaveAndEdit(objs);
    }

	@Override
	public void lookTime(@RequestParam("id")String id) {
		Files files = new Files();
		files.setId(id);
		FilesVo filesVo = filesService.selectFilesByPrimaryKey(files);
		files.setLookTime(filesVo.getLookTime()+1);
		filesService.saveOne(files);
	}


	;

	
}