<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.monitorwarn.mapper.IMonitorwarnMapper">

	<resultMap type="com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="from_unitid" property="fromUnitid"/>
		<result column="change_category" property="changeCategory"/>
		<result column="change_type" property="changeType"/>
		<result column="change_qyjc_old" property="changeQyjcOld"/>
		<result column="change_qyjc_new" property="changeQyjcNew"/>
		<result column="change_czr_code" property="changeCzrCode"/>
		<result column="change_reason" property="changeReason"/>
		<result column="change_status" property="changeStatus"/>
		<result column="change_info" property="changeInfo"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="have_read" property="haveRead"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		from_unitid,
		change_category, 
		change_type, 
		change_qyjc_old, 
		change_qyjc_new, 
		change_czr_code, 
		change_reason, 
		change_status, 
		change_info, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time,
		have_read
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.from_unitid,
		t.change_category, 
		t.change_type, 
		t.change_qyjc_old, 
		t.change_qyjc_new, 
		t.change_czr_code, 
		t.change_reason, 
		t.change_status, 
		t.change_info, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time,
		t.have_read
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{fromUnitid},
		#{changeCategory}, 
		#{changeType}, 
		#{changeQyjcOld}, 
		#{changeQyjcNew}, 
		#{changeCzrCode}, 
		#{changeReason}, 
		#{changeStatus}, 
		#{changeInfo}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime},
		#{haveRead}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="fromUnitid != null and fromUnitid != ''">
			and t.from_unitid = #{fromUnitid}
		</if>
		<if test="changeCategory != null and changeCategory != ''">
			and t.change_category = #{changeCategory}
		</if>
		<if test="changeType != null and changeType != ''">
			and t.change_type = #{changeType}
		</if>
		<if test="changeQyjcOld != null and changeQyjcOld != ''">
			and t.change_qyjc_old = #{changeQyjcOld}
		</if>
		<if test="changeQyjcNew != null and changeQyjcNew != ''">
			and t.change_qyjc_new = #{changeQyjcNew}
		</if>
		<if test="changeCzrCode != null and changeCzrCode != ''">
			and t.change_czr_code = #{changeCzrCode}
		</if>
		<if test="changeReason != null and changeReason != ''">
			and t.change_reason = #{changeReason}
		</if>
		<if test="changeStatus != null and changeStatus != ''">
			and t.change_status = #{changeStatus}
		</if>
		<if test="changeInfo != null and changeInfo != ''">
			and t.change_info = #{changeInfo}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_monitorwarn (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_monitorwarn set isDeleted = 'Y' where
		id in
		<foreach collection="monitorwarns" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_monitorwarn set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_monitorwarn  where
		id in
		<foreach collection="monitorwarns" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_monitorwarn  where id = #{id}
	</delete>
	
	<select id="selectMonitorwarnByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_monitorwarn
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_monitorwarn
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="fromUnitid != null">
				from_unitid=#{fromUnitid},
			</if>
			<if test="changeCategory != null">
				change_category=#{changeCategory},
			</if>
			<if test="changeType != null">
				change_type=#{changeType},
			</if>
			<if test="changeQyjcOld != null">
				change_qyjc_old=#{changeQyjcOld},
			</if>
			<if test="changeQyjcNew != null">
				change_qyjc_new=#{changeQyjcNew},
			</if>
			<if test="changeCzrCode != null">
				change_czr_code=#{changeCzrCode},
			</if>
			<if test="changeReason != null">
				change_reason=#{changeReason},
			</if>
			<if test="changeStatus != null">
				change_status=#{changeStatus},
			</if>
			<if test="changeInfo != null">
				change_info=#{changeInfo},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime},
			</if>
			<if test="haveRead != null and haveRead != ''">
				have_read=#{haveRead}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_monitorwarn
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			from_unitid=#{fromUnitid},
			change_category=#{changeCategory},
			change_type=#{changeType},
			change_qyjc_old=#{changeQyjcOld},
			change_qyjc_new=#{changeQyjcNew},
			change_czr_code=#{changeCzrCode},
			change_reason=#{changeReason},
			change_status=#{changeStatus},
			change_info=#{changeInfo},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime},
			have_read=#{haveRead}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_monitorwarn t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalMonitorwarns" parameterType="com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam" resultType="java.lang.Long">
		select
			count(t.ID)
		from cq_monitorwarn t
		left join sys_organization so on t.unitid = so.ORGANIZATION_ID
		where 1=1 and t.CHANGE_STATUS = '0'
		<if test="unitCode != null and unitCode != ''">
			and so.ORGANIZATION_CODE like concat(concat('%',#{unitCode}),'%')
			and (so.BUSINESSTYPE = '1' or so.BUSINESS_LEVEL is not null)
		</if>
		<if test="unitName != null and unitName != ''">
			and so.ORGANIZATION_NAME like concat(concat('%',#{unitName}),'%')
		</if>
		<if test="startTime != null">
			and t.create_time &gt;= #{startTime}
		</if>
		<if test="endTime != null">
			and t.create_time &lt;= #{endTime}
		</if>
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryMonitorwarnForList" parameterType="com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,so.ORGANIZATION_NAME as unitName
		from
			cq_monitorwarn t
			left join sys_organization so on t.unitid = so.ORGANIZATION_ID
		where 1=1 and t.CHANGE_STATUS = '0'
		<if test="unitCode != null and unitCode != ''">
			and so.ORGANIZATION_CODE like concat(concat('%',#{unitCode}),'%')
			and (so.BUSINESSTYPE = '1' or so.BUSINESS_LEVEL is not null)
		</if>
		<if test="unitName != null and unitName != ''">
			and so.ORGANIZATION_NAME like concat(concat('%',#{unitName}),'%')
		</if>
		<if test="startTime != null">
			and t.create_time &gt;= #{startTime}
		</if>
		<if test="endTime != null">
			and t.create_time &lt;= #{endTime}
		</if>
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_monitorwarn t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="fromUnitid != null and fromUnitid != ''">
				and t.from_unitid = #{fromUnitid}
			</if>
			<if test="changeCategory != null and changeCategory != ''">
				and t.change_category = #{changeCategory}
			</if>
			<if test="changeType != null and changeType != ''">
				and t.change_type = #{changeType}
			</if>
			<if test="changeQyjcOld != null and changeQyjcOld != ''">
				and t.change_qyjc_old = #{changeQyjcOld}
			</if>
			<if test="changeQyjcNew != null and changeQyjcNew != ''">
				and t.change_qyjc_new = #{changeQyjcNew}
			</if>
			<if test="changeCzrCode != null and changeCzrCode != ''">
				and t.change_czr_code = #{changeCzrCode}
			</if>
			<if test="changeReason != null and changeReason != ''">
				and t.change_reason = #{changeReason}
			</if>
			<if test="changeStatus != null and changeStatus != ''">
				and t.change_status = #{changeStatus}
			</if>
			<if test="changeInfo != null and changeInfo != ''">
				and t.change_info = #{changeInfo}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
			<if test="haveRead != null and haveRead != ''">
				and t.have_read = #{haveRead}
			</if>
	</select>

	<select id="selectWarnReviewList" parameterType="com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam"
			resultMap="baseResultMapExt">
		select
			DISTINCT a.*,d.JB_QYMC as unitName
		from cq_monitorwarn a
				 LEFT JOIN rg_business_info b on a.JBXX_ID = b.JBXX_ID
				 LEFT JOIN rg_auditflow_history c on b.ID = c.BUSINESS_INFO_ID
				 left join sys_organization so on a.unitid = so.ORGANIZATION_ID
				 left join view_cq_jbxxb_noDelete d on a.JBXX_ID = d.id
		where c.AF_PROCESSTYPE in('6','5','9')
-- 		and c.AF_PROCESSTYPE != '1'
		<if test="changeType != null and changeType != ''">
			and a.change_type = #{changeType}
		</if>
		<if test="unitCode != null and unitCode != ''">
			and so.ORGANIZATION_CODE like concat(concat('%',#{unitCode}),'%')
			and (so.BUSINESSTYPE = '1' or so.BUSINESS_LEVEL is not null)
		</if>
		<if test="unitName != null and unitName != ''">
			and so.ORGANIZATION_NAME like concat(concat('%',#{unitName}),'%')
		</if>
		<if test="startTime != null">
			and a.create_time &gt;= #{startTime}
		</if>
		<if test="endTime != null">
			and a.create_time &lt;= #{endTime}
		</if>
		<choose>
			<!--  初审人员跟复审人员  -->
			<when test="auditLevel == '1'.toString()">
				and b.AF_CURRENT_AUDIT_LEVEL = '初审'
			</when>
			<when test="auditLevel == '2'.toString()">
				and b.AF_CURRENT_AUDIT_LEVEL = '复审'
			</when>
		</choose>
		and c.AF_PROCESSUNITID in
		<foreach collection="auditHostingList" item="item" separator="," index="index" open="(" close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectWarnAuditNum" resultType="java.lang.Integer">
		select count(1)
		from cq_monitorwarn a
		LEFT JOIN rg_business_info b on a.JBXX_ID = b.JBXX_ID
		LEFT JOIN rg_auditflow_history c on b.ID = c.BUSINESS_INFO_ID
		left join view_cq_jbxxb_noDelete qj on b.JBXX_ID = qj.id
		where qj.id is not null and c.AF_PROCESSTYPE in('6','5','9')
		<choose>
			<!--  初审人员跟复审人员  -->
			<when test="auditLevel == '1'.toString()">
				and b.AF_CURRENT_AUDIT_LEVEL = '初审'
			</when>
			<when test="auditLevel == '2'.toString()">
				and b.AF_CURRENT_AUDIT_LEVEL = '复审'
			</when>
		</choose>
		and c.AF_PROCESSUNITID in
		<foreach collection="auditHostingList" item="item" separator="," index="index" open="(" close=")">
			#{item}
		</foreach>
	</select>
</mapper>