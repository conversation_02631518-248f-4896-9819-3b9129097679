<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.placard.mapper.IPlacardMapper">

	<resultMap type="com.zjhc.gzwcq.placard.entity.Placard" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="title" property="title"/>
		<result column="descript" property="descript"/>
		<result column="content" property="content"/>
		<result column="look_time" property="lookTime"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="have_read" property="haveRead"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.placard.entity.PlacardVo" extends="baseResultMap">
		<result column="createUserStr" property="createUserStr"/>
		<result column="lastUpdateUserStr" property="lastUpdateUserStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		title, 
		descript, 
		content, 
		look_time, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time,
		have_read
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.title, 
		t.descript, 
		t.content, 
		t.look_time, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time,
		t.have_read
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{title}, 
		#{descript}, 
		#{content}, 
		#{lookTime}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime},
		#{haveRead}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="title != null and title != ''">
			and t.title = #{title}
		</if>
		<if test="descript != null and descript != ''">
			and t.descript = #{descript}
		</if>
		<if test="content != null and content != ''">
			and t.content = #{content}
		</if>
		<if test="lookTime != null">
			and t.look_time = #{lookTime}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="haveRead != null and haveRead != ''">
			and t.have_read = #{haveRead}
		</if>
	</sql>

	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql2">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="title != null and title != ''">
			and t.title like concat('%',#{title},'%')
		</if>
		<if test="descript != null and descript != ''">
			and t.descript = #{descript}
		</if>
		<if test="content != null and content != ''">
			and t.content = #{content}
		</if>
		<if test="lookTime != null">
			and t.look_time = #{lookTime}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="beginDate != null and endDate != null">
			and t.create_time between #{beginDate} and #{endDate}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
		<if test="haveRead != null and haveRead != ''">
			and t.have_read = #{haveRead}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.placard.entity.Placard" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_placard (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_placard set isDeleted = 'Y' where
		id in
		<foreach collection="placards" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_placard set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_placard  where
		id in
		<foreach collection="placards" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_placard  where id = #{id}
	</delete>
	
	<select id="selectPlacardByPrimaryKey" resultMap="baseResultMapExt" parameterType="com.zjhc.gzwcq.placard.entity.Placard">
		select 
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr
		from cq_placard t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		where t.id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_placard
		<set>
			<if test="title != null">
				title=#{title},
			</if>
			<if test="descript != null">
				descript=#{descript},
			</if>
			<if test="content != null">
				content=#{content},
			</if>
			<if test="lookTime != null">
				look_time=#{lookTime},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime},
			</if>
			<if test="haveRead != null and haveRead != ''">
			    have_read = #{haveRead}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_placard
		<set>
			title=#{title},
			descript=#{descript},
			content=#{content},
			look_time=#{lookTime},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime},
			have_read = #{haveRead}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.placard.entity.Placard" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_placard t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalPlacards" parameterType="com.zjhc.gzwcq.placard.entity.PlacardParam" resultType="java.lang.Long">
		select
			count(id)
		from cq_placard t
		where 1=1
		<include refid="whereSql2" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryPlacardForList" parameterType="com.zjhc.gzwcq.placard.entity.PlacardParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr
		from
			cq_placard t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		where 1=1
		<include refid="whereSql2" />
		order by t.create_time desc
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.placard.entity.Placard" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_placard t
		where t.id != #{id}
			<if test="title != null and title != ''">
				and t.title = #{title}
			</if>
			<if test="descript != null and descript != ''">
				and t.descript = #{descript}
			</if>
			<if test="content != null and content != ''">
				and t.content = #{content}
			</if>
			<if test="lookTime != null">
				and t.look_time = #{lookTime}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
			<if test="haveRead != null and haveRead != ''">
				and t.have_read = #{haveRead}
			</if>
	</select>

</mapper>