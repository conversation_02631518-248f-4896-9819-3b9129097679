package com.zjhc.gzwcq.ywzbb.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbParam;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo;

public interface IYwzbbService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Ywzbb ywzbb);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Ywzbb ywzbb);

	YwzbbVo selectByJbxxId(String jbxxId);
	/**
	* 更新
	*/
	void update(Ywzbb ywzbb);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<YwzbbVo> queryYwzbbByPage(YwzbbParam ywzbbParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalYwzbbs(YwzbbParam ywzbbParam);
  
	
	/**
	 *通过ID查询数据
	 */
	YwzbbVo selectYwzbbByPrimaryKey(Ywzbb ywzbb);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Ywzbb> selectForList(Ywzbb ywzbb);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Ywzbb ywzbb);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Ywzbb ywzbb);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Ywzbb[] objs);

    void deleteByJbxxId(String jbxxId);
}