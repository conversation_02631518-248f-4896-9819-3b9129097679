package com.zjhc.gzwcq.requestResultInfo.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.requestResultInfo.mapper.IRequestResultInfoMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfo;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoVo;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoParam;
import com.zjhc.gzwcq.requestResultInfo.service.api.IRequestResultInfoService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class RequestResultInfoServiceImpl implements IRequestResultInfoService {
	
	@Autowired
	private IRequestResultInfoMapper requestResultInfoMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(RequestResultInfo requestResultInfo){
		requestResultInfoMapper.insert(requestResultInfo);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		requestResultInfoMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		requestResultInfoMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(RequestResultInfo requestResultInfo){
		requestResultInfoMapper.updateIgnoreNull(requestResultInfo);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(RequestResultInfo requestResultInfo){
		requestResultInfoMapper.update(requestResultInfo);
	}
	
	public List<RequestResultInfoVo> queryRequestResultInfoByPage(RequestResultInfoParam requestResultInfoParam) {
      	//分页
      	PageHelper.startPage(requestResultInfoParam.getPageNumber(),requestResultInfoParam.getLimit(),false);
		return requestResultInfoMapper.queryRequestResultInfoForList(requestResultInfoParam);
	}
	

	public RequestResultInfo selectRequestResultInfoByPrimaryKey(RequestResultInfo RequestResultInfo) {
		return requestResultInfoMapper.selectRequestResultInfoByPrimaryKey(RequestResultInfo);
	}
	
	public long queryTotalRequestResultInfos(RequestResultInfoParam requestResultInfoParam) {
		return requestResultInfoMapper.queryTotalRequestResultInfos(requestResultInfoParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<RequestResultInfo> selectForList(RequestResultInfo requestResultInfo){
		return requestResultInfoMapper.selectForList(requestResultInfo);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(RequestResultInfo requestResultInfo) {
		return requestResultInfoMapper.selectForUnique(requestResultInfo).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(RequestResultInfo requestResultInfo) {
      	if(requestResultInfo.getId() == null) {
			this.insert(requestResultInfo);
		}else {
			this.updateIgnoreNull(requestResultInfo);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(RequestResultInfo[] objs) {
		for(RequestResultInfo requestResultInfo : objs) {
			this.saveOne(requestResultInfo);
		}
	}
}
