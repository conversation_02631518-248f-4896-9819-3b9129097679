package com.zjhc.gzwcq.newHome.mapper;

import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.newHome.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:46:44
 **/
@Repository
public interface INewHomeMapper {
    SysUser selectUserStatus(@Param("userId") String userId);

    DataBusinessAssessmentVO selectBusinessAssessment(@Param("orId") String orId, @Param("dateTime") String dateTime, @Param("dataStatus") String dataStatus, @Param("status") String status);

    /**
     * 查的是包括国资委提交的数据
     * @param orId
     * @param dateTime
     * @return
     */
    BusinessAssessmentInfoTotalDto selectBusinessAssessmentInfo(@Param("orId") String orId, @Param("dateTime") String dateTime, @Param("yearMonth") String yearMonth);
    /**
     * 新的需求 查的是不包括国资委提交的数据
     * @param orId
     * @param dateTime
     * @return
     */
    List<DataBusinessAssessmentInfoVO> selectBusinessAssessmentInfoNew(@Param("orId") String orId, @Param("dateTime") String dateTime);

    /**
     * 查询企业户数
     *
     * @param orgId      组织ID
     * @param yearMonth 年月（截止）
     * @return 企业户数
     * <AUTHOR>
     */
    Integer selectCompanyCount(@Param("orgId") String orgId, @Param("yearMonth") String yearMonth, @Param("orgIdList") List<String> orgIdList);

    List<DataBusinessTransactionVO> selectHandleMattersInfo(@Param("dateTime") String dateTime, @Param("ordId") String ordId, @Param("yearMonth") String yearMonth);

    /**
     * @description 查询一级企业办理事项情况 性能优化
     * <AUTHOR>
     * @date 2025/7/27 14:07
     */
    List<DataBusinessTransactionVO> selectHandleMattersInfoV2(@Param("dateTime") String dateTime, @Param("ordId") String ordId, @Param("yearMonth") String yearMonth);
}
