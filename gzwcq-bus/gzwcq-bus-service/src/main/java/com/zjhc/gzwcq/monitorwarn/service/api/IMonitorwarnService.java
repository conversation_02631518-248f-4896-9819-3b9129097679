package com.zjhc.gzwcq.monitorwarn.service.api;

import java.util.Map;
import java.util.List;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;

public interface IMonitorwarnService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Monitorwarn monitorwarn);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Monitorwarn monitorwarn);
	
	/**
	* 更新
	*/
	void update(Monitorwarn monitorwarn);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<MonitorwarnVo> queryMonitorwarnByPage(MonitorwarnParam monitorwarnParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalMonitorwarns(MonitorwarnParam monitorwarnParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Monitorwarn selectMonitorwarnByPrimaryKey(Monitorwarn monitorwarn);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Monitorwarn> selectForList(Monitorwarn monitorwarn);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Monitorwarn monitorwarn);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Monitorwarn monitorwarn);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Monitorwarn[] objs);

	/**
	 * 审核完成后，进行自动预警
	 * @param jbxxb
	 */
	void monitorWarn(Jbxxb jbxxb);

	/**
	 * 预警发起审核
	 * @param monitorwarn
	 */
	String submitWarnReview(Monitorwarn monitorwarn);

	/**
	 * 查询预警审核列表数据
	 * @param param
	 * @return
	 */
	BootstrapTableModel<MonitorwarnVo> selectWarnReviewList(MonitorwarnParam param);

	/**
	 * 预警读消息
	 * @param monitorwarn
	 */
    void readMessage(Monitorwarn monitorwarn);
}