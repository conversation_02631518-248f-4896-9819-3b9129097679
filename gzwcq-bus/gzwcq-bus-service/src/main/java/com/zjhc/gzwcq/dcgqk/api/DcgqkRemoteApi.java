package com.zjhc.gzwcq.dcgqk.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.dcgqk.client.DcgqkFeignClient;
import com.zjhc.gzwcq.dcgqk.service.api.IDcgqkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkParam;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/dcgqkRemoteApi")
@Api(value="dcgqk接口文档",tags="代持股情况浮动表")
public class DcgqkRemoteApi implements DcgqkFeignClient {
  
  	@Autowired
	private IDcgqkService dcgqkService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Dcgqk
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<DcgqkVo> queryByPage(@RequestBody DcgqkParam dcgqkParam) {
        BootstrapTableModel<DcgqkVo> model = new BootstrapTableModel<DcgqkVo>();
		model.setRows(dcgqkService.queryDcgqkByPage(dcgqkParam));
		model.setTotal(dcgqkService.queryTotalDcgqks(dcgqkParam));
		return model;
	}

	@Override
	public BootstrapTableModel<DcgqkVo> loadByJbxxbId(@RequestBody DcgqkParam dcgqkParam) {
		return dcgqkService.loadByJbxxbId(dcgqkParam);
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Dcgqk dcgqk){
    	dcgqkService.insert(dcgqk);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	dcgqkService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	dcgqkService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Dcgqk dcgqk){
    	dcgqkService.updateIgnoreNull(dcgqk);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Dcgqk dcgqk){
    	dcgqkService.update(dcgqk);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Dcgqk selectDcgqkByPrimaryKey(@RequestBody Dcgqk dcgqk){
  		return dcgqkService.selectDcgqkByPrimaryKey(dcgqk);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Dcgqk> selectForList(@RequestBody Dcgqk dcgqk){
    	return dcgqkService.selectForList(dcgqk);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Dcgqk dcgqk){
    	return dcgqkService.validateUniqueParam(dcgqk);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Dcgqk dcgqk){
    	dcgqkService.saveOne(dcgqk);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Dcgqk[] objs){
    	dcgqkService.multipleSaveAndEdit(objs);
    };
	
}