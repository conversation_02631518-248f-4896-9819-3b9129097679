package com.zjhc.gzwcq.jbxxb.service.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.itextpdf.text.DocumentException;
import com.zjhc.gzwcq.jbxxb.entity.*;
import org.springframework.http.ResponseEntity;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IJbxxbService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Jbxxb jbxxb);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Jbxxb jbxxb);
	
	/**
	* 更新
	*/
	void update(Jbxxb jbxxb);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<JbxxbVo> queryJbxxbByPage(JbxxbParam jbxxbParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalJbxxbs(JbxxbParam jbxxbParam);
  
	
	/**
	 *通过ID查询数据
	 */
	JbxxbVo selectJbxxbByPrimaryKey(Jbxxb jbxxb);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Jbxxb> selectForList(Jbxxb jbxxb);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Jbxxb jbxxb);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	Jbxxb saveOne(Jbxxb jbxxb);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Jbxxb[] objs);

	/**
	 * 分页查询未办工商列表
	 */
	BootstrapTableModel<JbxxbVo> loadWbgsByPage(JbxxbParam jbxxbParam);

	/**
	 * 保存合伙企业
	 */
	String savePartnership(FormVo vo);

	/**
	 * 国有资本保存
	 * */
	String saveGovernmentCapital(FormVo vo);

	/**
	 * 判断企业是否存在办理中业务
	 */
    boolean hasProcessing(Jbxxb jbxxb);

	/**
	 * 根据组织id获取其最新的状态数据
	 */
    JbxxbVo loadRecentApprovedByUnitId(Jbxxb jbxxb);

	/**
	 * 是否国家出资企业主业(有交集为是,1:是;2:否)
	 */
    byte isMainBusiness(Jbxxb jbxxb);

	/**
	 * 比较当前填报中的企业的基本信息与其最新审核通过的基本信息的不同
	 */
	Map<String, Object> jbxxCompare(Jbxxb jbxxb) throws IllegalAccessException;

	/**
	 * 是否境外转投境内企业(1:是;2:否)
	 */
	byte isJwToJn(Jbxxb jbxxb);

	ResponseEntity<byte[]> export(Jbxxb jbxxb) throws IOException;

	/**
	 * 根据主键获取对象,转为pdf
	 * @param jbxxb
	 * @return
	 */
	ResponseEntity<byte[]> loadPdf(Jbxxb jbxxb) throws IOException;

	List<DictionaryVo> loadAllSituations();

	/**
	 * 判断企业是否存在下级
	 */
	Boolean hasSubordinate(Jbxxb jbxxb);

	/**
	 * 根据主键获取对象,转为pdf-登记证
	 * @param jbxxb
	 * @return
	 */
	ResponseEntity<byte[]> loadDJZPdf(Jbxxb jbxxb) throws IOException, DocumentException;

	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<JbxxbVo> queryDjByPage(JbxxbParam jbxxbParam);

	/**
	 *分页查询总条数
	 */
	long queryTotalDj(JbxxbParam jbxxbParam);

	/**
	 * 根据组织id获取其最新的状态数据(不含信息采集和合规资料数据)
	 */
    JbxxbVo loadRecentJbxxOnly(Jbxxb jbxxb);

	/**
	 * 根据组织id获取其最新的状态数据--对外接口
	 */
	List<JbxxbVo> loadRecentApprovedByOpenApi(Integer pageNumber,Integer limit,List<String> orgId);


	 List<JbxxbVo> loadRecentApprovedByOrdId(Integer pageNumber,Integer limit,List<String> orgId);
}