package com.zjhc.gzwcq.auditflowHistory.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.auditflowHistory.client.AuditflowHistoryFeignClient;
import com.zjhc.gzwcq.auditflowHistory.service.api.IAuditflowHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryParam;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/auditflowHistoryRemoteApi")
@Api(value="auditflowHistory接口文档",tags="审核历史记录")
public class AuditflowHistoryRemoteApi implements AuditflowHistoryFeignClient {
  
  	@Autowired
	private IAuditflowHistoryService auditflowHistoryService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param AuditflowHistory
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<AuditflowHistoryVo> queryByPage(@RequestBody AuditflowHistoryParam auditflowHistoryParam) {
        BootstrapTableModel<AuditflowHistoryVo> model = new BootstrapTableModel<AuditflowHistoryVo>();
		model.setRows(auditflowHistoryService.queryAuditflowHistoryByPage(auditflowHistoryParam));
		model.setTotal(auditflowHistoryService.queryTotalAuditflowHistorys(auditflowHistoryParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody AuditflowHistory auditflowHistory){
    	auditflowHistoryService.insert(auditflowHistory);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	auditflowHistoryService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	auditflowHistoryService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody AuditflowHistory auditflowHistory){
    	auditflowHistoryService.updateIgnoreNull(auditflowHistory);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody AuditflowHistory auditflowHistory){
    	auditflowHistoryService.update(auditflowHistory);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public AuditflowHistory selectAuditflowHistoryByPrimaryKey(@RequestBody AuditflowHistory auditflowHistory){
  		return auditflowHistoryService.selectAuditflowHistoryByPrimaryKey(auditflowHistory);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<AuditflowHistoryVo> selectForList(@RequestBody AuditflowHistory auditflowHistory){
    	return auditflowHistoryService.selectForList(auditflowHistory);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody AuditflowHistory auditflowHistory){
    	return auditflowHistoryService.validateUniqueParam(auditflowHistory);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody AuditflowHistory auditflowHistory){
    	auditflowHistoryService.saveOne(auditflowHistory);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody AuditflowHistory[] objs){
    	auditflowHistoryService.multipleSaveAndEdit(objs);
    };
	
}