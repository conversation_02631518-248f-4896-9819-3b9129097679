package com.zjhc.gzwcq.files.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.files.entity.Files;
import com.zjhc.gzwcq.files.entity.FilesVo;
import com.zjhc.gzwcq.files.entity.FilesParam;

public interface IFilesService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Files files);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Files files);
	
	/**
	* 更新
	*/
	void update(Files files);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<FilesVo> queryFilesByPage(FilesParam filesParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalFiless(FilesParam filesParam);
  
	
	/**
	 *通过ID查询数据
	 */
	FilesVo selectFilesByPrimaryKey(Files files);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Files> selectForList(Files files);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Files files);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Files files);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Files[] objs);
	
}