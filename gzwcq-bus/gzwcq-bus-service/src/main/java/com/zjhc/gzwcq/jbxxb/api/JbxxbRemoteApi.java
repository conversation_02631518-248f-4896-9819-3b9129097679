package com.zjhc.gzwcq.jbxxb.api;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.itextpdf.text.DocumentException;
import com.zjhc.gzwcq.jbxxb.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.zjhc.gzwcq.jbxxb.client.JbxxbFeignClient;
import com.zjhc.gzwcq.jbxxb.service.api.IJbxxbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/jbxxbRemoteApi")
@Api(value="jbxxb接口文档",tags="产权基本信息表")
public class JbxxbRemoteApi implements JbxxbFeignClient {
  
  	@Autowired
	private IJbxxbService jbxxbService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Jbxxb
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<JbxxbVo> queryByPage(@RequestBody JbxxbParam jbxxbParam) {
        BootstrapTableModel<JbxxbVo> model = new BootstrapTableModel<JbxxbVo>();
		model.setRows(jbxxbService.queryJbxxbByPage(jbxxbParam));
		model.setTotal(jbxxbService.queryTotalJbxxbs(jbxxbParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Jbxxb jbxxb){
    	jbxxbService.insert(jbxxb);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	jbxxbService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	jbxxbService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Jbxxb jbxxb){
    	jbxxbService.updateIgnoreNull(jbxxb);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Jbxxb jbxxb){
    	jbxxbService.update(jbxxb);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public JbxxbVo selectJbxxbByPrimaryKey(@RequestBody Jbxxb jbxxb){
  		return jbxxbService.selectJbxxbByPrimaryKey(jbxxb);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Jbxxb> selectForList(@RequestBody Jbxxb jbxxb){
    	return jbxxbService.selectForList(jbxxb);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Jbxxb jbxxb){
    	return jbxxbService.validateUniqueParam(jbxxb);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Jbxxb jbxxb){
    	jbxxbService.saveOne(jbxxb);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Jbxxb[] objs){
    	jbxxbService.multipleSaveAndEdit(objs);
    }

	/**
	 * 保存合伙企业
	 */
	@Override
	public String savePartnership(@RequestBody FormVo vo) {
		return jbxxbService.savePartnership(vo);
	}

	/**
	 * 国有资本保存
	 * */
	@Override
	public String saveGovernmentCapital(@RequestBody FormVo vo) {
		return jbxxbService.saveGovernmentCapital(vo);
	}

	@ApiOperation(value="分页查询未办工商列表")
	public BootstrapTableModel<JbxxbVo> loadWbgsByPage(@RequestBody JbxxbParam jbxxbParam) {
		return jbxxbService.loadWbgsByPage(jbxxbParam);
	}

	@ApiOperation(value="判断企业是否存在办理中业务")
	public boolean hasProcessing(@RequestBody Jbxxb jbxxb) {
		return jbxxbService.hasProcessing(jbxxb);
	}

	@ApiOperation(value="根据组织id获取其最新的状态数据")
	public JbxxbVo loadRecentApprovedByUnitId(@RequestBody Jbxxb jbxxb) {
		return jbxxbService.loadRecentApprovedByUnitId(jbxxb);
	}

	@ApiOperation(value = "是否国家出资企业主业(有交集为是,1:是;2:否)")
	public byte isMainBusiness(@RequestBody Jbxxb jbxxb) {
		return jbxxbService.isMainBusiness(jbxxb);
	}

	@ApiOperation("比较当前填报中的企业的基本信息与其最新审核通过的基本信息的不同")
	public Map<String, Object> jbxxCompare(@RequestBody Jbxxb jbxxb) throws IllegalAccessException {
		return jbxxbService.jbxxCompare(jbxxb);
	}

	@ApiOperation("是否境外转投境内企业(1:是;2:否)")
	public byte isJwToJn(Jbxxb jbxxb) {
		return jbxxbService.isJwToJn(jbxxb);
	}

	/**
	 * 导出基本信息表excel
	 */
	@ApiOperation("导出基本信息表excel")
	public ResponseEntity<byte[]> export(@RequestBody Jbxxb jbxxb) throws IOException {
		return jbxxbService.export(jbxxb);
	}

	/**
	 * 根据主键获取对象,转为pdf
	 */
	@ApiOperation("根据主键获取对象,转为pdf")
	public ResponseEntity<byte[]> loadPdf(@RequestBody Jbxxb jbxxb) throws IOException {
		return jbxxbService.loadPdf(jbxxb);
	}

	@ApiOperation("获取所有登记情形")
	public List<DictionaryVo> loadAllSituations() {
		return jbxxbService.loadAllSituations();
	}

	/**
	 * 判断企业是否存在下级
	 */
	@Override
	@ApiOperation("判断企业是否存在下级")
	public boolean hasSubordinate(@RequestBody Jbxxb jbxxb) {
		return jbxxbService.hasSubordinate(jbxxb);
	}

	/**
	 * 根据主键获取对象,转为pdf-登记证
	 */
	@ApiOperation("根据主键获取对象,转为pdf-登记证")
	public ResponseEntity<byte[]> loadDJZPdf(@RequestBody Jbxxb jbxxb) throws IOException, DocumentException {
		return jbxxbService.loadDJZPdf(jbxxb);
	}

	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<JbxxbVo> queryByDjPage(@RequestBody JbxxbParam jbxxbParam) {
		BootstrapTableModel<JbxxbVo> model = new BootstrapTableModel<JbxxbVo>();
		model.setRows(jbxxbService.queryDjByPage(jbxxbParam));
		model.setTotal(jbxxbService.queryTotalDj(jbxxbParam));
		return model;
	}

	@ApiOperation(value="根据组织id获取其最新的状态数据(不含信息采集和合规资料数据)")
	public JbxxbVo loadRecentJbxxOnly(@RequestBody Jbxxb jbxxb) {
		return jbxxbService.loadRecentJbxxOnly(jbxxb);
	}
}