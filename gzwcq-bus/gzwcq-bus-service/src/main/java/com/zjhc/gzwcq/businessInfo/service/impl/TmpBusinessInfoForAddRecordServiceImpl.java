package com.zjhc.gzwcq.businessInfo.service.impl;

import com.boot.IAdmin.common.utils.Constants;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;
import com.boot.iAdmin.access.service.impl.OrganizationService;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.boot.iAdmin.redis.service.RedisSerialNumberService;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.auditflowHistory.mapper.IAuditflowHistoryMapper;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.mapper.IBusinessInfoMapper;
import com.zjhc.gzwcq.businessInfo.service.api.IBusinessInfoService;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import com.zjhc.gzwcq.jbxxb.service.api.IJbxxbService;
import com.zjhc.gzwcq.monitorwarn.service.api.IMonitorwarnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 国务院国资委新增字段补录 审核流程 - 只需一个人审核即可
 */
@Slf4j
@Service
public class TmpBusinessInfoForAddRecordServiceImpl {

    @Autowired
    private IBusinessInfoMapper businessInfoMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private IAuditflowHistoryMapper auditflowHistoryMapper;

    @Autowired
    private IJbxxbService jbxxbService;
    @Autowired
    private IMonitorwarnService monitorwarnService;
    @Autowired
    private SysUserInfoMapper userInfoMapper;
    @Autowired
    private RedisSerialNumberService redisSerialNumberService;

    @Autowired
    private IBusinessInfoService businessInfoService;

    @Autowired
    private DictCacheStrategy dictCacheStrategy;
    public static final String APPROVAL_STATUS = "APPROVAL_STATUS";//登记类型


    @Transactional(rollbackFor = Exception.class)
    public void review(BusinessInfoParam param,BusinessInfo businessInfo,Jbxxb jbxxb,SysUser user){
        Date nowDate = new Date();

        //查询审核人组织信息
        SysOrganization userOrg = organizationMapper.getOrgByOrgId(user.getOrganization_id());
        //查找当前审核节点
        SysOrganization currOrg = organizationMapper.getOrgByOrgId(businessInfo.getAfCurrentunitid());
        //查询变动企业组织信息
        SysOrganization unit = organizationMapper.getOrgByOrgId(businessInfo.getUnitid());
        if(null == unit){
            //占有企业取查基本信息，带过来
            unit = new SysOrganization();
            unit.setOrganization_id(jbxxb.getUnitid()); //组织id
            unit.setOrganization_code(jbxxb.getJbZzjgdm()); //组织机构代码
            unit.setOrganization_name(jbxxb.getJbQymc()); //组织名称
            unit.setBusiness_nature(jbxxb.getBusinessNature()); //企业性质(2合伙企业，其他为国有)
        }
        //查询上报人企业信息

        //查询审核历史列表
        AuditflowHistory auditflowHistory = new AuditflowHistory();
        auditflowHistory.setBusinessInfoId(businessInfo.getId());
        List<AuditflowHistoryVo> historyList = auditflowHistoryMapper.selectForList(auditflowHistory);
        AuditflowHistory lastHistory = historyList.get(historyList.size()-1);
        //查询上报企业
        auditflowHistory.setAfProcesstype(Constants.APPROVAL_STATUS_1);
        List<AuditflowHistoryVo> submitAuditFlowHistoryList = auditflowHistoryMapper.selectForList(auditflowHistory);
        AuditflowHistoryVo submitAuditFlowHistory = submitAuditFlowHistoryList.get(0);
        SysOrganization submitOrg = organizationMapper.getOrgByOrgId(submitAuditFlowHistory.getAfProcessunitid());
        //上报人
        SysUserVo submitUser = userInfoMapper.getUserInfoByUserId(submitAuditFlowHistory.getAfProcessuserid());
        String auditLevel = null;//审核层级中文(存到business_info表)
        //补充审核节点信息
        lastHistory.setAfProcesstype(param.getApprovalResult());
        lastHistory.setAfProcessuserid(user.getUser_id());
        lastHistory.setAfProcessunitid(userOrg.getOrganization_id());
        lastHistory.setAfProcessdate(nowDate);
        lastHistory.setAfProcesscomment(param.getAfProcesscomment());
        lastHistory.setAfProcessusername(user.getUsername());
        lastHistory.setAfProcessusertitle(user.getName());
        lastHistory.setAfProcessunitcode(userOrg.getOrganization_code());
        lastHistory.setAfProcessunittitle(userOrg.getOrganization_name());
        lastHistory.setLastUpdateTime(nowDate);
        lastHistory.setLastUpdateUser(user.getUser_id());
        auditflowHistoryMapper.updateIgnoreNull(lastHistory);

        String reviewTrial = "";
        AuditflowHistory nextHistory = new AuditflowHistory();
        SysOrganization nextReviewNode = null;
        String approvalStr = "";
        String APPROVAL_STATUS = Constants.APPROVAL_STATUS_2, //审批历史表下一个审核数据状态默认待审核，退回时改为待上报
                REVIEW_STATUS=Constants.REVIEW_STATUS_2; //登记表状态每次审核默认审核中，当退回才改为退回
        //默认没到工商补录阶段 用印懒加载审批人与审批流
        boolean DJ_STATUS = false;
        if(param.getApprovalResult().equals(Constants.APPROVAL_STATUS_3)){ //审核通过,只需要审核一次就结束
            businessInfo.setRgTimemark(nowDate); //里程牌时间快照

            //判断占有跟变动是否已办工商选的否，则需要补录，选的是则直接审批结束
            if(
                    Constants.JBXX_SFYBGS_N.equals(jbxxb.getJbSfybgs())){
                APPROVAL_STATUS = Constants.APPROVAL_STATUS_9; //下一条审批历史状态为待工商登记资料补录
                REVIEW_STATUS = Constants.REVIEW_STATUS_9; //登记表状态改为待工商登记资料补录
                nextReviewNode = submitOrg; //下一节点就是上报企业组织
                approvalStr = "待工商登记资料补录";
                DJ_STATUS = true;
                //基本信息表状态改为待工商登记资料补录
                this.updateJbxxbStatus(jbxxb.getId(),Integer.parseInt(Constants.JBXX_REVIEW_STATUS_9));

            }else{
//                //自动预警判断,只有变动才需要
//                if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())){
//                    Jbxxb monitorJbxxb = new Jbxxb();
//                    BeanUtils.copyProperties(jbxxb,monitorJbxxb);
//                    monitorwarnService.monitorWarn(monitorJbxxb);
//                }
                String sixCode = ""; //六位区域码
                if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())){ //变动审核后，判断父级组织是否改变
                    //修改企业信息
                    SysOrganization rootorg = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
                    sixCode = rootorg.getSsgzjgjg();
                    rootorg.setOrganization_name(jbxxb.getJbQymc());
                    rootorg.setOrganization_desc(jbxxb.getJbQymc());
                    rootorg.setBusiness_level(jbxxb.getJbQyjc());
                    rootorg.setOrganization_code(sixCode + "_" + jbxxb.getJbZzjgdm());
                    rootorg.setSyncode(jbxxb.getJbZzjgdm());
                    organizationMapper.updateIgnoreNull(rootorg);

                    reviewPassWhenUpdate(jbxxb);
                }
                businessInfo.setRegisterCode(redisSerialNumberService.getSerialNumberByForFixedLength(sixCode,null,5)); //登记编号
                businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_3);
                businessInfo.setAfCurrentNode("审核完成");
                businessInfo.setAfCurrentunitid("");  //当前审核节点组织id置空
                businessInfo.setAfCurrentAuditLevel(""); //当前审核层级置空
                businessInfoService.updateIgnoreNull(businessInfo);
                //基本信息表状态改为审核通过
                this.updateJbxxbStatus(jbxxb.getId(), Integer.parseInt(Constants.JBXX_REVIEW_STATUS_2));
                return;
            }

        }else if(param.getApprovalResult().equals(Constants.APPROVAL_STATUS_4)){ //审核退回
            //判断当前提交人是否为审核企业的初审人员
            //如果当前提交人员不是审核企业的初审人员，则从初审开始走流程
            //如果当前提交人员是审核企业的初审人员，则从复审开始走流程
            //初始审批企业节点
            AuditflowHistory startReviewHistory = historyList.get(1);
            SysOrganization startReviewOrg = organizationMapper.getOrgByOrgId(startReviewHistory.getAfProcessunitid());
            String afProcessgroup = getStartLevel(startReviewOrg, submitUser);

            APPROVAL_STATUS = Constants.APPROVAL_STATUS_5; //下一条审批历史状态为待上报
            REVIEW_STATUS = Constants.REVIEW_STATUS_4; //登记表状态改为退回
            reviewTrial = Constants.REVIEW_FQZ;//上报
            nextReviewNode = submitOrg; //下一节点就是操作上报企业组织
            approvalStr = "待上报";
            //将上报的审核层级置空
            businessInfo.setAfCurrentAuditLevel(null);
            //指定发起人id
            nextHistory.setAfProcessuserid(submitUser.getUser_id());
            //基本信息表改为流程未发起
				/*jbxxb.setJbShzt(Integer.parseInt(Constants.JBXX_REVIEW_STATUS_3));
				jbxxbService.update(jbxxb);*/
        }else{
            throw new RuntimeException("审核结果为非法值，请确认！");
        }

        nextHistory.setBusinessInfoId(businessInfo.getId());
        nextHistory.setAfUnitid(businessInfo.getUnitid());
        nextHistory.setAfDatatime(businessInfo.getDatatime());
        nextHistory.setAfProcesstype(APPROVAL_STATUS);
        nextHistory.setAfProcessunitid(nextReviewNode.getOrganization_id());
        nextHistory.setAfProcessunitcode(nextReviewNode.getOrganization_code());
        nextHistory.setAfProcessunittitle(nextReviewNode.getOrganization_name());
        nextHistory.setCreateUser(user.getUser_id());
        nextHistory.setCreateTime(nowDate);
        nextHistory.setAfProcessgroup(StringUtils.isNotEmpty(reviewTrial) ? reviewTrial : Constants.REVIEW_AUDITOR);
        auditflowHistoryMapper.insert(nextHistory);

        //更新登记表状态
        businessInfo.setAfCurrentunitid(nextReviewNode.getOrganization_id());
        businessInfo.setRgUnitstate(REVIEW_STATUS);
        businessInfo.setAfCurrentNode(approvalStr);
        businessInfo.setDjStatus(DJ_STATUS);
        businessInfoService.updateIgnoreNull(businessInfo);
    }


    //更新审核状态
    private void updateJbxxbStatus(String jbxxbId, int shzt) {
        Jbxxb jb = new Jbxxb();
        jb.setId(jbxxbId);
        jb.setJbShzt(shzt);
        jbxxbService.updateIgnoreNull(jb);
    }

    /**
     * 变动审核通过后业务
     */
    private void reviewPassWhenUpdate(Jbxxb jbxxb){
        //获取上版本的基本信息
        Jbxxb jbxxbNew = new Jbxxb();
        jbxxbNew.setUnitid(jbxxb.getUnitid());
        JbxxbVo jbxxbVo = jbxxbService.loadRecentApprovedByUnitId(jbxxbNew);
        if(!Objects.equals(jbxxbVo.getJbCzrzzjgid(),jbxxb.getJbCzrzzjgid())){
            //查询新的父级组织
            SysOrganization orgParentNew = organizationMapper.getOrgByOrgId(jbxxb.getJbCzrzzjgid());
            //查询本组织
            SysOrganization rootorg = organizationMapper.getOrgByOrgId(jbxxb.getUnitid());
            rootorg.setParents(orgParentNew.getParents()+","+rootorg.getOrganization_id());
            rootorg.setParent_id(orgParentNew.getOrganization_id());
            organizationMapper.updateIgnoreNull(rootorg);

            reviewPassWhenUpdateDo(rootorg,orgParentNew);
        }
    }

    private void reviewPassWhenUpdateDo(SysOrganization rootorg,SysOrganization orgParentNew){
        rootorg.setParents(orgParentNew.getParents()+","+rootorg.getOrganization_id());
        organizationMapper.updateIgnoreNull(rootorg);
        //查询子组织列表
        SysOrganization param = new SysOrganization();
        param.setParent_id(rootorg.getOrganization_id());
        List<SysOrganization> childOrgList = organizationMapper.selectForList(param);
        if(CollectionUtils.isEmpty(childOrgList)){
            return;
        }
        for(SysOrganization child : childOrgList){
            reviewPassWhenUpdateDo(child,rootorg);
        }

    }

    /**
     * 传入审批组织和上报人，判断先初审还是复审
     * @param nextReviewNode
     * @param user
     * @return
     */
    private String getStartLevel(SysOrganization nextReviewNode,SysUser user){
        //判断当前提交人是否为审核企业的初审人员
        //如果当前提交人员不是审核企业的初审人员，则从初审开始走流程
        //如果当前提交人员是审核企业的初审人员，则从复审开始走流程
        String afProcessgroup = Constants.REVIEW_FIRST_TRIAL;  //默认从初审开始走
        if(nextReviewNode != null && user != null){
            SysUser userGroup = new SysUser();
            userGroup.setOrganization_id(nextReviewNode.getOrganization_id());
            userGroup.setIsdeleted(Constants.NO_DELETED);
            userGroup.setAudit_level(Constants.REVIEW_FIRST_TRIAL_1); //初审人员
            List<SysUser> userGroupList = userInfoMapper.selectForList(userGroup);
            if(CollectionUtils.isNotEmpty(userGroupList) && user != null){
                Optional<SysUser> op =userGroupList.stream().filter(u -> u.getUser_id().equals(user.getUser_id()))
                        .findFirst();
                if(op.isPresent()){
                    afProcessgroup = Constants.REVIEW_SECEND_TRIAL;
                }
            }
        }
        return afProcessgroup;
    }

}
