package com.zjhc.gzwcq.fhbzcfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.fhbzcfd.client.FhbzcfdFeignClient;
import com.zjhc.gzwcq.fhbzcfd.service.api.IFhbzcfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdParam;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/fhbzcfdRemoteApi")
@Api(value="fhbzcfd接口文档",tags="非货币资产浮动")
public class FhbzcfdRemoteApi implements FhbzcfdFeignClient {
  
  	@Autowired
	private IFhbzcfdService fhbzcfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Fhbzcfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<FhbzcfdVo> queryByPage(@RequestBody FhbzcfdParam fhbzcfdParam) {
        BootstrapTableModel<FhbzcfdVo> model = new BootstrapTableModel<FhbzcfdVo>();
		model.setRows(fhbzcfdService.queryFhbzcfdByPage(fhbzcfdParam));
		model.setTotal(fhbzcfdService.queryTotalFhbzcfds(fhbzcfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Fhbzcfd fhbzcfd){
    	fhbzcfdService.insert(fhbzcfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	fhbzcfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	fhbzcfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Fhbzcfd fhbzcfd){
    	fhbzcfdService.updateIgnoreNull(fhbzcfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Fhbzcfd fhbzcfd){
    	fhbzcfdService.update(fhbzcfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public FhbzcfdVo selectFhbzcfdByPrimaryKey(@RequestBody Fhbzcfd fhbzcfd){
  		return fhbzcfdService.selectFhbzcfdByPrimaryKey(fhbzcfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Fhbzcfd> selectForList(@RequestBody Fhbzcfd fhbzcfd){
    	return fhbzcfdService.selectForList(fhbzcfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Fhbzcfd fhbzcfd){
    	return fhbzcfdService.validateUniqueParam(fhbzcfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Fhbzcfd fhbzcfd){
    	fhbzcfdService.saveOne(fhbzcfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Fhbzcfd[] objs){
    	fhbzcfdService.multipleSaveAndEdit(objs);
    };
	
}