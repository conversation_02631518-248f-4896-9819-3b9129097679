package com.zjhc.gzwcq.ywzbb2.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.ywzbb2.client.Ywzbb2FeignClient;
import com.zjhc.gzwcq.ywzbb2.service.api.IYwzbb2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Param;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/ywzbb2RemoteApi")
@Api(value="ywzbb2接口文档",tags="产权业务指标表2")
public class Ywzbb2RemoteApi implements Ywzbb2FeignClient {
  
  	@Autowired
	private IYwzbb2Service ywzbb2Service;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Ywzbb2
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<Ywzbb2Vo> queryByPage(@RequestBody Ywzbb2Param ywzbb2Param) {
        BootstrapTableModel<Ywzbb2Vo> model = new BootstrapTableModel<Ywzbb2Vo>();
		model.setRows(ywzbb2Service.queryYwzbb2ByPage(ywzbb2Param));
		model.setTotal(ywzbb2Service.queryTotalYwzbb2s(ywzbb2Param));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Ywzbb2 ywzbb2){
    	ywzbb2Service.insert(ywzbb2);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	ywzbb2Service.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	ywzbb2Service.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Ywzbb2 ywzbb2){
    	ywzbb2Service.updateIgnoreNull(ywzbb2);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Ywzbb2 ywzbb2){
    	ywzbb2Service.update(ywzbb2);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Ywzbb2Vo selectYwzbb2ByPrimaryKey(@RequestBody Ywzbb2 ywzbb2){
  		return ywzbb2Service.selectYwzbb2ByPrimaryKey(ywzbb2);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Ywzbb2> selectForList(@RequestBody Ywzbb2 ywzbb2){
    	return ywzbb2Service.selectForList(ywzbb2);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Ywzbb2 ywzbb2){
    	return ywzbb2Service.validateUniqueParam(ywzbb2);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Ywzbb2 ywzbb2){
    	ywzbb2Service.saveOne(ywzbb2);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Ywzbb2[] objs){
    	ywzbb2Service.multipleSaveAndEdit(objs);
    };
	
}