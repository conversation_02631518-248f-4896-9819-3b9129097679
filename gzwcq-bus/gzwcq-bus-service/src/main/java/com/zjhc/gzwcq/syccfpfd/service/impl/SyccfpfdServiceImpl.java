package com.zjhc.gzwcq.syccfpfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.syccfpfd.mapper.ISyccfpfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdVo;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdParam;
import com.zjhc.gzwcq.syccfpfd.service.api.ISyccfpfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class SyccfpfdServiceImpl implements ISyccfpfdService {
	
	@Autowired
	private ISyccfpfdMapper syccfpfdMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		syccfpfdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Syccfpfd syccfpfd){
		syccfpfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		syccfpfd.setCreateTime(new Date());//创建时间
		syccfpfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		syccfpfd.setLastUpdateTime(new Date());//更新时间
		syccfpfdMapper.insert(syccfpfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		syccfpfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		syccfpfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Syccfpfd syccfpfd){
		syccfpfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		syccfpfd.setLastUpdateTime(new Date());//更新时间
		syccfpfdMapper.updateIgnoreNull(syccfpfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Syccfpfd syccfpfd){
		syccfpfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		syccfpfd.setLastUpdateTime(new Date());//更新时间
		syccfpfdMapper.update(syccfpfd);
	}
	
	public List<SyccfpfdVo> querySyccfpfdByPage(SyccfpfdParam syccfpfdParam) {
      	//分页
      	PageHelper.startPage(syccfpfdParam.getPageNumber(),syccfpfdParam.getLimit(),false);
		return syccfpfdMapper.querySyccfpfdForList(syccfpfdParam);
	}
	

	public Syccfpfd selectSyccfpfdByPrimaryKey(Syccfpfd Syccfpfd) {
		return syccfpfdMapper.selectSyccfpfdByPrimaryKey(Syccfpfd);
	}
	
	public long queryTotalSyccfpfds(SyccfpfdParam syccfpfdParam) {
		return syccfpfdMapper.queryTotalSyccfpfds(syccfpfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Syccfpfd> selectForList(Syccfpfd syccfpfd){
		return syccfpfdMapper.selectForList(syccfpfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Syccfpfd syccfpfd) {
		return syccfpfdMapper.selectForUnique(syccfpfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Syccfpfd syccfpfd) {
		if(StringUtils.isBlank(syccfpfd.getId())) {
			this.insert(syccfpfd);
		}else {
			this.updateIgnoreNull(syccfpfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Syccfpfd[] objs) {
		for(Syccfpfd syccfpfd : objs) {
			this.saveOne(syccfpfd);
		}
	}

	@Override
	public List<SyccfpfdVo> selectByJbxxId(String jbxxId) {
		return syccfpfdMapper.selectByJbxxId(jbxxId);
	}
}
