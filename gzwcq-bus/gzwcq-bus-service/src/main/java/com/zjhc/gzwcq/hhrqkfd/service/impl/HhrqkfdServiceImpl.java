package com.zjhc.gzwcq.hhrqkfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.hhrqkfd.mapper.IHhrqkfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdParam;
import com.zjhc.gzwcq.hhrqkfd.service.api.IHhrqkfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class HhrqkfdServiceImpl implements IHhrqkfdService {
	
	@Autowired
	private IHhrqkfdMapper hhrqkfdMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		hhrqkfdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Hhrqkfd hhrqkfd){
		hhrqkfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		hhrqkfd.setCreateTime(new Date());//创建时间
		hhrqkfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hhrqkfd.setLastUpdateTime(new Date());//更新时间
		hhrqkfdMapper.insert(hhrqkfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		hhrqkfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		hhrqkfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Hhrqkfd hhrqkfd){
		hhrqkfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hhrqkfd.setLastUpdateTime(new Date());//更新时间
		hhrqkfdMapper.updateIgnoreNull(hhrqkfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Hhrqkfd hhrqkfd){
		hhrqkfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hhrqkfd.setLastUpdateTime(new Date());//更新时间
		hhrqkfdMapper.update(hhrqkfd);
	}
	
	public List<HhrqkfdVo> queryHhrqkfdByPage(HhrqkfdParam hhrqkfdParam) {
      	//分页
      	PageHelper.startPage(hhrqkfdParam.getPageNumber(),hhrqkfdParam.getLimit(),false);
		return hhrqkfdMapper.queryHhrqkfdForList(hhrqkfdParam);
	}
	

	public Hhrqkfd selectHhrqkfdByPrimaryKey(Hhrqkfd Hhrqkfd) {
		return hhrqkfdMapper.selectHhrqkfdByPrimaryKey(Hhrqkfd);
	}
	
	public long queryTotalHhrqkfds(HhrqkfdParam hhrqkfdParam) {
		return hhrqkfdMapper.queryTotalHhrqkfds(hhrqkfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Hhrqkfd> selectForList(Hhrqkfd hhrqkfd){
		return hhrqkfdMapper.selectForList(hhrqkfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Hhrqkfd hhrqkfd) {
		return hhrqkfdMapper.selectForUnique(hhrqkfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Hhrqkfd hhrqkfd) {
		if(StringUtils.isBlank(hhrqkfd.getId())) {
			this.insert(hhrqkfd);
		}else {
			this.updateIgnoreNull(hhrqkfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Hhrqkfd[] objs) {
		for(Hhrqkfd hhrqkfd : objs) {
			this.saveOne(hhrqkfd);
		}
	}

	@Override
	public List<HhrqkfdVo> selectByJbxxbId(String jbxxId) {
		return hhrqkfdMapper.selectByJbxxbId(jbxxId);
	}
}
