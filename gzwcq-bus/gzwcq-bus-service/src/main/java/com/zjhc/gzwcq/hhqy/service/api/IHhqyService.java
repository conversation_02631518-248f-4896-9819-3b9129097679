package com.zjhc.gzwcq.hhqy.service.api;

import com.zjhc.gzwcq.hhqy.entity.Hhqy;
import com.zjhc.gzwcq.hhqy.entity.HhqyParam;
import com.zjhc.gzwcq.hhqy.entity.HhqyVo;

import java.util.Map;
import java.util.List;

public interface IHhqyService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Hhqy hhqy);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Hhqy hhqy);
	
	/**
	* 更新
	*/
	void update(Hhqy hhqy);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<HhqyVo> queryHhqyByPage(HhqyParam hhqyParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalHhqys(HhqyParam hhqyParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Hhqy selectHhqyByPrimaryKey(Hhqy hhqy);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Hhqy> selectForList(Hhqy hhqy);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Hhqy hhqy);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Hhqy hhqy);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Hhqy[] objs);

	/**
	 * 按基本信息id查询合伙企业数据
	 */
    HhqyVo selectByJbxxbId(String jbxxId);

    void deleteByJbxxId(String jbxxId);
}