package com.zjhc.gzwcq.dwtzqkfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.dwtzqkfd.mapper.IDwtzqkfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdParam;
import com.zjhc.gzwcq.dwtzqkfd.service.api.IDwtzqkfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class DwtzqkfdServiceImpl implements IDwtzqkfdService {
	
	@Autowired
	private IDwtzqkfdMapper dwtzqkfdMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		dwtzqkfdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Dwtzqkfd dwtzqkfd){
		dwtzqkfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		dwtzqkfd.setCreateTime(new Date());//创建时间
		dwtzqkfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		dwtzqkfd.setLastUpdateTime(new Date());//更新时间
		dwtzqkfdMapper.insert(dwtzqkfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		dwtzqkfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		dwtzqkfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Dwtzqkfd dwtzqkfd){
		dwtzqkfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		dwtzqkfd.setLastUpdateTime(new Date());//更新时间
		dwtzqkfdMapper.updateIgnoreNull(dwtzqkfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Dwtzqkfd dwtzqkfd){
		dwtzqkfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		dwtzqkfd.setLastUpdateTime(new Date());//更新时间
		dwtzqkfdMapper.update(dwtzqkfd);
	}
	
	public List<DwtzqkfdVo> queryDwtzqkfdByPage(DwtzqkfdParam dwtzqkfdParam) {
      	//分页
      	PageHelper.startPage(dwtzqkfdParam.getPageNumber(),dwtzqkfdParam.getLimit(),false);
		return dwtzqkfdMapper.queryDwtzqkfdForList(dwtzqkfdParam);
	}
	

	public Dwtzqkfd selectDwtzqkfdByPrimaryKey(Dwtzqkfd Dwtzqkfd) {
		return dwtzqkfdMapper.selectDwtzqkfdByPrimaryKey(Dwtzqkfd);
	}
	
	public long queryTotalDwtzqkfds(DwtzqkfdParam dwtzqkfdParam) {
		return dwtzqkfdMapper.queryTotalDwtzqkfds(dwtzqkfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Dwtzqkfd> selectForList(Dwtzqkfd dwtzqkfd){
		return dwtzqkfdMapper.selectForList(dwtzqkfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Dwtzqkfd dwtzqkfd) {
		return dwtzqkfdMapper.selectForUnique(dwtzqkfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Dwtzqkfd dwtzqkfd) {
		if(StringUtils.isBlank(dwtzqkfd.getId())) {
			this.insert(dwtzqkfd);
		}else {
			this.updateIgnoreNull(dwtzqkfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Dwtzqkfd[] objs) {
		for(Dwtzqkfd dwtzqkfd : objs) {
			this.saveOne(dwtzqkfd);
		}
	}

	@Override
	public List<DwtzqkfdVo> selectByJbxxbId(String jbxxId) {
		return dwtzqkfdMapper.selectByJbxxbId(jbxxId);
	}
}
