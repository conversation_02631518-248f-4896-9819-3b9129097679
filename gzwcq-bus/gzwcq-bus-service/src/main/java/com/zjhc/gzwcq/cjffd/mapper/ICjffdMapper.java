package com.zjhc.gzwcq.cjffd.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.cjffd.entity.Cjffd;
import com.zjhc.gzwcq.cjffd.entity.CjffdVo;
import com.zjhc.gzwcq.cjffd.entity.CjffdParam;
import org.apache.ibatis.annotations.Param;

public interface ICjffdMapper {
	
	/*保存对象*/
	void insert(Cjffd cjffd);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Cjffd cjffd);
	
	/**更新*/
	void update(Cjffd cjffd);
	
	/*分页查询对象*/
	List<CjffdVo> queryCjffdForList(CjffdParam cjffdParam);
	
	/*数据总量查询*/
	long queryTotalCjffds(CjffdParam cjffdParam);
	
	/*根据主键查询对象*/
	Cjffd selectCjffdByPrimaryKey(Cjffd cjffd);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Cjffd> selectForList(Cjffd cjffd);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Cjffd> selectForUnique(Cjffd cjffd);

    void deleteByJbxxId(String jbxxId);

    List<CjffdVo> selectByJbxxId(String jbxxId);

	List<Cjffd>selectAllByJbxxId(@Param("jbxxId") String jbxxId);
}