package com.zjhc.gzwcq.czfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.czfd.mapper.ICzfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.czfd.entity.CzfdParam;
import com.zjhc.gzwcq.czfd.service.api.ICzfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class CzfdServiceImpl implements ICzfdService {
	
	@Autowired
	private ICzfdMapper czfdMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Czfd czfd){
		czfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		czfd.setCreateTime(new Date());//创建时间
		czfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		czfd.setLastUpdateTime(new Date());//更新时间
		czfdMapper.insert(czfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		czfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		czfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Czfd czfd){
		czfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		czfd.setLastUpdateTime(new Date());//更新时间
		czfdMapper.updateIgnoreNull(czfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Czfd czfd){
		czfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		czfd.setLastUpdateTime(new Date());//更新时间
		czfdMapper.update(czfd);
	}
	
	public List<CzfdVo> queryCzfdByPage(CzfdParam czfdParam) {
      	//分页
      	PageHelper.startPage(czfdParam.getPageNumber(),czfdParam.getLimit(),false);
		return czfdMapper.queryCzfdForList(czfdParam);
	}
	

	public Czfd selectCzfdByPrimaryKey(Czfd Czfd) {
		return czfdMapper.selectCzfdByPrimaryKey(Czfd);
	}
	
	public long queryTotalCzfds(CzfdParam czfdParam) {
		return czfdMapper.queryTotalCzfds(czfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Czfd> selectForList(Czfd czfd){
		return czfdMapper.selectForList(czfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Czfd czfd) {
		return czfdMapper.selectForUnique(czfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Czfd czfd) {
		if(StringUtils.isBlank(czfd.getId())) {
			this.insert(czfd);
		}else {
			this.updateIgnoreNull(czfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Czfd[] objs) {
		for(Czfd czfd : objs) {
			this.saveOne(czfd);
		}
	}

	@Override
	public void deleteByJbxxId(String jbxxId) {
		czfdMapper.deleteByJbxxId(jbxxId);
	}
}
