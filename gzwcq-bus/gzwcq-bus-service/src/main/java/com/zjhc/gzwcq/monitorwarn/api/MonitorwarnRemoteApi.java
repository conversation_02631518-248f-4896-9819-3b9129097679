package com.zjhc.gzwcq.monitorwarn.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zjhc.gzwcq.monitorwarn.client.MonitorwarnFeignClient;
import com.zjhc.gzwcq.monitorwarn.service.api.IMonitorwarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/monitorwarnRemoteApi")
@Api(value="monitorwarn接口文档",tags="自动预警表")
public class MonitorwarnRemoteApi implements MonitorwarnFeignClient {
  
  	@Autowired
	private IMonitorwarnService monitorwarnService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Monitorwarn
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<MonitorwarnVo> queryByPage(@RequestBody MonitorwarnParam monitorwarnParam) {
        BootstrapTableModel<MonitorwarnVo> model = new BootstrapTableModel<MonitorwarnVo>();
		model.setRows(monitorwarnService.queryMonitorwarnByPage(monitorwarnParam));
		model.setTotal(monitorwarnService.queryTotalMonitorwarns(monitorwarnParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Monitorwarn monitorwarn){
    	monitorwarnService.insert(monitorwarn);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	monitorwarnService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	monitorwarnService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Monitorwarn monitorwarn){
    	monitorwarnService.updateIgnoreNull(monitorwarn);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Monitorwarn monitorwarn){
    	monitorwarnService.update(monitorwarn);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Monitorwarn selectMonitorwarnByPrimaryKey(@RequestBody Monitorwarn monitorwarn){
  		return monitorwarnService.selectMonitorwarnByPrimaryKey(monitorwarn);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Monitorwarn> selectForList(@RequestBody Monitorwarn monitorwarn){
    	return monitorwarnService.selectForList(monitorwarn);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Monitorwarn monitorwarn){
    	return monitorwarnService.validateUniqueParam(monitorwarn);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Monitorwarn monitorwarn){
    	monitorwarnService.saveOne(monitorwarn);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Monitorwarn[] objs){
    	monitorwarnService.multipleSaveAndEdit(objs);
    };

	/**
	 * 全自动预警处理 (发起审核)
	 */
	@ApiOperation(value="全自动预警处理 (发起审核)")
	public String submitWarnReview(@RequestBody Monitorwarn objs){
		return monitorwarnService.submitWarnReview(objs);
	};

	/**
	 * 查询预警审核列表数据
	 */
	@ApiOperation(value="查询预警审核列表数据")
	public BootstrapTableModel<MonitorwarnVo> selectWarnReviewList(@RequestBody MonitorwarnParam param){
		return monitorwarnService.selectWarnReviewList(param);
	}

	@ApiOperation(value="预警读消息")
	public void readMessage(@RequestBody Monitorwarn monitorwarn) {
		monitorwarnService.readMessage(monitorwarn);
	}


}