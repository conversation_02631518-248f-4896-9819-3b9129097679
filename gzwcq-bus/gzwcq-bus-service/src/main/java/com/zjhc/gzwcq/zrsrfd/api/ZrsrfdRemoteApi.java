package com.zjhc.gzwcq.zrsrfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.zrsrfd.client.ZrsrfdFeignClient;
import com.zjhc.gzwcq.zrsrfd.service.api.IZrsrfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdParam;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/zrsrfdRemoteApi")
@Api(value="zrsrfd接口文档",tags="转让受让浮动")
public class ZrsrfdRemoteApi implements ZrsrfdFeignClient {
  
  	@Autowired
	private IZrsrfdService zrsrfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Zrsrfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<ZrsrfdVo> queryByPage(@RequestBody ZrsrfdParam zrsrfdParam) {
        BootstrapTableModel<ZrsrfdVo> model = new BootstrapTableModel<ZrsrfdVo>();
		model.setRows(zrsrfdService.queryZrsrfdByPage(zrsrfdParam));
		model.setTotal(zrsrfdService.queryTotalZrsrfds(zrsrfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Zrsrfd zrsrfd){
    	zrsrfdService.insert(zrsrfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	zrsrfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	zrsrfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Zrsrfd zrsrfd){
    	zrsrfdService.updateIgnoreNull(zrsrfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Zrsrfd zrsrfd){
    	zrsrfdService.update(zrsrfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public ZrsrfdVo selectZrsrfdByPrimaryKey(@RequestBody Zrsrfd zrsrfd){
  		return zrsrfdService.selectZrsrfdByPrimaryKey(zrsrfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Zrsrfd> selectForList(@RequestBody Zrsrfd zrsrfd){
    	return zrsrfdService.selectForList(zrsrfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Zrsrfd zrsrfd){
    	return zrsrfdService.validateUniqueParam(zrsrfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Zrsrfd zrsrfd){
    	zrsrfdService.saveOne(zrsrfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Zrsrfd[] objs){
    	zrsrfdService.multipleSaveAndEdit(objs);
    };
	
}