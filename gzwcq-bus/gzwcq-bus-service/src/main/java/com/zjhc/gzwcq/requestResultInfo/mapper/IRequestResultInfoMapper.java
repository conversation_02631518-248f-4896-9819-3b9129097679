package com.zjhc.gzwcq.requestResultInfo.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfo;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoVo;
import com.zjhc.gzwcq.requestResultInfo.entity.RequestResultInfoParam;
import org.apache.ibatis.annotations.Param;

public interface IRequestResultInfoMapper {
	
	/*保存对象*/
	void insert(RequestResultInfo requestResultInfo);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(RequestResultInfo requestResultInfo);
	
	/**更新*/
	void update(RequestResultInfo requestResultInfo);
	
	/*分页查询对象*/
	List<RequestResultInfoVo> queryRequestResultInfoForList(RequestResultInfoParam requestResultInfoParam);
	
	/*数据总量查询*/
	long queryTotalRequestResultInfos(RequestResultInfoParam requestResultInfoParam);
	
	/*根据主键查询对象*/
	RequestResultInfo selectRequestResultInfoByPrimaryKey(RequestResultInfo requestResultInfo);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<RequestResultInfo> selectForList(RequestResultInfo requestResultInfo);
	
	/**
	 * 数据唯一性验证
	 * */
	List<RequestResultInfo> selectForUnique(RequestResultInfo requestResultInfo);
	
}