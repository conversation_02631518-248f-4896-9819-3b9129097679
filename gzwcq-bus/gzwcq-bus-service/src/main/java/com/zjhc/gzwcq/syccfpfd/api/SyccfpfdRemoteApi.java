package com.zjhc.gzwcq.syccfpfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.syccfpfd.client.SyccfpfdFeignClient;
import com.zjhc.gzwcq.syccfpfd.service.api.ISyccfpfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdParam;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/syccfpfdRemoteApi")
@Api(value="syccfpfd接口文档",tags="剩余财产分配浮动")
public class SyccfpfdRemoteApi implements SyccfpfdFeignClient {
  
  	@Autowired
	private ISyccfpfdService syccfpfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Syccfpfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<SyccfpfdVo> queryByPage(@RequestBody SyccfpfdParam syccfpfdParam) {
        BootstrapTableModel<SyccfpfdVo> model = new BootstrapTableModel<SyccfpfdVo>();
		model.setRows(syccfpfdService.querySyccfpfdByPage(syccfpfdParam));
		model.setTotal(syccfpfdService.queryTotalSyccfpfds(syccfpfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Syccfpfd syccfpfd){
    	syccfpfdService.insert(syccfpfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	syccfpfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	syccfpfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Syccfpfd syccfpfd){
    	syccfpfdService.updateIgnoreNull(syccfpfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Syccfpfd syccfpfd){
    	syccfpfdService.update(syccfpfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
	@ApiOperation(value="根据主键查询")
	public Syccfpfd selectSyccfpfdByPrimaryKey(@RequestBody Syccfpfd syccfpfd){
  		return syccfpfdService.selectSyccfpfdByPrimaryKey(syccfpfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Syccfpfd> selectForList(@RequestBody Syccfpfd syccfpfd){
    	return syccfpfdService.selectForList(syccfpfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Syccfpfd syccfpfd){
    	return syccfpfdService.validateUniqueParam(syccfpfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Syccfpfd syccfpfd){
    	syccfpfdService.saveOne(syccfpfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Syccfpfd[] objs){
    	syccfpfdService.multipleSaveAndEdit(objs);
    };
	
}