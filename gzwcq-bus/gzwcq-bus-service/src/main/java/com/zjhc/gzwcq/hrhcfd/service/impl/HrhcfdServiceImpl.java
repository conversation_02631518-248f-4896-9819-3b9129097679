package com.zjhc.gzwcq.hrhcfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;

import com.boot.iAdmin.cache.core.DictCacheStrategy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.hrhcfd.mapper.IHrhcfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdVo;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdParam;
import com.zjhc.gzwcq.hrhcfd.service.api.IHrhcfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class HrhcfdServiceImpl implements IHrhcfdService {
	
	@Autowired
	private IHrhcfdMapper hrhcfdMapper;
	@Autowired
	private DictCacheStrategy dictCacheStrategy;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		hrhcfdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Hrhcfd hrhcfd){
		hrhcfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		hrhcfd.setCreateTime(new Date());//创建时间
		hrhcfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hrhcfd.setLastUpdateTime(new Date());//更新时间
		hrhcfdMapper.insert(hrhcfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		hrhcfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		hrhcfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Hrhcfd hrhcfd){
		hrhcfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hrhcfd.setLastUpdateTime(new Date());//更新时间
		hrhcfdMapper.updateIgnoreNull(hrhcfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Hrhcfd hrhcfd){
		hrhcfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hrhcfd.setLastUpdateTime(new Date());//更新时间
		hrhcfdMapper.update(hrhcfd);
	}
	
	public List<HrhcfdVo> queryHrhcfdByPage(HrhcfdParam hrhcfdParam) {
      	//分页
      	PageHelper.startPage(hrhcfdParam.getPageNumber(),hrhcfdParam.getLimit(),false);
		return hrhcfdMapper.queryHrhcfdForList(hrhcfdParam);
	}
	

	public Hrhcfd selectHrhcfdByPrimaryKey(Hrhcfd Hrhcfd) {
		return hrhcfdMapper.selectHrhcfdByPrimaryKey(Hrhcfd);
	}
	
	public long queryTotalHrhcfds(HrhcfdParam hrhcfdParam) {
		return hrhcfdMapper.queryTotalHrhcfds(hrhcfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Hrhcfd> selectForList(Hrhcfd hrhcfd){
		return hrhcfdMapper.selectForList(hrhcfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Hrhcfd hrhcfd) {
		return hrhcfdMapper.selectForUnique(hrhcfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Hrhcfd hrhcfd) {
		if(StringUtils.isBlank(hrhcfd.getId())) {
			this.insert(hrhcfd);
		}else {
			this.updateIgnoreNull(hrhcfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Hrhcfd[] objs) {
		for(Hrhcfd hrhcfd : objs) {
			this.saveOne(hrhcfd);
		}
	}
	/**
	 * 根据基本信息表id查数据
	 */
	@Override
	public List<HrhcfdVo> selectByJbxxId(String id) {
		List<HrhcfdVo> vos = hrhcfdMapper.selectByJbxxId(id);
		for(HrhcfdVo vo : vos){
			if(StringUtils.isEmpty(vo.getFd7HcfssgzjgjgStr())
					&& StringUtils.isNotEmpty(vo.getFd7Hcfssgzjgjg())){
				vo.setFd7HcfssgzjgjgStr(dictCacheStrategy.getTextByVal("GZJGJGFD",vo.getFd7Hcfssgzjgjg()));
			}
		}
		return vos;
	}
}
