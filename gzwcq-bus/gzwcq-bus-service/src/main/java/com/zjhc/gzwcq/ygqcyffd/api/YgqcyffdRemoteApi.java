package com.zjhc.gzwcq.ygqcyffd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.ygqcyffd.client.YgqcyffdFeignClient;
import com.zjhc.gzwcq.ygqcyffd.service.api.IYgqcyffdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdParam;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/ygqcyffdRemoteApi")
@Api(value="ygqcyffd接口文档",tags="原股权持有方浮动")
public class YgqcyffdRemoteApi implements YgqcyffdFeignClient {
  
  	@Autowired
	private IYgqcyffdService ygqcyffdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Ygqcyffd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<YgqcyffdVo> queryByPage(@RequestBody YgqcyffdParam ygqcyffdParam) {
        BootstrapTableModel<YgqcyffdVo> model = new BootstrapTableModel<YgqcyffdVo>();
		model.setRows(ygqcyffdService.queryYgqcyffdByPage(ygqcyffdParam));
		model.setTotal(ygqcyffdService.queryTotalYgqcyffds(ygqcyffdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Ygqcyffd ygqcyffd){
    	ygqcyffdService.insert(ygqcyffd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	ygqcyffdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	ygqcyffdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Ygqcyffd ygqcyffd){
    	ygqcyffdService.updateIgnoreNull(ygqcyffd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Ygqcyffd ygqcyffd){
    	ygqcyffdService.update(ygqcyffd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public YgqcyffdVo selectYgqcyffdByPrimaryKey(@RequestBody Ygqcyffd ygqcyffd){
  		return ygqcyffdService.selectYgqcyffdByPrimaryKey(ygqcyffd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Ygqcyffd> selectForList(@RequestBody Ygqcyffd ygqcyffd){
    	return ygqcyffdService.selectForList(ygqcyffd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Ygqcyffd ygqcyffd){
    	return ygqcyffdService.validateUniqueParam(ygqcyffd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Ygqcyffd ygqcyffd){
    	ygqcyffdService.saveOne(ygqcyffd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Ygqcyffd[] objs){
    	ygqcyffdService.multipleSaveAndEdit(objs);
    };
	
}