package com.zjhc.gzwcq.requestErroInfo.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.requestErroInfo.client.RequestErroInfoFeignClient;
import com.zjhc.gzwcq.requestErroInfo.service.api.IRequestErroInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfo;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoParam;
import com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/requestErroInfoRemoteApi")
@Api(value="requestErroInfo接口文档",tags="api_request_erro_info")
public class RequestErroInfoRemoteApi implements RequestErroInfoFeignClient {
  
  	@Autowired
	private IRequestErroInfoService requestErroInfoService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param RequestErroInfo
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<RequestErroInfoVo> queryByPage(@RequestBody RequestErroInfoParam requestErroInfoParam) {
        BootstrapTableModel<RequestErroInfoVo> model = new BootstrapTableModel<RequestErroInfoVo>();
		model.setRows(requestErroInfoService.queryRequestErroInfoByPage(requestErroInfoParam));
		model.setTotal(requestErroInfoService.queryTotalRequestErroInfos(requestErroInfoParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody RequestErroInfo requestErroInfo){
    	requestErroInfoService.insert(requestErroInfo);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	requestErroInfoService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	requestErroInfoService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody RequestErroInfo requestErroInfo){
    	requestErroInfoService.updateIgnoreNull(requestErroInfo);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody RequestErroInfo requestErroInfo){
    	requestErroInfoService.update(requestErroInfo);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public RequestErroInfoVo selectRequestErroInfoByPrimaryKey(@RequestBody RequestErroInfo requestErroInfo){
  		return requestErroInfoService.selectRequestErroInfoByPrimaryKey(requestErroInfo);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<RequestErroInfo> selectForList(@RequestBody RequestErroInfo requestErroInfo){
    	return requestErroInfoService.selectForList(requestErroInfo);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody RequestErroInfo requestErroInfo){
    	return requestErroInfoService.validateUniqueParam(requestErroInfo);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody RequestErroInfo requestErroInfo){
    	requestErroInfoService.saveOne(requestErroInfo);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody RequestErroInfo[] objs){
    	requestErroInfoService.multipleSaveAndEdit(objs);
    };
	
}