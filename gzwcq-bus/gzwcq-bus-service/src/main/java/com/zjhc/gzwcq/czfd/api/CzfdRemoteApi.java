package com.zjhc.gzwcq.czfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.czfd.client.CzfdFeignClient;
import com.zjhc.gzwcq.czfd.service.api.ICzfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdParam;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/czfdRemoteApi")
@Api(value="czfd接口文档",tags="产权出资浮动行")
public class CzfdRemoteApi implements CzfdFeignClient {
  
  	@Autowired
	private ICzfdService czfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Czfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<CzfdVo> queryByPage(@RequestBody CzfdParam czfdParam) {
        BootstrapTableModel<CzfdVo> model = new BootstrapTableModel<CzfdVo>();
		model.setRows(czfdService.queryCzfdByPage(czfdParam));
		model.setTotal(czfdService.queryTotalCzfds(czfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Czfd czfd){
    	czfdService.insert(czfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	czfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	czfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Czfd czfd){
    	czfdService.updateIgnoreNull(czfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Czfd czfd){
    	czfdService.update(czfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Czfd selectCzfdByPrimaryKey(@RequestBody Czfd czfd){
  		return czfdService.selectCzfdByPrimaryKey(czfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Czfd> selectForList(@RequestBody Czfd czfd){
    	return czfdService.selectForList(czfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Czfd czfd){
    	return czfdService.validateUniqueParam(czfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Czfd czfd){
    	czfdService.saveOne(czfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Czfd[] objs){
    	czfdService.multipleSaveAndEdit(objs);
    };
	
}