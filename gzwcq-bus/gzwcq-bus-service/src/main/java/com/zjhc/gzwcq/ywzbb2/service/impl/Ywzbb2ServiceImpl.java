package com.zjhc.gzwcq.ywzbb2.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;

import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.service.api.IAttachmentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.ywzbb2.mapper.IYwzbb2Mapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Param;
import com.zjhc.gzwcq.ywzbb2.service.api.IYwzbb2Service;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;

@Service
public class Ywzbb2ServiceImpl implements IYwzbb2Service {
	
	@Autowired
	private IYwzbb2Mapper ywzbb2Mapper;

	@Autowired
	private IAttachmentService attachmentService;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		ywzbb2Mapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Ywzbb2 ywzbb2){
		ywzbb2.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		ywzbb2.setCreateTime(new Date());//创建时间
		ywzbb2.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ywzbb2.setLastUpdateTime(new Date());//更新时间
		ywzbb2Mapper.insert(ywzbb2);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		ywzbb2Mapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		ywzbb2Mapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Ywzbb2 ywzbb2){
		ywzbb2.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ywzbb2.setLastUpdateTime(new Date());//更新时间
		ywzbb2Mapper.updateIgnoreNull(ywzbb2);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Ywzbb2 ywzbb2){
		ywzbb2.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ywzbb2.setLastUpdateTime(new Date());//更新时间
		ywzbb2Mapper.update(ywzbb2);
	}
	
	public List<Ywzbb2Vo> queryYwzbb2ByPage(Ywzbb2Param ywzbb2Param) {
      	//分页
      	PageHelper.startPage(ywzbb2Param.getPageNumber(),ywzbb2Param.getLimit(),false);
		return ywzbb2Mapper.queryYwzbb2ForList(ywzbb2Param);
	}
	

	public Ywzbb2Vo selectYwzbb2ByPrimaryKey(Ywzbb2 Ywzbb2) {
		return ywzbb2Mapper.selectYwzbb2ByPrimaryKey(Ywzbb2);
	}
	
	public long queryTotalYwzbb2s(Ywzbb2Param ywzbb2Param) {
		return ywzbb2Mapper.queryTotalYwzbb2s(ywzbb2Param);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Ywzbb2> selectForList(Ywzbb2 ywzbb2){
		return ywzbb2Mapper.selectForList(ywzbb2);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Ywzbb2 ywzbb2) {
		return ywzbb2Mapper.selectForUnique(ywzbb2).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Ywzbb2 ywzbb2) {
		if(StringUtils.isBlank(ywzbb2.getId())) {
			this.insert(ywzbb2);
		}else {
			this.updateIgnoreNull(ywzbb2);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Ywzbb2[] objs) {
		for(Ywzbb2 ywzbb2 : objs) {
			this.saveOne(ywzbb2);
		}
	}

	@Override
	public Ywzbb2Vo selectByJbxxId(String jbxxId) {
		Ywzbb2Vo ywzbb2Vo = ywzbb2Mapper.selectByJbxxId(jbxxId);
		if (ywzbb2Vo != null){
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlQsbg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlQsbg());
				ywzbb2Vo.setZlQsbgAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlFlxys())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlFlxys());
				ywzbb2Vo.setZlFlxysAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlGszxzm())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlGszxzm());
				ywzbb2Vo.setZlGszxzmAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlZgdbdhjy())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlZgdbdhjy());
				ywzbb2Vo.setZlZgdbdhjyAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlGqszfawj())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlGqszfawj());
				ywzbb2Vo.setZlGqszfawjAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlZxgg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlZxgg());
				ywzbb2Vo.setZlZxggAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlHbxys())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlHbxys());
				ywzbb2Vo.setZlHbxysAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlJzrsjbg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlJzrsjbg());
				ywzbb2Vo.setZlJzrsjbgAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlZhxy())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlZhxy());
				ywzbb2Vo.setZlZhxyAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlFhbpgba())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlFhbpgba());
				ywzbb2Vo.setZlFhbpgbaAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlZjyqsjbg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlZjyqsjbg());
				ywzbb2Vo.setZlZjyqsjbgAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlPgba())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlPgba());
				ywzbb2Vo.setZlPgbaAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlBdpgba())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlBdpgba());
				ywzbb2Vo.setZlBdpgbaAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlWchzxy())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlWchzxy());
				ywzbb2Vo.setZlWchzxyAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlJzgg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlJzgg());
				ywzbb2Vo.setZlJzggAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlYyzz())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlYyzz());
				ywzbb2Vo.setZlYyzzAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlSjbg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlSjbg());
				ywzbb2Vo.setZlSjbgAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlYzbg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlYzbg());
				ywzbb2Vo.setZlYzbgAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlQyzc())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlQyzc());
				ywzbb2Vo.setZlQyzcAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlGdqkdjb())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlGdqkdjb());
				ywzbb2Vo.setZlGdqkdjbAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlZhypgbab())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlZhypgbab());
				ywzbb2Vo.setZlZhypgbabAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlSyzcczxy())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlSyzcczxy());
				ywzbb2Vo.setZlSyzcczxyAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlTjpgbab())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlTjpgbab());
				ywzbb2Vo.setZlTjpgbabAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlGqzr())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlGqzr());
				ywzbb2Vo.setZlGqzrAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlZhepgbab())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlZhepgbab());
				ywzbb2Vo.setZlZhepgbabAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlXbpgbab())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlXbpgbab());
				ywzbb2Vo.setZlXbpgbabAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlTzxy())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlTzxy());
				ywzbb2Vo.setZlTzxyAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlBxbpgbab())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlBxbpgbab());
				ywzbb2Vo.setZlBxbpgbabAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlGytdba())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlGytdba());
				ywzbb2Vo.setZlGytdbaAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlJcjg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlJcjg());
				ywzbb2Vo.setZlJcjgAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlJcwj())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlJcwj());
				ywzbb2Vo.setZlJcwjAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlPcgg())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlPcgg());
				ywzbb2Vo.setZlPcggAttachment(attachmentService.selectForList(attachment));
			}
			if (StringUtils.isNotBlank(ywzbb2Vo.getZlYxhztzs())){
				Attachment attachment = new Attachment(ywzbb2Vo.getZlYxhztzs());
				ywzbb2Vo.setZlYxhztzsAttachment(attachmentService.selectForList(attachment));
			}
		}
		return ywzbb2Vo;
	}
}
