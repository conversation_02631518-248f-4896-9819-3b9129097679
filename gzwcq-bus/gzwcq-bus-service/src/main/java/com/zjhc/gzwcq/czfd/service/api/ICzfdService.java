package com.zjhc.gzwcq.czfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.czfd.entity.CzfdParam;

public interface ICzfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Czfd czfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Czfd czfd);
	
	/**
	* 更新
	*/
	void update(Czfd czfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<CzfdVo> queryCzfdByPage(CzfdParam czfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalCzfds(CzfdParam czfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Czfd selectCzfdByPrimaryKey(Czfd czfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Czfd> selectForList(Czfd czfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Czfd czfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Czfd czfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Czfd[] objs);

    void deleteByJbxxId(String jbxxId);
}