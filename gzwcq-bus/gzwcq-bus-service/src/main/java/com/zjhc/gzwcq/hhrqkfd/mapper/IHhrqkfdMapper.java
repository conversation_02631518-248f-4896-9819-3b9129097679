package com.zjhc.gzwcq.hhrqkfd.mapper;

import java.util.Map;
import java.util.List;

import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdParam;
import org.apache.ibatis.annotations.Param;

public interface IHhrqkfdMapper {

    /*保存对象*/
    void insert(Hhrqkfd hhrqkfd);

    //物理删除
    void deleteByPrimaryKeys(Map<String, Object> map);

    void deleteByPrimaryKey(@Param("id") String id);

    //逻辑删除
    void logicDeleteByPrimaryKeys(Map<String, Object> map);

    void logicDeleteByPrimaryKey(Map<String, Object> map);

    /*根据非空属性更新对象信息*/
    void updateIgnoreNull(Hhrqkfd hhrqkfd);

    /**
     * 更新
     */
    void update(Hhrqkfd hhrqkfd);

    /*分页查询对象*/
    List<HhrqkfdVo> queryHhrqkfdForList(HhrqkfdParam hhrqkfdParam);

    /*数据总量查询*/
    long queryTotalHhrqkfds(HhrqkfdParam hhrqkfdParam);

    /*根据主键查询对象*/
    Hhrqkfd selectHhrqkfdByPrimaryKey(Hhrqkfd hhrqkfd);

    /*根据部分属性对象查询全部结果，不分页*/
    List<Hhrqkfd> selectForList(Hhrqkfd hhrqkfd);

    /**
     * 按基本信息id查询合伙企业数据
     */
    List<HhrqkfdVo> selectInfoByJbxxId(String jbxxId);

    /**
     * 数据唯一性验证
     */
    List<Hhrqkfd> selectForUnique(Hhrqkfd hhrqkfd);

    List<HhrqkfdVo> selectByJbxxbId(String jbxxId);

    void deleteByJbxxId(String jbxxId);

    List<Hhrqkfd> selectAllByJbxxId(@Param("jbxxId") String jbxxId);
}