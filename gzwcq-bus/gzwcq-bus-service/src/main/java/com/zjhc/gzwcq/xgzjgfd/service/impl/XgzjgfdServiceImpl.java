package com.zjhc.gzwcq.xgzjgfd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.xgzjgfd.mapper.IXgzjgfdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdVo;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdParam;
import com.zjhc.gzwcq.xgzjgfd.service.api.IXgzjgfdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class XgzjgfdServiceImpl implements IXgzjgfdService {
	
	@Autowired
	private IXgzjgfdMapper xgzjgfdMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		xgzjgfdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Xgzjgfd xgzjgfd){
		xgzjgfd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		xgzjgfd.setCreateTime(new Date());//创建时间
		xgzjgfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		xgzjgfd.setLastUpdateTime(new Date());//更新时间
		xgzjgfdMapper.insert(xgzjgfd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		xgzjgfdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		xgzjgfdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Xgzjgfd xgzjgfd){
		xgzjgfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		xgzjgfd.setLastUpdateTime(new Date());//更新时间
		xgzjgfdMapper.updateIgnoreNull(xgzjgfd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Xgzjgfd xgzjgfd){
		xgzjgfd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		xgzjgfd.setLastUpdateTime(new Date());//更新时间
		xgzjgfdMapper.update(xgzjgfd);
	}
	
	public List<XgzjgfdVo> queryXgzjgfdByPage(XgzjgfdParam xgzjgfdParam) {
      	//分页
      	PageHelper.startPage(xgzjgfdParam.getPageNumber(),xgzjgfdParam.getLimit(),false);
		return xgzjgfdMapper.queryXgzjgfdForList(xgzjgfdParam);
	}
	

	public Xgzjgfd selectXgzjgfdByPrimaryKey(Xgzjgfd Xgzjgfd) {
		return xgzjgfdMapper.selectXgzjgfdByPrimaryKey(Xgzjgfd);
	}
	
	public long queryTotalXgzjgfds(XgzjgfdParam xgzjgfdParam) {
		return xgzjgfdMapper.queryTotalXgzjgfds(xgzjgfdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Xgzjgfd> selectForList(Xgzjgfd xgzjgfd){
		return xgzjgfdMapper.selectForList(xgzjgfd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Xgzjgfd xgzjgfd) {
		return xgzjgfdMapper.selectForUnique(xgzjgfd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Xgzjgfd xgzjgfd) {
		if(StringUtils.isBlank(xgzjgfd.getId())) {
			this.insert(xgzjgfd);
		}else {
			this.updateIgnoreNull(xgzjgfd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Xgzjgfd[] objs) {
		for(Xgzjgfd xgzjgfd : objs) {
			this.saveOne(xgzjgfd);
		}
	}
	/**
	 * 根据基本信息表id查数据
	 */
	@Override
	public List<XgzjgfdVo> selectByJbxxId(String id) {
		return xgzjgfdMapper.selectByJbxxId(id);
	}
}
