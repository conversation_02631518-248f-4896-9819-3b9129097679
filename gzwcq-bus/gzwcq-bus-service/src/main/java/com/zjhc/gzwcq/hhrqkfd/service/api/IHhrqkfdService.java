package com.zjhc.gzwcq.hhrqkfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdParam;

public interface IHhrqkfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Hhrqkfd hhrqkfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Hhrqkfd hhrqkfd);
	
	/**
	* 更新
	*/
	void update(Hhrqkfd hhrqkfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<HhrqkfdVo> queryHhrqkfdByPage(HhrqkfdParam hhrqkfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalHhrqkfds(HhrqkfdParam hhrqkfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Hhrqkfd selectHhrqkfdByPrimaryKey(Hhrqkfd hhrqkfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Hhrqkfd> selectForList(Hhrqkfd hhrqkfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Hhrqkfd hhrqkfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Hhrqkfd hhrqkfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Hhrqkfd[] objs);

    List<HhrqkfdVo> selectByJbxxbId(String jbxxId);

    void deleteByJbxxId(String jbxxId);
}