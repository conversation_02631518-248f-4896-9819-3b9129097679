package com.zjhc.gzwcq.fhbzcfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdVo;
import com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdParam;

public interface IFhbzcfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Fhbzcfd fhbzcfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Fhbzcfd fhbzcfd);
	
	/**
	* 更新
	*/
	void update(Fhbzcfd fhbzcfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<FhbzcfdVo> queryFhbzcfdByPage(FhbzcfdParam fhbzcfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalFhbzcfds(FhbzcfdParam fhbzcfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	FhbzcfdVo selectFhbzcfdByPrimaryKey(Fhbzcfd fhbzcfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Fhbzcfd> selectForList(Fhbzcfd fhbzcfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Fhbzcfd fhbzcfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Fhbzcfd fhbzcfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Fhbzcfd[] objs);

	/**
	 * 根据基本信息表id查数据
	 * @param id
	 * @return
	 */
    List<FhbzcfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);
}