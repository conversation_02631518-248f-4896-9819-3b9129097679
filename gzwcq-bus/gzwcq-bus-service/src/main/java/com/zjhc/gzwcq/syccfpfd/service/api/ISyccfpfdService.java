package com.zjhc.gzwcq.syccfpfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdVo;
import com.zjhc.gzwcq.syccfpfd.entity.SyccfpfdParam;

public interface ISyccfpfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Syccfpfd syccfpfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Syccfpfd syccfpfd);
	
	/**
	* 更新
	*/
	void update(Syccfpfd syccfpfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<SyccfpfdVo> querySyccfpfdByPage(SyccfpfdParam syccfpfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalSyccfpfds(SyccfpfdParam syccfpfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Syccfpfd selectSyccfpfdByPrimaryKey(Syccfpfd syccfpfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Syccfpfd> selectForList(Syccfpfd syccfpfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Syccfpfd syccfpfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Syccfpfd syccfpfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Syccfpfd[] objs);

    void deleteByJbxxId(String jbxxId);

    List<SyccfpfdVo> selectByJbxxId(String jbxxId);
}