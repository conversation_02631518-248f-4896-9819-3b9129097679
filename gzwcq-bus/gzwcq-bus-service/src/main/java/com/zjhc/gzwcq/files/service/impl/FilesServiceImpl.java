package com.zjhc.gzwcq.files.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.files.mapper.IFilesMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.files.entity.Files;
import com.zjhc.gzwcq.files.entity.FilesVo;
import com.zjhc.gzwcq.files.entity.FilesParam;
import com.zjhc.gzwcq.files.service.api.IFilesService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class FilesServiceImpl implements IFilesService {
	
	@Autowired
	private IFilesMapper filesMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Files files){
		files.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		files.setCreateTime(new Date());//创建时间
		files.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		files.setLastUpdateTime(new Date());//更新时间
		files.setLookTime(0);
		filesMapper.insert(files);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		filesMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		filesMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Files files){
		files.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		files.setLastUpdateTime(new Date());//更新时间
		filesMapper.updateIgnoreNull(files);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Files files){
		files.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		files.setLastUpdateTime(new Date());//更新时间
		filesMapper.update(files);
	}
	
	public List<FilesVo> queryFilesByPage(FilesParam filesParam) {
      	//分页
      	PageHelper.startPage(filesParam.getPageNumber(),filesParam.getLimit(),false);
		return filesMapper.queryFilesForList(filesParam);
	}
	

	public FilesVo selectFilesByPrimaryKey(Files files) {
		//查看前浏览次数加一
		FilesVo filesVo = filesMapper.selectFilesByPrimaryKey(files);
		/*files.setLookTime(files.getLookTime()+1);
		filesMapper.updateIgnoreNull(files);*/
		return filesVo;
	}
	
	public long queryTotalFiless(FilesParam filesParam) {
		return filesMapper.queryTotalFiless(filesParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Files> selectForList(Files files){
		return filesMapper.selectForList(files);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Files files) {
		return filesMapper.selectForUnique(files).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Files files) {
		if(StringUtils.isBlank(files.getId())) {
			this.insert(files);
		}else {
			this.updateIgnoreNull(files);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Files[] objs) {
		for(Files files : objs) {
			this.saveOne(files);
		}
	}
}
