package com.zjhc.gzwcq.dwtzqkfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdParam;

public interface IDwtzqkfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Dwtzqkfd dwtzqkfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Dwtzqkfd dwtzqkfd);
	
	/**
	* 更新
	*/
	void update(Dwtzqkfd dwtzqkfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<DwtzqkfdVo> queryDwtzqkfdByPage(DwtzqkfdParam dwtzqkfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalDwtzqkfds(DwtzqkfdParam dwtzqkfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Dwtzqkfd selectDwtzqkfdByPrimaryKey(Dwtzqkfd dwtzqkfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Dwtzqkfd> selectForList(Dwtzqkfd dwtzqkfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Dwtzqkfd dwtzqkfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Dwtzqkfd dwtzqkfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Dwtzqkfd[] objs);

    List<DwtzqkfdVo> selectByJbxxbId(String jbxxId);

    void deleteByJbxxId(String jbxxId);
}