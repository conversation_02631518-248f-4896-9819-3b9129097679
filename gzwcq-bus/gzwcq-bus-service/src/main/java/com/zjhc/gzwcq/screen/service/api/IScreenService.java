package com.zjhc.gzwcq.screen.service.api;

import com.boot.IAdmin.dict.model.DictionaryVo;

import java.util.List;
import java.util.Map;

public interface IScreenService {

    /**
     * 待办事项数据统计
     */
    Map<String, Integer> todoList();

    /**
     * 企业户数统计(按组织形式)
     */
    List<Map<String,Object>> companyNumByZzxs(String unitId);
    /**
    * @description: TODO 企业户数统计(按组织形式) 新
    * @author: hhy
    * @date: 2025/7/25 17:46
    * @param unitId 参数说明
    * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
    */
    List<Map<String,Object>> companyNumByZzxsNew(String unitId);

    /**
     * 企业户数统计(按组织形式)V2 - 直接从字典获取组织形式名称
     * <AUTHOR>
     */
    List<Map<String,Object>> companyNumByZzxsV2(String unitId);

    /**
     * 企业户数统计(按企业级次)
     */
    List<Map<String, Object>> companyNumByLevel(String unitId);

    /**
     * 企业户数统计(按季度及企业类别)
     */
    List<Map<String, Object>> companyNumBySeason(String unitId,String year);

    /**
     * 下拉组织树
     */
    List<DictionaryVo> loadOrgs();

    /**
     * 业务考核
     * @return
     */
    Map<String,Object> businessAssessment(String type,String year);
}
