package com.zjhc.gzwcq.attachment.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.entity.AttachmentVo;
import com.zjhc.gzwcq.attachment.entity.AttachmentParam;
import org.springframework.web.multipart.MultipartFile;

public interface IAttachmentService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Attachment attachment);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Attachment attachment);
	
	/**
	* 更新
	*/
	void update(Attachment attachment);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<AttachmentVo> queryAttachmentByPage(AttachmentParam attachmentParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalAttachments(AttachmentParam attachmentParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Attachment selectAttachmentByPrimaryKey(Attachment attachment);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Attachment> selectForList(Attachment attachment);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Attachment attachment);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Attachment attachment);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Attachment[] objs);

	/**
	 * 上传文件
	 */
	Attachment uploadFile(MultipartFile file);
}