package com.zjhc.gzwcq.apiCallLogs.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.apiCallLogs.mapper.IApiCallLogsMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogsVo;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogsParam;
import com.zjhc.gzwcq.apiCallLogs.service.api.IApiCallLogsService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class ApiCallLogsServiceImpl implements IApiCallLogsService {
	
	@Autowired
	private IApiCallLogsMapper apiCallLogsMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(ApiCallLogs apiCallLogs){
		apiCallLogs.setCreateTime(new Date());//创建时间
		apiCallLogs.setLastUpdateTime(new Date());//更新时间
		apiCallLogsMapper.insert(apiCallLogs);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		apiCallLogsMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		apiCallLogsMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(ApiCallLogs apiCallLogs){
		apiCallLogs.setLastUpdateTime(new Date());//更新时间
		apiCallLogsMapper.updateIgnoreNull(apiCallLogs);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(ApiCallLogs apiCallLogs){
		apiCallLogs.setLastUpdateTime(new Date());//更新时间
		apiCallLogsMapper.update(apiCallLogs);
	}
	
	public List<ApiCallLogsVo> queryApiCallLogsByPage(ApiCallLogsParam apiCallLogsParam) {
      	//分页
      	PageHelper.startPage(apiCallLogsParam.getPageNumber(),apiCallLogsParam.getLimit(),false);
		return apiCallLogsMapper.queryApiCallLogsForList(apiCallLogsParam);
	}
	

	public ApiCallLogs selectApiCallLogsByPrimaryKey(ApiCallLogs ApiCallLogs) {
		return apiCallLogsMapper.selectApiCallLogsByPrimaryKey(ApiCallLogs);
	}
	
	public long queryTotalApiCallLogss(ApiCallLogsParam apiCallLogsParam) {
		return apiCallLogsMapper.queryTotalApiCallLogss(apiCallLogsParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<ApiCallLogs> selectForList(ApiCallLogs apiCallLogs){
		return apiCallLogsMapper.selectForList(apiCallLogs);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(ApiCallLogs apiCallLogs) {
		return apiCallLogsMapper.selectForUnique(apiCallLogs).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(ApiCallLogs apiCallLogs) {
      	if(apiCallLogs.getId() == null) {
			this.insert(apiCallLogs);
		}else {
			this.updateIgnoreNull(apiCallLogs);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(ApiCallLogs[] objs) {
		for(ApiCallLogs apiCallLogs : objs) {
			this.saveOne(apiCallLogs);
		}
	}
}
