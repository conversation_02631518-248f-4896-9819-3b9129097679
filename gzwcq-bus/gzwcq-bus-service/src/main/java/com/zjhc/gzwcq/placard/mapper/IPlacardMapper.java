package com.zjhc.gzwcq.placard.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.placard.entity.Placard;
import com.zjhc.gzwcq.placard.entity.PlacardVo;
import com.zjhc.gzwcq.placard.entity.PlacardParam;
import org.apache.ibatis.annotations.Param;

public interface IPlacardMapper {
	
	/*保存对象*/
	void insert(Placard placard);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Placard placard);
	
	/**更新*/
	void update(Placard placard);
	
	/*分页查询对象*/
	List<PlacardVo> queryPlacardForList(PlacardParam placardParam);
	
	/*数据总量查询*/
	long queryTotalPlacards(PlacardParam placardParam);
	
	/*根据主键查询对象*/
	PlacardVo selectPlacardByPrimaryKey(Placard placard);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Placard> selectForList(Placard placard);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Placard> selectForUnique(Placard placard);
	
}