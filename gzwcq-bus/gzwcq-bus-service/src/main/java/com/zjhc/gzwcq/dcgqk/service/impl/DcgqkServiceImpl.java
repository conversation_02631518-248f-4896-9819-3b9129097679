package com.zjhc.gzwcq.dcgqk.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.dcgqk.mapper.IDcgqkMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkVo;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkParam;
import com.zjhc.gzwcq.dcgqk.service.api.IDcgqkService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class DcgqkServiceImpl implements IDcgqkService {
	
	@Autowired
	private IDcgqkMapper dcgqkMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Dcgqk dcgqk){
		dcgqk.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		dcgqk.setCreateTime(new Date());//创建时间
		dcgqk.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		dcgqk.setLastUpdateTime(new Date());//更新时间
		dcgqkMapper.insert(dcgqk);
	}

	@Override
	public BootstrapTableModel<DcgqkVo> loadByJbxxbId(DcgqkParam dcgqkParam) {
		BootstrapTableModel<DcgqkVo> vo = new BootstrapTableModel<DcgqkVo>();
		List<DcgqkVo> dcgqkVos = dcgqkMapper.selectAllByJbxxId(dcgqkParam.getJbxxId());
		vo.setRows(dcgqkVos);
		return vo;
	}

	@Override
	public void deletebyJbxxbId(String id) {
		dcgqkMapper.deletebyJbxxbId(id);
	}

	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		dcgqkMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		dcgqkMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Dcgqk dcgqk){
		dcgqk.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		dcgqk.setLastUpdateTime(new Date());//更新时间
		dcgqkMapper.updateIgnoreNull(dcgqk);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Dcgqk dcgqk){
		dcgqk.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		dcgqk.setLastUpdateTime(new Date());//更新时间
		dcgqkMapper.update(dcgqk);
	}
	
	public List<DcgqkVo> queryDcgqkByPage(DcgqkParam dcgqkParam) {
      	//分页
      	PageHelper.startPage(dcgqkParam.getPageNumber(),dcgqkParam.getLimit(),false);
		return dcgqkMapper.queryDcgqkForList(dcgqkParam);
	}
	

	public Dcgqk selectDcgqkByPrimaryKey(Dcgqk Dcgqk) {
		return dcgqkMapper.selectDcgqkByPrimaryKey(Dcgqk);
	}
	
	public long queryTotalDcgqks(DcgqkParam dcgqkParam) {
		return dcgqkMapper.queryTotalDcgqks(dcgqkParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Dcgqk> selectForList(Dcgqk dcgqk){
		return dcgqkMapper.selectForList(dcgqk);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Dcgqk dcgqk) {
		return dcgqkMapper.selectForUnique(dcgqk).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Dcgqk dcgqk) {
		if(StringUtils.isBlank(dcgqk.getId())) {
			this.insert(dcgqk);
		}else {
			this.updateIgnoreNull(dcgqk);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Dcgqk[] objs) {
		for(Dcgqk dcgqk : objs) {
			this.saveOne(dcgqk);
		}
	}
}
