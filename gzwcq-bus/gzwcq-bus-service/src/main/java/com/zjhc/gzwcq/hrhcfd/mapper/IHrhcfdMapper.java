package com.zjhc.gzwcq.hrhcfd.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdVo;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdParam;
import org.apache.ibatis.annotations.Param;

public interface IHrhcfdMapper {
	
	/*保存对象*/
	void insert(Hrhcfd hrhcfd);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Hrhcfd hrhcfd);
	
	/**更新*/
	void update(Hrhcfd hrhcfd);
	
	/*分页查询对象*/
	List<HrhcfdVo> queryHrhcfdForList(HrhcfdParam hrhcfdParam);
	
	/*数据总量查询*/
	long queryTotalHrhcfds(HrhcfdParam hrhcfdParam);
	
	/*根据主键查询对象*/
	Hrhcfd selectHrhcfdByPrimaryKey(Hrhcfd hrhcfd);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Hrhcfd> selectForList(Hrhcfd hrhcfd);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Hrhcfd> selectForUnique(Hrhcfd hrhcfd);
	/**
	 * 根据基本信息表id查数据
	 */
    List<HrhcfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);

	List<Hrhcfd> selectAllByJbxxId(@Param("jbxxid")String jbxxId);
}