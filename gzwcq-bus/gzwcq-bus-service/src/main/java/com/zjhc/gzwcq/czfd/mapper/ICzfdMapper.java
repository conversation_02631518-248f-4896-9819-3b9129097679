package com.zjhc.gzwcq.czfd.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.czfd.entity.CzfdParam;
import org.apache.ibatis.annotations.Param;

public interface ICzfdMapper {
	
	/*保存对象*/
	void insert(Czfd czfd);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Czfd czfd);
	
	/**更新*/
	void update(Czfd czfd);
	
	/*分页查询对象*/
	List<CzfdVo> queryCzfdForList(CzfdParam czfdParam);
	
	/*数据总量查询*/
	long queryTotalCzfds(CzfdParam czfdParam);
	
	/*根据主键查询对象*/
	Czfd selectCzfdByPrimaryKey(Czfd czfd);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Czfd> selectForList(Czfd czfd);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Czfd> selectForUnique(Czfd czfd);

    List<CzfdVo> selectByJbxxId(String jbxxId);

    void deleteByJbxxId(String jbxxId);

	/**
	 * 根据id 查询出资人数据
	 * @param jbxxId
	 * @return
	 */
	List<Czfd> selectAllByJbxxId(@Param("jbxxId") String jbxxId);
}