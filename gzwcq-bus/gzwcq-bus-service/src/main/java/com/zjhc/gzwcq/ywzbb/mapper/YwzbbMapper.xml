<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.ywzbb.mapper.IYwzbbMapper">

	<resultMap type="com.zjhc.gzwcq.ywzbb.entity.Ywzbb" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="xx_pgjzcz" property="xxPgjzcz"/>
		<result column="xx_sjjzcz" property="xxSjjzcz"/>
		<result column="xx_fxjg" property="xxFxjg"/>
		<result column="zl_jcwj_ly" property="zlJcwjLy"/>
		<result column="zl_yzbg_zjjgmc" property="zlYzbgZjjgmc"/>
		<result column="zl_yzbg_yzbgh" property="zlYzbgYzbgh"/>
		<result column="zl_yzbg_ly" property="zlYzbgLy"/>
		<result column="zl_yzbg_yzcjr" property="zlYzbgYzcjr"/>
		<result column="zl_qyzc_ly" property="zlQyzcLy"/>
		<result column="zl_yyzz_ly" property="zlYyzzLy"/>
		<result column="zl_pgba_ly" property="zlPgbaLy"/>
		<result column="zl_fhbpgba_ly" property="zlFhbpgbaLy"/>
		<result column="zl_bdpgba_ly" property="zlBdpgbaLy"/>
		<result column="zl_gytdba_ly" property="zlGytdbaLy"/>
		<result column="zl_jcjg_jyjg" property="zlJcjgJyjg"/>
		<result column="zl_jcjg_jgd" property="zlJcjgJgd"/>
		<result column="zl_jcjg_cjrq" property="zlJcjgCjrq"/>
		<result column="zl_jcjg_ly" property="zlJcjgLy"/>
		<result column="zl_sjbg_zjjg" property="zlSjbgZjjg"/>
		<result column="zl_sjbg_bgh" property="zlSjbgBgh"/>
		<result column="zl_sjbg_cjrq" property="zlSjbgCjrq"/>
		<result column="zl_sjbg_ly" property="zlSjbgLy"/>
		<result column="zl_gqzr_ly" property="zlGqzrLy"/>
		<result column="zl_zhxy_ly" property="zlZhxyLy"/>
		<result column="zl_hbxys_ly" property="zlHbxysLy"/>
		<result column="zl_flxys_ly" property="zlFlxysLy"/>
		<result column="zl_wchzxy_ly" property="zlWchzxyLy"/>
		<result column="zl_jzrsjbg_zjjgmc" property="zlJzrsjbgZjjgmc"/>
		<result column="zl_jzrsjbg_yzbgh" property="zlJzrsjbgYzbgh"/>
		<result column="zl_jzrsjbg_ly" property="zlJzrsjbgLy"/>
		<result column="zl_jzgg_mtmc" property="zlJzggMtmc"/>
		<result column="zl_jzgg_ggrq" property="zlJzggGgrq"/>
		<result column="zl_jzgg_ly" property="zlJzggLy"/>
		<result column="xx_cjfmc" property="xxCjfmc"/>
		<result column="xx_sfzj" property="xxSfzj"/>
		<result column="zl_zjyqsjbg_zjjg" property="zlZjyqsjbgZjjg"/>
		<result column="zl_zjyqsjbg_bgh" property="zlZjyqsjbgBgh"/>
		<result column="zl_zjyqsjbg_ly" property="zlZjyqsjbgLy"/>
		<result column="zl_tzxy_ly" property="zlTzxyLy"/>
		<result column="xx_qsjzcz" property="xxQsjzcz"/>
		<result column="xx_qsfy" property="xxQsfy"/>
		<result column="xx_qczw" property="xxQczw"/>
		<result column="xx_pcccze" property="xxPcccze"/>
		<result column="xx_pcfyhgyzw" property="xxPcfyhgyzw"/>
		<result column="xx_ptzw" property="xxPtzw"/>
		<result column="zl_qsbg_zjjgmc" property="zlQsbgZjjgmc"/>
		<result column="zl_qsbg_bgh" property="zlQsbgBgh"/>
		<result column="zl_qsbg_ly" property="zlQsbgLy"/>
		<result column="zl_zxgg_mtmc" property="zlZxggMtmc"/>
		<result column="zl_zxgg_ggrq" property="zlZxggGgrq"/>
		<result column="zl_zxgg_ly" property="zlZxggLy"/>
		<result column="zl_gszxzm_ly" property="zlGszxzmLy"/>
		<result column="zl_pcgg_mtmc" property="zlPcggMtmc"/>
		<result column="zl_pcgg_ggrq" property="zlPcggGgrq"/>
		<result column="zl_pcgg_ly" property="zlPcggLy"/>
		<result column="zl_tzxy_yw" property="zlTzxyYw"/>
		<result column="zl_jcwj_yw" property="zlJcwjYw"/>
		<result column="zl_jzgg_yw" property="zlJzggYw"/>
		<result column="zl_pcgg_yw" property="zlPcggYw"/>
		<result column="zl_jzrsjbg_yw" property="zlJzrsjbgYw"/>
		<result column="zl_qyzc_yw" property="zlQyzcYw"/>
		<result column="zl_jcjg_yw" property="zlJcjgYw"/>
		<result column="zl_sjbg_yw" property="zlSjbgYw"/>
		<result column="zl_fhbpgba_yw" property="zlFhbpgbaYw"/>
		<result column="zl_yyzz_yw" property="zlYyzzYw"/>
		<result column="zl_pgba_yw" property="zlPgbaYw"/>
		<result column="zl_gszxzm_yw" property="zlGszxzmYw"/>
		<result column="zl_gytdba_yw" property="zlGytdbaYw"/>
		<result column="zl_yzbg_yw" property="zlYzbgYw"/>
		<result column="zl_hbxys_yw" property="zlHbxysYw"/>
		<result column="zl_zhxy_yw" property="zlZhxyYw"/>
		<result column="zl_gqzr_yw" property="zlGqzrYw"/>
		<result column="zl_wchzxy_yw" property="zlWchzxyYw"/>
		<result column="zl_bdpgba_yw" property="zlBdpgbaYw"/>
		<result column="zl_zjyqsjbg_yw" property="zlZjyqsjbgYw"/>
		<result column="zl_flxys_yw" property="zlFlxysYw"/>
		<result column="zl_zxgg_yw" property="zlZxggYw"/>
		<result column="zl_qsbg_yw" property="zlQsbgYw"/>
		<result column="xx_bdqyxz" property="xxBdqyxz"/>
		<result column="xx_bxbfxz" property="xxBxbfxz"/>
		<result column="xx_jsyy" property="xxJsyy"/>
		<result column="zl_gszxzm_zxrq" property="zlGszxzmZxrq"/>
		<result column="zl_jcwj_dwmc" property="zlJcwjDwmc"/>
		<result column="zl_jcwj_wjmc" property="zlJcwjWjmc"/>
		<result column="zl_jcwj_wjh" property="zlJcwjWjh"/>
		<result column="zl_pgba_zjjgmc" property="zlPgbaZjjgmc"/>
		<result column="zl_pgba_pgbgh" property="zlPgbaPgbgh"/>
		<result column="zl_pgba_hzdwmc" property="zlPgbaHzdwmc"/>
		<result column="zl_pgba_hzwjh" property="zlPgbaHzwjh"/>
		<result column="zl_fhbpgba_zjjgmc" property="zlFhbpgbaZjjgmc"/>
		<result column="zl_fhbpgba_pgbgh" property="zlFhbpgbaPgbgh"/>
		<result column="zl_fhbpgba_hzdwmc" property="zlFhbpgbaHzdwmc"/>
		<result column="zl_fhbpgba_hzwjh" property="zlFhbpgbaHzwjh"/>
		<result column="zl_bdpgba_zjjgmc" property="zlBdpgbaZjjgmc"/>
		<result column="zl_bdpgba_pgbgh" property="zlBdpgbaPgbgh"/>
		<result column="zl_bdpgba_hzdwmc" property="zlBdpgbaHzdwmc"/>
		<result column="zl_bdpgba_hzwjh" property="zlBdpgbaHzwjh"/>
		<result column="zl_gytdba_pzdw" property="zlGytdbaPzdw"/>
		<result column="zl_gytdba_pzwh" property="zlGytdbaPzwh"/>
		<result column="zl_ywblsqwj" property="zlYwblsqwj"/>
		<result column="zl_zhypgbab_yw" property="zlZhypgbabYw"/>
		<result column="zl_zhypgbab_zjjgmc" property="zlZhypgbabZjjgmc"/>
		<result column="zl_zhypgbab_pgbgh" property="zlZhypgbabPgbgh"/>
		<result column="zl_zhypgbab_hzdwmc" property="zlZhypgbabHzdwmc"/>
		<result column="zl_zhypgbab_hzwjh" property="zlZhypgbabHzwjh"/>
		<result column="zl_zhypgbab_ly" property="zlZhypgbabLy"/>
		<result column="zl_zhepgbab_yw" property="zlZhepgbabYw"/>
		<result column="zl_zhepgbab_zjjgmc" property="zlZhepgbabZjjgmc"/>
		<result column="zl_zhepgbab_pgbgh" property="zlZhepgbabPgbgh"/>
		<result column="zl_zhepgbab_hzdwmc" property="zlZhepgbabHzdwmc"/>
		<result column="zl_zhepgbab_hzwjh" property="zlZhepgbabHzwjh"/>
		<result column="zl_zhepgbab_ly" property="zlZhepgbabLy"/>
		<result column="zl_xbpgbab_yw" property="zlXbpgbabYw"/>
		<result column="zl_xbpgbab_zjjgmc" property="zlXbpgbabZjjgmc"/>
		<result column="zl_xbpgbab_pgbgh" property="zlXbpgbabPgbgh"/>
		<result column="zl_xbpgbab_hzdwmc" property="zlXbpgbabHzdwmc"/>
		<result column="zl_xbpgbab_hzwjh" property="zlXbpgbabHzwjh"/>
		<result column="zl_xbpgbab_ly" property="zlXbpgbabLy"/>
		<result column="zl_bxbpgbab_yw" property="zlBxbpgbabYw"/>
		<result column="zl_bxbpgbab_zjjgmc" property="zlBxbpgbabZjjgmc"/>
		<result column="zl_bxbpgbab_pgbgh" property="zlBxbpgbabPgbgh"/>
		<result column="zl_bxbpgbab_hzdwmc" property="zlBxbpgbabHzdwmc"/>
		<result column="zl_bxbpgbab_hzwjh" property="zlBxbpgbabHzwjh"/>
		<result column="zl_bxbpgbab_ly" property="zlBxbpgbabLy"/>
		<result column="zl_tjpgbab_yw" property="zlTjpgbabYw"/>
		<result column="zl_tjpgbab_zjjgmc" property="zlTjpgbabZjjgmc"/>
		<result column="zl_tjpgbab_pgbgh" property="zlTjpgbabPgbgh"/>
		<result column="zl_tjpgbab_hzdwmc" property="zlTjpgbabHzdwmc"/>
		<result column="zl_tjpgbab_hzwjh" property="zlTjpgbabHzwjh"/>
		<result column="zl_tjpgbab_ly" property="zlTjpgbabLy"/>
		<result column="zl_syzcczxy_yw" property="zlSyzcczxyYw"/>
		<result column="zl_syzcczxy_ly" property="zlSyzcczxyLy"/>
		<result column="zl_zgdbdhjy_yw" property="zlZgdbdhjyYw"/>
		<result column="zl_zgdbdhjy_yj" property="zlZgdbdhjyYj"/>
		<result column="zl_zgdbdhjy_ly" property="zlZgdbdhjyLy"/>
		<result column="zl_gqszfawj_yw" property="zlGqszfawjYw"/>
		<result column="zl_gqszfawj_pzdw" property="zlGqszfawjPzdw"/>
		<result column="zl_gqszfawj_pzwh" property="zlGqszfawjPzwh"/>
		<result column="zl_gqszfawj_ly" property="zlGqszfawjLy"/>
		<result column="zl_gdqkdjb_yw" property="zlGdqkdjbYw"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	<resultMap type="com.zjhc.gzwcq.ywzbb.entity.Ywzbb12VO" id="selectAllByjbxxIdMapper" extends="baseResultMap">
		<result column="zl_gdqkdjb_ly" property="zlGdqkdjbLy"/>
		<result column="zl_qt" property="zlQt"/>
		<result column="zl_gszxzm_gsbmmc" property="zlGszxzmGsbmmc"/>
		<result column="xx_ywfhbcz" property="xxYwfhbcz"/>
		<result column="xx_ywfhbzfsgjk" property="xxYwfhbzfsgjk"/>
		<result column="xx_bdqypgjzcz" property="xxBdqypgjzcz"/>
		<result column="xx_bz" property="xxBz"/>
		<result column="xx_bdqysjjzcz" property="xxBdqysjjzcz"/>
		<result column="xx_zhyqymc" property="xxZhyqymc"/>
		<result column="xx_zhyssgzjgjg" property="xxZhyssgzjgjg"/>
		<result column="xx_zhyssgjczqy" property="xxZhyssgjczqy"/>
		<result column="xx_zhyyyzhdzcpgz" property="xxZhyyyzhdzcpgz"/>
		<result column="xx_zhybz" property="xxZhybz"/>
		<result column="xx_zheqymc" property="xxZheqymc"/>
		<result column="xx_zhessgzjgjg" property="xxZhessgzjgjg"/>
		<result column="xx_zhessgjczqy" property="xxZhessgjczqy"/>
		<result column="xx_zheyyzhdzcpgz" property="xxZheyyzhdzcpgz"/>
		<result column="xx_zhebz" property="xxZhebz"/>
		<result column="xx_azfyze" property="xxAzfyze"/>
		<result column="xx_gyqy" property="xxGyqy"/>
		<result column="xx_bxbfmc" property="xxBxbfmc"/>
		<result column="xx_bxbfpgjzcz" property="xxBxbfpgjzcz"/>
		<result column="xx_xbfpgjzcz" property="xxXbfpgjzcz"/>
		<result column="xx_bflqypgjzcz" property="xxBflqypgjzcz"/>
		<result column="xx_cxqypgjzcz" property="xxCxqypgjzcz"/>
		<result column="xx_cxqyzgbz" property="xxCxqyzgbz"/>
		<result column="xx_yytzdgqpgz" property="xxYytzdgqpgz"/>
		<result column="xx_yytzdgqzj" property="xxYytzdgqzj"/>
		<result column="xx_syjzcczsr" property="xxSyjzcczsr"/>
		<result column="xx_cjfssgzjgjg" property="xxCjfssgzjgjg"/>
		<result column="xx_bdqymc" property="xxBdqymc"/>
		<result column="xx_bdqyssgzjgjg" property="xxBdqyssgzjgjg"/>
		<result column="xx_qsfpbc" property="xxQsfpbc"/>
		<result column="xx_qsfpsqsk" property="xxQsfpsqsk"/>
		<result column="xx_qsccsfzyqczw" property="xxQsccsfzyqczw"/>
		<result column="xx_pcfpbc" property="xxPcfpbc"/>
		<result column="xx_pcfpsqsk" property="xxPcfpsqsk"/>
		<result column="xx_ywfhbjz" property="xxYwfhbjz"/>
		<result column="fd1_czzjhj" property="fd1Czzjhj"/>
		<result column="fd1_pgzhj" property="fd1Pgzhj"/>
		<result column="fd1_zfzjhj" property="fd1Zfzjhj"/>
		<result column="fd1_jzzjhj" property="fd1Jzzjhj"/>
		<result column="fd2_sgjghj" property="fd2Sgjghj"/>
		<result column="fd2_sjjzczhj" property="fd2Sjjzczhj"/>
		<result column="fd2_pgjzczhj" property="fd2Pgjzczhj"/>
		<result column="fd4_zgslhj" property="fd4Zgslhj"/>
		<result column="fd5_sfzjhj" property="fd5Sfzjhj"/>
		<result column="fd6_syccfpjghj" property="fd6Syccfpjghj"/>
		<result column="fd7_hzgqblhj" property="fd7Hzgqblhj"/>
		<result column="fd7_hzjzczhj" property="fd7Hzjzczhj"/>
		<result column="fd8_srgqsjjzczhj" property="fd8Srgqsjjzczhj"/>
		<result column="fd8_srgqpgjzczhj" property="fd8Srgqpgjzczhj"/>
		<result column="fd8_cjjhj" property="fd8Cjjhj"/>
		<result column="fd8_zrgqblhj" property="fd8Zrgqblhj"/>
		<result column="fd8_zrgqsjjzczhj" property="fd8Zrgqsjjzczhj"/>
		<result column="fd8_zrgqpgjzczhj" property="fd8Zrgqpgjzczhj"/>
		<result column="fd8_srgqblhj" property="fd8Srgqblhj"/>
		<result column="fd2_sggqblhj" property="fd2Sggqblhj"/>
		<result column="xx_fxgs" property="xxFxgs"/>
		<result column="xx_gkfxgs" property="xxGkfxgs"/>
		<result column="xx_azryzs" property="xxAzryzs"/>
		<result column="xx_bxbfjzczhxbfgqbl" property="xxBxbfjzczhxbfgqbl"/>
		<result column="xx_yytzgqzhbdqygqbl" property="xxYytzgqzhbdqygqbl"/>
		<result column="zl_fj" property="zlFj"/>
		<result column="xx_bdqypgjzcz_bzz" property="xxBdqypgjzczBzz"/>
		<result column="xx_bdqypgjzcz_bjz" property="xxBdqypgjzczBjz"/>
		<result column="xx_bdqypgjzcz_bgz" property="xxBdqypgjzczBgz"/>
		<result column="xx_bdqysjjzcz_bgz" property="xxBdqysjjzczBgz"/>
		<result column="xx_zgbz_xzgb" property="xxZgbzXzgb"/>
		<result column="xx_zgbz_js" property="xxZgbzJs"/>
		<result column="xx_zgbz_tzxz" property="xxZgbzTzxz"/>
		<result column="xx_zgbz_xshb" property="xxZgbzXshb"/>
		<result column="xx_zgbz_gqcz" property="xxZgbzGqcz"/>
		<result column="zl_yxhztzs_yw" property="zlYxhztzsYw"/>
		<result column="zl_yxhztzs_ly" property="zlYxhztzsLy"/>
		<result column="zl_yxhztzs_hzdw" property="zlYxhztzsHzdw"/>
		<result column="jc_30sspj" property="jc30sspj"/>
		<result column="jc_mgjzcz" property="jcMgjzcz"/>
		<result column="jc_jcgs" property="jcJcgs"/>
		<result column="jc_jcjj" property="jcJcjj"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
		<result column="zl_qsbg" property="zlQsbg"/>
		<result column="zl_flxys" property="zlFlxys"/>
		<result column="zl_gszxzm" property="zlGszxzm"/>
		<result column="zl_zgdbdhjy" property="zlZgdbdhjy"/>
		<result column="zl_gqszfawj" property="zlGqszfawj"/>
		<result column="zl_zxgg" property="zlZxgg"/>
		<result column="zl_hbxys" property="zlHbxys"/>
		<result column="zl_jzrsjbg" property="zlJzrsjbg"/>
		<result column="zl_zhxy" property="zlZhxy"/>
		<result column="zl_fhbpgba" property="zlFhbpgba"/>
		<result column="zl_zjyqsjbg" property="zlZjyqsjbg"/>
		<result column="zl_pgba" property="zlPgba"/>
		<result column="zl_bdpgba" property="zlBdpgba"/>
		<result column="zl_wchzxy" property="zlWchzxy"/>
		<result column="zl_jzgg" property="zlJzgg"/>
		<result column="zl_yyzz" property="zlYyzz"/>
		<result column="zl_sjbg" property="zlSjbg"/>
		<result column="zl_yzbg" property="zlYzbg"/>
		<result column="zl_qyzc" property="zlQyzc"/>
		<result column="zl_gdqkdjb" property="zlGdqkdjb"/>
		<result column="zl_zhypgbab" property="zlZhypgbab"/>
		<result column="zl_syzcczxy" property="zlSyzcczxy"/>
		<result column="zl_tjpgbab" property="zlTjpgbab"/>
		<result column="zl_gqzr" property="zlGqzr"/>
		<result column="zl_zhepgbab" property="zlZhepgbab"/>
		<result column="zl_xbpgbab" property="zlXbpgbab"/>
		<result column="zl_tzxy" property="zlTzxy"/>
		<result column="zl_bxbpgbab" property="zlBxbpgbab"/>
		<result column="zl_gytdba" property="zlGytdba"/>
		<result column="zl_jcjg" property="zlJcjg"/>
		<result column="zl_jcwj" property="zlJcwj"/>
		<result column="zl_pcgg" property="zlPcgg"/>
		<result column="zl_yxhztzs" property="zlYxhztzs"/>
	</resultMap>
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.ywzbb.entity.YwzbbVo" extends="baseResultMap">
		<result column="zlTzxyYwStr" property="zlTzxyYwStr"/>
		<result column="zlJcwjYwStr" property="zlJcwjYwStr"/>
		<result column="zlJzggYwStr" property="zlJzggYwStr"/>
		<result column="zlPcggYwStr" property="zlPcggYwStr"/>
		<result column="zlJzrsjbgYwStr" property="zlJzrsjbgYwStr"/>
		<result column="zlQyzcYwStr" property="zlQyzcYwStr"/>
		<result column="zlJcjgYwStr" property="zlJcjgYwStr"/>
		<result column="zlSjbgYwStr" property="zlSjbgYwStr"/>
		<result column="zlFhbpgbaYwStr" property="zlFhbpgbaYwStr"/>
		<result column="zlYyzzYwStr" property="zlYyzzYwStr"/>
		<result column="zlPgbaYwStr" property="zlPgbaYwStr"/>
		<result column="zlGszxzmYwStr" property="zlGszxzmYwStr"/>
		<result column="zlGytdbaYwStr" property="zlGytdbaYwStr"/>
		<result column="zlYzbgYwStr" property="zlYzbgYwStr"/>
		<result column="xxBdqyxzStr" property="xxBdqyxzStr"/>
		<result column="xxBxbfxzStr" property="xxBxbfxzStr"/>
		<result column="zlZgdbdhjyYjStr" property="zlZgdbdhjyYjStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		xx_pgjzcz, 
		xx_sjjzcz, 
		xx_fxjg, 
		zl_jcwj_ly, 
		zl_yzbg_zjjgmc, 
		zl_yzbg_yzbgh, 
		zl_yzbg_ly, 
		zl_yzbg_yzcjr, 
		zl_qyzc_ly, 
		zl_yyzz_ly, 
		zl_pgba_ly, 
		zl_fhbpgba_ly, 
		zl_bdpgba_ly, 
		zl_gytdba_ly, 
		zl_jcjg_jyjg, 
		zl_jcjg_jgd, 
		zl_jcjg_cjrq, 
		zl_jcjg_ly, 
		zl_sjbg_zjjg, 
		zl_sjbg_bgh, 
		zl_sjbg_cjrq, 
		zl_sjbg_ly, 
		zl_gqzr_ly, 
		zl_zhxy_ly, 
		zl_hbxys_ly, 
		zl_flxys_ly, 
		zl_wchzxy_ly, 
		zl_jzrsjbg_zjjgmc, 
		zl_jzrsjbg_yzbgh, 
		zl_jzrsjbg_ly, 
		zl_jzgg_mtmc, 
		zl_jzgg_ggrq, 
		zl_jzgg_ly, 
		xx_cjfmc, 
		xx_sfzj, 
		zl_zjyqsjbg_zjjg, 
		zl_zjyqsjbg_bgh, 
		zl_zjyqsjbg_ly, 
		zl_tzxy_ly, 
		xx_qsjzcz, 
		xx_qsfy, 
		xx_qczw, 
		xx_pcccze, 
		xx_pcfyhgyzw, 
		xx_ptzw, 
		zl_qsbg_zjjgmc, 
		zl_qsbg_bgh, 
		zl_qsbg_ly, 
		zl_zxgg_mtmc, 
		zl_zxgg_ggrq, 
		zl_zxgg_ly, 
		zl_gszxzm_ly, 
		zl_pcgg_mtmc, 
		zl_pcgg_ggrq, 
		zl_pcgg_ly, 
		zl_tzxy_yw, 
		zl_jcwj_yw, 
		zl_jzgg_yw, 
		zl_pcgg_yw, 
		zl_jzrsjbg_yw, 
		zl_qyzc_yw, 
		zl_jcjg_yw, 
		zl_sjbg_yw, 
		zl_fhbpgba_yw, 
		zl_yyzz_yw, 
		zl_pgba_yw, 
		zl_gszxzm_yw, 
		zl_gytdba_yw, 
		zl_yzbg_yw, 
		zl_hbxys_yw, 
		zl_zhxy_yw, 
		zl_gqzr_yw, 
		zl_wchzxy_yw, 
		zl_bdpgba_yw, 
		zl_zjyqsjbg_yw, 
		zl_flxys_yw, 
		zl_zxgg_yw, 
		zl_qsbg_yw, 
		xx_bdqyxz, 
		xx_bxbfxz, 
		xx_jsyy, 
		zl_gszxzm_zxrq, 
		zl_jcwj_dwmc, 
		zl_jcwj_wjmc, 
		zl_jcwj_wjh, 
		zl_pgba_zjjgmc, 
		zl_pgba_pgbgh, 
		zl_pgba_hzdwmc, 
		zl_pgba_hzwjh, 
		zl_fhbpgba_zjjgmc, 
		zl_fhbpgba_pgbgh, 
		zl_fhbpgba_hzdwmc, 
		zl_fhbpgba_hzwjh, 
		zl_bdpgba_zjjgmc, 
		zl_bdpgba_pgbgh, 
		zl_bdpgba_hzdwmc, 
		zl_bdpgba_hzwjh, 
		zl_gytdba_pzdw, 
		zl_gytdba_pzwh, 
		zl_ywblsqwj, 
		zl_zhypgbab_yw, 
		zl_zhypgbab_zjjgmc, 
		zl_zhypgbab_pgbgh, 
		zl_zhypgbab_hzdwmc, 
		zl_zhypgbab_hzwjh, 
		zl_zhypgbab_ly, 
		zl_zhepgbab_yw, 
		zl_zhepgbab_zjjgmc, 
		zl_zhepgbab_pgbgh, 
		zl_zhepgbab_hzdwmc, 
		zl_zhepgbab_hzwjh, 
		zl_zhepgbab_ly, 
		zl_xbpgbab_yw, 
		zl_xbpgbab_zjjgmc, 
		zl_xbpgbab_pgbgh, 
		zl_xbpgbab_hzdwmc, 
		zl_xbpgbab_hzwjh, 
		zl_xbpgbab_ly, 
		zl_bxbpgbab_yw, 
		zl_bxbpgbab_zjjgmc, 
		zl_bxbpgbab_pgbgh, 
		zl_bxbpgbab_hzdwmc, 
		zl_bxbpgbab_hzwjh, 
		zl_bxbpgbab_ly, 
		zl_tjpgbab_yw, 
		zl_tjpgbab_zjjgmc, 
		zl_tjpgbab_pgbgh, 
		zl_tjpgbab_hzdwmc, 
		zl_tjpgbab_hzwjh, 
		zl_tjpgbab_ly, 
		zl_syzcczxy_yw, 
		zl_syzcczxy_ly, 
		zl_zgdbdhjy_yw, 
		zl_zgdbdhjy_yj, 
		zl_zgdbdhjy_ly, 
		zl_gqszfawj_yw, 
		zl_gqszfawj_pzdw, 
		zl_gqszfawj_pzwh, 
		zl_gqszfawj_ly, 
		zl_gdqkdjb_yw, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.xx_pgjzcz, 
		t.xx_sjjzcz, 
		t.xx_fxjg, 
		t.zl_jcwj_ly, 
		t.zl_yzbg_zjjgmc, 
		t.zl_yzbg_yzbgh, 
		t.zl_yzbg_ly, 
		t.zl_yzbg_yzcjr, 
		t.zl_qyzc_ly, 
		t.zl_yyzz_ly, 
		t.zl_pgba_ly, 
		t.zl_fhbpgba_ly, 
		t.zl_bdpgba_ly, 
		t.zl_gytdba_ly, 
		t.zl_jcjg_jyjg, 
		t.zl_jcjg_jgd, 
		t.zl_jcjg_cjrq, 
		t.zl_jcjg_ly, 
		t.zl_sjbg_zjjg, 
		t.zl_sjbg_bgh, 
		t.zl_sjbg_cjrq, 
		t.zl_sjbg_ly, 
		t.zl_gqzr_ly, 
		t.zl_zhxy_ly, 
		t.zl_hbxys_ly, 
		t.zl_flxys_ly, 
		t.zl_wchzxy_ly, 
		t.zl_jzrsjbg_zjjgmc, 
		t.zl_jzrsjbg_yzbgh, 
		t.zl_jzrsjbg_ly, 
		t.zl_jzgg_mtmc, 
		t.zl_jzgg_ggrq, 
		t.zl_jzgg_ly, 
		t.xx_cjfmc, 
		t.xx_sfzj, 
		t.zl_zjyqsjbg_zjjg, 
		t.zl_zjyqsjbg_bgh, 
		t.zl_zjyqsjbg_ly, 
		t.zl_tzxy_ly, 
		t.xx_qsjzcz, 
		t.xx_qsfy, 
		t.xx_qczw, 
		t.xx_pcccze, 
		t.xx_pcfyhgyzw, 
		t.xx_ptzw, 
		t.zl_qsbg_zjjgmc, 
		t.zl_qsbg_bgh, 
		t.zl_qsbg_ly, 
		t.zl_zxgg_mtmc, 
		t.zl_zxgg_ggrq, 
		t.zl_zxgg_ly, 
		t.zl_gszxzm_ly, 
		t.zl_pcgg_mtmc, 
		t.zl_pcgg_ggrq, 
		t.zl_pcgg_ly, 
		t.zl_tzxy_yw, 
		t.zl_jcwj_yw, 
		t.zl_jzgg_yw, 
		t.zl_pcgg_yw, 
		t.zl_jzrsjbg_yw, 
		t.zl_qyzc_yw, 
		t.zl_jcjg_yw, 
		t.zl_sjbg_yw, 
		t.zl_fhbpgba_yw, 
		t.zl_yyzz_yw, 
		t.zl_pgba_yw, 
		t.zl_gszxzm_yw, 
		t.zl_gytdba_yw, 
		t.zl_yzbg_yw, 
		t.zl_hbxys_yw, 
		t.zl_zhxy_yw, 
		t.zl_gqzr_yw, 
		t.zl_wchzxy_yw, 
		t.zl_bdpgba_yw, 
		t.zl_zjyqsjbg_yw, 
		t.zl_flxys_yw, 
		t.zl_zxgg_yw, 
		t.zl_qsbg_yw, 
		t.xx_bdqyxz, 
		t.xx_bxbfxz, 
		t.xx_jsyy, 
		t.zl_gszxzm_zxrq, 
		t.zl_jcwj_dwmc, 
		t.zl_jcwj_wjmc, 
		t.zl_jcwj_wjh, 
		t.zl_pgba_zjjgmc, 
		t.zl_pgba_pgbgh, 
		t.zl_pgba_hzdwmc, 
		t.zl_pgba_hzwjh, 
		t.zl_fhbpgba_zjjgmc, 
		t.zl_fhbpgba_pgbgh, 
		t.zl_fhbpgba_hzdwmc, 
		t.zl_fhbpgba_hzwjh, 
		t.zl_bdpgba_zjjgmc, 
		t.zl_bdpgba_pgbgh, 
		t.zl_bdpgba_hzdwmc, 
		t.zl_bdpgba_hzwjh, 
		t.zl_gytdba_pzdw, 
		t.zl_gytdba_pzwh, 
		t.zl_ywblsqwj, 
		t.zl_zhypgbab_yw, 
		t.zl_zhypgbab_zjjgmc, 
		t.zl_zhypgbab_pgbgh, 
		t.zl_zhypgbab_hzdwmc, 
		t.zl_zhypgbab_hzwjh, 
		t.zl_zhypgbab_ly, 
		t.zl_zhepgbab_yw, 
		t.zl_zhepgbab_zjjgmc, 
		t.zl_zhepgbab_pgbgh, 
		t.zl_zhepgbab_hzdwmc, 
		t.zl_zhepgbab_hzwjh, 
		t.zl_zhepgbab_ly, 
		t.zl_xbpgbab_yw, 
		t.zl_xbpgbab_zjjgmc, 
		t.zl_xbpgbab_pgbgh, 
		t.zl_xbpgbab_hzdwmc, 
		t.zl_xbpgbab_hzwjh, 
		t.zl_xbpgbab_ly, 
		t.zl_bxbpgbab_yw, 
		t.zl_bxbpgbab_zjjgmc, 
		t.zl_bxbpgbab_pgbgh, 
		t.zl_bxbpgbab_hzdwmc, 
		t.zl_bxbpgbab_hzwjh, 
		t.zl_bxbpgbab_ly, 
		t.zl_tjpgbab_yw, 
		t.zl_tjpgbab_zjjgmc, 
		t.zl_tjpgbab_pgbgh, 
		t.zl_tjpgbab_hzdwmc, 
		t.zl_tjpgbab_hzwjh, 
		t.zl_tjpgbab_ly, 
		t.zl_syzcczxy_yw, 
		t.zl_syzcczxy_ly, 
		t.zl_zgdbdhjy_yw, 
		t.zl_zgdbdhjy_yj, 
		t.zl_zgdbdhjy_ly, 
		t.zl_gqszfawj_yw, 
		t.zl_gqszfawj_pzdw, 
		t.zl_gqszfawj_pzwh, 
		t.zl_gqszfawj_ly, 
		t.zl_gdqkdjb_yw, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{xxPgjzcz}, 
		#{xxSjjzcz}, 
		#{xxFxjg}, 
		#{zlJcwjLy}, 
		#{zlYzbgZjjgmc}, 
		#{zlYzbgYzbgh}, 
		#{zlYzbgLy}, 
		#{zlYzbgYzcjr}, 
		#{zlQyzcLy}, 
		#{zlYyzzLy}, 
		#{zlPgbaLy}, 
		#{zlFhbpgbaLy}, 
		#{zlBdpgbaLy}, 
		#{zlGytdbaLy}, 
		#{zlJcjgJyjg}, 
		#{zlJcjgJgd}, 
		#{zlJcjgCjrq}, 
		#{zlJcjgLy}, 
		#{zlSjbgZjjg}, 
		#{zlSjbgBgh}, 
		#{zlSjbgCjrq}, 
		#{zlSjbgLy}, 
		#{zlGqzrLy}, 
		#{zlZhxyLy}, 
		#{zlHbxysLy}, 
		#{zlFlxysLy}, 
		#{zlWchzxyLy}, 
		#{zlJzrsjbgZjjgmc}, 
		#{zlJzrsjbgYzbgh}, 
		#{zlJzrsjbgLy}, 
		#{zlJzggMtmc}, 
		#{zlJzggGgrq}, 
		#{zlJzggLy}, 
		#{xxCjfmc}, 
		#{xxSfzj}, 
		#{zlZjyqsjbgZjjg}, 
		#{zlZjyqsjbgBgh}, 
		#{zlZjyqsjbgLy}, 
		#{zlTzxyLy}, 
		#{xxQsjzcz}, 
		#{xxQsfy}, 
		#{xxQczw}, 
		#{xxPcccze}, 
		#{xxPcfyhgyzw}, 
		#{xxPtzw}, 
		#{zlQsbgZjjgmc}, 
		#{zlQsbgBgh}, 
		#{zlQsbgLy}, 
		#{zlZxggMtmc}, 
		#{zlZxggGgrq}, 
		#{zlZxggLy}, 
		#{zlGszxzmLy}, 
		#{zlPcggMtmc}, 
		#{zlPcggGgrq}, 
		#{zlPcggLy}, 
		#{zlTzxyYw}, 
		#{zlJcwjYw}, 
		#{zlJzggYw}, 
		#{zlPcggYw}, 
		#{zlJzrsjbgYw}, 
		#{zlQyzcYw}, 
		#{zlJcjgYw}, 
		#{zlSjbgYw}, 
		#{zlFhbpgbaYw}, 
		#{zlYyzzYw}, 
		#{zlPgbaYw}, 
		#{zlGszxzmYw}, 
		#{zlGytdbaYw}, 
		#{zlYzbgYw}, 
		#{zlHbxysYw}, 
		#{zlZhxyYw}, 
		#{zlGqzrYw}, 
		#{zlWchzxyYw}, 
		#{zlBdpgbaYw}, 
		#{zlZjyqsjbgYw}, 
		#{zlFlxysYw}, 
		#{zlZxggYw}, 
		#{zlQsbgYw}, 
		#{xxBdqyxz}, 
		#{xxBxbfxz}, 
		#{xxJsyy}, 
		#{zlGszxzmZxrq}, 
		#{zlJcwjDwmc}, 
		#{zlJcwjWjmc}, 
		#{zlJcwjWjh}, 
		#{zlPgbaZjjgmc}, 
		#{zlPgbaPgbgh}, 
		#{zlPgbaHzdwmc}, 
		#{zlPgbaHzwjh}, 
		#{zlFhbpgbaZjjgmc}, 
		#{zlFhbpgbaPgbgh}, 
		#{zlFhbpgbaHzdwmc}, 
		#{zlFhbpgbaHzwjh}, 
		#{zlBdpgbaZjjgmc}, 
		#{zlBdpgbaPgbgh}, 
		#{zlBdpgbaHzdwmc}, 
		#{zlBdpgbaHzwjh}, 
		#{zlGytdbaPzdw}, 
		#{zlGytdbaPzwh}, 
		#{zlYwblsqwj}, 
		#{zlZhypgbabYw}, 
		#{zlZhypgbabZjjgmc}, 
		#{zlZhypgbabPgbgh}, 
		#{zlZhypgbabHzdwmc}, 
		#{zlZhypgbabHzwjh}, 
		#{zlZhypgbabLy}, 
		#{zlZhepgbabYw}, 
		#{zlZhepgbabZjjgmc}, 
		#{zlZhepgbabPgbgh}, 
		#{zlZhepgbabHzdwmc}, 
		#{zlZhepgbabHzwjh}, 
		#{zlZhepgbabLy}, 
		#{zlXbpgbabYw}, 
		#{zlXbpgbabZjjgmc}, 
		#{zlXbpgbabPgbgh}, 
		#{zlXbpgbabHzdwmc}, 
		#{zlXbpgbabHzwjh}, 
		#{zlXbpgbabLy}, 
		#{zlBxbpgbabYw}, 
		#{zlBxbpgbabZjjgmc}, 
		#{zlBxbpgbabPgbgh}, 
		#{zlBxbpgbabHzdwmc}, 
		#{zlBxbpgbabHzwjh}, 
		#{zlBxbpgbabLy}, 
		#{zlTjpgbabYw}, 
		#{zlTjpgbabZjjgmc}, 
		#{zlTjpgbabPgbgh}, 
		#{zlTjpgbabHzdwmc}, 
		#{zlTjpgbabHzwjh}, 
		#{zlTjpgbabLy}, 
		#{zlSyzcczxyYw}, 
		#{zlSyzcczxyLy}, 
		#{zlZgdbdhjyYw}, 
		#{zlZgdbdhjyYj}, 
		#{zlZgdbdhjyLy}, 
		#{zlGqszfawjYw}, 
		#{zlGqszfawjPzdw}, 
		#{zlGqszfawjPzwh}, 
		#{zlGqszfawjLy}, 
		#{zlGdqkdjbYw}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="xxPgjzcz != null">
			and t.xx_pgjzcz = #{xxPgjzcz}
		</if>
		<if test="xxSjjzcz != null">
			and t.xx_sjjzcz = #{xxSjjzcz}
		</if>
		<if test="xxFxjg != null">
			and t.xx_fxjg = #{xxFxjg}
		</if>
		<if test="zlJcwjLy != null and zlJcwjLy != ''">
			and t.zl_jcwj_ly = #{zlJcwjLy}
		</if>
		<if test="zlYzbgZjjgmc != null and zlYzbgZjjgmc != ''">
			and t.zl_yzbg_zjjgmc = #{zlYzbgZjjgmc}
		</if>
		<if test="zlYzbgYzbgh != null and zlYzbgYzbgh != ''">
			and t.zl_yzbg_yzbgh = #{zlYzbgYzbgh}
		</if>
		<if test="zlYzbgLy != null and zlYzbgLy != ''">
			and t.zl_yzbg_ly = #{zlYzbgLy}
		</if>
		<if test="zlYzbgYzcjr != null">
			and t.zl_yzbg_yzcjr = #{zlYzbgYzcjr}
		</if>
		<if test="zlQyzcLy != null and zlQyzcLy != ''">
			and t.zl_qyzc_ly = #{zlQyzcLy}
		</if>
		<if test="zlYyzzLy != null and zlYyzzLy != ''">
			and t.zl_yyzz_ly = #{zlYyzzLy}
		</if>
		<if test="zlPgbaLy != null and zlPgbaLy != ''">
			and t.zl_pgba_ly = #{zlPgbaLy}
		</if>
		<if test="zlFhbpgbaLy != null and zlFhbpgbaLy != ''">
			and t.zl_fhbpgba_ly = #{zlFhbpgbaLy}
		</if>
		<if test="zlBdpgbaLy != null and zlBdpgbaLy != ''">
			and t.zl_bdpgba_ly = #{zlBdpgbaLy}
		</if>
		<if test="zlGytdbaLy != null and zlGytdbaLy != ''">
			and t.zl_gytdba_ly = #{zlGytdbaLy}
		</if>
		<if test="zlJcjgJyjg != null and zlJcjgJyjg != ''">
			and t.zl_jcjg_jyjg = #{zlJcjgJyjg}
		</if>
		<if test="zlJcjgJgd != null and zlJcjgJgd != ''">
			and t.zl_jcjg_jgd = #{zlJcjgJgd}
		</if>
		<if test="zlJcjgCjrq != null">
			and t.zl_jcjg_cjrq = #{zlJcjgCjrq}
		</if>
		<if test="zlJcjgLy != null and zlJcjgLy != ''">
			and t.zl_jcjg_ly = #{zlJcjgLy}
		</if>
		<if test="zlSjbgZjjg != null and zlSjbgZjjg != ''">
			and t.zl_sjbg_zjjg = #{zlSjbgZjjg}
		</if>
		<if test="zlSjbgBgh != null and zlSjbgBgh != ''">
			and t.zl_sjbg_bgh = #{zlSjbgBgh}
		</if>
		<if test="zlSjbgCjrq != null">
			and t.zl_sjbg_cjrq = #{zlSjbgCjrq}
		</if>
		<if test="zlSjbgLy != null and zlSjbgLy != ''">
			and t.zl_sjbg_ly = #{zlSjbgLy}
		</if>
		<if test="zlGqzrLy != null and zlGqzrLy != ''">
			and t.zl_gqzr_ly = #{zlGqzrLy}
		</if>
		<if test="zlZhxyLy != null and zlZhxyLy != ''">
			and t.zl_zhxy_ly = #{zlZhxyLy}
		</if>
		<if test="zlHbxysLy != null and zlHbxysLy != ''">
			and t.zl_hbxys_ly = #{zlHbxysLy}
		</if>
		<if test="zlFlxysLy != null and zlFlxysLy != ''">
			and t.zl_flxys_ly = #{zlFlxysLy}
		</if>
		<if test="zlWchzxyLy != null and zlWchzxyLy != ''">
			and t.zl_wchzxy_ly = #{zlWchzxyLy}
		</if>
		<if test="zlJzrsjbgZjjgmc != null and zlJzrsjbgZjjgmc != ''">
			and t.zl_jzrsjbg_zjjgmc = #{zlJzrsjbgZjjgmc}
		</if>
		<if test="zlJzrsjbgYzbgh != null and zlJzrsjbgYzbgh != ''">
			and t.zl_jzrsjbg_yzbgh = #{zlJzrsjbgYzbgh}
		</if>
		<if test="zlJzrsjbgLy != null and zlJzrsjbgLy != ''">
			and t.zl_jzrsjbg_ly = #{zlJzrsjbgLy}
		</if>
		<if test="zlJzggMtmc != null and zlJzggMtmc != ''">
			and t.zl_jzgg_mtmc = #{zlJzggMtmc}
		</if>
		<if test="zlJzggGgrq != null">
			and t.zl_jzgg_ggrq = #{zlJzggGgrq}
		</if>
		<if test="zlJzggLy != null and zlJzggLy != ''">
			and t.zl_jzgg_ly = #{zlJzggLy}
		</if>
		<if test="xxCjfmc != null and xxCjfmc != ''">
			and t.xx_cjfmc = #{xxCjfmc}
		</if>
		<if test="xxSfzj != null">
			and t.xx_sfzj = #{xxSfzj}
		</if>
		<if test="zlZjyqsjbgZjjg != null and zlZjyqsjbgZjjg != ''">
			and t.zl_zjyqsjbg_zjjg = #{zlZjyqsjbgZjjg}
		</if>
		<if test="zlZjyqsjbgBgh != null and zlZjyqsjbgBgh != ''">
			and t.zl_zjyqsjbg_bgh = #{zlZjyqsjbgBgh}
		</if>
		<if test="zlZjyqsjbgLy != null and zlZjyqsjbgLy != ''">
			and t.zl_zjyqsjbg_ly = #{zlZjyqsjbgLy}
		</if>
		<if test="zlTzxyLy != null and zlTzxyLy != ''">
			and t.zl_tzxy_ly = #{zlTzxyLy}
		</if>
		<if test="xxQsjzcz != null">
			and t.xx_qsjzcz = #{xxQsjzcz}
		</if>
		<if test="xxQsfy != null">
			and t.xx_qsfy = #{xxQsfy}
		</if>
		<if test="xxQczw != null">
			and t.xx_qczw = #{xxQczw}
		</if>
		<if test="xxPcccze != null">
			and t.xx_pcccze = #{xxPcccze}
		</if>
		<if test="xxPcfyhgyzw != null">
			and t.xx_pcfyhgyzw = #{xxPcfyhgyzw}
		</if>
		<if test="xxPtzw != null">
			and t.xx_ptzw = #{xxPtzw}
		</if>
		<if test="zlQsbgZjjgmc != null and zlQsbgZjjgmc != ''">
			and t.zl_qsbg_zjjgmc = #{zlQsbgZjjgmc}
		</if>
		<if test="zlQsbgBgh != null and zlQsbgBgh != ''">
			and t.zl_qsbg_bgh = #{zlQsbgBgh}
		</if>
		<if test="zlQsbgLy != null and zlQsbgLy != ''">
			and t.zl_qsbg_ly = #{zlQsbgLy}
		</if>
		<if test="zlZxggMtmc != null and zlZxggMtmc != ''">
			and t.zl_zxgg_mtmc = #{zlZxggMtmc}
		</if>
		<if test="zlZxggGgrq != null">
			and t.zl_zxgg_ggrq = #{zlZxggGgrq}
		</if>
		<if test="zlZxggLy != null and zlZxggLy != ''">
			and t.zl_zxgg_ly = #{zlZxggLy}
		</if>
		<if test="zlGszxzmLy != null and zlGszxzmLy != ''">
			and t.zl_gszxzm_ly = #{zlGszxzmLy}
		</if>
		<if test="zlPcggMtmc != null and zlPcggMtmc != ''">
			and t.zl_pcgg_mtmc = #{zlPcggMtmc}
		</if>
		<if test="zlPcggGgrq != null">
			and t.zl_pcgg_ggrq = #{zlPcggGgrq}
		</if>
		<if test="zlPcggLy != null and zlPcggLy != ''">
			and t.zl_pcgg_ly = #{zlPcggLy}
		</if>
		<if test="zlTzxyYw != null and zlTzxyYw != ''">
			and t.zl_tzxy_yw = #{zlTzxyYw}
		</if>
		<if test="zlJcwjYw != null and zlJcwjYw != ''">
			and t.zl_jcwj_yw = #{zlJcwjYw}
		</if>
		<if test="zlJzggYw != null and zlJzggYw != ''">
			and t.zl_jzgg_yw = #{zlJzggYw}
		</if>
		<if test="zlPcggYw != null and zlPcggYw != ''">
			and t.zl_pcgg_yw = #{zlPcggYw}
		</if>
		<if test="zlJzrsjbgYw != null and zlJzrsjbgYw != ''">
			and t.zl_jzrsjbg_yw = #{zlJzrsjbgYw}
		</if>
		<if test="zlQyzcYw != null and zlQyzcYw != ''">
			and t.zl_qyzc_yw = #{zlQyzcYw}
		</if>
		<if test="zlJcjgYw != null and zlJcjgYw != ''">
			and t.zl_jcjg_yw = #{zlJcjgYw}
		</if>
		<if test="zlSjbgYw != null and zlSjbgYw != ''">
			and t.zl_sjbg_yw = #{zlSjbgYw}
		</if>
		<if test="zlFhbpgbaYw != null and zlFhbpgbaYw != ''">
			and t.zl_fhbpgba_yw = #{zlFhbpgbaYw}
		</if>
		<if test="zlYyzzYw != null and zlYyzzYw != ''">
			and t.zl_yyzz_yw = #{zlYyzzYw}
		</if>
		<if test="zlPgbaYw != null and zlPgbaYw != ''">
			and t.zl_pgba_yw = #{zlPgbaYw}
		</if>
		<if test="zlGszxzmYw != null and zlGszxzmYw != ''">
			and t.zl_gszxzm_yw = #{zlGszxzmYw}
		</if>
		<if test="zlGytdbaYw != null and zlGytdbaYw != ''">
			and t.zl_gytdba_yw = #{zlGytdbaYw}
		</if>
		<if test="zlYzbgYw != null and zlYzbgYw != ''">
			and t.zl_yzbg_yw = #{zlYzbgYw}
		</if>
		<if test="zlHbxysYw != null and zlHbxysYw != ''">
			and t.zl_hbxys_yw = #{zlHbxysYw}
		</if>
		<if test="zlZhxyYw != null and zlZhxyYw != ''">
			and t.zl_zhxy_yw = #{zlZhxyYw}
		</if>
		<if test="zlGqzrYw != null and zlGqzrYw != ''">
			and t.zl_gqzr_yw = #{zlGqzrYw}
		</if>
		<if test="zlWchzxyYw != null and zlWchzxyYw != ''">
			and t.zl_wchzxy_yw = #{zlWchzxyYw}
		</if>
		<if test="zlBdpgbaYw != null and zlBdpgbaYw != ''">
			and t.zl_bdpgba_yw = #{zlBdpgbaYw}
		</if>
		<if test="zlZjyqsjbgYw != null and zlZjyqsjbgYw != ''">
			and t.zl_zjyqsjbg_yw = #{zlZjyqsjbgYw}
		</if>
		<if test="zlFlxysYw != null and zlFlxysYw != ''">
			and t.zl_flxys_yw = #{zlFlxysYw}
		</if>
		<if test="zlZxggYw != null and zlZxggYw != ''">
			and t.zl_zxgg_yw = #{zlZxggYw}
		</if>
		<if test="zlQsbgYw != null and zlQsbgYw != ''">
			and t.zl_qsbg_yw = #{zlQsbgYw}
		</if>
		<if test="xxBdqyxz != null and xxBdqyxz != ''">
			and t.xx_bdqyxz = #{xxBdqyxz}
		</if>
		<if test="xxBxbfxz != null and xxBxbfxz != ''">
			and t.xx_bxbfxz = #{xxBxbfxz}
		</if>
		<if test="xxJsyy != null and xxJsyy != ''">
			and t.xx_jsyy = #{xxJsyy}
		</if>
		<if test="zlGszxzmZxrq != null">
			and t.zl_gszxzm_zxrq = #{zlGszxzmZxrq}
		</if>
		<if test="zlJcwjDwmc != null and zlJcwjDwmc != ''">
			and t.zl_jcwj_dwmc = #{zlJcwjDwmc}
		</if>
		<if test="zlJcwjWjmc != null and zlJcwjWjmc != ''">
			and t.zl_jcwj_wjmc = #{zlJcwjWjmc}
		</if>
		<if test="zlJcwjWjh != null and zlJcwjWjh != ''">
			and t.zl_jcwj_wjh = #{zlJcwjWjh}
		</if>
		<if test="zlPgbaZjjgmc != null and zlPgbaZjjgmc != ''">
			and t.zl_pgba_zjjgmc = #{zlPgbaZjjgmc}
		</if>
		<if test="zlPgbaPgbgh != null and zlPgbaPgbgh != ''">
			and t.zl_pgba_pgbgh = #{zlPgbaPgbgh}
		</if>
		<if test="zlPgbaHzdwmc != null and zlPgbaHzdwmc != ''">
			and t.zl_pgba_hzdwmc = #{zlPgbaHzdwmc}
		</if>
		<if test="zlPgbaHzwjh != null and zlPgbaHzwjh != ''">
			and t.zl_pgba_hzwjh = #{zlPgbaHzwjh}
		</if>
		<if test="zlFhbpgbaZjjgmc != null and zlFhbpgbaZjjgmc != ''">
			and t.zl_fhbpgba_zjjgmc = #{zlFhbpgbaZjjgmc}
		</if>
		<if test="zlFhbpgbaPgbgh != null and zlFhbpgbaPgbgh != ''">
			and t.zl_fhbpgba_pgbgh = #{zlFhbpgbaPgbgh}
		</if>
		<if test="zlFhbpgbaHzdwmc != null and zlFhbpgbaHzdwmc != ''">
			and t.zl_fhbpgba_hzdwmc = #{zlFhbpgbaHzdwmc}
		</if>
		<if test="zlFhbpgbaHzwjh != null and zlFhbpgbaHzwjh != ''">
			and t.zl_fhbpgba_hzwjh = #{zlFhbpgbaHzwjh}
		</if>
		<if test="zlBdpgbaZjjgmc != null and zlBdpgbaZjjgmc != ''">
			and t.zl_bdpgba_zjjgmc = #{zlBdpgbaZjjgmc}
		</if>
		<if test="zlBdpgbaPgbgh != null and zlBdpgbaPgbgh != ''">
			and t.zl_bdpgba_pgbgh = #{zlBdpgbaPgbgh}
		</if>
		<if test="zlBdpgbaHzdwmc != null and zlBdpgbaHzdwmc != ''">
			and t.zl_bdpgba_hzdwmc = #{zlBdpgbaHzdwmc}
		</if>
		<if test="zlBdpgbaHzwjh != null and zlBdpgbaHzwjh != ''">
			and t.zl_bdpgba_hzwjh = #{zlBdpgbaHzwjh}
		</if>
		<if test="zlGytdbaPzdw != null and zlGytdbaPzdw != ''">
			and t.zl_gytdba_pzdw = #{zlGytdbaPzdw}
		</if>
		<if test="zlGytdbaPzwh != null and zlGytdbaPzwh != ''">
			and t.zl_gytdba_pzwh = #{zlGytdbaPzwh}
		</if>
		<if test="zlYwblsqwj != null and zlYwblsqwj != ''">
			and t.zl_ywblsqwj = #{zlYwblsqwj}
		</if>
		<if test="zlZhypgbabYw != null and zlZhypgbabYw != ''">
			and t.zl_zhypgbab_yw = #{zlZhypgbabYw}
		</if>
		<if test="zlZhypgbabZjjgmc != null and zlZhypgbabZjjgmc != ''">
			and t.zl_zhypgbab_zjjgmc = #{zlZhypgbabZjjgmc}
		</if>
		<if test="zlZhypgbabPgbgh != null and zlZhypgbabPgbgh != ''">
			and t.zl_zhypgbab_pgbgh = #{zlZhypgbabPgbgh}
		</if>
		<if test="zlZhypgbabHzdwmc != null and zlZhypgbabHzdwmc != ''">
			and t.zl_zhypgbab_hzdwmc = #{zlZhypgbabHzdwmc}
		</if>
		<if test="zlZhypgbabHzwjh != null and zlZhypgbabHzwjh != ''">
			and t.zl_zhypgbab_hzwjh = #{zlZhypgbabHzwjh}
		</if>
		<if test="zlZhypgbabLy != null and zlZhypgbabLy != ''">
			and t.zl_zhypgbab_ly = #{zlZhypgbabLy}
		</if>
		<if test="zlZhepgbabYw != null and zlZhepgbabYw != ''">
			and t.zl_zhepgbab_yw = #{zlZhepgbabYw}
		</if>
		<if test="zlZhepgbabZjjgmc != null and zlZhepgbabZjjgmc != ''">
			and t.zl_zhepgbab_zjjgmc = #{zlZhepgbabZjjgmc}
		</if>
		<if test="zlZhepgbabPgbgh != null and zlZhepgbabPgbgh != ''">
			and t.zl_zhepgbab_pgbgh = #{zlZhepgbabPgbgh}
		</if>
		<if test="zlZhepgbabHzdwmc != null and zlZhepgbabHzdwmc != ''">
			and t.zl_zhepgbab_hzdwmc = #{zlZhepgbabHzdwmc}
		</if>
		<if test="zlZhepgbabHzwjh != null and zlZhepgbabHzwjh != ''">
			and t.zl_zhepgbab_hzwjh = #{zlZhepgbabHzwjh}
		</if>
		<if test="zlZhepgbabLy != null and zlZhepgbabLy != ''">
			and t.zl_zhepgbab_ly = #{zlZhepgbabLy}
		</if>
		<if test="zlXbpgbabYw != null and zlXbpgbabYw != ''">
			and t.zl_xbpgbab_yw = #{zlXbpgbabYw}
		</if>
		<if test="zlXbpgbabZjjgmc != null and zlXbpgbabZjjgmc != ''">
			and t.zl_xbpgbab_zjjgmc = #{zlXbpgbabZjjgmc}
		</if>
		<if test="zlXbpgbabPgbgh != null and zlXbpgbabPgbgh != ''">
			and t.zl_xbpgbab_pgbgh = #{zlXbpgbabPgbgh}
		</if>
		<if test="zlXbpgbabHzdwmc != null and zlXbpgbabHzdwmc != ''">
			and t.zl_xbpgbab_hzdwmc = #{zlXbpgbabHzdwmc}
		</if>
		<if test="zlXbpgbabHzwjh != null and zlXbpgbabHzwjh != ''">
			and t.zl_xbpgbab_hzwjh = #{zlXbpgbabHzwjh}
		</if>
		<if test="zlXbpgbabLy != null and zlXbpgbabLy != ''">
			and t.zl_xbpgbab_ly = #{zlXbpgbabLy}
		</if>
		<if test="zlBxbpgbabYw != null and zlBxbpgbabYw != ''">
			and t.zl_bxbpgbab_yw = #{zlBxbpgbabYw}
		</if>
		<if test="zlBxbpgbabZjjgmc != null and zlBxbpgbabZjjgmc != ''">
			and t.zl_bxbpgbab_zjjgmc = #{zlBxbpgbabZjjgmc}
		</if>
		<if test="zlBxbpgbabPgbgh != null and zlBxbpgbabPgbgh != ''">
			and t.zl_bxbpgbab_pgbgh = #{zlBxbpgbabPgbgh}
		</if>
		<if test="zlBxbpgbabHzdwmc != null and zlBxbpgbabHzdwmc != ''">
			and t.zl_bxbpgbab_hzdwmc = #{zlBxbpgbabHzdwmc}
		</if>
		<if test="zlBxbpgbabHzwjh != null and zlBxbpgbabHzwjh != ''">
			and t.zl_bxbpgbab_hzwjh = #{zlBxbpgbabHzwjh}
		</if>
		<if test="zlBxbpgbabLy != null and zlBxbpgbabLy != ''">
			and t.zl_bxbpgbab_ly = #{zlBxbpgbabLy}
		</if>
		<if test="zlTjpgbabYw != null and zlTjpgbabYw != ''">
			and t.zl_tjpgbab_yw = #{zlTjpgbabYw}
		</if>
		<if test="zlTjpgbabZjjgmc != null and zlTjpgbabZjjgmc != ''">
			and t.zl_tjpgbab_zjjgmc = #{zlTjpgbabZjjgmc}
		</if>
		<if test="zlTjpgbabPgbgh != null and zlTjpgbabPgbgh != ''">
			and t.zl_tjpgbab_pgbgh = #{zlTjpgbabPgbgh}
		</if>
		<if test="zlTjpgbabHzdwmc != null and zlTjpgbabHzdwmc != ''">
			and t.zl_tjpgbab_hzdwmc = #{zlTjpgbabHzdwmc}
		</if>
		<if test="zlTjpgbabHzwjh != null and zlTjpgbabHzwjh != ''">
			and t.zl_tjpgbab_hzwjh = #{zlTjpgbabHzwjh}
		</if>
		<if test="zlTjpgbabLy != null and zlTjpgbabLy != ''">
			and t.zl_tjpgbab_ly = #{zlTjpgbabLy}
		</if>
		<if test="zlSyzcczxyYw != null and zlSyzcczxyYw != ''">
			and t.zl_syzcczxy_yw = #{zlSyzcczxyYw}
		</if>
		<if test="zlSyzcczxyLy != null and zlSyzcczxyLy != ''">
			and t.zl_syzcczxy_ly = #{zlSyzcczxyLy}
		</if>
		<if test="zlZgdbdhjyYw != null and zlZgdbdhjyYw != ''">
			and t.zl_zgdbdhjy_yw = #{zlZgdbdhjyYw}
		</if>
		<if test="zlZgdbdhjyYj != null and zlZgdbdhjyYj != ''">
			and t.zl_zgdbdhjy_yj = #{zlZgdbdhjyYj}
		</if>
		<if test="zlZgdbdhjyLy != null and zlZgdbdhjyLy != ''">
			and t.zl_zgdbdhjy_ly = #{zlZgdbdhjyLy}
		</if>
		<if test="zlGqszfawjYw != null and zlGqszfawjYw != ''">
			and t.zl_gqszfawj_yw = #{zlGqszfawjYw}
		</if>
		<if test="zlGqszfawjPzdw != null and zlGqszfawjPzdw != ''">
			and t.zl_gqszfawj_pzdw = #{zlGqszfawjPzdw}
		</if>
		<if test="zlGqszfawjPzwh != null and zlGqszfawjPzwh != ''">
			and t.zl_gqszfawj_pzwh = #{zlGqszfawjPzwh}
		</if>
		<if test="zlGqszfawjLy != null and zlGqszfawjLy != ''">
			and t.zl_gqszfawj_ly = #{zlGqszfawjLy}
		</if>
		<if test="zlGdqkdjbYw != null and zlGdqkdjbYw != ''">
			and t.zl_gdqkdjb_yw = #{zlGdqkdjbYw}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.ywzbb.entity.Ywzbb" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_ywzbb (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_ywzbb set isDeleted = 'Y' where
		id in
		<foreach collection="ywzbbs" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_ywzbb set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_ywzbb  where
		id in
		<foreach collection="ywzbbs" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_ywzbb  where id = #{id}
	</delete>
	
	<select id="selectYwzbbByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_ywzbb
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_ywzbb
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="xxPgjzcz != null">
				xx_pgjzcz=#{xxPgjzcz},
			</if>
			<if test="xxSjjzcz != null">
				xx_sjjzcz=#{xxSjjzcz},
			</if>
			<if test="xxFxjg != null">
				xx_fxjg=#{xxFxjg},
			</if>
			<if test="zlJcwjLy != null">
				zl_jcwj_ly=#{zlJcwjLy},
			</if>
			<if test="zlYzbgZjjgmc != null">
				zl_yzbg_zjjgmc=#{zlYzbgZjjgmc},
			</if>
			<if test="zlYzbgYzbgh != null">
				zl_yzbg_yzbgh=#{zlYzbgYzbgh},
			</if>
			<if test="zlYzbgLy != null">
				zl_yzbg_ly=#{zlYzbgLy},
			</if>
			<if test="zlYzbgYzcjr != null">
				zl_yzbg_yzcjr=#{zlYzbgYzcjr},
			</if>
			<if test="zlQyzcLy != null">
				zl_qyzc_ly=#{zlQyzcLy},
			</if>
			<if test="zlYyzzLy != null">
				zl_yyzz_ly=#{zlYyzzLy},
			</if>
			<if test="zlPgbaLy != null">
				zl_pgba_ly=#{zlPgbaLy},
			</if>
			<if test="zlFhbpgbaLy != null">
				zl_fhbpgba_ly=#{zlFhbpgbaLy},
			</if>
			<if test="zlBdpgbaLy != null">
				zl_bdpgba_ly=#{zlBdpgbaLy},
			</if>
			<if test="zlGytdbaLy != null">
				zl_gytdba_ly=#{zlGytdbaLy},
			</if>
			<if test="zlJcjgJyjg != null">
				zl_jcjg_jyjg=#{zlJcjgJyjg},
			</if>
			<if test="zlJcjgJgd != null">
				zl_jcjg_jgd=#{zlJcjgJgd},
			</if>
			<if test="zlJcjgCjrq != null">
				zl_jcjg_cjrq=#{zlJcjgCjrq},
			</if>
			<if test="zlJcjgLy != null">
				zl_jcjg_ly=#{zlJcjgLy},
			</if>
			<if test="zlSjbgZjjg != null">
				zl_sjbg_zjjg=#{zlSjbgZjjg},
			</if>
			<if test="zlSjbgBgh != null">
				zl_sjbg_bgh=#{zlSjbgBgh},
			</if>
			<if test="zlSjbgCjrq != null">
				zl_sjbg_cjrq=#{zlSjbgCjrq},
			</if>
			<if test="zlSjbgLy != null">
				zl_sjbg_ly=#{zlSjbgLy},
			</if>
			<if test="zlGqzrLy != null">
				zl_gqzr_ly=#{zlGqzrLy},
			</if>
			<if test="zlZhxyLy != null">
				zl_zhxy_ly=#{zlZhxyLy},
			</if>
			<if test="zlHbxysLy != null">
				zl_hbxys_ly=#{zlHbxysLy},
			</if>
			<if test="zlFlxysLy != null">
				zl_flxys_ly=#{zlFlxysLy},
			</if>
			<if test="zlWchzxyLy != null">
				zl_wchzxy_ly=#{zlWchzxyLy},
			</if>
			<if test="zlJzrsjbgZjjgmc != null">
				zl_jzrsjbg_zjjgmc=#{zlJzrsjbgZjjgmc},
			</if>
			<if test="zlJzrsjbgYzbgh != null">
				zl_jzrsjbg_yzbgh=#{zlJzrsjbgYzbgh},
			</if>
			<if test="zlJzrsjbgLy != null">
				zl_jzrsjbg_ly=#{zlJzrsjbgLy},
			</if>
			<if test="zlJzggMtmc != null">
				zl_jzgg_mtmc=#{zlJzggMtmc},
			</if>
			<if test="zlJzggGgrq != null">
				zl_jzgg_ggrq=#{zlJzggGgrq},
			</if>
			<if test="zlJzggLy != null">
				zl_jzgg_ly=#{zlJzggLy},
			</if>
			<if test="xxCjfmc != null">
				xx_cjfmc=#{xxCjfmc},
			</if>
			<if test="xxSfzj != null">
				xx_sfzj=#{xxSfzj},
			</if>
			<if test="zlZjyqsjbgZjjg != null">
				zl_zjyqsjbg_zjjg=#{zlZjyqsjbgZjjg},
			</if>
			<if test="zlZjyqsjbgBgh != null">
				zl_zjyqsjbg_bgh=#{zlZjyqsjbgBgh},
			</if>
			<if test="zlZjyqsjbgLy != null">
				zl_zjyqsjbg_ly=#{zlZjyqsjbgLy},
			</if>
			<if test="zlTzxyLy != null">
				zl_tzxy_ly=#{zlTzxyLy},
			</if>
			<if test="xxQsjzcz != null">
				xx_qsjzcz=#{xxQsjzcz},
			</if>
			<if test="xxQsfy != null">
				xx_qsfy=#{xxQsfy},
			</if>
			<if test="xxQczw != null">
				xx_qczw=#{xxQczw},
			</if>
			<if test="xxPcccze != null">
				xx_pcccze=#{xxPcccze},
			</if>
			<if test="xxPcfyhgyzw != null">
				xx_pcfyhgyzw=#{xxPcfyhgyzw},
			</if>
			<if test="xxPtzw != null">
				xx_ptzw=#{xxPtzw},
			</if>
			<if test="zlQsbgZjjgmc != null">
				zl_qsbg_zjjgmc=#{zlQsbgZjjgmc},
			</if>
			<if test="zlQsbgBgh != null">
				zl_qsbg_bgh=#{zlQsbgBgh},
			</if>
			<if test="zlQsbgLy != null">
				zl_qsbg_ly=#{zlQsbgLy},
			</if>
			<if test="zlZxggMtmc != null">
				zl_zxgg_mtmc=#{zlZxggMtmc},
			</if>
			<if test="zlZxggGgrq != null">
				zl_zxgg_ggrq=#{zlZxggGgrq},
			</if>
			<if test="zlZxggLy != null">
				zl_zxgg_ly=#{zlZxggLy},
			</if>
			<if test="zlGszxzmLy != null">
				zl_gszxzm_ly=#{zlGszxzmLy},
			</if>
			<if test="zlPcggMtmc != null">
				zl_pcgg_mtmc=#{zlPcggMtmc},
			</if>
			<if test="zlPcggGgrq != null">
				zl_pcgg_ggrq=#{zlPcggGgrq},
			</if>
			<if test="zlPcggLy != null">
				zl_pcgg_ly=#{zlPcggLy},
			</if>
			<if test="zlTzxyYw != null">
				zl_tzxy_yw=#{zlTzxyYw},
			</if>
			<if test="zlJcwjYw != null">
				zl_jcwj_yw=#{zlJcwjYw},
			</if>
			<if test="zlJzggYw != null">
				zl_jzgg_yw=#{zlJzggYw},
			</if>
			<if test="zlPcggYw != null">
				zl_pcgg_yw=#{zlPcggYw},
			</if>
			<if test="zlJzrsjbgYw != null">
				zl_jzrsjbg_yw=#{zlJzrsjbgYw},
			</if>
			<if test="zlQyzcYw != null">
				zl_qyzc_yw=#{zlQyzcYw},
			</if>
			<if test="zlJcjgYw != null">
				zl_jcjg_yw=#{zlJcjgYw},
			</if>
			<if test="zlSjbgYw != null">
				zl_sjbg_yw=#{zlSjbgYw},
			</if>
			<if test="zlFhbpgbaYw != null">
				zl_fhbpgba_yw=#{zlFhbpgbaYw},
			</if>
			<if test="zlYyzzYw != null">
				zl_yyzz_yw=#{zlYyzzYw},
			</if>
			<if test="zlPgbaYw != null">
				zl_pgba_yw=#{zlPgbaYw},
			</if>
			<if test="zlGszxzmYw != null">
				zl_gszxzm_yw=#{zlGszxzmYw},
			</if>
			<if test="zlGytdbaYw != null">
				zl_gytdba_yw=#{zlGytdbaYw},
			</if>
			<if test="zlYzbgYw != null">
				zl_yzbg_yw=#{zlYzbgYw},
			</if>
			<if test="zlHbxysYw != null">
				zl_hbxys_yw=#{zlHbxysYw},
			</if>
			<if test="zlZhxyYw != null">
				zl_zhxy_yw=#{zlZhxyYw},
			</if>
			<if test="zlGqzrYw != null">
				zl_gqzr_yw=#{zlGqzrYw},
			</if>
			<if test="zlWchzxyYw != null">
				zl_wchzxy_yw=#{zlWchzxyYw},
			</if>
			<if test="zlBdpgbaYw != null">
				zl_bdpgba_yw=#{zlBdpgbaYw},
			</if>
			<if test="zlZjyqsjbgYw != null">
				zl_zjyqsjbg_yw=#{zlZjyqsjbgYw},
			</if>
			<if test="zlFlxysYw != null">
				zl_flxys_yw=#{zlFlxysYw},
			</if>
			<if test="zlZxggYw != null">
				zl_zxgg_yw=#{zlZxggYw},
			</if>
			<if test="zlQsbgYw != null">
				zl_qsbg_yw=#{zlQsbgYw},
			</if>
			<if test="xxBdqyxz != null">
				xx_bdqyxz=#{xxBdqyxz},
			</if>
			<if test="xxBxbfxz != null">
				xx_bxbfxz=#{xxBxbfxz},
			</if>
			<if test="xxJsyy != null">
				xx_jsyy=#{xxJsyy},
			</if>
			<if test="zlGszxzmZxrq != null">
				zl_gszxzm_zxrq=#{zlGszxzmZxrq},
			</if>
			<if test="zlJcwjDwmc != null">
				zl_jcwj_dwmc=#{zlJcwjDwmc},
			</if>
			<if test="zlJcwjWjmc != null">
				zl_jcwj_wjmc=#{zlJcwjWjmc},
			</if>
			<if test="zlJcwjWjh != null">
				zl_jcwj_wjh=#{zlJcwjWjh},
			</if>
			<if test="zlPgbaZjjgmc != null">
				zl_pgba_zjjgmc=#{zlPgbaZjjgmc},
			</if>
			<if test="zlPgbaPgbgh != null">
				zl_pgba_pgbgh=#{zlPgbaPgbgh},
			</if>
			<if test="zlPgbaHzdwmc != null">
				zl_pgba_hzdwmc=#{zlPgbaHzdwmc},
			</if>
			<if test="zlPgbaHzwjh != null">
				zl_pgba_hzwjh=#{zlPgbaHzwjh},
			</if>
			<if test="zlFhbpgbaZjjgmc != null">
				zl_fhbpgba_zjjgmc=#{zlFhbpgbaZjjgmc},
			</if>
			<if test="zlFhbpgbaPgbgh != null">
				zl_fhbpgba_pgbgh=#{zlFhbpgbaPgbgh},
			</if>
			<if test="zlFhbpgbaHzdwmc != null">
				zl_fhbpgba_hzdwmc=#{zlFhbpgbaHzdwmc},
			</if>
			<if test="zlFhbpgbaHzwjh != null">
				zl_fhbpgba_hzwjh=#{zlFhbpgbaHzwjh},
			</if>
			<if test="zlBdpgbaZjjgmc != null">
				zl_bdpgba_zjjgmc=#{zlBdpgbaZjjgmc},
			</if>
			<if test="zlBdpgbaPgbgh != null">
				zl_bdpgba_pgbgh=#{zlBdpgbaPgbgh},
			</if>
			<if test="zlBdpgbaHzdwmc != null">
				zl_bdpgba_hzdwmc=#{zlBdpgbaHzdwmc},
			</if>
			<if test="zlBdpgbaHzwjh != null">
				zl_bdpgba_hzwjh=#{zlBdpgbaHzwjh},
			</if>
			<if test="zlGytdbaPzdw != null">
				zl_gytdba_pzdw=#{zlGytdbaPzdw},
			</if>
			<if test="zlGytdbaPzwh != null">
				zl_gytdba_pzwh=#{zlGytdbaPzwh},
			</if>
			<if test="zlYwblsqwj != null">
				zl_ywblsqwj=#{zlYwblsqwj},
			</if>
			<if test="zlZhypgbabYw != null">
				zl_zhypgbab_yw=#{zlZhypgbabYw},
			</if>
			<if test="zlZhypgbabZjjgmc != null">
				zl_zhypgbab_zjjgmc=#{zlZhypgbabZjjgmc},
			</if>
			<if test="zlZhypgbabPgbgh != null">
				zl_zhypgbab_pgbgh=#{zlZhypgbabPgbgh},
			</if>
			<if test="zlZhypgbabHzdwmc != null">
				zl_zhypgbab_hzdwmc=#{zlZhypgbabHzdwmc},
			</if>
			<if test="zlZhypgbabHzwjh != null">
				zl_zhypgbab_hzwjh=#{zlZhypgbabHzwjh},
			</if>
			<if test="zlZhypgbabLy != null">
				zl_zhypgbab_ly=#{zlZhypgbabLy},
			</if>
			<if test="zlZhepgbabYw != null">
				zl_zhepgbab_yw=#{zlZhepgbabYw},
			</if>
			<if test="zlZhepgbabZjjgmc != null">
				zl_zhepgbab_zjjgmc=#{zlZhepgbabZjjgmc},
			</if>
			<if test="zlZhepgbabPgbgh != null">
				zl_zhepgbab_pgbgh=#{zlZhepgbabPgbgh},
			</if>
			<if test="zlZhepgbabHzdwmc != null">
				zl_zhepgbab_hzdwmc=#{zlZhepgbabHzdwmc},
			</if>
			<if test="zlZhepgbabHzwjh != null">
				zl_zhepgbab_hzwjh=#{zlZhepgbabHzwjh},
			</if>
			<if test="zlZhepgbabLy != null">
				zl_zhepgbab_ly=#{zlZhepgbabLy},
			</if>
			<if test="zlXbpgbabYw != null">
				zl_xbpgbab_yw=#{zlXbpgbabYw},
			</if>
			<if test="zlXbpgbabZjjgmc != null">
				zl_xbpgbab_zjjgmc=#{zlXbpgbabZjjgmc},
			</if>
			<if test="zlXbpgbabPgbgh != null">
				zl_xbpgbab_pgbgh=#{zlXbpgbabPgbgh},
			</if>
			<if test="zlXbpgbabHzdwmc != null">
				zl_xbpgbab_hzdwmc=#{zlXbpgbabHzdwmc},
			</if>
			<if test="zlXbpgbabHzwjh != null">
				zl_xbpgbab_hzwjh=#{zlXbpgbabHzwjh},
			</if>
			<if test="zlXbpgbabLy != null">
				zl_xbpgbab_ly=#{zlXbpgbabLy},
			</if>
			<if test="zlBxbpgbabYw != null">
				zl_bxbpgbab_yw=#{zlBxbpgbabYw},
			</if>
			<if test="zlBxbpgbabZjjgmc != null">
				zl_bxbpgbab_zjjgmc=#{zlBxbpgbabZjjgmc},
			</if>
			<if test="zlBxbpgbabPgbgh != null">
				zl_bxbpgbab_pgbgh=#{zlBxbpgbabPgbgh},
			</if>
			<if test="zlBxbpgbabHzdwmc != null">
				zl_bxbpgbab_hzdwmc=#{zlBxbpgbabHzdwmc},
			</if>
			<if test="zlBxbpgbabHzwjh != null">
				zl_bxbpgbab_hzwjh=#{zlBxbpgbabHzwjh},
			</if>
			<if test="zlBxbpgbabLy != null">
				zl_bxbpgbab_ly=#{zlBxbpgbabLy},
			</if>
			<if test="zlTjpgbabYw != null">
				zl_tjpgbab_yw=#{zlTjpgbabYw},
			</if>
			<if test="zlTjpgbabZjjgmc != null">
				zl_tjpgbab_zjjgmc=#{zlTjpgbabZjjgmc},
			</if>
			<if test="zlTjpgbabPgbgh != null">
				zl_tjpgbab_pgbgh=#{zlTjpgbabPgbgh},
			</if>
			<if test="zlTjpgbabHzdwmc != null">
				zl_tjpgbab_hzdwmc=#{zlTjpgbabHzdwmc},
			</if>
			<if test="zlTjpgbabHzwjh != null">
				zl_tjpgbab_hzwjh=#{zlTjpgbabHzwjh},
			</if>
			<if test="zlTjpgbabLy != null">
				zl_tjpgbab_ly=#{zlTjpgbabLy},
			</if>
			<if test="zlSyzcczxyYw != null">
				zl_syzcczxy_yw=#{zlSyzcczxyYw},
			</if>
			<if test="zlSyzcczxyLy != null">
				zl_syzcczxy_ly=#{zlSyzcczxyLy},
			</if>
			<if test="zlZgdbdhjyYw != null">
				zl_zgdbdhjy_yw=#{zlZgdbdhjyYw},
			</if>
			<if test="zlZgdbdhjyYj != null">
				zl_zgdbdhjy_yj=#{zlZgdbdhjyYj},
			</if>
			<if test="zlZgdbdhjyLy != null">
				zl_zgdbdhjy_ly=#{zlZgdbdhjyLy},
			</if>
			<if test="zlGqszfawjYw != null">
				zl_gqszfawj_yw=#{zlGqszfawjYw},
			</if>
			<if test="zlGqszfawjPzdw != null">
				zl_gqszfawj_pzdw=#{zlGqszfawjPzdw},
			</if>
			<if test="zlGqszfawjPzwh != null">
				zl_gqszfawj_pzwh=#{zlGqszfawjPzwh},
			</if>
			<if test="zlGqszfawjLy != null">
				zl_gqszfawj_ly=#{zlGqszfawjLy},
			</if>
			<if test="zlGdqkdjbYw != null">
				zl_gdqkdjb_yw=#{zlGdqkdjbYw},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_ywzbb
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			xx_pgjzcz=#{xxPgjzcz},
			xx_sjjzcz=#{xxSjjzcz},
			xx_fxjg=#{xxFxjg},
			zl_jcwj_ly=#{zlJcwjLy},
			zl_yzbg_zjjgmc=#{zlYzbgZjjgmc},
			zl_yzbg_yzbgh=#{zlYzbgYzbgh},
			zl_yzbg_ly=#{zlYzbgLy},
			zl_yzbg_yzcjr=#{zlYzbgYzcjr},
			zl_qyzc_ly=#{zlQyzcLy},
			zl_yyzz_ly=#{zlYyzzLy},
			zl_pgba_ly=#{zlPgbaLy},
			zl_fhbpgba_ly=#{zlFhbpgbaLy},
			zl_bdpgba_ly=#{zlBdpgbaLy},
			zl_gytdba_ly=#{zlGytdbaLy},
			zl_jcjg_jyjg=#{zlJcjgJyjg},
			zl_jcjg_jgd=#{zlJcjgJgd},
			zl_jcjg_cjrq=#{zlJcjgCjrq},
			zl_jcjg_ly=#{zlJcjgLy},
			zl_sjbg_zjjg=#{zlSjbgZjjg},
			zl_sjbg_bgh=#{zlSjbgBgh},
			zl_sjbg_cjrq=#{zlSjbgCjrq},
			zl_sjbg_ly=#{zlSjbgLy},
			zl_gqzr_ly=#{zlGqzrLy},
			zl_zhxy_ly=#{zlZhxyLy},
			zl_hbxys_ly=#{zlHbxysLy},
			zl_flxys_ly=#{zlFlxysLy},
			zl_wchzxy_ly=#{zlWchzxyLy},
			zl_jzrsjbg_zjjgmc=#{zlJzrsjbgZjjgmc},
			zl_jzrsjbg_yzbgh=#{zlJzrsjbgYzbgh},
			zl_jzrsjbg_ly=#{zlJzrsjbgLy},
			zl_jzgg_mtmc=#{zlJzggMtmc},
			zl_jzgg_ggrq=#{zlJzggGgrq},
			zl_jzgg_ly=#{zlJzggLy},
			xx_cjfmc=#{xxCjfmc},
			xx_sfzj=#{xxSfzj},
			zl_zjyqsjbg_zjjg=#{zlZjyqsjbgZjjg},
			zl_zjyqsjbg_bgh=#{zlZjyqsjbgBgh},
			zl_zjyqsjbg_ly=#{zlZjyqsjbgLy},
			zl_tzxy_ly=#{zlTzxyLy},
			xx_qsjzcz=#{xxQsjzcz},
			xx_qsfy=#{xxQsfy},
			xx_qczw=#{xxQczw},
			xx_pcccze=#{xxPcccze},
			xx_pcfyhgyzw=#{xxPcfyhgyzw},
			xx_ptzw=#{xxPtzw},
			zl_qsbg_zjjgmc=#{zlQsbgZjjgmc},
			zl_qsbg_bgh=#{zlQsbgBgh},
			zl_qsbg_ly=#{zlQsbgLy},
			zl_zxgg_mtmc=#{zlZxggMtmc},
			zl_zxgg_ggrq=#{zlZxggGgrq},
			zl_zxgg_ly=#{zlZxggLy},
			zl_gszxzm_ly=#{zlGszxzmLy},
			zl_pcgg_mtmc=#{zlPcggMtmc},
			zl_pcgg_ggrq=#{zlPcggGgrq},
			zl_pcgg_ly=#{zlPcggLy},
			zl_tzxy_yw=#{zlTzxyYw},
			zl_jcwj_yw=#{zlJcwjYw},
			zl_jzgg_yw=#{zlJzggYw},
			zl_pcgg_yw=#{zlPcggYw},
			zl_jzrsjbg_yw=#{zlJzrsjbgYw},
			zl_qyzc_yw=#{zlQyzcYw},
			zl_jcjg_yw=#{zlJcjgYw},
			zl_sjbg_yw=#{zlSjbgYw},
			zl_fhbpgba_yw=#{zlFhbpgbaYw},
			zl_yyzz_yw=#{zlYyzzYw},
			zl_pgba_yw=#{zlPgbaYw},
			zl_gszxzm_yw=#{zlGszxzmYw},
			zl_gytdba_yw=#{zlGytdbaYw},
			zl_yzbg_yw=#{zlYzbgYw},
			zl_hbxys_yw=#{zlHbxysYw},
			zl_zhxy_yw=#{zlZhxyYw},
			zl_gqzr_yw=#{zlGqzrYw},
			zl_wchzxy_yw=#{zlWchzxyYw},
			zl_bdpgba_yw=#{zlBdpgbaYw},
			zl_zjyqsjbg_yw=#{zlZjyqsjbgYw},
			zl_flxys_yw=#{zlFlxysYw},
			zl_zxgg_yw=#{zlZxggYw},
			zl_qsbg_yw=#{zlQsbgYw},
			xx_bdqyxz=#{xxBdqyxz},
			xx_bxbfxz=#{xxBxbfxz},
			xx_jsyy=#{xxJsyy},
			zl_gszxzm_zxrq=#{zlGszxzmZxrq},
			zl_jcwj_dwmc=#{zlJcwjDwmc},
			zl_jcwj_wjmc=#{zlJcwjWjmc},
			zl_jcwj_wjh=#{zlJcwjWjh},
			zl_pgba_zjjgmc=#{zlPgbaZjjgmc},
			zl_pgba_pgbgh=#{zlPgbaPgbgh},
			zl_pgba_hzdwmc=#{zlPgbaHzdwmc},
			zl_pgba_hzwjh=#{zlPgbaHzwjh},
			zl_fhbpgba_zjjgmc=#{zlFhbpgbaZjjgmc},
			zl_fhbpgba_pgbgh=#{zlFhbpgbaPgbgh},
			zl_fhbpgba_hzdwmc=#{zlFhbpgbaHzdwmc},
			zl_fhbpgba_hzwjh=#{zlFhbpgbaHzwjh},
			zl_bdpgba_zjjgmc=#{zlBdpgbaZjjgmc},
			zl_bdpgba_pgbgh=#{zlBdpgbaPgbgh},
			zl_bdpgba_hzdwmc=#{zlBdpgbaHzdwmc},
			zl_bdpgba_hzwjh=#{zlBdpgbaHzwjh},
			zl_gytdba_pzdw=#{zlGytdbaPzdw},
			zl_gytdba_pzwh=#{zlGytdbaPzwh},
			zl_ywblsqwj=#{zlYwblsqwj},
			zl_zhypgbab_yw=#{zlZhypgbabYw},
			zl_zhypgbab_zjjgmc=#{zlZhypgbabZjjgmc},
			zl_zhypgbab_pgbgh=#{zlZhypgbabPgbgh},
			zl_zhypgbab_hzdwmc=#{zlZhypgbabHzdwmc},
			zl_zhypgbab_hzwjh=#{zlZhypgbabHzwjh},
			zl_zhypgbab_ly=#{zlZhypgbabLy},
			zl_zhepgbab_yw=#{zlZhepgbabYw},
			zl_zhepgbab_zjjgmc=#{zlZhepgbabZjjgmc},
			zl_zhepgbab_pgbgh=#{zlZhepgbabPgbgh},
			zl_zhepgbab_hzdwmc=#{zlZhepgbabHzdwmc},
			zl_zhepgbab_hzwjh=#{zlZhepgbabHzwjh},
			zl_zhepgbab_ly=#{zlZhepgbabLy},
			zl_xbpgbab_yw=#{zlXbpgbabYw},
			zl_xbpgbab_zjjgmc=#{zlXbpgbabZjjgmc},
			zl_xbpgbab_pgbgh=#{zlXbpgbabPgbgh},
			zl_xbpgbab_hzdwmc=#{zlXbpgbabHzdwmc},
			zl_xbpgbab_hzwjh=#{zlXbpgbabHzwjh},
			zl_xbpgbab_ly=#{zlXbpgbabLy},
			zl_bxbpgbab_yw=#{zlBxbpgbabYw},
			zl_bxbpgbab_zjjgmc=#{zlBxbpgbabZjjgmc},
			zl_bxbpgbab_pgbgh=#{zlBxbpgbabPgbgh},
			zl_bxbpgbab_hzdwmc=#{zlBxbpgbabHzdwmc},
			zl_bxbpgbab_hzwjh=#{zlBxbpgbabHzwjh},
			zl_bxbpgbab_ly=#{zlBxbpgbabLy},
			zl_tjpgbab_yw=#{zlTjpgbabYw},
			zl_tjpgbab_zjjgmc=#{zlTjpgbabZjjgmc},
			zl_tjpgbab_pgbgh=#{zlTjpgbabPgbgh},
			zl_tjpgbab_hzdwmc=#{zlTjpgbabHzdwmc},
			zl_tjpgbab_hzwjh=#{zlTjpgbabHzwjh},
			zl_tjpgbab_ly=#{zlTjpgbabLy},
			zl_syzcczxy_yw=#{zlSyzcczxyYw},
			zl_syzcczxy_ly=#{zlSyzcczxyLy},
			zl_zgdbdhjy_yw=#{zlZgdbdhjyYw},
			zl_zgdbdhjy_yj=#{zlZgdbdhjyYj},
			zl_zgdbdhjy_ly=#{zlZgdbdhjyLy},
			zl_gqszfawj_yw=#{zlGqszfawjYw},
			zl_gqszfawj_pzdw=#{zlGqszfawjPzdw},
			zl_gqszfawj_pzwh=#{zlGqszfawjPzwh},
			zl_gqszfawj_ly=#{zlGqszfawjLy},
			zl_gdqkdjb_yw=#{zlGdqkdjbYw},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.ywzbb.entity.Ywzbb" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_ywzbb t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalYwzbbs" parameterType="com.zjhc.gzwcq.ywzbb.entity.YwzbbParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_ywzbb t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryYwzbbForList" parameterType="com.zjhc.gzwcq.ywzbb.entity.YwzbbParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_ywzbb t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.ywzbb.entity.Ywzbb" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_ywzbb t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="xxPgjzcz != null and xxPgjzcz != ''">
				and t.xx_pgjzcz = #{xxPgjzcz}
			</if>
			<if test="xxSjjzcz != null and xxSjjzcz != ''">
				and t.xx_sjjzcz = #{xxSjjzcz}
			</if>
			<if test="xxFxjg != null and xxFxjg != ''">
				and t.xx_fxjg = #{xxFxjg}
			</if>
			<if test="zlJcwjLy != null and zlJcwjLy != ''">
				and t.zl_jcwj_ly = #{zlJcwjLy}
			</if>
			<if test="zlYzbgZjjgmc != null and zlYzbgZjjgmc != ''">
				and t.zl_yzbg_zjjgmc = #{zlYzbgZjjgmc}
			</if>
			<if test="zlYzbgYzbgh != null and zlYzbgYzbgh != ''">
				and t.zl_yzbg_yzbgh = #{zlYzbgYzbgh}
			</if>
			<if test="zlYzbgLy != null and zlYzbgLy != ''">
				and t.zl_yzbg_ly = #{zlYzbgLy}
			</if>
			<if test="zlYzbgYzcjr != null">
				and t.zl_yzbg_yzcjr = #{zlYzbgYzcjr}
			</if>
			<if test="zlQyzcLy != null and zlQyzcLy != ''">
				and t.zl_qyzc_ly = #{zlQyzcLy}
			</if>
			<if test="zlYyzzLy != null and zlYyzzLy != ''">
				and t.zl_yyzz_ly = #{zlYyzzLy}
			</if>
			<if test="zlPgbaLy != null and zlPgbaLy != ''">
				and t.zl_pgba_ly = #{zlPgbaLy}
			</if>
			<if test="zlFhbpgbaLy != null and zlFhbpgbaLy != ''">
				and t.zl_fhbpgba_ly = #{zlFhbpgbaLy}
			</if>
			<if test="zlBdpgbaLy != null and zlBdpgbaLy != ''">
				and t.zl_bdpgba_ly = #{zlBdpgbaLy}
			</if>
			<if test="zlGytdbaLy != null and zlGytdbaLy != ''">
				and t.zl_gytdba_ly = #{zlGytdbaLy}
			</if>
			<if test="zlJcjgJyjg != null and zlJcjgJyjg != ''">
				and t.zl_jcjg_jyjg = #{zlJcjgJyjg}
			</if>
			<if test="zlJcjgJgd != null and zlJcjgJgd != ''">
				and t.zl_jcjg_jgd = #{zlJcjgJgd}
			</if>
			<if test="zlJcjgCjrq != null">
				and t.zl_jcjg_cjrq = #{zlJcjgCjrq}
			</if>
			<if test="zlJcjgLy != null and zlJcjgLy != ''">
				and t.zl_jcjg_ly = #{zlJcjgLy}
			</if>
			<if test="zlSjbgZjjg != null and zlSjbgZjjg != ''">
				and t.zl_sjbg_zjjg = #{zlSjbgZjjg}
			</if>
			<if test="zlSjbgBgh != null and zlSjbgBgh != ''">
				and t.zl_sjbg_bgh = #{zlSjbgBgh}
			</if>
			<if test="zlSjbgCjrq != null">
				and t.zl_sjbg_cjrq = #{zlSjbgCjrq}
			</if>
			<if test="zlSjbgLy != null and zlSjbgLy != ''">
				and t.zl_sjbg_ly = #{zlSjbgLy}
			</if>
			<if test="zlGqzrLy != null and zlGqzrLy != ''">
				and t.zl_gqzr_ly = #{zlGqzrLy}
			</if>
			<if test="zlZhxyLy != null and zlZhxyLy != ''">
				and t.zl_zhxy_ly = #{zlZhxyLy}
			</if>
			<if test="zlHbxysLy != null and zlHbxysLy != ''">
				and t.zl_hbxys_ly = #{zlHbxysLy}
			</if>
			<if test="zlFlxysLy != null and zlFlxysLy != ''">
				and t.zl_flxys_ly = #{zlFlxysLy}
			</if>
			<if test="zlWchzxyLy != null and zlWchzxyLy != ''">
				and t.zl_wchzxy_ly = #{zlWchzxyLy}
			</if>
			<if test="zlJzrsjbgZjjgmc != null and zlJzrsjbgZjjgmc != ''">
				and t.zl_jzrsjbg_zjjgmc = #{zlJzrsjbgZjjgmc}
			</if>
			<if test="zlJzrsjbgYzbgh != null and zlJzrsjbgYzbgh != ''">
				and t.zl_jzrsjbg_yzbgh = #{zlJzrsjbgYzbgh}
			</if>
			<if test="zlJzrsjbgLy != null and zlJzrsjbgLy != ''">
				and t.zl_jzrsjbg_ly = #{zlJzrsjbgLy}
			</if>
			<if test="zlJzggMtmc != null and zlJzggMtmc != ''">
				and t.zl_jzgg_mtmc = #{zlJzggMtmc}
			</if>
			<if test="zlJzggGgrq != null">
				and t.zl_jzgg_ggrq = #{zlJzggGgrq}
			</if>
			<if test="zlJzggLy != null and zlJzggLy != ''">
				and t.zl_jzgg_ly = #{zlJzggLy}
			</if>
			<if test="xxCjfmc != null and xxCjfmc != ''">
				and t.xx_cjfmc = #{xxCjfmc}
			</if>
			<if test="xxSfzj != null and xxSfzj != ''">
				and t.xx_sfzj = #{xxSfzj}
			</if>
			<if test="zlZjyqsjbgZjjg != null and zlZjyqsjbgZjjg != ''">
				and t.zl_zjyqsjbg_zjjg = #{zlZjyqsjbgZjjg}
			</if>
			<if test="zlZjyqsjbgBgh != null and zlZjyqsjbgBgh != ''">
				and t.zl_zjyqsjbg_bgh = #{zlZjyqsjbgBgh}
			</if>
			<if test="zlZjyqsjbgLy != null and zlZjyqsjbgLy != ''">
				and t.zl_zjyqsjbg_ly = #{zlZjyqsjbgLy}
			</if>
			<if test="zlTzxyLy != null and zlTzxyLy != ''">
				and t.zl_tzxy_ly = #{zlTzxyLy}
			</if>
			<if test="xxQsjzcz != null and xxQsjzcz != ''">
				and t.xx_qsjzcz = #{xxQsjzcz}
			</if>
			<if test="xxQsfy != null and xxQsfy != ''">
				and t.xx_qsfy = #{xxQsfy}
			</if>
			<if test="xxQczw != null and xxQczw != ''">
				and t.xx_qczw = #{xxQczw}
			</if>
			<if test="xxPcccze != null and xxPcccze != ''">
				and t.xx_pcccze = #{xxPcccze}
			</if>
			<if test="xxPcfyhgyzw != null and xxPcfyhgyzw != ''">
				and t.xx_pcfyhgyzw = #{xxPcfyhgyzw}
			</if>
			<if test="xxPtzw != null and xxPtzw != ''">
				and t.xx_ptzw = #{xxPtzw}
			</if>
			<if test="zlQsbgZjjgmc != null and zlQsbgZjjgmc != ''">
				and t.zl_qsbg_zjjgmc = #{zlQsbgZjjgmc}
			</if>
			<if test="zlQsbgBgh != null and zlQsbgBgh != ''">
				and t.zl_qsbg_bgh = #{zlQsbgBgh}
			</if>
			<if test="zlQsbgLy != null and zlQsbgLy != ''">
				and t.zl_qsbg_ly = #{zlQsbgLy}
			</if>
			<if test="zlZxggMtmc != null and zlZxggMtmc != ''">
				and t.zl_zxgg_mtmc = #{zlZxggMtmc}
			</if>
			<if test="zlZxggGgrq != null">
				and t.zl_zxgg_ggrq = #{zlZxggGgrq}
			</if>
			<if test="zlZxggLy != null and zlZxggLy != ''">
				and t.zl_zxgg_ly = #{zlZxggLy}
			</if>
			<if test="zlGszxzmLy != null and zlGszxzmLy != ''">
				and t.zl_gszxzm_ly = #{zlGszxzmLy}
			</if>
			<if test="zlPcggMtmc != null and zlPcggMtmc != ''">
				and t.zl_pcgg_mtmc = #{zlPcggMtmc}
			</if>
			<if test="zlPcggGgrq != null">
				and t.zl_pcgg_ggrq = #{zlPcggGgrq}
			</if>
			<if test="zlPcggLy != null and zlPcggLy != ''">
				and t.zl_pcgg_ly = #{zlPcggLy}
			</if>
			<if test="zlTzxyYw != null and zlTzxyYw != ''">
				and t.zl_tzxy_yw = #{zlTzxyYw}
			</if>
			<if test="zlJcwjYw != null and zlJcwjYw != ''">
				and t.zl_jcwj_yw = #{zlJcwjYw}
			</if>
			<if test="zlJzggYw != null and zlJzggYw != ''">
				and t.zl_jzgg_yw = #{zlJzggYw}
			</if>
			<if test="zlPcggYw != null and zlPcggYw != ''">
				and t.zl_pcgg_yw = #{zlPcggYw}
			</if>
			<if test="zlJzrsjbgYw != null and zlJzrsjbgYw != ''">
				and t.zl_jzrsjbg_yw = #{zlJzrsjbgYw}
			</if>
			<if test="zlQyzcYw != null and zlQyzcYw != ''">
				and t.zl_qyzc_yw = #{zlQyzcYw}
			</if>
			<if test="zlJcjgYw != null and zlJcjgYw != ''">
				and t.zl_jcjg_yw = #{zlJcjgYw}
			</if>
			<if test="zlSjbgYw != null and zlSjbgYw != ''">
				and t.zl_sjbg_yw = #{zlSjbgYw}
			</if>
			<if test="zlFhbpgbaYw != null and zlFhbpgbaYw != ''">
				and t.zl_fhbpgba_yw = #{zlFhbpgbaYw}
			</if>
			<if test="zlYyzzYw != null and zlYyzzYw != ''">
				and t.zl_yyzz_yw = #{zlYyzzYw}
			</if>
			<if test="zlPgbaYw != null and zlPgbaYw != ''">
				and t.zl_pgba_yw = #{zlPgbaYw}
			</if>
			<if test="zlGszxzmYw != null and zlGszxzmYw != ''">
				and t.zl_gszxzm_yw = #{zlGszxzmYw}
			</if>
			<if test="zlGytdbaYw != null and zlGytdbaYw != ''">
				and t.zl_gytdba_yw = #{zlGytdbaYw}
			</if>
			<if test="zlYzbgYw != null and zlYzbgYw != ''">
				and t.zl_yzbg_yw = #{zlYzbgYw}
			</if>
			<if test="zlHbxysYw != null and zlHbxysYw != ''">
				and t.zl_hbxys_yw = #{zlHbxysYw}
			</if>
			<if test="zlZhxyYw != null and zlZhxyYw != ''">
				and t.zl_zhxy_yw = #{zlZhxyYw}
			</if>
			<if test="zlGqzrYw != null and zlGqzrYw != ''">
				and t.zl_gqzr_yw = #{zlGqzrYw}
			</if>
			<if test="zlWchzxyYw != null and zlWchzxyYw != ''">
				and t.zl_wchzxy_yw = #{zlWchzxyYw}
			</if>
			<if test="zlBdpgbaYw != null and zlBdpgbaYw != ''">
				and t.zl_bdpgba_yw = #{zlBdpgbaYw}
			</if>
			<if test="zlZjyqsjbgYw != null and zlZjyqsjbgYw != ''">
				and t.zl_zjyqsjbg_yw = #{zlZjyqsjbgYw}
			</if>
			<if test="zlFlxysYw != null and zlFlxysYw != ''">
				and t.zl_flxys_yw = #{zlFlxysYw}
			</if>
			<if test="zlZxggYw != null and zlZxggYw != ''">
				and t.zl_zxgg_yw = #{zlZxggYw}
			</if>
			<if test="zlQsbgYw != null and zlQsbgYw != ''">
				and t.zl_qsbg_yw = #{zlQsbgYw}
			</if>
			<if test="xxBdqyxz != null and xxBdqyxz != ''">
				and t.xx_bdqyxz = #{xxBdqyxz}
			</if>
			<if test="xxBxbfxz != null and xxBxbfxz != ''">
				and t.xx_bxbfxz = #{xxBxbfxz}
			</if>
			<if test="xxJsyy != null and xxJsyy != ''">
				and t.xx_jsyy = #{xxJsyy}
			</if>
			<if test="zlGszxzmZxrq != null">
				and t.zl_gszxzm_zxrq = #{zlGszxzmZxrq}
			</if>
			<if test="zlJcwjDwmc != null and zlJcwjDwmc != ''">
				and t.zl_jcwj_dwmc = #{zlJcwjDwmc}
			</if>
			<if test="zlJcwjWjmc != null and zlJcwjWjmc != ''">
				and t.zl_jcwj_wjmc = #{zlJcwjWjmc}
			</if>
			<if test="zlJcwjWjh != null and zlJcwjWjh != ''">
				and t.zl_jcwj_wjh = #{zlJcwjWjh}
			</if>
			<if test="zlPgbaZjjgmc != null and zlPgbaZjjgmc != ''">
				and t.zl_pgba_zjjgmc = #{zlPgbaZjjgmc}
			</if>
			<if test="zlPgbaPgbgh != null and zlPgbaPgbgh != ''">
				and t.zl_pgba_pgbgh = #{zlPgbaPgbgh}
			</if>
			<if test="zlPgbaHzdwmc != null and zlPgbaHzdwmc != ''">
				and t.zl_pgba_hzdwmc = #{zlPgbaHzdwmc}
			</if>
			<if test="zlPgbaHzwjh != null and zlPgbaHzwjh != ''">
				and t.zl_pgba_hzwjh = #{zlPgbaHzwjh}
			</if>
			<if test="zlFhbpgbaZjjgmc != null and zlFhbpgbaZjjgmc != ''">
				and t.zl_fhbpgba_zjjgmc = #{zlFhbpgbaZjjgmc}
			</if>
			<if test="zlFhbpgbaPgbgh != null and zlFhbpgbaPgbgh != ''">
				and t.zl_fhbpgba_pgbgh = #{zlFhbpgbaPgbgh}
			</if>
			<if test="zlFhbpgbaHzdwmc != null and zlFhbpgbaHzdwmc != ''">
				and t.zl_fhbpgba_hzdwmc = #{zlFhbpgbaHzdwmc}
			</if>
			<if test="zlFhbpgbaHzwjh != null and zlFhbpgbaHzwjh != ''">
				and t.zl_fhbpgba_hzwjh = #{zlFhbpgbaHzwjh}
			</if>
			<if test="zlBdpgbaZjjgmc != null and zlBdpgbaZjjgmc != ''">
				and t.zl_bdpgba_zjjgmc = #{zlBdpgbaZjjgmc}
			</if>
			<if test="zlBdpgbaPgbgh != null and zlBdpgbaPgbgh != ''">
				and t.zl_bdpgba_pgbgh = #{zlBdpgbaPgbgh}
			</if>
			<if test="zlBdpgbaHzdwmc != null and zlBdpgbaHzdwmc != ''">
				and t.zl_bdpgba_hzdwmc = #{zlBdpgbaHzdwmc}
			</if>
			<if test="zlBdpgbaHzwjh != null and zlBdpgbaHzwjh != ''">
				and t.zl_bdpgba_hzwjh = #{zlBdpgbaHzwjh}
			</if>
			<if test="zlGytdbaPzdw != null and zlGytdbaPzdw != ''">
				and t.zl_gytdba_pzdw = #{zlGytdbaPzdw}
			</if>
			<if test="zlGytdbaPzwh != null and zlGytdbaPzwh != ''">
				and t.zl_gytdba_pzwh = #{zlGytdbaPzwh}
			</if>
			<if test="zlYwblsqwj != null and zlYwblsqwj != ''">
				and t.zl_ywblsqwj = #{zlYwblsqwj}
			</if>
			<if test="zlZhypgbabYw != null and zlZhypgbabYw != ''">
				and t.zl_zhypgbab_yw = #{zlZhypgbabYw}
			</if>
			<if test="zlZhypgbabZjjgmc != null and zlZhypgbabZjjgmc != ''">
				and t.zl_zhypgbab_zjjgmc = #{zlZhypgbabZjjgmc}
			</if>
			<if test="zlZhypgbabPgbgh != null and zlZhypgbabPgbgh != ''">
				and t.zl_zhypgbab_pgbgh = #{zlZhypgbabPgbgh}
			</if>
			<if test="zlZhypgbabHzdwmc != null and zlZhypgbabHzdwmc != ''">
				and t.zl_zhypgbab_hzdwmc = #{zlZhypgbabHzdwmc}
			</if>
			<if test="zlZhypgbabHzwjh != null and zlZhypgbabHzwjh != ''">
				and t.zl_zhypgbab_hzwjh = #{zlZhypgbabHzwjh}
			</if>
			<if test="zlZhypgbabLy != null and zlZhypgbabLy != ''">
				and t.zl_zhypgbab_ly = #{zlZhypgbabLy}
			</if>
			<if test="zlZhepgbabYw != null and zlZhepgbabYw != ''">
				and t.zl_zhepgbab_yw = #{zlZhepgbabYw}
			</if>
			<if test="zlZhepgbabZjjgmc != null and zlZhepgbabZjjgmc != ''">
				and t.zl_zhepgbab_zjjgmc = #{zlZhepgbabZjjgmc}
			</if>
			<if test="zlZhepgbabPgbgh != null and zlZhepgbabPgbgh != ''">
				and t.zl_zhepgbab_pgbgh = #{zlZhepgbabPgbgh}
			</if>
			<if test="zlZhepgbabHzdwmc != null and zlZhepgbabHzdwmc != ''">
				and t.zl_zhepgbab_hzdwmc = #{zlZhepgbabHzdwmc}
			</if>
			<if test="zlZhepgbabHzwjh != null and zlZhepgbabHzwjh != ''">
				and t.zl_zhepgbab_hzwjh = #{zlZhepgbabHzwjh}
			</if>
			<if test="zlZhepgbabLy != null and zlZhepgbabLy != ''">
				and t.zl_zhepgbab_ly = #{zlZhepgbabLy}
			</if>
			<if test="zlXbpgbabYw != null and zlXbpgbabYw != ''">
				and t.zl_xbpgbab_yw = #{zlXbpgbabYw}
			</if>
			<if test="zlXbpgbabZjjgmc != null and zlXbpgbabZjjgmc != ''">
				and t.zl_xbpgbab_zjjgmc = #{zlXbpgbabZjjgmc}
			</if>
			<if test="zlXbpgbabPgbgh != null and zlXbpgbabPgbgh != ''">
				and t.zl_xbpgbab_pgbgh = #{zlXbpgbabPgbgh}
			</if>
			<if test="zlXbpgbabHzdwmc != null and zlXbpgbabHzdwmc != ''">
				and t.zl_xbpgbab_hzdwmc = #{zlXbpgbabHzdwmc}
			</if>
			<if test="zlXbpgbabHzwjh != null and zlXbpgbabHzwjh != ''">
				and t.zl_xbpgbab_hzwjh = #{zlXbpgbabHzwjh}
			</if>
			<if test="zlXbpgbabLy != null and zlXbpgbabLy != ''">
				and t.zl_xbpgbab_ly = #{zlXbpgbabLy}
			</if>
			<if test="zlBxbpgbabYw != null and zlBxbpgbabYw != ''">
				and t.zl_bxbpgbab_yw = #{zlBxbpgbabYw}
			</if>
			<if test="zlBxbpgbabZjjgmc != null and zlBxbpgbabZjjgmc != ''">
				and t.zl_bxbpgbab_zjjgmc = #{zlBxbpgbabZjjgmc}
			</if>
			<if test="zlBxbpgbabPgbgh != null and zlBxbpgbabPgbgh != ''">
				and t.zl_bxbpgbab_pgbgh = #{zlBxbpgbabPgbgh}
			</if>
			<if test="zlBxbpgbabHzdwmc != null and zlBxbpgbabHzdwmc != ''">
				and t.zl_bxbpgbab_hzdwmc = #{zlBxbpgbabHzdwmc}
			</if>
			<if test="zlBxbpgbabHzwjh != null and zlBxbpgbabHzwjh != ''">
				and t.zl_bxbpgbab_hzwjh = #{zlBxbpgbabHzwjh}
			</if>
			<if test="zlBxbpgbabLy != null and zlBxbpgbabLy != ''">
				and t.zl_bxbpgbab_ly = #{zlBxbpgbabLy}
			</if>
			<if test="zlTjpgbabYw != null and zlTjpgbabYw != ''">
				and t.zl_tjpgbab_yw = #{zlTjpgbabYw}
			</if>
			<if test="zlTjpgbabZjjgmc != null and zlTjpgbabZjjgmc != ''">
				and t.zl_tjpgbab_zjjgmc = #{zlTjpgbabZjjgmc}
			</if>
			<if test="zlTjpgbabPgbgh != null and zlTjpgbabPgbgh != ''">
				and t.zl_tjpgbab_pgbgh = #{zlTjpgbabPgbgh}
			</if>
			<if test="zlTjpgbabHzdwmc != null and zlTjpgbabHzdwmc != ''">
				and t.zl_tjpgbab_hzdwmc = #{zlTjpgbabHzdwmc}
			</if>
			<if test="zlTjpgbabHzwjh != null and zlTjpgbabHzwjh != ''">
				and t.zl_tjpgbab_hzwjh = #{zlTjpgbabHzwjh}
			</if>
			<if test="zlTjpgbabLy != null and zlTjpgbabLy != ''">
				and t.zl_tjpgbab_ly = #{zlTjpgbabLy}
			</if>
			<if test="zlSyzcczxyYw != null and zlSyzcczxyYw != ''">
				and t.zl_syzcczxy_yw = #{zlSyzcczxyYw}
			</if>
			<if test="zlSyzcczxyLy != null and zlSyzcczxyLy != ''">
				and t.zl_syzcczxy_ly = #{zlSyzcczxyLy}
			</if>
			<if test="zlZgdbdhjyYw != null and zlZgdbdhjyYw != ''">
				and t.zl_zgdbdhjy_yw = #{zlZgdbdhjyYw}
			</if>
			<if test="zlZgdbdhjyYj != null and zlZgdbdhjyYj != ''">
				and t.zl_zgdbdhjy_yj = #{zlZgdbdhjyYj}
			</if>
			<if test="zlZgdbdhjyLy != null and zlZgdbdhjyLy != ''">
				and t.zl_zgdbdhjy_ly = #{zlZgdbdhjyLy}
			</if>
			<if test="zlGqszfawjYw != null and zlGqszfawjYw != ''">
				and t.zl_gqszfawj_yw = #{zlGqszfawjYw}
			</if>
			<if test="zlGqszfawjPzdw != null and zlGqszfawjPzdw != ''">
				and t.zl_gqszfawj_pzdw = #{zlGqszfawjPzdw}
			</if>
			<if test="zlGqszfawjPzwh != null and zlGqszfawjPzwh != ''">
				and t.zl_gqszfawj_pzwh = #{zlGqszfawjPzwh}
			</if>
			<if test="zlGqszfawjLy != null and zlGqszfawjLy != ''">
				and t.zl_gqszfawj_ly = #{zlGqszfawjLy}
			</if>
			<if test="zlGdqkdjbYw != null and zlGdqkdjbYw != ''">
				and t.zl_gdqkdjb_yw = #{zlGdqkdjbYw}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<resultMap id="resultMapWithAttachment" type="com.zjhc.gzwcq.ywzbb.entity.YwzbbVo" extends="baseResultMapExt">
		<collection property="mainAttachments" ofType="com.zjhc.gzwcq.attachment.entity.Attachment">
			<id column="a_id" property="id"/>
			<result column="a_file_name" property="fileName"/>
			<result column="a_last_file_name" property="lastFileName"/>
			<result column="a_file_type" property="fileType"/>
			<result column="a_ftp_file_path" property="ftpFilePath"/>
			<result column="a_org_id" property="orgId"/>
			<result column="a_remark" property="remark"/>
			<result column="a_create_user" property="createUser"/>
			<result column="a_create_time" property="createTime"/>
			<result column="a_last_update_user" property="lastUpdateUser"/>
			<result column="a_last_update_time" property="lastUpdateTime"/>
		</collection>
	</resultMap>
	<select id="selectByJbxxId" parameterType="java.lang.String" resultMap="resultMapWithAttachment">
		select
		<include refid="columnsAlias"/>,su1.name createUserStr,su2.name lastUpdateUserStr,
		sa.id a_id,sa.file_name a_file_name,sa.last_file_name a_last_file_name,
		sa.file_type a_file_type,sa.ftp_file_path a_ftp_file_path,sa.org_id a_org_id,
		sa.remark a_remark,sa.create_user a_create_user,sa.create_time a_create_time,
		sa.last_update_user a_last_update_user,sa.last_update_time a_last_update_time,
		sd2.text xxBxbfxzStr,sd3.text xxBdqyxzStr,sd4.text zlZgdbdhjyYjStr
		from
		cq_ywzbb t
		left join sys_users su1 on su1.user_id = t.create_user
		left join sys_users su2 on su2.user_id = t.last_update_user
		left join sys_attachment sa on t.zl_ywblsqwj = sa.id
		left join sys_dictionary sd2 on sd2.val=t.XX_BXBFXZ and sd2.type_id=(select id from sys_dictionary where type_code='BXBFXZ')
		left join sys_dictionary sd3 on sd3.val=t.XX_BDQYXZ and sd3.type_id=(select id from sys_dictionary where type_code='BDQYXZ')
		left join sys_dictionary sd4 on sd4.val=t.zl_zgdbdhjy_yj and sd4.type_id=(select id from sys_dictionary where type_code='MJ_YJ')
		where t.jbxx_id = #{jbxxId}
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_ywzbb where jbxx_id = #{jbxxId}
	</delete>

	<select id="selectAllByJbxxId" resultMap="selectAllByjbxxIdMapper">
		SELECT
			<include refid="ywzbbVo"/>
		FROM
			`cq_ywzbb` cy
		JOIN
			cq_ywzbb_2 cy2 on cy2.JBXX_ID = cy.JBXX_ID
		WHERE
		cy.JBXX_ID =#{jbxxId}

	</select>
	<sql id="ywzbbVo">
		cy.xx_pgjzcz ,
		 cy.xx_sjjzcz ,
		 cy.xx_fxjg ,
		 cy.zl_jcwj_ly ,
		 cy.zl_yzbg_zjjgmc ,
		 cy.zl_yzbg_yzbgh ,
		 cy.zl_yzbg_ly ,
		 cy.zl_yzbg_yzcjr ,
		 cy.zl_qyzc_ly ,
		 cy.zl_yyzz_ly ,
		 cy.zl_pgba_ly ,
		 cy.zl_fhbpgba_ly ,
		 cy.zl_bdpgba_ly ,
		 cy.zl_gytdba_ly ,
		 cy.zl_jcjg_jyjg ,
		 cy.zl_jcjg_jgd ,
		 cy.zl_jcjg_cjrq ,
		 cy.zl_jcjg_ly ,
		 cy.zl_sjbg_zjjg ,
		 cy.zl_sjbg_bgh ,
		 cy.zl_sjbg_cjrq ,
		 cy.zl_sjbg_ly ,
		 cy.zl_gqzr_ly ,
		 cy.zl_zhxy_ly ,
		 cy.zl_hbxys_ly ,
		 cy.zl_flxys_ly ,
		 cy.zl_wchzxy_ly ,
		 cy.zl_jzrsjbg_zjjgmc ,
		 cy.zl_jzrsjbg_yzbgh ,
		 cy.zl_jzrsjbg_ly ,
		 cy.zl_jzgg_mtmc ,
		 cy.zl_jzgg_ggrq ,
		 cy.zl_jzgg_ly ,
		 cy.xx_cjfmc ,
		 cy.xx_sfzj ,
		 cy.zl_zjyqsjbg_zjjg ,
		 cy.zl_zjyqsjbg_bgh ,
		 cy.zl_zjyqsjbg_ly ,
		 cy.zl_tzxy_ly ,
		 cy.xx_qsjzcz ,
		 cy.xx_qsfy ,
		 cy.xx_qczw ,
		 cy.xx_pcccze ,
		 cy.xx_pcfyhgyzw ,
		 cy.xx_ptzw ,
		 cy.zl_qsbg_zjjgmc ,
		 cy.zl_qsbg_bgh ,
		 cy.zl_qsbg_ly ,
		 cy.zl_zxgg_mtmc ,
		 cy.zl_zxgg_ggrq ,
		 cy.zl_zxgg_ly ,
		 cy.zl_gszxzm_ly ,
		 cy.zl_pcgg_mtmc ,
		 cy.zl_pcgg_ggrq ,
		 cy.zl_pcgg_ly ,
		 cy.zl_tzxy_yw ,
		 cy.zl_jcwj_yw ,
		 cy.zl_jzgg_yw ,
		 cy.zl_pcgg_yw ,
		 cy.zl_jzrsjbg_yw ,
		 cy.zl_qyzc_yw ,
		 cy.zl_jcjg_yw ,
		 cy.zl_sjbg_yw ,
		 cy.zl_fhbpgba_yw ,
		 cy.zl_yyzz_yw ,
		 cy.zl_pgba_yw ,
		 cy.zl_gszxzm_yw ,
		 cy.zl_gytdba_yw ,
		 cy.zl_yzbg_yw ,
		 cy.zl_hbxys_yw ,
		 cy.zl_zhxy_yw ,
		 cy.zl_gqzr_yw ,
		 cy.zl_wchzxy_yw ,
		 cy.zl_bdpgba_yw ,
		 cy.zl_zjyqsjbg_yw ,
		 cy.zl_flxys_yw ,
		 cy.zl_zxgg_yw ,
		 cy.zl_qsbg_yw ,
		 cy.xx_bdqyxz ,
		 cy.xx_bxbfxz ,
		 cy.xx_jsyy ,
		 cy.zl_gszxzm_zxrq ,
		 cy.zl_jcwj_dwmc ,
		 cy.zl_jcwj_wjmc ,
		 cy.zl_jcwj_wjh ,
		 cy.zl_pgba_zjjgmc ,
		 cy.zl_pgba_pgbgh ,
		 cy.zl_pgba_hzdwmc ,
		 cy.zl_pgba_hzwjh ,
		 cy.zl_fhbpgba_zjjgmc,
		 cy.zl_fhbpgba_pgbgh ,
		 cy.zl_fhbpgba_hzdwmc ,
		 cy.zl_fhbpgba_hzwjh ,
		 cy.zl_bdpgba_zjjgmc ,
		 cy.zl_bdpgba_pgbgh,
		 cy.zl_bdpgba_hzdwmc ,
		 cy.zl_bdpgba_hzwjh ,
		 cy.zl_gytdba_pzdw ,
		 cy.zl_gytdba_pzwh ,
		 cy.zl_ywblsqwj ,
		 cy.zl_zhypgbab_yw ,
		 cy.zl_zhypgbab_zjjgmc ,
		 cy.zl_zhypgbab_pgbgh ,
		 cy.zl_zhypgbab_hzdwmc ,
		 cy.zl_zhypgbab_hzwjh ,
		 cy.zl_zhypgbab_ly ,
		 cy.zl_zhepgbab_yw ,
		 cy.zl_zhepgbab_zjjgmc ,
		 cy.zl_zhepgbab_pgbgh ,
		 cy.zl_zhepgbab_hzdwmc ,
		 cy.zl_zhepgbab_hzwjh ,
		 cy.zl_zhepgbab_ly ,
		 cy.zl_xbpgbab_yw ,
		 cy.zl_xbpgbab_zjjgmc ,
		 cy.zl_xbpgbab_pgbgh ,
		 cy.zl_xbpgbab_hzdwmc ,
		 cy.zl_xbpgbab_hzwjh ,
		 cy.zl_xbpgbab_ly ,
		 cy.zl_bxbpgbab_yw ,
		 cy.zl_bxbpgbab_zjjgmc ,
		 cy.zl_bxbpgbab_pgbgh ,
		 cy.zl_bxbpgbab_hzdwmc,
		 cy.zl_bxbpgbab_hzwjh ,
		 cy.zl_bxbpgbab_ly ,
		 cy.zl_tjpgbab_yw ,
		 cy.zl_tjpgbab_zjjgmc ,
		 cy.zl_tjpgbab_pgbgh ,
		 cy.zl_tjpgbab_hzdwmc ,
		 cy.zl_tjpgbab_hzwjh ,
		 cy.zl_tjpgbab_ly,
		 cy.zl_syzcczxy_yw ,
		 cy.zl_syzcczxy_ly ,
		 cy.zl_zgdbdhjy_yw ,
		 cy.zl_zgdbdhjy_yj ,
		 cy.zl_zgdbdhjy_ly ,
		 cy.zl_gqszfawj_yw ,
		 cy.zl_gqszfawj_pzdw ,
		 cy.zl_gqszfawj_pzwh ,
		 cy.zl_gqszfawj_ly ,
		 cy.zl_gdqkdjb_yw,
cy2.zl_gdqkdjb_ly  as   zlGdqkdjbLy,
		cy2.zl_qt  as   zlQt,
		cy2.zl_gszxzm_gsbmmc  as   zlGszxzmGsbmmc,
		cy2.xx_ywfhbcz  as   xxYwfhbcz,
		cy2.xx_ywfhbzfsgjk  as   xxYwfhbzfsgjk,
		cy2.xx_bdqypgjzcz  as   xxBdqypgjzcz,
		cy2.xx_bz  as   xxBz,
		cy2.xx_bdqysjjzcz  as   xxBdqysjjzcz,
		cy2.xx_zhyqymc  as   xxZhyqymc,
		cy2.xx_zhyssgzjgjg  as   xxZhyssgzjgjg,
		cy2.xx_zhyssgjczqy  as   xxZhyssgjczqy,
		cy2.xx_zhyyyzhdzcpgz  as   xxZhyyyzhdzcpgz,
		cy2.xx_zhybz  as   xxZhybz,
		cy2.xx_zheqymc  as   xxZheqymc,
		cy2.xx_zhessgzjgjg  as   xxZhessgzjgjg,
		cy2.xx_zhessgjczqy  as   xxZhessgjczqy,
		cy2.xx_zheyyzhdzcpgz  as   xxZheyyzhdzcpgz,
		cy2.xx_zhebz  as   xxZhebz,
		cy2.xx_azfyze  as   xxAzfyze,
		cy2.xx_gyqy  as   xxGyqy,
		cy2.xx_bxbfmc  as   xxBxbfmc,
		cy2.xx_bxbfpgjzcz  as   xxBxbfpgjzcz,
		cy2.xx_xbfpgjzcz  as   xxXbfpgjzcz,
		cy2.xx_bflqypgjzcz  as   xxBflqypgjzcz,
		cy2.xx_cxqypgjzcz  as   xxCxqypgjzcz,
		cy2.xx_cxqyzgbz  as   xxCxqyzgbz,
		cy2.xx_yytzdgqpgz  as   xxYytzdgqpgz,
		cy2.xx_yytzdgqzj  as   xxYytzdgqzj,
		cy2.xx_syjzcczsr  as   xxSyjzcczsr,
		cy2.xx_cjfssgzjgjg  as   xxCjfssgzjgjg,
		cy2.xx_bdqymc  as   xxBdqymc,
		cy2.xx_bdqyssgzjgjg  as   xxBdqyssgzjgjg,
		cy2.xx_qsfpbc  as   xxQsfpbc,
		cy2.xx_qsfpsqsk  as   xxQsfpsqsk,
		cy2.xx_qsccsfzyqczw  as   xxQsccsfzyqczw,
		cy2.xx_pcfpbc  as   xxPcfpbc,
		cy2.xx_pcfpsqsk  as   xxPcfpsqsk,
		cy2.xx_ywfhbjz  as   xxYwfhbjz,
		cy2.fd1_czzjhj  as   fd1Czzjhj,
		cy2.fd1_pgzhj  as   fd1Pgzhj,
		cy2.fd1_zfzjhj  as   fd1Zfzjhj,
		cy2.fd1_jzzjhj  as   fd1Jzzjhj,
		cy2.fd2_sgjghj  as   fd2Sgjghj,
		cy2.fd2_sjjzczhj  as   fd2Sjjzczhj,
		cy2.fd2_pgjzczhj  as   fd2Pgjzczhj,
		cy2.fd4_zgslhj  as   fd4Zgslhj,
		cy2.fd5_sfzjhj  as   fd5Sfzjhj,
		cy2.fd6_syccfpjghj  as   fd6Syccfpjghj,
		cy2.fd7_hzgqblhj  as   fd7Hzgqblhj,
		cy2.fd7_hzjzczhj  as   fd7Hzjzczhj,
		cy2.fd8_srgqsjjzczhj  as   fd8Srgqsjjzczhj,
		cy2.fd8_srgqpgjzczhj  as   fd8Srgqpgjzczhj,
		cy2.fd8_cjjhj  as   fd8Cjjhj,
		cy2.fd8_zrgqblhj  as   fd8Zrgqblhj,
		cy2.fd8_zrgqsjjzczhj  as   fd8Zrgqsjjzczhj,
		cy2.fd8_zrgqpgjzczhj  as   fd8Zrgqpgjzczhj,
		cy2.fd8_srgqblhj  as   fd8Srgqblhj,
		cy2.fd2_sggqblhj  as   fd2Sggqblhj,
		cy2.xx_fxgs  as   xxFxgs,
		cy2.xx_gkfxgs  as   xxGkfxgs,
		cy2.xx_azryzs  as   xxAzryzs,
		cy2.xx_bxbfjzczhxbfgqbl  as   xxBxbfjzczhxbfgqbl,
		cy2.xx_yytzgqzhbdqygqbl  as   xxYytzgqzhbdqygqbl,
		cy2.zl_fj  as   zlFj,
		cy2.xx_bdqypgjzcz_bzz  as   xxBdqypgjzczBzz,
		cy2.xx_bdqypgjzcz_bjz  as   xxBdqypgjzczBjz,
		cy2.xx_bdqypgjzcz_bgz  as   xxBdqypgjzczBgz,
		cy2.xx_bdqysjjzcz_bgz  as   xxBdqysjjzczBgz,
		cy2.xx_zgbz_xzgb  as   xxZgbzXzgb,
		cy2.xx_zgbz_js  as   xxZgbzJs,
		cy2.xx_zgbz_tzxz  as   xxZgbzTzxz,
		cy2.xx_zgbz_xshb  as   xxZgbzXshb,
		cy2.xx_zgbz_gqcz  as   xxZgbzGqcz,
		cy2.zl_yxhztzs_yw  as   zlYxhztzsYw,
		cy2.zl_yxhztzs_ly  as   zlYxhztzsLy,
		cy2.zl_yxhztzs_hzdw  as   zlYxhztzsHzdw,
		cy2.jc_30sspj  as   jc30sspj,
		cy2.jc_mgjzcz  as   jcMgjzcz,
		cy2.jc_jcgs  as   jcJcgs,
		cy2.jc_jcjj  as   jcJcjj,
		cy2.zl_qsbg  as   zlQsbg,
		cy2.zl_flxys  as   zlFlxys,
		cy2.zl_gszxzm  as   zlGszxzm,
		cy2.zl_zgdbdhjy  as   zlZgdbdhjy,
		cy2.zl_gqszfawj  as   zlGqszfawj,
		cy2.zl_zxgg  as   zlZxgg,
		cy2.zl_hbxys  as   zlHbxys,
		cy2.zl_jzrsjbg  as   zlJzrsjbg,
		cy2.zl_zhxy  as   zlZhxy,
		cy2.zl_fhbpgba  as   zlFhbpgba,
		cy2.zl_zjyqsjbg  as   zlZjyqsjbg,
		cy2.zl_pgba  as   zlPgba,
		cy2.zl_bdpgba  as   zlBdpgba,
		cy2.zl_wchzxy  as   zlWchzxy,
		cy2.zl_jzgg  as   zlJzgg,
		cy2.zl_yyzz  as   zlYyzz,
		cy2.zl_sjbg  as   zlSjbg,
		cy2.zl_yzbg  as   zlYzbg,
		cy2.zl_qyzc  as   zlQyzc,
		cy2.zl_gdqkdjb  as   zlGdqkdjb,
		cy2.zl_zhypgbab  as   zlZhypgbab,
		cy2.zl_syzcczxy  as   zlSyzcczxy,
		cy2.zl_tjpgbab  as   zlTjpgbab,
		cy2.zl_gqzr  as   zlGqzr,
		cy2.zl_zhepgbab  as   zlZhepgbab,
		cy2.zl_xbpgbab  as   zlXbpgbab,
		cy2.zl_tzxy  as   zlTzxy,
		cy2.zl_bxbpgbab  as   zlBxbpgbab,
		cy2.zl_gytdba  as   zlGytdba,
		cy2.zl_jcjg  as   zlJcjg,
		cy2.zl_jcwj  as   zlJcwj,
		cy2.zl_pcgg  as   zlPcgg,
		cy2.zl_yxhztzs  as   zlYxhztzs
	</sql>
</mapper>