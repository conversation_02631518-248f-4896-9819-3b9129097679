package com.zjhc.gzwcq.hhqy.api;

import com.zjhc.gzwcq.hhqy.entity.Hhqy;
import com.zjhc.gzwcq.hhqy.entity.HhqyParam;
import com.zjhc.gzwcq.hhqy.entity.HhqyVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.hhqy.client.HhqyFeignClient;
import com.zjhc.gzwcq.hhqy.service.api.IHhqyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;

import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/hhqyRemoteApi")
@Api(value="hhqy接口文档",tags="合伙企业信息扩展表")
public class HhqyRemoteApi implements HhqyFeignClient {
  
  	@Autowired
	private IHhqyService hhqyService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Hhqy
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<HhqyVo> queryByPage(@RequestBody HhqyParam hhqyParam) {
        BootstrapTableModel<HhqyVo> model = new BootstrapTableModel<HhqyVo>();
		model.setRows(hhqyService.queryHhqyByPage(hhqyParam));
		model.setTotal(hhqyService.queryTotalHhqys(hhqyParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Hhqy hhqy){
    	hhqyService.insert(hhqy);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	hhqyService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	hhqyService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Hhqy hhqy){
    	hhqyService.updateIgnoreNull(hhqy);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Hhqy hhqy){
    	hhqyService.update(hhqy);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Hhqy selectHhqyByPrimaryKey(@RequestBody Hhqy hhqy){
  		return hhqyService.selectHhqyByPrimaryKey(hhqy);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Hhqy> selectForList(@RequestBody Hhqy hhqy){
    	return hhqyService.selectForList(hhqy);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Hhqy hhqy){
    	return hhqyService.validateUniqueParam(hhqy);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Hhqy hhqy){
    	hhqyService.saveOne(hhqy);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Hhqy[] objs){
    	hhqyService.multipleSaveAndEdit(objs);
    };
	
}