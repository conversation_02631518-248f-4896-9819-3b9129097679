package com.zjhc.gzwcq.screen.api;

import com.boot.IAdmin.dict.model.DictionaryVo;
import com.zjhc.gzwcq.screen.client.ScreenFeignClient;
import com.zjhc.gzwcq.screen.service.api.IScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value="/screenRemoteApi")
@Api(value="screen接口文档",tags="首页大屏接口文档")
public class ScreenRemoteApi implements ScreenFeignClient {

    @Autowired
    private IScreenService screenService;

    @Override
    @ApiOperation(value="待办事项")
    public Map<String, Integer> todoList() {
        return screenService.todoList();
    }

    @Override
    @ApiOperation(value="企业户数统计(按组织形式)")
    public List<Map<String,Object>> companyNumByZzxs(@RequestParam(value = "unitId") String unitId) {
        return screenService.companyNumByZzxsNew(unitId);
    }

    /**
     * 企业户数统计(按组织形式)V2 - 直接从字典获取组织形式名称
     *
     * <AUTHOR>
     */
    @Override
    @ApiOperation(value="企业户数统计(按组织形式)V2")
    public List<Map<String,Object>> companyNumByZzxsV2(@RequestParam(value = "unitId") String unitId) {
        return screenService.companyNumByZzxsV2(unitId);
    }

    @Override
    @ApiOperation(value="企业户数统计(按企业级次)")
    public List<Map<String, Object>> companyNumByLevel(@RequestParam(value = "unitId") String unitId) {
        return screenService.companyNumByLevel(unitId);
    }

    @Override
    @ApiOperation(value="企业户数统计(按季度及企业类别)")
    public List<Map<String, Object>> companyNumBySeason(@RequestParam(value = "unitId",required = false) String unitId,
                                                        @RequestParam(value = "year",required = false) String year) {
        return screenService.companyNumBySeason(unitId,year);
    }

    @Override
    @ApiOperation(value="加载组织下拉树")
    public List<DictionaryVo> loadOrgs() {
        return screenService.loadOrgs();
    }

    @Override
    @ApiOperation(value="业务考核")
    public Map<String,Object> businessAssessment(String type,String year) {
        return screenService.businessAssessment(type,year);
    }
}
