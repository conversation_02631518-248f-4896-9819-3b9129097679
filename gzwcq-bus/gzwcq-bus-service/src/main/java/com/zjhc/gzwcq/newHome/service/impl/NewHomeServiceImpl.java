package com.zjhc.gzwcq.newHome.service.impl;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.model.user.SysUser;
import com.zjhc.gzwcq.newHome.dto.BusinessAssessmentDTO;
import com.zjhc.gzwcq.newHome.mapper.INewHomeMapper;
import com.zjhc.gzwcq.newHome.service.api.INewHomeService;
import com.zjhc.gzwcq.newHome.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04:14:43:45
 **/
@Service
@Slf4j
public class NewHomeServiceImpl implements INewHomeService {
    @Autowired
    private INewHomeMapper mapper;
    @Resource
    private OrganizationMapper organizationMapper;

    @Override
    public ResponseEnvelope selectUserStatus() {
        ResponseEnvelope vo = new ResponseEnvelope();
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        String user_id = user.getUser_id();
        user = mapper.selectUserStatus(user_id);
        vo.setResult(!Objects.isNull(user));
        return vo;
    }

    @Override
    public ResponseEnvelope selectBusinessAssessment(BusinessAssessmentDTO dto) {
        Map<String, Object> re = new HashMap<>();
        ResponseEnvelope vos = new ResponseEnvelope();
        BusinessAssessmentVO vo = new BusinessAssessmentVO();
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        String user_id = user.getUser_id();
        String organization_id = user.getOrganization_id();
        String dataStatus = dto.getDataStatus();
        String status = "";
        boolean result = (boolean) this.selectUserStatus().getResult();
        status = result ? "0" : "1";
        DataBusinessAssessmentVO dataBusinessAssessmentVO = null;
        try {
            dataBusinessAssessmentVO = mapper.selectBusinessAssessment(organization_id, dto.getDateTime(), dataStatus, status);
        }catch (Exception e){
            log.error(String.format("业务考核统计异常%s,%s,%s,%s",organization_id,dto.getDateTime(),dataStatus,status),e);
            throw e;
        }
        //总的提交次数
        String countNum = dataBusinessAssessmentVO.getCountNum();
        BigDecimal count = new BigDecimal(countNum);
        //及时的提交次数
        String count1 = dataBusinessAssessmentVO.getCount();
        //待审批数量
        String approvalPending = dataBusinessAssessmentVO.getApprovalPending();
        //通过次数
        String passNum = dataBusinessAssessmentVO.getPassNum();
        //拒绝次数
        String backspaceNum = dataBusinessAssessmentVO.getBackspaceNum();
        //及时处理的次数
        String inTimeNum = dataBusinessAssessmentVO.getInTimeNum();
        //未及时处理的次数
        String noInTimeNum = dataBusinessAssessmentVO.getNoInTimeNum();

        //及时概率
        BigDecimal multiply = new BigDecimal("100");
        BigDecimal timelinessRatio = new BigDecimal(inTimeNum);
        timelinessRatio = StringUtils.equals(inTimeNum, "0") ? BigDecimal.ZERO : timelinessRatio.multiply(multiply).divide(new BigDecimal(count1), 2, BigDecimal.ROUND_HALF_UP);
//        vo.setTimelinessRatio(timelinessRatio.toString());
        Map<String, Object> tlMap = new HashMap<>();
        tlMap.put("allNum", count1);
        tlMap.put("tlNum", new BigDecimal(inTimeNum));
        tlMap.put("rate", timelinessRatio);
        re.put("up", tlMap);
        //通过 率
        BigDecimal throughRate = new BigDecimal(passNum);
        throughRate = StringUtils.equals(passNum, "0") ? BigDecimal.ZERO : throughRate.multiply(multiply).divide(count, 2, BigDecimal.ROUND_HALF_UP);
//        vo.setThroughRate(throughRate.toString());
        Map<String, Object> voMap = new HashMap<>();
        voMap.put("submitNum", count);
        voMap.put("passNum", passNum);
        voMap.put("rate", throughRate);
        re.put("pass", voMap);
        //退回 率
        BigDecimal rejectionRate = new BigDecimal(backspaceNum);
        rejectionRate = StringUtils.equals(backspaceNum, "0") ? BigDecimal.ZERO : rejectionRate.multiply(multiply).divide(count, 2, BigDecimal.ROUND_HALF_UP);
//        vo.setRejectionRate(rejectionRate.toString());
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("submitNum", count);
        returnMap.put("returnNum", new BigDecimal(backspaceNum));
        returnMap.put("rate", rejectionRate);
        re.put("down", returnMap);
//        vos.setResult(vo);
        vos.setResult(re);
        return vos;
    }

    /**
     * 查询业务考核信息 
     * modify by blp 增加企业户数查询
     * @param dto 查询参数
     * @return 业务考核信息
     */
    @Override
    public ResponseEnvelope selectBusinessAssessmentInfo(BusinessAssessmentDTO dto) {
        Set<String> organizationNum = new HashSet<>();
        ResponseEnvelope vos = new ResponseEnvelope();
        BusinessAssessmentInfoVO vo = new BusinessAssessmentInfoVO();
        Integer count = 0;
        //待处理
        Integer approvalPending = 0;
        //通过
        Integer passNum = 0;
        //退回
        Integer backspaceNum = 0;
        //及时
        Integer inTimeNum = 0;
        //不及时
        Integer noInTimeNum = 0;
        boolean result = (boolean) this.selectUserStatus().getResult();
        if (!result) {
            vos.setSuccess(false);
            vos.setMessage("此账号没有权限访问此数据功能");
            return vos;
        }
        //董的需求目前是写死的 组织id
        String ordId = "39DC82B5C0000021A568D4D612672F5A";
        CompletableFuture<Integer> companyCountFuture = CompletableFuture.supplyAsync(()-> dto.getYearMonth() != null ? mapper.selectCompanyCount(ordId, dto.getYearMonth(), organizationMapper.selectAuditHosting(ordId)) : 0);
        BusinessAssessmentInfoTotalDto businessAssessmentInfoTotalDto = mapper.selectBusinessAssessmentInfo(ordId, dto.getDateTime(), dto.getYearMonth());
        /*for (DataBusinessAssessmentInfoVO item : dataBusinessAssessmentInfoVOS) {
            String ordId1 = item.getOrdId();
            if (!StringUtils.isBlank(ordId1)) {
                organizationNum.add(ordId1);
            }
            Integer approvalPending1 = item.getApprovalPending();
            approvalPending += approvalPending1;
            Integer passNum1 = item.getPassNum();
            passNum += passNum1;
            Integer backspaceNum1 = item.getBackspaceNum();
            backspaceNum += backspaceNum1;
            //提交次数
            count = count + (approvalPending1 + passNum1 + backspaceNum1);
        }*/

        //企业数量
        vo.setEnterpriseCount(String.valueOf(businessAssessmentInfoTotalDto.getOrgNum()));
        // 企业户数 根据年月查询 只用于新的大屏
        try {
            vo.setCompanyCount(companyCountFuture.get() != null ? ""+companyCountFuture.get() : "0");
        } catch (Exception e) {
            log.error("获取企业户数失败", e);
        }
        //提交事项数
        String countStr = String.valueOf(businessAssessmentInfoTotalDto.getCount());
        vo.setSubmitNum(countStr);
        //审核事项数
        String auditNum = String.valueOf(businessAssessmentInfoTotalDto.getAuditNum());
        vo.setAuditNum(auditNum);
        //通过数
        String passNumStr = String.valueOf(businessAssessmentInfoTotalDto.getPassNum());
        vo.setPassNum(passNumStr);
        //通过率
        BigDecimal multiplicand = new BigDecimal("100");
        //分母改成审核事项
        BigDecimal divisor = new BigDecimal(auditNum);
        vo.setThroughRate(StringUtils.equals(countStr, "0") || StringUtils.equals(auditNum,"0") ? BigDecimal.ZERO.toString() : (new BigDecimal(passNumStr).multiply(multiplicand)).divide(divisor, 2, BigDecimal.ROUND_HALF_UP).toString());
        //退回数
        String rejectionNumStr = String.valueOf(businessAssessmentInfoTotalDto.getRejectionNum());
        vo.setRejectionNum(rejectionNumStr);
        //退回率
        vo.setRejectionRate(StringUtils.equals(rejectionNumStr, "0") || StringUtils.equals(auditNum,"0") ? BigDecimal.ZERO.toString() : (new BigDecimal(rejectionNumStr).multiply(multiplicand)).divide(divisor, 2, BigDecimal.ROUND_HALF_UP).toString());
        vos.setResult(vo);
        return vos;
    }

    /**
     * 查询一级企业办理事项情况
     */
    @Override
    public BootstrapTableModel<BusinessTransactionVO> selectHandleMattersInfo(BusinessAssessmentDTO dto) {
        BootstrapTableModel<BusinessTransactionVO> vos = new BootstrapTableModel<BusinessTransactionVO>();
        boolean result = (boolean) this.selectUserStatus().getResult();
        if (!result) {
            vos.setSuccess(false);
            vos.setMessage("此账号没有权限访问此数据功能");
            return vos;
        }
        int pageNumber = dto.getPageNumber();
        int limit = dto.getLimit();
        String ordId = "39DC82B5C0000021A568D4D612672F5A";
        List<DataBusinessTransactionVO> list = mapper.selectHandleMattersInfoV2(dto.getDateTime(), ordId, dto.getYearMonth());
        List<BusinessTransactionVO> vo = list.stream().collect(ArrayList<BusinessTransactionVO>::new, (results, obj) -> {
            Integer rank = results.size() + 1; // 当前对象的排名
            String ordId1 = obj.getOrdId();
            String name = obj.getName();

            //通过数
            Integer passNum = obj.getPassNum();
            //等待
            //Integer pendingNum = obj.getPendingNum();
            //回退
            Integer rollbackNum = obj.getRollbackNum();
            //审核事项数
            Integer auditNum =  passNum + rollbackNum;
            //提交事项数
            Integer count = obj.getCount();
            //Integer sum = (passNum + rollbackNum);
            BigDecimal multiplicand = new BigDecimal("100");
            //分母改成审核事项数
            BigDecimal divisor = new BigDecimal(auditNum.toString());
            // 计算通过率
            String format = passNum.intValue() == 0 ? BigDecimal.ZERO.toString() : (new BigDecimal(passNum.toString()).multiply(multiplicand)).divide(divisor, 2, BigDecimal.ROUND_HALF_UP).toString();

            String format2 = rollbackNum.intValue() == 0 ? BigDecimal.ZERO.toString() : (new BigDecimal(rollbackNum.toString()).multiply(multiplicand)).divide(divisor, 2, BigDecimal.ROUND_HALF_UP).toString();

            results.add(new BusinessTransactionVO(ordId1, name, count.toString(), String.valueOf((passNum + rollbackNum))
                    , passNum.toString(), format + "%", rollbackNum.toString(), format2 + "%", rank.toString()
            ));
        }, ArrayList::addAll);

        int size = vo.size();
        //初始的索引
        int index = limit * (pageNumber - 1);
        //获取分页后 startIndex
        int startIndex = index > size ? size : index;
        int number = index + limit;
        int endIndex = number > size ? size : number;
        vo = vo.subList(startIndex, endIndex);
        vos.setRows(vo);
        vos.setTotal(size);
        return vos;
    }
}
