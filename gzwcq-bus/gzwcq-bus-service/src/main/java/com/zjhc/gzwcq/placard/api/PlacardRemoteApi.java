package com.zjhc.gzwcq.placard.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.placard.client.PlacardFeignClient;
import com.zjhc.gzwcq.placard.service.api.IPlacardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.placard.entity.Placard;
import com.zjhc.gzwcq.placard.entity.PlacardParam;
import com.zjhc.gzwcq.placard.entity.PlacardVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/placardRemoteApi")
@Api(value="placard接口文档",tags="cq_placard")
public class PlacardRemoteApi implements PlacardFeignClient {
  
  	@Autowired
	private IPlacardService placardService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Placard
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<PlacardVo> queryByPage(@RequestBody PlacardParam placardParam) {
        BootstrapTableModel<PlacardVo> model = new BootstrapTableModel<PlacardVo>();
		model.setRows(placardService.queryPlacardByPage(placardParam));
		model.setTotal(placardService.queryTotalPlacards(placardParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Placard placard){
    	placardService.insert(placard);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	placardService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	placardService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Placard placard){
    	placardService.updateIgnoreNull(placard);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Placard placard){
    	placardService.update(placard);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public PlacardVo selectPlacardByPrimaryKey(@RequestBody Placard placard){
  		return placardService.selectPlacardByPrimaryKey(placard);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Placard> selectForList(@RequestBody Placard placard){
    	return placardService.selectForList(placard);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Placard placard){
    	return placardService.validateUniqueParam(placard);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Placard placard){
    	placardService.saveOne(placard);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Placard[] objs){
    	placardService.multipleSaveAndEdit(objs);
    }

	/**
	 * 按对象中的主键进行所有非空属性的修改
	 *
	 * @param placard
	 */
	@ApiOperation(value="更新查阅次数")
	public void updateLookTimeAndRead(PlacardVo placard) {
		placardService.updateLookTimeAndRead(placard);
	}

	/**
	 * 消息数量
	 */
	@ApiOperation(value="消息数量")
	public HashMap<String, Long> messageTotal(PlacardVo placard) {
		return placardService.messageTotal(placard);
	}


}