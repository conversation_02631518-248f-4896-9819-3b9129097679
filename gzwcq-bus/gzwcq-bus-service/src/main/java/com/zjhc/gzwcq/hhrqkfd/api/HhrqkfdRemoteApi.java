package com.zjhc.gzwcq.hhrqkfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.hhrqkfd.client.HhrqkfdFeignClient;
import com.zjhc.gzwcq.hhrqkfd.service.api.IHhrqkfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdParam;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/hhrqkfdRemoteApi")
@Api(value="hhrqkfd接口文档",tags="合伙人情况浮动")
public class HhrqkfdRemoteApi implements HhrqkfdFeignClient {
  
  	@Autowired
	private IHhrqkfdService hhrqkfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Hhrqkfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<HhrqkfdVo> queryByPage(@RequestBody HhrqkfdParam hhrqkfdParam) {
        BootstrapTableModel<HhrqkfdVo> model = new BootstrapTableModel<HhrqkfdVo>();
		model.setRows(hhrqkfdService.queryHhrqkfdByPage(hhrqkfdParam));
		model.setTotal(hhrqkfdService.queryTotalHhrqkfds(hhrqkfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Hhrqkfd hhrqkfd){
    	hhrqkfdService.insert(hhrqkfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	hhrqkfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	hhrqkfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Hhrqkfd hhrqkfd){
    	hhrqkfdService.updateIgnoreNull(hhrqkfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Hhrqkfd hhrqkfd){
    	hhrqkfdService.update(hhrqkfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Hhrqkfd selectHhrqkfdByPrimaryKey(@RequestBody Hhrqkfd hhrqkfd){
  		return hhrqkfdService.selectHhrqkfdByPrimaryKey(hhrqkfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Hhrqkfd> selectForList(@RequestBody Hhrqkfd hhrqkfd){
    	return hhrqkfdService.selectForList(hhrqkfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Hhrqkfd hhrqkfd){
    	return hhrqkfdService.validateUniqueParam(hhrqkfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Hhrqkfd hhrqkfd){
    	hhrqkfdService.saveOne(hhrqkfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Hhrqkfd[] objs){
    	hhrqkfdService.multipleSaveAndEdit(objs);
    };
	
}