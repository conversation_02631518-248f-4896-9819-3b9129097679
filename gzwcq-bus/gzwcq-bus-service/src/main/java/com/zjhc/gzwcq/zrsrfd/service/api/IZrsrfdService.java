package com.zjhc.gzwcq.zrsrfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdVo;
import com.zjhc.gzwcq.zrsrfd.entity.ZrsrfdParam;

public interface IZrsrfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Zrsrfd zrsrfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Zrsrfd zrsrfd);
	
	/**
	* 更新
	*/
	void update(Zrsrfd zrsrfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<ZrsrfdVo> queryZrsrfdByPage(ZrsrfdParam zrsrfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalZrsrfds(ZrsrfdParam zrsrfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	ZrsrfdVo selectZrsrfdByPrimaryKey(Zrsrfd zrsrfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Zrsrfd> selectForList(Zrsrfd zrsrfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Zrsrfd zrsrfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Zrsrfd zrsrfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Zrsrfd[] objs);

	/**
	 * 根据基本信息表id查数据
	 */
    List<ZrsrfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);
}