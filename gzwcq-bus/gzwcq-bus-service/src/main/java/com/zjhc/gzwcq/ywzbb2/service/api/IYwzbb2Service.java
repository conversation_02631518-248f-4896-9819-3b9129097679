package com.zjhc.gzwcq.ywzbb2.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Param;

public interface IYwzbb2Service {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Ywzbb2 ywzbb2);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Ywzbb2 ywzbb2);
	
	/**
	* 更新
	*/
	void update(Ywzbb2 ywzbb2);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<Ywzbb2Vo> queryYwzbb2ByPage(Ywzbb2Param ywzbb2Param);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalYwzbb2s(Ywzbb2Param ywzbb2Param);
  
	
	/**
	 *通过ID查询数据
	 */
	Ywzbb2Vo selectYwzbb2ByPrimaryKey(Ywzbb2 ywzbb2);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Ywzbb2> selectForList(Ywzbb2 ywzbb2);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Ywzbb2 ywzbb2);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Ywzbb2 ywzbb2);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Ywzbb2[] objs);

    void deleteByJbxxId(String jbxxId);

    Ywzbb2Vo selectByJbxxId(String jbxxId);
}