package com.zjhc.gzwcq.ywzbb.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.ywzbb.mapper.IYwzbbMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbParam;
import com.zjhc.gzwcq.ywzbb.service.api.IYwzbbService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class YwzbbServiceImpl implements IYwzbbService {
	
	@Autowired
	private IYwzbbMapper ywzbbMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		ywzbbMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Ywzbb ywzbb){
		ywzbb.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		ywzbb.setCreateTime(new Date());//创建时间
		ywzbb.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ywzbb.setLastUpdateTime(new Date());//更新时间
		ywzbbMapper.insert(ywzbb);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		ywzbbMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		ywzbbMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Ywzbb ywzbb){
		ywzbb.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ywzbb.setLastUpdateTime(new Date());//更新时间
		ywzbbMapper.updateIgnoreNull(ywzbb);
	}

	@Override
	public YwzbbVo selectByJbxxId(String jbxxId) {
		return ywzbbMapper.selectByJbxxId(jbxxId);
	}

	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Ywzbb ywzbb){
		ywzbb.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		ywzbb.setLastUpdateTime(new Date());//更新时间
		ywzbbMapper.update(ywzbb);
	}
	
	public List<YwzbbVo> queryYwzbbByPage(YwzbbParam ywzbbParam) {
      	//分页
      	PageHelper.startPage(ywzbbParam.getPageNumber(),ywzbbParam.getLimit(),false);
		return ywzbbMapper.queryYwzbbForList(ywzbbParam);
	}
	

	public YwzbbVo selectYwzbbByPrimaryKey(Ywzbb Ywzbb) {
		return ywzbbMapper.selectYwzbbByPrimaryKey(Ywzbb);
	}
	
	public long queryTotalYwzbbs(YwzbbParam ywzbbParam) {
		return ywzbbMapper.queryTotalYwzbbs(ywzbbParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Ywzbb> selectForList(Ywzbb ywzbb){
		return ywzbbMapper.selectForList(ywzbb);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Ywzbb ywzbb) {
		return ywzbbMapper.selectForUnique(ywzbb).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Ywzbb ywzbb) {
		if(StringUtils.isBlank(ywzbb.getId())) {
			this.insert(ywzbb);
		}else {
			this.updateIgnoreNull(ywzbb);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Ywzbb[] objs) {
		for(Ywzbb ywzbb : objs) {
			this.saveOne(ywzbb);
		}
	}
}
