<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.hrhcfd.mapper.IHrhcfdMapper">

	<resultMap type="com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="fd7_hcfmc" property="fd7Hcfmc"/>
		<result column="fd7_wchzlb" property="fd7Wchzlb"/>
		<result column="fd7_hzjzr" property="fd7Hzjzr"/>
		<result column="fd7_hzjzcz" property="fd7Hzjzcz"/>
		<result column="fd7_hcfssgjczqy" property="fd7Hcfssgjczqy"/>
		<result column="fd7_hzbdqymc" property="fd7Hzbdqymc"/>
		<result column="fd7_hrfmc" property="fd7Hrfmc"/>
		<result column="fd7_hrfssgjczqy" property="fd7Hrfssgjczqy"/>
		<result column="fd7_bz" property="fd7Bz"/>
		<result column="fd7_hcfssgzjgjg" property="fd7Hcfssgzjgjg"/>
		<result column="fd7_hrfssgzjgjg" property="fd7Hrfssgzjgjg"/>
		<result column="fd7_hzgqbl" property="fd7Hzgqbl"/>
		<result column="fd7_hrfmc_zx" property="fd7HrfmcZx"/>
		<result column="fd7_hrfssgzjgjg_zx" property="fd7HrfssgzjgjgZx"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.hrhcfd.entity.HrhcfdVo" extends="baseResultMap">
		<result column="fd7WchzlbStr" property="fd7WchzlbStr"/>
		<result column="fd7HcfssgzjgjgStr" property="fd7HcfssgzjgjgStr"/>
		<result column="fd7HrfssgzjgjgStr" property="fd7HrfssgzjgjgStr"/>
		<result column="fd7HrfssgjczqyStr" property="fd7HrfssgjczqyStr"/>
		<result column="fd7HcfssgjczqyStr" property="fd7HcfssgjczqyStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		fd7_hcfmc, 
		fd7_wchzlb, 
		fd7_hzjzr, 
		fd7_hzjzcz, 
		fd7_hcfssgjczqy, 
		fd7_hzbdqymc, 
		fd7_hrfmc, 
		fd7_hrfssgjczqy, 
		fd7_bz, 
		fd7_hcfssgzjgjg, 
		fd7_hrfssgzjgjg, 
		fd7_hzgqbl, 
		fd7_hrfmc_zx, 
		fd7_hrfssgzjgjg_zx, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.fd7_hcfmc, 
		t.fd7_wchzlb, 
		t.fd7_hzjzr, 
		t.fd7_hzjzcz, 
		t.fd7_hcfssgjczqy, 
		t.fd7_hzbdqymc, 
		t.fd7_hrfmc, 
		t.fd7_hrfssgjczqy, 
		t.fd7_bz, 
		t.fd7_hcfssgzjgjg, 
		t.fd7_hrfssgzjgjg, 
		t.fd7_hzgqbl, 
		t.fd7_hrfmc_zx, 
		t.fd7_hrfssgzjgjg_zx, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{fd7Hcfmc}, 
		#{fd7Wchzlb}, 
		#{fd7Hzjzr}, 
		#{fd7Hzjzcz}, 
		#{fd7Hcfssgjczqy}, 
		#{fd7Hzbdqymc}, 
		#{fd7Hrfmc}, 
		#{fd7Hrfssgjczqy}, 
		#{fd7Bz}, 
		#{fd7Hcfssgzjgjg}, 
		#{fd7Hrfssgzjgjg}, 
		#{fd7Hzgqbl}, 
		#{fd7HrfmcZx}, 
		#{fd7HrfssgzjgjgZx}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="fd7Hcfmc != null and fd7Hcfmc != ''">
			and t.fd7_hcfmc = #{fd7Hcfmc}
		</if>
		<if test="fd7Wchzlb != null and fd7Wchzlb != ''">
			and t.fd7_wchzlb = #{fd7Wchzlb}
		</if>
		<if test="fd7Hzjzr != null">
			and t.fd7_hzjzr = #{fd7Hzjzr}
		</if>
		<if test="fd7Hzjzcz != null">
			and t.fd7_hzjzcz = #{fd7Hzjzcz}
		</if>
		<if test="fd7Hcfssgjczqy != null and fd7Hcfssgjczqy != ''">
			and t.fd7_hcfssgjczqy = #{fd7Hcfssgjczqy}
		</if>
		<if test="fd7Hzbdqymc != null and fd7Hzbdqymc != ''">
			and t.fd7_hzbdqymc = #{fd7Hzbdqymc}
		</if>
		<if test="fd7Hrfmc != null and fd7Hrfmc != ''">
			and t.fd7_hrfmc = #{fd7Hrfmc}
		</if>
		<if test="fd7Hrfssgjczqy != null and fd7Hrfssgjczqy != ''">
			and t.fd7_hrfssgjczqy = #{fd7Hrfssgjczqy}
		</if>
		<if test="fd7Bz != null and fd7Bz != ''">
			and t.fd7_bz = #{fd7Bz}
		</if>
		<if test="fd7Hcfssgzjgjg != null and fd7Hcfssgzjgjg != ''">
			and t.fd7_hcfssgzjgjg = #{fd7Hcfssgzjgjg}
		</if>
		<if test="fd7Hrfssgzjgjg != null and fd7Hrfssgzjgjg != ''">
			and t.fd7_hrfssgzjgjg = #{fd7Hrfssgzjgjg}
		</if>
		<if test="fd7Hzgqbl != null">
			and t.fd7_hzgqbl = #{fd7Hzgqbl}
		</if>
		<if test="fd7HrfmcZx != null and fd7HrfmcZx != ''">
			and t.fd7_hrfmc_zx = #{fd7HrfmcZx}
		</if>
		<if test="fd7HrfssgzjgjgZx != null and fd7HrfssgzjgjgZx != ''">
			and t.fd7_hrfssgzjgjg_zx = #{fd7HrfssgzjgjgZx}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_hrhcfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_hrhcfd set isDeleted = 'Y' where
		id in
		<foreach collection="hrhcfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_hrhcfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_hrhcfd  where
		id in
		<foreach collection="hrhcfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_hrhcfd  where id = #{id}
	</delete>
	
	<select id="selectHrhcfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_hrhcfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_hrhcfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="fd7Hcfmc != null">
				fd7_hcfmc=#{fd7Hcfmc},
			</if>
			<if test="fd7Wchzlb != null">
				fd7_wchzlb=#{fd7Wchzlb},
			</if>
			<if test="fd7Hzjzr != null">
				fd7_hzjzr=#{fd7Hzjzr},
			</if>
			<if test="fd7Hzjzcz != null">
				fd7_hzjzcz=#{fd7Hzjzcz},
			</if>
			<if test="fd7Hcfssgjczqy != null">
				fd7_hcfssgjczqy=#{fd7Hcfssgjczqy},
			</if>
			<if test="fd7Hzbdqymc != null">
				fd7_hzbdqymc=#{fd7Hzbdqymc},
			</if>
			<if test="fd7Hrfmc != null">
				fd7_hrfmc=#{fd7Hrfmc},
			</if>
			<if test="fd7Hrfssgjczqy != null">
				fd7_hrfssgjczqy=#{fd7Hrfssgjczqy},
			</if>
			<if test="fd7Bz != null">
				fd7_bz=#{fd7Bz},
			</if>
			<if test="fd7Hcfssgzjgjg != null">
				fd7_hcfssgzjgjg=#{fd7Hcfssgzjgjg},
			</if>
			<if test="fd7Hrfssgzjgjg != null">
				fd7_hrfssgzjgjg=#{fd7Hrfssgzjgjg},
			</if>
			<if test="fd7Hzgqbl != null">
				fd7_hzgqbl=#{fd7Hzgqbl},
			</if>
			<if test="fd7HrfmcZx != null">
				fd7_hrfmc_zx=#{fd7HrfmcZx},
			</if>
			<if test="fd7HrfssgzjgjgZx != null">
				fd7_hrfssgzjgjg_zx=#{fd7HrfssgzjgjgZx},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_hrhcfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			fd7_hcfmc=#{fd7Hcfmc},
			fd7_wchzlb=#{fd7Wchzlb},
			fd7_hzjzr=#{fd7Hzjzr},
			fd7_hzjzcz=#{fd7Hzjzcz},
			fd7_hcfssgjczqy=#{fd7Hcfssgjczqy},
			fd7_hzbdqymc=#{fd7Hzbdqymc},
			fd7_hrfmc=#{fd7Hrfmc},
			fd7_hrfssgjczqy=#{fd7Hrfssgjczqy},
			fd7_bz=#{fd7Bz},
			fd7_hcfssgzjgjg=#{fd7Hcfssgzjgjg},
			fd7_hrfssgzjgjg=#{fd7Hrfssgzjgjg},
			fd7_hzgqbl=#{fd7Hzgqbl},
			fd7_hrfmc_zx=#{fd7HrfmcZx},
			fd7_hrfssgzjgjg_zx=#{fd7HrfssgzjgjgZx},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_hrhcfd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalHrhcfds" parameterType="com.zjhc.gzwcq.hrhcfd.entity.HrhcfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_hrhcfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryHrhcfdForList" parameterType="com.zjhc.gzwcq.hrhcfd.entity.HrhcfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_hrhcfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_hrhcfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="fd7Hcfmc != null and fd7Hcfmc != ''">
				and t.fd7_hcfmc = #{fd7Hcfmc}
			</if>
			<if test="fd7Wchzlb != null and fd7Wchzlb != ''">
				and t.fd7_wchzlb = #{fd7Wchzlb}
			</if>
			<if test="fd7Hzjzr != null">
				and t.fd7_hzjzr = #{fd7Hzjzr}
			</if>
			<if test="fd7Hzjzcz != null and fd7Hzjzcz != ''">
				and t.fd7_hzjzcz = #{fd7Hzjzcz}
			</if>
			<if test="fd7Hcfssgjczqy != null and fd7Hcfssgjczqy != ''">
				and t.fd7_hcfssgjczqy = #{fd7Hcfssgjczqy}
			</if>
			<if test="fd7Hzbdqymc != null and fd7Hzbdqymc != ''">
				and t.fd7_hzbdqymc = #{fd7Hzbdqymc}
			</if>
			<if test="fd7Hrfmc != null and fd7Hrfmc != ''">
				and t.fd7_hrfmc = #{fd7Hrfmc}
			</if>
			<if test="fd7Hrfssgjczqy != null and fd7Hrfssgjczqy != ''">
				and t.fd7_hrfssgjczqy = #{fd7Hrfssgjczqy}
			</if>
			<if test="fd7Bz != null and fd7Bz != ''">
				and t.fd7_bz = #{fd7Bz}
			</if>
			<if test="fd7Hcfssgzjgjg != null and fd7Hcfssgzjgjg != ''">
				and t.fd7_hcfssgzjgjg = #{fd7Hcfssgzjgjg}
			</if>
			<if test="fd7Hrfssgzjgjg != null and fd7Hrfssgzjgjg != ''">
				and t.fd7_hrfssgzjgjg = #{fd7Hrfssgzjgjg}
			</if>
			<if test="fd7Hzgqbl != null and fd7Hzgqbl != ''">
				and t.fd7_hzgqbl = #{fd7Hzgqbl}
			</if>
			<if test="fd7HrfmcZx != null and fd7HrfmcZx != ''">
				and t.fd7_hrfmc_zx = #{fd7HrfmcZx}
			</if>
			<if test="fd7HrfssgzjgjgZx != null and fd7HrfssgzjgjgZx != ''">
				and t.fd7_hrfssgzjgjg_zx = #{fd7HrfssgzjgjgZx}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxId" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> ,sd1.text fd7WchzlbStr,sd2.text fd7HrfssgzjgjgStr,sd3.text fd7HcfssgzjgjgStr,
		sd4.text fd7HrfssgjczqyStr,sd5.text fd7HcfssgjczqyStr
		from cq_hrhcfd t
		left join sys_dictionary sd1 on sd1.val=t.FD7_WCHZLB and sd1.type_id=(select id from sys_dictionary where type_code='WCHZLB')
		left join sys_dictionary sd2 on sd2.val=t.FD7_HRFSSGZJGJG and sd2.type_id=(select id from sys_dictionary where type_code='GZJGJGFD')
		left join sys_dictionary sd3 on sd3.val=t.FD7_HCFSSGZJGJG and sd3.type_id=(select id from sys_dictionary where type_code='GZJGJGFD')
		left join sys_dictionary sd4 on sd4.val=t.FD7_HRFSSGJCZQY and sd4.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		left join sys_dictionary sd5 on sd5.val=t.FD7_HCFSSGJCZQY and sd5.type_id=(select id from sys_dictionary where type_code='GJCZQY')
		where t.JBXX_ID = #{id}
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_hrhcfd where jbxx_id = #{jbxxId}
	</delete>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			<include refid="selectAllByJbxxIdVo"/>
		FROM `cq_hrhcfd`
		WHERE
			JBXX_ID = #{jbxxid}
	</select>
	<sql id="selectAllByJbxxIdVo">
		fd7_hcfmc,
			fd7_wchzlb,
			fd7_hzjzr,
			fd7_hzjzcz,
			fd7_hcfssgjczqy,
			fd7_hzbdqymc,
			fd7_hrfmc,
			fd7_hrfssgjczqy,
			fd7_bz,
			fd7_hcfssgzjgjg,
			fd7_hrfssgzjgjg,
			fd7_hzgqbl,
			fd7_hrfmc_zx,
			fd7_hrfssgzjgjg_zx
	</sql>
</mapper>