<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.fhbzcfd.mapper.IFhbzcfdMapper">

	<resultMap type="com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="fd1_czf" property="fd1Czf"/>
		<result column="fd1_czzj" property="fd1Czzj"/>
		<result column="fd1_pgz" property="fd1Pgz"/>
		<result column="fd1_zff" property="fd1Zff"/>
		<result column="fd1_zfzj" property="fd1Zfzj"/>
		<result column="fd1_jzf" property="fd1Jzf"/>
		<result column="fd1_jzzj" property="fd1Jzzj"/>
		<result column="fd1_bz" property="fd1Bz"/>
		<result column="fd1_hfbzclb" property="fd1Hfbzclb"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		fd1_czf, 
		fd1_czzj, 
		fd1_pgz, 
		fd1_zff, 
		fd1_zfzj, 
		fd1_jzf, 
		fd1_jzzj, 
		fd1_bz, 
		fd1_hfbzclb, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.fd1_czf, 
		t.fd1_czzj, 
		t.fd1_pgz, 
		t.fd1_zff, 
		t.fd1_zfzj, 
		t.fd1_jzf, 
		t.fd1_jzzj, 
		t.fd1_bz, 
		t.fd1_hfbzclb, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{fd1Czf}, 
		#{fd1Czzj}, 
		#{fd1Pgz}, 
		#{fd1Zff}, 
		#{fd1Zfzj}, 
		#{fd1Jzf}, 
		#{fd1Jzzj}, 
		#{fd1Bz}, 
		#{fd1Hfbzclb}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="fd1Czf != null and fd1Czf != ''">
			and t.fd1_czf = #{fd1Czf}
		</if>
		<if test="fd1Czzj != null">
			and t.fd1_czzj = #{fd1Czzj}
		</if>
		<if test="fd1Pgz != null">
			and t.fd1_pgz = #{fd1Pgz}
		</if>
		<if test="fd1Zff != null and fd1Zff != ''">
			and t.fd1_zff = #{fd1Zff}
		</if>
		<if test="fd1Zfzj != null">
			and t.fd1_zfzj = #{fd1Zfzj}
		</if>
		<if test="fd1Jzf != null and fd1Jzf != ''">
			and t.fd1_jzf = #{fd1Jzf}
		</if>
		<if test="fd1Jzzj != null">
			and t.fd1_jzzj = #{fd1Jzzj}
		</if>
		<if test="fd1Bz != null and fd1Bz != ''">
			and t.fd1_bz = #{fd1Bz}
		</if>
		<if test="fd1Hfbzclb != null and fd1Hfbzclb != ''">
			and t.fd1_hfbzclb = #{fd1Hfbzclb}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_fhbzcfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_fhbzcfd set isDeleted = 'Y' where
		id in
		<foreach collection="fhbzcfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_fhbzcfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_fhbzcfd  where
		id in
		<foreach collection="fhbzcfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_fhbzcfd  where id = #{id}
	</delete>
	
	<select id="selectFhbzcfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_fhbzcfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_fhbzcfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="fd1Czf != null">
				fd1_czf=#{fd1Czf},
			</if>
			<if test="fd1Czzj != null">
				fd1_czzj=#{fd1Czzj},
			</if>
			<if test="fd1Pgz != null">
				fd1_pgz=#{fd1Pgz},
			</if>
			<if test="fd1Zff != null">
				fd1_zff=#{fd1Zff},
			</if>
			<if test="fd1Zfzj != null">
				fd1_zfzj=#{fd1Zfzj},
			</if>
			<if test="fd1Jzf != null">
				fd1_jzf=#{fd1Jzf},
			</if>
			<if test="fd1Jzzj != null">
				fd1_jzzj=#{fd1Jzzj},
			</if>
			<if test="fd1Bz != null">
				fd1_bz=#{fd1Bz},
			</if>
			<if test="fd1Hfbzclb != null">
				fd1_hfbzclb=#{fd1Hfbzclb},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_fhbzcfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			fd1_czf=#{fd1Czf},
			fd1_czzj=#{fd1Czzj},
			fd1_pgz=#{fd1Pgz},
			fd1_zff=#{fd1Zff},
			fd1_zfzj=#{fd1Zfzj},
			fd1_jzf=#{fd1Jzf},
			fd1_jzzj=#{fd1Jzzj},
			fd1_bz=#{fd1Bz},
			fd1_hfbzclb=#{fd1Hfbzclb},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_fhbzcfd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalFhbzcfds" parameterType="com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_fhbzcfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryFhbzcfdForList" parameterType="com.zjhc.gzwcq.fhbzcfd.entity.FhbzcfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_fhbzcfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_fhbzcfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="fd1Czf != null and fd1Czf != ''">
				and t.fd1_czf = #{fd1Czf}
			</if>
			<if test="fd1Czzj != null and fd1Czzj != ''">
				and t.fd1_czzj = #{fd1Czzj}
			</if>
			<if test="fd1Pgz != null and fd1Pgz != ''">
				and t.fd1_pgz = #{fd1Pgz}
			</if>
			<if test="fd1Zff != null and fd1Zff != ''">
				and t.fd1_zff = #{fd1Zff}
			</if>
			<if test="fd1Zfzj != null and fd1Zfzj != ''">
				and t.fd1_zfzj = #{fd1Zfzj}
			</if>
			<if test="fd1Jzf != null and fd1Jzf != ''">
				and t.fd1_jzf = #{fd1Jzf}
			</if>
			<if test="fd1Jzzj != null and fd1Jzzj != ''">
				and t.fd1_jzzj = #{fd1Jzzj}
			</if>
			<if test="fd1Bz != null and fd1Bz != ''">
				and t.fd1_bz = #{fd1Bz}
			</if>
			<if test="fd1Hfbzclb != null and fd1Hfbzclb != ''">
				and t.fd1_hfbzclb = #{fd1Hfbzclb}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxId" resultMap="baseResultMapExt">
		select t.*,sd1.text as fd1HfbzclbStr from cq_fhbzcfd t
		left join sys_dictionary sd1 on sd1.val=t.FD1_HFBZCLB and sd1.parent=(select id from sys_dictionary where type_code='FHBZCLB')
		where t.JBXX_ID = #{id}
    </select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_fhbzcfd where jbxx_id = #{jbxxId}
	</delete>

	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			fd1_czf,
			fd1_czzj,
			fd1_pgz,
			fd1_zff,
			fd1_zfzj,
			fd1_jzf,
			fd1_jzzj,
			fd1_bz,
			fd1_hfbzclb
		FROM `cq_fhbzcfd`
		WHERE
			JBXX_ID =#{jbxxId}
	</select>
</mapper>