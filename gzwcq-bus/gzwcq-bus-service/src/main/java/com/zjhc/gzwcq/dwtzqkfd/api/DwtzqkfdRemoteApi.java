package com.zjhc.gzwcq.dwtzqkfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.dwtzqkfd.client.DwtzqkfdFeignClient;
import com.zjhc.gzwcq.dwtzqkfd.service.api.IDwtzqkfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdParam;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/dwtzqkfdRemoteApi")
@Api(value="dwtzqkfd接口文档",tags="对外投资情况浮动")
public class DwtzqkfdRemoteApi implements DwtzqkfdFeignClient {
  
  	@Autowired
	private IDwtzqkfdService dwtzqkfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Dwtzqkfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<DwtzqkfdVo> queryByPage(@RequestBody DwtzqkfdParam dwtzqkfdParam) {
        BootstrapTableModel<DwtzqkfdVo> model = new BootstrapTableModel<DwtzqkfdVo>();
		model.setRows(dwtzqkfdService.queryDwtzqkfdByPage(dwtzqkfdParam));
		model.setTotal(dwtzqkfdService.queryTotalDwtzqkfds(dwtzqkfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Dwtzqkfd dwtzqkfd){
    	dwtzqkfdService.insert(dwtzqkfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	dwtzqkfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	dwtzqkfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Dwtzqkfd dwtzqkfd){
    	dwtzqkfdService.updateIgnoreNull(dwtzqkfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Dwtzqkfd dwtzqkfd){
    	dwtzqkfdService.update(dwtzqkfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Dwtzqkfd selectDwtzqkfdByPrimaryKey(@RequestBody Dwtzqkfd dwtzqkfd){
  		return dwtzqkfdService.selectDwtzqkfdByPrimaryKey(dwtzqkfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Dwtzqkfd> selectForList(@RequestBody Dwtzqkfd dwtzqkfd){
    	return dwtzqkfdService.selectForList(dwtzqkfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Dwtzqkfd dwtzqkfd){
    	return dwtzqkfdService.validateUniqueParam(dwtzqkfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Dwtzqkfd dwtzqkfd){
    	dwtzqkfdService.saveOne(dwtzqkfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Dwtzqkfd[] objs){
    	dwtzqkfdService.multipleSaveAndEdit(objs);
    };
	
}