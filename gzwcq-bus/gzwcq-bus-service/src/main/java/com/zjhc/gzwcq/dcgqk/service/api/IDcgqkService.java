package com.zjhc.gzwcq.dcgqk.service.api;

import java.util.Map;
import java.util.List;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkVo;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkParam;
import org.springframework.web.bind.annotation.RequestBody;

public interface IDcgqkService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Dcgqk dcgqk);

	BootstrapTableModel<DcgqkVo> loadByJbxxbId(@RequestBody DcgqkParam dcgqkParam);
	void  deletebyJbxxbId(String id);
	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Dcgqk dcgqk);
	
	/**
	* 更新
	*/
	void update(Dcgqk dcgqk);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<DcgqkVo> queryDcgqkByPage(DcgqkParam dcgqkParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalDcgqks(DcgqkParam dcgqkParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Dcgqk selectDcgqkByPrimaryKey(Dcgqk dcgqk);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Dcgqk> selectForList(Dcgqk dcgqk);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Dcgqk dcgqk);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Dcgqk dcgqk);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Dcgqk[] objs);
	
}