package com.zjhc.gzwcq.xgzjgfd.mapper;

import java.util.Map;
import java.util.List;

import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdVo;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdParam;
import org.apache.ibatis.annotations.Param;

public interface IXgzjgfdMapper {

    /*保存对象*/
    void insert(Xgzjgfd xgzjgfd);

    //物理删除
    void deleteByPrimaryKeys(Map<String, Object> map);

    void deleteByPrimaryKey(@Param("id") String id);

    //逻辑删除
    void logicDeleteByPrimaryKeys(Map<String, Object> map);

    void logicDeleteByPrimaryKey(Map<String, Object> map);

    /*根据非空属性更新对象信息*/
    void updateIgnoreNull(Xgzjgfd xgzjgfd);

    /**
     * 更新
     */
    void update(Xgzjgfd xgzjgfd);

    /*分页查询对象*/
    List<XgzjgfdVo> queryXgzjgfdForList(XgzjgfdParam xgzjgfdParam);

    /*数据总量查询*/
    long queryTotalXgzjgfds(XgzjgfdParam xgzjgfdParam);

    /*根据主键查询对象*/
    Xgzjgfd selectXgzjgfdByPrimaryKey(Xgzjgfd xgzjgfd);

    /*根据部分属性对象查询全部结果，不分页*/
    List<Xgzjgfd> selectForList(Xgzjgfd xgzjgfd);

    /**
     * 数据唯一性验证
     */
    List<Xgzjgfd> selectForUnique(Xgzjgfd xgzjgfd);

    /**
     * 根据基本信息表id查数据
     */
    List<XgzjgfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);

    List<Xgzjgfd> selectAllByJbxxId(@Param("jbxxId") String jbxxId);
}