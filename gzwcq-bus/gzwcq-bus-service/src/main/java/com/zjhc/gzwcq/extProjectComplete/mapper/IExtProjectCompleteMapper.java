package com.zjhc.gzwcq.extProjectComplete.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectComplete;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteVo;
import com.zjhc.gzwcq.extProjectComplete.entity.ExtProjectCompleteParam;
import org.apache.ibatis.annotations.Param;

public interface IExtProjectCompleteMapper {
	
	/*保存对象*/
	void insert(ExtProjectComplete extProjectComplete);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(ExtProjectComplete extProjectComplete);
	
	/**更新*/
	void update(ExtProjectComplete extProjectComplete);
	
	/*分页查询对象*/
	List<ExtProjectCompleteVo> queryExtProjectCompleteForList(ExtProjectCompleteParam extProjectCompleteParam);

	List<Long> selectAllById(ExtProjectCompleteParam extProjectCompleteParam);
	/*数据总量查询*/
	long queryTotalExtProjectCompletes(ExtProjectCompleteParam extProjectCompleteParam);
	
	/*根据主键查询对象*/
	ExtProjectComplete selectExtProjectCompleteByPrimaryKey(ExtProjectComplete extProjectComplete);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<ExtProjectComplete> selectForList(ExtProjectComplete extProjectComplete);
	
	/**
	 * 数据唯一性验证
	 * */
	List<ExtProjectComplete> selectForUnique(ExtProjectComplete extProjectComplete);


	Long  unreadNumber(@Param("organizationId") String organizationId);
}