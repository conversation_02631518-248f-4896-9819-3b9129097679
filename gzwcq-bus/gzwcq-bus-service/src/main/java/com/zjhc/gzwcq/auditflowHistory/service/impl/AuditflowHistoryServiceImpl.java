package com.zjhc.gzwcq.auditflowHistory.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.auditflowHistory.mapper.IAuditflowHistoryMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistory;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryParam;
import com.zjhc.gzwcq.auditflowHistory.service.api.IAuditflowHistoryService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class AuditflowHistoryServiceImpl implements IAuditflowHistoryService {
	
	@Autowired
	private IAuditflowHistoryMapper auditflowHistoryMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(AuditflowHistory auditflowHistory){
		auditflowHistory.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		auditflowHistory.setCreateTime(new Date());//创建时间
		auditflowHistory.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		auditflowHistory.setLastUpdateTime(new Date());//更新时间
		auditflowHistoryMapper.insert(auditflowHistory);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		auditflowHistoryMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		auditflowHistoryMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(AuditflowHistory auditflowHistory){
		auditflowHistory.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		auditflowHistory.setLastUpdateTime(new Date());//更新时间
		auditflowHistoryMapper.updateIgnoreNull(auditflowHistory);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(AuditflowHistory auditflowHistory){
		auditflowHistory.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		auditflowHistory.setLastUpdateTime(new Date());//更新时间
		auditflowHistoryMapper.update(auditflowHistory);
	}
	
	public List<AuditflowHistoryVo> queryAuditflowHistoryByPage(AuditflowHistoryParam auditflowHistoryParam) {
      	//分页
      	PageHelper.startPage(auditflowHistoryParam.getPageNumber(),auditflowHistoryParam.getLimit(),false);
		return auditflowHistoryMapper.queryAuditflowHistoryForList(auditflowHistoryParam);
	}
	

	public AuditflowHistory selectAuditflowHistoryByPrimaryKey(AuditflowHistory AuditflowHistory) {
		return auditflowHistoryMapper.selectAuditflowHistoryByPrimaryKey(AuditflowHistory);
	}
	
	public long queryTotalAuditflowHistorys(AuditflowHistoryParam auditflowHistoryParam) {
		return auditflowHistoryMapper.queryTotalAuditflowHistorys(auditflowHistoryParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<AuditflowHistoryVo> selectForList(AuditflowHistory auditflowHistory){
		return auditflowHistoryMapper.selectForList(auditflowHistory);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(AuditflowHistory auditflowHistory) {
		return auditflowHistoryMapper.selectForUnique(auditflowHistory).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(AuditflowHistory auditflowHistory) {
		if(StringUtils.isBlank(auditflowHistory.getId())) {
			this.insert(auditflowHistory);
		}else {
			this.updateIgnoreNull(auditflowHistory);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(AuditflowHistory[] objs) {
		for(AuditflowHistory auditflowHistory : objs) {
			this.saveOne(auditflowHistory);
		}
	}
}
