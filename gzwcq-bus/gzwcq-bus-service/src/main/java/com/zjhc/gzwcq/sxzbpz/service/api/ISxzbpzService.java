package com.zjhc.gzwcq.sxzbpz.service.api;

import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;

import java.util.List;
import java.util.Map;

public interface ISxzbpzService {

    /**
     * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
     */
    void insert(Sxzbpz sxzbpz);

    /**
     * 按对象中的主键进行删除，
     */
    void deleteByPrimaryKeys(Map<String, Object> map);

    /**
     * 按对象中的主键进行删除，
     */
    void deleteByPrimaryKey(String id);

    /**
     * 按对象中的主键进行所有非空属性的修改
     */
    void updateIgnoreNull(Sxzbpz sxzbpz);

    /**
     * 更新
     */
    void update(Sxzbpz sxzbpz);

    /**
     * 根据页面查询条件查询数据并分页
     */
    List<SxzbpzVo> querySxzbpzByPage(SxzbpzParam sxzbpzParam);

    /**
     * 分页查询总条数
     */
    long queryTotalSxzbpzs(SxzbpzParam sxzbpzParam);


    /**
     * 通过ID查询数据
     */
    SxzbpzVo selectSxzbpzByPrimaryKey(Sxzbpz sxzbpz);

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    List<Sxzbpz> selectForList(Sxzbpz sxzbpz);

    /**
     * 数据唯一性验证
     * <P>代码生成，必要时可以使用
     */
    boolean validateUniqueParam(Sxzbpz sxzbpz);

    /**
     * 保存单个对象
     * <P>存在则更新，不存在则新增
     */
    void saveOne(Sxzbpz sxzbpz);

    /**
     * 保存多个对象
     */
    void multipleSaveAndEdit(Sxzbpz[] objs);

    /**
     * 按条件获取业务相关表的所有字段,按表名分组展示
     */
    List<SxzbpzVo> loadFields(SxzbpzParam param);

    void saveBatch(String ids, Sxzbpz sxzbpz);

    /**
     * 获取所有经济行为分析指标,按指标类型分组
     */
    List<SxzbpzVo> loadByIndexType(SxzbpzParam param);

    /**
     * 按字段中文名搜索
     */
    List<SxzbpzVo> loadByFieldNameCn(Sxzbpz sxzbpz);
}