package com.zjhc.gzwcq.xgzjgfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdVo;
import com.zjhc.gzwcq.xgzjgfd.entity.XgzjgfdParam;

public interface IXgzjgfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Xgzjgfd xgzjgfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Xgzjgfd xgzjgfd);
	
	/**
	* 更新
	*/
	void update(Xgzjgfd xgzjgfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<XgzjgfdVo> queryXgzjgfdByPage(XgzjgfdParam xgzjgfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalXgzjgfds(XgzjgfdParam xgzjgfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Xgzjgfd selectXgzjgfdByPrimaryKey(Xgzjgfd xgzjgfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Xgzjgfd> selectForList(Xgzjgfd xgzjgfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Xgzjgfd xgzjgfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Xgzjgfd xgzjgfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Xgzjgfd[] objs);
	/**
	 * 根据基本信息表id查数据
	 */
    List<XgzjgfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);
}