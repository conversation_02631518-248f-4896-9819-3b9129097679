package com.zjhc.gzwcq.attachment.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.ftp.util.FtpPoolHelper;
import com.github.pagehelper.PageHelper;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.entity.AttachmentParam;
import com.zjhc.gzwcq.attachment.entity.AttachmentVo;
import com.zjhc.gzwcq.attachment.mapper.IAttachmentMapper;
import com.zjhc.gzwcq.attachment.service.api.IAttachmentService;

@Service
public class AttachmentServiceImpl implements IAttachmentService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	//附件保存文件夹
	@Value(value="${common.attachmentFtpDir}")
	private String attachmentFtpDir;
	
	@Autowired
	private IAttachmentMapper attachmentMapper;
  	
	
  	@Transactional(rollbackFor=Exception.class)
	public void insert(Attachment attachment){
		attachment.setOrgId(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getCurrOrgId());//获取当前用户登录组织
		attachment.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		attachment.setCreateTime(new Date());//创建时间
		attachment.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		attachment.setLastUpdateTime(new Date());//更新时间
		attachmentMapper.insert(attachment);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		attachmentMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		attachmentMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Attachment attachment){
		attachment.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		attachment.setLastUpdateTime(new Date());//更新时间
		attachmentMapper.updateIgnoreNull(attachment);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Attachment attachment){
		attachment.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		attachment.setLastUpdateTime(new Date());//更新时间
		attachmentMapper.update(attachment);
	}
	
	public List<AttachmentVo> queryAttachmentByPage(AttachmentParam attachmentParam) {
      	//分页
      	PageHelper.startPage(attachmentParam.getPageNumber(),attachmentParam.getLimit(),false);
		return attachmentMapper.queryAttachmentForList(attachmentParam);
	}
	

	public Attachment selectAttachmentByPrimaryKey(Attachment Attachment) {
		return attachmentMapper.selectAttachmentByPrimaryKey(Attachment);
	}
	
	public long queryTotalAttachments(AttachmentParam attachmentParam) {
		return attachmentMapper.queryTotalAttachments(attachmentParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Attachment> selectForList(Attachment attachment){
		return attachmentMapper.selectForList(attachment);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Attachment attachment) {
		return attachmentMapper.selectForUnique(attachment).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Attachment attachment) {
		if(StringUtils.isBlank(attachment.getId())) {
			this.insert(attachment);
		}else {
			this.updateIgnoreNull(attachment);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Attachment[] objs) {
		for(Attachment attachment : objs) {
			this.saveOne(attachment);
		}
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public Attachment uploadFile(MultipartFile file) {
		Attachment attachment = new Attachment();
		String result = "";
		if(!file.isEmpty()) {
			//文件上传到FTP
			String fileName = file.getOriginalFilename();
			String fileType = fileName.substring(fileName.lastIndexOf(".")+1,fileName.length());
			fileName = fileName.substring(0, fileName.lastIndexOf(".")) + UUID.randomUUID().toString() + "." + fileType;
			String ftpPath = attachmentFtpDir + new SimpleDateFormat("yyyy/MM/dd").format(new Date());
			logger.info(String.format("开始上传文件%s，大小%s，目标地址%s", fileName,file.getSize(),ftpPath));
			if(FtpPoolHelper.uploadLocalFile(ftpPath, file, fileName)) {//上传成功
				try {
					//保存数据库
					attachment.setFileName(file.getOriginalFilename());
					attachment.setFileType(fileType);
					attachment.setFtpFilePath(ftpPath+ "/" +fileName);
					attachment.setLastFileName(fileName);
					this.saveOne(attachment);
					logger.info(String.format("上传文件%s成功", fileName));
				}catch(Exception e) {
					logger.error("保存文件记录失败", e);
					result += String.format("文件[%s]上传失败；", file.getOriginalFilename());
				}
			}else {
				logger.info(String.format("上传文件%s失败", fileName));
				throw new RuntimeException(String.format("文件[%s]上传失败；", file.getOriginalFilename()));
			}
		}else {
			throw new RuntimeException(String.format("文件[%s]为空；", file.getOriginalFilename()));
		}
		return attachment;
	}
}
