package com.zjhc.gzwcq.extProjectTransferee.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeParam;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeVo;
import com.zjhc.gzwcq.extProjectTransferee.entity.FileInfoDTO;
import com.zjhc.gzwcq.extProjectTransferee.mapper.IExtProjectTransfereeMapper;
import com.zjhc.gzwcq.extProjectTransferee.service.api.IExtProjectTransfereeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class ExtProjectTransfereeServiceImpl implements IExtProjectTransfereeService {

    @Autowired
    private IExtProjectTransfereeMapper extProjectTransfereeMapper;


    @Transactional(rollbackFor = Exception.class)
    public void insert(ExtProjectTransferee extProjectTransferee) {
        extProjectTransferee.setCreateTime(new Date());//创建时间
        extProjectTransferee.setLastUpdateTime(new Date());//更新时间
        extProjectTransfereeMapper.insert(extProjectTransferee);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKeys(Map<String, Object> map) {
        extProjectTransfereeMapper.deleteByPrimaryKeys(map);
    }

    /**
     * 删除一个对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKey(String id) {
        extProjectTransfereeMapper.deleteByPrimaryKey(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateIgnoreNull(ExtProjectTransferee extProjectTransferee) {
        extProjectTransferee.setLastUpdateTime(new Date());//更新时间
        extProjectTransfereeMapper.updateIgnoreNull(extProjectTransferee);
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(ExtProjectTransferee extProjectTransferee) {
        extProjectTransferee.setLastUpdateTime(new Date());//更新时间
        extProjectTransfereeMapper.update(extProjectTransferee);
    }

    public List<ExtProjectTransfereeVo> queryExtProjectTransfereeByPage(ExtProjectTransfereeParam extProjectTransfereeParam) {
        //分页
        PageHelper.startPage(extProjectTransfereeParam.getPageNumber(), extProjectTransfereeParam.getLimit(), false);
        List<ExtProjectTransfereeVo> extProjectTransfereeVos = extProjectTransfereeMapper.queryExtProjectTransfereeForList(extProjectTransfereeParam);
        return extProjectTransfereeVos;
    }

    /**
     * todo 暂时不使用因为在插入的时候已经做处理了 查询的时候就不需要处理
     * @param vos
     */
    public void ExtProjectCompleteData(List<ExtProjectTransfereeVo> vos) {
        vos.stream().forEach(item -> {
            String yxffj = item.getYxffj();
            if (StringUtils.isBlank(yxffj)) {
                return;
            }
            List<FileInfoDTO> dto = JSON.parseObject(yxffj, new TypeReference<List<FileInfoDTO>>() {
            });
            int size = dto.size();
            //说明没有数据不需要做数据处理
            if (size <= 0) {
                return;
            } else if (size > 1) {
                //说明有多个数据全要处理
                String fName = "";
                List<String> fAtt = new ArrayList<>();
                for (FileInfoDTO fileInfoDTO : dto) {
                    if (StringUtils.isBlank(fName)) {
                        fName = fileInfoDTO.getFName();
                    } else {
                        fName += "," + fileInfoDTO.getFName();
                    }
                    fAtt.addAll(fileInfoDTO.getFAtt());
                }
                String fAttString = "";
                for (String url : fAtt) {
                    if (StringUtils.isBlank(fAttString)) {
                        fAttString = url;
                    } else {
                        fAttString += "," + url;
                    }
                }
                item.setYxffj(fName);
                item.setFatt(fAttString);
            } else {
                //说明就一条数据需要处理
                String fName = "";
                String fAttString = "";
                for (FileInfoDTO fileInfoDTO : dto) {
                    fName = fileInfoDTO.getFName();
                    fAttString = fileInfoDTO.getFAtt().get(0);
                }
                item.setYxffj(fName);
                item.setFatt(fAttString);
            }
        });
    }

	public ExtProjectTransferee selectExtProjectTransfereeByPrimaryKey(ExtProjectTransferee ExtProjectTransferee) {
		return extProjectTransfereeMapper.selectExtProjectTransfereeByPrimaryKey(ExtProjectTransferee);
	}
	
	public long queryTotalExtProjectTransferees(ExtProjectTransfereeParam extProjectTransfereeParam) {
		return extProjectTransfereeMapper.queryTotalExtProjectTransferees(extProjectTransfereeParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<ExtProjectTransferee> selectForList(ExtProjectTransferee extProjectTransferee){
		return extProjectTransfereeMapper.selectForList(extProjectTransferee);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(ExtProjectTransferee extProjectTransferee) {
		return extProjectTransfereeMapper.selectForUnique(extProjectTransferee).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(ExtProjectTransferee extProjectTransferee) {
      	if(extProjectTransferee.getId() == null) {
			this.insert(extProjectTransferee);
		}else {
			this.updateIgnoreNull(extProjectTransferee);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(ExtProjectTransferee[] objs) {
		for(ExtProjectTransferee extProjectTransferee : objs) {
			this.saveOne(extProjectTransferee);
		}
	}
}
