package com.zjhc.gzwcq.businessInfo.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

public interface IBusinessInfoMapper {
	
	/*保存对象*/
	void insert(BusinessInfo businessInfo);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(BusinessInfo businessInfo);
	
	/**更新*/
	void update(BusinessInfo businessInfo);
	
	/*分页查询对象*/
	List<BusinessInfoVo> queryBusinessInfoForList(BusinessInfoParam businessInfoParam);
	
	/*数据总量查询*/
	long queryTotalBusinessInfos(BusinessInfoParam businessInfoParam);
	
	/*根据主键查询对象*/
	BusinessInfo selectBusinessInfoByPrimaryKey(BusinessInfo businessInfo);

	BusinessInfo selectBusinessInfoByPrimaryKeyByJbxxbId(@Param("jbxxbId") String jbxxbId);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<BusinessInfo> selectForList(BusinessInfo businessInfo);
	
	/**
	 * 数据唯一性验证
	 * */
	List<BusinessInfo> selectForUnique(BusinessInfo businessInfo);

	/**
	 * 查询企业是否有未审核通过的登记表数据
	 * @param unitid  占有，变动，注销组织id
	 * @return
	 */
	Integer findNoAllApprovalBus(@Param("jbxxId")String jbxxId,@Param("unitid")String unitid);

	/**
	 * 查询工作台审核代办
	 * @param todoType  工作台tab页，1待审核，2上级退回
	 * @param auditHostingList 自己组织加被托管组织
	 * @return
	 */
	List<BusinessInfoVo> selectTodoList(@Param("todoType")String todoType,@Param("auditHostingList")List<String> auditHostingList);

	/**
	 * 待办（待审核/退回）列表查询(分页+筛选条件)
	 */
	List<BusinessInfoVo> todoOrReturnList(@Param("param")BusinessInfoParam param, @Param("auditHostingList")List<String> auditHostingList);

    void deleteByJbxxId(String jbxxId);

	/**
	 * 查询当前企业所有审核通过的历史记录和当前这条记录
	 */
    List<BusinessInfoVo> allApprovedAndNowList(BusinessInfo param);

	/**
	 * 查询当前登录人待审核/退回的数量
	 */
    int loginUserTodoOrReturnNum(@Param("param")BusinessInfoParam param, @Param("auditHostingList")List<String> auditHostingList);
	/**
	 * 查询当前登录人已审核审核/退回的数据
	 */
	List<BusinessInfoVo> getPassData(BusinessInfoParam param);
}