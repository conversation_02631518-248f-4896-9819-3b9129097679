package com.zjhc.gzwcq.businessInfo.service.api;

import java.util.Map;
import java.util.List;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.zjhc.gzwcq.auditflowHistory.entity.AuditflowHistoryVo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoVo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import org.springframework.web.bind.annotation.RequestBody;

public interface IBusinessInfoService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(BusinessInfo businessInfo);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(BusinessInfo businessInfo);
	
	/**
	* 更新
	*/
	void update(BusinessInfo businessInfo);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<BusinessInfoVo> queryBusinessInfoByPage(BusinessInfoParam businessInfoParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalBusinessInfos(BusinessInfoParam businessInfoParam);
  
	
	/**
	 *通过ID查询数据
	 */
	BusinessInfo selectBusinessInfoByPrimaryKey(BusinessInfo businessInfo);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<BusinessInfo> selectForList(BusinessInfo businessInfo);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(BusinessInfo businessInfo);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(BusinessInfo businessInfo);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(BusinessInfo[] objs);

	/**
	 * 获取企业等级历史记录列表(只查审核通过的)
	 * @param businessInfo 根据企业id查询
	 * @return
	 */
	List<BusinessInfoVo> loadHistoryList(BusinessInfoParam param);

	/**
	 * 提交审核
	 * @param param
	 */
	String submitReview(FormVo formVo);

	/**
	 * 审核
	 * 组织审核由下级往上级，一级一级进行审核(如5级 -> 4级)
	 * 组织父级可能为虚拟组织，则跳过继续找上级组织
	 * 1级组织审核时，有(初审和复审)，需要先初审，再复审
	 * 1级组织审核后，如果变动企业是国有企业，则需要国资委审核，如果是合伙企业则直接结束
	 * @param param
	 */
	void review(BusinessInfoParam param);

	ResponseEnvelope recallReview(BusinessInfoParam param);

	ResponseEnvelope selectRecallReview(BusinessInfoParam param);
	/**
	 * 审核轨迹列表查询
	 * @param param 传入jbxxid进行查询
	 * @return
	 */
	List<AuditflowHistoryVo> reviewHistoryList(BusinessInfoParam param);

	/**
	 * 待办（待审核/退回）列表查询
	 * @param param 传入查询条件企业名称或企业代码
	 * @return
	 */
	List<BusinessInfoVo> todoList(BusinessInfoParam param);

	/**
	 * 待办（待审核/退回）列表查询(分页+筛选条件)
	 */
    BootstrapTableModel<BusinessInfoVo> todoOrReturnList(BusinessInfoParam param);

    void deleteByJbxxId(String jbxxId);

	/**
	 * 当前企业所有审核通过的历史列表和当前这条记录
	 */
	BootstrapTableModel<BusinessInfoVo> allApprovedAndNowList(BusinessInfo param);

	String submitReviewStep2(BusinessInfoParam param);

	/**
	 * 工商登记资料补录上报
	 */
    String suppleSubmitReview(Jbxxb jbxxb);

    BootstrapTableModel<BusinessInfoVo> getPassData(BusinessInfoParam param);
}