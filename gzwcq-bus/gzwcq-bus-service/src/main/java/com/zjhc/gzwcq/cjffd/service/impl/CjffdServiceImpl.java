package com.zjhc.gzwcq.cjffd.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.cjffd.mapper.ICjffdMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.cjffd.entity.Cjffd;
import com.zjhc.gzwcq.cjffd.entity.CjffdVo;
import com.zjhc.gzwcq.cjffd.entity.CjffdParam;
import com.zjhc.gzwcq.cjffd.service.api.ICjffdService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import com.boot.IAdmin.common.utils.Constants;
import org.apache.commons.lang3.StringUtils;

@Service
public class CjffdServiceImpl implements ICjffdService {
	
	@Autowired
	private ICjffdMapper cjffdMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		cjffdMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Cjffd cjffd){
		cjffd.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		cjffd.setCreateTime(new Date());//创建时间
		cjffd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		cjffd.setLastUpdateTime(new Date());//更新时间
		cjffdMapper.insert(cjffd);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		cjffdMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		cjffdMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Cjffd cjffd){
		cjffd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		cjffd.setLastUpdateTime(new Date());//更新时间
		cjffdMapper.updateIgnoreNull(cjffd);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Cjffd cjffd){
		cjffd.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		cjffd.setLastUpdateTime(new Date());//更新时间
		cjffdMapper.update(cjffd);
	}
	
	public List<CjffdVo> queryCjffdByPage(CjffdParam cjffdParam) {
      	//分页
      	PageHelper.startPage(cjffdParam.getPageNumber(),cjffdParam.getLimit(),false);
		return cjffdMapper.queryCjffdForList(cjffdParam);
	}
	

	public Cjffd selectCjffdByPrimaryKey(Cjffd Cjffd) {
		return cjffdMapper.selectCjffdByPrimaryKey(Cjffd);
	}
	
	public long queryTotalCjffds(CjffdParam cjffdParam) {
		return cjffdMapper.queryTotalCjffds(cjffdParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Cjffd> selectForList(Cjffd cjffd){
		return cjffdMapper.selectForList(cjffd);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Cjffd cjffd) {
		return cjffdMapper.selectForUnique(cjffd).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Cjffd cjffd) {
		if(StringUtils.isBlank(cjffd.getId())) {
			this.insert(cjffd);
		}else {
			this.updateIgnoreNull(cjffd);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Cjffd[] objs) {
		for(Cjffd cjffd : objs) {
			this.saveOne(cjffd);
		}
	}

	@Override
	public List<CjffdVo> selectByJbxxId(String jbxxId) {
		return cjffdMapper.selectByJbxxId(jbxxId);
	}
}
