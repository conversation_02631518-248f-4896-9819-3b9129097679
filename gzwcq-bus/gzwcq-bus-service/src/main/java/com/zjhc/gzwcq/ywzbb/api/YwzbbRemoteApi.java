package com.zjhc.gzwcq.ywzbb.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.ywzbb.client.YwzbbFeignClient;
import com.zjhc.gzwcq.ywzbb.service.api.IYwzbbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbParam;
import com.zjhc.gzwcq.ywzbb.entity.YwzbbVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/ywzbbRemoteApi")
@Api(value="ywzbb接口文档",tags="产权业务指标表")
public class YwzbbRemoteApi implements YwzbbFeignClient {
  
  	@Autowired
	private IYwzbbService ywzbbService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Ywzbb
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<YwzbbVo> queryByPage(@RequestBody YwzbbParam ywzbbParam) {
        BootstrapTableModel<YwzbbVo> model = new BootstrapTableModel<YwzbbVo>();
		model.setRows(ywzbbService.queryYwzbbByPage(ywzbbParam));
		model.setTotal(ywzbbService.queryTotalYwzbbs(ywzbbParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Ywzbb ywzbb){
    	ywzbbService.insert(ywzbb);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	ywzbbService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	ywzbbService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Ywzbb ywzbb){
    	ywzbbService.updateIgnoreNull(ywzbb);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Ywzbb ywzbb){
    	ywzbbService.update(ywzbb);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public YwzbbVo selectYwzbbByPrimaryKey(@RequestBody Ywzbb ywzbb){
  		return ywzbbService.selectYwzbbByPrimaryKey(ywzbb);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Ywzbb> selectForList(@RequestBody Ywzbb ywzbb){
    	return ywzbbService.selectForList(ywzbb);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Ywzbb ywzbb){
    	return ywzbbService.validateUniqueParam(ywzbb);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Ywzbb ywzbb){
    	ywzbbService.saveOne(ywzbb);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Ywzbb[] objs){
    	ywzbbService.multipleSaveAndEdit(objs);
    };
	
}