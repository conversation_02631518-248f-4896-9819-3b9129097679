package com.zjhc.gzwcq.ygqcyffd.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdVo;
import com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdParam;
import org.apache.ibatis.annotations.Param;

public interface IYgqcyffdMapper {
	
	/*保存对象*/
	void insert(Ygqcyffd ygqcyffd);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Ygqcyffd ygqcyffd);
	
	/**更新*/
	void update(Ygqcyffd ygqcyffd);
	
	/*分页查询对象*/
	List<YgqcyffdVo> queryYgqcyffdForList(YgqcyffdParam ygqcyffdParam);
	
	/*数据总量查询*/
	long queryTotalYgqcyffds(YgqcyffdParam ygqcyffdParam);
	
	/*根据主键查询对象*/
	YgqcyffdVo selectYgqcyffdByPrimaryKey(Ygqcyffd ygqcyffd);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Ygqcyffd> selectForList(Ygqcyffd ygqcyffd);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Ygqcyffd> selectForUnique(Ygqcyffd ygqcyffd);
	/**
	 * 根据基本信息表id查数据
	 */
    List<YgqcyffdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);

	List<Ygqcyffd> selectAllByJbxxId(@Param("jbxxId")String jbxxId);
}