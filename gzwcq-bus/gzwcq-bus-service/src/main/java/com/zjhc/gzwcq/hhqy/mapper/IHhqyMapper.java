package com.zjhc.gzwcq.hhqy.mapper;

import java.util.Map;
import java.util.List;

import com.zjhc.gzwcq.hhqy.entity.Hhqy;
import com.zjhc.gzwcq.hhqy.entity.HhqyParam;
import com.zjhc.gzwcq.hhqy.entity.HhqyVo;
import org.apache.ibatis.annotations.Param;

public interface IHhqyMapper {
	
	/*保存对象*/
	void insert(Hhqy hhqy);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Hhqy hhqy);
	
	/**更新*/
	void update(Hhqy hhqy);
	
	/*分页查询对象*/
	List<HhqyVo> queryHhqyForList(HhqyParam hhqyParam);
	
	/*数据总量查询*/
	long queryTotalHhqys(HhqyParam hhqyParam);
	
	/*根据主键查询对象*/
	Hhqy selectHhqyByPrimaryKey(Hhqy hhqy);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Hhqy> selectForList(Hhqy hhqy);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Hhqy> selectForUnique(Hhqy hhqy);

	/**
	 * 按基本信息id查询合伙企业数据
	 */
    HhqyVo selectByJbxxbId(String jbxxId);

    void deleteByJbxxId(String jbxxId);

	List<Hhqy> selectAllByJbxxId(@Param("jbxxId")String jbxxId);
}