package com.zjhc.gzwcq.cgrfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.cgrfd.client.CgrfdFeignClient;
import com.zjhc.gzwcq.cgrfd.service.api.ICgrfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdParam;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/cgrfdRemoteApi")
@Api(value="cgrfd接口文档",tags="持股人浮动表")
public class CgrfdRemoteApi implements CgrfdFeignClient {
  
  	@Autowired
	private ICgrfdService cgrfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Cgrfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<CgrfdVo> queryByPage(@RequestBody CgrfdParam cgrfdParam) {
        BootstrapTableModel<CgrfdVo> model = new BootstrapTableModel<CgrfdVo>();
		model.setRows(cgrfdService.queryCgrfdByPage(cgrfdParam));
		model.setTotal(cgrfdService.queryTotalCgrfds(cgrfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Cgrfd cgrfd){
    	cgrfdService.insert(cgrfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	cgrfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	cgrfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Cgrfd cgrfd){
    	cgrfdService.updateIgnoreNull(cgrfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Cgrfd cgrfd){
    	cgrfdService.update(cgrfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Cgrfd selectCgrfdByPrimaryKey(@RequestBody Cgrfd cgrfd){
  		return cgrfdService.selectCgrfdByPrimaryKey(cgrfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Cgrfd> selectForList(@RequestBody Cgrfd cgrfd){
    	return cgrfdService.selectForList(cgrfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Cgrfd cgrfd){
    	return cgrfdService.validateUniqueParam(cgrfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Cgrfd cgrfd){
    	cgrfdService.saveOne(cgrfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Cgrfd[] objs){
    	cgrfdService.multipleSaveAndEdit(objs);
    };
	
}