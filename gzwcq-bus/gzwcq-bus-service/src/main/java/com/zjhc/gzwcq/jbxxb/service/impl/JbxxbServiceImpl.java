package com.zjhc.gzwcq.jbxxb.service.impl;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.domain.ResponseEnvelope;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.common.utils.DateUtils;
import com.boot.IAdmin.common.utils.File.FileUtils;
import com.boot.IAdmin.common.utils.StringUtil;
import com.boot.IAdmin.common.utils.UUIDGenerator;
import com.boot.IAdmin.common.utils.office.Doc2Pdf;
import com.boot.IAdmin.common.utils.office.WordPoiUtil;
import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.mapper.SysUserInfoMapper;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.model.user.SysUserVo;
import com.boot.iAdmin.access.service.impl.OrganizationService;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.boot.iAdmin.ftp.util.FtpPoolHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfDictionary;
import com.itextpdf.text.pdf.PdfName;
import com.itextpdf.text.pdf.PdfNumber;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfSmartCopy;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.service.api.IAttachmentService;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfo;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.mapper.IBusinessInfoMapper;
import com.zjhc.gzwcq.businessInfo.service.api.IBusinessInfoService;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cgrfd.entity.CgrfdVo;
import com.zjhc.gzwcq.cgrfd.mapper.ICgrfdMapper;
import com.zjhc.gzwcq.cgrfd.service.api.ICgrfdService;
import com.zjhc.gzwcq.cjffd.entity.Cjffd;
import com.zjhc.gzwcq.cjffd.entity.CjffdVo;
import com.zjhc.gzwcq.cjffd.service.api.ICjffdService;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.czfd.mapper.ICzfdMapper;
import com.zjhc.gzwcq.czfd.service.api.ICzfdService;
import com.zjhc.gzwcq.dcgqk.entity.Dcgqk;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkParam;
import com.zjhc.gzwcq.dcgqk.entity.DcgqkVo;
import com.zjhc.gzwcq.dcgqk.mapper.IDcgqkMapper;
import com.zjhc.gzwcq.dcgqk.service.api.IDcgqkService;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.dwtzqkfd.entity.DwtzqkfdVo;
import com.zjhc.gzwcq.dwtzqkfd.service.api.IDwtzqkfdService;
import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.fhbzcfd.service.api.IFhbzcfdService;
import com.zjhc.gzwcq.hhqy.entity.Hhqy;
import com.zjhc.gzwcq.hhqy.entity.HhqyVo;
import com.zjhc.gzwcq.hhqy.service.api.IHhqyService;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.hhrqkfd.service.api.IHhrqkfdService;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.hrhcfd.service.api.IHrhcfdService;
import com.zjhc.gzwcq.jbxxb.entity.FormVo;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbParam;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import com.zjhc.gzwcq.jbxxb.mapper.IJbxxbMapper;
import com.zjhc.gzwcq.jbxxb.service.api.IJbxxbService;
import com.zjhc.gzwcq.monitorwarn.entity.Monitorwarn;
import com.zjhc.gzwcq.monitorwarn.service.api.IMonitorwarnService;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.syccfpfd.service.api.ISyccfpfdService;
import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.xgzjgfd.service.api.IXgzjgfdService;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.ygqcyffd.service.api.IYgqcyffdService;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb;
import com.zjhc.gzwcq.ywzbb.mapper.IYwzbbMapper;
import com.zjhc.gzwcq.ywzbb.service.api.IYwzbbService;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2;
import com.zjhc.gzwcq.ywzbb2.service.api.IYwzbb2Service;
import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import com.zjhc.gzwcq.zrsrfd.service.api.IZrsrfdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class JbxxbServiceImpl implements IJbxxbService {

    @Resource
    private IJbxxbMapper jbxxbMapper;

    @Autowired
    private IBusinessInfoService businessInfoService;

    @Resource
    private ICgrfdMapper cgrfdMapper;

    @Resource
    private ICzfdMapper czfdMapper;
    @Autowired
    private IFhbzcfdService fhbzcfdService;
    @Autowired
    private IXgzjgfdService xgzjgfdService;
    @Autowired
    private IZrsrfdService zrsrfdService;
    @Autowired
    private ICzfdService czfdService;
    @Autowired
    private IYgqcyffdService ygqcyffdService;
    @Autowired
    private ICgrfdService cgrfdService;
    @Autowired
    private IYwzbb2Service ywzbb2Service;
    @Autowired
    private ICjffdService cjffdService;
    @Autowired
    private IYwzbbService ywzbbService;
    @Autowired
    private IHrhcfdService hrhcfdService;
    @Autowired
    private IDwtzqkfdService dwtzqkfdService;
    @Autowired
    private IHhrqkfdService hhrqkfdService;
    @Autowired
    private IHhqyService hhqyService;
    @Resource
    private IYwzbbMapper ywzbbMapper;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ISyccfpfdService syccfpfdService;
    @Autowired
    private IAttachmentService attachmentService;
    @Autowired
    private DictCacheStrategy dictCacheStrategy;
    @Resource
    private SysUserInfoMapper userInfoMapper;
    @Autowired
    private IMonitorwarnService monitorwarnService;
    @Resource
    private IBusinessInfoMapper businessInfoMapper;
    @Autowired
    private IDcgqkService dcgqkService;
    @Autowired
    private IDcgqkMapper dcgqkMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(Jbxxb jbxxb) {
        jbxxb.setCreateUser(((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
        jbxxb.setCreateTime(new Date());//创建时间
        jbxxb.setLastUpdateUser(((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
        jbxxb.setLastUpdateTime(new Date());//更新时间
        jbxxb.setJbShzt(Integer.parseInt(Constants.JBXX_REVIEW_STATUS_3)); //默认流程未发起
        jbxxb.setJbDeleted(Constants.NO_DELETED);
        jbxxbMapper.insert(jbxxb);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKeys(Map<String, Object> map) {
        final List<String> jbxxIds = (List<String>) map.get("jbxxbs");
        Boolean realDel = (Boolean) map.get("map");
        if(null != realDel && realDel){  //物理删除
            jbxxbMapper.deleteByPrimaryKeys(map);
            for (String jbxxId : jbxxIds) {
                //校验--被退回的审批流，需判断流程是否到过国资委，如果到过则不允许删除，提示删除失败，存在国资委退回记录
//            List<AuditflowHistoryVo> historyVos = jbxxbMapper.selectHasGzwApprovalCount(jbxxId);
//            if(CollectionUtils.isNotEmpty(historyVos)){
//                long count = historyVos.stream().filter(h -> ("1".equals(h.getAfProcesstype()) || "2".equals(h.getAfProcesstype()))
//                        && "1".equals(h.getBusinesstype())).count();
//                if(!"1".equals(historyVos.get(0).getBusinesstype())
//                        && count > 0){
//                    throw new RuntimeException("含有经过国资委审核数据，无法删除!");
//                }
//            }
                businessInfoService.deleteByJbxxId(jbxxId);
                ywzbbService.deleteByJbxxId(jbxxId);
                ywzbb2Service.deleteByJbxxId(jbxxId);
                hhqyService.deleteByJbxxId(jbxxId);
                dwtzqkfdService.deleteByJbxxId(jbxxId);
                hhrqkfdService.deleteByJbxxId(jbxxId);
                fhbzcfdService.deleteByJbxxId(jbxxId);
                czfdService.deleteByJbxxId(jbxxId);
                ygqcyffdService.deleteByJbxxId(jbxxId);
                hrhcfdService.deleteByJbxxId(jbxxId);
                zrsrfdService.deleteByJbxxId(jbxxId);
                cgrfdService.deleteByJbxxId(jbxxId);
                syccfpfdService.deleteByJbxxId(jbxxId);
                xgzjgfdService.deleteByJbxxId(jbxxId);
                cjffdService.deleteByJbxxId(jbxxId);
            }
        }else{ //逻辑删除
            jbxxbMapper.logicDeleteByPrimaryKeys(map);
        }
    }

    /**
     * 删除一个对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrimaryKey(String id) {
        jbxxbMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIgnoreNull(Jbxxb jbxxb) {
        jbxxb.setLastUpdateUser(((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
        jbxxb.setLastUpdateTime(new Date());//更新时间
        jbxxbMapper.updateIgnoreNull(jbxxb);
    }

    /**
     * 更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Jbxxb jbxxb) {
        jbxxb.setLastUpdateUser(((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
        jbxxb.setLastUpdateTime(new Date());//更新时间
        jbxxbMapper.update(jbxxb);
    }

    @Override
    public List<JbxxbVo> queryJbxxbByPage(JbxxbParam jbxxbParam) {
        //当前登陆人可见组织根节点
        jbxxbParam.setVisibles(organizationService.getVisibles());
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        jbxxbParam.setUserId(user.getUser_id());
        //分页
        PageHelper.startPage(jbxxbParam.getPageNumber(), jbxxbParam.getLimit(), false);
        List<JbxxbVo> jbxxbVos = jbxxbMapper.queryJbxxbForList(jbxxbParam);
        //只有填报中的需要遍历
        if (jbxxbParam.getJbShzt() != null
                && jbxxbParam.getJbShzt() == Integer.parseInt(Constants.JBXX_REVIEW_STATUS_3)) {
            for (JbxxbVo jbxxbVo : jbxxbVos) {
                jbxxbVo.setCanEdit(false);
                jbxxbVo.setCanDelete(false);
                //填报中的编辑仅开放给申报人及同级账号
                //当前登陆人的组织和填报人一致,并且账号类型及审核层级一致
                SysUserVo createUser = userInfoMapper.getUserInfoByUserId(jbxxbVo.getCreateUser());
                //审核层级如为空则转为null
                if (createUser != null && StringUtils.isBlank(createUser.getAudit_level())){
                    createUser.setAudit_level(null);
                }
                if (StringUtils.isBlank(user.getAudit_level())){
                    user.setAudit_level(null);
                }
                if (createUser != null && Objects.equals(user.getOrganization_id(),createUser.getOrganization_id())
                        && Objects.equals(user.getType(),createUser.getType())
                        && Objects.equals(createUser.getAudit_level(),user.getAudit_level())){
                    jbxxbVo.setCanEdit(true);
                    jbxxbVo.setCanDelete(true);
                }
                //产权登记情形
                String ZYCQDJQX = dictCacheStrategy.getTextByVal(Constants.ZYCQDJQX, jbxxbVo.getJbZycqdjqx());
                String ZXCQDJQX = dictCacheStrategy.getTextByVal(Constants.ZXCQDJQX, jbxxbVo.getJbZxcqdjqx());
                String BDCQDJQX = dictCacheStrategy.getTextByVal(Constants.BDCQDJQX, jbxxbVo.getJbBdcqdjqx());
                String jbCqdjqxStr = ZYCQDJQX == null ? (ZXCQDJQX == null ? (BDCQDJQX == null ? "" : BDCQDJQX) : ZXCQDJQX) : ZYCQDJQX;
                jbxxbVo.setJbCqdjqxStr(jbCqdjqxStr);
            }
        }
        return jbxxbVos;
    }

    @Override
    public JbxxbVo selectJbxxbByPrimaryKey(Jbxxb Jbxxb) {
        JbxxbVo jbxxbVo = jbxxbMapper.selectJbxxbByPrimaryKey(Jbxxb);
        //SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
        //jbxxbVo.setJbCzrzzjgStr(sysOrganization == null?"":sysOrganization.getOrganization_code());  //+sysOrganization.getOrganization_name()
        jbxxbVo.setJbCzrzzjgStr(jbxxbVo.getJbCzrzzjgdm());
        jbxxbVo.setFd9TableData(cgrfdMapper.selectByJbxxId(Jbxxb.getId()));
        List<CzfdVo> czfdVos = czfdMapper.selectByJbxxId(Jbxxb.getId());
        jbxxbVo.setFdTableData(czfdVos);
        jbxxbVo.setYwzbbVo(ywzbbMapper.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setYwzbb2Vo(ywzbb2Service.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setFd6TableData(syccfpfdService.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setFd1TableData(fhbzcfdService.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setFd3TableData(xgzjgfdService.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setFd8TableData(zrsrfdService.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setFd7TableData(hrhcfdService.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setFd2TableData(ygqcyffdService.selectByJbxxId(Jbxxb.getId()));
        jbxxbVo.setFd5TableData(cjffdService.selectByJbxxId(Jbxxb.getId()));
        if (StringUtils.isNotBlank(jbxxbVo.getJbGsdjxgzl())){
            List<Attachment> attachments = attachmentService.selectForList(new Attachment(jbxxbVo.getJbGsdjxgzl()));
            Attachment attachment = CollectionUtils.isEmpty(attachments) ? null : attachments.get(0);
            jbxxbVo.setGsAttachment(attachment);
        }
        //合伙企业
        HhqyVo hhqyVo = hhqyService.selectByJbxxbId(Jbxxb.getId());
        if (!Objects.isNull(hhqyVo)) {
            String hhCzqyStr = hhqyVo.getHhCzqyStr();
            if (StringUtils.isNotBlank(hhCzqyStr)) {
                String[] s1 = hhCzqyStr.split(" ");
                String s;
                if (s1.length > 1){
                     s = s1[1];
                }else {
                     s = s1[0];
                }

                hhqyVo.setHhCzqyStr(s);
            }
        }
        String jbGjczqy = jbxxbVo.getJbGjczqy();
        if (StringUtils.isNotBlank(jbGjczqy)){
            String[] s1 = jbGjczqy.split("_");
            String s;
            if (s1.length >1 ){
                 s = s1[1];
            }else {
                s = s1[0];
            }

            jbxxbVo.setJbGjczqyCode(s);
        }
        //对外投资情况
        List<DwtzqkfdVo> dwtzqkfdVos = dwtzqkfdService.selectByJbxxbId(Jbxxb.getId());
        if (hhqyVo != null){
            //注册地
            if (Constants.JNJW_1.equals(jbxxbVo.getJbJnjw())){
                hhqyVo.setHhZyjycsStr(dictCacheStrategy.getTextByVal("ZCD",hhqyVo.getHhZyjycs()));
                if (CollectionUtils.isNotEmpty(dwtzqkfdVos)){
                    for (DwtzqkfdVo dwtzqkfdVo : dwtzqkfdVos) {
                        dwtzqkfdVo.setAddressStr(dictCacheStrategy.getTextByVal("ZCD",dwtzqkfdVo.getAddress()));
                    }
                }
            }else if (Constants.JNJW_2.equals(jbxxbVo.getJbJnjw())){
                hhqyVo.setHhZyjycsStr(dictCacheStrategy.getTextByVal("ZCDJW",hhqyVo.getHhZyjycs()));
                if (CollectionUtils.isNotEmpty(dwtzqkfdVos)){
                    for (DwtzqkfdVo dwtzqkfdVo : dwtzqkfdVos) {
                        dwtzqkfdVo.setAddressStr(dictCacheStrategy.getTextByVal("ZCDJW",dwtzqkfdVo.getAddress()));
                    }
                }
            }
        }
        jbxxbVo.setHhqyVo(hhqyVo);
        jbxxbVo.setHhrqkfdList(hhrqkfdService.selectByJbxxbId(Jbxxb.getId()));
        jbxxbVo.setDwtzqkfdList(dwtzqkfdVos);
        //是否隐藏导出按钮
        jbxxbVo.setCanExportJbxx(jbxxbVo.getJbShzt() != null && jbxxbVo.getJbShzt() != 8);
        this.dataJbxxbVo(jbxxbVo);
        BootstrapTableModel<DcgqkVo> voBootstrapTableModel = dcgqkService.loadByJbxxbId(new DcgqkParam() {{
            jbxxId = jbxxbVo.getId();
        }});
        List<DcgqkVo> collect = voBootstrapTableModel.getRows().stream().collect(Collectors.toList());
        for (DcgqkVo dcgqkVo : collect) {
            this.dataDcgqkVo(dcgqkVo);
        }
        jbxxbVo.setJbDcggkList(collect);
        //当变动或者注销时，企业设立登记状态写死为产权工商均已设立登记
        if(Constants.RG_TYPE_BD.equals(jbxxbVo.getRgType()) || Constants.RG_TYPE_ZX.equals(jbxxbVo.getRgType())){
            jbxxbVo.setJbQyslzt(Constants.GSDJYBLZT);
        }
        return jbxxbVo;
    }

    @Override
    public long queryTotalJbxxbs(JbxxbParam jbxxbParam) {
        return jbxxbMapper.queryTotalJbxxbs(jbxxbParam);
    }

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    @Override
    public List<Jbxxb> selectForList(Jbxxb jbxxb) {
        return jbxxbMapper.selectForList(jbxxb);
    }

    /**
     * 数据唯一性验证
     */
    @Override
    public boolean validateUniqueParam(Jbxxb jbxxb) {
        if (StringUtils.isBlank(jbxxb.getJbCzrzzjgid())){
            //国有/合伙企业未传出资人id,无法判断,直接返回true
            if (StringUtils.isBlank(jbxxb.getHhCzqyId())){
                return true;
            }
            jbxxb.setJbCzrzzjgid(jbxxb.getHhCzqyId());
        }
        //出资企业
        SysOrganization czrzz = organizationService.selectOrganizationById(new SysOrganization(jbxxb.getJbCzrzzjgid()));
        jbxxb.setJbSsgzjgjg(czrzz.getSsgzjgjg());
        return jbxxbMapper.selectForUnique(jbxxb) == 0L;
    }

    /**
     * 保存单个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Jbxxb saveOne(Jbxxb jbxxb) {
        if (StringUtils.isBlank(jbxxb.getId())) {
            this.insert(jbxxb);
        } else {
            this.updateIgnoreNull(jbxxb);
        }

        return jbxxb;
    }

    /**
     * 保存多个对象
     * <p>存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void multipleSaveAndEdit(Jbxxb[] objs) {
        for (Jbxxb jbxxb : objs) {
            this.saveOne(jbxxb);
        }
    }

    /**
     * 分页查询未办工商列表
     */
    @Override
    public BootstrapTableModel<JbxxbVo> loadWbgsByPage(JbxxbParam jbxxbParam) {
        jbxxbParam.setJbSfybgs(Constants.JBXX_SFYBGS_N);
        jbxxbParam.setJbShzt(Integer.valueOf(Constants.JBXX_REVIEW_STATUS_9));
        BootstrapTableModel<JbxxbVo> model = new BootstrapTableModel<>();
        Set<String> visibles = organizationService.getVisibles();
        PageHelper.startPage(jbxxbParam.getPageNumber(), jbxxbParam.getLimit());
        List<JbxxbVo> jbxxbVos = jbxxbMapper.selectWbgsByPage(jbxxbParam,visibles);
        jbxxbVos.stream().forEach(item->{
            String id = item.getId();
            BusinessInfoParam param =new BusinessInfoParam();
            param.setJbxxId(id);
            ResponseEnvelope responseEnvelope = businessInfoService.selectRecallReview(param);
            item.setJbRevocationStatus(responseEnvelope.isState());
        });
        PageInfo<JbxxbVo> pageInfo = new PageInfo<>(jbxxbVos);
        model.setRows(pageInfo.getList());
        model.setTotal(pageInfo.getTotal());
        return model;
    }

    /**
     * 检查填报人组织是否为虚拟节点组织
     */
    private void checkSubmitUser(){
        SysUser submitUser = (SysUser)SpringSecurityUserTools.instance().getUser(null);
        Set<String> allVirtualNode = organizationService.getAllVirtualNode();
        if(allVirtualNode.contains(submitUser.getOrganization_id())){
            throw new RuntimeException("登记填报人所在组织必须为非虚拟节点组织!");
        }
    }

    /**
     * 保存合伙企业
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String savePartnership(FormVo form) {
        this.checkSubmitUser();
        if (Constants.RG_TYPE_ZY.equals(form.getRgType())) {
            form.setJbBdcqdjqx(null);
            form.setJbZxcqdjqx(null);
            // 只有第一次占有登记需要生成unitId,其余情况unitId不变
            if (StringUtils.isBlank(form.getJbxxId())){
                form.setUnitid(UUIDGenerator.generate());
            }else {
                form.setUnitid(jbxxbMapper.selectForList(new Jbxxb(form.getJbxxId())).get(0).getUnitid());
            }
        } else if (Constants.RG_TYPE_BD.equals(form.getRgType())) {
            //变动和注销校验unitId是否确实对应了一个未删除的企业
            SysOrganization org = organizationService.selectOrganizationById(new SysOrganization(form.getUnitid()));
            if (org == null){
                throw new RuntimeException("该变动登记对应的企业不存在!");
            }
            if (businessInfoMapper.findNoAllApprovalBus(form.getJbxxId(),form.getUnitid()) > 0){
                throw new RuntimeException("企业还存在未审核通过的在途单，不可以再次上报!");
            }
            form.setJbZycqdjqx(null);
            form.setJbZxcqdjqx(null);
        } else {
            //变动和注销校验unitId是否确实对应了一个未删除的企业
            SysOrganization org = organizationService.selectOrganizationById(new SysOrganization(form.getUnitid()));
            if (org == null){
                throw new RuntimeException("该注销登记对应的企业不存在!");
            }
            if (businessInfoMapper.findNoAllApprovalBus(form.getJbxxId(),form.getUnitid()) > 0){
                throw new RuntimeException("企业还存在未审核通过的在途单，不可以再次上报!");
            }
            form.setJbZycqdjqx(null);
            form.setJbBdcqdjqx(null);
        }
        String dataTime = String.valueOf(System.currentTimeMillis());
        if (StringUtils.isNotBlank(form.getHhCzqyId())){
            form.setJbCzrzzjgid(form.getHhCzqyId());
            SysOrganization czrzz = organizationService.getOrgByOrgId(form.getJbCzrzzjgid());
            //出资人企业长代码
            form.setHhCzqy(czrzz.getOrganization_code());
            form.setJbSsgzjgjg(czrzz.getSsgzjgjg());
            //信用代码
            String xyCode = czrzz.getOrganization_code().substring(czrzz.getOrganization_code().lastIndexOf("_") + 1);
            form.setHhCzqyCode(xyCode);
            form.setJbCzrzzjgdm(xyCode);
        }
        //可能用到的信息存一份到jbxxb中
        form.setJbGjczqy(form.getHhGjczqy());//国家出资企业信用代码
        form.setJbQymc(form.getHhCompanyName());//合伙企业名称
        //基本信息表
        Jbxxb jbxxb = new Jbxxb();
        BeanUtils.copyProperties(form, jbxxb);
        boolean djbInsert = true;
        //如果传了id,代表不是第一次保存,进行全删全增操作
        if (StringUtils.isNotBlank(form.getJbxxId())) {
            jbxxb.setId(form.getJbxxId());
//            businessInfoService.deleteByJbxxId(jbxxb.getId());
            ywzbbService.deleteByJbxxId(jbxxb.getId());
            ywzbb2Service.deleteByJbxxId(jbxxb.getId());
            hhqyService.deleteByJbxxId(jbxxb.getId());
            dwtzqkfdService.deleteByJbxxId(jbxxb.getId());
            hhrqkfdService.deleteByJbxxId(jbxxb.getId());
            djbInsert = false;
        }
/*        if (StringUtils.isBlank(jbxxb.getUnitid())) {
            String unitId = UUIDGenerator.generate();
            jbxxb.setUnitid(unitId);
        }*/
        jbxxb.setDatatime(dataTime);
        jbxxb.setFloatorder(BigDecimal.ONE);
        jbxxb.setBusinessNature(Constants.QYLX_HHQY);
        //如果传了id,代表不是第一次保存,进行全删全增操作
        if (StringUtils.isNotBlank(form.getJbxxId())) {
            dcgqkService.deletebyJbxxbId(form.getJbxxId());
        }
        this.saveOne(jbxxb);
        String id = jbxxb.getId();
        List<Dcgqk> jbDcggkList = form.getJbDcggkList();
        if (!Objects.isNull(jbDcggkList)&&!jbDcggkList.isEmpty()){
            for (Dcgqk dcgqk : jbDcggkList) {
                dcgqk.setJbxxId(id);
                dcgqkService.insert(dcgqk);
            }
        }
        //如果没有上传工商登记相关资料
        if (StringUtils.isBlank(jbxxb.getJbGsdjxgzl())){
            jbxxbMapper.updateGsdjzlToNull(id);
        }
        BusinessInfo businessInfo = new BusinessInfo();
        String id1 = jbxxb.getId();
        businessInfo.setJbxxId(id1);
        if(CollectionUtils.isEmpty(businessInfoMapper.selectForList(businessInfo))){
            //生成登记表数据-只新增不修改
            businessInfo = new BusinessInfoParam();
            businessInfo.setJbxxId(jbxxb.getId());
            businessInfo.setUnitid(jbxxb.getUnitid());
            businessInfo.setDatatime(dataTime);
            businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_1); //默认未待上报
            //登记类型 1：变动登记 0：占有登记 3：注销登记
            businessInfo.setRgType(form.getRgType());
            businessInfo.setRgDate(new Date());
            businessInfo.setAfCurrentNode("待上报");
            businessInfo.setFloatorder(BigDecimal.ONE);
            businessInfo.setRgSolutionid("1");
            businessInfo.setRgTimemark(new Date());
            businessInfoService.saveOne(businessInfo);
        }
        //Fhbzcfd
        List<Fhbzcfd> fd1TableData = form.getFd1TableData();
        if (CollectionUtils.isNotEmpty(fd1TableData)){
            for (Fhbzcfd fhbzcfd : fd1TableData) {
                fhbzcfd.setId(null);//全部都是新增
                fhbzcfd.setJbxxId(id);
                fhbzcfd.setUnitid(jbxxb.getUnitid());
                fhbzcfd.setDatatime(dataTime);
                fhbzcfd.setFloatorder(BigDecimal.ONE);
                fhbzcfdService.saveOne(fhbzcfd);
            }
        }
        //ywzbb
        Ywzbb ywzbb = new Ywzbb();
        BeanUtils.copyProperties(form, ywzbb);
        if (ywzbb != null) {
            ywzbb.setId(null);
            ywzbb.setJbxxId(id);
            ywzbb.setUnitid(jbxxb.getUnitid());
            ywzbb.setDatatime(dataTime);
            ywzbb.setFloatorder(BigDecimal.ONE);
            ywzbbService.saveOne(ywzbb);
        }
        //ywzbb2
        Ywzbb2 ywzbb2 = new Ywzbb2();
        BeanUtils.copyProperties(form, ywzbb2);
        if (ywzbb2 != null) {
            ywzbb2.setJbxxId(id);
            ywzbb2.setId(ywzbb.getId());
            //此处对象存在id,必须用insert
            ywzbb2Service.insert(ywzbb2);
        }
        //hhqy
        Hhqy hhqy = new Hhqy();
        BeanUtils.copyProperties(form, hhqy);
        if (hhqy != null) {
            hhqy.setId(null);
            hhqy.setJbxxId(id);
            hhqy.setUnitid(jbxxb.getUnitid());
            hhqyService.saveOne(hhqy);
        }
        //Dwtzqkfd
        List<Dwtzqkfd> dwtzqkfdList = form.getDwtzqkfdList();
        if (dwtzqkfdList != null && !dwtzqkfdList.isEmpty()) {
            for (Dwtzqkfd dwtzqkfd : dwtzqkfdList) {
                dwtzqkfd.setId(null);
                dwtzqkfd.setJbxxId(id);
                dwtzqkfd.setUnitid(jbxxb.getUnitid());
                dwtzqkfd.setFloatorder(1);
                dwtzqkfdService.saveOne(dwtzqkfd);
            }
        }
        //Hhrqkfd
        List<Hhrqkfd> hhrqkfdList = form.getHhrqkfdList();
        if (hhrqkfdList != null && !hhrqkfdList.isEmpty()) {
            for (Hhrqkfd hhrqkfd : hhrqkfdList) {
                hhrqkfd.setId(null);
                hhrqkfd.setJbxxId(id);
                hhrqkfd.setUnitid(jbxxb.getUnitid());
                hhrqkfdService.saveOne(hhrqkfd);
            }
        }
        //有预警id的，则吧预警置为已处理
        if(StringUtils.isNotEmpty(form.getMonitorwarnId())){
            Monitorwarn monitorwarn = new Monitorwarn();
            monitorwarn.setId(form.getMonitorwarnId());
            monitorwarn.setChangeStatus(Constants.BDZT_1);
            monitorwarnService.updateIgnoreNull(monitorwarn);
        }
        return id;
    }

    /**
     * 国有资本保存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveGovernmentCapital(FormVo form) {
        this.checkSubmitUser();
        if (Constants.RG_TYPE_ZY.equals(form.getRgType())) {
            form.setJbBdcqdjqx(null);
            form.setJbZxcqdjqx(null);
            // 只有第一次占有登记需要生成unitId,其余情况unitId不变
            if (StringUtils.isBlank(form.getJbxxId())){
                form.setUnitid(UUIDGenerator.generate());
            } else {
                form.setUnitid(jbxxbMapper.selectForList(new Jbxxb(form.getJbxxId())).get(0).getUnitid());
            }
        } else if (Constants.RG_TYPE_BD.equals(form.getRgType())) {
            //变动和注销校验unitId是否确实对应了一个未删除的企业
            SysOrganization org = organizationService.selectOrganizationById(new SysOrganization(form.getUnitid()));
            if (org == null){
                throw new RuntimeException("该变动登记对应的企业不存在!");
            }
            if (businessInfoMapper.findNoAllApprovalBus(form.getJbxxId(),form.getUnitid()) > 0){
                throw new RuntimeException("企业还存在未审核通过的在途单，不可以再次上报!");
            }
            form.setJbZycqdjqx(null);
            form.setJbZxcqdjqx(null);
        } else {
            //变动和注销校验unitId是否确实对应了一个未删除的企业
            SysOrganization org = organizationService.selectOrganizationById(new SysOrganization(form.getUnitid()));
            if (org == null){
                throw new RuntimeException("该注销登记对应的企业不存在!");
            }
            if (businessInfoMapper.findNoAllApprovalBus(form.getJbxxId(),form.getUnitid()) > 0){
                throw new RuntimeException("企业还存在未审核通过的在途单，不可以再次上报!");
            }
            form.setJbZycqdjqx(null);
            form.setJbBdcqdjqx(null);
        }
        //基本信息表
        Jbxxb jbxxb = new Jbxxb();
        if (StringUtils.isNotBlank(form.getJbCzrzzjgid())){
            SysOrganization czrzz = organizationService.getOrgByOrgId(form.getJbCzrzzjgid());
            String xyCode = czrzz.getOrganization_code().substring(czrzz.getOrganization_code().lastIndexOf("_") + 1);
            form.setJbCzrzzjgdm(xyCode);
            form.setJbSsgzjgjg(czrzz.getSsgzjgjg());
        }
        boolean djbInsert = true;
        BeanUtils.copyProperties(form, jbxxb);
        //如果传了id,代表不是第一次保存,进行全删全增操作
        if (StringUtils.isNotBlank(form.getJbxxId())) {
            jbxxb.setId(form.getJbxxId());
//            businessInfoService.deleteByJbxxId(jbxxb.getId());
            fhbzcfdService.deleteByJbxxId(jbxxb.getId());
            czfdService.deleteByJbxxId(jbxxb.getId());
            ygqcyffdService.deleteByJbxxId(jbxxb.getId());
            hrhcfdService.deleteByJbxxId(jbxxb.getId());
            zrsrfdService.deleteByJbxxId(jbxxb.getId());
            cgrfdService.deleteByJbxxId(jbxxb.getId());
            syccfpfdService.deleteByJbxxId(jbxxb.getId());
            ywzbbService.deleteByJbxxId(jbxxb.getId());
            ywzbb2Service.deleteByJbxxId(jbxxb.getId());
            xgzjgfdService.deleteByJbxxId(jbxxb.getId());
            cjffdService.deleteByJbxxId(jbxxb.getId());
            djbInsert = false;
        }
/*        if (StringUtils.isBlank(jbxxb.getUnitid())) {
            String unitId = UUIDGenerator.generate();
            jbxxb.setUnitid(unitId);
        }*/
        String dataTime = String.valueOf(System.currentTimeMillis());
        jbxxb.setDatatime(dataTime);
        jbxxb.setFloatorder(BigDecimal.ONE);
        jbxxb.setBusinessNature(Constants.QYLX_GYQY);
        if (StringUtils.isNotBlank(form.getJbxxId())) {
            dcgqkService.deletebyJbxxbId(form.getJbxxId());
        }
        this.saveOne(jbxxb);
        String id = jbxxb.getId();
        List<Dcgqk> jbDcggkList = form.getJbDcggkList();
        if (!Objects.isNull(jbDcggkList)&&!jbDcggkList.isEmpty()){
            for (Dcgqk dcgqk : jbDcggkList) {
                dcgqk.setJbxxId(id);
                dcgqkService.insert(dcgqk);
            }
        }

        //如果没有上传工商登记相关资料
        if (StringUtils.isBlank(jbxxb.getJbGsdjxgzl())){
            jbxxbMapper.updateGsdjzlToNull(id);
        }
        BusinessInfo businessInfo = new BusinessInfo();
        String id1 = jbxxb.getId();
        businessInfo.setJbxxId(id1);
        if(CollectionUtils.isEmpty(businessInfoMapper.selectForList(businessInfo))){
            //生成登记表数据-只生成不修改
            businessInfo = new BusinessInfoParam();
            businessInfo.setJbxxId(jbxxb.getId());
            businessInfo.setUnitid(jbxxb.getUnitid());
            businessInfo.setDatatime(dataTime);
            businessInfo.setRgUnitstate(Constants.REVIEW_STATUS_1); //默认未待上报
            //登记类型 1：变动登记 0：占有登记 3：注销登记
            businessInfo.setRgType(form.getRgType());
            businessInfo.setRgDate(new Date());
            businessInfo.setAfCurrentNode("待上报");
            businessInfo.setFloatorder(BigDecimal.ONE);
            businessInfo.setRgSolutionid("1");
            businessInfo.setRgTimemark(new Date());
            businessInfoService.saveOne(businessInfo);
        }
        //Fhbzcfd
        List<Fhbzcfd> fd1TableData = form.getFd1TableData();
        if (fd1TableData != null) {
            for (Fhbzcfd fhbzcfd : fd1TableData) {
                fhbzcfd.setId(null);//全部都是新增
                fhbzcfd.setJbxxId(id);
                fhbzcfd.setUnitid(jbxxb.getUnitid());
                fhbzcfd.setDatatime(dataTime);
                fhbzcfd.setFloatorder(BigDecimal.ONE);
                fhbzcfdService.saveOne(fhbzcfd);
            }
        }
        // Czfd
        List<Czfd> fdTableData = form.getFdTableData();
        if (fdTableData != null) {
            for (Czfd czfd : fdTableData) {
                czfd.setId(null);//全部都是新增
                czfd.setJbxxId(id);
                czfd.setUnitid(jbxxb.getUnitid());
                czfd.setDatatime(dataTime);
                czfd.setFloatorder(BigDecimal.ONE);
                czfdService.saveOne(czfd);
            }
        }
        //Ygqcyffd
        List<Ygqcyffd> fd2TableData = form.getFd2TableData();
        if (fd2TableData != null) {
            for (Ygqcyffd ygqcyffd : fd2TableData) {
                ygqcyffd.setId(null);//全部都是新增
                ygqcyffd.setJbxxId(id);
                ygqcyffd.setUnitid(jbxxb.getUnitid());
                ygqcyffd.setDatatime(dataTime);
                ygqcyffd.setFloatorder(BigDecimal.ONE);
                ygqcyffdService.saveOne(ygqcyffd);
            }
        }
        //Hrhcfd
        List<Hrhcfd> fd7TableData = form.getFd7TableData();
        if (fd7TableData != null) {
            for (Hrhcfd hrhcfd : fd7TableData) {
                hrhcfd.setId(null);//全部都是新增
                hrhcfd.setJbxxId(id);
                hrhcfd.setUnitid(jbxxb.getUnitid());
                hrhcfd.setDatatime(dataTime);
                hrhcfd.setFloatorder(BigDecimal.ONE);
                hrhcfdService.saveOne(hrhcfd);
            }
        }
        //Zrsrfd
        List<Zrsrfd> fd8TableData = form.getFd8TableData();
        if (fd8TableData != null) {
            for (Zrsrfd zrsrfd : fd8TableData) {
                zrsrfd.setId(null);//全部都是新增
                zrsrfd.setJbxxId(id);
                zrsrfd.setUnitid(jbxxb.getUnitid());
                zrsrfd.setDatatime(dataTime);
                zrsrfd.setFloatorder(BigDecimal.ONE);
                zrsrfdService.saveOne(zrsrfd);
            }
        }
        //Cgrfd
        List<Cgrfd> fd9TableData = form.getFd9TableData();
        if (fd9TableData != null) {
            for (Cgrfd cgrfd : fd9TableData) {
                cgrfd.setId(null);
                cgrfd.setJbxxId(id);
                cgrfd.setUnitid(jbxxb.getUnitid());
                cgrfd.setDatatime(dataTime);
                cgrfd.setFloatorder(BigDecimal.ONE);
                cgrfdService.saveOne(cgrfd);
            }
        }
        //Syccfpfd
        List<Syccfpfd> fd6TableData = form.getFd6TableData();
        if (fd6TableData != null) {
            for (Syccfpfd syccfpfd : fd6TableData) {
                syccfpfd.setId(null);
                syccfpfd.setJbxxId(id);
                syccfpfd.setUnitid(jbxxb.getUnitid());
                syccfpfd.setDatatime(dataTime);
                syccfpfd.setFloatorder(BigDecimal.ONE);
                syccfpfdService.saveOne(syccfpfd);
            }
        }
        //YWZBB
        Ywzbb ywzbb = new Ywzbb();
        BeanUtils.copyProperties(form, ywzbb);
        if (ywzbb != null) {
            ywzbb.setId(null);
            ywzbb.setJbxxId(id);
            ywzbb.setUnitid(jbxxb.getUnitid());
            ywzbb.setDatatime(dataTime);
            ywzbb.setFloatorder(BigDecimal.ONE);
            ywzbbService.saveOne(ywzbb);
        }
        //Ywzzb-2
        Ywzbb2 ywzbb2 = new Ywzbb2();
        BeanUtils.copyProperties(form, ywzbb2);
        if (ywzbb2 != null) {
            ywzbb2.setJbxxId(id);
            ywzbb2.setId(ywzbb.getId());
            //此处对象存在id,必须用insert
            ywzbb2Service.insert(ywzbb2);
        }
        //Xgzjgfd
        List<Xgzjgfd> fd3TableData = form.getFd3TableData();
        if (fd3TableData != null && !fd3TableData.isEmpty()) {
            for (Xgzjgfd xgzj : fd3TableData) {
                xgzj.setId(null);
                xgzj.setUnitid(jbxxb.getUnitid());
                xgzj.setDatatime(dataTime);
                xgzj.setFloatorder(BigDecimal.ONE);
                xgzj.setJbxxId(id);
                xgzjgfdService.insert(xgzj);
            }
        }
        //cq_cjffd
        Cjffd cjffd = new Cjffd();
        BeanUtils.copyProperties(form, cjffd);
        if (cjffd != null && StringUtils.isNotEmpty(cjffd.getFd5Cjfmc())) {
            cjffd.setJbxxId(id);
            cjffd.setId(ywzbb.getId());
            cjffd.setUnitid(jbxxb.getUnitid());
            cjffd.setDatatime(dataTime);
            cjffd.setFloatorder(BigDecimal.ONE);
            cjffdService.insert(cjffd);//带id的只能用insert
        }
        if(CollectionUtils.isNotEmpty(form.getFd5TableData())){
            for(CjffdVo cjffdVo : form.getFd5TableData()){
                cjffdVo.setJbxxId(id);
                cjffdVo.setId(ywzbb.getId());
                cjffdVo.setUnitid(jbxxb.getUnitid());
                cjffdVo.setDatatime(dataTime);
                cjffdVo.setFloatorder(BigDecimal.ONE);
                cjffdService.insert(cjffdVo);//带id的只能用insert
            }
        }
        //有预警id的，则吧预警置为已处理
        if(StringUtils.isNotEmpty(form.getMonitorwarnId())){
            Monitorwarn monitorwarn = new Monitorwarn();
            monitorwarn.setId(form.getMonitorwarnId());
            monitorwarn.setChangeStatus(Constants.BDZT_1);
            monitorwarnService.updateIgnoreNull(monitorwarn);
        }
        return id;
    }

    /**
     * 判断企业是否存在办理中业务
     */
    @Override
    public boolean hasProcessing(Jbxxb jbxxb) {
        jbxxb.setJbShzt(Integer.valueOf(Constants.JBXX_REVIEW_STATUS_2));//审核通过
        return jbxxbMapper.countUnapprovedByUnitId(jbxxb) == 0;
        // return true;
    }

    /**
     * 根据组织id获取其最新的状态数据
     */
    @Override
    public JbxxbVo loadRecentApprovedByUnitId(Jbxxb jbxxb) {
        if (StringUtils.isNotBlank(jbxxb.getId())){
            JbxxbVo vo = jbxxbMapper.selectJbxxbByPrimaryKey(jbxxb);
            if (vo == null){
                return null;
            }
            jbxxb.setUnitid(vo.getUnitid());
        }
        JbxxbVo jbxxbVo = jbxxbMapper.loadRecentApprovedByUnitId(jbxxb);
        Map<String,String> s = jbxxbMapper.loadRecentApprovedByUnitIdNew(jbxxb);
        if (Objects.nonNull(s)){
            String jbZyhyList = s.get("jbZyhyList");
            List<String> collect;

            if (StringUtils.isNotBlank(jbZyhyList)) {
                collect = Arrays.stream(jbZyhyList.split(",")).collect(Collectors.toList());
                jbxxbVo.setJbZyhyList(collect);
            }
            jbZyhyList = s.get("jbZyhyList2");
            if (StringUtils.isNotBlank(jbZyhyList)) {

                collect = Arrays.stream(jbZyhyList.split(",")).collect(Collectors.toList());

                jbxxbVo.setJbZyhyList2(collect);
            }
            jbZyhyList = s.get("jbZyhyList3");
            if (StringUtils.isNotBlank(jbZyhyList)) {

                collect = Arrays.stream(jbZyhyList.split(",")).collect(Collectors.toList());
                jbxxbVo.setJbZyhyList3(collect);
            }
            jbZyhyList = s.get("jbZyhyList1");
            if (StringUtils.isNotBlank(jbZyhyList)) {
                collect = Arrays.stream(jbZyhyList.split(",")).collect(Collectors.toList());
                jbxxbVo.setJbZyhyList1(collect);
            }
        }
        if (jbxxbVo != null) {
            //SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
            //jbxxbVo.setJbCzrzzjgStr(sysOrganization == null?"":sysOrganization.getOrganization_code()+sysOrganization.getOrganization_name());
            jbxxbVo.setJbCzrzzjgStr(jbxxbVo.getJbCzrzzjgdm());
            jbxxbVo.setFd9TableData(cgrfdMapper.selectByJbxxId(jbxxbVo.getId()));
            List<CzfdVo> czfdVos = czfdMapper.selectByJbxxId(jbxxbVo.getId());
            jbxxbVo.setFdTableData(czfdVos);
            jbxxbVo.setYwzbbVo(ywzbbMapper.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setYwzbb2Vo(ywzbb2Service.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setFd6TableData(syccfpfdService.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setFd1TableData(fhbzcfdService.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setFd3TableData(xgzjgfdService.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setFd8TableData(zrsrfdService.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setFd7TableData(hrhcfdService.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setFd2TableData(ygqcyffdService.selectByJbxxId(jbxxbVo.getId()));
            jbxxbVo.setFd5TableData(cjffdService.selectByJbxxId(jbxxbVo.getId()));
            if (StringUtils.isNotBlank(jbxxbVo.getJbGsdjxgzl())){
                List<Attachment> attachments = attachmentService.selectForList(new Attachment(jbxxbVo.getJbGsdjxgzl()));
                Attachment attachment = CollectionUtils.isEmpty(attachments) ? null : attachments.get(0);
                jbxxbVo.setGsAttachment(attachment);
            }
            //合伙企业
            HhqyVo hhqyVo = hhqyService.selectByJbxxbId(jbxxbVo.getId());
            if (Objects.nonNull(hhqyVo)) {
                String hhCzqyStr = hhqyVo.getHhCzqyStr();
                if (StringUtils.isNotBlank(hhCzqyStr)) {
                    String[] s2 = hhCzqyStr.split(" ");
                    String s1;
                    if (s2.length>1){
                        s1 = s2[1];
                    }else {
                        s1= s2[0];
                    }

                    hhqyVo.setHhCzqyStr(s1);
                }
            }
            String jbGjczqy = jbxxbVo.getJbGjczqy();
            if (StringUtils.isNotBlank(jbGjczqy)){
                String[] s2 = jbGjczqy.split("_");
                String jbGjczqyCode;
                if (s2.length>1){
                    jbGjczqyCode = s2[1];
                }else {
                    jbGjczqyCode= s2[0];
                }
                jbxxbVo.setJbGjczqyCode(jbGjczqyCode);
            }
            jbxxbVo.setHhqyVo(hhqyVo);
            jbxxbVo.setHhrqkfdList(hhrqkfdService.selectByJbxxbId(jbxxbVo.getId()));
            jbxxbVo.setDwtzqkfdList(dwtzqkfdService.selectByJbxxbId(jbxxbVo.getId()));
            this.dataJbxxbVo(jbxxbVo);
            List<DcgqkVo> collect = dcgqkService.loadByJbxxbId(new DcgqkParam() {{
                jbxxId = jbxxbVo.getId();
            }}).getRows().stream().collect(Collectors.toList());
            for (DcgqkVo dcgqkVo : collect) {
                this.dataDcgqkVo(dcgqkVo);
            }
            jbxxbVo.setJbDcggkList(collect);
            //当变动或者注销时，企业设立登记状态写死为产权工商均已设立登记
            if(Constants.RG_TYPE_BD.equals(jbxxbVo.getRgType()) || Constants.RG_TYPE_ZX.equals(jbxxbVo.getRgType())){
                jbxxbVo.setJbQyslzt(Constants.GSDJYBLZT);
            }
        }
        return jbxxbVo;
    }
    private void dataJbxxbVo(JbxxbVo jbxxbVo){
        String jbSfss = jbxxbVo.getJbSfss();
        String sfss = dictCacheStrategy.getTextByVal("SFSS", jbSfss);
        jbxxbVo.setJbSfssStr(sfss);
         String jbSfbbStr = dictCacheStrategy.getTextByVal("SFSS", jbxxbVo.getJbSfbb());
         jbxxbVo.setJbSfbbStr(jbSfbbStr);
         String jbSftgqyStr = dictCacheStrategy.getTextByVal("SFSS", jbxxbVo.getJbSftgqy());
         jbxxbVo.setJbSftgqyStr(jbSftgqyStr);
         String jbSfczblztStr = dictCacheStrategy.getTextByVal("SFSS", jbxxbVo.getJbSfczblzt());
         jbxxbVo.setJbSfczblztStr(jbSfczblztStr);
         String jbSfkzgsStr = dictCacheStrategy.getTextByVal("SFSS", jbxxbVo.getJbSfkzgs());
         jbxxbVo.setJbSfkzgsStr(jbSfkzgsStr);
         String jbQygljcStr = dictCacheStrategy.getTextByVal("QYGLJC", jbxxbVo.getJbQygljc());
         jbxxbVo.setJbQygljcStr(jbQygljcStr);
         String jbQzwjkjglxStr = dictCacheStrategy.getTextByVal("GZWJKJGLX", jbxxbVo.getJbQzwjkjglx());
         jbxxbVo.setJbQzwjkjglxStr(jbQzwjkjglxStr);
         String jbGzwjkjgmx = dictCacheStrategy.getTextByVal("GZJGJG", jbxxbVo.getJbGzwjkjgmx());
         jbxxbVo.setJbGzwjkjgmxStr(jbGzwjkjgmx);
        String jbRjzbbz = dictCacheStrategy.getTextByVal("BZXZ",  jbxxbVo.getJbRjzbbz());
        jbxxbVo.setJbRjzbbzStr(jbRjzbbz);
        List<String> jbZyhyList1 = jbxxbVo.getJbZyhyList1();
        if (!Objects.isNull(jbZyhyList1)&&!jbZyhyList1.isEmpty()){
            String collect = jbZyhyList1.stream().collect(Collectors.joining(","));
            jbxxbVo.setJbZyhy1Str(collect);
        }
        jbZyhyList1 = jbxxbVo.getJbZyhyList2();
        if (!Objects.isNull(jbZyhyList1)&&!jbZyhyList1.isEmpty()){
            String collect = jbZyhyList1.stream().collect(Collectors.joining(","));
            jbxxbVo.setJbZyhy2Str(collect);
        }
        jbZyhyList1 = jbxxbVo.getJbZyhyList3();
        if (!Objects.isNull(jbZyhyList1)&&!jbZyhyList1.isEmpty()){
            String collect = jbZyhyList1.stream().collect(Collectors.joining(","));
            jbxxbVo.setJbZyhy3Str(collect);
        }

    }
    private void dataDcgqkVo(DcgqkVo dcgqkVo){
         String reason = dcgqkVo.getReason();// 设立原因
        String reasonStr = dictCacheStrategy.getTextByVal("REASON", reason);
        dcgqkVo.setReasonStr(reasonStr);
         String measures = dcgqkVo.getMeasures();// 保全措施
        String measuresStr = dictCacheStrategy.getTextByVal("MEASURES", measures);
        String czrArea = dcgqkVo.getCzrArea();
        String czrAreaStr = dictCacheStrategy.getTextByVal("JNJW", czrArea);
        dcgqkVo.setMeasuresStr(measuresStr);
        dcgqkVo.setCzrAreaStr(czrAreaStr);
    }
    /**
     * 根据组织id获取其最新的状态数据(不含信息采集和合规资料数据)
     */
    @Override
    public JbxxbVo loadRecentJbxxOnly(Jbxxb jbxxb) {
        JbxxbVo jbxxbVo = jbxxbMapper.loadRecentApprovedByUnitId(jbxxb);
        if (jbxxbVo != null) {
            //SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
            //jbxxbVo.setJbCzrzzjgStr(sysOrganization == null?"":sysOrganization.getOrganization_code()+sysOrganization.getOrganization_name());
            jbxxbVo.setJbCzrzzjgStr(jbxxbVo.getJbCzrzzjgdm());
            jbxxbVo.setFd9TableData(cgrfdMapper.selectByJbxxId(jbxxbVo.getId()));
            List<CzfdVo> czfdVos = czfdMapper.selectByJbxxId(jbxxbVo.getId());
            jbxxbVo.setFdTableData(czfdVos);
            //其他非基本信息的列表全部设为空集合
            jbxxbVo.setFd6TableData(Collections.EMPTY_LIST);
            jbxxbVo.setFd1TableData(Collections.EMPTY_LIST);
            jbxxbVo.setFd3TableData(Collections.EMPTY_LIST);
            jbxxbVo.setFd8TableData(Collections.EMPTY_LIST);
            jbxxbVo.setFd7TableData(Collections.EMPTY_LIST);
            jbxxbVo.setFd2TableData(Collections.EMPTY_LIST);
            jbxxbVo.setFd5TableData(Collections.EMPTY_LIST);
            if (StringUtils.isNotBlank(jbxxbVo.getJbGsdjxgzl())){
                List<Attachment> attachments = attachmentService.selectForList(new Attachment(jbxxbVo.getJbGsdjxgzl()));
                Attachment attachment = CollectionUtils.isEmpty(attachments) ? null : attachments.get(0);
                jbxxbVo.setGsAttachment(attachment);
            }
            //合伙企业
            HhqyVo hhqyVo = hhqyService.selectByJbxxbId(jbxxbVo.getId());
            if (!Objects.isNull(hhqyVo)) {
                String hhCzqyStr = hhqyVo.getHhCzqyStr();
                if (StringUtils.isNotBlank(hhCzqyStr)) {
                    String[] s1 = hhCzqyStr.split(" ");
                    String s;
                    if (s1.length > 1){
                         s = s1[1];
                    }else {
                        s=s1[0];
                    }
                    hhqyVo.setHhCzqyStr(s);
                }
            }
            String jbGjczqy = jbxxbVo.getJbGjczqy();
            if (StringUtils.isNotBlank(jbGjczqy)){
                String[] s1 = jbGjczqy.split("_");
                String s;
                if (s1.length > 1){
                    s = s1[1];
                }else {
                    s=s1[0];
                }
                jbxxbVo.setJbGjczqyCode(s);
            }
            jbxxbVo.setHhqyVo(hhqyVo);
            jbxxbVo.setHhrqkfdList(hhrqkfdService.selectByJbxxbId(jbxxbVo.getId()));
            jbxxbVo.setDwtzqkfdList(dwtzqkfdService.selectByJbxxbId(jbxxbVo.getId()));
            //代持股情况浮动表
            BootstrapTableModel<DcgqkVo> voBootstrapTableModel = dcgqkService.loadByJbxxbId(new
                                                                                                    DcgqkParam() {{
                                                                                                        jbxxId = jbxxbVo.getId();
                                                                                                    }});
            List<DcgqkVo> dcgqkVos = voBootstrapTableModel.getRows().stream().collect(Collectors.toList());
            for (DcgqkVo dcgqkVo : dcgqkVos) {
                this.dataDcgqkVo(dcgqkVo);
            }
            this.dataJbxxbVo(jbxxbVo);
            jbxxbVo.setJbDcggkList(dcgqkVos);

            //当变动或者注销时，企业设立登记状态写死为产权工商均已设立登记
            jbxxbVo.setJbQyslzt(Constants.GSDJYBLZT);
        }
        return jbxxbVo;
    }

    /**
     * 是否国家出资企业主业(有交集为是,1:是;2:否)
     */
    @Override
    public byte isMainBusiness(Jbxxb jbxxb) {
        String[] split = jbxxb.getJbZyhy().split(",");
        //当前待占有企业选中的所属行业
        List<String> list = Arrays.asList(split);
        jbxxb.setJbZyhy(null);
        if(StringUtils.isEmpty(jbxxb.getJbGjczqy())){
            return 2;
        }
        SysOrganization org = organizationService.getOrgByCode(jbxxb.getJbGjczqy());
        if (org != null) {
            jbxxb.setJbGjczqy(null);
            jbxxb.setUnitid(org.getOrganization_id());
            JbxxbVo recentVo = this.loadRecentApprovedByUnitId(jbxxb);
            if (recentVo != null){
                String jbZyhy = recentVo.getJbZyhy();
                if (StringUtils.isNotBlank(jbZyhy)) {
                    String[] split1 = jbZyhy.split(",");
                    List<String> list1 = Arrays.asList(split1);
                    return (byte) (Collections.disjoint(list, list1) ? 2 : 1);
                }
            }
        }
        return 2;
    }

    /**
     * 比较当前填报中的企业的基本信息与其最新审核通过的基本信息的不同
     */
    @Override
    public Map<String, Object> jbxxCompare(Jbxxb jbxxb) throws IllegalAccessException {
        Map<String, Object> map = null;
        JbxxbVo nowVo = this.selectJbxxbByPrimaryKey(jbxxb);
        jbxxb.setUnitid(nowVo.getUnitid());
        JbxxbVo pastVo = this.loadRecentApprovedByUnitId(jbxxb);
        if (pastVo == null) {
            return null;
        }
        //不参与比较的字段
        String[] ignoreFields = {
                "jbxxbList", "fd9TableData", "fd1TableData",
                "fd3TableData", "fd8TableData", "fd7TableData", "fd2TableData","fd5TableData",
                "fd6TableData", "ywzbbVo", "ywzbb2Vo", "hhrqkfdList", "dwtzqkfdList",
                "createUserStr", "lastUpdateUserStr", "rgType", "datatime", "floatorder",
                "createUser", "createTime", "lastUpdateUser", "lastUpdateTime", "id",
                "jbDylsh", "jbSshy", "jbShzt","hhqyVo"};
        Map<String, Map<String, Object>> fieldsMap = com.boot.IAdmin.common.utils.BeanUtils.fieldsCompare(pastVo, nowVo, ignoreFields);
        map = new HashMap<>(fieldsMap.size());
        for (Map.Entry<String,Map<String,Object>> entry : fieldsMap.entrySet()){
            map.put(entry.getKey(),entry.getValue().get("past"));
        }
        fieldsMap = com.boot.IAdmin.common.utils.BeanUtils.fieldsCompare(pastVo.getHhqyVo(), nowVo.getHhqyVo(), ignoreFields);
        Map<String, Object> hhqyVo = new HashMap<>();
        for (Map.Entry<String,Map<String,Object>> entry : fieldsMap.entrySet()){
            hhqyVo.put(entry.getKey(),entry.getValue().get("past"));
        }
        map.put("hhqyVo",hhqyVo);
        //当前的出资人列表
        List<CzfdVo> nowCzfd = nowVo.getFdTableData();
        //最新审核通过的出资人浮动列表
        List<CzfdVo> pastCzfd = pastVo.getFdTableData();
        //当前版本较上版本变化
        map.put("fdTableData", getCzfdChange(pastCzfd, nowCzfd));
        //上版本较当前版本变化
        map.put("fdTableData1", getCzfdChange(nowCzfd, pastCzfd));
        //当前的持股人浮动列表
        List<CgrfdVo> nowCgr = nowVo.getFd9TableData();
        //最新审核通过的的持股人浮动列表
        List<CgrfdVo> pastCgr = pastVo.getFd9TableData();
        map.put("fd9TableData", getCgrfdChange(pastCgr, nowCgr));
        map.put("fd9TableData1", getCgrfdChange(nowCgr, pastCgr));
        //当前合伙人浮动
        List<HhrqkfdVo> nowHhr = nowVo.getHhrqkfdList();
        //最新审核通过的的合伙人浮动列表
        List<HhrqkfdVo> pastHhr = pastVo.getHhrqkfdList();
        map.put("hhrqkfdList", getHhrFdChange(pastHhr, nowHhr));
        map.put("hhrqkfdList1", getHhrFdChange(nowHhr, pastHhr));
        //当前投资情况
        List<DwtzqkfdVo> nowTz = nowVo.getDwtzqkfdList();
        //最新审核通过的投资情况
        List<DwtzqkfdVo> pastTz = pastVo.getDwtzqkfdList();
        map.put("dwtzqkfdList", getDwtzFdChange(pastTz, nowTz));
        map.put("dwtzqkfdList1", getDwtzFdChange(nowTz, pastTz));
        //当前的
        List<DcgqkVo> nowDcgqk = nowVo.getJbDcggkList();

        //上期的
        List<DcgqkVo> pastDcgqk = pastVo.getJbDcggkList();
        map.put("jbDcggkList",  getDcgqkChange(pastDcgqk, nowDcgqk));
        map.put("jbDcggkList1",  getDcgqkChange(nowDcgqk, pastDcgqk));

        return map;
    }

    /**
     * 是否境外转投境内企业(1:是;2:否)
     */
    @Override
    public byte isJwToJn(Jbxxb jbxxb) {
        //当前企业为境内且上级企业为境外则为是
        if (Constants.JNJW_1.equals(jbxxb.getJbJnjw())) {
            Jbxxb j = new Jbxxb();
            j.setUnitid(jbxxb.getJbCzrzzjgid());
            JbxxbVo jbxxbVo = this.loadRecentApprovedByUnitId(j);
            if (jbxxbVo != null){
                return (byte) (Constants.JNJW_2.equals(jbxxbVo.getJbJnjw()) ? 1 : 2);
            }
        }
        return 2;
    }

    /**
     * 获取出资浮动变化
     */
    private Map<Integer, Byte[]> getCzfdChange(List<CzfdVo> pastCzfd, List<CzfdVo> nowCzfd) {
        final Byte[] allOne = {1, 1, 1, 1, 1, 1, 1, 1, 1,1,1,1,1};
        if (CollectionUtils.isNotEmpty(nowCzfd)) {
            //如果以前无,现在有
            if (CollectionUtils.isEmpty(pastCzfd)) {
                Map<Integer, Byte[]> map2 = new HashMap<>(nowCzfd.size());
                for (int i = 0; i < nowCzfd.size(); i++) {
                    map2.put(i, allOne);
                }
                return map2;
                //以前有,现在也有
            } else {
                Map<Integer, Byte[]> arrayMap = new HashMap<>();
                for (int i = 0; i < nowCzfd.size(); i++) {
                    int count = 0;
                    for (CzfdVo czfdVo : pastCzfd) {
                        if (Objects.equals(nowCzfd.get(i).getFdCzrzzjgdm(),czfdVo.getFdCzrzzjgdm())) {
                            count++;
                            Byte[] array = new Byte[13];
                            array[0] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdCzrmc(),czfdVo.getFdCzrmc());
                            array[1] = 0;
                            array[2] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdCzrlb(),czfdVo.getFdCzrlb());
                            array[3] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdCzebz(),czfdVo.getFdCzebz());
                            array[4] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdCze(),czfdVo.getFdCze());
                            array[5] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdSjzczbbz(),czfdVo.getFdSjzczbbz());
                            array[6] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdSjzcj(),czfdVo.getFdSjzcj());
                            array[7] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdGqbl(),czfdVo.getFdGqbl());
                            array[8] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdRjzbbz(),czfdVo.getFdRjzbbz());
                            array[9] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdRjzb(),czfdVo.getFdRjzb());
                            array[10] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdGqbl(),czfdVo.getFdGqbl());
                            array[11] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdCzfs(),czfdVo.getFdCzfs());
                            array[12] = strOrDecimalFieldCompare(nowCzfd.get(i).getFdJfqx(),czfdVo.getFdJfqx());
                            arrayMap.put(i, array);
                            break;
                        }
                    }
                    if (count == 0) {
                        arrayMap.put(i, allOne);
                    }
                }
                return arrayMap;
            }
        }
        return null;
    }

    /**
     * 获取持股人浮动前后变化
     */
    private Map<Integer, Byte[]> getCgrfdChange(List<CgrfdVo> past, List<CgrfdVo> now) {
        final Byte[] allOne = {1, 1, 1};
        if (CollectionUtils.isNotEmpty(now)) {
            //如果以前无,现在有
            if (CollectionUtils.isEmpty(past)) {
                Map<Integer, Byte[]> map2 = new HashMap<>(now.size());
                for (int i = 0; i < now.size(); i++) {
                    map2.put(i, allOne);
                }
                return map2;
                //以前有,现在也有
            } else {
                Map<Integer, Byte[]> arrayMap = new HashMap<>();
                for (int i = 0; i < now.size(); i++) {
                    int count = 0;
                    for (CgrfdVo vo : past) {
                        if (Objects.equals(now.get(i).getFd9Cgrmc(),vo.getFd9Cgrmc())) {
                            Byte[] array = new Byte[3];
                            count++;
                            array[0] = 0;
                            array[1] = strOrDecimalFieldCompare(now.get(i).getFd9Sjczr(),vo.getFd9Sjczr());
                            array[2] = strOrDecimalFieldCompare(now.get(i).getFd9Info(),vo.getFd9Info());
                            arrayMap.put(i, array);
                            break;
                        }
                    }
                    if (count == 0) {
                        arrayMap.put(i, allOne);
                    }
                }
                return arrayMap;
            }
        }
        return null;
    }

    /**
     * (合伙企业)合伙人情况浮动前后变化
     */
    private Map<Integer, Byte[]> getHhrFdChange(List<HhrqkfdVo> past, List<HhrqkfdVo> now) {
        final Byte[] allOne = {1, 1, 1, 1, 1, 1, 1, 1, 1};
        if (CollectionUtils.isNotEmpty(now)) {
            //如果以前无,现在有
            if (CollectionUtils.isEmpty(past)) {
                Map<Integer, Byte[]> map2 = new HashMap<>(now.size());
                for (int i = 0; i < now.size(); i++) {
                    map2.put(i, allOne);
                }
                return map2;
                //以前有,现在也有
            } else {
                Map<Integer, Byte[]> arrayMap = new HashMap<>();
                for (int i = 0; i < now.size(); i++) {
                    int count = 0;
                    for (HhrqkfdVo vo : past) {
                        if (Objects.equals(now.get(i).getHhrCode(),vo.getHhrCode())) {
                            Byte[] array = new Byte[9];
                            count++;
                            array[0] = strOrDecimalFieldCompare(now.get(i).getName(),vo.getName());
                            array[1] = 0;
                            array[2] = strOrDecimalFieldCompare(now.get(i).getType(),vo.getType());
                            array[3] = strOrDecimalFieldCompare(now.get(i).getCategory(),vo.getCategory());
                            array[4] = strOrDecimalFieldCompare(now.get(i).getRjcze(),vo.getRjcze());
                            array[5] = strOrDecimalFieldCompare(now.get(i).getRjczbl(),vo.getRjczbl());
                            array[6] = strOrDecimalFieldCompare(now.get(i).getSjcze(),vo.getSjcze());
                            array[7] = strOrDecimalFieldCompare(now.get(i).getCzfs(),vo.getCzfs());
                            array[8] = strOrDecimalFieldCompare(now.get(i).getJfqx(),vo.getJfqx());
                            arrayMap.put(i, array);
                            break;
                        }
                    }
                    if (count == 0) {
                        arrayMap.put(i, allOne);
                    }
                }
                return arrayMap;
            }
        }
        return null;
    }

    /**
     * 获取浮动
     * @param past
     * @param now
     * @return
     */
 private Map<Integer, Byte[]> getDcgqkChange( List<DcgqkVo> past, List<DcgqkVo> now) {
     final Byte[] allOne = {1, 1, 1, 1, 1, 1, 1, 1};
     if (CollectionUtils.isNotEmpty(now)) {
         //如果以前无,现在有
         if (CollectionUtils.isEmpty(past)) {
             Map<Integer, Byte[]> map2 = new HashMap<>(now.size());
             for (int i = 0; i < now.size(); i++) {
                 map2.put(i, allOne);
             }
             return map2;
             //以前有,现在也有
         } else {
             Map<Integer, Byte[]> arrayMap = new HashMap<>();
             for (int i = 0; i < now.size(); i++) {
                 int count = 0;
                 for (DcgqkVo vo : past) {
                     if (Objects.equals(now.get(i).getCompanyName(),vo.getCompanyName())) {
                         Byte[] array = new Byte[8];
                         count++;
                         array[0] = strOrDecimalFieldCompare(now.get(i).getCompanyName(),vo.getCompanyName());
                         array[1] = strOrDecimalFieldCompare(now.get(i).getCzrName(),vo.getCzrName());
                         array[2] = strOrDecimalFieldCompare(now.get(i).getCzrArea(),vo.getCzrArea());
                         array[3] = strOrDecimalFieldCompare(now.get(i).getCzrCode(),vo.getCzrCode());;
                         array[4] = strOrDecimalFieldCompare(now.get(i).getSjCzr(),vo.getSjCzr());
                         array[5] = strOrDecimalFieldCompare(now.get(i).getReason(),vo.getReason());
                         array[6] = strOrDecimalFieldCompare(now.get(i).getDcRate(),vo.getDcRate());
                         array[7] = strOrDecimalFieldCompare(now.get(i).getMeasures(),vo.getMeasures());

                         arrayMap.put(i, array);
                         break;
                     }
                 }
                 if (count == 0) {
                     arrayMap.put(i, allOne);
                 }
             }
             return arrayMap;
         }
     }
     return null;
    }

        /**
         * 获取对外投资情况前后变化(合伙企业)
         */
    private Map<Integer, Byte[]> getDwtzFdChange(List<DwtzqkfdVo> past, List<DwtzqkfdVo> now) {
        final Byte[] allOne = {1, 1, 1, 1, 1, 1, 1, 1};
        if (CollectionUtils.isNotEmpty(now)) {
            //如果以前无,现在有
            if (CollectionUtils.isEmpty(past)) {
                Map<Integer, Byte[]> map2 = new HashMap<>(now.size());
                for (int i = 0; i < now.size(); i++) {
                    map2.put(i, allOne);
                }
                return map2;
                //以前有,现在也有
            } else {
                Map<Integer, Byte[]> arrayMap = new HashMap<>();
                for (int i = 0; i < now.size(); i++) {
                    int count = 0;
                    for (DwtzqkfdVo vo : past) {
                        if (Objects.equals(now.get(i).getCode(),vo.getCode())) {
                            Byte[] array = new Byte[8];
                            count++;
                            array[0] = strOrDecimalFieldCompare(now.get(i).getBdlx(),vo.getBdlx());
                            array[1] = strOrDecimalFieldCompare(now.get(i).getBdmc(),vo.getBdmc());
                            array[2] = 0;
                            array[3] = strOrDecimalFieldCompare(now.get(i).getSshy(),vo.getSshy());
                            array[4] = strOrDecimalFieldCompare(now.get(i).getAddress(),vo.getAddress());
                            array[5] = strOrDecimalFieldCompare(now.get(i).getTze(),vo.getTze());
                            array[6] = strOrDecimalFieldCompare(now.get(i).getTzbl(),vo.getTzbl());
                            array[7] = strOrDecimalFieldCompare(now.get(i).getSfsjkz(),vo.getSfsjkz());
                            arrayMap.put(i, array);
                            break;
                        }
                    }
                    if (count == 0) {
                        arrayMap.put(i, allOne);
                    }
                }
                return arrayMap;
            }
        }
        return null;
    }

    @Override
    public ResponseEntity<byte[]> export(Jbxxb jbxxb) throws IOException {
        JbxxbVo jbxxbVo = this.selectJbxxbByPrimaryKey(jbxxb);
        if (jbxxbVo.getJbShzt() == null || jbxxbVo.getJbShzt() == 8) {
            throw new RuntimeException("未提交的表单不可导出!");
        }
        String jbSfybgsStr = jbxxbVo.getJbSfybgsStr();
        String status = StringUtils.equals("否",jbSfybgsStr) ? "产权已设立登记，工商未设立登记" : StringUtils.equals("是",jbSfybgsStr) ?"产权工商均已设立登记":"产权已设立登记，工商未设立登记";

        String title;
        // 文件名称区分已完成登记和在途登记：企业名称+基本信息表；企业名称+基本信息表（审核中）
        String qymc = jbxxbVo.getJbQymc().replace("\n","");
        String fileName = jbxxbVo.getJbShzt() == 4 ? qymc + "基本信息表.xlsx" : qymc + "基本信息表(审核中).xlsx";
        String sheetName = jbxxbVo.getJbShzt() == 4 ? "基本信息表" : "基本信息表(审核中)";
        String type;
        if (StringUtils.isNotBlank(jbxxbVo.getJbZycqdjqx())) {
            title = "占有产权登记表";
            type = jbxxbVo.getJbZycqdjqxStr();
        } else if (StringUtils.isNotBlank(jbxxbVo.getJbBdcqdjqx())) {
            title = "变动产权登记表";
            type = jbxxbVo.getJbBdcqdjqxStr();
        } else {
            title = "注销产权登记表";
            type = jbxxbVo.getJbZxcqdjqxStr();
        }
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(sheetName);
        HSSFFont font1 = workbook.createFont();
        font1.setFontHeightInPoints((short) 12);
        font1.setBold(true);
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font1);
        style.setWrapText(true);
        HSSFFont font2 = workbook.createFont();
        font2.setFontHeightInPoints((short) 15);
        font2.setBold(true);
        HSSFCellStyle style2 = workbook.createCellStyle();
        style2.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style2.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style2.setFont(font2);
        HSSFCellStyle style3 = workbook.createCellStyle();
        style3.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style3.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        //写死10列,差不多够用
        int collNum = 10;
        int crNum;
        //国有+境内
        if (!Constants.QYLX_HHQY.equals(jbxxbVo.getBusinessNature()) && Constants.JNJW_1.equals(jbxxbVo.getJbJnjw())) {
            for (int i = 0; i < collNum; i++) {
                sheet.setColumnWidth(i, 18 * 256);
            }
            //合并单元格
            CellRangeAddress cr0 = new CellRangeAddress(0, 0, 0, collNum - 1);
            CellRangeAddress cr1 = new CellRangeAddress(1, 1, 0, collNum - 1);
            sheet.addMergedRegion(cr0);
            sheet.addMergedRegion(cr1);
            crNum = 12;
            //如果是特殊目的公司则多1行
            if ("1".equals(jbxxbVo.getJbSftsmdgs())) {
                crNum = 13;
            }
            for (int i = 0; i < crNum; i++) {
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 0, 1));
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 2, 4));
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 5, 6));
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 7, 9));
            }
            //标题行
            HSSFCell cell0_0 = sheet.createRow(0).createCell(0);
            cell0_0.setCellStyle(style2);
            cell0_0.setCellValue(title);
            HSSFCell cell1_0 = sheet.createRow(1).createCell(0);
            cell1_0.setCellStyle(style);
            cell1_0.setCellValue("基本信息");
            HSSFRow row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("企业名称");
            row2.createCell(2).setCellValue(jbxxbVo.getJbQymc());
            row2.createCell(5).setCellValue("统一社会信用编码");
            row2.createCell(7).setCellValue(jbxxbVo.getJbZzjgdm());
            HSSFRow row3 = sheet.createRow(3);
            row3.createCell(0).setCellValue("企业设立登记状态");
            row3.createCell(2).setCellValue(status);
            row3.createCell(5).setCellValue("国资监管机构类型");
            row3.createCell(7).setCellValue(jbxxbVo.getJbQzwjkjglxStr());
            HSSFRow row4 = sheet.createRow(4);
            row4.createCell(0).setCellValue("国资监管机构明细");
            row4.createCell(2).setCellValue(jbxxbVo.getJbGzwjkjgmxStr());
            row4.createCell(5).setCellValue("国家出资企业");
            final SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
            String czqyStr = sysOrganization == null? "":sysOrganization.getOrganization_name();
            row4.createCell(7).setCellValue(czqyStr);
            HSSFRow row5 = sheet.createRow(5);
            row5.createCell(0).setCellValue("国家出资企业统一社会信用编码");
            row5.createCell(2).setCellValue(jbxxbVo.getJbGjczqyCode());
            row5.createCell(5).setCellValue("与国家出资企业关系");
            row5.createCell(7).setCellValue(jbxxbVo.getJbRelaStr());
            HSSFRow row7 = sheet.createRow(6);
            row7.createCell(0).setCellValue("企业类别");
            row7.createCell(2).setCellValue(jbxxbVo.getJbQylbStr());
            row7.createCell(5).setCellValue("是否混改企业");
            row7.createCell(7).setCellValue(jbxxbVo.getJbHgqyStr());
            HSSFRow row8 = sheet.createRow(7);
            row8.createCell(0).setCellValue("组织形式");
            row8.createCell(2).setCellValue(jbxxbVo.getJbZzxsStr());
            row8.createCell(5).setCellValue("是否上市公司");
            row8.createCell(7).setCellValue(jbxxbVo.getJbSfssStr());
            HSSFRow row9 = sheet.createRow(8);
            row9.createCell(0).setCellValue("是否并表");
            row9.createCell(2).setCellValue(jbxxbVo.getJbSfbbStr());
            row9.createCell(5).setCellValue("企业产权级次");
            row9.createCell(7).setCellValue(jbxxbVo.getJbQyjcStr());
            HSSFRow row10 = sheet.createRow(9);
            row10.createCell(0).setCellValue("企业管理级次");
            row10.createCell(2).setCellValue(jbxxbVo.getJbQygljcStr());
            row10.createCell(5).setCellValue("注册/成立日期");
            row10.createCell(7).setCellValue(dateTransfer(jbxxbVo.getJbZcrq()));
            HSSFRow row11 = sheet.createRow(10);
            row11.createCell(0).setCellValue("工商登记日期");
            row11.createCell(2).setCellValue(dateTransfer(jbxxbVo.getJbGsdjrq()));
            row11.createCell(5).setCellValue("营业执照住所");
            row11.createCell(7).setCellValue(jbxxbVo.getJbQymc());
            HSSFRow row12 = sheet.createRow(11);
            row12.createCell(0).setCellValue("注册地");
            row12.createCell(2).setCellValue(jbxxbVo.getJbZcdStr());
            row12.createCell(5).setCellValue("主要出资人统一社会信用编码");
            row12.createCell(7).setCellValue(jbxxbVo.getJbCzrzzjgStr());
            HSSFRow row13 = sheet.createRow(12);
            row13.createCell(0).setCellValue("主要出资人");
            row13.createCell(2).setCellValue(jbxxbVo.getJdZyczr());
            row13.createCell(5).setCellValue("注册资本/认缴资本(万元)");
            row13.createCell(7).setCellValue("人民币:"+jbxxbVo.getJbZczb() +"\n"+jbxxbVo.getJbRjzbbzStr()+":"+jbxxbVo.getJbRjzb());
            HSSFRow row14 = sheet.createRow(13);
            row14.createCell(0).setCellValue("国有资本（万元）");
            row14.createCell(2).setCellValue(this.transDecimalToStr(jbxxbVo.getJbGyzb(),6));
            row14.createCell(5).setCellValue("所属行业/经营范围");
            row14.createCell(7).setCellValue(jbxxbVo.getJbZyhyStr());
            HSSFRow row15 = sheet.createRow(14);
            row15.createCell(0).setCellValue("是否国家出资企业主业");
            row15.createCell(2).setCellValue(jbxxbVo.getJbSfzyStr());
            row15.createCell(5).setCellValue("主要行业1");
            row15.createCell(7).setCellValue(jbxxbVo.getJbZyhy1Str());
            HSSFRow row16 = sheet.createRow(15);
            row16.createCell(0).setCellValue("主要行业2");
            row16.createCell(2).setCellValue(jbxxbVo.getJbZyhy2Str());
            row16.createCell(5).setCellValue("主要行业3");
            row16.createCell(7).setCellValue(jbxxbVo.getJbZyhy3Str());
            HSSFRow row17 = sheet.createRow(16);
            row17.createCell(0).setCellValue("主要行业2");
            row17.createCell(2).setCellValue(jbxxbVo.getJbZyhy2Str());
            row17.createCell(5).setCellValue("主要行业3");
            row17.createCell(7).setCellValue(jbxxbVo.getJbZyhy3Str());
            HSSFRow row18 = sheet.createRow(17);
            row18.createCell(0).setCellValue("经营状况");
            row18.createCell(2).setCellValue(jbxxbVo.getJbJyzkStr());
            row18.createCell(5).setCellValue("是否代管托管");
            row18.createCell(7).setCellValue(jbxxbVo.getJbSftgqyStr());
            HSSFRow row19 = sheet.createRow(18);
            row19.createCell(0).setCellValue("是否存在休眠、停业、歇业等情况");
            row19.createCell(2).setCellValue(jbxxbVo.getJbSfczblztStr());
            row19.createCell(5).setCellValue("境内/境外");
            row19.createCell(7).setCellValue(jbxxbVo.getJbJnjwStr());

                HSSFRow row20 = sheet.createRow(19);
                row20.createCell(0).setCellValue("是否境外转投境内企业");
                row20.createCell(2).setCellValue(jbxxbVo.getJbSfztjnStr());
                row20.createCell(5).setCellValue("是否已办工商");
                row20.createCell(7).setCellValue(jbxxbVo.getJbSfybgsStr());
                HSSFRow row21 = sheet.createRow(20);
                row21.createCell(0).setCellValue("产权登记情形");
                row21.createCell(2).setCellValue(type);



            int cgrfdBegin = 21;
            int czfdBegin = 21;

//            if ("1".equals(jbxxbVo.getJbSftsmdgs())) {
//                row12.createCell(0).setCellValue("注册目的");
//                row12.createCell(2).setCellValue(jbxxbVo.getJbZcmdStr());
//                row12.createCell(5).setCellValue("是否存在个人代持股");
//                row12.createCell(7).setCellValue(jbxxbVo.getJbSfczgrdcgStr());
//                row13.createCell(0).setCellValue("国有资本(万元)");
//                row13.createCell(2).setCellValue(transDecimalToStr(jbxxbVo.getJbGyzb(),6));
//                row13.createCell(5).setCellValue("与国家出资企业的关系");
//                row13.createCell(7).setCellValue(jbxxbVo.getJbRelaStr());
//                HSSFRow row14 = sheet.createRow(14);
//                row14.createCell(0).setCellValue("是否混改企业");
//                row14.createCell(1).setCellValue(jbxxbVo.getJbHgqyStr());
//                cgrfdBegin = 15;
//                czfdBegin = 15;
//            } else {
//                row12.createCell(0).setCellValue("是否存在个人代持股");
//                row12.createCell(2).setCellValue(jbxxbVo.getJbSfczgrdcgStr());
//                row12.createCell(5).setCellValue("国有资本(万元)");
//                row12.createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getJbGyzb(),6));
//                row13.createCell(0).setCellValue("与国家出资企业的关系");
//                row13.createCell(2).setCellValue(jbxxbVo.getJbRelaStr());
//                row13.createCell(5).setCellValue("是否混改企业");
//                row13.createCell(7).setCellValue(jbxxbVo.getJbHgqyStr());
//                cgrfdBegin = 14;
//                czfdBegin = 14;
//            }
            if (CollectionUtils.isNotEmpty(jbxxbVo.getFd9TableData())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getFd9TableData().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin, cgrfdBegin, 0, 9));
                HSSFCell cell_0 = sheet.createRow(cgrfdBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("持股人情况");
                String[] czfdTitle = {"序号", "持股人姓名", "", "", "实际出资人", "", "", "说明", "", ""};
                for (int i = 0; i <= jbxxbVo.getFd9TableData().size(); i++) {
                    sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin + 1 + i, cgrfdBegin + 1 + i, 1, 3));
                    sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin + 1 + i, cgrfdBegin + 1 + i, 4, 6));
                    sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin + 1 + i, cgrfdBegin + 1 + i, 7, 9));
                    rows[i] = sheet.createRow(cgrfdBegin + 1 + i);
                }
                for (int i = 0; i < collNum; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(jbxxbVo.getFd9TableData().get(i - 1).getFd9Cgrmc());
                    rows[i].createCell(4).setCellValue(jbxxbVo.getFd9TableData().get(i - 1).getFd9Sjczr());
                    rows[i].createCell(7).setCellValue(jbxxbVo.getFd9TableData().get(i - 1).getFd9Info());
                }
                czfdBegin = cgrfdBegin + jbxxbVo.getFd9TableData().size() + 1;
            }
            if (CollectionUtils.isNotEmpty(jbxxbVo.getFdTableData())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getFdTableData().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin, czfdBegin, 0, 9));
                HSSFCell cell_0 = sheet.createRow(czfdBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("出资人情况");
                String[] czfdTitle = {"序号", "出资人姓名", "统一社会信用代码", "出资人类别", "出资额(万元)", "实缴资本(万元)",
                        "认缴资本(万元)", "股权比例(%)","出资方式","缴付期限"};
                for (int i = 0; i <= jbxxbVo.getFdTableData().size(); i++) {
                    rows[i] = sheet.createRow(czfdBegin + 1 + i);
                }
                for (int i = 0; i < collNum; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }

                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(jbxxbVo.getFdTableData().get(i - 1).getFdCzrmc());
                    rows[i].createCell(2).setCellValue(jbxxbVo.getFdTableData().get(i - 1).getFdCzrzzjgdm());
                    rows[i].createCell(3).setCellValue(dictCacheStrategy.getTextByVal("CZRLB", jbxxbVo.getFdTableData().get(i - 1).getFdCzrlb()));
                    rows[i].createCell(4).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 1).getFdCze(),6));
                    rows[i].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 1).getFdSjzcj(),6));
                    rows[i].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 1).getFdRjzb(),6));
                    rows[i].createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 1).getFdGqbl(),6));
                    rows[i].createCell(8).setCellValue(jbxxbVo.getFdTableData().get(i - 1).getFdCzfsStr());
                    rows[i].createCell(9).setCellValue(this.dateTransfer(jbxxbVo.getFdTableData().get(i - 1).getFdJfqx()));
                }
                int sumRow = czfdBegin + jbxxbVo.getFdTableData().size() + 2;
                sheet.addMergedRegion(new CellRangeAddress(sumRow, sumRow, 0, 3));
                HSSFRow row = sheet.createRow(sumRow);
                HSSFCell cell = row.createCell(0);
                cell.setCellStyle(style3);
                cell.setCellValue("合计");
                row.createCell(4).setCellValue(transDecimalToStr(jbxxbVo.getJbHjcze(),6));
                row.createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbHjsjzcj(),6));
                row.createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbHjrjzb(),6));
                row.createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getJbHjbl(),6));
                //资本状况
                sheet.addMergedRegion(new CellRangeAddress(sumRow+1, sumRow+1, 0, 9));
                HSSFRow row_1 = sheet.createRow(sumRow+1);
                HSSFCell cell_1 = row_1.createCell(0);
                cell_1.setCellStyle(style);
                cell_1.setCellValue("资本状况");
                sheet.addMergedRegion(new CellRangeAddress(sumRow+2, sumRow+2, 0, 4));
                sheet.addMergedRegion(new CellRangeAddress(sumRow+2, sumRow+2, 5, 9));
                sheet.addMergedRegion(new CellRangeAddress(sumRow+3, sumRow+8, 0, 1));
                HSSFRow sheetRow = sheet.createRow(sumRow + 2);
                sheetRow.createCell(0).setCellValue("项目");
                sheetRow.createCell(5).setCellValue("企业申报数(万元)");
                HSSFRow[] hssfRows = new HSSFRow[6];
                for (int i = sumRow+3;i<sumRow+9;i++){
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 2, 4));
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 5, 9));
                    hssfRows[i-sumRow-3] = sheet.createRow(i);
                }
                hssfRows[0].createCell(0).setCellValue("实收资本");
                hssfRows[0].createCell(2).setCellValue("国家出资");
                hssfRows[0].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbGjsbs(),6));
                hssfRows[1].createCell(2).setCellValue("国有法人出资");
                hssfRows[1].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbGyfrsbs(),6));
                hssfRows[2].createCell(2).setCellValue("国有绝对控股法人出资");
                hssfRows[2].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbGyjdkgsbs(),6));
                hssfRows[3].createCell(2).setCellValue("国有实际控制法人出资");
                hssfRows[3].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbGysjkzsbs(),6));
                hssfRows[4].createCell(2).setCellValue("其他");
                hssfRows[4].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbQtqysbs(),6));
                hssfRows[5].createCell(2).setCellValue("合计");
                hssfRows[5].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbHjqysbs(),6));
            }
            //国有+境外
        } else if (!Constants.QYLX_HHQY.equals(jbxxbVo.getBusinessNature()) && Constants.JNJW_2.equals(jbxxbVo.getJbJnjw())) {
            //境外需要多4列
            collNum = collNum + 4;
            for (int i = 0; i < collNum; i++) {
                sheet.setColumnWidth(i, 13 * 256);
            }
            //合并单元格
            CellRangeAddress cr0 = new CellRangeAddress(0, 0, 0, collNum - 1);
            CellRangeAddress cr1 = new CellRangeAddress(1, 1, 0, collNum - 1);
            sheet.addMergedRegion(cr0);
            sheet.addMergedRegion(cr1);
            crNum = 14;
            for (int i = 0; i < crNum; i++) {
                if (i != 2) {
                    if (i == 1) {
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 3, 0, 2));
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 3, 3, 6));
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 3, 7, 9));
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 10, 11));
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 12, 13));
                    } else {
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 0, 2));
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 3, 6));
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 7, 9));
                        sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 10, 13));
                    }
                } else {
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 10, 11));
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 12, 13));
                }
            }
            //标题行
            HSSFCell cell0_0 = sheet.createRow(0).createCell(0);
            cell0_0.setCellStyle(style2);
            cell0_0.setCellValue(title);
            HSSFCell cell1_0 = sheet.createRow(1).createCell(0);
            cell1_0.setCellStyle(style);
            cell1_0.setCellValue("基本信息");
            HSSFRow row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("企业名称");
            row2.createCell(2).setCellValue(jbxxbVo.getJbQymc());
            row2.createCell(5).setCellValue("统一社会信用编码");
            row2.createCell(7).setCellValue(jbxxbVo.getJbZzjgdm());
            HSSFRow row3 = sheet.createRow(3);
            row3.createCell(0).setCellValue("企业设立登记状态");
            row3.createCell(2).setCellValue(status);
            row3.createCell(5).setCellValue("国资监管机构类型");
            row3.createCell(7).setCellValue(jbxxbVo.getJbQzwjkjglxStr());
            HSSFRow row4 = sheet.createRow(4);
            row4.createCell(0).setCellValue("国资监管机构明细");
            row4.createCell(2).setCellValue(jbxxbVo.getJbGzwjkjgmxStr());
            row4.createCell(5).setCellValue("国家出资企业");
            final SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
            String czqyStr = sysOrganization == null? "":sysOrganization.getOrganization_name();
            row4.createCell(7).setCellValue(czqyStr);
            HSSFRow row5 = sheet.createRow(5);
            row5.createCell(0).setCellValue("国家出资企业统一社会信用编码");
            row5.createCell(2).setCellValue(jbxxbVo.getJbGjczqyCode());
            row5.createCell(5).setCellValue("与国家出资企业关系");
            row5.createCell(7).setCellValue(jbxxbVo.getJbRelaStr());
            HSSFRow row7 = sheet.createRow(6);
            row7.createCell(0).setCellValue("企业类别");
            row7.createCell(2).setCellValue(jbxxbVo.getJbQylbStr());
            row7.createCell(5).setCellValue("是否混改企业");
            row7.createCell(7).setCellValue(jbxxbVo.getJbHgqyStr());
            HSSFRow row8 = sheet.createRow(7);
            row8.createCell(0).setCellValue("组织形式");
            row8.createCell(2).setCellValue(jbxxbVo.getJbZzxsStr());
            row8.createCell(5).setCellValue("是否上市公司");
            row8.createCell(7).setCellValue(jbxxbVo.getJbSfssStr());
            HSSFRow row9 = sheet.createRow(8);
            row9.createCell(0).setCellValue("是否并表");
            row9.createCell(2).setCellValue(jbxxbVo.getJbSfbbStr());
            row9.createCell(5).setCellValue("企业产权级次");
            row9.createCell(7).setCellValue(jbxxbVo.getJbQyjcStr());
            HSSFRow row10 = sheet.createRow(9);
            row10.createCell(0).setCellValue("企业管理级次");
            row10.createCell(2).setCellValue(jbxxbVo.getJbQygljcStr());
            row10.createCell(5).setCellValue("注册/成立日期");
            row10.createCell(7).setCellValue(dateTransfer(jbxxbVo.getJbZcrq()));
            HSSFRow row11 = sheet.createRow(10);
            row11.createCell(0).setCellValue("工商登记日期");
            row11.createCell(2).setCellValue(dateTransfer(jbxxbVo.getJbGsdjrq()));
            row11.createCell(5).setCellValue("营业执照住所");
            row11.createCell(7).setCellValue(jbxxbVo.getJbQymc());
            HSSFRow row12 = sheet.createRow(11);
            row12.createCell(0).setCellValue("注册地");
            row12.createCell(2).setCellValue(jbxxbVo.getJbZcdStr());
            row12.createCell(5).setCellValue("主要出资人统一社会信用编码");
            row12.createCell(7).setCellValue(jbxxbVo.getJbCzrzzjgStr());
            HSSFRow row13 = sheet.createRow(12);
            row13.createCell(0).setCellValue("主要出资人");
            row13.createCell(2).setCellValue(jbxxbVo.getJdZyczr());
            row13.createCell(5).setCellValue("注册资本/认缴资本(万元)");
            row13.createCell(7).setCellValue("人民币:"+jbxxbVo.getJbZczb() +"\n"+jbxxbVo.getJbRjzbbzStr()+":"+jbxxbVo.getJbRjzb());
            HSSFRow row14 = sheet.createRow(13);
            row14.createCell(0).setCellValue("国有资本（万元）");
            row14.createCell(2).setCellValue(this.transDecimalToStr(jbxxbVo.getJbGyzb(),6));
            row14.createCell(5).setCellValue("所属行业/经营范围");
            row14.createCell(7).setCellValue(jbxxbVo.getJbZyhyStr());
            HSSFRow row15 = sheet.createRow(14);
            row15.createCell(0).setCellValue("是否国家出资企业主业");
            row15.createCell(2).setCellValue(jbxxbVo.getJbSfzyStr());
            row15.createCell(5).setCellValue("主要行业1");
            row15.createCell(7).setCellValue(jbxxbVo.getJbZyhy1Str());
            HSSFRow row16 = sheet.createRow(15);
            row16.createCell(0).setCellValue("主要行业2");
            row16.createCell(2).setCellValue(jbxxbVo.getJbZyhy2Str());
            row16.createCell(5).setCellValue("主要行业3");
            row16.createCell(7).setCellValue(jbxxbVo.getJbZyhy3Str());
            HSSFRow row17 = sheet.createRow(16);
            row17.createCell(0).setCellValue("主要行业2");
            row17.createCell(2).setCellValue(jbxxbVo.getJbZyhy2Str());
            row17.createCell(5).setCellValue("主要行业3");
            row17.createCell(7).setCellValue(jbxxbVo.getJbZyhy3Str());
            HSSFRow row18 = sheet.createRow(17);
            row18.createCell(0).setCellValue("经营状况");
            row18.createCell(2).setCellValue(jbxxbVo.getJbJyzkStr());
            row18.createCell(5).setCellValue("是否代管托管");
            row18.createCell(7).setCellValue(jbxxbVo.getJbSftgqyStr());
            HSSFRow row19 = sheet.createRow(18);
            row19.createCell(0).setCellValue("是否存在休眠、停业、歇业等情况");
            row19.createCell(2).setCellValue(jbxxbVo.getJbSfczblztStr());
            row19.createCell(5).setCellValue("境内/境外");
            row19.createCell(7).setCellValue(jbxxbVo.getJbJnjwStr());
            HSSFRow row20 = sheet.createRow(19);
            row20.createCell(0).setCellValue("是否计划清理");
            row20.createCell(2).setCellValue(jbxxbVo.getJbSfztjnStr());
            row20.createCell(5).setCellValue("计划清理时间");
            row20.createCell(7).setCellValue(jbxxbVo.getJbSfybgsStr());
            HSSFRow row21 = sheet.createRow(20);
            row21.createCell(0).setCellValue("是否壳公司");
            row21.createCell(2).setCellValue(jbxxbVo.getJbSfkzgsStr());
            row21.createCell(5).setCellValue("是否存在个人代持股");
            row21.createCell(7).setCellValue(jbxxbVo.getJbSfczgrdcgStr());
            String jbSftsmdgsStr = jbxxbVo.getJbSftsmdgsStr();
            if (StringUtils.equals(jbSftsmdgsStr,"是")){
                HSSFRow row22 = sheet.createRow(21);
                row22.createCell(0).setCellValue("是否特殊目的公司");
                row22.createCell(2).setCellValue(jbSftsmdgsStr);
                row22.createCell(5).setCellValue("注册目的");
                row22.createCell(7).setCellValue(jbxxbVo.getJbZcmdStr());
                HSSFRow row23 = sheet.createRow(22);
                row23.createCell(0).setCellValue("是否已办工商");
                row23.createCell(2).setCellValue(jbxxbVo.getJbSfybgsStr());
                row23.createCell(5).setCellValue("产权登记情形");
                row23.createCell(7).setCellValue(type);
            }else {
                HSSFRow row22 = sheet.createRow(21);
                row22.createCell(0).setCellValue("是否特殊目的公司");
                row22.createCell(2).setCellValue(jbSftsmdgsStr);
                row22.createCell(5).setCellValue("是否已办工商");
                row22.createCell(7).setCellValue(jbxxbVo.getJbSfybgsStr());
                HSSFRow row23 = sheet.createRow(22);
                row23.createCell(0).setCellValue("产权登记情形");
                row23.createCell(2).setCellValue(type);
            }






            int cgrfdBegin = 22;
            int czfdBegin = 22;

//            if ("1".equals(jbxxbVo.getJbSftsmdgs())) {
//                row12.createCell(7).setCellValue("注册目的");
//                row12.createCell(10).setCellValue(jbxxbVo.getJbZcmdStr());
//                row13.createCell(0).setCellValue("是否计划清理");
//                row13.createCell(3).setCellValue(jbxxbVo.getJbQljhStr());
//                row13.createCell(7).setCellValue("计划清理时间");
//                row13.createCell(10).setCellValue(this.dateTransfer(jbxxbVo.getJbJhqlsj()));
//                row14.createCell(0).setCellValue("是否存在个人代持股");
//                row14.createCell(3).setCellValue(jbxxbVo.getJbSfczgrdcgStr());
//                row14.createCell(7).setCellValue("国有资本(万元)");
//                row14.createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbGyzb(),6));
//                row15.createCell(0).setCellValue("与国家出资企业的关系");
//                row15.createCell(3).setCellValue(jbxxbVo.getJbRelaStr());
//                row15.createCell(7).setCellValue("是否混改企业");
//                row15.createCell(10).setCellValue(jbxxbVo.getJbHgqyStr());
//            } else {
//                row12.createCell(7).setCellValue("是否计划清理");
//                row12.createCell(10).setCellValue(jbxxbVo.getJbQljhStr());
//                row13.createCell(0).setCellValue("计划清理时间");
//                row13.createCell(3).setCellValue(this.dateTransfer(jbxxbVo.getJbJhqlsj()));
//                row13.createCell(7).setCellValue("是否存在个人代持股");
//                row13.createCell(10).setCellValue(jbxxbVo.getJbSfczgrdcgStr());
//                row14.createCell(0).setCellValue("国有资本(万元)");
//                row14.createCell(3).setCellValue(transDecimalToStr(jbxxbVo.getJbGyzb(),6));
//                row14.createCell(7).setCellValue("与国家出资企业的关系");
//                row14.createCell(10).setCellValue(jbxxbVo.getJbRelaStr());
//                row15.createCell(0).setCellValue("是否混改企业");
//                row15.createCell(3).setCellValue(jbxxbVo.getJbHgqyStr());
//            }
            if (CollectionUtils.isNotEmpty(jbxxbVo.getFd9TableData())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getFd9TableData().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin, cgrfdBegin, 0, 13));
                HSSFCell cell_0 = sheet.createRow(cgrfdBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("持股人情况");
                String[] czfdTitle = {"序号", "持股人姓名", "", "", "", "实际出资人", "", "", "", "说明", "", "","",""};
                for (int i = 0; i <= jbxxbVo.getFd9TableData().size(); i++) {
                    sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin + 1 + i, cgrfdBegin + 1 + i, 1, 4));
                    sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin + 1 + i, cgrfdBegin + 1 + i, 5, 8));
                    sheet.addMergedRegion(new CellRangeAddress(cgrfdBegin + 1 + i, cgrfdBegin + 1 + i, 9, 13));
                    rows[i] = sheet.createRow(cgrfdBegin + 1 + i);
                }
                for (int i = 0; i < collNum; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(jbxxbVo.getFd9TableData().get(i - 1).getFd9Cgrmc());
                    rows[i].createCell(5).setCellValue(jbxxbVo.getFd9TableData().get(i - 1).getFd9Sjczr());
                    rows[i].createCell(9).setCellValue(jbxxbVo.getFd9TableData().get(i - 1).getFd9Info());
                }
                czfdBegin = cgrfdBegin + jbxxbVo.getFd9TableData().size() + 2;
            }
            if (CollectionUtils.isNotEmpty(jbxxbVo.getFdTableData())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getFdTableData().size() + 2];
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin, czfdBegin, 0, 13));
                HSSFCell cell_0 = sheet.createRow(czfdBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("出资人情况");
                String[] czfdTitle = {"序号","出资人姓名","", "统一社会信用代码", "出资人类别", "出资额(万元)", "", "实缴资本(万元)", "",
                        "认缴资本(万元)", "", "股权比例(%)","出资方式","缴付期限"};
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 2, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 2, 1, 2));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 2, 3, 3));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 2, 4, 4));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 2, 11, 11));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 2, 12, 12));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 2, 13, 13));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 1, 5, 6));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 1, 7, 8));
                sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 1, czfdBegin + 1, 9, 10));
                for (int i = 0; i <= jbxxbVo.getFdTableData().size() + 1; i++) {
                    rows[i] = sheet.createRow(czfdBegin + 1 + i);
                    if (i  < jbxxbVo.getFdTableData().size()){
                        sheet.addMergedRegion(new CellRangeAddress(czfdBegin + 3 + i,czfdBegin + 3 + i,1,2));
                    }
                }
                for (int i = 0; i < collNum; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                rows[1].createCell(5).setCellValue(dictCacheStrategy.getTextByVal("BZXZ", jbxxbVo.getJbCzebzxz()));
                rows[1].createCell(6).setCellValue("人民币");
                rows[1].createCell(7).setCellValue(dictCacheStrategy.getTextByVal("BZXZ", jbxxbVo.getJbSjzczbbzxz()));
                rows[1].createCell(8).setCellValue("人民币");
                rows[1].createCell(9).setCellValue(dictCacheStrategy.getTextByVal("BZXZ", jbxxbVo.getJbRjzbbzxz()));
                rows[1].createCell(10).setCellValue("人民币");
                for (int i = 2; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i - 1);
                    rows[i].createCell(1).setCellValue(jbxxbVo.getFdTableData().get(i - 2).getFdCzrmc());
                    rows[i].createCell(3).setCellValue(jbxxbVo.getFdTableData().get(i - 2).getFdCzrzzjgdm());
                    rows[i].createCell(4).setCellValue(dictCacheStrategy.getTextByVal("CZRLB", jbxxbVo.getFdTableData().get(i - 2).getFdCzrlb()));
                    rows[i].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 2).getFdCzebz(),6));
                    rows[i].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 2).getFdCze(),6));
                    rows[i].createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 2).getFdSjzczbbz(),6));
                    rows[i].createCell(8).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 2).getFdSjzcj(),6));
                    rows[i].createCell(9).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 2).getFdRjzbbz(),6));
                    rows[i].createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 2).getFdRjzb(),6));
                    rows[i].createCell(11).setCellValue(transDecimalToStr(jbxxbVo.getFdTableData().get(i - 2).getFdGqbl(),6));
                    rows[i].createCell(12).setCellValue(jbxxbVo.getFdTableData().get(i - 2).getFdCzfsStr());
                    rows[i].createCell(13).setCellValue(this.dateTransfer(jbxxbVo.getFdTableData().get(i - 2).getFdJfqx()));
                }
                int sumRow = czfdBegin + jbxxbVo.getFdTableData().size() + 3;
                sheet.addMergedRegion(new CellRangeAddress(sumRow, sumRow, 0, 4));
                HSSFRow row = sheet.createRow(sumRow);
                HSSFCell cell = row.createCell(0);
                cell.setCellStyle(style3);
                cell.setCellValue("合计");
                row.createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getJbHjcjebz(),6));
                row.createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbHjcze(),6));
                row.createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getJbHjsjzcjbz(),6));
                row.createCell(8).setCellValue(transDecimalToStr(jbxxbVo.getJbHjsjzcj(),6));
                row.createCell(9).setCellValue(transDecimalToStr(jbxxbVo.getJbHjrjzbbz(),6));
                row.createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbHjrjzb(),6));
                row.createCell(11).setCellValue(transDecimalToStr(jbxxbVo.getJbHjbl(),6));
                //资本状况
                sheet.addMergedRegion(new CellRangeAddress(sumRow+1, sumRow+1, 0, collNum-1));
                HSSFRow row_1 = sheet.createRow(sumRow+1);
                HSSFCell cell_1 = row_1.createCell(0);
                cell_1.setCellStyle(style);
                cell_1.setCellValue("资本状况");
                sheet.addMergedRegion(new CellRangeAddress(sumRow+2, sumRow+3, 0, 5));
                sheet.addMergedRegion(new CellRangeAddress(sumRow+2, sumRow+2, 6, 13));
                sheet.addMergedRegion(new CellRangeAddress(sumRow+3, sumRow+3, 6, 9));
                sheet.addMergedRegion(new CellRangeAddress(sumRow+3, sumRow+3, 10, 13));
                sheet.addMergedRegion(new CellRangeAddress(sumRow+4, sumRow+9, 0, 2));
                HSSFRow sheetRow = sheet.createRow(sumRow + 2);
                sheetRow.createCell(0).setCellValue("项目");
                sheetRow.createCell(6).setCellValue("企业申报数(万元)");
                HSSFRow sheetRow1 = sheet.createRow(sumRow + 3);
                sheetRow1.createCell(6).setCellValue(dictCacheStrategy.getTextByVal("BZXZ",jbxxbVo.getJbSjzczbbzxz()));
                sheetRow1.createCell(10).setCellValue("人民币");
                HSSFRow[] hssfRows = new HSSFRow[6];
                for (int i = sumRow+4;i<sumRow+10;i++){
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 3, 5));
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 6, 9));
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 10, 13));
                    hssfRows[i-sumRow-4] = sheet.createRow(i);
                }
                hssfRows[0].createCell(0).setCellValue("实收资本");
                hssfRows[0].createCell(3).setCellValue("国家出资");
                hssfRows[0].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbGjczqysbsbz(),6));
                hssfRows[0].createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbGjsbs(),6));
                hssfRows[1].createCell(3).setCellValue("国有法人出资");
                hssfRows[1].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbGyfrczsbsbz(),6));
                hssfRows[1].createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbGyfrsbs(),6));
                hssfRows[2].createCell(3).setCellValue("国有绝对控股法人出资");
                hssfRows[2].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbGyjdkgfrsbsbz(),6));
                hssfRows[2].createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbGyjdkgsbs(),6));
                hssfRows[3].createCell(3).setCellValue("国有实际控制法人出资");
                hssfRows[3].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbGysjkzfrsbsbz(),6));
                hssfRows[3].createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbGysjkzsbs(),6));
                hssfRows[4].createCell(3).setCellValue("其他");
                hssfRows[4].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbQtqysdsbz(),6));
                hssfRows[4].createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbQtqysbs(),6));
                hssfRows[5].createCell(3).setCellValue("合计");
                hssfRows[5].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getJbHjqysbsbz(),6));
                hssfRows[5].createCell(10).setCellValue(transDecimalToStr(jbxxbVo.getJbHjqysbs(),6));
            }
            //合伙企业
        } else if (Constants.QYLX_HHQY.equals(jbxxbVo.getBusinessNature())&& Constants.JNJW_1.equals(jbxxbVo.getJbJnjw())){
            for (int i = 0; i < collNum; i++) {
                sheet.setColumnWidth(i, 18 * 256);
            }
            //合并单元格
            CellRangeAddress cr0 = new CellRangeAddress(0, 0, 0, collNum - 1);
            CellRangeAddress cr1 = new CellRangeAddress(1, 1, 0, collNum - 1);
            sheet.addMergedRegion(cr0);
            sheet.addMergedRegion(cr1);
            crNum = 23;
            for (int i = 0; i < crNum; i++) {
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 0, 1));
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 5, 6));
                if (i == 13){
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 2, 3));
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 7, 8));
                }else {
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 2, 4));
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 7, 9));
                }
            }
            //标题行
            HSSFCell cell0_0 = sheet.createRow(0).createCell(0);
            cell0_0.setCellStyle(style2);
            cell0_0.setCellValue(title);
            HSSFCell cell1_0 = sheet.createRow(1).createCell(0);
            cell1_0.setCellStyle(style);
            cell1_0.setCellValue("基本信息");
            HSSFRow row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("企业名称");
            row2.createCell(2).setCellValue(jbxxbVo.getHhqyVo().getHhCompanyName());
            row2.createCell(5).setCellValue("统一社会信用编码");
            row2.createCell(7).setCellValue(jbxxbVo.getJbZzjgdm());
            HSSFRow row3 = sheet.createRow(3);
            row3.createCell(0).setCellValue("企业设立登记状态");
            row3.createCell(2).setCellValue(status);
            row3.createCell(5).setCellValue("合伙期限");
            row3.createCell(7).setCellValue(jbxxbVo.getHhqyVo().getHhQx());
            HSSFRow row4 = sheet.createRow(4);
            row4.createCell(0).setCellValue("国资监管机构类型");
            row4.createCell(2).setCellValue(jbxxbVo.getJbQzwjkjglxStr());
            row4.createCell(5).setCellValue("国资监管机构明细");
            row4.createCell(7).setCellValue(jbxxbVo.getJbGzwjkjgmxStr());
            HSSFRow row5 = sheet.createRow(5);
            row5.createCell(0).setCellValue("国家出资企业");
            final SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
            String czqyStr = sysOrganization == null? "":sysOrganization.getOrganization_name();
            row5.createCell(2).setCellValue(czqyStr);
            row5.createCell(5).setCellValue("国家出资企业统一社会信用编码");
            row5.createCell(7).setCellValue(jbxxbVo.getJbGjczqyCode());
            HSSFRow row6 = sheet.createRow(6);
            row6.createCell(0).setCellValue("与国家出资企业关系");
            row6.createCell(2).setCellValue(jbxxbVo.getJbRelaStr());
            row6.createCell(5).setCellValue("企业类别");
            row6.createCell(7).setCellValue(jbxxbVo.getJbQylbStr());
            HSSFRow row7 = sheet.createRow(7);
            row7.createCell(0).setCellValue("是否混改企业");
            row7.createCell(2).setCellValue(jbxxbVo.getJbHgqyStr());
            row7.createCell(5).setCellValue("组织形式");
            row7.createCell(7).setCellValue(jbxxbVo.getJbZzxsStr());
            HSSFRow row8 = sheet.createRow(8);
            row8.createCell(0).setCellValue("是否上市公司");
            row8.createCell(2).setCellValue(jbxxbVo.getJbSfssStr());
            row8.createCell(5).setCellValue("是否并表");
            row8.createCell(7).setCellValue(jbxxbVo.getJbSfbbStr());
            HSSFRow row9 = sheet.createRow(9);
            row9.createCell(0).setCellValue("企业产权级次");
            row9.createCell(2).setCellValue(jbxxbVo.getJbQyjcStr());
            row9.createCell(5).setCellValue("企业管理级次");
            row9.createCell(7).setCellValue(jbxxbVo.getJbQygljcStr());
            HSSFRow row10 = sheet.createRow(10);
            row10.createCell(0).setCellValue("注册/成立日期");
            row10.createCell(2).setCellValue(dateTransfer(jbxxbVo.getJbZcrq()));
            row10.createCell(5).setCellValue("工商登记日期");
            row10.createCell(7).setCellValue(dateTransfer(jbxxbVo.getJbGsdjrq()));
            HSSFRow row11 = sheet.createRow(11);
            row11.createCell(0).setCellValue("主要经营场所");
            row11.createCell(2).setCellValue(jbxxbVo.getJbQyjycs());
            row11.createCell(5).setCellValue("注册地");
            row11.createCell(7).setCellValue(jbxxbVo.getJbZcd());
            HSSFRow row12 = sheet.createRow(12);
            row12.createCell(0).setCellValue("执行事务合伙人");
            row12.createCell(2).setCellValue(jbxxbVo.getHhqyVo().getHhZxswhhr());
            row12.createCell(5).setCellValue("执行事务合伙人统一信用编码");
            row12.createCell(7).setCellValue(jbxxbVo.getHhqyVo().getHhZxswhhrCode());
            HSSFRow row13 = sheet.createRow(13);
            row13.createCell(0).setCellValue("主要出资人统一社会信用编码");
            row13.createCell(2).setCellValue(jbxxbVo.getJbCzrzzjgStr());
            row13.createCell(5).setCellValue("主要出资人");
            row13.createCell(7).setCellValue(jbxxbVo.getJdZyczr());
            HSSFRow row14 = sheet.createRow(14);
            row14.createCell(0).setCellValue("注册资本/认缴资本(万元)");
            HhqyVo hhqyVo = jbxxbVo.getHhqyVo();
            row14.createCell(2).setCellValue("人民币:" + hhqyVo.getHhRjczermb() + "\n" + hhqyVo.getHhRjczebzStr() + ":" + hhqyVo.getHhRjcze());
            row14.createCell(5).setCellValue("实收资本/实缴出资额(万元)");
            row14.createCell(7).setCellValue("人民币:" + hhqyVo.getHhSjczermb() + "\n" + hhqyVo.getHhSjczebzStr() + ":" + hhqyVo.getHhSjcze());
            HSSFRow row15 = sheet.createRow(15);
            row15.createCell(0).setCellValue("国有资本(万元)");
            row15.createCell(2).setCellValue(this.transDecimalToStr(jbxxbVo.getJbGyzb(),6));
            row15.createCell(4).setCellValue(jbxxbVo.getHhqyVo().getHhRjczebzStr());
            row15.createCell(5).setCellValue("是否为私募投资基金");
            row15.createCell(7).setCellValue(jbxxbVo.getHhqyVo().getHhSfsmtzjjStr());
            HSSFRow row16 = sheet.createRow(16);
            row16.createCell(0).setCellValue("所属行业/经营范围");
            row16.createCell(2).setCellValue(jbxxbVo.getJbZyhyStr());
            row16.createCell(5).setCellValue("是否国家出资企业主业");
            row16.createCell(7).setCellValue(jbxxbVo.getJbSfzyStr());
            HSSFRow row17 = sheet.createRow(17);
            row17.createCell(0).setCellValue("主要行业1");
            row17.createCell(2).setCellValue(jbxxbVo.getJbZyhy1Str());
            row17.createCell(5).setCellValue("主要行业2");
            row17.createCell(7).setCellValue(jbxxbVo.getJbZyhy2Str());
            HSSFRow row18 = sheet.createRow(18);
            row18.createCell(0).setCellValue("主要行业3");
            row18.createCell(2).setCellValue(jbxxbVo.getJbZyhy3Str());
            row18.createCell(5).setCellValue("经营状况");
            row18.createCell(7).setCellValue(jbxxbVo.getJbJyzkStr());
            HSSFRow row19 = sheet.createRow(19);
            row19.createCell(0).setCellValue("是否代管托管");
            row19.createCell(2).setCellValue(jbxxbVo.getJbSftgqyStr());
            row19.createCell(5).setCellValue("是否存在休眠、停业、歇业等情况");
            row19.createCell(7).setCellValue(jbxxbVo.getJbSfczblztStr());
            HSSFRow row20 = sheet.createRow(20);
            row20.createCell(0).setCellValue("境内/境外");
            row20.createCell(2).setCellValue(jbxxbVo.getJbJnjwStr());
            row20.createCell(5).setCellValue("是否境外转投境内企业");
            row20.createCell(7).setCellValue(jbxxbVo.getJbSfztjnStr());
            HSSFRow row21 = sheet.createRow(21);
            row21.createCell(0).setCellValue("是否已办工商");
            row21.createCell(2).setCellValue(jbxxbVo.getJbSfybgsStr());
            row21.createCell(5).setCellValue("产权登记情形");
            row21.createCell(7).setCellValue(type);
            HSSFRow row22 = sheet.createRow(22);
            row22.createCell(0).setCellValue("合伙协议");
            final Attachment attachment = attachmentService.selectAttachmentByPrimaryKey(jbxxbVo.getHhqyVo().getHhXyAttachment());
            String hhxyStr = attachment == null? "":attachment.getFileName();
            row22.createCell(2).setCellValue(hhxyStr);

            int hhrqkBegin = crNum + 2;
            int dwtzBegin = crNum + 2;
            if (CollectionUtils.isNotEmpty(jbxxbVo.getHhrqkfdList())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getHhrqkfdList().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(hhrqkBegin, hhrqkBegin, 0, 9));
                HSSFCell cell_0 = sheet.createRow(hhrqkBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("合伙人情况");
                String[] czfdTitle = {"序号", "合伙人名称", "合伙人统一信用代码", "合伙人类型", "合伙人类别", "认缴出资额(万元)",
                        "认缴出资比例(%)", "实缴出资额(万元)", "出资方式", "缴付期限"};
                for (int i = 0; i <= jbxxbVo.getHhrqkfdList().size(); i++) {
                    rows[i] = sheet.createRow(hhrqkBegin + 1 + i);
                }
                for (int i = 0; i < 10; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(jbxxbVo.getHhrqkfdList().get(i - 1).getName());
                    rows[i].createCell(2).setCellValue(jbxxbVo.getHhrqkfdList().get(i - 1).getHhrCode());
                    rows[i].createCell(3).setCellValue(dictCacheStrategy.getTextByVal("HHRLX", jbxxbVo.getHhrqkfdList().get(i - 1).getType()));
                    rows[i].createCell(4).setCellValue(dictCacheStrategy.getTextByVal("HHRLB", jbxxbVo.getHhrqkfdList().get(i - 1).getCategory()));
                    rows[i].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getHhrqkfdList().get(i - 1).getRjcze(),6));
                    rows[i].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getHhrqkfdList().get(i - 1).getRjczbl(),6));
                    rows[i].createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getHhrqkfdList().get(i - 1).getSjcze(),6));
                    String typeCode;
                    if ("1".equals(jbxxbVo.getHhrqkfdList().get(i - 1).getType())) {
                        typeCode = "CZFSPT";
                    } else {
                        typeCode = "CZFSYX";
                    }
                    rows[i].createCell(8).setCellValue(dictCacheStrategy.getTextByVal(typeCode, jbxxbVo.getHhrqkfdList().get(i - 1).getCzfs()));
                    rows[i].createCell(9).setCellValue(this.dateTransfer(jbxxbVo.getHhrqkfdList().get(i - 1).getJfqx()));
                }
                int sumRow = hhrqkBegin + jbxxbVo.getHhrqkfdList().size() + 2;
                sheet.addMergedRegion(new CellRangeAddress(sumRow, sumRow, 0, 4));
                HSSFRow row = sheet.createRow(sumRow);
                HSSFCell cell = row.createCell(0);
                cell.setCellStyle(style3);
                cell.setCellValue("合计");
                row.createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getHhqyVo().getHjrjcze(),6));
                row.createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getHhqyVo().getHjrjczbl(),6));
                row.createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getHhqyVo().getHhSjcze(),6));
                dwtzBegin = hhrqkBegin + jbxxbVo.getHhrqkfdList().size() + 3;
            }
            if (CollectionUtils.isNotEmpty(jbxxbVo.getDwtzqkfdList())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getDwtzqkfdList().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(dwtzBegin, dwtzBegin, 0, 9));
                HSSFCell cell_0 = sheet.createRow(dwtzBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("对外投资情况");
                String[] czfdTitle = {"序号", "标的类型", "标的名称", "标的企业统一社会信用代码", "", "所属行业", "注册地或所在地", "投资额(万元)",
                        "投资比例(%)", "是否实际控制"};
                for (int i = 0; i <= jbxxbVo.getDwtzqkfdList().size(); i++) {
                    sheet.addMergedRegion(new CellRangeAddress(dwtzBegin + 1 + i, dwtzBegin + 1 + i, 3, 4));
                    rows[i] = sheet.createRow(dwtzBegin + 1 + i);
                }
                for (int i = 0; i < 10; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(dictCacheStrategy.getTextByVal("BDLX", jbxxbVo.getDwtzqkfdList().get(i - 1).getBdlx()));
                    rows[i].createCell(2).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getBdmc());
                    rows[i].createCell(3).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getCode());
                    rows[i].createCell(5).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getSshyStr());
                    rows[i].createCell(6).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getAddressStr());
                    rows[i].createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getDwtzqkfdList().get(i - 1).getTze(),6));
                    rows[i].createCell(8).setCellValue(transDecimalToStr(jbxxbVo.getDwtzqkfdList().get(i - 1).getTzbl(),6));
                    rows[i].createCell(9).setCellValue(dictCacheStrategy.getTextByVal("YesNo", jbxxbVo.getDwtzqkfdList().get(i - 1).getSfsjkz()));
                }
            }
        }else if (Constants.QYLX_HHQY.equals(jbxxbVo.getBusinessNature())&& Constants.JNJW_2.equals(jbxxbVo.getJbJnjw())) {
            for (int i = 0; i < collNum; i++) {
                sheet.setColumnWidth(i, 18 * 256);
            }
            //合并单元格
            CellRangeAddress cr0 = new CellRangeAddress(0, 0, 0, collNum - 1);
            CellRangeAddress cr1 = new CellRangeAddress(1, 1, 0, collNum - 1);
            sheet.addMergedRegion(cr0);
            sheet.addMergedRegion(cr1);
            crNum = 25;
            for (int i = 0; i < crNum; i++) {
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 0, 1));
                sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 5, 6));
                if (i == 13) {
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 2, 3));
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 7, 8));
                } else {
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 2, 4));
                    sheet.addMergedRegion(new CellRangeAddress(i + 2, i + 2, 7, 9));
                }
            }
            //标题行
            HSSFCell cell0_0 = sheet.createRow(0).createCell(0);
            cell0_0.setCellStyle(style2);
            cell0_0.setCellValue(title);
            HSSFCell cell1_0 = sheet.createRow(1).createCell(0);
            cell1_0.setCellStyle(style);
            cell1_0.setCellValue("基本信息");
            HSSFRow row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("企业名称");
            row2.createCell(2).setCellValue(jbxxbVo.getHhqyVo().getHhCompanyName());
            row2.createCell(5).setCellValue("统一社会信用编码");
            row2.createCell(7).setCellValue(jbxxbVo.getJbZzjgdm());
            HSSFRow row3 = sheet.createRow(3);
            row3.createCell(0).setCellValue("企业设立登记状态");
            row3.createCell(2).setCellValue(status);
            row3.createCell(5).setCellValue("合伙期限");
            row3.createCell(7).setCellValue(jbxxbVo.getHhqyVo().getHhQx());
            HSSFRow row4 = sheet.createRow(4);
            row4.createCell(0).setCellValue("国资监管机构类型");
            row4.createCell(2).setCellValue(jbxxbVo.getJbQzwjkjglxStr());
            row4.createCell(5).setCellValue("国资监管机构明细");
            row4.createCell(7).setCellValue(jbxxbVo.getJbGzwjkjgmxStr());
            HSSFRow row5 = sheet.createRow(5);
            row5.createCell(0).setCellValue("国家出资企业");
            final SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
            String czqyStr = sysOrganization == null ? "" : sysOrganization.getOrganization_name();
            row5.createCell(2).setCellValue(czqyStr);
            row5.createCell(5).setCellValue("国家出资企业统一社会信用编码");
            row5.createCell(7).setCellValue(jbxxbVo.getJbGjczqyCode());
            HSSFRow row6 = sheet.createRow(6);
            row6.createCell(0).setCellValue("与国家出资企业关系");
            row6.createCell(2).setCellValue(jbxxbVo.getJbRelaStr());
            row6.createCell(5).setCellValue("企业类别");
            row6.createCell(7).setCellValue(jbxxbVo.getJbQylbStr());
            HSSFRow row7 = sheet.createRow(7);
            row7.createCell(0).setCellValue("是否混改企业");
            row7.createCell(2).setCellValue(jbxxbVo.getJbHgqyStr());
            row7.createCell(5).setCellValue("组织形式");
            row7.createCell(7).setCellValue(jbxxbVo.getJbZzxsStr());
            HSSFRow row8 = sheet.createRow(8);
            row8.createCell(0).setCellValue("是否上市公司");
            row8.createCell(2).setCellValue(jbxxbVo.getJbSfssStr());
            row8.createCell(5).setCellValue("是否并表");
            row8.createCell(7).setCellValue(jbxxbVo.getJbSfbbStr());
            HSSFRow row9 = sheet.createRow(9);
            row9.createCell(0).setCellValue("企业产权级次");
            row9.createCell(2).setCellValue(jbxxbVo.getJbQyjcStr());
            row9.createCell(5).setCellValue("企业管理级次");
            row9.createCell(7).setCellValue(jbxxbVo.getJbQygljcStr());
            HSSFRow row10 = sheet.createRow(10);
            row10.createCell(0).setCellValue("注册/成立日期");
            row10.createCell(2).setCellValue(dateTransfer(jbxxbVo.getJbZcrq()));
            row10.createCell(5).setCellValue("工商登记日期");
            row10.createCell(7).setCellValue(dateTransfer(jbxxbVo.getJbGsdjrq()));
            HSSFRow row11 = sheet.createRow(11);
            row11.createCell(0).setCellValue("主要经营场所");
            row11.createCell(2).setCellValue(jbxxbVo.getJbQyjycs());
            row11.createCell(5).setCellValue("注册地");
            row11.createCell(7).setCellValue(jbxxbVo.getJbZcd());
            HSSFRow row12 = sheet.createRow(12);
            row12.createCell(0).setCellValue("执行事务合伙人");
            row12.createCell(2).setCellValue(jbxxbVo.getHhqyVo().getHhZxswhhr());
            row12.createCell(5).setCellValue("执行事务合伙人统一信用编码");
            row12.createCell(7).setCellValue(jbxxbVo.getHhqyVo().getHhZxswhhrCode());
            HSSFRow row13 = sheet.createRow(13);
            row13.createCell(0).setCellValue("主要出资人统一社会信用编码");
            row13.createCell(2).setCellValue(jbxxbVo.getJbCzrzzjgStr());
            row13.createCell(5).setCellValue("主要出资人");
            row13.createCell(7).setCellValue(jbxxbVo.getJdZyczr());
            HSSFRow row14 = sheet.createRow(14);
            row14.createCell(0).setCellValue("注册资本/认缴资本(万元)");
            HhqyVo hhqyVo = jbxxbVo.getHhqyVo();
            row14.createCell(2).setCellValue("人民币:" + hhqyVo.getHhRjczermb() + "\n" + hhqyVo.getHhRjczebzStr() + ":" + hhqyVo.getHhRjcze());
            row14.createCell(5).setCellValue("实收资本/实缴出资额(万元)");
            row14.createCell(7).setCellValue("人民币:" + hhqyVo.getHhSjczermb() + "\n" + hhqyVo.getHhSjczebzStr() + ":" + hhqyVo.getHhSjcze());
            HSSFRow row15 = sheet.createRow(15);
            row15.createCell(0).setCellValue("国有资本(万元)");
            row15.createCell(2).setCellValue(this.transDecimalToStr(jbxxbVo.getJbGyzb(), 6));
//            row15.createCell(4).setCellValue(jbxxbVo.getHhqyVo().getHhRjczebzStr());
            row15.createCell(5).setCellValue("是否为私募投资基金");
            row15.createCell(7).setCellValue(jbxxbVo.getHhqyVo().getHhSfsmtzjjStr());
            HSSFRow row16 = sheet.createRow(16);
            row16.createCell(0).setCellValue("所属行业/经营范围");
            row16.createCell(2).setCellValue(jbxxbVo.getJbZyhyStr());
            row16.createCell(5).setCellValue("是否国家出资企业主业");
            row16.createCell(7).setCellValue(jbxxbVo.getJbSfzyStr());
            HSSFRow row17 = sheet.createRow(17);
            row17.createCell(0).setCellValue("主要行业1");
            row17.createCell(2).setCellValue(jbxxbVo.getJbZyhy1Str());
            row17.createCell(5).setCellValue("主要行业2");
            row17.createCell(7).setCellValue(jbxxbVo.getJbZyhy2Str());
            HSSFRow row18 = sheet.createRow(18);
            row18.createCell(0).setCellValue("主要行业3");
            row18.createCell(2).setCellValue(jbxxbVo.getJbZyhy3Str());
            row18.createCell(5).setCellValue("经营状况");
            row18.createCell(7).setCellValue(jbxxbVo.getJbJyzkStr());
            HSSFRow row19 = sheet.createRow(19);
            row19.createCell(0).setCellValue("是否代管托管");
            row19.createCell(2).setCellValue(jbxxbVo.getJbSftgqyStr());
            row19.createCell(5).setCellValue("是否存在休眠、停业、歇业等情况");
            row19.createCell(7).setCellValue(jbxxbVo.getJbSfczblztStr());
            HSSFRow row20 = sheet.createRow(20);
            row20.createCell(0).setCellValue("境内/境外");
            row20.createCell(2).setCellValue(jbxxbVo.getJbJnjwStr());
            row20.createCell(5).setCellValue("是否境外转投境内企业");
            row20.createCell(7).setCellValue(jbxxbVo.getJbSfztjnStr());
            HSSFRow row21 = sheet.createRow(21);
            String jbSfkzgsStr = jbxxbVo.getJbSfkzgsStr();
            row21.createCell(0).setCellValue("是否壳公司");
            row21.createCell(2).setCellValue(jbSfkzgsStr);
            row21.createCell(5).setCellValue("是否存在个人代持股");
            row21.createCell(7).setCellValue(jbxxbVo.getJbSfczgrdcgStr());
            String jbSftsmdgsStr = jbxxbVo.getJbSftsmdgsStr();
            if (StringUtils.equals("是",jbSftsmdgsStr)) {
                HSSFRow row22 = sheet.createRow(22);
                row22.createCell(0).setCellValue("是否特殊目的公司");
                row22.createCell(2).setCellValue(jbSftsmdgsStr);
                row22.createCell(5).setCellValue("注册目的");
                row22.createCell(7).setCellValue(jbxxbVo.getJbZcmdStr());
                HSSFRow row23 = sheet.createRow(23);
                row23.createCell(0).setCellValue("是否已办工商");
                row23.createCell(2).setCellValue(jbxxbVo.getJbSfybgsStr());
                row23.createCell(5).setCellValue("产权登记情形");
                row23.createCell(7).setCellValue(type);
                HSSFRow row24 = sheet.createRow(24);
                row24.createCell(0).setCellValue("合伙协议");
                final Attachment attachment = attachmentService.selectAttachmentByPrimaryKey(jbxxbVo.getHhqyVo().getHhXyAttachment());
                String hhxyStr = attachment == null ? "" : attachment.getFileName();
                row24.createCell(2).setCellValue(hhxyStr);
            }else {
                HSSFRow row22 = sheet.createRow(22);
                row22.createCell(0).setCellValue("是否特殊目的公司");
                row22.createCell(2).setCellValue(jbSftsmdgsStr);
                row22.createCell(5).setCellValue("是否已办工商");
                row22.createCell(7).setCellValue(jbxxbVo.getJbSfybgsStr());
                HSSFRow row23 = sheet.createRow(23);
                row23.createCell(0).setCellValue("产权登记情形");
                row23.createCell(2).setCellValue(type);
                row23.createCell(5).setCellValue("合伙协议");
                final Attachment attachment = attachmentService.selectAttachmentByPrimaryKey(jbxxbVo.getHhqyVo().getHhXyAttachment());
                String hhxyStr = attachment == null ? "" : attachment.getFileName();
                row23.createCell(7).setCellValue(hhxyStr);
            }
            int hhrqkBegin = crNum + 2;
            int dwtzBegin = crNum + 2;
            if (CollectionUtils.isNotEmpty(jbxxbVo.getJbDcggkList())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getJbDcggkList().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(hhrqkBegin, hhrqkBegin, 0, 9));
                HSSFCell cell_0 = sheet.createRow(hhrqkBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("代持股情况");
                String[] czfdTitle = {"序号", "企业名称", "名义出资人姓名", "名义出资人待持人地区", "名义出资人统一社会信用代码/身份证件号",
                        "实际出资人", "设立原因", "待持股权比例（%）", "保全措施"};
                for (int i = 0; i <= jbxxbVo.getJbDcggkList().size(); i++) {
                    rows[i] = sheet.createRow(hhrqkBegin + 1 + i);
                }
                for (int i = 0; i < czfdTitle.length; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(jbxxbVo.getJbDcggkList().get(i - 1).getCompanyName());
                    rows[i].createCell(2).setCellValue(jbxxbVo.getJbDcggkList().get(i - 1).getCzrName());
                    rows[i].createCell(3).setCellValue( jbxxbVo.getJbDcggkList().get(i - 1).getCzrAreaStr());
                    rows[i].createCell(4).setCellValue(jbxxbVo.getJbDcggkList().get(i - 1).getCzrCode());
                    rows[i].createCell(5).setCellValue(jbxxbVo.getJbDcggkList().get(i - 1).getSjCzr());
                    rows[i].createCell(6).setCellValue(jbxxbVo.getJbDcggkList().get(i - 1).getReasonStr());
                    rows[i].createCell(7).setCellValue(jbxxbVo.getJbDcggkList().get(i - 1).getDcRate());
                    rows[i].createCell(8).setCellValue( jbxxbVo.getJbDcggkList().get(i - 1).getMeasuresStr());
                }
                hhrqkBegin += jbxxbVo.getJbDcggkList().size();
                dwtzBegin += jbxxbVo.getJbDcggkList().size();
            }
            if (CollectionUtils.isNotEmpty(jbxxbVo.getHhrqkfdList())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getHhrqkfdList().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(hhrqkBegin, hhrqkBegin, 0, 9));
                HSSFCell cell_0 = sheet.createRow(hhrqkBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("合伙人情况");
                String[] czfdTitle = {"序号", "合伙人名称", "合伙人统一信用代码", "合伙人类型", "合伙人类别", "认缴出资额(万元)",
                        "认缴出资比例(%)", "实缴出资额(万元)", "出资方式", "缴付期限"};
                for (int i = 0; i <= jbxxbVo.getHhrqkfdList().size(); i++) {
                    rows[i] = sheet.createRow(hhrqkBegin + 1 + i);
                }
                for (int i = 0; i < 10; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(jbxxbVo.getHhrqkfdList().get(i - 1).getName());
                    rows[i].createCell(2).setCellValue(jbxxbVo.getHhrqkfdList().get(i - 1).getHhrCode());
                    rows[i].createCell(3).setCellValue(dictCacheStrategy.getTextByVal("HHRLX", jbxxbVo.getHhrqkfdList().get(i - 1).getType()));
                    rows[i].createCell(4).setCellValue(dictCacheStrategy.getTextByVal("HHRLB", jbxxbVo.getHhrqkfdList().get(i - 1).getCategory()));
                    rows[i].createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getHhrqkfdList().get(i - 1).getRjcze(), 6));
                    rows[i].createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getHhrqkfdList().get(i - 1).getRjczbl(), 6));
                    rows[i].createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getHhrqkfdList().get(i - 1).getSjcze(), 6));
                    String typeCode;
                    if ("1".equals(jbxxbVo.getHhrqkfdList().get(i - 1).getType())) {
                        typeCode = "CZFSPT";
                    } else {
                        typeCode = "CZFSYX";
                    }
                    rows[i].createCell(8).setCellValue(dictCacheStrategy.getTextByVal(typeCode, jbxxbVo.getHhrqkfdList().get(i - 1).getCzfs()));
                    rows[i].createCell(9).setCellValue(this.dateTransfer(jbxxbVo.getHhrqkfdList().get(i - 1).getJfqx()));
                }
                int sumRow = hhrqkBegin + jbxxbVo.getHhrqkfdList().size() + 2;
                sheet.addMergedRegion(new CellRangeAddress(sumRow, sumRow, 0, 4));
                HSSFRow row = sheet.createRow(sumRow);
                HSSFCell cell = row.createCell(0);
                cell.setCellStyle(style3);
                cell.setCellValue("合计");
                row.createCell(5).setCellValue(transDecimalToStr(jbxxbVo.getHhqyVo().getHjrjcze(), 6));
                row.createCell(6).setCellValue(transDecimalToStr(jbxxbVo.getHhqyVo().getHjrjczbl(), 6));
                row.createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getHhqyVo().getHhSjcze(), 6));
                dwtzBegin = hhrqkBegin + jbxxbVo.getHhrqkfdList().size() + 3;
            }
            if (CollectionUtils.isNotEmpty(jbxxbVo.getDwtzqkfdList())) {
                HSSFRow[] rows = new HSSFRow[jbxxbVo.getDwtzqkfdList().size() + 1];
                sheet.addMergedRegion(new CellRangeAddress(dwtzBegin, dwtzBegin, 0, 9));
                HSSFCell cell_0 = sheet.createRow(dwtzBegin).createCell(0);
                cell_0.setCellStyle(style);
                cell_0.setCellValue("对外投资情况");
                String[] czfdTitle = {"序号", "标的类型", "标的名称", "标的企业统一社会信用代码", "", "所属行业", "注册地或所在地", "投资额(万元)",
                        "投资比例(%)", "是否实际控制"};
                for (int i = 0; i <= jbxxbVo.getDwtzqkfdList().size(); i++) {
                    sheet.addMergedRegion(new CellRangeAddress(dwtzBegin + 1 + i, dwtzBegin + 1 + i, 3, 4));
                    rows[i] = sheet.createRow(dwtzBegin + 1 + i);
                }
                for (int i = 0; i < 10; i++) {
                    rows[0].createCell(i).setCellValue(czfdTitle[i]);
                }
                for (int i = 1; i < rows.length; i++) {
                    rows[i].createCell(0).setCellValue(i);
                    rows[i].createCell(1).setCellValue(dictCacheStrategy.getTextByVal("BDLX", jbxxbVo.getDwtzqkfdList().get(i - 1).getBdlx()));
                    rows[i].createCell(2).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getBdmc());
                    rows[i].createCell(3).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getCode());
                    rows[i].createCell(5).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getSshyStr());
                    rows[i].createCell(6).setCellValue(jbxxbVo.getDwtzqkfdList().get(i - 1).getAddressStr());
                    rows[i].createCell(7).setCellValue(transDecimalToStr(jbxxbVo.getDwtzqkfdList().get(i - 1).getTze(), 6));
                    rows[i].createCell(8).setCellValue(transDecimalToStr(jbxxbVo.getDwtzqkfdList().get(i - 1).getTzbl(), 6));
                    rows[i].createCell(9).setCellValue(dictCacheStrategy.getTextByVal("YesNo", jbxxbVo.getDwtzqkfdList().get(i - 1).getSfsjkz()));
                }
            }
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData("attachment",
                new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        workbook.write(out);
        return new ResponseEntity<>(out.toByteArray(), headers, HttpStatus.CREATED);
    }

    //    @Value("${common.wordModelFile}")
//    private String wordModelFile; //登记表word模板文件
    @Value("${common.wordModelHHFile}")
    private String wordModelHHFile; //登记表合伙word模板文件
    @Value("${common.wordModelFHHFile}")
    private String wordModelFHHFile; //登记表非合伙word模板文件
    @Value("${common.wordModelFileDJZ}")
    private String wordModelFileDJZ; //登记证word模板文件

    @Override
    public ResponseEntity<byte[]> loadPdf(Jbxxb jbxxb) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //查询jbxxb详情
        JbxxbVo vo = this.selectJbxxbByPrimaryKey(jbxxb);
        List<HhrqkfdVo> hhrqkfdVoList = vo.getHhrqkfdList(); //合伙人企业列表
        List<CzfdVo> czfdVoList = vo.getFdTableData(); //出资人企业列表
        HhqyVo hhqyVo = vo.getHhqyVo();//合伙企业扩展表
        InputStream input = null;

        List<Map<String, String>> tableDataList = new ArrayList<>();
        int rowNum = 0;
        String zcrq = "", zcdd = "", zczb = "", sshy = "",   //注册/成立日期，注册地/主要经营场所，注册资本/认缴出资额（万元），所属行业/经营范围
                hhZxswhhr = "", hhZxswhhrCode = "", hhQx = "", hhSfsmtzjj = "",sszb = ""; //执行事务合伙人，执行事务合伙人统一信用代码，合伙期限，是否为私募投资基金
        if(StringUtils.isNotEmpty(vo.getBusinessNature()) && "2".equals(vo.getBusinessNature())){ //合伙企业
            input = FtpPoolHelper.getInputStreamByName(wordModelHHFile);
            rowNum = 8;
            zcrq = vo.getJbZcrq() == null ? "" : sdf.format(vo.getJbZcrq());
            zcdd = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhZyjycsStr());
            zczb = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhRjcze());
            sshy = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhJyfw());
            hhZxswhhr = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhZxswhhr());
            hhZxswhhrCode = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhZxswhhrCode());
            hhQx = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhQx());
            hhSfsmtzjj = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhSfsmtzjjStr());
            sszb = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhSszb());
            String hhrjcze = hhqyVo == null ? "" : StringUtil.valueOf(hhqyVo.getHhRjcze());

            Map<String, String> map1 = new HashMap<String, String>();
            map1.put("jbQymc", StringUtil.valueOf(vo.getJbQymc()));
            tableDataList.add(map1);
            Map<String, String> map2 = new HashMap<String, String>();
            map2.put("jbGjczqy", StringUtil.valueOf(vo.getJbGjczqyStr()));
            map2.put("cqjc", StringUtil.valueOf(vo.getJbQyjcStr()));
            tableDataList.add(map2);
            Map<String, String> map3 = new HashMap<String, String>();
            map3.put("zcdd", zcdd);
            map3.put("zcrq", zcrq);
            tableDataList.add(map3);
            Map<String, String> map4 = new HashMap<String, String>();
            map4.put("zczb", hhrjcze);
            map4.put("jbZzxs", StringUtil.valueOf(vo.getJbZzxsStr()));
            tableDataList.add(map4);
            Map<String, String> map5 = new HashMap<String, String>();
            map5.put("hhZxswhhr", hhZxswhhr);
            tableDataList.add(map5);
            Map<String, String> map6 = new HashMap<String, String>();
            map6.put("hhZxswhhrCode", hhZxswhhrCode);
            tableDataList.add(map6);
        }else{
            input = FtpPoolHelper.getInputStreamByName(wordModelFHHFile);
            rowNum = 6;
            zcrq = vo.getJbZcrq() == null ? "" : sdf.format(vo.getJbZcrq());
            zcdd = StringUtils.isEmpty(vo.getJbZcdStr()) ?
                    (StringUtils.isEmpty(vo.getJbZcdjwStr()) ? "" : StringUtil.valueOf(vo.getJbZcdjwStr()))
                    : StringUtil.valueOf(vo.getJbZcdStr());
            zczb = vo.getJbZczb() == null ? "" : StringUtil.valueOf(vo.getJbZczb());
            sshy = StringUtil.valueOf(vo.getJbZyhyStr());
            sszb = StringUtil.valueOf(vo.getJbHjqysbs());

            Map<String, String> map1 = new HashMap<String, String>();
            map1.put("jbQymc", StringUtil.valueOf(vo.getJbQymc()));
            tableDataList.add(map1);
            Map<String, String> map2 = new HashMap<String, String>();
            map2.put("jbGjczqy", StringUtil.valueOf(vo.getJbGjczqyStr()));
            map2.put("cqjc", StringUtil.valueOf(vo.getJbQyjcStr()));
            tableDataList.add(map2);
            Map<String, String> map3 = new HashMap<String, String>();
            map3.put("zcdd", zcdd);
            map3.put("zcrq", zcrq);
            tableDataList.add(map3);
            Map<String, String> map4 = new HashMap<String, String>();
            map4.put("zczb", zczb);
            map4.put("jbZzxs", StringUtil.valueOf(vo.getJbZzxsStr()));
            tableDataList.add(map4);
        }

//        Map<String, String> map1 = new HashMap<String, String>();
//        map1.put("jbQymc", StringUtil.valueOf(vo.getJbQymc()));
//        map1.put("jbZzjgdm", StringUtil.valueOf(vo.getJbZzjgdm()));
//        tableDataList.add(map1);
//        Map<String, String> map2 = new HashMap<String, String>();
//        map2.put("jbGjczqy", StringUtil.valueOf(vo.getJbGjczqyStr()));
//        map2.put("jbGzjgjg", StringUtil.valueOf(vo.getJbGzjgjgStr()));
//        tableDataList.add(map2);
//        Map<String, String> map3 = new HashMap<String, String>();
//        map3.put("zcrq", zcrq);
//        map3.put("zcdd", zcdd);
//        tableDataList.add(map3);
//        Map<String, String> map4 = new HashMap<String, String>();
//        map4.put("zczb", zczb);
//        map4.put("sszb", sszb);
//        tableDataList.add(map4);
//        Map<String, String> map5 = new HashMap<String, String>();
//        map5.put("jbZzxs", StringUtil.valueOf(vo.getJbZzxsStr()));
//        map5.put("jbQylb", StringUtil.valueOf(vo.getJbQylbStr()));
//        tableDataList.add(map5);
//        Map<String, String> map6 = new HashMap<String, String>();
//        map6.put("jbGyzb", StringUtil.valueOf(vo.getJbGyzb()));
//        map6.put("jbRela", StringUtil.valueOf(vo.getJbRelaStr()));
//        tableDataList.add(map6);
//        Map<String, String> map7 = new HashMap<String, String>();
//        map7.put("jbJnjw", StringUtil.valueOf(vo.getJbJnjwStr()));
//        map7.put("sshy", sshy);
//        tableDataList.add(map7);
//        Map<String, String> map8 = new HashMap<String, String>();
//        map8.put("cqjc", StringUtil.valueOf(vo.getJbQyjcStr()));
//        map8.put("jbHgqy", StringUtil.valueOf(vo.getJbHgqyStr()));
//        tableDataList.add(map8);
//        Map<String, String> map9 = new HashMap<String, String>();
//        map9.put("jbSftsmdgs", StringUtil.valueOf(vo.getJbSftsmdgsStr()));
//        map9.put("jbSfczgrdcg", StringUtil.valueOf(vo.getJbSfczgrdcgStr()));
//        tableDataList.add(map9);
//        Map<String, String> map10 = new HashMap<String, String>();
//        map10.put("jbJyzk", StringUtil.valueOf(vo.getJbJyzkStr()));
//        map10.put("jbSfzy", StringUtil.valueOf(vo.getJbSfzyStr()));
//        tableDataList.add(map10);
//        Map<String, String> map11 = new HashMap<String, String>();
//        //登记情形需要根据登记表判断
        //查询登记表信息
        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setJbxxId(vo.getId());
        List<BusinessInfo> infoList = businessInfoService.selectForList(businessInfo);
        if(CollectionUtils.isNotEmpty(infoList)){
            businessInfo = infoList.get(0);
        }
//        String djqx = "";
//        if(businessInfo != null){
//            if(Constants.RG_TYPE_ZY.equals(businessInfo.getRgType())){ //占有
//                djqx = StringUtil.valueOf(vo.getJbZycqdjqxStr());
//            }else if(Constants.RG_TYPE_BD.equals(businessInfo.getRgType())){  //变动
//                djqx = StringUtil.valueOf(vo.getJbBdcqdjqxStr());
//            }else if(Constants.RG_TYPE_ZX.equals(businessInfo.getRgType())){  //注销
//                djqx = StringUtil.valueOf(vo.getJbZxcqdjqxStr());
//            }
//        }
//        map11.put("djqx", djqx);
//        map11.put("jbSsbm", StringUtil.valueOf(vo.getJbSsbmStr()));
//        tableDataList.add(map11);
//        Map<String, String> mapt1 = new HashMap<String, String>();
//        tableDataList.add(mapt1);
//        Map<String, String> map12 = new HashMap<String, String>();
//        map12.put("hhZxswhhr", hhZxswhhr);
//        map12.put("hhZxswhhrCode", hhZxswhhrCode);
//        tableDataList.add(map12);
//        Map<String, String> map13 = new HashMap<String, String>();
//        map13.put("hhQx", hhQx);
//        map13.put("hhSfsmtzjj", hhSfsmtzjj);
//        tableDataList.add(map13);
        Map<String, String> mapt2 = new HashMap<String, String>();
        tableDataList.add(mapt2);


        BigDecimal sjzbSum = new BigDecimal(0),rjzbSum = new BigDecimal(0),gqblSum = new BigDecimal(0);
        int i = 1;
        if((StringUtils.isNotEmpty(vo.getBusinessNature()) && "2".equals(vo.getBusinessNature()))){
            if(CollectionUtils.isNotEmpty(hhrqkfdVoList)){
                for(HhrqkfdVo hhrqkfdVo : hhrqkfdVoList){
                    Map<String, String> mapList = new HashMap<String, String>();
                    mapList.put("rownum", String.valueOf(i));
                    mapList.put("fdCzrmc", StringUtil.valueOf(hhrqkfdVo.getName()));
//                    mapList.put("fdCzrlb", StringUtil.valueOf(hhrqkfdVo.getCategoryStr()));
                    mapList.put("fdSjzcj", hhrqkfdVo.getSjcze() == null ? "" : String.valueOf(hhrqkfdVo.getSjcze()));
                    mapList.put("fdRjzb", hhrqkfdVo.getRjcze() == null ? "" : String.valueOf(hhrqkfdVo.getRjcze()));
                    mapList.put("fdGqbl", hhrqkfdVo.getRjczbl() == null ? "" : String.valueOf(hhrqkfdVo.getRjczbl()));
//                    mapList.put("czfs", StringUtil.valueOf(hhrqkfdVo.getCzfsStr()));
//                    mapList.put("jfqx", hhrqkfdVo.getJfqx() == null ? "" : sdf.format(hhrqkfdVo.getJfqx()));
                    tableDataList.add(mapList);
                    i++;
                    sjzbSum = sjzbSum.add(hhrqkfdVo.getSjcze() == null ? new BigDecimal(0) : hhrqkfdVo.getSjcze());
                    rjzbSum = rjzbSum.add(hhrqkfdVo.getRjcze() == null ? new BigDecimal(0) : hhrqkfdVo.getRjcze());
                    gqblSum = gqblSum.add(hhrqkfdVo.getRjczbl() == null ? new BigDecimal(0) : hhrqkfdVo.getRjczbl());
                }
            }else{
                Map<String, String> mapList = new HashMap<String, String>();
                mapList.put("rownum", String.valueOf(i));
                mapList.put("fdCzrmc", "");
//                mapList.put("fdCzrlb", "");
                mapList.put("fdSjzcj", "");
                mapList.put("fdRjzb", "");
                mapList.put("fdGqbl", "");
//                mapList.put("czfs", "");
//                mapList.put("jfqx", "");
                tableDataList.add(mapList);
            }
        }else{
            if(CollectionUtils.isNotEmpty(czfdVoList)){
                for(CzfdVo czfdVo : czfdVoList){
                    Map<String, String> mapList = new HashMap<String, String>();
                    mapList.put("rownum", String.valueOf(i));
                    mapList.put("fdCzrmc", StringUtil.valueOf(czfdVo.getFdCzrmc()));
//                    mapList.put("fdCzrlb", StringUtil.valueOf(czfdVo.getFdCzrlbStr()));
                    mapList.put("fdSjzcj", czfdVo.getFdSjzcj() == null ? "" : String.valueOf(czfdVo.getFdSjzcj()));
                    mapList.put("fdRjzb", czfdVo.getFdRjzb() == null ? "" : String.valueOf(czfdVo.getFdRjzb()));
                    mapList.put("fdGqbl", czfdVo.getFdGqbl() == null ? "" : String.valueOf(czfdVo.getFdGqbl()));
//                    mapList.put("czfs", StringUtil.valueOf(czfdVo.getFdCzfsStr()));
//                    mapList.put("jfqx", czfdVo.getFdJfqx() == null ? "" : sdf.format(czfdVo.getFdJfqx()));
                    tableDataList.add(mapList);
                    i++;
                    sjzbSum = sjzbSum.add(czfdVo.getFdSjzcj() == null ? new BigDecimal(0) : czfdVo.getFdSjzcj());
                    rjzbSum = rjzbSum.add(czfdVo.getFdRjzb() == null ? new BigDecimal(0) : czfdVo.getFdRjzb());
                    gqblSum = gqblSum.add(czfdVo.getFdGqbl() == null ? new BigDecimal(0) : czfdVo.getFdGqbl());
                }
            }else{
                Map<String, String> mapList = new HashMap<String, String>();
                mapList.put("rownum", String.valueOf(i));
                mapList.put("fdCzrmc", "");
//                mapList.put("fdCzrlb", "");
                mapList.put("fdSjzcj", "");
                mapList.put("fdRjzb", "");
                mapList.put("fdGqbl", "");
//                mapList.put("czfs", "");
//                mapList.put("jfqx", "");
                tableDataList.add(mapList);
            }
        }
        Map<String, String> map14 = new HashMap<String, String>();
        map14.put("sjzbSum", sjzbSum == null ? "" : String.valueOf(sjzbSum));
        map14.put("rjzbSum", rjzbSum == null ? "" : String.valueOf(rjzbSum));
        map14.put("gqblSum", gqblSum == null ? "" : String.valueOf(gqblSum));
        tableDataList.add(map14);
        //登记表编号
        Map<String, String> textMap = new HashMap<>();
        textMap.put("registerCode",businessInfo == null ? "" : StringUtil.valueOf(businessInfo.getRegisterCode()));

        String qymc = vo.getJbQymc().replace("\n","");
        String docFileName = qymc + System.currentTimeMillis() + ".docx";
        String pdfFileName = qymc + System.currentTimeMillis() + ".pdf";
        String localPath = System.getProperties().getProperty("user.home");
        File localDir = new File(localPath);
        localDir.mkdirs();
        int listNum = (StringUtils.isNotEmpty(vo.getBusinessNature()) && "2".equals(vo.getBusinessNature())) ?
                vo.getHhrqkfdList().size() : czfdVoList.size();
        WordPoiUtil.changWord(input,localPath+"/" + docFileName,textMap,tableDataList,rowNum,listNum);

        ByteArrayOutputStream out = null;
        FileInputStream in = null;
        try {
            File file = new File(localPath+"/" + docFileName);
            in = new FileInputStream(file);
            out = (ByteArrayOutputStream) Doc2Pdf.doc2pdf(in);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment",
                    new String(pdfFileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//	        headers.setContentLength(out.size());

            //获取pdf页码
            ByteArrayInputStream swapStream = new ByteArrayInputStream(out.toByteArray());
            PdfReader pdfReader = new PdfReader(swapStream);
            int pages = pdfReader.getNumberOfPages();
            headers.set("pages",String.valueOf(pages));

            //删除本地文件
            FileUtils.deleteFile(localPath+"/" + docFileName);
            return new ResponseEntity<>(out.toByteArray(), headers, HttpStatus.OK);
        }finally {
            if(out != null) {
                out.close();
            }
            if(in != null) {
                in.close();
            }
        }
    }



    /**
     * 获取所有产权登记情形(占有+变动+注销)
     */
    @Override
    public List<DictionaryVo> loadAllSituations() {
        String[] situationNames = {"占有产权登记情形","变动产权登记情形","注销产权登记情形"};
        String[] typeCodes = {"ZYCQDJQX","BDCQDJQX","ZXCQDJQX"};
        List<DictionaryVo> list = new ArrayList<>(typeCodes.length);
        for (int i = 0; i < typeCodes.length; i++) {
            DictionaryVo dicVo = new DictionaryVo();
            dicVo.setText(situationNames[i]);
            dicVo.setDictionaryList(dictCacheStrategy.getCache(typeCodes[i]).getDictionaryList());
            list.add(dicVo);
        }
        return list;
    }

    /**
     * 将Decimal数据转为String,null值转为0
     */
    private String transDecimalToStr(BigDecimal decimal,int scale){
        return decimal == null? BigDecimal.ZERO.toString():decimal.setScale(scale,RoundingMode.HALF_UP).toString();
    }

    /**
     * 判断企业是否存在下级
     */
    @Override
    public Boolean hasSubordinate(Jbxxb jbxxb) {
        return jbxxbMapper.hasSubordinate(jbxxb) == 0;
    }

    @Override
    public ResponseEntity<byte[]> loadDJZPdf(Jbxxb jbxxb) throws IOException, DocumentException {
        HttpHeaders headers = new HttpHeaders();
        //非国资委账号不可以打印登记证
        SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
        SysOrganization org = organizationService.getOrgByOrgId(user.getOrganization_id());
        if(StringUtils.isEmpty(org.getBusinesstype()) || !org.getBusinesstype().equals("1")){
//            throw new RuntimeException("非国资委账号不可以打印登记证.");
            headers.set("isgzw","false");
        }else{
            headers.set("isgzw","true");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //查询jbxxb详情
        JbxxbVo vo = this.selectJbxxbByPrimaryKey(jbxxb);
        List<CzfdVo> czfdVoList = vo.getFdTableData();
        InputStream input = FtpPoolHelper.getInputStreamByName(wordModelFileDJZ);

        List<Map<String, String>> tableDataList = new ArrayList<>();

        Map<String, String> map = new HashMap<String, String>();
        map.put("qymc", StringUtil.valueOf(vo.getJbQymc()));
        map.put("zcd", StringUtil.valueOf(vo.getJbZcdStr()));
        map.put("zcrq", sdf.format(vo.getJbZcrq()));
        map.put("zczb", vo.getJbZczb() == null ? "" : String.valueOf(vo.getJbZczb()));
        map.put("zzxs", StringUtil.valueOf(vo.getJbZzxsStr()));
        map.put("gyzb", vo.getJbGjsbs() == null ? StringUtil.valueOf(new BigDecimal(0)) : StringUtil.valueOf(vo.getJbGjsbs()));
        BigDecimal sjzbSum = new BigDecimal(0),rjzbSum = new BigDecimal(0),gqblSum = new BigDecimal(0);
        int i = 1;
        for(CzfdVo czfdVo : czfdVoList){
            map.put("czrmc"+i, czfdVo.getFdCzrmc());
            map.put("sjzj"+i, czfdVo.getFdSjzcj() == null ? "" : String.valueOf(czfdVo.getFdSjzcj()));
            map.put("rjzj"+i, czfdVo.getFdRjzb() == null ? "" : String.valueOf(czfdVo.getFdRjzb()));
            map.put("gqbl"+i, czfdVo.getFdGqbl() == null ? "" : String.valueOf(czfdVo.getFdGqbl()));
            sjzbSum = sjzbSum.add(czfdVo.getFdSjzcj() == null ? new BigDecimal(0) : czfdVo.getFdSjzcj());
            rjzbSum = rjzbSum.add(czfdVo.getFdRjzb() == null ? new BigDecimal(0) : czfdVo.getFdRjzb());
            gqblSum = gqblSum.add(czfdVo.getFdGqbl() == null ? new BigDecimal(0) : czfdVo.getFdGqbl());
            i++;
            if(i > 20){
                break;
            }
        }
        map.put("sjzjSum", sjzbSum == null ? "" : String.valueOf(sjzbSum));
        map.put("rjzjSum", rjzbSum == null ? "" : String.valueOf(rjzbSum));
        map.put("gqblSum", gqblSum == null ? "" : String.valueOf(gqblSum));

        String qymc = vo.getJbQymc().replace("\n","");
        String docFileName = qymc + System.currentTimeMillis() + ".docx";
        String pdfFileName = qymc + System.currentTimeMillis() + ".pdf";
        String localPath = System.getProperties().getProperty("user.home");
        File localDir = new File(localPath);
        localDir.mkdirs();
        WordPoiUtil.changWord(input,localPath+"/" + docFileName,map,new ArrayList<>());

        File file = new File(localPath+"/" + docFileName);

        FileInputStream in = new FileInputStream(file);
        ByteArrayOutputStream out = (ByteArrayOutputStream) Doc2Pdf.doc2pdf(in);


        String pdfCopyFileName = qymc+ "copy" + System.currentTimeMillis() + ".pdf";
        Document document = new Document(); // 建立文档
        FileOutputStream copyOut = new FileOutputStream(localPath+"/" + pdfCopyFileName);
        PdfCopy pdfCopy = new PdfSmartCopy(document,copyOut); // 生成的目标PDF文件
        document.open();
        //获取pdf页码
        ByteArrayInputStream swapStream = new ByteArrayInputStream(out.toByteArray());
        PdfReader pdfReader = new PdfReader(swapStream);
        int pages = pdfReader.getNumberOfPages();
        for(int p = 1; p <= pages; p++){
            PdfDictionary pd = pdfReader.getPageN(p);
            pd.put(PdfName.ROTATE, new PdfNumber(90)); // 顺时针旋转90°
        }
        for (int page = 0; page < pages; ) {
            pdfCopy.addPage(pdfCopy.getImportedPage(pdfReader, ++page));
        }
        pdfCopy.flush();
        document.close();
        copyOut.close();
        headers.set("pages",String.valueOf(pages));
        headers.setContentDispositionFormData("attachment",
                new String(pdfCopyFileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        File fileCopy = new File(localPath+"/" + pdfCopyFileName);
        FileInputStream copyIn = null;
        ByteArrayOutputStream copyOutByte = null;
        byte[] byt = new byte[(int)fileCopy.length()];
        try{
            copyIn = new FileInputStream(fileCopy);
            int len = 0;
            copyOutByte = new ByteArrayOutputStream();
            while((len = copyIn.read(byt)) != -1) {
                copyOutByte.write(byt, 0, len);
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(copyOutByte != null){
                copyOutByte.close();
            }
            if(copyIn != null){
                copyIn.close();
            }
        }

        //删除本地文件
        FileUtils.deleteFile(localPath+"/" + docFileName);
        FileUtils.deleteFile(localPath+"/" + pdfCopyFileName);

        return new ResponseEntity<>(copyOutByte.toByteArray(), headers, HttpStatus.CREATED);
    }

    @Override
    public List<JbxxbVo> queryDjByPage(JbxxbParam jbxxbParam) {
        //当前登陆人组织及以下
        SysUser loginUser = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        jbxxbParam.setUserId(loginUser.getUser_id());
        jbxxbParam.setUserOrgId(loginUser.getOrganization_id());
        //分页
        PageHelper.startPage(jbxxbParam.getPageNumber(), jbxxbParam.getLimit(), false);
        List<JbxxbVo> jbxxbVos = jbxxbMapper.queryDjForList(jbxxbParam);
        if (CollectionUtils.isNotEmpty(jbxxbVos)) {
            for (JbxxbVo jbxxbVo : jbxxbVos) {
                jbxxbVo.setCanEdit(false);
                SysUser currentUser =(SysUser) SpringSecurityUserTools.instance().getUser(null);
                //填报中的编辑仅开放给申报人及同级账号
                //如果当前登陆人的组织和填报人一致则为同级账号???
                SysUserVo createUser = userInfoMapper.getUserInfoByUserId(jbxxbVo.getCreateUser());
                if (createUser != null &&
                        Objects.equals(currentUser.getOrganization_id(),createUser.getOrganization_id())){
                    jbxxbVo.setCanEdit(true);
                }
            }
        }
        return jbxxbVos;
    }

    @Override
    public long queryTotalDj(JbxxbParam jbxxbParam) {
        return jbxxbMapper.queryTotalDj(jbxxbParam);
    }

    /**
     * 前后登记的浮动行字段比较
     */
    private byte strOrDecimalFieldCompare(Object now, Object past){
        //前后登记值同时为null认为相同
        if (now == null && past == null){
            return 0;
        }
        //前后登记中仅存一个null则认为不同
        if (now == null || past == null){
            return 1;
        }
        if (now.getClass() != past.getClass()){
            return 1;
        }
        //目前浮动行字段只存在String,BigDecimal,Date
        if (now instanceof String){
            return (byte) (now.equals(past)? 0:1);
        } else if (now instanceof BigDecimal){
            return (byte) ((((BigDecimal) now).compareTo((BigDecimal) past)) == 0? 0:1);
        } else if (now instanceof Date){
            return (byte) (((Date) now).getTime() == ((Date) past).getTime()? 0:1);
        } else {
            return (byte) (now.toString().equals(past.toString())? 0:1);
        }
    }

    /**
     * 时间转日期字符串,null转空字符串
     */
    private String dateTransfer(Date date){
        if (date == null){
            return "";
        }
        return DateUtils.dateFormat(date);
    }

    /**
     * 根据组织id获取其最新的状态数据--对外接口
     */
    @Override
    public List<JbxxbVo> loadRecentApprovedByOpenApi(Integer pageNumber,Integer limit,List<String> orgId){
        //分页
        PageHelper.startPage(pageNumber, limit, false);
        List<JbxxbVo> jbxxbVoList = jbxxbMapper.loadRecentApprovedByOpenApi(orgId);
        for(JbxxbVo jbxxbVo : jbxxbVoList){
            if (jbxxbVo != null) {
                //SysOrganization sysOrganization = organizationService.selectOrganizationById(new SysOrganization(jbxxbVo.getJbCzrzzjgid()));
                //jbxxbVo.setJbCzrzzjgStr(sysOrganization == null?"":sysOrganization.getOrganization_code()+sysOrganization.getOrganization_name());
                jbxxbVo.setJbCzrzzjgStr(jbxxbVo.getJbCzrzzjgdm());
//                jbxxbVo.setFd9TableData(cgrfdMapper.selectByJbxxId(jbxxbVo.getId()));
                List<CzfdVo> czfdVos = czfdMapper.selectByJbxxId(jbxxbVo.getId());
                jbxxbVo.setFdTableData(czfdVos);
//                jbxxbVo.setYwzbbVo(ywzbbMapper.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setYwzbb2Vo(ywzbb2Service.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setFd6TableData(syccfpfdService.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setFd1TableData(fhbzcfdService.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setFd3TableData(xgzjgfdService.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setFd8TableData(zrsrfdService.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setFd7TableData(hrhcfdService.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setFd2TableData(ygqcyffdService.selectByJbxxId(jbxxbVo.getId()));
//                jbxxbVo.setFd5TableData(cjffdService.selectByJbxxId(jbxxbVo.getId()));
                if (StringUtils.isNotBlank(jbxxbVo.getJbGsdjxgzl())){
                    List<Attachment> attachments = attachmentService.selectForList(new Attachment(jbxxbVo.getJbGsdjxgzl()));
                    Attachment attachment = CollectionUtils.isEmpty(attachments) ? null : attachments.get(0);
                    jbxxbVo.setGsAttachment(attachment);
                }
                //合伙企业
                jbxxbVo.setHhqyVo(hhqyService.selectByJbxxbId(jbxxbVo.getId()));
                jbxxbVo.setHhrqkfdList(hhrqkfdService.selectByJbxxbId(jbxxbVo.getId()));
//                jbxxbVo.setDwtzqkfdList(dwtzqkfdService.selectByJbxxbId(jbxxbVo.getId()));
                //转换字典表
//                toStr(jbxxbVo);
            }
        }
        return jbxxbVoList;
    }

    @Override
    public List<JbxxbVo> loadRecentApprovedByOrdId(Integer pageNumber, Integer limit, List<String> orgId) {
        //分页
        PageHelper.startPage(pageNumber, limit, false);
        List<JbxxbVo> jbxxbVoList = jbxxbMapper.loadRecentApprovedByOpenApi(orgId);
        return jbxxbVoList;
    }

    private void toStr(JbxxbVo jbxxbVo){
        List<Dictionary> JNJWList = dictCacheStrategy.getByTypeCodeCommon("JNJW"); //境内境外
        List<Dictionary> SFYBGSList = dictCacheStrategy.getByTypeCodeCommon("SFYBGS");//是否已办工商
        List<Dictionary> ZYCQDJQXList = dictCacheStrategy.getByTypeCodeCommon("ZYCQDJQX");//占有产权登记情形
        List<Dictionary> QYLBList = dictCacheStrategy.getByTypeCodeCommon("QYLB");//企业类别
        List<Dictionary> ZZXSList = dictCacheStrategy.getByTypeCodeCommon("ZZXS");//组织形式
        List<Dictionary> QYJCList = dictCacheStrategy.getByTypeCodeCommon("QYJC");//企业级次
        List<Dictionary> JYZKList = dictCacheStrategy.getByTypeCodeCommon("JYZK");//经营状况
        List<Dictionary> ZCDList = dictCacheStrategy.getByTypeCodeCommon("ZCD");//注册地(境内)
        List<Dictionary> ZCDJWList = dictCacheStrategy.getByTypeCodeCommon("ZCDJW");//注册地(境外)
        List<Dictionary> GJCZQYList = dictCacheStrategy.getByTypeCodeCommon("GJCZQY");//国家出资企业
        List<Dictionary> SSBMList = dictCacheStrategy.getByTypeCodeCommon("SSBM");//所属部门
        List<Dictionary> GZJGJGList = dictCacheStrategy.getByTypeCodeCommon("GZJGJG");//国资监管机构
        List<Dictionary> INDUSTRYList = dictCacheStrategy.getByTypeCodeCommon("INDUSTRY_CLASSIFICATION_TREE");//所属行业
        List<Dictionary> YesNoList = dictCacheStrategy.getByTypeCodeCommon("YesNo");//是否
        List<Dictionary> ZCMDList = dictCacheStrategy.getByTypeCodeCommon("ZCMD");//注册目的
        List<Dictionary> RELAList = dictCacheStrategy.getByTypeCodeCommon("RELA");//与国家出资企业的关系
        List<Dictionary> BDCQDJQXList = dictCacheStrategy.getByTypeCodeCommon("BDCQDJQX");//变动产权登记情形
        List<Dictionary> ZXCQDJQXList = dictCacheStrategy.getByTypeCodeCommon("ZXCQDJQX");//注销产权登记情形
        List<Dictionary> BZXZList = dictCacheStrategy.getByTypeCodeCommon("BZXZ");//币种选择
        if(StringUtils.isNotEmpty(jbxxbVo.getJbJnjw())){
            Optional<Dictionary> op = JNJWList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbJnjw())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbJnjwStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSfybgs())){
            Optional<Dictionary> op = SFYBGSList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSfybgs())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSfybgsStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZycqdjqx())){
            Optional<Dictionary> op = ZYCQDJQXList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZycqdjqx())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZycqdjqxStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbQylb())){
            Optional<Dictionary> op = QYLBList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbQylb())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbQylbStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZzxs())){
            Optional<Dictionary> op = ZZXSList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZzxs())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZzxsStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbQyjc())){
            Optional<Dictionary> op = QYJCList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbQyjc())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbQyjcStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbJyzk())){
            Optional<Dictionary> op = JYZKList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbJyzk())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbJyzkStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZcd())){
            Optional<Dictionary> op = ZCDList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZcd())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZcdStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZcdjw())){
            Optional<Dictionary> op = ZCDJWList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZcdjw())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZcdjwStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbGjczqy())){
            Optional<Dictionary> op = GJCZQYList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbGjczqy())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbGjczqyStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSsbm())){
            Optional<Dictionary> op = SSBMList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSsbm())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSsbmStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbGzjgjg())){
            Optional<Dictionary> op = GZJGJGList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbGzjgjg())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbGzjgjgStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZyhy())){
            StringBuffer sb = new StringBuffer();
            String[] zyhyList = jbxxbVo.getJbZyhy().split(",");
            for(String zyhy : zyhyList){
                Optional<Dictionary> op = INDUSTRYList.stream().filter(d -> d.getVal().equals(zyhy)).findFirst();
                if(op.isPresent()) {
                    sb.append(op.get().getText()).append(",");
                }
            }
            jbxxbVo.setJbZyhyStr(sb.toString());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSfzy())){
            Optional<Dictionary> op = YesNoList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSfzy())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSfzyStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSfztjn())){
            Optional<Dictionary> op = YesNoList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSfztjn())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSfztjnStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSftsmdgs())){
            Optional<Dictionary> op = YesNoList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSftsmdgs())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSftsmdgsStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZcmd())){
            Optional<Dictionary> op = ZCMDList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZcmd())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZcmdStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSfczgrdcg())){
            Optional<Dictionary> op = YesNoList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSfczgrdcg())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSfczgrdcgStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbRela())){
            Optional<Dictionary> op = RELAList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbRela())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbRelaStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbHgqy())){
            Optional<Dictionary> op = YesNoList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbHgqy())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbHgqyStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSfyz())){
            Optional<Dictionary> op = YesNoList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSfyz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSfyzStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbBdcqdjqx())){
            Optional<Dictionary> op = BDCQDJQXList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbBdcqdjqx())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbBdcqdjqxStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZxcqdjqx())){
            Optional<Dictionary> op = ZXCQDJQXList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZxcqdjqx())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZxcqdjqxStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbQljh())){
            Optional<Dictionary> op = YesNoList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbQljh())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbQljhStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbQysbsbzxz())){
            Optional<Dictionary> op = BZXZList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbQysbsbzxz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbQysbsbzxzStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSdsbzxz())){
            Optional<Dictionary> op = BZXZList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSdsbzxz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSdsbzxzStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbCzebzxz())){
            Optional<Dictionary> op = BZXZList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbCzebzxz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbCzebzxzStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbSjzczbbzxz())){
            Optional<Dictionary> op = BZXZList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbSjzczbbzxz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbSjzczbbzxzStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbRjzbbzxz())){
            Optional<Dictionary> op = BZXZList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbRjzbbzxz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbRjzbbzxzStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZczbbzxz())){
            Optional<Dictionary> op = BZXZList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZczbbzxz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZczbbzxzStr(op.get().getText());
        }
        if(StringUtils.isNotEmpty(jbxxbVo.getJbZczbbz())){
            Optional<Dictionary> op = BZXZList.stream().filter(d -> d.getVal().equals(jbxxbVo.getJbZczbbz())).findFirst();
            if(op.isPresent()) jbxxbVo.setJbZczbbzStr(op.get().getText());
        }
    }
}
