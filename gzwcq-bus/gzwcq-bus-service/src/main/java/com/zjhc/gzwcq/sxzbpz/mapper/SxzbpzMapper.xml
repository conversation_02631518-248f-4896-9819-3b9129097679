<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.sxzbpz.mapper.ISxzbpzMapper">

	<resultMap type="com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="table_name" property="tableName"/>
		<result column="table_name_cn" property="tableNameCn"/>
		<result column="field_name" property="fieldName"/>
		<result column="field_name_cn" property="fieldNameCn"/>
		<result column="type" property="type"/>
		<result column="dic_type" property="dicType"/>
		<result column="data_search" property="dataSearch"/>
		<result column="economic_behavior_analysis" property="economicBehaviorAnalysis"/>
		<result column="statistics" property="statistics"/>
		<result column="detail" property="detail"/>
		<result column="index_type" property="indexType"/>
		<result column="situations" property="situations"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		table_name, 
		table_name_cn, 
		field_name, 
		field_name_cn, 
		type, 
		dic_type, 
		data_search, 
		economic_behavior_analysis, 
		statistics, 
		detail, 
		index_type, 
		situations
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.table_name, 
		t.table_name_cn, 
		t.field_name, 
		t.field_name_cn, 
		t.type, 
		t.dic_type, 
		t.data_search, 
		t.economic_behavior_analysis, 
		t.statistics, 
		t.detail, 
		t.index_type, 
		t.situations
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{tableName}, 
		#{tableNameCn}, 
		#{fieldName}, 
		#{fieldNameCn}, 
		#{type}, 
		#{dicType}, 
		#{dataSearch}, 
		#{economicBehaviorAnalysis}, 
		#{statistics}, 
		#{detail}, 
		#{indexType}, 
		#{situations}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="tableName != null and tableName != ''">
			and t.table_name = #{tableName}
		</if>
		<if test="tableNameCn != null and tableNameCn != ''">
			and t.table_name_cn = #{tableNameCn}
		</if>
		<if test="fieldName != null and fieldName != ''">
			and t.field_name = #{fieldName}
		</if>
		<if test="fieldNameCn != null and fieldNameCn != ''">
			and t.field_name_cn = #{fieldNameCn}
		</if>
		<if test="type != null and type != ''">
			and t.type = #{type}
		</if>
		<if test="dicType != null and dicType != ''">
			and t.dic_type = #{dicType}
		</if>
		<if test="dataSearch != null">
			and t.data_search = #{dataSearch}
		</if>
		<if test="economicBehaviorAnalysis != null">
			and t.economic_behavior_analysis = #{economicBehaviorAnalysis}
		</if>
		<if test="statistics != null">
			and t.statistics = #{statistics}
		</if>
		<if test="detail != null">
			and t.detail = #{detail}
		</if>
		<if test="indexType != null and indexType != ''">
			and t.index_type = #{indexType}
		</if>
		<if test="situations != null and situations != ''">
			and t.situations = #{situations}
		</if>
	</sql>

	<sql id="whereSql2">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="tableName != null and tableName != ''">
			and t.table_name = #{tableName}
		</if>
		<if test="tableNameCn != null and tableNameCn != ''">
			and t.table_name_cn = #{tableNameCn}
		</if>
		<if test="fieldName != null and fieldName != ''">
			and t.field_name = #{fieldName}
		</if>
		<if test="fieldNameCn != null and fieldNameCn != ''">
			and t.field_name_cn like concat('%',#{fieldNameCn},'%')
		</if>
		<if test="type != null and type != ''">
			and t.type = #{type}
		</if>
		<if test="dicType != null and dicType != ''">
			and t.dic_type = #{dicType}
		</if>
		<if test="dataSearch != null">
			and t.data_search = #{dataSearch}
		</if>
		<if test="economicBehaviorAnalysis != null">
			and t.economic_behavior_analysis = #{economicBehaviorAnalysis}
		</if>
		<if test="statistics != null">
			and t.statistics = #{statistics}
		</if>
		<if test="detail != null">
			and t.detail = #{detail}
		</if>
		<if test="indexType != null and indexType != ''">
			and t.index_type = #{indexType}
		</if>
		<if test="situations != null and situations != ''">
			and t.situations = #{situations}
		</if>
	</sql>

	<insert id="insert" parameterType="com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_sxzbpz (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_sxzbpz set isDeleted = 'Y' where
		id in
		<foreach collection="sxzbpzs" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_sxzbpz set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_sxzbpz  where
		id in
		<foreach collection="sxzbpzs" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_sxzbpz  where id = #{id}
	</delete>
	
	<select id="selectSxzbpzByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_sxzbpz
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_sxzbpz
		<set>
			<if test="tableName != null">
				table_name=#{tableName},
			</if>
			<if test="tableNameCn != null">
				table_name_cn=#{tableNameCn},
			</if>
			<if test="fieldName != null">
				field_name=#{fieldName},
			</if>
			<if test="fieldNameCn != null">
				field_name_cn=#{fieldNameCn},
			</if>
			<if test="type != null">
				type=#{type},
			</if>
			<if test="dicType != null">
				dic_type=#{dicType},
			</if>
			<if test="dataSearch != null">
				data_search=#{dataSearch},
			</if>
			<if test="economicBehaviorAnalysis != null">
				economic_behavior_analysis=#{economicBehaviorAnalysis},
			</if>
			<if test="statistics != null">
				statistics=#{statistics},
			</if>
			<if test="detail != null">
				detail=#{detail},
			</if>
			<if test="indexType != null">
				index_type=#{indexType},
			</if>
			<if test="situations != null">
				situations=#{situations}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_sxzbpz
		<set>
			table_name=#{tableName},
			table_name_cn=#{tableNameCn},
			field_name=#{fieldName},
			field_name_cn=#{fieldNameCn},
			type=#{type},
			dic_type=#{dicType},
			data_search=#{dataSearch},
			economic_behavior_analysis=#{economicBehaviorAnalysis},
			statistics=#{statistics},
			detail=#{detail},
			index_type=#{indexType},
			situations=#{situations}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_sxzbpz t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalSxzbpzs" parameterType="com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam" resultType="java.lang.Long">
		select
			count(id)
		from cq_sxzbpz t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="querySxzbpzForList" parameterType="com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_sxzbpz t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_sxzbpz t
		where t.id != #{id}
			<if test="tableName != null and tableName != ''">
				and t.table_name = #{tableName}
			</if>
			<if test="tableNameCn != null and tableNameCn != ''">
				and t.table_name_cn = #{tableNameCn}
			</if>
			<if test="fieldName != null and fieldName != ''">
				and t.field_name = #{fieldName}
			</if>
			<if test="fieldNameCn != null and fieldNameCn != ''">
				and t.field_name_cn = #{fieldNameCn}
			</if>
			<if test="type != null and type != ''">
				and t.type = #{type}
			</if>
			<if test="dicType != null and dicType != ''">
				and t.dic_type = #{dicType}
			</if>
			<if test="dataSearch != null">
				and t.data_search = #{dataSearch}
			</if>
			<if test="economicBehaviorAnalysis != null">
				and t.economic_behavior_analysis = #{economicBehaviorAnalysis}
			</if>
			<if test="statistics != null">
				and t.statistics = #{statistics}
			</if>
			<if test="detail != null">
				and t.detail = #{detail}
			</if>
			<if test="indexType != null and indexType != ''">
				and t.index_type = #{indexType}
			</if>
			<if test="situations != null and situations != ''">
				and t.situations = #{situations}
			</if>
	</select>

	<select id="selectTables" parameterType="com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz" resultMap="baseResultMapExt">
		SELECT
		t.table_name,t.table_name_cn
		FROM(
		select t.table_name,t.table_name_cn, min(t.table_seq) row_nume from cq_sxzbpz t
		<where>
			<include refid="whereSql"/>
		</where>
		group by t.table_name,t.table_name_cn
		)t
		ORDER BY row_nume
	</select>

	<update id="saveBatch">
		update cq_sxzbpz
		<set>
			<if test="sxzbpz.tableName != null and sxzbpz.tableName != ''">
				table_name=#{sxzbpz.tableName},
			</if>
			<if test="sxzbpz.tableNameCn != null and sxzbpz.tableNameCn != ''">
				table_name_cn=#{sxzbpz.tableNameCn},
			</if>
			<if test="sxzbpz.fieldName != null and sxzbpz.fieldName != ''">
				field_name=#{sxzbpz.fieldName},
			</if>
			<if test="sxzbpz.fieldNameCn != null and sxzbpz.fieldNameCn != ''">
				field_name_cn=#{sxzbpz.fieldNameCn},
			</if>
			type=#{sxzbpz.type},
			dic_type=#{sxzbpz.dicType},
			data_search=#{sxzbpz.dataSearch},
			economic_behavior_analysis=#{sxzbpz.economicBehaviorAnalysis},
			statistics=#{sxzbpz.statistics},
			detail=#{sxzbpz.detail},
			index_type=#{sxzbpz.indexType},
			situations=#{sxzbpz.situations}
		</set>
		where
		id in
		<foreach collection="sxzIds" open="(" close=")" separator="," item="id">
			#{id}
		</foreach>
	</update>

	<select id="selectByIndexType" parameterType="com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam" resultMap="baseResultMapExt">
		select t.index_type from cq_sxzbpz t
		<where>
			<include refid="whereSql"/>
		</where>
		group by t.index_type
	</select>

	<select id="selectFieldsByParam" parameterType="com.zjhc.gzwcq.sxzbpz.entity.SxzbpzParam" resultMap="baseResultMapExt">
		select distinct <include refid="columnsAlias"/>
		from
		cq_sxzbpz t
		<where>
			<include refid="whereSql" />
			<if test="situationList != null and !situationList.isEmpty() and situationList.size()>0">
				and
				(<foreach collection="situationList" item="item" open="(" close=")" separator="or">
					FIND_IN_SET(#{item},situations)
				</foreach>
				<!--基本信息指标不做筛选-->
				or t.index_type = 'jbxxzb')
			</if>
		</where>
	</select>

	<select id="loadByFieldNameCn" parameterType="com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/>
		from cq_sxzbpz t
		<where>
			<include refid="whereSql2" />
		</where>
	</select>
</mapper>