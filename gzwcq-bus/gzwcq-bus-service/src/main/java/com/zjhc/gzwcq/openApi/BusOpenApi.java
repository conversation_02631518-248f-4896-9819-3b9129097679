package com.zjhc.gzwcq.openApi;

import com.alibaba.fastjson.JSON;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.service.api.IOrganizationService;
import com.boot.iAdmin.appInfo.entity.AppInfo;
import com.boot.iAdmin.appInfo.mapper.IAppInfoMapper;
import com.boot.iAdmin.ftp.util.FtpPoolHelper;
import com.github.pagehelper.PageHelper;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.apiCallLogs.mapper.IApiCallLogsMapper;
import com.zjhc.gzwcq.attachment.mapper.IAttachmentMapper;
import com.zjhc.gzwcq.cgrfd.entity.Cgrfd;
import com.zjhc.gzwcq.cgrfd.mapper.ICgrfdMapper;
import com.zjhc.gzwcq.cjffd.entity.Cjffd;
import com.zjhc.gzwcq.cjffd.mapper.ICjffdMapper;
import com.zjhc.gzwcq.czfd.entity.Czfd;
import com.zjhc.gzwcq.czfd.entity.CzfdVo;
import com.zjhc.gzwcq.czfd.mapper.ICzfdMapper;
import com.zjhc.gzwcq.dwtzqkfd.entity.Dwtzqkfd;
import com.zjhc.gzwcq.dwtzqkfd.mapper.IDwtzqkfdMapper;
import com.zjhc.gzwcq.fhbzcfd.entity.Fhbzcfd;
import com.zjhc.gzwcq.fhbzcfd.mapper.IFhbzcfdMapper;
import com.zjhc.gzwcq.hhqy.entity.Hhqy;
import com.zjhc.gzwcq.hhqy.mapper.IHhqyMapper;
import com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd;
import com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo;
import com.zjhc.gzwcq.hhrqkfd.mapper.IHhrqkfdMapper;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.hrhcfd.mapper.IHrhcfdMapper;
import com.zjhc.gzwcq.jbxxb.entity.Jbxxb;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import com.zjhc.gzwcq.jbxxb.mapper.IJbxxbMapper;
import com.zjhc.gzwcq.jbxxb.service.api.IJbxxbService;
import com.zjhc.gzwcq.openApi.IntegrateDataVO;
import com.zjhc.gzwcq.syccfpfd.entity.Syccfpfd;
import com.zjhc.gzwcq.syccfpfd.mapper.ISyccfpfdMapper;
import com.zjhc.gzwcq.xgzjgfd.entity.Xgzjgfd;
import com.zjhc.gzwcq.xgzjgfd.mapper.IXgzjgfdMapper;
import com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd;
import com.zjhc.gzwcq.ygqcyffd.mapper.IYgqcyffdMapper;
import com.zjhc.gzwcq.ywzbb.entity.Ywzbb12VO;
import com.zjhc.gzwcq.ywzbb.mapper.IYwzbbMapper;
import com.zjhc.gzwcq.zrsrfd.entity.Zrsrfd;
import com.zjhc.gzwcq.zrsrfd.mapper.IZrsrfdMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/openApi")
@Api(value = "openApi接口文档", tags = "对外开放接口")
@Slf4j
public class BusOpenApi implements BusOpenApiClient {

    @Autowired
    private IJbxxbService jbxxbService;
    @Autowired
    private IOrganizationService organizationService;

    @Autowired
    private IJbxxbMapper jbxxbMapper;

    @Autowired
    private ICzfdMapper czfdMapper;

    @Autowired
    private ICgrfdMapper cgrfdMapper;

    @Autowired
    private IFhbzcfdMapper fd1TableData;

    @Autowired
    private IXgzjgfdMapper xgzjgfdMapper;

    @Autowired
    private IZrsrfdMapper zrsrfdMapper;

    @Autowired
    private IHrhcfdMapper hrhcfdMapper;

    @Autowired
    private IYgqcyffdMapper ygqcyffdMapper;

    @Autowired
    private ISyccfpfdMapper syccfpfdMapper;

    @Autowired
    private ICjffdMapper cjffdMapper;

    @Autowired
    private IYwzbbMapper ywzbbMapper;

    @Autowired
    private IHhqyMapper hhqyMapper;

    @Autowired
    private IHhrqkfdMapper hhrqkfdMapper;

    @Autowired
    private IDwtzqkfdMapper dwtzqkfdMapper;

    @Autowired
    private IAppInfoMapper iAppInfoMapper;

    /**
     * 分页获取企业基本信息
     */
    @Override
    public List<JbxxbVo> getJbxxbByOrgId(Integer pageNumber, Integer limit, String orgId, String appId) {
        List<String> orgListPag = new ArrayList<>();
        //组织id为空的时候就不需要在校验数据权限
        if (StringUtils.isBlank(orgId)) {
            orgId = iAppInfoMapper.selectAppInfoByAppKey(appId).getOrgId();
            //组织id 为空的时候查询自己的数据权限组织id
            List<SysOrganization> orgListPage = organizationService.getOrgListPage(1, Integer.MAX_VALUE, orgId);
            orgListPag = orgListPage.stream().map(item -> item.getOrganization_id()).collect(Collectors.toList());
        } else {
            //组织id 不为空的时候校验数据权限
            if (!this.selectAppIdAuthorityStatus(appId, orgId)) {
                //说明这个id 不属于这个权限能查询的
                return null;
            }
            //权限通过回显id
            orgListPag.add(orgId);
        }
        return jbxxbService.loadRecentApprovedByOrdId(pageNumber, limit, orgListPag);
    }

    /**
     * 分页获取产权树列表
     */
    @Override
    public List<SysOrganization> getOrgListPage(Integer pageNumber, Integer limit, String appId) {
        AppInfo appInfo = iAppInfoMapper.selectAppInfoByAppKey(appId);
        if (Objects.isNull(appInfo)) {
            return null;
        }
        return organizationService.getOrgListPage(pageNumber, limit, appInfo.getOrgId());
    }

    @Autowired
    IAttachmentMapper attachmentMapper;

    /**
     * 下载文件
     */
    public byte[] downloadFileForOpenApi(@RequestParam("attachmentId") String attachmentId, String appId) {
        ByteArrayOutputStream outStream;
        try {
            //查询当前文件所属的组织
            String ordId = attachmentMapper.selectOrdIdById(attachmentId);
//            校验 当前文件，是否能被此账号请求
            if (!this.selectAppIdAuthorityStatus(appId, ordId)) return null;
            outStream = new ByteArrayOutputStream();
            String ftpPath = attachmentMapper.selectAllById(attachmentId);
            if (!StringUtils.isNotBlank(ftpPath)) {
                return null;
            }
            InputStream fileInputStream = FtpPoolHelper.getInputStreamByName(ftpPath);
            byte[] bytes = new byte[1024];
            int len = 0;
            while ((len = fileInputStream.read(bytes)) != -1) {
                outStream.write(bytes, 0, len);
            }
            fileInputStream.close();
            outStream.close();
            outStream.flush();
            return outStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public IntegrateDataVO getJbxxbDetailByOrgId(@RequestParam("orgId") String orgId, String appId) {
        Jbxxb jbxxb;
        String jbxxbId;
        List<Czfd> czfdVos;
        IntegrateDataVO response;
        try {
            //当前传入的组织id和当前账号的权限不符
            if (!this.selectAppIdAuthorityStatus(appId, orgId)) return null;
            //请求参数为空
            if (!Objects.nonNull(orgId)) {
                return null;
            }
            jbxxb = jbxxbMapper.selectRecentOneDataByorgId(orgId);
            //判断组织id是否为假
            if (Objects.isNull(jbxxb)) {
                return null;
            }
            jbxxbId = jbxxb.getId();
            //将id删除, 因为返回不需要id
            jbxxb.setId(null);
            //出资人列表
            czfdVos = czfdMapper.selectAllByJbxxId(jbxxbId);
            //股权列表
            List<Cgrfd> cgrfds = cgrfdMapper.selectAllByJbxxId(jbxxbId);
            //非货币资产列表
            List<Fhbzcfd> fhbzcfdVos = fd1TableData.selectAllByJbxxId(jbxxbId);
            //新国资机构列表
            List<Xgzjgfd> xgzjgfdVos = xgzjgfdMapper.selectAllByJbxxId(jbxxbId);
            //转让受让列表
            List<Zrsrfd> zrsrfds = zrsrfdMapper.selectAllByJbxxId(jbxxbId);
            //划入划出浮动
            List<Hrhcfd> hrhcfdVos = hrhcfdMapper.selectAllByJbxxId(jbxxbId);
            //查询原股权持有方列表
            List<Ygqcyffd> ygqcyffds = ygqcyffdMapper.selectAllByJbxxId(jbxxbId);
            //剩余财产分配列表
            List<Syccfpfd> syccfpfds = syccfpfdMapper.selectAllByJbxxId(jbxxbId);
            //查询承接方数据
            List<Cjffd> cjffds = cjffdMapper.selectAllByJbxxId(jbxxbId);
            //查询产权业务数据
            List<Ywzbb12VO> ywzbb12VOS = ywzbbMapper.selectAllByJbxxId(jbxxbId);
            String ywzbb12VOJson = JSON.toJSONString(ywzbb12VOS);
            //查询出资企业 | 合伙企業
            List<Hhqy> hhqies = hhqyMapper.selectAllByJbxxId(jbxxbId);
            String hhqiesJson = JSON.toJSONString(hhqies);
            //合伙人情况列表
            List<Hhrqkfd> hhrqkfds = hhrqkfdMapper.selectAllByJbxxId(jbxxbId);
            //对外投资情况列表
            List<Dwtzqkfd> dwtzqkfds = dwtzqkfdMapper.selectAllByJbxxId(jbxxbId);
            response =
                    new IntegrateDataVO(jbxxb, czfdVos, cgrfds, fhbzcfdVos, xgzjgfdVos, zrsrfds
                            , hrhcfdVos, ygqcyffds, syccfpfds, cjffds, ywzbb12VOJson, hhqiesJson, hhrqkfds, dwtzqkfds);
        } catch (Exception e) {
            log.error("同步企业数据失败err：\t", e);
            throw e;
        }
        return response;
    }

    @Override
    public   List<JbxxbInfo> getJbxxbOrCzrInfoDetail(Integer pageNumber,String appId, Integer limit, String orgId) {

        //请求参数不为空
        if (StringUtils.isNotBlank(orgId)) {
            //当前传入的组织id和当前账号的权限不符
            if (!this.selectAppIdAuthorityStatus(appId, orgId)) return null;
        }
        AppInfo appInfo = iAppInfoMapper.selectAppInfoByAppKey(appId);
        pageNumber = Objects.isNull(pageNumber) ? 1 : pageNumber;
        limit = Objects.isNull(limit) ||  limit > 1000 ? 1000 : limit;
        PageHelper.startPage(pageNumber, limit,false);
        List<JbxxbInfo> jbxxbOrCzrDetail = jbxxbMapper.getJbxxbOrCzrDetail(orgId, appInfo.getOrgId());
        jbxxbOrCzrDetail.stream().forEach(item->{
            String id = item.getId();
            String jbZczbz = item.getJbZczbz();
            if (StringUtils.equals(item.getBusinessNature(), "1") ){
                List<CzfdVo> czfdVos = czfdMapper.selectByJbxxId(id);
                if(!czfdVos.isEmpty()){
                    czfdVos.stream().forEach(item1->{
                        item1.setFdCzebzStr(jbZczbz);
                        item1.setFdRjzbbzStr(jbZczbz);
                        item1.setFdSjzczbbzStr(jbZczbz);
                    });
                }
                item.setCzTableData(czfdVos);
            }else {
                List<HhrqkfdVo> hhrqkfdVo = hhrqkfdMapper.selectInfoByJbxxId(id);
                item.setHhrqkfdList(hhrqkfdVo);
            }
        });
        return jbxxbOrCzrDetail;
    }

    @Override
    public  Long getJbxxbOrCzrInfoDetailTotal(String appId, String orgId) {

        //请求参数不为空
        if (StringUtils.isNotBlank(orgId)) {
            //当前传入的组织id和当前账号的权限不符
            if (!this.selectAppIdAuthorityStatus(appId, orgId)) return null;
        }
        AppInfo appInfo = iAppInfoMapper.selectAppInfoByAppKey(appId);
        Long total = jbxxbMapper.getJbxxbOrCzrDetailTotal(orgId, appInfo.getOrgId());
        return total;
    }
    @Autowired
    private IApiCallLogsMapper  iApiCallLogsMapper;
    @Override
    public void insetApiCallLogs(ApiCallLogs apiCallLogs) {
        apiCallLogs.setCreateTime(new Date());
        apiCallLogs.setLastUpdateTime(new Date());
        iApiCallLogsMapper.insert(apiCallLogs);
    }

    @Override
    public AppInfo selectAppInfo(String appId) {
        AppInfo appInfo = iAppInfoMapper.selectAppInfoByAppKey(appId);
        return appInfo;
    }

    @Autowired
    private OrganizationMapper organizationMapperl;
    /**
     * 查询是否有权限可以查询当前组织的数据
     *
     * @param appId
     * @return
     */
    private boolean selectAppIdAuthorityStatus(String appId, String orgId) {
        AppInfo appInfo = iAppInfoMapper.selectAppInfoByAppKey(appId);
        if (Objects.isNull(appInfo)) {
            return false;
        }
        String appOrgId = appInfo.getOrgId();
        //可以直接返回
        if (StringUtils.equals(appOrgId, orgId)) return true;

        Long countNumber = organizationMapperl.containsKey(appOrgId, orgId);
             //查看需要查询的id 是否在自己名下  大于0说明有权限
        return countNumber > 0;
    }
}
