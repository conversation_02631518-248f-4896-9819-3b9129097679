package com.zjhc.gzwcq.screen.service.impl;

import com.boot.IAdmin.common.utils.DateUtils;
import com.boot.IAdmin.dict.mapper.DictionaryMapper;
import com.boot.IAdmin.dict.model.DictionaryVo;
import com.boot.iAdmin.access.mapper.OrganizationMapper;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.access.model.user.SysUser;
import com.boot.iAdmin.access.service.api.IOrganizationService;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.sun.org.apache.bcel.internal.generic.NEW;
import com.zjhc.gzwcq.businessInfo.entity.BusinessInfoParam;
import com.zjhc.gzwcq.businessInfo.mapper.IBusinessInfoMapper;
import com.zjhc.gzwcq.jbxxb.ZzxsEnum;
import com.zjhc.gzwcq.jbxxb.mapper.IJbxxbMapper;
import com.zjhc.gzwcq.monitorwarn.entity.MonitorwarnParam;
import com.zjhc.gzwcq.monitorwarn.mapper.IMonitorwarnMapper;
import com.zjhc.gzwcq.screen.entity.TimelinessRate;
import com.zjhc.gzwcq.screen.service.api.IScreenService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ScreenServiceImpl implements IScreenService {

    @Resource
    private IBusinessInfoMapper businessInfoMapper;

    @Resource
    private IOrganizationService organizationService;

    @Resource
    private IJbxxbMapper jbxxbMapper;

    @Resource
    private IMonitorwarnMapper monitorwarnMapper;

    @Resource
    private OrganizationMapper organizationMapper;

    @Autowired(required = false)
    private DictCacheStrategy dictCacheStrategy;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    private static final Set<Integer> ZZXS_OTHER_VALS;

    static {
        ZZXS_OTHER_VALS = new HashSet<>(3);
        ZZXS_OTHER_VALS.add(10);
        ZZXS_OTHER_VALS.add(20);
        ZZXS_OTHER_VALS.add(30);
    }

    /**
     * 待办事项数据统计
     */
    @Override
    public Map<String, Integer> todoList() {
        Map<String, Integer> map = new HashMap<>(5);
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        SysOrganization org = organizationService.getOrgByOrgId(user.getOrganization_id());
        BusinessInfoParam param = new BusinessInfoParam();
        MonitorwarnParam monitorwarnParam = new MonitorwarnParam();
        //当企业为1级或者行政机构时才需要设置初审和复审
//        if ("01".equals(org.getBusiness_level()) || StringUtils.equals("1", org.getBusinesstype())) {
        if (StringUtils.isNotEmpty(user.getAudit_level())) {
            param.setAuditLevel(user.getAudit_level());
            monitorwarnParam.setAuditLevel(user.getAudit_level());
        }
//        }
        //查询当前登录人组织被托管的组织列表
        List<String> auditHostingList = organizationService.dataPermissionsForApproval();
        monitorwarnParam.setAuditHostingList(auditHostingList);
        //"1"->待审核
        param.setTodoType("1");
        map.put("todoNum", businessInfoMapper.loginUserTodoOrReturnNum(param, auditHostingList));
        //"2"->上级退回
        param.setTodoType("2");
        param.setUserId(user.getUser_id());
        map.put("returnNum", businessInfoMapper.loginUserTodoOrReturnNum(param, auditHostingList));
        //未办工商
        map.put("wbgsNum", jbxxbMapper.selectWbgsNumByLoginUser(auditHostingList));
        //预警审核
        map.put("warnAuditNum", monitorwarnMapper.selectWarnAuditNum(monitorwarnParam));
        //预警监控
        monitorwarnParam.setUnitid(user.getOrganization_id());
        monitorwarnParam.setChangeCategory("1"); //自动
        map.put("warnMonitorNum", (int) monitorwarnMapper.queryTotalMonitorwarns(monitorwarnParam));
        return map;
    }

    /**
     * 企业户数统计(按组织形式)
     */
    @Override
    public List<Map<String, Object>> companyNumByZzxs(String unitId) {
        List<Map<String, Object>> mapList = new ArrayList<>(3);
        Map<String, Object> map1 = new HashMap<>(4);
        map1.put("text", "公司制企业");
        Map<String, Object> map2 = new HashMap<>(4);
        map2.put("text", "非公司制企业");
        Map<String, Object> map3 = new HashMap<>(4);
        map3.put("text", "其他类型企业、机构和单位等");
        mapList.add(map1);
        mapList.add(map2);
        mapList.add(map3);
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (StringUtils.isBlank(unitId)) {
            unitId = user.getOrganization_id();
        }
        //查询所选组织被托管的组织列表
        List<String> auditHostingList = organizationMapper.selectAuditHosting(unitId);
        auditHostingList.add(unitId);
        List<Map<String, Object>> zzxsList = jbxxbMapper.companyNumByZzxs(auditHostingList);
        //公司制
        final List<Map<String, Object>> gonsizhi = zzxsList.stream().filter(m -> {
            final int val = Integer.parseInt(m.get("val").toString());
            return val >= 10 && val < 20;
        }).collect(Collectors.toList());
        long total1 = gonsizhi.stream().mapToLong(m -> (Long) m.get("num")).sum();
        if (total1 == 0) {
            mapList.remove(map1);
        } else {
            for (Map<String, Object> map : gonsizhi) {
                if (ZZXS_OTHER_VALS.contains(Integer.parseInt(map.get("val").toString()))) {
                    map.put("text", "其他");
                }
                final BigDecimal rate = BigDecimal.valueOf(100L * (Long) map.get("num")).divide(BigDecimal.valueOf(total1), 2, RoundingMode.HALF_UP);
                map.put("rate", rate);
            }
            map1.put("list", gonsizhi);
            map1.put("num", total1);
        }
        //非公司制
        final List<Map<String, Object>> notgonsizhi = zzxsList.stream().filter(m -> {
            final int val = Integer.parseInt(m.get("val").toString());
            return val >= 20 && val < 30;
        }).collect(Collectors.toList());
        long total2 = notgonsizhi.stream().mapToLong(m -> (Long) m.get("num")).sum();
        if (total2 == 0) {
            mapList.remove(map2);
        } else {
            for (Map<String, Object> map : notgonsizhi) {
                if (ZZXS_OTHER_VALS.contains(Integer.parseInt(map.get("val").toString()))) {
                    map.put("text", "其他");
                }
                final BigDecimal rate = BigDecimal.valueOf(100L * (Long) map.get("num")).divide(BigDecimal.valueOf(total2), 2, RoundingMode.HALF_UP);
                map.put("rate", rate);
            }
            map2.put("list", notgonsizhi);
            map2.put("num", total2);
        }
        //其他
        final List<Map<String, Object>> others = zzxsList.stream().filter(m -> {
            final int val = Integer.parseInt(m.get("val").toString());
            return val >= 30;
        }).collect(Collectors.toList());
        long total3 = others.stream().mapToLong(m -> (Long) m.get("num")).sum();
        if (total3 == 0) {
            mapList.remove(map3);
        } else {
            for (Map<String, Object> map : others) {
                if (ZZXS_OTHER_VALS.contains(Integer.parseInt(map.get("val").toString()))) {
                    map.put("text", "其他");
                }
                final BigDecimal rate = BigDecimal.valueOf(100L * (Long) map.get("num")).divide(BigDecimal.valueOf(total3), 2, RoundingMode.HALF_UP);
                map.put("rate", rate);
            }
            map3.put("list", others);
            map3.put("num", total3);
        }
        final BigDecimal all = BigDecimal.valueOf(total1 + total2 + total3);
        if (BigDecimal.ZERO.compareTo(all) < 0) {
            if (total1 > 0) {
                map1.put("rate", BigDecimal.valueOf(100L * total1).divide(all, 2, RoundingMode.HALF_UP));
            }
            if (total2 > 0) {
                map2.put("rate", BigDecimal.valueOf(100L * total2).divide(all, 2, RoundingMode.HALF_UP));
            }
            if (total3 > 0) {
                map3.put("rate", BigDecimal.valueOf(100L * total3).divide(all, 2, RoundingMode.HALF_UP));
            }
        }
        return mapList.parallelStream().sorted(Comparator.comparing(m -> Long.parseLong(m.get("num").toString()))).collect(Collectors.toList());
    }

    /**
    * @description: TODO 企业户数统计(按组织形式) 新
    * @author: hhy
    * @date: 2025/7/25 16:51
    * @param unitId 参数说明
    * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
    */
    @Override
    public List<Map<String, Object>> companyNumByZzxsNew(String unitId) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (StringUtils.isBlank(unitId)) {
            unitId = user.getOrganization_id();
        }
        //查询所选组织被托管的组织列表
        List<String> auditHostingList = organizationMapper.selectAuditHosting(unitId);
        auditHostingList.add(unitId);
        List<Map<String, Object>> zzxsList = jbxxbMapper.companyNumByZzxsNew(auditHostingList);
        Long num = zzxsList.stream().mapToLong(m -> (Long) m.get("num")).sum();
        Map<String, List<Map<String, Object>>> zzxs = zzxsList.stream().collect(Collectors.groupingBy(m -> m.get("zzxs").toString()));
        List<DictionaryVo> vos = jbxxbMapper.loadZzxs();
        vos.stream().forEach(vo -> {
            Map<String, Object> map = new HashMap<>();
            map.put("text", vo.getText());
            List<Map<String, Object>> list = zzxs.get(vo.getVal());
            if (CollectionUtils.isEmpty(list)){
                return;
            }
            Long num1 = list.stream().mapToLong(m -> (Long) m.get("num")).sum();
            map.put("num", num1);
            final BigDecimal rate = BigDecimal.valueOf(100L * num).divide(BigDecimal.valueOf(num1), 2, RoundingMode.HALF_UP);
            map.put("rate", rate);
            mapList.add(map);
        });
            return mapList;
    }

    
    /**
     * 企业户数统计(按组织形式)V2 - 直接从字典获取组织形式名称
     * <AUTHOR>
     */
    @Override
    public List<Map<String, Object>> companyNumByZzxsV2(String unitId) {
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (StringUtils.isBlank(unitId)) {
            unitId = user.getOrganization_id();
        }
        //查询所选组织被托管的组织列表
        List<String> auditHostingList = organizationMapper.selectAuditHosting(unitId);
        auditHostingList.add(unitId);
        List<Map<String, Object>> zzxsList = jbxxbMapper.companyNumByZzxs(auditHostingList);

        // 直接返回从数据库查询的结果，不进行分类处理
        // 数据库查询已经包含了字典表的text字段，直接使用即可
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> item : zzxsList) {
            Map<String, Object> resultItem = new HashMap<>();
            resultItem.put("val", item.get("val"));
            resultItem.put("text", item.get("text"));
            resultItem.put("num", item.get("num"));

            // 计算占比，基于总数计算
            long totalNum = zzxsList.stream().mapToLong(m -> (Long) m.get("num")).sum();
            if (totalNum > 0) {
                BigDecimal rate = BigDecimal.valueOf(100L * (Long) item.get("num"))
                        .divide(BigDecimal.valueOf(totalNum), 2, RoundingMode.HALF_UP);
                resultItem.put("rate", rate);
            } else {
                resultItem.put("rate", BigDecimal.ZERO);
            }

            result.add(resultItem);
        }

        // 按企业数量降序排列
        return result.stream()
                .sorted(Comparator.comparing((Map<String, Object> m) -> (Long) m.get("num")).reversed())
                .collect(Collectors.toList());
    }


    /**
     * 企业户数统计(按企业级次)
     */
    @Override
    public List<Map<String, Object>> companyNumByLevel(String unitId) {
        List<Map<String, Object>> list = new ArrayList<>(4);
        Map<String, Object> level4 = new HashMap<>(4);
        level4.put("text", "4级及以下企业");
        level4.put("num", 0);
        level4.put("rate", 0.00);
        level4.put("level", "04");
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (StringUtils.isBlank(unitId)) {
            unitId = user.getOrganization_id();
        }
        //查询所选组织被托管的组织列表
        List<String> auditHostingList = organizationMapper.selectAuditHosting(unitId);
        auditHostingList.add(unitId);
        List<Map<String, Object>> mapList = organizationMapper.selectChildrenOrgs(auditHostingList);
        //登录人本级及以下没有任何一家有级次的企业,全返回0
        if (mapList.isEmpty()) {
            list.add(level4);
            for (int i = 3; i > 0; i--) {
                Map<String, Object> m = new HashMap<>(4);
                m.put("text", i + "级企业");
                m.put("num", 0);
                m.put("rate", 0.00);
                m.put("level", "0" + i);
                list.add(m);
            }
            return list;
        }
        long total = mapList.stream().mapToLong(m -> (Long) m.get("num")).sum();
        for (Map<String, Object> map : mapList) {
            final BigDecimal rate = BigDecimal.valueOf(100L * (Long) map.get("num")).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
            map.put("rate", rate);
            final int level = Integer.parseInt(map.get("level").toString());
            if (level < 4) {
                list.add(map);
            }
        }
        final long level4Sum = mapList.stream().filter(m -> Integer.parseInt(m.get("level").toString()) >= 4).mapToLong(m -> (Long) m.get("num")).sum();
        if (level4Sum > 0) {
            final BigDecimal level4Rate = BigDecimal.valueOf(100 * level4Sum).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
            level4.put("num", level4Sum);
            level4.put("rate", level4Rate);
            list.add(level4);
        }
        return list.parallelStream().sorted(Comparator.comparing(
                (Map<String, Object> m) -> Integer.parseInt(m.get("level").toString())).reversed()).collect(Collectors.toList());
    }

    /**
     * 企业户数统计(按季度及企业类别)
     */
    @Override
    public List<Map<String, Object>> companyNumBySeason(String unitId, String year) {
        if (StringUtils.isBlank(year)) {
            year = DateUtils.dateFormat(new Date(), "yyyy");
        }
        //所选年度每个季度最后一天
        List<String> yearSeasonLastDay = this.getYearSeasonLastDayByYear(Integer.parseInt(year));
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (StringUtils.isBlank(unitId)) {
            unitId = user.getOrganization_id();
        }
        //查询所选组织被托管的组织列表
        List<String> auditHostingList = organizationMapper.selectAuditHosting(unitId);
        auditHostingList.add(unitId);
        //企业类别
        final List<DictionaryVo> qylbList = dictCacheStrategy.getCache("QYLB").getDictionaryList();
        List<Map<String, Object>> list = new ArrayList<>();
        for (String seasonLastDay : yearSeasonLastDay) {
            list.addAll(jbxxbMapper.companyNumBySeason(auditHostingList, seasonLastDay));
        }
        List<Map<String, Object>> mapList = new ArrayList<>(qylbList.size());
        for (DictionaryVo vo : qylbList) {
            Map<String, Object> map = new HashMap<>(2 + yearSeasonLastDay.size());
            map.put("val", vo.getVal());
            map.put("text", vo.getText());
            for (int i = 1; i <= yearSeasonLastDay.size(); i++) {
                int finalI = i;
                Optional<Map<String, Object>> any = list.stream().filter(m ->
                        vo.getVal().equals(m.get("val")) && finalI == Integer.parseInt(m.get("season").toString())).findAny();
                if (any.isPresent()) {
                    map.put("season" + i, any.get().get("num"));
                } else {
                    map.put("season" + i, 0);
                }
            }
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 组织下拉树获取
     */
    @Override
    public List<DictionaryVo> loadOrgs() {
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        SysOrganization org = organizationService.getOrgByOrgId(user.getOrganization_id());
        //一级及以下企业无下拉选择框,按默认展示本级及以下即可
        if (org.getBusiness_level() != null && Integer.parseInt(org.getBusiness_level()) >= 1) {
            return null;
        }
        List<DictionaryVo> vos = dictionaryMapper.getAllLevelByTypeCode("INDEX_JGJG");
        for (DictionaryVo vo : vos) {
            if (vo.getLevel() < 3 && org.getOrganization_id().equals(vo.getVal())) {
                final List<DictionaryVo> collect = vos.stream().filter(v -> v.getParent().equals(vo.getId())).collect(Collectors.toList());
                DictionaryVo dictionaryVo = new DictionaryVo();
                dictionaryVo.setVal(org.getOrganization_id());
                dictionaryVo.setText(org.getOrganization_name());
                List<DictionaryVo> list = new ArrayList<>(collect.size() + 1);
                list.add(dictionaryVo);
                list.addAll(collect);
                return list;
            }
        }
        return null;
    }

    /**
     * 根据所选年份获取该年季度最后一天
     * 如果所选年份等于今年,则返回今年当前时间所在季度及之前的季度
     */
    private List<String> getYearSeasonLastDayByYear(int year) {
        Date now = new Date();
        int nowYear = Integer.parseInt(DateUtils.dateFormat(now, "yyyy"));
        int seasonNum;
        List<String> everySeasonLastDay = Arrays.asList(year + "0331", year + "0630", year + "0930", year + "1231");
        //所选年度就是当前年度
        if (nowYear == year) {
            int nowMonth = Integer.parseInt(DateUtils.dateFormat(now, "MM"));
            seasonNum = (nowMonth - 1) / 3 + 1;
            return everySeasonLastDay.subList(0, seasonNum);
        } else if (nowYear < year) {
            throw new RuntimeException("所选年份不可大于当前年份");
        }
        return everySeasonLastDay;
    }

    @Override
    public Map<String, Object> businessAssessment(String type, String year) {
        Map<String, Object> re = new HashMap<>();
        //当前登录人相关信息
        SysUser user = (SysUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, Object> tlMap = new HashMap<>();
        //查询所有登记数据列表
        List<TimelinessRate> tlList = jbxxbMapper.queryTimelinessRateList(type, user.getCurrOrgId(), year);
        int all = 0, tl = 0; //分母/分子
        //将所有BUSINESS_INFO_ID拉出来查询
        List<String> idList = tlList.stream().map(TimelinessRate::getBusinessInfoId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList)) {
            //所有提交流程第一次到国资委的时间，流程以BUSINESS_INFO_ID为准
            List<TimelinessRate> dateList = jbxxbMapper.queryAllApprovalDate(idList);
            Date nowDate = new Date();

            for (TimelinessRate t : tlList) {
                //不是已办工商的，算在及时里
                if ("1".equals(t.getJbSfybgs())) {
                    //根据BUSINESS_INFO_ID筛选国资委时间
                    Optional<TimelinessRate> op = dateList.stream().filter(d -> d.getBusinessInfoId()
                            .equals(t.getBusinessInfoId())).findFirst();
                    if (op.isPresent()) {
                        if (op.get().getAfProcessDate() != null && t.getJbGsdjrq() != null) {
                            //一级企业审核通过的时间间隔在30天内
                            long differTime = op.get().getAfProcessDate().getTime() - t.getJbGsdjrq().getTime();
                            if ((differTime / (1000 * 3600 * 24)) <= 30) {
                                tl++;
                            }
                        }
                        all++;
                    } else {  //未到国资委的
                        long differTime = nowDate.getTime() - t.getJbGsdjrq().getTime();
                        if ((differTime / (1000 * 3600 * 24)) > 30) {  //30天内不计入登记数量，30天外算不及时
                            all++;
                        }
                    }
                } else {  //已办工商不为是的，直接算及时
                    all++;
                    tl++;
                }
            }
        }
        tlMap.put("allNum", all);
        tlMap.put("tlNum", tl);
        tlMap.put("rate", all == 0 ? 0 : new BigDecimal(tl).divide(new BigDecimal(all), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
        re.put("up", tlMap);


        //退回率
        //将所有BUSINESS_INFO_ID拉出来查询
        List<String> infoIdList = jbxxbMapper.queryAllSubmitList(type, user.getCurrOrgId(), year);
        int returnNum = 0;
        int submitNum = infoIdList.size();
        if (CollectionUtils.isNotEmpty(infoIdList)) {
            returnNum = jbxxbMapper.queryAllReturnList(infoIdList, year);
        }
        BigDecimal rate = submitNum == 0 ? BigDecimal.ZERO : new BigDecimal(returnNum).divide(new BigDecimal(submitNum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("submitNum", submitNum);
        returnMap.put("returnNum", returnNum);
        returnMap.put("rate", rate);
        re.put("down", returnMap);
        //待审批
        int waitInt = 0 ;
        if (CollectionUtils.isNotEmpty(infoIdList)) {
             waitInt = jbxxbMapper.queryAllWaitList(infoIdList, year);
        }
        Map<String, Object> passMap = new HashMap<>();
         int passNum = (int) (submitNum - returnNum - waitInt);
        BigDecimal subtract = passNum == 0 || submitNum == 0 ? BigDecimal.ZERO : (new BigDecimal(passNum).multiply(new BigDecimal("100"))).divide(new BigDecimal(submitNum),2,BigDecimal.ROUND_HALF_UP);
        returnMap.put("submitNum", submitNum);
        returnMap.put("passNum", passNum);
        returnMap.put("rate", subtract);
        re.put("pass", passMap);
        return re;
    }
}
