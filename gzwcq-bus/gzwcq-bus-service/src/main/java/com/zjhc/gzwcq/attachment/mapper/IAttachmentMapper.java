package com.zjhc.gzwcq.attachment.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.attachment.entity.Attachment;
import com.zjhc.gzwcq.attachment.entity.AttachmentVo;
import com.zjhc.gzwcq.attachment.entity.AttachmentParam;
import org.apache.ibatis.annotations.Param;

public interface IAttachmentMapper {
	
	/*保存对象*/
	void insert(Attachment attachment);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Attachment attachment);
	
	/**更新*/
	void update(Attachment attachment);
	
	/*分页查询对象*/
	List<AttachmentVo> queryAttachmentForList(AttachmentParam attachmentParam);
	
	/*数据总量查询*/
	long queryTotalAttachments(AttachmentParam attachmentParam);
	
	/*根据主键查询对象*/
	Attachment selectAttachmentByPrimaryKey(Attachment attachment);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Attachment> selectForList(Attachment attachment);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Attachment> selectForUnique(Attachment attachment);

	String selectAllById(@Param("id") String id);

	/**
	 * 查询当前文件所属的组织id
	 * @param id
	 * @return
	 */
	String selectOrdIdById(@Param("id") String id);
}