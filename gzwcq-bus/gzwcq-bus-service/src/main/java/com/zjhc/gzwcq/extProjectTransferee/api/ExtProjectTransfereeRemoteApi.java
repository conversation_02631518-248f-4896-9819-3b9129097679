package com.zjhc.gzwcq.extProjectTransferee.api;

import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.extProjectTransferee.client.ExtProjectTransfereeFeignClient;
import com.zjhc.gzwcq.extProjectTransferee.service.api.IExtProjectTransfereeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransferee;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeParam;
import com.zjhc.gzwcq.extProjectTransferee.entity.ExtProjectTransfereeVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/extProjectTransfereeRemoteApi")
@Api(value="extProjectTransferee接口文档",tags="项目受让方")
public class ExtProjectTransfereeRemoteApi implements ExtProjectTransfereeFeignClient {
  
  	@Autowired
	private IExtProjectTransfereeService extProjectTransfereeService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param ExtProjectTransferee
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<ExtProjectTransfereeVo> queryByPage(@RequestBody ExtProjectTransfereeParam extProjectTransfereeParam) {
        BootstrapTableModel<ExtProjectTransfereeVo> model = new BootstrapTableModel<ExtProjectTransfereeVo>();
		SysUser user = (SysUser) SpringSecurityUserTools.instance().getUser(null);
		extProjectTransfereeParam.setOrganizationId(user.getOrganization_id());
		model.setRows(extProjectTransfereeService.queryExtProjectTransfereeByPage(extProjectTransfereeParam));
		model.setTotal(extProjectTransfereeService.queryTotalExtProjectTransferees(extProjectTransfereeParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody ExtProjectTransferee extProjectTransferee){
    	extProjectTransfereeService.insert(extProjectTransferee);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	extProjectTransfereeService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	extProjectTransfereeService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody ExtProjectTransferee extProjectTransferee){
    	extProjectTransfereeService.updateIgnoreNull(extProjectTransferee);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody ExtProjectTransferee extProjectTransferee){
    	extProjectTransfereeService.update(extProjectTransferee);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public ExtProjectTransferee selectExtProjectTransfereeByPrimaryKey(@RequestBody ExtProjectTransferee extProjectTransferee){
  		return extProjectTransfereeService.selectExtProjectTransfereeByPrimaryKey(extProjectTransferee);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<ExtProjectTransferee> selectForList(@RequestBody ExtProjectTransferee extProjectTransferee){
    	return extProjectTransfereeService.selectForList(extProjectTransferee);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody ExtProjectTransferee extProjectTransferee){
    	return extProjectTransfereeService.validateUniqueParam(extProjectTransferee);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody ExtProjectTransferee extProjectTransferee){
    	extProjectTransfereeService.saveOne(extProjectTransferee);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody ExtProjectTransferee[] objs){
    	extProjectTransfereeService.multipleSaveAndEdit(objs);
    };
	
}