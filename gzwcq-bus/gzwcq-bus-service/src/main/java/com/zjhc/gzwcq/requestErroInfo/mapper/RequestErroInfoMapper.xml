<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.requestErroInfo.mapper.IRequestErroInfoMapper">

	<resultMap type="com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfo" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="api_request_info_id" property="apiRequestInfoId"/>
		<result column="org_id" property="orgId"/>
		<result column="qymc" property="qymc"/>
		<result column="xybm" property="xybm"/>
		<result column="error_message" property="errorMessage"/>
		<result column="error_index" property="errorIndex"/>
		<result column="error_type" property="errorType"/>
		<result column="error_level" property="errorLevel"/>
		<result column="process_mode" property="processMode"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoVo" extends="baseResultMap">
		<result column="requst_time" property="requstTime"/>
	</resultMap>

	<sql id="columns">
		id, 
		api_request_info_id, 
		org_id, 
		qymc, 
		xybm, 
		error_message, 
		error_index, 
		error_type, 
		error_level, 
		process_mode
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.api_request_info_id, 
		t.org_id, 
		t.qymc, 
		t.xybm, 
		t.error_message, 
		t.error_index, 
		t.error_type, 
		t.error_level, 
		t.process_mode
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{apiRequestInfoId}, 
		#{orgId}, 
		#{qymc}, 
		#{xybm}, 
		#{errorMessage}, 
		#{errorIndex}, 
		#{errorType}, 
		#{errorLevel}, 
		#{processMode}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null">
			and t.id = #{id}
		</if>
		<if test="apiRequestInfoId != null">
			and t.api_request_info_id = #{apiRequestInfoId}
		</if>
		<if test="orgId != null and orgId != ''">
			and t.org_id = #{orgId}
		</if>
		<if test="qymc != null and qymc != ''">
			and t.qymc = #{qymc}
		</if>
		<if test="xybm != null and xybm != ''">
			and t.xybm = #{xybm}
		</if>
		<if test="errorMessage != null and errorMessage != ''">
			and t.error_message = #{errorMessage}
		</if>
		<if test="errorIndex != null and errorIndex != ''">
			and t.error_index = #{errorIndex}
		</if>
		<if test="errorType != null and errorType != ''">
			and t.error_type = #{errorType}
		</if>
		<if test="errorLevel != null and errorLevel != ''">
			and t.error_level = #{errorLevel}
		</if>
		<if test="processMode != null and processMode != ''">
			and t.process_mode = #{processMode}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfo" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into api_request_erro_info (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update api_request_erro_info set isDeleted = 'Y' where
		id in
		<foreach collection="requestErroInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update api_request_erro_info set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from api_request_erro_info  where
		id in
		<foreach collection="requestErroInfos" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from api_request_erro_info  where id = #{id}
	</delete>
	
	<select id="selectRequestErroInfoByPrimaryKey" resultMap="baseResultMapExt">
		SELECT
			err.id,
			info.requst_time,
			err.error_type,
			err.qymc,
			err.xybm,
			err.error_message,
			err.org_id
		FROM
			(
				select
					t.id,
					t.requst_time
				from
					api_request_result_info t
				where
					t.requst_success = 1
				ORDER BY
					requst_time DESC LIMIT 1
			)info
				JOIN api_request_erro_info err on err.api_request_info_id = info.id
			where
			err.id = #{id}
	</select>

	<update id="updateIgnoreNull">
		update api_request_erro_info
		<set>
			<if test="apiRequestInfoId != null">
				api_request_info_id=#{apiRequestInfoId},
			</if>
			<if test="orgId != null">
				org_id=#{orgId},
			</if>
			<if test="qymc != null">
				qymc=#{qymc},
			</if>
			<if test="xybm != null">
				xybm=#{xybm},
			</if>
			<if test="errorMessage != null">
				error_message=#{errorMessage},
			</if>
			<if test="errorIndex != null">
				error_index=#{errorIndex},
			</if>
			<if test="errorType != null">
				error_type=#{errorType},
			</if>
			<if test="errorLevel != null">
				error_level=#{errorLevel},
			</if>
			<if test="processMode != null">
				process_mode=#{processMode}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update api_request_erro_info
		<set>
			api_request_info_id=#{apiRequestInfoId},
			org_id=#{orgId},
			qymc=#{qymc},
			xybm=#{xybm},
			error_message=#{errorMessage},
			error_index=#{errorIndex},
			error_type=#{errorType},
			error_level=#{errorLevel},
			process_mode=#{processMode}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfo" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			api_request_erro_info t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalRequestErroInfos" parameterType="com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoParam" resultType="java.lang.Long">
		select
			count(id)
		from api_request_erro_info t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryRequestErroInfoForList" parameterType="com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfoParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			api_request_erro_info t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.requestErroInfo.entity.RequestErroInfo" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from api_request_erro_info t
		where t.id != #{id}
			<if test="apiRequestInfoId != null and apiRequestInfoId != ''">
				and t.api_request_info_id = #{apiRequestInfoId}
			</if>
			<if test="orgId != null and orgId != ''">
				and t.org_id = #{orgId}
			</if>
			<if test="qymc != null and qymc != ''">
				and t.qymc = #{qymc}
			</if>
			<if test="xybm != null and xybm != ''">
				and t.xybm = #{xybm}
			</if>
			<if test="errorMessage != null and errorMessage != ''">
				and t.error_message = #{errorMessage}
			</if>
			<if test="errorIndex != null and errorIndex != ''">
				and t.error_index = #{errorIndex}
			</if>
			<if test="errorType != null and errorType != ''">
				and t.error_type = #{errorType}
			</if>
			<if test="errorLevel != null and errorLevel != ''">
				and t.error_level = #{errorLevel}
			</if>
			<if test="processMode != null and processMode != ''">
				and t.process_mode = #{processMode}
			</if>
	</select>

</mapper>