<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.hhrqkfd.mapper.IHhrqkfdMapper">

	<resultMap type="com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="name" property="name"/>
		<result column="hhr_code" property="hhrCode"/>
		<result column="type" property="type"/>
		<result column="category" property="category"/>
		<result column="fd_cze" property="fdCze"/>
		<result column="rjcze" property="rjcze"/>
		<result column="rjczbl" property="rjczbl"/>
		<result column="sjcze" property="sjcze"/>
		<result column="czfs" property="czfs"/>
		<result column="jfqx" property="jfqx"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdVo" extends="baseResultMap">
		<result column="typeStr" property="typeStr"/>
		<result column="categoryStr" property="categoryStr"/>
		<result column="czfsStr" property="czfsStr"/>
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		name, 
		hhr_code,
		type, 
		category,
		fd_cze,
		rjcze, 
		rjczbl, 
		sjcze, 
		czfs, 
		jfqx, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.name, 
		t.hhr_code,
		t.type, 
		t.category,
		t.fd_cze,
		t.rjcze, 
		t.rjczbl, 
		t.sjcze, 
		t.czfs, 
		t.jfqx, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{name}, 
		#{hhrCode},
		#{type}, 
		#{category}, 
		#{fdCze},
		#{rjcze},
		#{rjczbl},
		#{sjcze}, 
		#{czfs}, 
		#{jfqx}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="name != null and name != ''">
			and t.name = #{name}
		</if>
		<if test="hhrCode != null and hhrCode != ''">
			and t.hhr_code = #{hhrCode}
		</if>
		<if test="type != null and type != ''">
			and t.type = #{type}
		</if>
		<if test="category != null and category != ''">
			and t.category = #{category}
		</if>
		<if test="fdCze != null">
			and t.fd_cze = #{fdCze}
		</if>
		<if test="rjcze != null">
			and t.rjcze = #{rjcze}
		</if>
		<if test="rjczbl != null">
			and t.rjczbl = #{rjczbl}
		</if>
		<if test="sjcze != null">
			and t.sjcze = #{sjcze}
		</if>
		<if test="czfs != null and czfs != ''">
			and t.czfs = #{czfs}
		</if>
		<if test="jfqx != null">
			and t.jfqx = #{jfqx}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>


	<select id="selectInfoByJbxxId" resultMap="baseResultMapExt">
		select
			t.id,
			t.jbxx_id,
			t.unitid,
			t.name,
			t.hhr_code,
			t.type,
			t.category,
			t.fd_cze,
			t.rjcze,
			t.rjczbl,
			t.sjcze,
			t.czfs,
			t.jfqx,
			t.create_user,
			t.create_time,
			t.last_update_user,
			t.last_update_time,sd1.text typeStr,sd2.text categoryStr,
			sd3.text czfsStr
		from
			cq_hhrqkfd t
				left join sys_dictionary sd1 on sd1.val=t.type and sd1.type_id=(select id from sys_dictionary where type_code='HHRLX')
				left join sys_dictionary sd2 on sd2.val=t.category and sd2.type_id=(select id from sys_dictionary where type_code='HHRLB')
				left join sys_dictionary sd3 on sd3.val=t.czfs and sd3.type_id=
																   (select id from sys_dictionary where type_code=
																										case when t.type = '1' then 'CZFSPT' else 'CZFSYX' end)
		where t.jbxx_id = #{jbxxId}
	</select>
	<insert id="insert" parameterType="com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_hhrqkfd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_hhrqkfd set isDeleted = 'Y' where
		id in
		<foreach collection="hhrqkfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_hhrqkfd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_hhrqkfd  where
		id in
		<foreach collection="hhrqkfds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_hhrqkfd  where id = #{id}
	</delete>
	
	<select id="selectHhrqkfdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_hhrqkfd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_hhrqkfd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="name != null">
				name=#{name},
			</if>
			<if test="hhrCode != null">
				hhr_code=#{hhrCode},
			</if>
			<if test="type != null">
				type=#{type},
			</if>
			<if test="category != null">
				category=#{category},
			</if>
			<if test="fdCze != null">
				fd_cze=#{fdCze},
			</if>
			<if test="rjcze != null">
				rjcze=#{rjcze},
			</if>
			<if test="rjczbl != null">
				rjczbl=#{rjczbl},
			</if>
			<if test="sjcze != null">
				sjcze=#{sjcze},
			</if>
			<if test="czfs != null">
				czfs=#{czfs},
			</if>
			<if test="jfqx != null">
				jfqx=#{jfqx},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_hhrqkfd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			name=#{name},
			hhr_code=#{hhrCode},
			type=#{type},
			category=#{category},
			fd_cze=#{fdCze},
			rjcze=#{rjcze},
			rjczbl=#{rjczbl},
			sjcze=#{sjcze},
			czfs=#{czfs},
			jfqx=#{jfqx},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_hhrqkfd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalHhrqkfds" parameterType="com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_hhrqkfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryHhrqkfdForList" parameterType="com.zjhc.gzwcq.hhrqkfd.entity.HhrqkfdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_hhrqkfd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.hhrqkfd.entity.Hhrqkfd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_hhrqkfd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="name != null and name != ''">
				and t.name = #{name}
			</if>
			<if test="hhrCode != null and hhrCode != ''">
				and t.hhr_code = #{hhrCode}
			</if>
			<if test="type != null and type != ''">
				and t.type = #{type}
			</if>
			<if test="category != null and category != ''">
				and t.category = #{category}
			</if>
			<if test="fdCze != null and fdCze != ''">
				and t.fd_cze = #{fdCze}
			</if>
			<if test="rjcze != null and rjcze != ''">
				and t.rjcze = #{rjcze}
			</if>
			<if test="rjczbl != null and rjczbl != ''">
				and t.rjczbl = #{rjczbl}
			</if>
			<if test="sjcze != null and sjcze != ''">
				and t.sjcze = #{sjcze}
			</if>
			<if test="czfs != null and czfs != ''">
				and t.czfs = #{czfs}
			</if>
			<if test="jfqx != null">
				and t.jfqx = #{jfqx}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxbId" parameterType="java.lang.String" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>,sd1.text typeStr,sd2.text categoryStr,
		sd3.text czfsStr
		from
		cq_hhrqkfd t
		left join sys_dictionary sd1 on sd1.val=t.type and sd1.type_id=(select id from sys_dictionary where type_code='HHRLX')
		left join sys_dictionary sd2 on sd2.val=t.category and sd2.type_id=(select id from sys_dictionary where type_code='HHRLB')
		left join sys_dictionary sd3 on sd3.val=t.czfs and sd3.type_id=
		    (select id from sys_dictionary where type_code=
			case when t.type = '1' then 'CZFSPT' else 'CZFSYX' end)
		where t.jbxx_id = #{jbxxId}
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_hhrqkfd where jbxx_id = #{jbxxId}
	</delete>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			name,
			hhr_code,
			type,
			category,
			fd_cze,
			rjcze,
			rjczbl,
			sjcze,
			czfs,
			jfqx
		FROM
			`cq_hhrqkfd`
		WHERE
			JBXX_ID =#{jbxxId}
	</select>
</mapper>