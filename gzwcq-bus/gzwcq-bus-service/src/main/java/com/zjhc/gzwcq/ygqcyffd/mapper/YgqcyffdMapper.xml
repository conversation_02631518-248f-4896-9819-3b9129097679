<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.ygqcyffd.mapper.IYgqcyffdMapper">

	<resultMap type="com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="jbxx_id" property="jbxxId"/>
		<result column="unitid" property="unitid"/>
		<result column="datatime" property="datatime"/>
		<result column="floatorder" property="floatorder"/>
		<result column="fd2_ygqcyfmc" property="fd2Ygqcyfmc"/>
		<result column="fd2_zgbz" property="fd2Zgbz"/>
		<result column="fd2_sgjg" property="fd2Sgjg"/>
		<result column="fd2_sjjzcz" property="fd2Sjjzcz"/>
		<result column="fd2_pgjzcz" property="fd2Pgjzcz"/>
		<result column="fd2_sgfs" property="fd2Sgfs"/>
		<result column="fd2_zjyj" property="fd2Zjyj"/>
		<result column="fd2_bz" property="fd2Bz"/>
		<result column="fd2_ygqcyfxz" property="fd2Ygqcyfxz"/>
		<result column="fd2_sggqbl" property="fd2Sggqbl"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		jbxx_id, 
		unitid, 
		datatime, 
		floatorder, 
		fd2_ygqcyfmc, 
		fd2_zgbz, 
		fd2_sgjg, 
		fd2_sjjzcz, 
		fd2_pgjzcz, 
		fd2_sgfs, 
		fd2_zjyj, 
		fd2_bz, 
		fd2_ygqcyfxz, 
		fd2_sggqbl, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.jbxx_id, 
		t.unitid, 
		t.datatime, 
		t.floatorder, 
		t.fd2_ygqcyfmc, 
		t.fd2_zgbz, 
		t.fd2_sgjg, 
		t.fd2_sjjzcz, 
		t.fd2_pgjzcz, 
		t.fd2_sgfs, 
		t.fd2_zjyj, 
		t.fd2_bz, 
		t.fd2_ygqcyfxz, 
		t.fd2_sggqbl, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{jbxxId}, 
		#{unitid}, 
		#{datatime}, 
		#{floatorder}, 
		#{fd2Ygqcyfmc}, 
		#{fd2Zgbz}, 
		#{fd2Sgjg}, 
		#{fd2Sjjzcz}, 
		#{fd2Pgjzcz}, 
		#{fd2Sgfs}, 
		#{fd2Zjyj}, 
		#{fd2Bz}, 
		#{fd2Ygqcyfxz}, 
		#{fd2Sggqbl}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="jbxxId != null and jbxxId != ''">
			and t.jbxx_id = #{jbxxId}
		</if>
		<if test="unitid != null and unitid != ''">
			and t.unitid = #{unitid}
		</if>
		<if test="datatime != null and datatime != ''">
			and t.datatime = #{datatime}
		</if>
		<if test="floatorder != null">
			and t.floatorder = #{floatorder}
		</if>
		<if test="fd2Ygqcyfmc != null and fd2Ygqcyfmc != ''">
			and t.fd2_ygqcyfmc = #{fd2Ygqcyfmc}
		</if>
		<if test="fd2Zgbz != null">
			and t.fd2_zgbz = #{fd2Zgbz}
		</if>
		<if test="fd2Sgjg != null">
			and t.fd2_sgjg = #{fd2Sgjg}
		</if>
		<if test="fd2Sjjzcz != null">
			and t.fd2_sjjzcz = #{fd2Sjjzcz}
		</if>
		<if test="fd2Pgjzcz != null">
			and t.fd2_pgjzcz = #{fd2Pgjzcz}
		</if>
		<if test="fd2Sgfs != null and fd2Sgfs != ''">
			and t.fd2_sgfs = #{fd2Sgfs}
		</if>
		<if test="fd2Zjyj != null and fd2Zjyj != ''">
			and t.fd2_zjyj = #{fd2Zjyj}
		</if>
		<if test="fd2Bz != null and fd2Bz != ''">
			and t.fd2_bz = #{fd2Bz}
		</if>
		<if test="fd2Ygqcyfxz != null and fd2Ygqcyfxz != ''">
			and t.fd2_ygqcyfxz = #{fd2Ygqcyfxz}
		</if>
		<if test="fd2Sggqbl != null">
			and t.fd2_sggqbl = #{fd2Sggqbl}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_ygqcyffd (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_ygqcyffd set isDeleted = 'Y' where
		id in
		<foreach collection="ygqcyffds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_ygqcyffd set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_ygqcyffd  where
		id in
		<foreach collection="ygqcyffds" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_ygqcyffd  where id = #{id}
	</delete>
	
	<select id="selectYgqcyffdByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_ygqcyffd
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_ygqcyffd
		<set>
			<if test="jbxxId != null">
				jbxx_id=#{jbxxId},
			</if>
			<if test="unitid != null">
				unitid=#{unitid},
			</if>
			<if test="datatime != null">
				datatime=#{datatime},
			</if>
			<if test="floatorder != null">
				floatorder=#{floatorder},
			</if>
			<if test="fd2Ygqcyfmc != null">
				fd2_ygqcyfmc=#{fd2Ygqcyfmc},
			</if>
			<if test="fd2Zgbz != null">
				fd2_zgbz=#{fd2Zgbz},
			</if>
			<if test="fd2Sgjg != null">
				fd2_sgjg=#{fd2Sgjg},
			</if>
			<if test="fd2Sjjzcz != null">
				fd2_sjjzcz=#{fd2Sjjzcz},
			</if>
			<if test="fd2Pgjzcz != null">
				fd2_pgjzcz=#{fd2Pgjzcz},
			</if>
			<if test="fd2Sgfs != null">
				fd2_sgfs=#{fd2Sgfs},
			</if>
			<if test="fd2Zjyj != null">
				fd2_zjyj=#{fd2Zjyj},
			</if>
			<if test="fd2Bz != null">
				fd2_bz=#{fd2Bz},
			</if>
			<if test="fd2Ygqcyfxz != null">
				fd2_ygqcyfxz=#{fd2Ygqcyfxz},
			</if>
			<if test="fd2Sggqbl != null">
				fd2_sggqbl=#{fd2Sggqbl},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_ygqcyffd
		<set>
			jbxx_id=#{jbxxId},
			unitid=#{unitid},
			datatime=#{datatime},
			floatorder=#{floatorder},
			fd2_ygqcyfmc=#{fd2Ygqcyfmc},
			fd2_zgbz=#{fd2Zgbz},
			fd2_sgjg=#{fd2Sgjg},
			fd2_sjjzcz=#{fd2Sjjzcz},
			fd2_pgjzcz=#{fd2Pgjzcz},
			fd2_sgfs=#{fd2Sgfs},
			fd2_zjyj=#{fd2Zjyj},
			fd2_bz=#{fd2Bz},
			fd2_ygqcyfxz=#{fd2Ygqcyfxz},
			fd2_sggqbl=#{fd2Sggqbl},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_ygqcyffd t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalYgqcyffds" parameterType="com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdParam" resultType="java.lang.Long">
		select
			count(ID)
		from cq_ygqcyffd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryYgqcyffdForList" parameterType="com.zjhc.gzwcq.ygqcyffd.entity.YgqcyffdParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_ygqcyffd t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.ygqcyffd.entity.Ygqcyffd" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_ygqcyffd t
		where t.id != #{id}
			<if test="jbxxId != null and jbxxId != ''">
				and t.jbxx_id = #{jbxxId}
			</if>
			<if test="unitid != null and unitid != ''">
				and t.unitid = #{unitid}
			</if>
			<if test="datatime != null and datatime != ''">
				and t.datatime = #{datatime}
			</if>
			<if test="floatorder != null and floatorder != ''">
				and t.floatorder = #{floatorder}
			</if>
			<if test="fd2Ygqcyfmc != null and fd2Ygqcyfmc != ''">
				and t.fd2_ygqcyfmc = #{fd2Ygqcyfmc}
			</if>
			<if test="fd2Zgbz != null and fd2Zgbz != ''">
				and t.fd2_zgbz = #{fd2Zgbz}
			</if>
			<if test="fd2Sgjg != null and fd2Sgjg != ''">
				and t.fd2_sgjg = #{fd2Sgjg}
			</if>
			<if test="fd2Sjjzcz != null and fd2Sjjzcz != ''">
				and t.fd2_sjjzcz = #{fd2Sjjzcz}
			</if>
			<if test="fd2Pgjzcz != null and fd2Pgjzcz != ''">
				and t.fd2_pgjzcz = #{fd2Pgjzcz}
			</if>
			<if test="fd2Sgfs != null and fd2Sgfs != ''">
				and t.fd2_sgfs = #{fd2Sgfs}
			</if>
			<if test="fd2Zjyj != null and fd2Zjyj != ''">
				and t.fd2_zjyj = #{fd2Zjyj}
			</if>
			<if test="fd2Bz != null and fd2Bz != ''">
				and t.fd2_bz = #{fd2Bz}
			</if>
			<if test="fd2Ygqcyfxz != null and fd2Ygqcyfxz != ''">
				and t.fd2_ygqcyfxz = #{fd2Ygqcyfxz}
			</if>
			<if test="fd2Sggqbl != null and fd2Sggqbl != ''">
				and t.fd2_sggqbl = #{fd2Sggqbl}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectByJbxxId" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/>,sd3.text fd2YgqcyfxzStr,sd1.text fd2SgfsStr,sd2.text fd2ZjyjStr from cq_ygqcyffd t
		left join sys_dictionary sd1 on sd1.val=t.FD2_SGFS and sd1.parent=(select id from sys_dictionary where type_code='SGFS')
		left join sys_dictionary sd2 on sd2.val=t.FD2_ZJYJ and sd2.parent=(select id from sys_dictionary where type_code='ZJYJ')
		left join sys_dictionary sd3 on sd3.val=t.FD2_YGQCYFXZ and sd3.parent=(select id from sys_dictionary where type_code='YFQCYFXZ')

		where t.jbxx_id = #{jbxxId}
	</select>

	<delete id="deleteByJbxxId" parameterType="java.lang.String">
		delete from cq_ygqcyffd where jbxx_id = #{jbxxId}
	</delete>
	<sql id="selectAllByJbxxIdVo">
			fd2_ygqcyfmc,
			fd2_zgbz,
			fd2_sgjg,
			fd2_sjjzcz,
			fd2_pgjzcz,
			fd2_sgfs,
			fd2_zjyj,
			fd2_bz,
			fd2_ygqcyfxz,
			fd2_sggqbl
	</sql>
	<select id="selectAllByJbxxId" resultMap="baseResultMap">
		SELECT
			<include refid="selectAllByJbxxIdVo"/>
		FROM
			`cq_ygqcyffd`
		WHERE
			JBXX_ID = #{jbxxId}
	</select>
</mapper>