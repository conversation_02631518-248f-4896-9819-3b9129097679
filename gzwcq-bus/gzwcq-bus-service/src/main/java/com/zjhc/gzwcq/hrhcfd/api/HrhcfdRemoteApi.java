package com.zjhc.gzwcq.hrhcfd.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.zjhc.gzwcq.hrhcfd.client.HrhcfdFeignClient;
import com.zjhc.gzwcq.hrhcfd.service.api.IHrhcfdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;
import java.util.Map;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdParam;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdVo;
import com.boot.IAdmin.common.domain.BootstrapTableModel;

@RestController
@RequestMapping(value="/hrhcfdRemoteApi")
@Api(value="hrhcfd接口文档",tags="划入划出浮动")
public class HrhcfdRemoteApi implements HrhcfdFeignClient {
  
  	@Autowired
	private IHrhcfdService hrhcfdService;
  
  	/**
	 * 分页查询列表
	 * @Title: load
	 * <AUTHOR> @param BootstrapTableModel
	 * @param Hrhcfd
	 * @return String
	 */
  	@ApiOperation(value="分页查询列表")
	public BootstrapTableModel<HrhcfdVo> queryByPage(@RequestBody HrhcfdParam hrhcfdParam) {
        BootstrapTableModel<HrhcfdVo> model = new BootstrapTableModel<HrhcfdVo>();
		model.setRows(hrhcfdService.queryHrhcfdByPage(hrhcfdParam));
		model.setTotal(hrhcfdService.queryTotalHrhcfds(hrhcfdParam));
		return model;
	}

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@ApiOperation(value="新增")
	public void insert(@RequestBody Hrhcfd hrhcfd){
    	hrhcfdService.insert(hrhcfd);
    };

	/**
	 * 按对象中的主键进行删除，
	 */
    @ApiOperation(value="批量删除")
	public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map){
    	hrhcfdService.deleteByPrimaryKeys(map);
    };
  
  	/**
	 * 按对象中的主键进行删除，
	 */
  	@ApiOperation(value="单条删除")
	public void deleteByPrimaryKey(@RequestParam("id") String id){
    	hrhcfdService.deleteByPrimaryKey(id);
    };
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@ApiOperation(value="更新忽略空字段")
	public void updateIgnoreNull(@RequestBody Hrhcfd hrhcfd){
    	hrhcfdService.updateIgnoreNull(hrhcfd);
    };
	
	/**
	* 更新
	*/
  	@ApiOperation(value="更新全部字段")
  	public void update(@RequestBody Hrhcfd hrhcfd){
    	hrhcfdService.update(hrhcfd);
    };  
	
	/**
	 *通过ID查询数据
	 */
  	@ApiOperation(value="根据主键查询")
	public Hrhcfd selectHrhcfdByPrimaryKey(@RequestBody Hrhcfd hrhcfd){
  		return hrhcfdService.selectHrhcfdByPrimaryKey(hrhcfd);
	};
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@ApiOperation(value="查询列表")
	public List<Hrhcfd> selectForList(@RequestBody Hrhcfd hrhcfd){
    	return hrhcfdService.selectForList(hrhcfd);
    };
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	@ApiOperation(value="唯一性校验")
	public boolean validateUniqueParam(@RequestBody Hrhcfd hrhcfd){
    	return hrhcfdService.validateUniqueParam(hrhcfd);
    };
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	@ApiOperation(value="插入更新")
	public void saveOne(@RequestBody Hrhcfd hrhcfd){
    	hrhcfdService.saveOne(hrhcfd);
    };
	
	/**
	* 保存多个对象
	*/
	@ApiOperation(value="批量插入更新")
	public void multipleSaveAndEdit(@RequestBody Hrhcfd[] objs){
    	hrhcfdService.multipleSaveAndEdit(objs);
    };
	
}