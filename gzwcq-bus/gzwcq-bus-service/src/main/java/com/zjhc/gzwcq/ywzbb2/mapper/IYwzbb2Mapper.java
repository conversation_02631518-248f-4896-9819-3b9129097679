package com.zjhc.gzwcq.ywzbb2.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Vo;
import com.zjhc.gzwcq.ywzbb2.entity.Ywzbb2Param;
import org.apache.ibatis.annotations.Param;

public interface IYwzbb2Mapper {
	
	/*保存对象*/
	void insert(Ywzbb2 ywzbb2);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Ywzbb2 ywzbb2);
	
	/**更新*/
	void update(Ywzbb2 ywzbb2);
	
	/*分页查询对象*/
	List<Ywzbb2Vo> queryYwzbb2ForList(Ywzbb2Param ywzbb2Param);
	
	/*数据总量查询*/
	long queryTotalYwzbb2s(Ywzbb2Param ywzbb2Param);
	
	/*根据主键查询对象*/
	Ywzbb2Vo selectYwzbb2ByPrimaryKey(Ywzbb2 ywzbb2);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Ywzbb2> selectForList(Ywzbb2 ywzbb2);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Ywzbb2> selectForUnique(Ywzbb2 ywzbb2);

	/**
	 * 根据jbxxId查询数据
	 */
    Ywzbb2Vo selectByJbxxId(String jbxxId);

    void deleteByJbxxId(String jbxxId);
}