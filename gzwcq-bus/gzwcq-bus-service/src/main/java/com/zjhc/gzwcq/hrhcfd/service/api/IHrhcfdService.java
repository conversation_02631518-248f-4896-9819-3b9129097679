package com.zjhc.gzwcq.hrhcfd.service.api;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.hrhcfd.entity.Hrhcfd;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdVo;
import com.zjhc.gzwcq.hrhcfd.entity.HrhcfdParam;

public interface IHrhcfdService {

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(Hrhcfd hrhcfd);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(Hrhcfd hrhcfd);
	
	/**
	* 更新
	*/
	void update(Hrhcfd hrhcfd);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<HrhcfdVo> queryHrhcfdByPage(HrhcfdParam hrhcfdParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalHrhcfds(HrhcfdParam hrhcfdParam);
  
	
	/**
	 *通过ID查询数据
	 */
	Hrhcfd selectHrhcfdByPrimaryKey(Hrhcfd hrhcfd);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<Hrhcfd> selectForList(Hrhcfd hrhcfd);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(Hrhcfd hrhcfd);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(Hrhcfd hrhcfd);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(Hrhcfd[] objs);
	/**
	 * 根据基本信息表id查数据
	 */
    List<HrhcfdVo> selectByJbxxId(String id);

    void deleteByJbxxId(String jbxxId);
}