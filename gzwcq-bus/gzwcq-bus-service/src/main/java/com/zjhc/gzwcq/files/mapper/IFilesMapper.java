package com.zjhc.gzwcq.files.mapper;

import java.util.Map;
import java.util.List;
import com.zjhc.gzwcq.files.entity.Files;
import com.zjhc.gzwcq.files.entity.FilesVo;
import com.zjhc.gzwcq.files.entity.FilesParam;
import org.apache.ibatis.annotations.Param;

public interface IFilesMapper {
	
	/*保存对象*/
	void insert(Files files);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(Files files);
	
	/**更新*/
	void update(Files files);
	
	/*分页查询对象*/
	List<FilesVo> queryFilesForList(FilesParam filesParam);
	
	/*数据总量查询*/
	long queryTotalFiless(FilesParam filesParam);
	
	/*根据主键查询对象*/
	FilesVo selectFilesByPrimaryKey(Files files);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<Files> selectForList(Files files);
	
	/**
	 * 数据唯一性验证
	 * */
	List<Files> selectForUnique(Files files);
	
}