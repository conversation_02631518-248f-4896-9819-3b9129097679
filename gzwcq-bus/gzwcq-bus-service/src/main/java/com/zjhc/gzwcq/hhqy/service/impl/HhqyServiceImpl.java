package com.zjhc.gzwcq.hhqy.service.impl;

import java.util.Map;
import java.util.List;
import java.util.Date;

import com.zjhc.gzwcq.hhqy.entity.Hhqy;
import com.zjhc.gzwcq.hhqy.entity.HhqyParam;
import com.zjhc.gzwcq.hhqy.entity.HhqyVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zjhc.gzwcq.hhqy.mapper.IHhqyMapper;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.zjhc.gzwcq.hhqy.service.api.IHhqyService;
import com.boot.iAdmin.access.model.user.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;

@Service
public class HhqyServiceImpl implements IHhqyService {
	
	@Autowired
	private IHhqyMapper hhqyMapper;

	@Override
	public void deleteByJbxxId(String jbxxId) {
		hhqyMapper.deleteByJbxxId(jbxxId);
	}

	@Transactional(rollbackFor=Exception.class)
	public void insert(Hhqy hhqy){
		hhqy.setCreateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//创建人
		hhqy.setCreateTime(new Date());//创建时间
		hhqy.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hhqy.setLastUpdateTime(new Date());//更新时间
		hhqyMapper.insert(hhqy);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		hhqyMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		hhqyMapper.deleteByPrimaryKey(id);
	}
	
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(Hhqy hhqy){
		hhqy.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hhqy.setLastUpdateTime(new Date());//更新时间
		hhqyMapper.updateIgnoreNull(hhqy);
	}
	
	/**
	* 更新
	*/
  	@Transactional(rollbackFor=Exception.class)
	public void update(Hhqy hhqy){
		hhqy.setLastUpdateUser(((SysUser)SpringSecurityUserTools.instance().getUser(null)).getUser_id());//更新人
		hhqy.setLastUpdateTime(new Date());//更新时间
		hhqyMapper.update(hhqy);
	}
	
	public List<HhqyVo> queryHhqyByPage(HhqyParam hhqyParam) {
      	//分页
      	PageHelper.startPage(hhqyParam.getPageNumber(),hhqyParam.getLimit(),false);
		return hhqyMapper.queryHhqyForList(hhqyParam);
	}
	

	public Hhqy selectHhqyByPrimaryKey(Hhqy Hhqy) {
		return hhqyMapper.selectHhqyByPrimaryKey(Hhqy);
	}
	
	public long queryTotalHhqys(HhqyParam hhqyParam) {
		return hhqyMapper.queryTotalHhqys(hhqyParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	public List<Hhqy> selectForList(Hhqy hhqy){
		return hhqyMapper.selectForList(hhqy);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(Hhqy hhqy) {
		return hhqyMapper.selectForUnique(hhqy).size() == 0;
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(Hhqy hhqy) {
		if(StringUtils.isBlank(hhqy.getId())) {
			this.insert(hhqy);
		}else {
			this.updateIgnoreNull(hhqy);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(Hhqy[] objs) {
		for(Hhqy hhqy : objs) {
			this.saveOne(hhqy);
		}
	}

	@Override
	public HhqyVo selectByJbxxbId(String jbxxId) {
		return hhqyMapper.selectByJbxxbId(jbxxId);
	}
}
