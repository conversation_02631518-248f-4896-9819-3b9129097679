<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
		<artifactId>gzwcq-bus</artifactId>
		<version>1.0.0</version>
	</parent>
	<groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-bus</groupId>
	<artifactId>gzwcq-bus-service</artifactId>

	<dependencies>

		<!--Nacos服务注册/发现依赖 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--Nacos配置中心 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>

		<!-- 链路追踪 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-sleuth</artifactId>
		</dependency>

		<!-- <dependency> <groupId>org.springframework.cloud</groupId> <artifactId>spring-cloud-starter-zipkin</artifactId> 
			</dependency> -->

		<!-- RPC调用 feign/rabbion/hystrix -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>

		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-httpclient</artifactId>
		</dependency>

		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-access</groupId>
			<artifactId>gzwcq-access-service-starter</artifactId>
			<version>${wfw.version}</version>
		</dependency>

		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-bus</groupId>
			<artifactId>gzwcq-bus-client</artifactId>
			<version>${wfw.version}</version>
		</dependency>

		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
			<artifactId>gzwcq-ftp-starter</artifactId>
			<version>${wfw.version}</version>
		</dependency>

	</dependencies>

	<build>
		<!-- mvn clean install -->
		<finalName>${artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>1.4.2.RELEASE</version><!--$NO-MVN-MAN-VER$ -->
				<configuration>
					<mainClass>com.boot.core.Application</mainClass><!-- 指定SpringBoot的main入口 -->
					<!--本地依赖的system作用域包打包到项目jar -->
					<includeSystemScope>true</includeSystemScope>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<!-- 打包时将jsp文件拷贝到META-INF目录下 -->
			<resource>
				<!-- 指定resources插件处理哪个目录下的资源文件 -->
				<directory>src/main/webapp</directory>
				<!--注意此次必须要放在此目录下才能被访问到 -->
				<targetPath>META-INF/resources</targetPath>
				<includes>
					<include>**/**</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/**</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/java</directory><!--所在的目录 -->
				<includes><!--包括目录下的.properties,.xml文件都会扫描到 -->
					<include>**/*.properties</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>${project.basedir}/src/main/lib</directory>
				<targetPath>BOOT-INF/lib/</targetPath>
				<includes>
					<include>**/*.jar</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>