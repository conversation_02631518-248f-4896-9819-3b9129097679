package com.zjhc.gzwcq.openApi;

import com.alibaba.fastjson.JSON;
import com.boot.IAdmin.common.domain.ResponseEnvelopeOpenApi;
import com.boot.IAdmin.common.utils.DateUtils;
import com.boot.iAdmin.access.model.organization.SysOrganization;
import com.boot.iAdmin.appInfo.entity.AppInfo;
import com.boot.iAdmin.redis.common.RedisUtil;
import com.zjhc.gzwcq.apiCallLogs.entity.ApiCallLogs;
import com.zjhc.gzwcq.jbxxb.entity.JbxxbVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 国资委对外接口
 */
@RequestMapping("/openApi")
@RestController
@Api(value = "国资委对外接口文档", tags = "国资委对外接口文档")
@Slf4j
public class OpenApiController {

    @Autowired
    private BusOpenApiClient openApiClient;
    @Autowired
    private RedisUtil redisUtil;
    private final String redisKeyPrefix = "OpenApi:";
    // 1-调用日志 2-被调用日志
    private final String apiLogType= "2";
    /**
     * 2.2	企业基本信息获取接口
     */
    @PostMapping("/getJbxxbByOrgId")
    @ResponseBody
    @ApiOperation(value = "企业基本信息获取接口")
    public ResponseEnvelopeOpenApi getJbxxbByOrgId(Integer pageNumber, HttpServletRequest request,
                                                   @RequestParam(required = false, defaultValue = "50") Integer limit,
                                                   @RequestParam(required = false) String orgId) {
        ResponseEnvelopeOpenApi re = new ResponseEnvelopeOpenApi();
        try {
            //当前登录的id
            String appId = this.getHeaders(request).get("appid");

            if (StringUtils.isBlank(appId)) {
                re.setResultCode("0101");
                throw new RuntimeException("appId不能不传");
            }
            if (pageNumber == null) {
                re.setResultCode("0101");
                throw new RuntimeException("参数异常,请传入分页参数pageNumber");
            }
            if (pageNumber <= 0 || limit <= 0) {
                re.setResultCode("0501");
                throw new RuntimeException("参数异常,请检查pageNumber,limit值");
            }
            List<JbxxbVo> jbxxbByOrgData = openApiClient.getJbxxbByOrgId(pageNumber, limit, orgId, appId);
            if (Objects.isNull(jbxxbByOrgData)) {
                re.setResultCode("0110");
                throw new RuntimeException("密钥对应的组织id有误,请运维排查");
            }
            re.setResult(jbxxbByOrgData);
            re.setResultCode("0000");
        } catch (Exception e) {
            log.error("QUERY ERROR:", e);
            re.setSuccess(false);
            re.setMessage("查询失败：" + ExceptionUtils.getMessage(e));
        }
        return re;
    }


    /**
     * 产权树分页查询
     */
    @PostMapping("/getOrgList")
    @ResponseBody
    @ApiOperation(value = "产权树分页查询")
    public ResponseEnvelopeOpenApi getOrgList(Integer pageNumber, HttpServletRequest request,
                                              @RequestParam(required = false, defaultValue = "50") Integer limit) {
        ResponseEnvelopeOpenApi re = new ResponseEnvelopeOpenApi();
        try {
            //当前登录的id
            String appId = this.getHeaders(request).get("appid");
            if (StringUtils.isBlank(appId)) {
                re.setResultCode("0101");
                throw new RuntimeException("appId不能不传");
            }
            if (pageNumber == null) {
                re.setResultCode("0101");
                throw new RuntimeException("参数异常,请传入分页参数pageNumber");
            }
            if (pageNumber <= 0 || limit <= 0) {
                re.setResultCode("0501");
                throw new RuntimeException("参数异常,请检查pageNumber,limit值");
            }
            List<SysOrganization> orgListPage = openApiClient.getOrgListPage(pageNumber, limit, appId);
            if (Objects.isNull(orgListPage)) {
                re.setResultCode("0110");
                throw new RuntimeException("密钥对应的组织id有误,请检查运维排查");
            }
            re.setResult(orgListPage);
            re.setResultCode("0000");
        } catch (Exception e) {
            log.error("QUERY ERROR:", e);
            re.setSuccess(false);
            re.setMessage("查询失败：" + ExceptionUtils.getMessage(e));
        }
        return re;
    }


    /**
     * 文件下载对外接口
     */
    @PostMapping("/downLoadFile")
    @ResponseBody
    @ApiOperation(value = "文件下载对外接口")
    public ResponseEnvelopeOpenApi downLoadFile(@RequestParam("attachmentId") String attachmentId, HttpServletRequest request) {
        ResponseEnvelopeOpenApi re = new ResponseEnvelopeOpenApi();
        try {
            //当前登录的id
            String appId = this.getHeaders(request).get("appid");
            if (StringUtils.isBlank(appId)) {
                re.setResultCode("0101");
                throw new RuntimeException("appId不能不传");
            }
            if (StringUtils.isEmpty(attachmentId)) {
                re.setResultCode("0101");
                throw new RuntimeException("参数异常,请传入文件ftp完整Id");
            }
            // 从Ftp获取文件
            byte[] data = openApiClient.downloadFileForOpenApi(attachmentId, appId);
            if (Objects.isNull(data)) {
                log.error("文件下载对外接口 ERROR: 文件id不存在");
                log.error("可能是账号权限不一致");
                re.setSuccess(false);
                re.setMessage("文件下载失败：" + "文件id不存在" + "\n可能是账号权限不一致");
                re.setResultCode("0501");
                return re;
            }
//            File file = new File("C:\\Users\\<USER>\\Desktop\\登记表模板.docx");
//            FileOutputStream fos = new FileOutputStream(file);
//            BufferedOutputStream bos = new BufferedOutputStream(fos);
//            bos.write(data);
//            bos.close();
//            fos.close();
            re.setResult(data);
            re.setResultCode("0000");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("文件下载对外接口 ERROR:", e);
            re.setSuccess(false);
            re.setMessage("文件下载失败：" + ExceptionUtils.getMessage(e));
        }
        return re;
    }

    @ApiResponses({
            @ApiResponse(code = 0, message = "验证成功"),
            @ApiResponse(code = 0501, message = "非法入侵接口，传入的组织id不存在")
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "string", name = "orgId", value = "组织id", required = true)})
    @ApiOperation(value = "企业详细信息获取接口", notes = "通过传入的组织id查询，企业", httpMethod = "POST")
    @PostMapping("/getJbxxbDetailByOrgId")
    @ResponseBody
    public ResponseEnvelopeOpenApi getJbxxbDetailByOrgId(@RequestParam("orgId") String orgId, HttpServletRequest request) {
        ResponseEnvelopeOpenApi response = new ResponseEnvelopeOpenApi();
        try {
            //当前登录的id
            String appId = this.getHeaders(request).get("appid");
            if (StringUtils.isBlank(appId)) {
                log.error("appId不能不传");
                response.setMessage("appId不能不传");
                response.setResultCode("0110");
                return response;
            }
            if (!Objects.nonNull(orgId)) {
                log.error("企业详细信息获取接口 传入的组织id不能为空");
                response.setMessage("传入的组织id不能为空");
                response.setResultCode("0901");
                return response;
            }
            IntegrateDataVO result = openApiClient.getJbxxbDetailByOrgId(orgId, appId);
            if (result == null) {
                log.error("企业详细信息获取接口 传入的组织id不存在 ERROR Id:", orgId);
                log.error("可能是账号权限不一致");
                response.setMessage("传入的组织id不存在,可能是账号权限不一致");
                response.setResultCode("0501");
                return response;
            }
            response.setResult(result);
            response.setResultCode("0000");
            response.setMessage("调用成功");
        } catch (Exception e) {
            log.error("企业详细信息获取接口 传入的组织id不存在 ERROR:\t", e);
            response.setMessage(e.getMessage());
            response.setResultCode("0901");
        }
        return response;
    }

    /**
     * 获取请求头中的数据
     *
     * @param request
     * @return
     */
    public Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> voMap = new HashMap<>();
        // 获取所有请求头的名称
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            voMap.put(headerName, headerValue);
        }

        return voMap;
    }

    @ApiResponses({
            @ApiResponse(code = 0, message = "验证成功"),
            @ApiResponse(code = 0501, message = "非法入侵接口，传入的组织id不存在")
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "string", name = "orgId", value = "组织id", required = true)})
    @ApiOperation(value = "企业详细信息+出资信息获取接口", notes = "可通过传入的组织id查询，企业", httpMethod = "POST")
    @PostMapping("/getJbxxbOrCzrInfoDetail")
    @ResponseBody
    public ResponseEnvelopeOpenApi getJbxxbOrCzrInfoDetail(JbxxbInfoDTO dto,HttpServletRequest request) {
        ResponseEnvelopeOpenApi response = new ResponseEnvelopeOpenApi();
        long executionTimeMs = System.currentTimeMillis();
        String appId = null;
        String requestMethod = request.getMethod();
        String requestUrl = request.getRequestURL().toString();
        Map<String, String> headers = null;
        try {
            Integer limit = dto.getLimit();
            Integer pageNumber = dto.getPageNumber();
            String orgId = dto.getOrgId();
            //当前登录的id
            headers = this.getHeaders(request);
            appId = headers.get("appid");
            if (StringUtils.isBlank(appId)) {
                log.error("appId不能不传");
                response.setMessage("appId不能不传");
                response.setResultCode("0110");
                return response;
            }

            Long rows = redisUtil.incrementWithExpire(redisKeyPrefix + appId+":"+DateUtils.dateFormat(new Date(), "yyyy-MM-dd HH:mm"), 1, TimeUnit.MINUTES);
            if (rows > 10){
                log.error("appId:{} 访问次数超过限制",appId);
                response.setMessage("appId:{} 访问次数超过限,请勿制频繁请求"+appId);
                response.setResultCode("1000");
                return response;
            }
            List<JbxxbInfo> result = openApiClient.getJbxxbOrCzrInfoDetail(pageNumber,appId,limit,orgId);
            if (result == null) {
                log.error("企业详细信息获取接口 传入的组织id不存在 ERROR Id:", orgId);
                log.error("可能是账号权限不一致");
                response.setMessage("传入的组织id不存在,可能是账号权限不一致");
                response.setResultCode("0501");
                return response;
            }
            Long jbxxbOrCzrDetailTotal = openApiClient.getJbxxbOrCzrInfoDetailTotal(appId, orgId);
            response.setResult(result);
            response.setResultCode("0000");
            response.setMessage("调用成功");
            response.setTotal(jbxxbOrCzrDetailTotal);
        } catch (Exception e) {
            log.error("企业详细信息获取接口 传入的组织id不存在 ERROR:\t", e);
            response.setMessage(e.getMessage());
            response.setResultCode("0901");
        }finally {
            String message = response.getMessage();
            String resultCode = response.getResultCode();
            Integer status = 2;
            if ("0000".equals(resultCode)){
                status = 1;
            }
            AppInfo appInfo = openApiClient.selectAppInfo(appId);
            String apiName = Objects.isNull(appInfo)?"":appInfo.getAppName();
            openApiClient.insetApiCallLogs(new ApiCallLogs(Integer.valueOf(apiLogType),appId,apiName,requestMethod,requestUrl,
                    JSON.toJSONString(headers),JSON.toJSONString(dto),Integer.valueOf(resultCode),JSON.toJSONString(response),executionTimeMs,getIpAddr(request),status,message));
        }
        return response;
    }

    /**
     * *获得ip地址
     * * @author: hhy
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
