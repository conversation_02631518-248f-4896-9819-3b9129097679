package com.boot.interceptor;

import javax.servlet.Filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

/**
 * 对外API过滤器注册
 * */
public class OpenApiRegistrar {
	
	/**
	 * 对外开放接口验证
	 * */
	@Bean
	public FilterRegistrationBean<Filter> openApiFilterRegistration() {
		FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<Filter>();
		registration.setFilter(new OpenApiInterceptor());
		registration.addUrlPatterns("/openApi/*");
		registration.setOrder(Integer.MIN_VALUE+52);
		return registration;
	}
}
