package com.boot.interceptor;

import java.io.IOException;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.boot.IAdmin.common.utils.SpringContextUtil;
import com.boot.iAdmin.appInfo.entity.AppInfo;
import com.boot.iAdmin.appInfo.service.api.IAppInfoService;

import sun.misc.BASE64Encoder;

/**
 * 过滤器
 * <P>
 * 用于过滤客户端的服务请求，验证是否有访问权限
 * 
 * <AUTHOR>
 * @date 2022年11月17日 15:28:19
 */
public class OpenApiInterceptor implements Filter {

	Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;
		try {
			String appId = httpRequest.getHeader("appId");
			String timeStamp = httpRequest.getHeader("timeStamp");// yyyyMMdd HHmmss
			String authenticator = httpRequest.getHeader("authenticator");
			if (StringUtils.isBlank(appId) || StringUtils.isBlank(authenticator) || StringUtils.isBlank(timeStamp)) {
				throw new RuntimeException("appId、timeStamp、authenticator不能为空");
			} else {
				//验证请求是否过期
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				long timeDif = new Date().getTime() - dateFormat.parse(timeStamp).getTime();
				if(!(timeDif > -5 * 60 * 1000 && timeDif < 5 * 60 * 1000)){
					logger.info(String.format("%s请求已过期,请保证时间戳在5分钟内.", appId));
					throw new RuntimeException("请求不合法！");
				}
				// 验证appId和authenticator是否合法
				IAppInfoService appInfoService = (IAppInfoService) SpringContextUtil.getBean(IAppInfoService.class);
				AppInfo appInfo = appInfoService.selectAppInfoByAppKey(appId);
				if (appInfo == null) {
					throw new RuntimeException("请求不合法,appId不存在！");
				}
				String secret = appInfo.getToken();
				//开始验证请求是否合法BASE64(MD5(timeStamp+secret))
				if(!StringUtils.equals(authenticator, encoderByMd5(timeStamp+secret))) {
					throw new RuntimeException("请求不合法,token错误！");
				}
			}
			logger.info(String.format("openApi验证通过,appId=%s", appId));
			chain.doFilter(request, response);// 验证通过
		} catch (Exception e) {
			httpResponse.setStatus(403);
			httpResponse.setContentType("application/json;charset=UTF-8");
			Map<String, Object> resMap = new HashMap<String, Object>();
			resMap.put("success", false);
			resMap.put("message", e.getMessage());
			resMap.put("resultCode","0403");
			httpResponse.getWriter().print(JSONObject.toJSONString(resMap));
			return;
		}
	}

	private String encoderByMd5(String str) {
		try {
            // 确定计算方法BASE64(MD5(timeStamp+secret))
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            BASE64Encoder base64en = new BASE64Encoder();
            // 加密后的字符串
            return base64en.encode(md5.digest(str.getBytes("UTF-8")));
        } catch (Exception e) {
            return "";
        }
    }
	
	public static void main(String[] args) {
		System.out.println(new OpenApiInterceptor().encoderByMd5("2025-07-01 18:10:006BFA5C08C8237BD0DDE4C5B9E1B0B4C3"));
	}
}
