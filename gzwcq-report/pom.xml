<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zjhc.gzw</groupId>
		<artifactId>gzwcq-parent</artifactId>
		<version>1.0.0</version>
	</parent>
	<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
	<packaging>pom</packaging>
	<artifactId>gzwcq-report</artifactId>

	<dependencies>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
		</dependency>
		<!-- servlet 依赖. -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
		</dependency>

		<!-- 监控 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
	</dependencies>
	
	<modules>
		<module>gzwcq-report-client</module>
		<module>gzwcq-report-entity</module>
		<module>gzwcq-report-service</module>
</modules>
</project>