package com.zjhc.gzwcq.dataQueryModel.entity;

import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;

import java.util.List;
import java.util.Set;

/**
 * 多条件查询类
 */
public class DataQueryModelParam extends DataQueryModel{

    private static final long serialVersionUID = 11L;

    private Integer limit;
    private Integer pageNumber;

    //数据查询
    private String timeFilter;//时间轮1,时间区间2
    private String beginDate;//时间区间begin(yyyy-MM-dd)
    private String endDate;//时间区间end(yyyy-MM-dd)
    private String rgType;//登记类型
    private String queryMode;//查询模式,"2"时为查审核通过的数据,"1"或null为查全部数据

    private List<SxzbpzVo> select;//需查询字段
    private Set<String> joinTableName;//join的表名(where中有则为join)
    private Set<String> leftJoinTableName;//left join的表名(select中有且where中没有则为left join)
    private List<SxzbpzVo> where;//查询条件(总)
    private List<SxzbpzVo> textWhere;//文本查询条件(like)
    private List<SxzbpzVo> dicWhere;//字典查询条件(=)
    private List<SxzbpzVo> dateWhere;//日期查询条件(format(''.'%Y%m%d'))
    private List<SxzbpzVo> dateRangeWhere;//日期区间查询条件(between)
    private List<SxzbpzVo> numWhere;//数字类型(between)

    //以下为经济行为分析增加的查询条件
    private List<String> situations;//登记情形
    private List<SxzbpzVo> numSelect;//需展示的数字类型字段(展示合计值,非数字类型结果页展示无意义)

    //明细页
    private String situationCode;//等级情形字典码
    
    private Set<String> zcdSet;//注册地集合
    private Set<String> zyhySet;//主要行业集合

    private List<SxzbpzVo> multiLevelListRadio;//多层级字典筛选条件(单选)
    private List<SxzbpzVo> multiLevelListCheckBox;//多层级字典筛选条件(多选)

    private List<String> selectedOrganizationIds;//选中的企业id集合

    public List<SxzbpzVo> getSelect() {
        return select;
    }

    public void setSelect(List<SxzbpzVo> select) {
        this.select = select;
    }

    public Set<String> getJoinTableName() {
        return joinTableName;
    }

    public void setJoinTableName(Set<String> joinTableName) {
        this.joinTableName = joinTableName;
    }

    public Set<String> getLeftJoinTableName() {
        return leftJoinTableName;
    }

    public void setLeftJoinTableName(Set<String> leftJoinTableName) {
        this.leftJoinTableName = leftJoinTableName;
    }

    public List<SxzbpzVo> getWhere() {
        return where;
    }

    public void setWhere(List<SxzbpzVo> where) {
        this.where = where;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public List<SxzbpzVo> getTextWhere() {
        return textWhere;
    }

    public void setTextWhere(List<SxzbpzVo> textWhere) {
        this.textWhere = textWhere;
    }

    public List<SxzbpzVo> getDicWhere() {
        return dicWhere;
    }

    public void setDicWhere(List<SxzbpzVo> dicWhere) {
        this.dicWhere = dicWhere;
    }

    public List<SxzbpzVo> getDateWhere() {
        return dateWhere;
    }

    public void setDateWhere(List<SxzbpzVo> dateWhere) {
        this.dateWhere = dateWhere;
    }

    public List<SxzbpzVo> getDateRangeWhere() {
        return dateRangeWhere;
    }

    public void setDateRangeWhere(List<SxzbpzVo> dateRangeWhere) {
        this.dateRangeWhere = dateRangeWhere;
    }

    public String getTimeFilter() {
        return timeFilter;
    }

    public void setTimeFilter(String timeFilter) {
        this.timeFilter = timeFilter;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getRgType() {
        return rgType;
    }

    public void setRgType(String rgType) {
        this.rgType = rgType;
    }

    public List<SxzbpzVo> getNumWhere() {
        return numWhere;
    }

    public void setNumWhere(List<SxzbpzVo> numWhere) {
        this.numWhere = numWhere;
    }

    public List<String> getSituations() {
        return situations;
    }

    public void setSituations(List<String> situations) {
        this.situations = situations;
    }

    public List<SxzbpzVo> getNumSelect() {
        return numSelect;
    }

    public void setNumSelect(List<SxzbpzVo> numSelect) {
        this.numSelect = numSelect;
    }

    public String getSituationCode() {
        return situationCode;
    }

    public void setSituationCode(String situationCode) {
        this.situationCode = situationCode;
    }

    public String getQueryMode() {
        return queryMode;
    }

    public void setQueryMode(String queryMode) {
        this.queryMode = queryMode;
    }
    
    public Set<String> getZcdSet() {
        return zcdSet;
    }

    public void setZcdSet(Set<String> zcdSet) {
        this.zcdSet = zcdSet;
    }

    public Set<String> getZyhySet() {
        return zyhySet;
    }

    public void setZyhySet(Set<String> zyhySet) {
        this.zyhySet = zyhySet;
    }

    public List<SxzbpzVo> getMultiLevelListRadio() {
        return multiLevelListRadio;
    }

    public void setMultiLevelListRadio(List<SxzbpzVo> multiLevelListRadio) {
        this.multiLevelListRadio = multiLevelListRadio;
    }

    public List<SxzbpzVo> getMultiLevelListCheckBox() {
        return multiLevelListCheckBox;
    }

    public void setMultiLevelListCheckBox(List<SxzbpzVo> multiLevelListCheckBox) {
        this.multiLevelListCheckBox = multiLevelListCheckBox;
    }

    public List<String> getSelectedOrganizationIds() {
        return selectedOrganizationIds;
    }

    public void setSelectedOrganizationIds(List<String> selectedOrganizationIds) {
        this.selectedOrganizationIds = selectedOrganizationIds;
    }
}
