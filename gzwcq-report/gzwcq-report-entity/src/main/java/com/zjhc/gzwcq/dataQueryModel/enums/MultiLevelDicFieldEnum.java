package com.zjhc.gzwcq.dataQueryModel.enums;

/**
 * 多层级字典字段(高级查询时,需要查询所选字典及其以下的全部的匹配数据)
 * <AUTHOR>
 * @createTime 2022-04-29
 */
public enum MultiLevelDicFieldEnum {

    JB_ZYHY("JB_ZYHY","主要行业","INDUSTRY_CLASSIFICATION_TREE",true),
    JB_ZZXS("JB_ZZXS","组织形式","ZZXS",false),
    JB_GZJGJG("JB_GZJGJG","国资监管机构","GZJGJG",false),
    JB_GJCZQY("JB_GJCZQY","国家出资企业","GJCZQY",false),
    JB_ZCD("JB_ZCD","注册地","ZCD",false);

    //字段名
    private String fieldName;

    //字段中文名
    private String fieldNameCn;

    //对应的字典typeCode
    private String typeCode;

    //字典是否可以多选
    private boolean isCheckBox;

    MultiLevelDicFieldEnum(String fieldName, String fieldNameCn, String typeCode, boolean isCheckBox) {
        this.fieldName = fieldName;
        this.fieldNameCn = fieldNameCn;
        this.typeCode = typeCode;
        this.isCheckBox = isCheckBox;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldNameCn() {
        return fieldNameCn;
    }

    public void setFieldNameCn(String fieldNameCn) {
        this.fieldNameCn = fieldNameCn;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public boolean isCheckBox() {
        return isCheckBox;
    }

    public void setCheckBox(boolean checkBox) {
        isCheckBox = checkBox;
    }
}
