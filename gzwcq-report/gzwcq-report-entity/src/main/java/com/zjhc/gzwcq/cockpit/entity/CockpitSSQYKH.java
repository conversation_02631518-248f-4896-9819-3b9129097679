package com.zjhc.gzwcq.cockpit.entity;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ExcelIgnoreUnannotated
public class CockpitSSQYKH implements Serializable {
    private static final long serialVersionUID = 11L;

    protected String unitId;//组织id
    @ExcelProperty(value = "组织名字",index = 0)
    protected String unitName;//组织名字
    @ExcelProperty(value = "登记总数",index = 1)
    protected long registerNum;//登记总数
    @ExcelProperty(value = "及时登记数量",index = 2)
    protected long inTimeRegisterNum;//及时登记数量
    @ExcelProperty(value = "提交总次数",index = 3)
    protected long submitRegisterNum;//提交总次数
    @ExcelProperty(value = "退回次数",index = 4)
    protected long returnRegisterNum;//退回次数
}
