package com.zjhc.gzwcq.cockpit.entity.enums;

public enum QyjcEnum {

    QYJC_1(1,"1-1级企业"),
    QYJC_2(2,"2-2级企业"),
    QYJC_3(3,"3-3级企业"),
    QYJC_4(4,"4-4级及以下企业");

    private int value;
    private String text;

    QyjcEnum(int value, String text){
        this.value = value;
        this.text = text;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    /**
     * @param value
     * @return
     */
    public static QyjcEnum getByValue(Integer value){
        if(value == null || value <= 0){
            return null;
        }
        for(QyjcEnum x:values()){
            if(x.getValue() == value){
                return x;
            }else if(value > QYJC_4.value){
                return QYJC_4;
            }
        }
        return null;
    }

}
