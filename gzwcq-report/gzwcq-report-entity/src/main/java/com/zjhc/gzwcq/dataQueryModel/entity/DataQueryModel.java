package com.zjhc.gzwcq.dataQueryModel.entity;



import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 数据查询实体类
 */
public class DataQueryModel implements Serializable {

    private static final long serialVersionUID = 11L;
    protected String JB_CZEBZXZ;
    protected String FD9_SJCZR;
    protected String FD7_HCFMC;
    protected String ZL_QSBG_YW;
    protected String ZL_ZGDBDHJY_LY;
    protected String ZL_QSBG_BGH;
    protected String ZL_ZXGG_GGRQ;
    protected String ZL_XBPGBAB_PGBGH;
    protected String FD5_SFZJHJ;
    protected String ZL_QT;
    protected String FD8_SRFMC;
    protected String XX_QSJZCZ;
    protected String ZL_ZXGG_LY;
    protected String JB_JHQLSJ;
    protected String ZL_ZHYPGBAB_LY;
    protected String XX_ZHEBZ;
    protected String ZL_XBPGBAB_HZWJH;
    protected String ZL_FLXYS_YW;
    protected String FD7_HZJZCZHJ;
    protected String JB_SFTSMDGS;
    protected String JB_QLJH;
    protected String JB_GYSJKZFRSBSBZ;
    protected String ZL_GSZXZM_YW;
    protected String ZL_ZGDBDHJY_YW;
    protected String FD_CZRLB;
    protected String JB_DYLSH;
    protected String JB_SFZTJN;
    protected String FD7_HCFSSGZJGJG;
    protected String JB_SSBM;
    protected String ZL_GQSZFAWJ_YW;
    protected String JB_SJZCZBBZXZ;
    protected String JB_SFCZGRDCG;
    protected String ZL_YZBG_YZCJR;
    protected String ZL_ZHEPGBAB_HZWJH;
    protected String JB_GYFRSDS;
    protected String FD8_ZRLB;
    protected String AF_CURRENT_AUDIT_LEVEL;
    protected String JB_SFYBGS;
    protected String JB_XZQY;
    protected String FD5_SFZJ;
    protected String ZL_ZHYPGBAB_ZJJGMC;
    protected String XX_BDQYPGJZCZ;
    protected String ZL_QSBG_ZJJGMC;
    protected String ZL_YZBG_YZBGH;
    protected String ZL_HBXYS_LY;
    protected String ZL_ZXGG_YW;
    protected String FD8_SRGQSJJZCZ;
    protected String JB_HJCJEBZ;
    protected String XX_BDQYPGJZCZ_BJZ;
    protected String ZL_TJPGBAB_ZJJGMC;
    protected String ZL_GQZR_LY;
    protected String XX_PCCCZE;
    protected String JB_CGRXM;
    protected String ZL_BXBPGBAB_LY;
    protected String ZL_ZGDBDHJY_YJ;
    protected String ZL_ZHYPGBAB_HZDWMC;
    protected String FD9_CGRMC;
    protected String ZL_HBXYS_YW;
    protected String ZL_JZRSJBG_YW;
    protected String XX_ZHYSSGZJGJG;
    protected String FD6_CZRMC;
    protected String JB_GYSJKZFRSDSBZ;
    protected String JB_SHTGRQ;
    protected String ZL_XBPGBAB_ZJJGMC;
    protected String UNITID;
    protected String ZL_JCWJ_DWMC;
    protected String JB_HJSJZCJ;
    protected String JB_GYJDKGSBS;
    protected String JB_QYMC;
    protected String FD1_JZZJHJ;
    protected String XX_BDQYXZ;
    protected String JB_SFYZ;
    protected String JB_GJSDS;
    protected String FD2_PGJZCZ;
    protected String JB_CZRZZJGID;
    protected String ZL_JZRSJBG_YZBGH;
    protected String ZL_ZHXY_YW;
    protected String XX_PGJZCZ;
    protected String FD1_ZFZJHJ;
    protected String JB_QYLB;
    protected String JB_ZXCQDJQX;
    protected String ZL_JZGG_LY;
    protected String JB_GYKGCZSBS;
    protected String ZL_FHBPGBA_YW;
    protected String FD8_ZRGQSJJZCZ;
    protected String JB_HJBL;
    protected String FD6_SYCCFPJG;
    protected String ZL_TJPGBAB_HZDWMC;
    protected String ZL_FHBPGBA_HZWJH;
    protected String JB_ZCD;
    protected String FD8_ZRGQBL;
    protected String FD8_BZ;
    protected String MONITORWARN_ID;
    protected String JB_GYSJKZSBS;
    protected String ZL_QSBG_LY;
    protected String RG_TRANSFERTYPE;
    protected String XX_XBFPGJZCZ;
    protected String JB_QTSDS;
    protected String ZL_GDQKDJB_LY;
    protected String XX_YWFHBCZ;
    protected String JB_ZCMD;
    protected String XX_BXBFXZ;
    protected String ZL_BXBPGBAB_HZWJH;
    protected String ZL_ZJYQSJBG_YW;
    protected String FD8_SRGQPGJZCZ;
    protected String XX_ZHYQYMC;
    protected String FD8_ZRGQPGJZCZHJ;
    protected String FD7_HZGQBL;
    protected String ZL_GYTDBA_PZDW;
    protected String ZL_SJBG_CJRQ;
    protected String XX_SJJZCZ;
    protected String RG_DATE;
    protected String FD1_JZF;
    protected String XX_YWFHBZFSGJK;
    protected String XX_BZ;
    protected String XX_CJFMC;
    protected String ZL_ZHEPGBAB_PGBGH;
    protected String ZL_BDPGBA_PGBGH;
    protected String ZL_TJPGBAB_LY;
    protected String XX_ZGBZ_XSHB;
    protected String FD8_SRFSSGZJGJG;
    protected String ZL_JCJG_LY;
    protected String ZL_PGBA_YW;
    protected String ZL_YXHZTZS_HZDW;
    protected String FD2_ZGBZ;
    protected String JB_ZCZBJW;
    protected String ZL_JCJG_JYJG;
    protected String ZL_BDPGBA_YW;
    protected String JB_GYSJKZSDS;
    protected String FD5_CJFXZ;
    protected String XX_PCFPBC;
    protected String FD_SJZCZBBZ;
    protected String JB_JNJW;
    protected String JB_SFSJSCDM;
    protected String ZL_YZBG_LY;
    protected String ZL_FHBPGBA_ZJJGMC;
    protected String FD_CZEBZ;
    protected String JB_ZYHY;
    protected String JB_ZYHY2;
    protected String JB_ZYHY3;
    protected String ZL_YXHZTZS_LY;
    protected String ZL_ZHEPGBAB_ZJJGMC;
    protected String FD2_SGJG;
    protected String XX_QCZW;
    protected String ZL_TJPGBAB_PGBGH;
    protected String ZL_JZRSJBG_ZJJGMC;
    protected String JB_SJBLRQ;
    protected String XX_ZHYYYZHDZCPGZ;
    protected String XX_YYTZDGQZJ;
    protected String FD1_BZ;
    protected String JB_HJQYSBS;
    protected String FD8_ZJYJ;
    protected String JB_GJSBS;
    protected String JB_ZCDJW;
    protected String FD_CZE;
    protected String JB_QTQYSDSBZ;
    protected String ZL_PCGG_MTMC;
    protected String ZL_WCHZXY_YW;
    protected String FD3_BZ;
    protected String JB_RJZBBZXZ;
    protected String FD6_SYCCFPJGHJ;
    protected String XX_YWFHBJZ;
    protected String FD2_YGQCYFMC;
    protected String JB_QYSBSBZXZ;
    protected String FD8_SRGQBLHJ;
    protected String ZL_FHBPGBA_HZDWMC;
    protected String JB_GJCZQYSBSBZ;
    protected String FD_RJZBBZ;
    protected String XX_ZHYSSGJCZQY;
    protected String JB_SDSBZXZ;
    protected String FD2_PGJZCZHJ;
    protected String IF_MONITORWARN;
    protected String JB_ZZJGDM;
    protected String ZL_JZGG_YW;
    protected String JB_SFZY;
    protected String FD8_SRGQBL;
    protected String FD8_ZRGQSJJZCZHJ;
    protected String ZL_ZHEPGBAB_HZDWMC;
    protected String JB_SSHY;
    protected String XX_AZFYZE;
    protected String FD5_CJFMC;
    protected String JB_GYJDKGFRSDSBZ;
    protected String XX_ZHEQYMC;
    protected String JB_SHZT;
    protected String BUSINESS_NATURE;
    protected String XX_BXBFMC;
    protected String XX_BXBFJZCZHXBFGQBL;
    protected String JB_GJCZSDSBZ;
    protected String ZL_BXBPGBAB_PGBGH;
    protected String FD_GQBL;
    protected String ZL_YZBG_ZJJGMC;
    protected String ZL_PGBA_ZJJGMC;
    protected String ZL_ZHYPGBAB_HZWJH;
    protected String ZL_PGBA_HZDWMC;
    protected String JB_BDCQDJQX;
    protected String ZL_YYZZ_YW;
    protected String XX_CXQYPGJZCZ;
    protected String FD7_HZGQBLHJ;
    protected String ZL_GYTDBA_PZWH;
    protected String ZL_GQSZFAWJ_PZWH;
    protected String FD7_HZJZCZ;
    protected String ZL_ZHXY_LY;
    protected String ZL_FHBPGBA_LY;
    protected String FD1_ZFF;
    protected String ZL_GQSZFAWJ_PZDW;
    protected String FD2_SGGQBL;
    protected String ZL_SJBG_LY;
    protected String ZL_GSZXZM_GSBMMC;
    protected String FD7_HRFSSGZJGJG_ZX;
    protected String ZL_BDPGBA_HZWJH;
    protected String JB_SJCZR;
    protected String JB_CZRZZJGDM;
    protected String JB_GYZB;
    protected String XX_BXBFPGJZCZ;
    protected String XX_CJFSSGZJGJG;
    protected String XX_PTZW;
    protected String FD7_HZBDQYMC;
    protected String ZL_JZRSJBG_LY;
    protected String ZL_SJBG_YW;
    protected String ZL_TZXY_LY;
    protected String ZL_ZHYPGBAB_PGBGH;
    protected String ZL_SJBG_ZJJG;
    protected String ZL_BXBPGBAB_HZDWMC;
    protected String ZL_YZBG_YW;
    protected String ZL_GYTDBA_LY;
    protected String FD7_HCFSSGJCZQY;
    protected String FD9_INFO;
    protected String ZL_JCJG_CJRQ;
    protected String ZL_QYZC_YW;
    protected String JB_HJSDS;
    protected String ZL_GDQKDJB_YW;
    protected String RG_TIMEMARK;
    protected String FD2_SGGQBLHJ;
    protected String FD7_HRFMC_ZX;
    protected String FD3_XGZJGFD;
    protected String ZL_JCWJ_WJMC;
    protected String JB_HJSJZCJBZ;
    protected String RG_SOLUTIONID;
    protected String XX_ZHESSGZJGJG;
    protected String JB_QTSBSBZ;
    protected String JB_GYJDKGSDS;
    protected String ZL_ZHYPGBAB_YW;
    protected String JB_GJCZQY;
    protected String ZL_FHBPGBA_PGBGH;
    protected String JB_GYFRCZSDSBZ;
    protected String ZL_SYZCCZXY_YW;
    protected String FD2_ZJYJ;
    protected String FD1_CZZJHJ;
    protected String ZL_JZGG_GGRQ;
    protected String ZL_FJ;
    protected String XX_QSFPSQSK;
    protected String JB_ZYCQDJQX;
    protected String FD_CZRZZJGDM;
    protected String JB_GZJGJG;
    protected String FD8_ZRGQPGJZCZ;
    protected String XX_JSYY;
    protected String XX_BDQYMC;
    protected String ZL_XBPGBAB_HZDWMC;
    protected String XX_ZHESSGJCZQY;
    protected String JB_GYJDKGFRSBSBZ;
    protected String FD8_ZRFMC;
    protected String ZL_GQSZFAWJ_LY;
    protected String XX_BDQYSSGZJGJG;
    protected String ZL_TJPGBAB_YW;
    protected String ZL_BXBPGBAB_ZJJGMC;
    protected String ZL_WCHZXY_LY;
    protected String FD7_HRFSSGJCZQY;
    protected String FD8_ZRGQBLHJ;
    protected String ZL_ZJYQSJBG_BGH;
    protected String FD7_HRFSSGZJGJG;
    protected String JB_SFZDYJSJ;
    protected String ZL_SYZCCZXY_LY;
    protected String JB_HJCZE;
    protected String JB_GYKGCZ;
    protected String ZL_GQZR_YW;
    protected String ZL_PGBA_PGBGH;
    protected String JB_HJSDSBZ;
    protected String XX_ZGBZ_XZGB;
    protected String ZL_SJBG_BGH;
    protected String ZL_ZHEPGBAB_YW;
    protected String ZL_PGBA_LY;
    protected String ZL_JCJG_JGD;
    protected String XX_BDQYSJJZCZ;
    protected String ZL_PCGG_LY;
    protected String XX_GKFXGS;
    protected String AF_CURRENT_NODE;
    protected String XX_ZHYBZ;
    protected String ZL_ZHEPGBAB_LY;
    protected String FD1_CZZJ;
    protected String JB_GYFRCZSBSBZ;
    protected String FD1_HFBZCLB;
    protected String XX_BDQYSJJZCZ_BGZ;
    protected String JB_ZZXS;
    protected String XX_YYTZGQZHBDQYGQBL;
    protected String JB_ZCZBBZ;
    protected String JB_HJQYSBSBZ;
    protected String FD1_ZFZJ;
    protected String FD1_PGZHJ;
    protected String FD7_HZJZR;
    protected String FD1_PGZ;
    protected String FD1_JZZJ;
    protected String JB_BLQSHZT;
    protected String FD2_SGJGHJ;
    protected String JB_JYZK;
    protected String XX_ZHEYYZHDZCPGZ;
    protected String RG_UNITSTATE;
    protected String FD7_HRFMC;
    protected String ZL_PGBA_HZWJH;
    protected String ZL_XBPGBAB_YW;
    protected String ZL_TZXY_YW;
    protected String RG_TYPE;
    protected String FD4_ZGSLHJ;
    protected String XX_CXQYZGBZ;
    protected String JB_GYFRSBS;
    protected String ZL_FLXYS_LY;
    protected String XX_ZGBZ_JS;
    protected String JB_GSBLZK;
    protected String XX_QSCCSFZYQCZW;
    protected String ZL_BDPGBA_ZJJGMC;
    protected String FD_SJZCJ;
    protected String FD2_SJJZCZHJ;
    protected String ZL_TJPGBAB_HZWJH;
    protected String JB_GSDJXGZL;
    protected String ZL_JCWJ_LY;
    protected String ZL_YWBLSQWJ;
    protected String ZL_BDPGBA_LY;
    protected String FD2_SJJZCZ;
    protected String FD8_CJJHJ;
    protected String FD2_SGFS;
    protected String FD8_CJJ;
    protected String XX_PCFPSQSK;
    protected String XX_GYQY;
    protected String XX_SFZJ;
    protected String JB_BYZLY;
    protected String ZL_BXBPGBAB_YW;
    protected String ZL_YYZZ_LY;
    protected String ZL_ZJYQSJBG_LY;
    protected String XX_FXGS;
    protected String FD_CZRMC;
    protected String FD_RJZB;
    protected String XX_BFLQYPGJZCZ;
    protected String FD8_SRGQSJJZCZHJ;
    protected String XX_AZRYZS;
    protected String FD8_SRGQPGJZCZHJ;
    protected String FD2_BZ;
    protected String XX_PCFYHGYZW;
    protected String AF_CURRENTUNITID;
    protected String XX_ZGBZ_TZXZ;
    protected String XX_BDQYPGJZCZ_BZZ;
    protected String FD8_SRFXZ;
    protected String ZL_ZXGG_MTMC;
    protected String ZL_JZGG_MTMC;
    protected String XX_ZGBZ_GQCZ;
    protected String XX_YYTZDGQPGZ;
    protected String JB_HJRJZB;
    protected String XX_QSFY;
    protected String FD1_CZF;
    protected String JB_GSDJRQ;
    protected String JB_HJRJZBBZ;
    protected String XX_QSFPBC;
    protected String XX_BDQYPGJZCZ_BGZ;
    protected String ZL_GSZXZM_LY;
    protected String JB_RELA;
    protected String JB_ZCRQ;
    protected String ZL_JCWJ_WJH;
    protected String FD7_WCHZLB;
    protected String JB_HGQY;
    protected String ZL_GYTDBA_YW;
    protected String JB_ZCZBBZXZ;
    protected String JB_QYJC;
    protected String FD7_BZ;
    protected String ZL_YXHZTZS_YW;
    protected String FD8_ZRFSSGZJGJG;
    protected String ZL_JCJG_YW;
    protected String JB_SSGZJGJG;
    protected String ZL_PCGG_GGRQ;
    protected String ZL_QYZC_LY;
    protected String JB_ZCZB;
    protected String XX_FXJG;
    protected String ZL_ZJYQSJBG_ZJJG;
    protected String XX_SYJZCCZSR;
    protected String ZL_GSZXZM_ZXRQ;
    protected String ZL_JCWJ_YW;
    protected String ZL_PCGG_YW;
    protected String FD2_YGQCYFXZ;
    protected String ZL_BDPGBA_HZDWMC;
    protected String JB_QTQYSBS;
    protected String ZL_XBPGBAB_LY;
    protected String JB_CZRZZJGMC;
    protected String jb_qygljc;
    /*********以下为合伙企业相关表字段**********/
    //cq_hhqy
    protected String HH_COMPANY_NAME;//有限合伙企业名称
    protected String HH_CREDIT_CODE;//统一信用代码
    protected String HH_ZXSWHHR;//执行事务合伙人
    protected String HH_ZXSWHHR_CODE;//执行事务合伙人统一信用代码
    protected String SETUP_DATE;//成立日期
    protected String HH_QX;//合伙期限
    protected String HH_ZYJYCS;//主要经营场所
    protected String HH_SFSMTZJJ;//是否私募投资基金
    protected String HH_JYFW;//经营范围
    protected String HH_RJCZE;//认缴出资额（万元）
    protected String HH_RJCZEBZ;//认缴出资额币种
    protected String HH_SJCZE;//实缴出资额（万元）
    protected String HH_SJCZEBZ;//实缴出资额币种
    protected String HH_GJCZQY;//国家出资企业
    protected String HH_GJCZQY_CODE;//国家出资企业统一信用代码
    protected String HH_CZQYID;//出资企业id
    protected String HH_CZQY;//出资企业
    protected String HH_CZQY_CODE;//出资企业统一信用代码
    protected String HH_HHXY;//合伙协议附件
    protected String HJRJCZE;//合计认缴出资额（万元）
    protected String HJRJCZBL;//合计认缴出资比例%
    protected String HJSJCZE;//合计实缴出资额（万元）

    //cq_dwtzqkfd
    protected String BDLX;//标的类型
    protected String BDMC;//标的名称
    protected String CODE;//统一信用代码
    protected String SSHY;//所属行业
    protected String ADDRESS;//注册地或所在地
    protected String TZE;//投资额（万元）
    protected String TZBL;//投资比例%
    protected String SFSJKZ;//是否实际控制

    //cq_hhrqkfd
    protected String NAME;//合伙人名称
    protected String HHR_CODE;//统一信用代码
    protected String TYPE;//合伙人类型
    protected String CATEGORY;//合伙人类别
    protected String RJCZE;//认缴出资额（万元）
    protected String RJCZBL;//认缴出资比例
    protected String SJCZE;//实缴出资额（万元）
    protected String CZFS;//出资方式
    protected String JFQX;//缴付期限

    protected String HH_SSZB;//合伙_实收资本

    @JsonProperty("JB_CZEBZXZ")
    public String getJB_CZEBZXZ() {
        return JB_CZEBZXZ;
    }

    @JsonProperty("JB_CZEBZXZ")
    public void setJB_CZEBZXZ(String JB_CZEBZXZ) {
        this.JB_CZEBZXZ = JB_CZEBZXZ;
    }

    @JsonProperty("FD9_SJCZR")
    public String getFD9_SJCZR() {
        return FD9_SJCZR;
    }

    @JsonProperty("FD9_SJCZR")
    public void setFD9_SJCZR(String FD9_SJCZR) {
        this.FD9_SJCZR = FD9_SJCZR;
    }

    @JsonProperty("FD7_HCFMC")
    public String getFD7_HCFMC() {
        return FD7_HCFMC;
    }

    @JsonProperty("FD7_HCFMC")
    public void setFD7_HCFMC(String FD7_HCFMC) {
        this.FD7_HCFMC = FD7_HCFMC;
    }

    @JsonProperty("ZL_QSBG_YW")
    public String getZL_QSBG_YW() {
        return ZL_QSBG_YW;
    }

    @JsonProperty("ZL_QSBG_YW")
    public void setZL_QSBG_YW(String ZL_QSBG_YW) {
        this.ZL_QSBG_YW = ZL_QSBG_YW;
    }

    @JsonProperty("ZL_ZGDBDHJY_LY")
    public String getZL_ZGDBDHJY_LY() {
        return ZL_ZGDBDHJY_LY;
    }

    @JsonProperty("ZL_ZGDBDHJY_LY")
    public void setZL_ZGDBDHJY_LY(String ZL_ZGDBDHJY_LY) {
        this.ZL_ZGDBDHJY_LY = ZL_ZGDBDHJY_LY;
    }

    @JsonProperty("ZL_QSBG_BGH")
    public String getZL_QSBG_BGH() {
        return ZL_QSBG_BGH;
    }

    @JsonProperty("ZL_QSBG_BGH")
    public void setZL_QSBG_BGH(String ZL_QSBG_BGH) {
        this.ZL_QSBG_BGH = ZL_QSBG_BGH;
    }

    @JsonProperty("ZL_ZXGG_GGRQ")
    public String getZL_ZXGG_GGRQ() {
        return ZL_ZXGG_GGRQ;
    }

    @JsonProperty("ZL_ZXGG_GGRQ")
    public void setZL_ZXGG_GGRQ(String ZL_ZXGG_GGRQ) {
        this.ZL_ZXGG_GGRQ = ZL_ZXGG_GGRQ;
    }

    @JsonProperty("ZL_XBPGBAB_PGBGH")
    public String getZL_XBPGBAB_PGBGH() {
        return ZL_XBPGBAB_PGBGH;
    }

    @JsonProperty("ZL_XBPGBAB_PGBGH")
    public void setZL_XBPGBAB_PGBGH(String ZL_XBPGBAB_PGBGH) {
        this.ZL_XBPGBAB_PGBGH = ZL_XBPGBAB_PGBGH;
    }

    @JsonProperty("FD5_SFZJHJ")
    public String getFD5_SFZJHJ() {
        return FD5_SFZJHJ;
    }

    @JsonProperty("FD5_SFZJHJ")
    public void setFD5_SFZJHJ(String FD5_SFZJHJ) {
        this.FD5_SFZJHJ = FD5_SFZJHJ;
    }

    @JsonProperty("ZL_QT")
    public String getZL_QT() {
        return ZL_QT;
    }

    @JsonProperty("ZL_QT")
    public void setZL_QT(String ZL_QT) {
        this.ZL_QT = ZL_QT;
    }

    @JsonProperty("FD8_SRFMC")
    public String getFD8_SRFMC() {
        return FD8_SRFMC;
    }

    @JsonProperty("FD8_SRFMC")
    public void setFD8_SRFMC(String FD8_SRFMC) {
        this.FD8_SRFMC = FD8_SRFMC;
    }

    @JsonProperty("XX_QSJZCZ")
    public String getXX_QSJZCZ() {
        return XX_QSJZCZ;
    }

    @JsonProperty("XX_QSJZCZ")
    public void setXX_QSJZCZ(String XX_QSJZCZ) {
        this.XX_QSJZCZ = XX_QSJZCZ;
    }

    @JsonProperty("ZL_ZXGG_LY")
    public String getZL_ZXGG_LY() {
        return ZL_ZXGG_LY;
    }

    @JsonProperty("ZL_ZXGG_LY")
    public void setZL_ZXGG_LY(String ZL_ZXGG_LY) {
        this.ZL_ZXGG_LY = ZL_ZXGG_LY;
    }

    @JsonProperty("JB_JHQLSJ")
    public String getJB_JHQLSJ() {
        return JB_JHQLSJ;
    }

    @JsonProperty("JB_JHQLSJ")
    public void setJB_JHQLSJ(String JB_JHQLSJ) {
        this.JB_JHQLSJ = JB_JHQLSJ;
    }

    @JsonProperty("ZL_ZHYPGBAB_LY")
    public String getZL_ZHYPGBAB_LY() {
        return ZL_ZHYPGBAB_LY;
    }

    @JsonProperty("ZL_ZHYPGBAB_LY")
    public void setZL_ZHYPGBAB_LY(String ZL_ZHYPGBAB_LY) {
        this.ZL_ZHYPGBAB_LY = ZL_ZHYPGBAB_LY;
    }

    @JsonProperty("XX_ZHEBZ")
    public String getXX_ZHEBZ() {
        return XX_ZHEBZ;
    }

    @JsonProperty("XX_ZHEBZ")
    public void setXX_ZHEBZ(String XX_ZHEBZ) {
        this.XX_ZHEBZ = XX_ZHEBZ;
    }

    @JsonProperty("ZL_XBPGBAB_HZWJH")
    public String getZL_XBPGBAB_HZWJH() {
        return ZL_XBPGBAB_HZWJH;
    }

    @JsonProperty("ZL_XBPGBAB_HZWJH")
    public void setZL_XBPGBAB_HZWJH(String ZL_XBPGBAB_HZWJH) {
        this.ZL_XBPGBAB_HZWJH = ZL_XBPGBAB_HZWJH;
    }

    @JsonProperty("ZL_FLXYS_YW")
    public String getZL_FLXYS_YW() {
        return ZL_FLXYS_YW;
    }

    @JsonProperty("ZL_FLXYS_YW")
    public void setZL_FLXYS_YW(String ZL_FLXYS_YW) {
        this.ZL_FLXYS_YW = ZL_FLXYS_YW;
    }

    @JsonProperty("FD7_HZJZCZHJ")
    public String getFD7_HZJZCZHJ() {
        return FD7_HZJZCZHJ;
    }

    @JsonProperty("FD7_HZJZCZHJ")
    public void setFD7_HZJZCZHJ(String FD7_HZJZCZHJ) {
        this.FD7_HZJZCZHJ = FD7_HZJZCZHJ;
    }

    @JsonProperty("JB_SFTSMDGS")
    public String getJB_SFTSMDGS() {
        return JB_SFTSMDGS;
    }

    @JsonProperty("JB_SFTSMDGS")
    public void setJB_SFTSMDGS(String JB_SFTSMDGS) {
        this.JB_SFTSMDGS = JB_SFTSMDGS;
    }

    @JsonProperty("JB_QLJH")
    public String getJB_QLJH() {
        return JB_QLJH;
    }

    @JsonProperty("JB_QLJH")
    public void setJB_QLJH(String JB_QLJH) {
        this.JB_QLJH = JB_QLJH;
    }

    @JsonProperty("JB_GYSJKZFRSBSBZ")
    public String getJB_GYSJKZFRSBSBZ() {
        return JB_GYSJKZFRSBSBZ;
    }

    @JsonProperty("JB_GYSJKZFRSBSBZ")
    public void setJB_GYSJKZFRSBSBZ(String JB_GYSJKZFRSBSBZ) {
        this.JB_GYSJKZFRSBSBZ = JB_GYSJKZFRSBSBZ;
    }

    @JsonProperty("ZL_GSZXZM_YW")
    public String getZL_GSZXZM_YW() {
        return ZL_GSZXZM_YW;
    }

    @JsonProperty("ZL_GSZXZM_YW")
    public void setZL_GSZXZM_YW(String ZL_GSZXZM_YW) {
        this.ZL_GSZXZM_YW = ZL_GSZXZM_YW;
    }

    @JsonProperty("ZL_ZGDBDHJY_YW")
    public String getZL_ZGDBDHJY_YW() {
        return ZL_ZGDBDHJY_YW;
    }

    @JsonProperty("ZL_ZGDBDHJY_YW")
    public void setZL_ZGDBDHJY_YW(String ZL_ZGDBDHJY_YW) {
        this.ZL_ZGDBDHJY_YW = ZL_ZGDBDHJY_YW;
    }

    @JsonProperty("FD_CZRLB")
    public String getFD_CZRLB() {
        return FD_CZRLB;
    }

    @JsonProperty("FD_CZRLB")
    public void setFD_CZRLB(String FD_CZRLB) {
        this.FD_CZRLB = FD_CZRLB;
    }

    @JsonProperty("JB_DYLSH")
    public String getJB_DYLSH() {
        return JB_DYLSH;
    }

    @JsonProperty("JB_DYLSH")
    public void setJB_DYLSH(String JB_DYLSH) {
        this.JB_DYLSH = JB_DYLSH;
    }

    @JsonProperty("JB_SFZTJN")
    public String getJB_SFZTJN() {
        return JB_SFZTJN;
    }

    @JsonProperty("JB_SFZTJN")
    public void setJB_SFZTJN(String JB_SFZTJN) {
        this.JB_SFZTJN = JB_SFZTJN;
    }

    @JsonProperty("FD7_HCFSSGZJGJG")
    public String getFD7_HCFSSGZJGJG() {
        return FD7_HCFSSGZJGJG;
    }

    @JsonProperty("FD7_HCFSSGZJGJG")
    public void setFD7_HCFSSGZJGJG(String FD7_HCFSSGZJGJG) {
        this.FD7_HCFSSGZJGJG = FD7_HCFSSGZJGJG;
    }

    @JsonProperty("JB_SSBM")
    public String getJB_SSBM() {
        return JB_SSBM;
    }

    @JsonProperty("JB_SSBM")
    public void setJB_SSBM(String JB_SSBM) {
        this.JB_SSBM = JB_SSBM;
    }

    @JsonProperty("ZL_GQSZFAWJ_YW")
    public String getZL_GQSZFAWJ_YW() {
        return ZL_GQSZFAWJ_YW;
    }

    @JsonProperty("ZL_GQSZFAWJ_YW")
    public void setZL_GQSZFAWJ_YW(String ZL_GQSZFAWJ_YW) {
        this.ZL_GQSZFAWJ_YW = ZL_GQSZFAWJ_YW;
    }

    @JsonProperty("JB_SJZCZBBZXZ")
    public String getJB_SJZCZBBZXZ() {
        return JB_SJZCZBBZXZ;
    }

    @JsonProperty("JB_SJZCZBBZXZ")
    public void setJB_SJZCZBBZXZ(String JB_SJZCZBBZXZ) {
        this.JB_SJZCZBBZXZ = JB_SJZCZBBZXZ;
    }

    @JsonProperty("JB_SFCZGRDCG")
    public String getJB_SFCZGRDCG() {
        return JB_SFCZGRDCG;
    }

    @JsonProperty("JB_SFCZGRDCG")
    public void setJB_SFCZGRDCG(String JB_SFCZGRDCG) {
        this.JB_SFCZGRDCG = JB_SFCZGRDCG;
    }

    @JsonProperty("ZL_YZBG_YZCJR")
    public String getZL_YZBG_YZCJR() {
        return ZL_YZBG_YZCJR;
    }

    @JsonProperty("ZL_YZBG_YZCJR")
    public void setZL_YZBG_YZCJR(String ZL_YZBG_YZCJR) {
        this.ZL_YZBG_YZCJR = ZL_YZBG_YZCJR;
    }

    @JsonProperty("ZL_ZHEPGBAB_HZWJH")
    public String getZL_ZHEPGBAB_HZWJH() {
        return ZL_ZHEPGBAB_HZWJH;
    }

    @JsonProperty("ZL_ZHEPGBAB_HZWJH")
    public void setZL_ZHEPGBAB_HZWJH(String ZL_ZHEPGBAB_HZWJH) {
        this.ZL_ZHEPGBAB_HZWJH = ZL_ZHEPGBAB_HZWJH;
    }

    @JsonProperty("JB_GYFRSDS")
    public String getJB_GYFRSDS() {
        return JB_GYFRSDS;
    }

    @JsonProperty("JB_GYFRSDS")
    public void setJB_GYFRSDS(String JB_GYFRSDS) {
        this.JB_GYFRSDS = JB_GYFRSDS;
    }

    @JsonProperty("FD8_ZRLB")
    public String getFD8_ZRLB() {
        return FD8_ZRLB;
    }

    @JsonProperty("FD8_ZRLB")
    public void setFD8_ZRLB(String FD8_ZRLB) {
        this.FD8_ZRLB = FD8_ZRLB;
    }

    @JsonProperty("AF_CURRENT_AUDIT_LEVEL")
    public String getAF_CURRENT_AUDIT_LEVEL() {
        return AF_CURRENT_AUDIT_LEVEL;
    }

    @JsonProperty("AF_CURRENT_AUDIT_LEVEL")
    public void setAF_CURRENT_AUDIT_LEVEL(String AF_CURRENT_AUDIT_LEVEL) {
        this.AF_CURRENT_AUDIT_LEVEL = AF_CURRENT_AUDIT_LEVEL;
    }

    @JsonProperty("JB_SFYBGS")
    public String getJB_SFYBGS() {
        return JB_SFYBGS;
    }

    @JsonProperty("JB_SFYBGS")
    public void setJB_SFYBGS(String JB_SFYBGS) {
        this.JB_SFYBGS = JB_SFYBGS;
    }

    @JsonProperty("JB_XZQY")
    public String getJB_XZQY() {
        return JB_XZQY;
    }

    @JsonProperty("JB_XZQY")
    public void setJB_XZQY(String JB_XZQY) {
        this.JB_XZQY = JB_XZQY;
    }

    @JsonProperty("FD5_SFZJ")
    public String getFD5_SFZJ() {
        return FD5_SFZJ;
    }

    @JsonProperty("FD5_SFZJ")
    public void setFD5_SFZJ(String FD5_SFZJ) {
        this.FD5_SFZJ = FD5_SFZJ;
    }

    @JsonProperty("ZL_ZHYPGBAB_ZJJGMC")
    public String getZL_ZHYPGBAB_ZJJGMC() {
        return ZL_ZHYPGBAB_ZJJGMC;
    }

    @JsonProperty("ZL_ZHYPGBAB_ZJJGMC")
    public void setZL_ZHYPGBAB_ZJJGMC(String ZL_ZHYPGBAB_ZJJGMC) {
        this.ZL_ZHYPGBAB_ZJJGMC = ZL_ZHYPGBAB_ZJJGMC;
    }

    @JsonProperty("XX_BDQYPGJZCZ")
    public String getXX_BDQYPGJZCZ() {
        return XX_BDQYPGJZCZ;
    }

    @JsonProperty("XX_BDQYPGJZCZ")
    public void setXX_BDQYPGJZCZ(String XX_BDQYPGJZCZ) {
        this.XX_BDQYPGJZCZ = XX_BDQYPGJZCZ;
    }

    @JsonProperty("ZL_QSBG_ZJJGMC")
    public String getZL_QSBG_ZJJGMC() {
        return ZL_QSBG_ZJJGMC;
    }

    @JsonProperty("ZL_QSBG_ZJJGMC")
    public void setZL_QSBG_ZJJGMC(String ZL_QSBG_ZJJGMC) {
        this.ZL_QSBG_ZJJGMC = ZL_QSBG_ZJJGMC;
    }

    @JsonProperty("ZL_YZBG_YZBGH")
    public String getZL_YZBG_YZBGH() {
        return ZL_YZBG_YZBGH;
    }

    @JsonProperty("ZL_YZBG_YZBGH")
    public void setZL_YZBG_YZBGH(String ZL_YZBG_YZBGH) {
        this.ZL_YZBG_YZBGH = ZL_YZBG_YZBGH;
    }

    @JsonProperty("ZL_HBXYS_LY")
    public String getZL_HBXYS_LY() {
        return ZL_HBXYS_LY;
    }

    @JsonProperty("ZL_HBXYS_LY")
    public void setZL_HBXYS_LY(String ZL_HBXYS_LY) {
        this.ZL_HBXYS_LY = ZL_HBXYS_LY;
    }

    @JsonProperty("ZL_ZXGG_YW")
    public String getZL_ZXGG_YW() {
        return ZL_ZXGG_YW;
    }

    @JsonProperty("ZL_ZXGG_YW")
    public void setZL_ZXGG_YW(String ZL_ZXGG_YW) {
        this.ZL_ZXGG_YW = ZL_ZXGG_YW;
    }

    @JsonProperty("FD8_SRGQSJJZCZ")
    public String getFD8_SRGQSJJZCZ() {
        return FD8_SRGQSJJZCZ;
    }

    @JsonProperty("FD8_SRGQSJJZCZ")
    public void setFD8_SRGQSJJZCZ(String FD8_SRGQSJJZCZ) {
        this.FD8_SRGQSJJZCZ = FD8_SRGQSJJZCZ;
    }

    @JsonProperty("JB_HJCJEBZ")
    public String getJB_HJCJEBZ() {
        return JB_HJCJEBZ;
    }

    @JsonProperty("JB_HJCJEBZ")
    public void setJB_HJCJEBZ(String JB_HJCJEBZ) {
        this.JB_HJCJEBZ = JB_HJCJEBZ;
    }

    @JsonProperty("XX_BDQYPGJZCZ_BJZ")
    public String getXX_BDQYPGJZCZ_BJZ() {
        return XX_BDQYPGJZCZ_BJZ;
    }

    @JsonProperty("XX_BDQYPGJZCZ_BJZ")
    public void setXX_BDQYPGJZCZ_BJZ(String XX_BDQYPGJZCZ_BJZ) {
        this.XX_BDQYPGJZCZ_BJZ = XX_BDQYPGJZCZ_BJZ;
    }

    @JsonProperty("ZL_TJPGBAB_ZJJGMC")
    public String getZL_TJPGBAB_ZJJGMC() {
        return ZL_TJPGBAB_ZJJGMC;
    }

    @JsonProperty("ZL_TJPGBAB_ZJJGMC")
    public void setZL_TJPGBAB_ZJJGMC(String ZL_TJPGBAB_ZJJGMC) {
        this.ZL_TJPGBAB_ZJJGMC = ZL_TJPGBAB_ZJJGMC;
    }

    @JsonProperty("ZL_GQZR_LY")
    public String getZL_GQZR_LY() {
        return ZL_GQZR_LY;
    }

    @JsonProperty("ZL_GQZR_LY")
    public void setZL_GQZR_LY(String ZL_GQZR_LY) {
        this.ZL_GQZR_LY = ZL_GQZR_LY;
    }

    @JsonProperty("XX_PCCCZE")
    public String getXX_PCCCZE() {
        return XX_PCCCZE;
    }

    @JsonProperty("XX_PCCCZE")
    public void setXX_PCCCZE(String XX_PCCCZE) {
        this.XX_PCCCZE = XX_PCCCZE;
    }

    @JsonProperty("JB_CGRXM")
    public String getJB_CGRXM() {
        return JB_CGRXM;
    }

    @JsonProperty("JB_CGRXM")
    public void setJB_CGRXM(String JB_CGRXM) {
        this.JB_CGRXM = JB_CGRXM;
    }

    @JsonProperty("ZL_BXBPGBAB_LY")
    public String getZL_BXBPGBAB_LY() {
        return ZL_BXBPGBAB_LY;
    }

    @JsonProperty("ZL_BXBPGBAB_LY")
    public void setZL_BXBPGBAB_LY(String ZL_BXBPGBAB_LY) {
        this.ZL_BXBPGBAB_LY = ZL_BXBPGBAB_LY;
    }

    @JsonProperty("ZL_ZGDBDHJY_YJ")
    public String getZL_ZGDBDHJY_YJ() {
        return ZL_ZGDBDHJY_YJ;
    }

    @JsonProperty("ZL_ZGDBDHJY_YJ")
    public void setZL_ZGDBDHJY_YJ(String ZL_ZGDBDHJY_YJ) {
        this.ZL_ZGDBDHJY_YJ = ZL_ZGDBDHJY_YJ;
    }

    @JsonProperty("ZL_ZHYPGBAB_HZDWMC")
    public String getZL_ZHYPGBAB_HZDWMC() {
        return ZL_ZHYPGBAB_HZDWMC;
    }

    @JsonProperty("ZL_ZHYPGBAB_HZDWMC")
    public void setZL_ZHYPGBAB_HZDWMC(String ZL_ZHYPGBAB_HZDWMC) {
        this.ZL_ZHYPGBAB_HZDWMC = ZL_ZHYPGBAB_HZDWMC;
    }

    @JsonProperty("FD9_CGRMC")
    public String getFD9_CGRMC() {
        return FD9_CGRMC;
    }

    @JsonProperty("FD9_CGRMC")
    public void setFD9_CGRMC(String FD9_CGRMC) {
        this.FD9_CGRMC = FD9_CGRMC;
    }

    @JsonProperty("ZL_HBXYS_YW")
    public String getZL_HBXYS_YW() {
        return ZL_HBXYS_YW;
    }

    @JsonProperty("ZL_HBXYS_YW")
    public void setZL_HBXYS_YW(String ZL_HBXYS_YW) {
        this.ZL_HBXYS_YW = ZL_HBXYS_YW;
    }

    @JsonProperty("ZL_JZRSJBG_YW")
    public String getZL_JZRSJBG_YW() {
        return ZL_JZRSJBG_YW;
    }

    @JsonProperty("ZL_JZRSJBG_YW")
    public void setZL_JZRSJBG_YW(String ZL_JZRSJBG_YW) {
        this.ZL_JZRSJBG_YW = ZL_JZRSJBG_YW;
    }

    @JsonProperty("XX_ZHYSSGZJGJG")
    public String getXX_ZHYSSGZJGJG() {
        return XX_ZHYSSGZJGJG;
    }

    @JsonProperty("XX_ZHYSSGZJGJG")
    public void setXX_ZHYSSGZJGJG(String XX_ZHYSSGZJGJG) {
        this.XX_ZHYSSGZJGJG = XX_ZHYSSGZJGJG;
    }

    @JsonProperty("FD6_CZRMC")
    public String getFD6_CZRMC() {
        return FD6_CZRMC;
    }

    @JsonProperty("FD6_CZRMC")
    public void setFD6_CZRMC(String FD6_CZRMC) {
        this.FD6_CZRMC = FD6_CZRMC;
    }

    @JsonProperty("JB_GYSJKZFRSDSBZ")
    public String getJB_GYSJKZFRSDSBZ() {
        return JB_GYSJKZFRSDSBZ;
    }

    @JsonProperty("JB_GYSJKZFRSDSBZ")
    public void setJB_GYSJKZFRSDSBZ(String JB_GYSJKZFRSDSBZ) {
        this.JB_GYSJKZFRSDSBZ = JB_GYSJKZFRSDSBZ;
    }

    @JsonProperty("JB_SHTGRQ")
    public String getJB_SHTGRQ() {
        return JB_SHTGRQ;
    }

    @JsonProperty("JB_SHTGRQ")
    public void setJB_SHTGRQ(String JB_SHTGRQ) {
        this.JB_SHTGRQ = JB_SHTGRQ;
    }

    @JsonProperty("ZL_XBPGBAB_ZJJGMC")
    public String getZL_XBPGBAB_ZJJGMC() {
        return ZL_XBPGBAB_ZJJGMC;
    }

    @JsonProperty("ZL_XBPGBAB_ZJJGMC")
    public void setZL_XBPGBAB_ZJJGMC(String ZL_XBPGBAB_ZJJGMC) {
        this.ZL_XBPGBAB_ZJJGMC = ZL_XBPGBAB_ZJJGMC;
    }

    @JsonProperty("UNITID")
    public String getUNITID() {
        return UNITID;
    }

    @JsonProperty("UNITID")
    public void setUNITID(String UNITID) {
        this.UNITID = UNITID;
    }

    @JsonProperty("ZL_JCWJ_DWMC")
    public String getZL_JCWJ_DWMC() {
        return ZL_JCWJ_DWMC;
    }

    @JsonProperty("ZL_JCWJ_DWMC")
    public void setZL_JCWJ_DWMC(String ZL_JCWJ_DWMC) {
        this.ZL_JCWJ_DWMC = ZL_JCWJ_DWMC;
    }

    @JsonProperty("JB_HJSJZCJ")
    public String getJB_HJSJZCJ() {
        return JB_HJSJZCJ;
    }

    @JsonProperty("JB_HJSJZCJ")
    public void setJB_HJSJZCJ(String JB_HJSJZCJ) {
        this.JB_HJSJZCJ = JB_HJSJZCJ;
    }

    @JsonProperty("JB_GYJDKGSBS")
    public String getJB_GYJDKGSBS() {
        return JB_GYJDKGSBS;
    }

    @JsonProperty("JB_GYJDKGSBS")
    public void setJB_GYJDKGSBS(String JB_GYJDKGSBS) {
        this.JB_GYJDKGSBS = JB_GYJDKGSBS;
    }

    @JsonProperty("JB_QYMC")
    public String getJB_QYMC() {
        return JB_QYMC;
    }

    @JsonProperty("JB_QYMC")
    public void setJB_QYMC(String JB_QYMC) {
        this.JB_QYMC = JB_QYMC;
    }

    @JsonProperty("FD1_JZZJHJ")
    public String getFD1_JZZJHJ() {
        return FD1_JZZJHJ;
    }

    @JsonProperty("FD1_JZZJHJ")
    public void setFD1_JZZJHJ(String FD1_JZZJHJ) {
        this.FD1_JZZJHJ = FD1_JZZJHJ;
    }

    @JsonProperty("XX_BDQYXZ")
    public String getXX_BDQYXZ() {
        return XX_BDQYXZ;
    }

    @JsonProperty("XX_BDQYXZ")
    public void setXX_BDQYXZ(String XX_BDQYXZ) {
        this.XX_BDQYXZ = XX_BDQYXZ;
    }

    @JsonProperty("JB_SFYZ")
    public String getJB_SFYZ() {
        return JB_SFYZ;
    }

    @JsonProperty("JB_SFYZ")
    public void setJB_SFYZ(String JB_SFYZ) {
        this.JB_SFYZ = JB_SFYZ;
    }

    @JsonProperty("JB_GJSDS")
    public String getJB_GJSDS() {
        return JB_GJSDS;
    }

    @JsonProperty("JB_GJSDS")
    public void setJB_GJSDS(String JB_GJSDS) {
        this.JB_GJSDS = JB_GJSDS;
    }

    @JsonProperty("FD2_PGJZCZ")
    public String getFD2_PGJZCZ() {
        return FD2_PGJZCZ;
    }

    @JsonProperty("FD2_PGJZCZ")
    public void setFD2_PGJZCZ(String FD2_PGJZCZ) {
        this.FD2_PGJZCZ = FD2_PGJZCZ;
    }

    @JsonProperty("JB_CZRZZJGID")
    public String getJB_CZRZZJGID() {
        return JB_CZRZZJGID;
    }

    @JsonProperty("JB_CZRZZJGID")
    public void setJB_CZRZZJGID(String JB_CZRZZJGID) {
        this.JB_CZRZZJGID = JB_CZRZZJGID;
    }

    @JsonProperty("ZL_JZRSJBG_YZBGH")
    public String getZL_JZRSJBG_YZBGH() {
        return ZL_JZRSJBG_YZBGH;
    }

    @JsonProperty("ZL_JZRSJBG_YZBGH")
    public void setZL_JZRSJBG_YZBGH(String ZL_JZRSJBG_YZBGH) {
        this.ZL_JZRSJBG_YZBGH = ZL_JZRSJBG_YZBGH;
    }

    @JsonProperty("ZL_ZHXY_YW")
    public String getZL_ZHXY_YW() {
        return ZL_ZHXY_YW;
    }

    @JsonProperty("ZL_ZHXY_YW")
    public void setZL_ZHXY_YW(String ZL_ZHXY_YW) {
        this.ZL_ZHXY_YW = ZL_ZHXY_YW;
    }

    @JsonProperty("XX_PGJZCZ")
    public String getXX_PGJZCZ() {
        return XX_PGJZCZ;
    }

    @JsonProperty("XX_PGJZCZ")
    public void setXX_PGJZCZ(String XX_PGJZCZ) {
        this.XX_PGJZCZ = XX_PGJZCZ;
    }

    @JsonProperty("FD1_ZFZJHJ")
    public String getFD1_ZFZJHJ() {
        return FD1_ZFZJHJ;
    }

    @JsonProperty("FD1_ZFZJHJ")
    public void setFD1_ZFZJHJ(String FD1_ZFZJHJ) {
        this.FD1_ZFZJHJ = FD1_ZFZJHJ;
    }

    @JsonProperty("JB_QYLB")
    public String getJB_QYLB() {
        return JB_QYLB;
    }

    @JsonProperty("JB_QYLB")
    public void setJB_QYLB(String JB_QYLB) {
        this.JB_QYLB = JB_QYLB;
    }

    @JsonProperty("JB_ZXCQDJQX")
    public String getJB_ZXCQDJQX() {
        return JB_ZXCQDJQX;
    }

    @JsonProperty("JB_ZXCQDJQX")
    public void setJB_ZXCQDJQX(String JB_ZXCQDJQX) {
        this.JB_ZXCQDJQX = JB_ZXCQDJQX;
    }

    @JsonProperty("ZL_JZGG_LY")
    public String getZL_JZGG_LY() {
        return ZL_JZGG_LY;
    }

    @JsonProperty("ZL_JZGG_LY")
    public void setZL_JZGG_LY(String ZL_JZGG_LY) {
        this.ZL_JZGG_LY = ZL_JZGG_LY;
    }

    @JsonProperty("JB_GYKGCZSBS")
    public String getJB_GYKGCZSBS() {
        return JB_GYKGCZSBS;
    }

    @JsonProperty("JB_GYKGCZSBS")
    public void setJB_GYKGCZSBS(String JB_GYKGCZSBS) {
        this.JB_GYKGCZSBS = JB_GYKGCZSBS;
    }

    @JsonProperty("ZL_FHBPGBA_YW")
    public String getZL_FHBPGBA_YW() {
        return ZL_FHBPGBA_YW;
    }

    @JsonProperty("ZL_FHBPGBA_YW")
    public void setZL_FHBPGBA_YW(String ZL_FHBPGBA_YW) {
        this.ZL_FHBPGBA_YW = ZL_FHBPGBA_YW;
    }

    @JsonProperty("FD8_ZRGQSJJZCZ")
    public String getFD8_ZRGQSJJZCZ() {
        return FD8_ZRGQSJJZCZ;
    }

    @JsonProperty("FD8_ZRGQSJJZCZ")
    public void setFD8_ZRGQSJJZCZ(String FD8_ZRGQSJJZCZ) {
        this.FD8_ZRGQSJJZCZ = FD8_ZRGQSJJZCZ;
    }

    @JsonProperty("JB_HJBL")
    public String getJB_HJBL() {
        return JB_HJBL;
    }

    @JsonProperty("JB_HJBL")
    public void setJB_HJBL(String JB_HJBL) {
        this.JB_HJBL = JB_HJBL;
    }

    @JsonProperty("FD6_SYCCFPJG")
    public String getFD6_SYCCFPJG() {
        return FD6_SYCCFPJG;
    }

    @JsonProperty("FD6_SYCCFPJG")
    public void setFD6_SYCCFPJG(String FD6_SYCCFPJG) {
        this.FD6_SYCCFPJG = FD6_SYCCFPJG;
    }

    @JsonProperty("ZL_TJPGBAB_HZDWMC")
    public String getZL_TJPGBAB_HZDWMC() {
        return ZL_TJPGBAB_HZDWMC;
    }

    @JsonProperty("ZL_TJPGBAB_HZDWMC")
    public void setZL_TJPGBAB_HZDWMC(String ZL_TJPGBAB_HZDWMC) {
        this.ZL_TJPGBAB_HZDWMC = ZL_TJPGBAB_HZDWMC;
    }

    @JsonProperty("ZL_FHBPGBA_HZWJH")
    public String getZL_FHBPGBA_HZWJH() {
        return ZL_FHBPGBA_HZWJH;
    }

    @JsonProperty("ZL_FHBPGBA_HZWJH")
    public void setZL_FHBPGBA_HZWJH(String ZL_FHBPGBA_HZWJH) {
        this.ZL_FHBPGBA_HZWJH = ZL_FHBPGBA_HZWJH;
    }

    @JsonProperty("JB_ZCD")
    public String getJB_ZCD() {
        return JB_ZCD;
    }

    @JsonProperty("JB_ZCD")
    public void setJB_ZCD(String JB_ZCD) {
        this.JB_ZCD = JB_ZCD;
    }

    @JsonProperty("FD8_ZRGQBL")
    public String getFD8_ZRGQBL() {
        return FD8_ZRGQBL;
    }

    @JsonProperty("FD8_ZRGQBL")
    public void setFD8_ZRGQBL(String FD8_ZRGQBL) {
        this.FD8_ZRGQBL = FD8_ZRGQBL;
    }

    @JsonProperty("FD8_BZ")
    public String getFD8_BZ() {
        return FD8_BZ;
    }

    @JsonProperty("FD8_BZ")
    public void setFD8_BZ(String FD8_BZ) {
        this.FD8_BZ = FD8_BZ;
    }

    @JsonProperty("MONITORWARN_ID")
    public String getMONITORWARN_ID() {
        return MONITORWARN_ID;
    }

    @JsonProperty("MONITORWARN_ID")
    public void setMONITORWARN_ID(String MONITORWARN_ID) {
        this.MONITORWARN_ID = MONITORWARN_ID;
    }

    @JsonProperty("JB_GYSJKZSBS")
    public String getJB_GYSJKZSBS() {
        return JB_GYSJKZSBS;
    }

    @JsonProperty("JB_GYSJKZSBS")
    public void setJB_GYSJKZSBS(String JB_GYSJKZSBS) {
        this.JB_GYSJKZSBS = JB_GYSJKZSBS;
    }

    @JsonProperty("ZL_QSBG_LY")
    public String getZL_QSBG_LY() {
        return ZL_QSBG_LY;
    }

    @JsonProperty("ZL_QSBG_LY")
    public void setZL_QSBG_LY(String ZL_QSBG_LY) {
        this.ZL_QSBG_LY = ZL_QSBG_LY;
    }

    @JsonProperty("RG_TRANSFERTYPE")
    public String getRG_TRANSFERTYPE() {
        return RG_TRANSFERTYPE;
    }

    @JsonProperty("RG_TRANSFERTYPE")
    public void setRG_TRANSFERTYPE(String RG_TRANSFERTYPE) {
        this.RG_TRANSFERTYPE = RG_TRANSFERTYPE;
    }

    @JsonProperty("XX_XBFPGJZCZ")
    public String getXX_XBFPGJZCZ() {
        return XX_XBFPGJZCZ;
    }

    @JsonProperty("XX_XBFPGJZCZ")
    public void setXX_XBFPGJZCZ(String XX_XBFPGJZCZ) {
        this.XX_XBFPGJZCZ = XX_XBFPGJZCZ;
    }

    @JsonProperty("JB_QTSDS")
    public String getJB_QTSDS() {
        return JB_QTSDS;
    }

    @JsonProperty("JB_QTSDS")
    public void setJB_QTSDS(String JB_QTSDS) {
        this.JB_QTSDS = JB_QTSDS;
    }

    @JsonProperty("ZL_GDQKDJB_LY")
    public String getZL_GDQKDJB_LY() {
        return ZL_GDQKDJB_LY;
    }

    @JsonProperty("ZL_GDQKDJB_LY")
    public void setZL_GDQKDJB_LY(String ZL_GDQKDJB_LY) {
        this.ZL_GDQKDJB_LY = ZL_GDQKDJB_LY;
    }

    @JsonProperty("XX_YWFHBCZ")
    public String getXX_YWFHBCZ() {
        return XX_YWFHBCZ;
    }

    @JsonProperty("XX_YWFHBCZ")
    public void setXX_YWFHBCZ(String XX_YWFHBCZ) {
        this.XX_YWFHBCZ = XX_YWFHBCZ;
    }

    @JsonProperty("JB_ZCMD")
    public String getJB_ZCMD() {
        return JB_ZCMD;
    }

    @JsonProperty("JB_ZCMD")
    public void setJB_ZCMD(String JB_ZCMD) {
        this.JB_ZCMD = JB_ZCMD;
    }

    @JsonProperty("XX_BXBFXZ")
    public String getXX_BXBFXZ() {
        return XX_BXBFXZ;
    }

    @JsonProperty("XX_BXBFXZ")
    public void setXX_BXBFXZ(String XX_BXBFXZ) {
        this.XX_BXBFXZ = XX_BXBFXZ;
    }

    @JsonProperty("ZL_BXBPGBAB_HZWJH")
    public String getZL_BXBPGBAB_HZWJH() {
        return ZL_BXBPGBAB_HZWJH;
    }

    @JsonProperty("ZL_BXBPGBAB_HZWJH")
    public void setZL_BXBPGBAB_HZWJH(String ZL_BXBPGBAB_HZWJH) {
        this.ZL_BXBPGBAB_HZWJH = ZL_BXBPGBAB_HZWJH;
    }

    @JsonProperty("ZL_ZJYQSJBG_YW")
    public String getZL_ZJYQSJBG_YW() {
        return ZL_ZJYQSJBG_YW;
    }

    @JsonProperty("ZL_ZJYQSJBG_YW")
    public void setZL_ZJYQSJBG_YW(String ZL_ZJYQSJBG_YW) {
        this.ZL_ZJYQSJBG_YW = ZL_ZJYQSJBG_YW;
    }

    @JsonProperty("FD8_SRGQPGJZCZ")
    public String getFD8_SRGQPGJZCZ() {
        return FD8_SRGQPGJZCZ;
    }

    @JsonProperty("FD8_SRGQPGJZCZ")
    public void setFD8_SRGQPGJZCZ(String FD8_SRGQPGJZCZ) {
        this.FD8_SRGQPGJZCZ = FD8_SRGQPGJZCZ;
    }

    @JsonProperty("XX_ZHYQYMC")
    public String getXX_ZHYQYMC() {
        return XX_ZHYQYMC;
    }

    @JsonProperty("XX_ZHYQYMC")
    public void setXX_ZHYQYMC(String XX_ZHYQYMC) {
        this.XX_ZHYQYMC = XX_ZHYQYMC;
    }

    @JsonProperty("FD8_ZRGQPGJZCZHJ")
    public String getFD8_ZRGQPGJZCZHJ() {
        return FD8_ZRGQPGJZCZHJ;
    }

    @JsonProperty("FD8_ZRGQPGJZCZHJ")
    public void setFD8_ZRGQPGJZCZHJ(String FD8_ZRGQPGJZCZHJ) {
        this.FD8_ZRGQPGJZCZHJ = FD8_ZRGQPGJZCZHJ;
    }

    @JsonProperty("FD7_HZGQBL")
    public String getFD7_HZGQBL() {
        return FD7_HZGQBL;
    }

    @JsonProperty("FD7_HZGQBL")
    public void setFD7_HZGQBL(String FD7_HZGQBL) {
        this.FD7_HZGQBL = FD7_HZGQBL;
    }

    @JsonProperty("ZL_GYTDBA_PZDW")
    public String getZL_GYTDBA_PZDW() {
        return ZL_GYTDBA_PZDW;
    }

    @JsonProperty("ZL_GYTDBA_PZDW")
    public void setZL_GYTDBA_PZDW(String ZL_GYTDBA_PZDW) {
        this.ZL_GYTDBA_PZDW = ZL_GYTDBA_PZDW;
    }

    @JsonProperty("ZL_SJBG_CJRQ")
    public String getZL_SJBG_CJRQ() {
        return ZL_SJBG_CJRQ;
    }

    @JsonProperty("ZL_SJBG_CJRQ")
    public void setZL_SJBG_CJRQ(String ZL_SJBG_CJRQ) {
        this.ZL_SJBG_CJRQ = ZL_SJBG_CJRQ;
    }

    @JsonProperty("XX_SJJZCZ")
    public String getXX_SJJZCZ() {
        return XX_SJJZCZ;
    }

    @JsonProperty("XX_SJJZCZ")
    public void setXX_SJJZCZ(String XX_SJJZCZ) {
        this.XX_SJJZCZ = XX_SJJZCZ;
    }

    @JsonProperty("RG_DATE")
    public String getRG_DATE() {
        return RG_DATE;
    }

    @JsonProperty("RG_DATE")
    public void setRG_DATE(String RG_DATE) {
        this.RG_DATE = RG_DATE;
    }

    @JsonProperty("FD1_JZF")
    public String getFD1_JZF() {
        return FD1_JZF;
    }

    @JsonProperty("FD1_JZF")
    public void setFD1_JZF(String FD1_JZF) {
        this.FD1_JZF = FD1_JZF;
    }

    @JsonProperty("XX_YWFHBZFSGJK")
    public String getXX_YWFHBZFSGJK() {
        return XX_YWFHBZFSGJK;
    }

    @JsonProperty("XX_YWFHBZFSGJK")
    public void setXX_YWFHBZFSGJK(String XX_YWFHBZFSGJK) {
        this.XX_YWFHBZFSGJK = XX_YWFHBZFSGJK;
    }

    @JsonProperty("XX_BZ")
    public String getXX_BZ() {
        return XX_BZ;
    }

    @JsonProperty("XX_BZ")
    public void setXX_BZ(String XX_BZ) {
        this.XX_BZ = XX_BZ;
    }

    @JsonProperty("XX_CJFMC")
    public String getXX_CJFMC() {
        return XX_CJFMC;
    }

    @JsonProperty("XX_CJFMC")
    public void setXX_CJFMC(String XX_CJFMC) {
        this.XX_CJFMC = XX_CJFMC;
    }

    @JsonProperty("ZL_ZHEPGBAB_PGBGH")
    public String getZL_ZHEPGBAB_PGBGH() {
        return ZL_ZHEPGBAB_PGBGH;
    }

    @JsonProperty("ZL_ZHEPGBAB_PGBGH")
    public void setZL_ZHEPGBAB_PGBGH(String ZL_ZHEPGBAB_PGBGH) {
        this.ZL_ZHEPGBAB_PGBGH = ZL_ZHEPGBAB_PGBGH;
    }

    @JsonProperty("ZL_BDPGBA_PGBGH")
    public String getZL_BDPGBA_PGBGH() {
        return ZL_BDPGBA_PGBGH;
    }

    @JsonProperty("ZL_BDPGBA_PGBGH")
    public void setZL_BDPGBA_PGBGH(String ZL_BDPGBA_PGBGH) {
        this.ZL_BDPGBA_PGBGH = ZL_BDPGBA_PGBGH;
    }

    @JsonProperty("ZL_TJPGBAB_LY")
    public String getZL_TJPGBAB_LY() {
        return ZL_TJPGBAB_LY;
    }

    @JsonProperty("ZL_TJPGBAB_LY")
    public void setZL_TJPGBAB_LY(String ZL_TJPGBAB_LY) {
        this.ZL_TJPGBAB_LY = ZL_TJPGBAB_LY;
    }

    @JsonProperty("XX_ZGBZ_XSHB")
    public String getXX_ZGBZ_XSHB() {
        return XX_ZGBZ_XSHB;
    }

    @JsonProperty("XX_ZGBZ_XSHB")
    public void setXX_ZGBZ_XSHB(String XX_ZGBZ_XSHB) {
        this.XX_ZGBZ_XSHB = XX_ZGBZ_XSHB;
    }

    @JsonProperty("FD8_SRFSSGZJGJG")
    public String getFD8_SRFSSGZJGJG() {
        return FD8_SRFSSGZJGJG;
    }

    @JsonProperty("FD8_SRFSSGZJGJG")
    public void setFD8_SRFSSGZJGJG(String FD8_SRFSSGZJGJG) {
        this.FD8_SRFSSGZJGJG = FD8_SRFSSGZJGJG;
    }

    @JsonProperty("ZL_JCJG_LY")
    public String getZL_JCJG_LY() {
        return ZL_JCJG_LY;
    }

    @JsonProperty("ZL_JCJG_LY")
    public void setZL_JCJG_LY(String ZL_JCJG_LY) {
        this.ZL_JCJG_LY = ZL_JCJG_LY;
    }

    @JsonProperty("ZL_PGBA_YW")
    public String getZL_PGBA_YW() {
        return ZL_PGBA_YW;
    }

    @JsonProperty("ZL_PGBA_YW")
    public void setZL_PGBA_YW(String ZL_PGBA_YW) {
        this.ZL_PGBA_YW = ZL_PGBA_YW;
    }

    @JsonProperty("ZL_YXHZTZS_HZDW")
    public String getZL_YXHZTZS_HZDW() {
        return ZL_YXHZTZS_HZDW;
    }

    @JsonProperty("ZL_YXHZTZS_HZDW")
    public void setZL_YXHZTZS_HZDW(String ZL_YXHZTZS_HZDW) {
        this.ZL_YXHZTZS_HZDW = ZL_YXHZTZS_HZDW;
    }

    @JsonProperty("FD2_ZGBZ")
    public String getFD2_ZGBZ() {
        return FD2_ZGBZ;
    }

    @JsonProperty("FD2_ZGBZ")
    public void setFD2_ZGBZ(String FD2_ZGBZ) {
        this.FD2_ZGBZ = FD2_ZGBZ;
    }

    @JsonProperty("JB_ZCZBJW")
    public String getJB_ZCZBJW() {
        return JB_ZCZBJW;
    }

    @JsonProperty("JB_ZCZBJW")
    public void setJB_ZCZBJW(String JB_ZCZBJW) {
        this.JB_ZCZBJW = JB_ZCZBJW;
    }

    @JsonProperty("ZL_JCJG_JYJG")
    public String getZL_JCJG_JYJG() {
        return ZL_JCJG_JYJG;
    }

    @JsonProperty("ZL_JCJG_JYJG")
    public void setZL_JCJG_JYJG(String ZL_JCJG_JYJG) {
        this.ZL_JCJG_JYJG = ZL_JCJG_JYJG;
    }

    @JsonProperty("ZL_BDPGBA_YW")
    public String getZL_BDPGBA_YW() {
        return ZL_BDPGBA_YW;
    }

    @JsonProperty("ZL_BDPGBA_YW")
    public void setZL_BDPGBA_YW(String ZL_BDPGBA_YW) {
        this.ZL_BDPGBA_YW = ZL_BDPGBA_YW;
    }

    @JsonProperty("JB_GYSJKZSDS")
    public String getJB_GYSJKZSDS() {
        return JB_GYSJKZSDS;
    }

    @JsonProperty("JB_GYSJKZSDS")
    public void setJB_GYSJKZSDS(String JB_GYSJKZSDS) {
        this.JB_GYSJKZSDS = JB_GYSJKZSDS;
    }

    @JsonProperty("FD5_CJFXZ")
    public String getFD5_CJFXZ() {
        return FD5_CJFXZ;
    }

    @JsonProperty("FD5_CJFXZ")
    public void setFD5_CJFXZ(String FD5_CJFXZ) {
        this.FD5_CJFXZ = FD5_CJFXZ;
    }

    @JsonProperty("XX_PCFPBC")
    public String getXX_PCFPBC() {
        return XX_PCFPBC;
    }

    @JsonProperty("XX_PCFPBC")
    public void setXX_PCFPBC(String XX_PCFPBC) {
        this.XX_PCFPBC = XX_PCFPBC;
    }

    @JsonProperty("FD_SJZCZBBZ")
    public String getFD_SJZCZBBZ() {
        return FD_SJZCZBBZ;
    }

    @JsonProperty("FD_SJZCZBBZ")
    public void setFD_SJZCZBBZ(String FD_SJZCZBBZ) {
        this.FD_SJZCZBBZ = FD_SJZCZBBZ;
    }

    @JsonProperty("JB_JNJW")
    public String getJB_JNJW() {
        return JB_JNJW;
    }

    @JsonProperty("JB_JNJW")
    public void setJB_JNJW(String JB_JNJW) {
        this.JB_JNJW = JB_JNJW;
    }

    @JsonProperty("JB_SFSJSCDM")
    public String getJB_SFSJSCDM() {
        return JB_SFSJSCDM;
    }

    @JsonProperty("JB_SFSJSCDM")
    public void setJB_SFSJSCDM(String JB_SFSJSCDM) {
        this.JB_SFSJSCDM = JB_SFSJSCDM;
    }

    @JsonProperty("ZL_YZBG_LY")
    public String getZL_YZBG_LY() {
        return ZL_YZBG_LY;
    }

    @JsonProperty("ZL_YZBG_LY")
    public void setZL_YZBG_LY(String ZL_YZBG_LY) {
        this.ZL_YZBG_LY = ZL_YZBG_LY;
    }

    @JsonProperty("ZL_FHBPGBA_ZJJGMC")
    public String getZL_FHBPGBA_ZJJGMC() {
        return ZL_FHBPGBA_ZJJGMC;
    }

    @JsonProperty("ZL_FHBPGBA_ZJJGMC")
    public void setZL_FHBPGBA_ZJJGMC(String ZL_FHBPGBA_ZJJGMC) {
        this.ZL_FHBPGBA_ZJJGMC = ZL_FHBPGBA_ZJJGMC;
    }

    @JsonProperty("FD_CZEBZ")
    public String getFD_CZEBZ() {
        return FD_CZEBZ;
    }

    @JsonProperty("FD_CZEBZ")
    public void setFD_CZEBZ(String FD_CZEBZ) {
        this.FD_CZEBZ = FD_CZEBZ;
    }
    @JsonProperty("JB_ZYHY2")
    public String getJB_ZYHY2() {
        return JB_ZYHY2;
    }
    @JsonProperty("JB_ZYHY2")
    public void setJB_ZYHY2(String JB_ZYHY2) {
        this.JB_ZYHY2 = JB_ZYHY2;
    }
    @JsonProperty("JB_ZYHY3")
    public String getJB_ZYHY3() {
        return JB_ZYHY3;
    }
    @JsonProperty("JB_ZYHY3")
    public void setJB_ZYHY3(String JB_ZYHY3) {
        this.JB_ZYHY3 = JB_ZYHY3;
    }

    @JsonProperty("JB_ZYHY")
    public String getJB_ZYHY() {
        return JB_ZYHY;
    }

    @JsonProperty("JB_ZYHY")
    public void setJB_ZYHY(String JB_ZYHY) {
        this.JB_ZYHY = JB_ZYHY;
    }

    @JsonProperty("ZL_YXHZTZS_LY")
    public String getZL_YXHZTZS_LY() {
        return ZL_YXHZTZS_LY;
    }

    @JsonProperty("ZL_YXHZTZS_LY")
    public void setZL_YXHZTZS_LY(String ZL_YXHZTZS_LY) {
        this.ZL_YXHZTZS_LY = ZL_YXHZTZS_LY;
    }

    @JsonProperty("ZL_ZHEPGBAB_ZJJGMC")
    public String getZL_ZHEPGBAB_ZJJGMC() {
        return ZL_ZHEPGBAB_ZJJGMC;
    }

    @JsonProperty("ZL_ZHEPGBAB_ZJJGMC")
    public void setZL_ZHEPGBAB_ZJJGMC(String ZL_ZHEPGBAB_ZJJGMC) {
        this.ZL_ZHEPGBAB_ZJJGMC = ZL_ZHEPGBAB_ZJJGMC;
    }

    @JsonProperty("FD2_SGJG")
    public String getFD2_SGJG() {
        return FD2_SGJG;
    }

    @JsonProperty("FD2_SGJG")
    public void setFD2_SGJG(String FD2_SGJG) {
        this.FD2_SGJG = FD2_SGJG;
    }

    @JsonProperty("XX_QCZW")
    public String getXX_QCZW() {
        return XX_QCZW;
    }

    @JsonProperty("XX_QCZW")
    public void setXX_QCZW(String XX_QCZW) {
        this.XX_QCZW = XX_QCZW;
    }

    @JsonProperty("ZL_TJPGBAB_PGBGH")
    public String getZL_TJPGBAB_PGBGH() {
        return ZL_TJPGBAB_PGBGH;
    }

    @JsonProperty("ZL_TJPGBAB_PGBGH")
    public void setZL_TJPGBAB_PGBGH(String ZL_TJPGBAB_PGBGH) {
        this.ZL_TJPGBAB_PGBGH = ZL_TJPGBAB_PGBGH;
    }

    @JsonProperty("ZL_JZRSJBG_ZJJGMC")
    public String getZL_JZRSJBG_ZJJGMC() {
        return ZL_JZRSJBG_ZJJGMC;
    }

    @JsonProperty("ZL_JZRSJBG_ZJJGMC")
    public void setZL_JZRSJBG_ZJJGMC(String ZL_JZRSJBG_ZJJGMC) {
        this.ZL_JZRSJBG_ZJJGMC = ZL_JZRSJBG_ZJJGMC;
    }

    @JsonProperty("JB_SJBLRQ")
    public String getJB_SJBLRQ() {
        return JB_SJBLRQ;
    }

    @JsonProperty("JB_SJBLRQ")
    public void setJB_SJBLRQ(String JB_SJBLRQ) {
        this.JB_SJBLRQ = JB_SJBLRQ;
    }

    @JsonProperty("XX_ZHYYYZHDZCPGZ")
    public String getXX_ZHYYYZHDZCPGZ() {
        return XX_ZHYYYZHDZCPGZ;
    }

    @JsonProperty("XX_ZHYYYZHDZCPGZ")
    public void setXX_ZHYYYZHDZCPGZ(String XX_ZHYYYZHDZCPGZ) {
        this.XX_ZHYYYZHDZCPGZ = XX_ZHYYYZHDZCPGZ;
    }

    @JsonProperty("XX_YYTZDGQZJ")
    public String getXX_YYTZDGQZJ() {
        return XX_YYTZDGQZJ;
    }

    @JsonProperty("XX_YYTZDGQZJ")
    public void setXX_YYTZDGQZJ(String XX_YYTZDGQZJ) {
        this.XX_YYTZDGQZJ = XX_YYTZDGQZJ;
    }

    @JsonProperty("FD1_BZ")
    public String getFD1_BZ() {
        return FD1_BZ;
    }

    @JsonProperty("FD1_BZ")
    public void setFD1_BZ(String FD1_BZ) {
        this.FD1_BZ = FD1_BZ;
    }

    @JsonProperty("JB_HJQYSBS")
    public String getJB_HJQYSBS() {
        return JB_HJQYSBS;
    }

    @JsonProperty("JB_HJQYSBS")
    public void setJB_HJQYSBS(String JB_HJQYSBS) {
        this.JB_HJQYSBS = JB_HJQYSBS;
    }

    @JsonProperty("FD8_ZJYJ")
    public String getFD8_ZJYJ() {
        return FD8_ZJYJ;
    }

    @JsonProperty("FD8_ZJYJ")
    public void setFD8_ZJYJ(String FD8_ZJYJ) {
        this.FD8_ZJYJ = FD8_ZJYJ;
    }

    @JsonProperty("JB_GJSBS")
    public String getJB_GJSBS() {
        return JB_GJSBS;
    }

    @JsonProperty("JB_GJSBS")
    public void setJB_GJSBS(String JB_GJSBS) {
        this.JB_GJSBS = JB_GJSBS;
    }

    @JsonProperty("JB_ZCDJW")
    public String getJB_ZCDJW() {
        return JB_ZCDJW;
    }

    @JsonProperty("JB_ZCDJW")
    public void setJB_ZCDJW(String JB_ZCDJW) {
        this.JB_ZCDJW = JB_ZCDJW;
    }

    @JsonProperty("FD_CZE")
    public String getFD_CZE() {
        return FD_CZE;
    }

    @JsonProperty("FD_CZE")
    public void setFD_CZE(String FD_CZE) {
        this.FD_CZE = FD_CZE;
    }

    @JsonProperty("JB_QTQYSDSBZ")
    public String getJB_QTQYSDSBZ() {
        return JB_QTQYSDSBZ;
    }

    @JsonProperty("JB_QTQYSDSBZ")
    public void setJB_QTQYSDSBZ(String JB_QTQYSDSBZ) {
        this.JB_QTQYSDSBZ = JB_QTQYSDSBZ;
    }

    @JsonProperty("ZL_PCGG_MTMC")
    public String getZL_PCGG_MTMC() {
        return ZL_PCGG_MTMC;
    }

    @JsonProperty("ZL_PCGG_MTMC")
    public void setZL_PCGG_MTMC(String ZL_PCGG_MTMC) {
        this.ZL_PCGG_MTMC = ZL_PCGG_MTMC;
    }

    @JsonProperty("ZL_WCHZXY_YW")
    public String getZL_WCHZXY_YW() {
        return ZL_WCHZXY_YW;
    }

    @JsonProperty("ZL_WCHZXY_YW")
    public void setZL_WCHZXY_YW(String ZL_WCHZXY_YW) {
        this.ZL_WCHZXY_YW = ZL_WCHZXY_YW;
    }

    @JsonProperty("FD3_BZ")
    public String getFD3_BZ() {
        return FD3_BZ;
    }

    @JsonProperty("FD3_BZ")
    public void setFD3_BZ(String FD3_BZ) {
        this.FD3_BZ = FD3_BZ;
    }

    @JsonProperty("JB_RJZBBZXZ")
    public String getJB_RJZBBZXZ() {
        return JB_RJZBBZXZ;
    }

    @JsonProperty("JB_RJZBBZXZ")
    public void setJB_RJZBBZXZ(String JB_RJZBBZXZ) {
        this.JB_RJZBBZXZ = JB_RJZBBZXZ;
    }

    @JsonProperty("FD6_SYCCFPJGHJ")
    public String getFD6_SYCCFPJGHJ() {
        return FD6_SYCCFPJGHJ;
    }

    @JsonProperty("FD6_SYCCFPJGHJ")
    public void setFD6_SYCCFPJGHJ(String FD6_SYCCFPJGHJ) {
        this.FD6_SYCCFPJGHJ = FD6_SYCCFPJGHJ;
    }

    @JsonProperty("XX_YWFHBJZ")
    public String getXX_YWFHBJZ() {
        return XX_YWFHBJZ;
    }

    @JsonProperty("XX_YWFHBJZ")
    public void setXX_YWFHBJZ(String XX_YWFHBJZ) {
        this.XX_YWFHBJZ = XX_YWFHBJZ;
    }

    @JsonProperty("FD2_YGQCYFMC")
    public String getFD2_YGQCYFMC() {
        return FD2_YGQCYFMC;
    }

    @JsonProperty("FD2_YGQCYFMC")
    public void setFD2_YGQCYFMC(String FD2_YGQCYFMC) {
        this.FD2_YGQCYFMC = FD2_YGQCYFMC;
    }

    @JsonProperty("JB_QYSBSBZXZ")
    public String getJB_QYSBSBZXZ() {
        return JB_QYSBSBZXZ;
    }

    @JsonProperty("JB_QYSBSBZXZ")
    public void setJB_QYSBSBZXZ(String JB_QYSBSBZXZ) {
        this.JB_QYSBSBZXZ = JB_QYSBSBZXZ;
    }

    @JsonProperty("FD8_SRGQBLHJ")
    public String getFD8_SRGQBLHJ() {
        return FD8_SRGQBLHJ;
    }

    @JsonProperty("FD8_SRGQBLHJ")
    public void setFD8_SRGQBLHJ(String FD8_SRGQBLHJ) {
        this.FD8_SRGQBLHJ = FD8_SRGQBLHJ;
    }

    @JsonProperty("ZL_FHBPGBA_HZDWMC")
    public String getZL_FHBPGBA_HZDWMC() {
        return ZL_FHBPGBA_HZDWMC;
    }

    @JsonProperty("ZL_FHBPGBA_HZDWMC")
    public void setZL_FHBPGBA_HZDWMC(String ZL_FHBPGBA_HZDWMC) {
        this.ZL_FHBPGBA_HZDWMC = ZL_FHBPGBA_HZDWMC;
    }

    @JsonProperty("JB_GJCZQYSBSBZ")
    public String getJB_GJCZQYSBSBZ() {
        return JB_GJCZQYSBSBZ;
    }

    @JsonProperty("JB_GJCZQYSBSBZ")
    public void setJB_GJCZQYSBSBZ(String JB_GJCZQYSBSBZ) {
        this.JB_GJCZQYSBSBZ = JB_GJCZQYSBSBZ;
    }

    @JsonProperty("FD_RJZBBZ")
    public String getFD_RJZBBZ() {
        return FD_RJZBBZ;
    }

    @JsonProperty("FD_RJZBBZ")
    public void setFD_RJZBBZ(String FD_RJZBBZ) {
        this.FD_RJZBBZ = FD_RJZBBZ;
    }

    @JsonProperty("XX_ZHYSSGJCZQY")
    public String getXX_ZHYSSGJCZQY() {
        return XX_ZHYSSGJCZQY;
    }

    @JsonProperty("XX_ZHYSSGJCZQY")
    public void setXX_ZHYSSGJCZQY(String XX_ZHYSSGJCZQY) {
        this.XX_ZHYSSGJCZQY = XX_ZHYSSGJCZQY;
    }

    @JsonProperty("JB_SDSBZXZ")
    public String getJB_SDSBZXZ() {
        return JB_SDSBZXZ;
    }

    @JsonProperty("JB_SDSBZXZ")
    public void setJB_SDSBZXZ(String JB_SDSBZXZ) {
        this.JB_SDSBZXZ = JB_SDSBZXZ;
    }

    @JsonProperty("FD2_PGJZCZHJ")
    public String getFD2_PGJZCZHJ() {
        return FD2_PGJZCZHJ;
    }

    @JsonProperty("FD2_PGJZCZHJ")
    public void setFD2_PGJZCZHJ(String FD2_PGJZCZHJ) {
        this.FD2_PGJZCZHJ = FD2_PGJZCZHJ;
    }

    @JsonProperty("IF_MONITORWARN")
    public String getIF_MONITORWARN() {
        return IF_MONITORWARN;
    }

    @JsonProperty("IF_MONITORWARN")
    public void setIF_MONITORWARN(String IF_MONITORWARN) {
        this.IF_MONITORWARN = IF_MONITORWARN;
    }

    @JsonProperty("JB_ZZJGDM")
    public String getJB_ZZJGDM() {
        return JB_ZZJGDM;
    }

    @JsonProperty("JB_ZZJGDM")
    public void setJB_ZZJGDM(String JB_ZZJGDM) {
        this.JB_ZZJGDM = JB_ZZJGDM;
    }

    @JsonProperty("ZL_JZGG_YW")
    public String getZL_JZGG_YW() {
        return ZL_JZGG_YW;
    }

    @JsonProperty("ZL_JZGG_YW")
    public void setZL_JZGG_YW(String ZL_JZGG_YW) {
        this.ZL_JZGG_YW = ZL_JZGG_YW;
    }

    @JsonProperty("JB_SFZY")
    public String getJB_SFZY() {
        return JB_SFZY;
    }

    @JsonProperty("JB_SFZY")
    public void setJB_SFZY(String JB_SFZY) {
        this.JB_SFZY = JB_SFZY;
    }

    @JsonProperty("FD8_SRGQBL")
    public String getFD8_SRGQBL() {
        return FD8_SRGQBL;
    }

    @JsonProperty("FD8_SRGQBL")
    public void setFD8_SRGQBL(String FD8_SRGQBL) {
        this.FD8_SRGQBL = FD8_SRGQBL;
    }

    @JsonProperty("FD8_ZRGQSJJZCZHJ")
    public String getFD8_ZRGQSJJZCZHJ() {
        return FD8_ZRGQSJJZCZHJ;
    }

    @JsonProperty("FD8_ZRGQSJJZCZHJ")
    public void setFD8_ZRGQSJJZCZHJ(String FD8_ZRGQSJJZCZHJ) {
        this.FD8_ZRGQSJJZCZHJ = FD8_ZRGQSJJZCZHJ;
    }

    @JsonProperty("ZL_ZHEPGBAB_HZDWMC")
    public String getZL_ZHEPGBAB_HZDWMC() {
        return ZL_ZHEPGBAB_HZDWMC;
    }

    @JsonProperty("ZL_ZHEPGBAB_HZDWMC")
    public void setZL_ZHEPGBAB_HZDWMC(String ZL_ZHEPGBAB_HZDWMC) {
        this.ZL_ZHEPGBAB_HZDWMC = ZL_ZHEPGBAB_HZDWMC;
    }

    @JsonProperty("JB_SSHY")
    public String getJB_SSHY() {
        return JB_SSHY;
    }

    @JsonProperty("JB_SSHY")
    public void setJB_SSHY(String JB_SSHY) {
        this.JB_SSHY = JB_SSHY;
    }

    @JsonProperty("XX_AZFYZE")
    public String getXX_AZFYZE() {
        return XX_AZFYZE;
    }

    @JsonProperty("XX_AZFYZE")
    public void setXX_AZFYZE(String XX_AZFYZE) {
        this.XX_AZFYZE = XX_AZFYZE;
    }

    @JsonProperty("FD5_CJFMC")
    public String getFD5_CJFMC() {
        return FD5_CJFMC;
    }

    @JsonProperty("FD5_CJFMC")
    public void setFD5_CJFMC(String FD5_CJFMC) {
        this.FD5_CJFMC = FD5_CJFMC;
    }

    @JsonProperty("JB_GYJDKGFRSDSBZ")
    public String getJB_GYJDKGFRSDSBZ() {
        return JB_GYJDKGFRSDSBZ;
    }

    @JsonProperty("JB_GYJDKGFRSDSBZ")
    public void setJB_GYJDKGFRSDSBZ(String JB_GYJDKGFRSDSBZ) {
        this.JB_GYJDKGFRSDSBZ = JB_GYJDKGFRSDSBZ;
    }

    @JsonProperty("XX_ZHEQYMC")
    public String getXX_ZHEQYMC() {
        return XX_ZHEQYMC;
    }

    @JsonProperty("XX_ZHEQYMC")
    public void setXX_ZHEQYMC(String XX_ZHEQYMC) {
        this.XX_ZHEQYMC = XX_ZHEQYMC;
    }

    @JsonProperty("JB_SHZT")
    public String getJB_SHZT() {
        return JB_SHZT;
    }

    @JsonProperty("JB_SHZT")
    public void setJB_SHZT(String JB_SHZT) {
        this.JB_SHZT = JB_SHZT;
    }

    @JsonProperty("BUSINESS_NATURE")
    public String getBUSINESS_NATURE() {
        return BUSINESS_NATURE;
    }

    @JsonProperty("BUSINESS_NATURE")
    public void setBUSINESS_NATURE(String BUSINESS_NATURE) {
        this.BUSINESS_NATURE = BUSINESS_NATURE;
    }

    @JsonProperty("XX_BXBFMC")
    public String getXX_BXBFMC() {
        return XX_BXBFMC;
    }

    @JsonProperty("XX_BXBFMC")
    public void setXX_BXBFMC(String XX_BXBFMC) {
        this.XX_BXBFMC = XX_BXBFMC;
    }

    @JsonProperty("XX_BXBFJZCZHXBFGQBL")
    public String getXX_BXBFJZCZHXBFGQBL() {
        return XX_BXBFJZCZHXBFGQBL;
    }

    @JsonProperty("XX_BXBFJZCZHXBFGQBL")
    public void setXX_BXBFJZCZHXBFGQBL(String XX_BXBFJZCZHXBFGQBL) {
        this.XX_BXBFJZCZHXBFGQBL = XX_BXBFJZCZHXBFGQBL;
    }

    @JsonProperty("JB_GJCZSDSBZ")
    public String getJB_GJCZSDSBZ() {
        return JB_GJCZSDSBZ;
    }

    @JsonProperty("JB_GJCZSDSBZ")
    public void setJB_GJCZSDSBZ(String JB_GJCZSDSBZ) {
        this.JB_GJCZSDSBZ = JB_GJCZSDSBZ;
    }

    @JsonProperty("ZL_BXBPGBAB_PGBGH")
    public String getZL_BXBPGBAB_PGBGH() {
        return ZL_BXBPGBAB_PGBGH;
    }

    @JsonProperty("ZL_BXBPGBAB_PGBGH")
    public void setZL_BXBPGBAB_PGBGH(String ZL_BXBPGBAB_PGBGH) {
        this.ZL_BXBPGBAB_PGBGH = ZL_BXBPGBAB_PGBGH;
    }

    @JsonProperty("FD_GQBL")
    public String getFD_GQBL() {
        return FD_GQBL;
    }

    @JsonProperty("FD_GQBL")
    public void setFD_GQBL(String FD_GQBL) {
        this.FD_GQBL = FD_GQBL;
    }

    @JsonProperty("ZL_YZBG_ZJJGMC")
    public String getZL_YZBG_ZJJGMC() {
        return ZL_YZBG_ZJJGMC;
    }

    @JsonProperty("ZL_YZBG_ZJJGMC")
    public void setZL_YZBG_ZJJGMC(String ZL_YZBG_ZJJGMC) {
        this.ZL_YZBG_ZJJGMC = ZL_YZBG_ZJJGMC;
    }

    @JsonProperty("ZL_PGBA_ZJJGMC")
    public String getZL_PGBA_ZJJGMC() {
        return ZL_PGBA_ZJJGMC;
    }

    @JsonProperty("ZL_PGBA_ZJJGMC")
    public void setZL_PGBA_ZJJGMC(String ZL_PGBA_ZJJGMC) {
        this.ZL_PGBA_ZJJGMC = ZL_PGBA_ZJJGMC;
    }

    @JsonProperty("ZL_ZHYPGBAB_HZWJH")
    public String getZL_ZHYPGBAB_HZWJH() {
        return ZL_ZHYPGBAB_HZWJH;
    }

    @JsonProperty("ZL_ZHYPGBAB_HZWJH")
    public void setZL_ZHYPGBAB_HZWJH(String ZL_ZHYPGBAB_HZWJH) {
        this.ZL_ZHYPGBAB_HZWJH = ZL_ZHYPGBAB_HZWJH;
    }

    @JsonProperty("ZL_PGBA_HZDWMC")
    public String getZL_PGBA_HZDWMC() {
        return ZL_PGBA_HZDWMC;
    }

    @JsonProperty("ZL_PGBA_HZDWMC")
    public void setZL_PGBA_HZDWMC(String ZL_PGBA_HZDWMC) {
        this.ZL_PGBA_HZDWMC = ZL_PGBA_HZDWMC;
    }

    @JsonProperty("JB_BDCQDJQX")
    public String getJB_BDCQDJQX() {
        return JB_BDCQDJQX;
    }

    @JsonProperty("JB_BDCQDJQX")
    public void setJB_BDCQDJQX(String JB_BDCQDJQX) {
        this.JB_BDCQDJQX = JB_BDCQDJQX;
    }

    @JsonProperty("ZL_YYZZ_YW")
    public String getZL_YYZZ_YW() {
        return ZL_YYZZ_YW;
    }

    @JsonProperty("ZL_YYZZ_YW")
    public void setZL_YYZZ_YW(String ZL_YYZZ_YW) {
        this.ZL_YYZZ_YW = ZL_YYZZ_YW;
    }

    @JsonProperty("XX_CXQYPGJZCZ")
    public String getXX_CXQYPGJZCZ() {
        return XX_CXQYPGJZCZ;
    }

    @JsonProperty("XX_CXQYPGJZCZ")
    public void setXX_CXQYPGJZCZ(String XX_CXQYPGJZCZ) {
        this.XX_CXQYPGJZCZ = XX_CXQYPGJZCZ;
    }

    @JsonProperty("FD7_HZGQBLHJ")
    public String getFD7_HZGQBLHJ() {
        return FD7_HZGQBLHJ;
    }

    @JsonProperty("FD7_HZGQBLHJ")
    public void setFD7_HZGQBLHJ(String FD7_HZGQBLHJ) {
        this.FD7_HZGQBLHJ = FD7_HZGQBLHJ;
    }

    @JsonProperty("ZL_GYTDBA_PZWH")
    public String getZL_GYTDBA_PZWH() {
        return ZL_GYTDBA_PZWH;
    }

    @JsonProperty("ZL_GYTDBA_PZWH")
    public void setZL_GYTDBA_PZWH(String ZL_GYTDBA_PZWH) {
        this.ZL_GYTDBA_PZWH = ZL_GYTDBA_PZWH;
    }

    @JsonProperty("ZL_GQSZFAWJ_PZWH")
    public String getZL_GQSZFAWJ_PZWH() {
        return ZL_GQSZFAWJ_PZWH;
    }

    @JsonProperty("ZL_GQSZFAWJ_PZWH")
    public void setZL_GQSZFAWJ_PZWH(String ZL_GQSZFAWJ_PZWH) {
        this.ZL_GQSZFAWJ_PZWH = ZL_GQSZFAWJ_PZWH;
    }

    @JsonProperty("FD7_HZJZCZ")
    public String getFD7_HZJZCZ() {
        return FD7_HZJZCZ;
    }

    @JsonProperty("FD7_HZJZCZ")
    public void setFD7_HZJZCZ(String FD7_HZJZCZ) {
        this.FD7_HZJZCZ = FD7_HZJZCZ;
    }

    @JsonProperty("ZL_ZHXY_LY")
    public String getZL_ZHXY_LY() {
        return ZL_ZHXY_LY;
    }

    @JsonProperty("ZL_ZHXY_LY")
    public void setZL_ZHXY_LY(String ZL_ZHXY_LY) {
        this.ZL_ZHXY_LY = ZL_ZHXY_LY;
    }

    @JsonProperty("ZL_FHBPGBA_LY")
    public String getZL_FHBPGBA_LY() {
        return ZL_FHBPGBA_LY;
    }

    @JsonProperty("ZL_FHBPGBA_LY")
    public void setZL_FHBPGBA_LY(String ZL_FHBPGBA_LY) {
        this.ZL_FHBPGBA_LY = ZL_FHBPGBA_LY;
    }

    @JsonProperty("FD1_ZFF")
    public String getFD1_ZFF() {
        return FD1_ZFF;
    }

    @JsonProperty("FD1_ZFF")
    public void setFD1_ZFF(String FD1_ZFF) {
        this.FD1_ZFF = FD1_ZFF;
    }
    @JsonProperty("jb_qygljc")
    public String getJb_qygljc() {
        return jb_qygljc;
    }
    @JsonProperty("jb_qygljc")
    public void setJb_qygljc(String jb_qygljc) {
        this.jb_qygljc = jb_qygljc;
    }

    @JsonProperty("JB_CZRZZJGMC")
    public String getJB_CZRZZJGMC() {
        return JB_CZRZZJGMC;
    }
    @JsonProperty("JB_CZRZZJGMC")
    public void setJB_CZRZZJGMC(String JB_CZRZZJGMC) {
        this.JB_CZRZZJGMC = JB_CZRZZJGMC;
    }

    @JsonProperty("ZL_GQSZFAWJ_PZDW")
    public String getZL_GQSZFAWJ_PZDW() {
        return ZL_GQSZFAWJ_PZDW;
    }

    @JsonProperty("ZL_GQSZFAWJ_PZDW")
    public void setZL_GQSZFAWJ_PZDW(String ZL_GQSZFAWJ_PZDW) {
        this.ZL_GQSZFAWJ_PZDW = ZL_GQSZFAWJ_PZDW;
    }

    @JsonProperty("FD2_SGGQBL")
    public String getFD2_SGGQBL() {
        return FD2_SGGQBL;
    }

    @JsonProperty("FD2_SGGQBL")
    public void setFD2_SGGQBL(String FD2_SGGQBL) {
        this.FD2_SGGQBL = FD2_SGGQBL;
    }

    @JsonProperty("ZL_SJBG_LY")
    public String getZL_SJBG_LY() {
        return ZL_SJBG_LY;
    }

    @JsonProperty("ZL_SJBG_LY")
    public void setZL_SJBG_LY(String ZL_SJBG_LY) {
        this.ZL_SJBG_LY = ZL_SJBG_LY;
    }

    @JsonProperty("ZL_GSZXZM_GSBMMC")
    public String getZL_GSZXZM_GSBMMC() {
        return ZL_GSZXZM_GSBMMC;
    }

    @JsonProperty("ZL_GSZXZM_GSBMMC")
    public void setZL_GSZXZM_GSBMMC(String ZL_GSZXZM_GSBMMC) {
        this.ZL_GSZXZM_GSBMMC = ZL_GSZXZM_GSBMMC;
    }

    @JsonProperty("FD7_HRFSSGZJGJG_ZX")
    public String getFD7_HRFSSGZJGJG_ZX() {
        return FD7_HRFSSGZJGJG_ZX;
    }

    @JsonProperty("FD7_HRFSSGZJGJG_ZX")
    public void setFD7_HRFSSGZJGJG_ZX(String FD7_HRFSSGZJGJG_ZX) {
        this.FD7_HRFSSGZJGJG_ZX = FD7_HRFSSGZJGJG_ZX;
    }

    @JsonProperty("ZL_BDPGBA_HZWJH")
    public String getZL_BDPGBA_HZWJH() {
        return ZL_BDPGBA_HZWJH;
    }

    @JsonProperty("ZL_BDPGBA_HZWJH")
    public void setZL_BDPGBA_HZWJH(String ZL_BDPGBA_HZWJH) {
        this.ZL_BDPGBA_HZWJH = ZL_BDPGBA_HZWJH;
    }

    @JsonProperty("JB_SJCZR")
    public String getJB_SJCZR() {
        return JB_SJCZR;
    }

    @JsonProperty("JB_SJCZR")
    public void setJB_SJCZR(String JB_SJCZR) {
        this.JB_SJCZR = JB_SJCZR;
    }

    @JsonProperty("JB_CZRZZJGDM")
    public String getJB_CZRZZJGDM() {
        return JB_CZRZZJGDM;
    }

    @JsonProperty("JB_CZRZZJGDM")
    public void setJB_CZRZZJGDM(String JB_CZRZZJGDM) {
        this.JB_CZRZZJGDM = JB_CZRZZJGDM;
    }

    @JsonProperty("JB_GYZB")
    public String getJB_GYZB() {
        return JB_GYZB;
    }

    @JsonProperty("JB_GYZB")
    public void setJB_GYZB(String JB_GYZB) {
        this.JB_GYZB = JB_GYZB;
    }

    @JsonProperty("XX_BXBFPGJZCZ")
    public String getXX_BXBFPGJZCZ() {
        return XX_BXBFPGJZCZ;
    }

    @JsonProperty("XX_BXBFPGJZCZ")
    public void setXX_BXBFPGJZCZ(String XX_BXBFPGJZCZ) {
        this.XX_BXBFPGJZCZ = XX_BXBFPGJZCZ;
    }

    @JsonProperty("XX_CJFSSGZJGJG")
    public String getXX_CJFSSGZJGJG() {
        return XX_CJFSSGZJGJG;
    }

    @JsonProperty("XX_CJFSSGZJGJG")
    public void setXX_CJFSSGZJGJG(String XX_CJFSSGZJGJG) {
        this.XX_CJFSSGZJGJG = XX_CJFSSGZJGJG;
    }

    @JsonProperty("XX_PTZW")
    public String getXX_PTZW() {
        return XX_PTZW;
    }

    @JsonProperty("XX_PTZW")
    public void setXX_PTZW(String XX_PTZW) {
        this.XX_PTZW = XX_PTZW;
    }

    @JsonProperty("FD7_HZBDQYMC")
    public String getFD7_HZBDQYMC() {
        return FD7_HZBDQYMC;
    }

    @JsonProperty("FD7_HZBDQYMC")
    public void setFD7_HZBDQYMC(String FD7_HZBDQYMC) {
        this.FD7_HZBDQYMC = FD7_HZBDQYMC;
    }

    @JsonProperty("ZL_JZRSJBG_LY")
    public String getZL_JZRSJBG_LY() {
        return ZL_JZRSJBG_LY;
    }

    @JsonProperty("ZL_JZRSJBG_LY")
    public void setZL_JZRSJBG_LY(String ZL_JZRSJBG_LY) {
        this.ZL_JZRSJBG_LY = ZL_JZRSJBG_LY;
    }

    @JsonProperty("ZL_SJBG_YW")
    public String getZL_SJBG_YW() {
        return ZL_SJBG_YW;
    }

    @JsonProperty("ZL_SJBG_YW")
    public void setZL_SJBG_YW(String ZL_SJBG_YW) {
        this.ZL_SJBG_YW = ZL_SJBG_YW;
    }

    @JsonProperty("ZL_TZXY_LY")
    public String getZL_TZXY_LY() {
        return ZL_TZXY_LY;
    }

    @JsonProperty("ZL_TZXY_LY")
    public void setZL_TZXY_LY(String ZL_TZXY_LY) {
        this.ZL_TZXY_LY = ZL_TZXY_LY;
    }

    @JsonProperty("ZL_ZHYPGBAB_PGBGH")
    public String getZL_ZHYPGBAB_PGBGH() {
        return ZL_ZHYPGBAB_PGBGH;
    }

    @JsonProperty("ZL_ZHYPGBAB_PGBGH")
    public void setZL_ZHYPGBAB_PGBGH(String ZL_ZHYPGBAB_PGBGH) {
        this.ZL_ZHYPGBAB_PGBGH = ZL_ZHYPGBAB_PGBGH;
    }

    @JsonProperty("ZL_SJBG_ZJJG")
    public String getZL_SJBG_ZJJG() {
        return ZL_SJBG_ZJJG;
    }

    @JsonProperty("ZL_SJBG_ZJJG")
    public void setZL_SJBG_ZJJG(String ZL_SJBG_ZJJG) {
        this.ZL_SJBG_ZJJG = ZL_SJBG_ZJJG;
    }

    @JsonProperty("ZL_BXBPGBAB_HZDWMC")
    public String getZL_BXBPGBAB_HZDWMC() {
        return ZL_BXBPGBAB_HZDWMC;
    }

    @JsonProperty("ZL_BXBPGBAB_HZDWMC")
    public void setZL_BXBPGBAB_HZDWMC(String ZL_BXBPGBAB_HZDWMC) {
        this.ZL_BXBPGBAB_HZDWMC = ZL_BXBPGBAB_HZDWMC;
    }

    @JsonProperty("ZL_YZBG_YW")
    public String getZL_YZBG_YW() {
        return ZL_YZBG_YW;
    }

    @JsonProperty("ZL_YZBG_YW")
    public void setZL_YZBG_YW(String ZL_YZBG_YW) {
        this.ZL_YZBG_YW = ZL_YZBG_YW;
    }

    @JsonProperty("ZL_GYTDBA_LY")
    public String getZL_GYTDBA_LY() {
        return ZL_GYTDBA_LY;
    }

    @JsonProperty("ZL_GYTDBA_LY")
    public void setZL_GYTDBA_LY(String ZL_GYTDBA_LY) {
        this.ZL_GYTDBA_LY = ZL_GYTDBA_LY;
    }

    @JsonProperty("FD7_HCFSSGJCZQY")
    public String getFD7_HCFSSGJCZQY() {
        return FD7_HCFSSGJCZQY;
    }

    @JsonProperty("FD7_HCFSSGJCZQY")
    public void setFD7_HCFSSGJCZQY(String FD7_HCFSSGJCZQY) {
        this.FD7_HCFSSGJCZQY = FD7_HCFSSGJCZQY;
    }

    @JsonProperty("FD9_INFO")
    public String getFD9_INFO() {
        return FD9_INFO;
    }

    @JsonProperty("FD9_INFO")
    public void setFD9_INFO(String FD9_INFO) {
        this.FD9_INFO = FD9_INFO;
    }

    @JsonProperty("ZL_JCJG_CJRQ")
    public String getZL_JCJG_CJRQ() {
        return ZL_JCJG_CJRQ;
    }

    @JsonProperty("ZL_JCJG_CJRQ")
    public void setZL_JCJG_CJRQ(String ZL_JCJG_CJRQ) {
        this.ZL_JCJG_CJRQ = ZL_JCJG_CJRQ;
    }

    @JsonProperty("ZL_QYZC_YW")
    public String getZL_QYZC_YW() {
        return ZL_QYZC_YW;
    }

    @JsonProperty("ZL_QYZC_YW")
    public void setZL_QYZC_YW(String ZL_QYZC_YW) {
        this.ZL_QYZC_YW = ZL_QYZC_YW;
    }

    @JsonProperty("JB_HJSDS")
    public String getJB_HJSDS() {
        return JB_HJSDS;
    }

    @JsonProperty("JB_HJSDS")
    public void setJB_HJSDS(String JB_HJSDS) {
        this.JB_HJSDS = JB_HJSDS;
    }

    @JsonProperty("ZL_GDQKDJB_YW")
    public String getZL_GDQKDJB_YW() {
        return ZL_GDQKDJB_YW;
    }

    @JsonProperty("ZL_GDQKDJB_YW")
    public void setZL_GDQKDJB_YW(String ZL_GDQKDJB_YW) {
        this.ZL_GDQKDJB_YW = ZL_GDQKDJB_YW;
    }

    @JsonProperty("RG_TIMEMARK")
    public String getRG_TIMEMARK() {
        return RG_TIMEMARK;
    }

    @JsonProperty("RG_TIMEMARK")
    public void setRG_TIMEMARK(String RG_TIMEMARK) {
        this.RG_TIMEMARK = RG_TIMEMARK;
    }

    @JsonProperty("FD2_SGGQBLHJ")
    public String getFD2_SGGQBLHJ() {
        return FD2_SGGQBLHJ;
    }

    @JsonProperty("FD2_SGGQBLHJ")
    public void setFD2_SGGQBLHJ(String FD2_SGGQBLHJ) {
        this.FD2_SGGQBLHJ = FD2_SGGQBLHJ;
    }

    @JsonProperty("FD7_HRFMC_ZX")
    public String getFD7_HRFMC_ZX() {
        return FD7_HRFMC_ZX;
    }

    @JsonProperty("FD7_HRFMC_ZX")
    public void setFD7_HRFMC_ZX(String FD7_HRFMC_ZX) {
        this.FD7_HRFMC_ZX = FD7_HRFMC_ZX;
    }

    @JsonProperty("FD3_XGZJGFD")
    public String getFD3_XGZJGFD() {
        return FD3_XGZJGFD;
    }

    @JsonProperty("FD3_XGZJGFD")
    public void setFD3_XGZJGFD(String FD3_XGZJGFD) {
        this.FD3_XGZJGFD = FD3_XGZJGFD;
    }

    @JsonProperty("ZL_JCWJ_WJMC")
    public String getZL_JCWJ_WJMC() {
        return ZL_JCWJ_WJMC;
    }

    @JsonProperty("ZL_JCWJ_WJMC")
    public void setZL_JCWJ_WJMC(String ZL_JCWJ_WJMC) {
        this.ZL_JCWJ_WJMC = ZL_JCWJ_WJMC;
    }

    @JsonProperty("JB_HJSJZCJBZ")
    public String getJB_HJSJZCJBZ() {
        return JB_HJSJZCJBZ;
    }

    @JsonProperty("JB_HJSJZCJBZ")
    public void setJB_HJSJZCJBZ(String JB_HJSJZCJBZ) {
        this.JB_HJSJZCJBZ = JB_HJSJZCJBZ;
    }

    @JsonProperty("RG_SOLUTIONID")
    public String getRG_SOLUTIONID() {
        return RG_SOLUTIONID;
    }

    @JsonProperty("RG_SOLUTIONID")
    public void setRG_SOLUTIONID(String RG_SOLUTIONID) {
        this.RG_SOLUTIONID = RG_SOLUTIONID;
    }

    @JsonProperty("XX_ZHESSGZJGJG")
    public String getXX_ZHESSGZJGJG() {
        return XX_ZHESSGZJGJG;
    }

    @JsonProperty("XX_ZHESSGZJGJG")
    public void setXX_ZHESSGZJGJG(String XX_ZHESSGZJGJG) {
        this.XX_ZHESSGZJGJG = XX_ZHESSGZJGJG;
    }

    @JsonProperty("JB_QTSBSBZ")
    public String getJB_QTSBSBZ() {
        return JB_QTSBSBZ;
    }

    @JsonProperty("JB_QTSBSBZ")
    public void setJB_QTSBSBZ(String JB_QTSBSBZ) {
        this.JB_QTSBSBZ = JB_QTSBSBZ;
    }

    @JsonProperty("JB_GYJDKGSDS")
    public String getJB_GYJDKGSDS() {
        return JB_GYJDKGSDS;
    }

    @JsonProperty("JB_GYJDKGSDS")
    public void setJB_GYJDKGSDS(String JB_GYJDKGSDS) {
        this.JB_GYJDKGSDS = JB_GYJDKGSDS;
    }

    @JsonProperty("ZL_ZHYPGBAB_YW")
    public String getZL_ZHYPGBAB_YW() {
        return ZL_ZHYPGBAB_YW;
    }

    @JsonProperty("ZL_ZHYPGBAB_YW")
    public void setZL_ZHYPGBAB_YW(String ZL_ZHYPGBAB_YW) {
        this.ZL_ZHYPGBAB_YW = ZL_ZHYPGBAB_YW;
    }

    @JsonProperty("JB_GJCZQY")
    public String getJB_GJCZQY() {
        return JB_GJCZQY;
    }

    @JsonProperty("JB_GJCZQY")
    public void setJB_GJCZQY(String JB_GJCZQY) {
        this.JB_GJCZQY = JB_GJCZQY;
    }

    @JsonProperty("ZL_FHBPGBA_PGBGH")
    public String getZL_FHBPGBA_PGBGH() {
        return ZL_FHBPGBA_PGBGH;
    }

    @JsonProperty("ZL_FHBPGBA_PGBGH")
    public void setZL_FHBPGBA_PGBGH(String ZL_FHBPGBA_PGBGH) {
        this.ZL_FHBPGBA_PGBGH = ZL_FHBPGBA_PGBGH;
    }

    @JsonProperty("JB_GYFRCZSDSBZ")
    public String getJB_GYFRCZSDSBZ() {
        return JB_GYFRCZSDSBZ;
    }

    @JsonProperty("JB_GYFRCZSDSBZ")
    public void setJB_GYFRCZSDSBZ(String JB_GYFRCZSDSBZ) {
        this.JB_GYFRCZSDSBZ = JB_GYFRCZSDSBZ;
    }

    @JsonProperty("ZL_SYZCCZXY_YW")
    public String getZL_SYZCCZXY_YW() {
        return ZL_SYZCCZXY_YW;
    }

    @JsonProperty("ZL_SYZCCZXY_YW")
    public void setZL_SYZCCZXY_YW(String ZL_SYZCCZXY_YW) {
        this.ZL_SYZCCZXY_YW = ZL_SYZCCZXY_YW;
    }

    @JsonProperty("FD2_ZJYJ")
    public String getFD2_ZJYJ() {
        return FD2_ZJYJ;
    }

    @JsonProperty("FD2_ZJYJ")
    public void setFD2_ZJYJ(String FD2_ZJYJ) {
        this.FD2_ZJYJ = FD2_ZJYJ;
    }

    @JsonProperty("FD1_CZZJHJ")
    public String getFD1_CZZJHJ() {
        return FD1_CZZJHJ;
    }

    @JsonProperty("FD1_CZZJHJ")
    public void setFD1_CZZJHJ(String FD1_CZZJHJ) {
        this.FD1_CZZJHJ = FD1_CZZJHJ;
    }

    @JsonProperty("ZL_JZGG_GGRQ")
    public String getZL_JZGG_GGRQ() {
        return ZL_JZGG_GGRQ;
    }

    @JsonProperty("ZL_JZGG_GGRQ")
    public void setZL_JZGG_GGRQ(String ZL_JZGG_GGRQ) {
        this.ZL_JZGG_GGRQ = ZL_JZGG_GGRQ;
    }

    @JsonProperty("ZL_FJ")
    public String getZL_FJ() {
        return ZL_FJ;
    }

    @JsonProperty("ZL_FJ")
    public void setZL_FJ(String ZL_FJ) {
        this.ZL_FJ = ZL_FJ;
    }

    @JsonProperty("XX_QSFPSQSK")
    public String getXX_QSFPSQSK() {
        return XX_QSFPSQSK;
    }

    @JsonProperty("XX_QSFPSQSK")
    public void setXX_QSFPSQSK(String XX_QSFPSQSK) {
        this.XX_QSFPSQSK = XX_QSFPSQSK;
    }

    @JsonProperty("JB_ZYCQDJQX")
    public String getJB_ZYCQDJQX() {
        return JB_ZYCQDJQX;
    }

    @JsonProperty("JB_ZYCQDJQX")
    public void setJB_ZYCQDJQX(String JB_ZYCQDJQX) {
        this.JB_ZYCQDJQX = JB_ZYCQDJQX;
    }

    @JsonProperty("FD_CZRZZJGDM")
    public String getFD_CZRZZJGDM() {
        return FD_CZRZZJGDM;
    }

    @JsonProperty("FD_CZRZZJGDM")
    public void setFD_CZRZZJGDM(String FD_CZRZZJGDM) {
        this.FD_CZRZZJGDM = FD_CZRZZJGDM;
    }

    @JsonProperty("JB_GZJGJG")
    public String getJB_GZJGJG() {
        return JB_GZJGJG;
    }

    @JsonProperty("JB_GZJGJG")
    public void setJB_GZJGJG(String JB_GZJGJG) {
        this.JB_GZJGJG = JB_GZJGJG;
    }

    @JsonProperty("FD8_ZRGQPGJZCZ")
    public String getFD8_ZRGQPGJZCZ() {
        return FD8_ZRGQPGJZCZ;
    }

    @JsonProperty("FD8_ZRGQPGJZCZ")
    public void setFD8_ZRGQPGJZCZ(String FD8_ZRGQPGJZCZ) {
        this.FD8_ZRGQPGJZCZ = FD8_ZRGQPGJZCZ;
    }

    @JsonProperty("XX_JSYY")
    public String getXX_JSYY() {
        return XX_JSYY;
    }

    @JsonProperty("XX_JSYY")
    public void setXX_JSYY(String XX_JSYY) {
        this.XX_JSYY = XX_JSYY;
    }

    @JsonProperty("XX_BDQYMC")
    public String getXX_BDQYMC() {
        return XX_BDQYMC;
    }

    @JsonProperty("XX_BDQYMC")
    public void setXX_BDQYMC(String XX_BDQYMC) {
        this.XX_BDQYMC = XX_BDQYMC;
    }

    @JsonProperty("ZL_XBPGBAB_HZDWMC")
    public String getZL_XBPGBAB_HZDWMC() {
        return ZL_XBPGBAB_HZDWMC;
    }

    @JsonProperty("ZL_XBPGBAB_HZDWMC")
    public void setZL_XBPGBAB_HZDWMC(String ZL_XBPGBAB_HZDWMC) {
        this.ZL_XBPGBAB_HZDWMC = ZL_XBPGBAB_HZDWMC;
    }

    @JsonProperty("XX_ZHESSGJCZQY")
    public String getXX_ZHESSGJCZQY() {
        return XX_ZHESSGJCZQY;
    }

    @JsonProperty("XX_ZHESSGJCZQY")
    public void setXX_ZHESSGJCZQY(String XX_ZHESSGJCZQY) {
        this.XX_ZHESSGJCZQY = XX_ZHESSGJCZQY;
    }

    @JsonProperty("JB_GYJDKGFRSBSBZ")
    public String getJB_GYJDKGFRSBSBZ() {
        return JB_GYJDKGFRSBSBZ;
    }

    @JsonProperty("JB_GYJDKGFRSBSBZ")
    public void setJB_GYJDKGFRSBSBZ(String JB_GYJDKGFRSBSBZ) {
        this.JB_GYJDKGFRSBSBZ = JB_GYJDKGFRSBSBZ;
    }

    @JsonProperty("FD8_ZRFMC")
    public String getFD8_ZRFMC() {
        return FD8_ZRFMC;
    }

    @JsonProperty("FD8_ZRFMC")
    public void setFD8_ZRFMC(String FD8_ZRFMC) {
        this.FD8_ZRFMC = FD8_ZRFMC;
    }

    @JsonProperty("ZL_GQSZFAWJ_LY")
    public String getZL_GQSZFAWJ_LY() {
        return ZL_GQSZFAWJ_LY;
    }

    @JsonProperty("ZL_GQSZFAWJ_LY")
    public void setZL_GQSZFAWJ_LY(String ZL_GQSZFAWJ_LY) {
        this.ZL_GQSZFAWJ_LY = ZL_GQSZFAWJ_LY;
    }

    @JsonProperty("XX_BDQYSSGZJGJG")
    public String getXX_BDQYSSGZJGJG() {
        return XX_BDQYSSGZJGJG;
    }

    @JsonProperty("XX_BDQYSSGZJGJG")
    public void setXX_BDQYSSGZJGJG(String XX_BDQYSSGZJGJG) {
        this.XX_BDQYSSGZJGJG = XX_BDQYSSGZJGJG;
    }

    @JsonProperty("ZL_TJPGBAB_YW")
    public String getZL_TJPGBAB_YW() {
        return ZL_TJPGBAB_YW;
    }

    @JsonProperty("ZL_TJPGBAB_YW")
    public void setZL_TJPGBAB_YW(String ZL_TJPGBAB_YW) {
        this.ZL_TJPGBAB_YW = ZL_TJPGBAB_YW;
    }

    @JsonProperty("ZL_BXBPGBAB_ZJJGMC")
    public String getZL_BXBPGBAB_ZJJGMC() {
        return ZL_BXBPGBAB_ZJJGMC;
    }

    @JsonProperty("ZL_BXBPGBAB_ZJJGMC")
    public void setZL_BXBPGBAB_ZJJGMC(String ZL_BXBPGBAB_ZJJGMC) {
        this.ZL_BXBPGBAB_ZJJGMC = ZL_BXBPGBAB_ZJJGMC;
    }

    @JsonProperty("ZL_WCHZXY_LY")
    public String getZL_WCHZXY_LY() {
        return ZL_WCHZXY_LY;
    }

    @JsonProperty("ZL_WCHZXY_LY")
    public void setZL_WCHZXY_LY(String ZL_WCHZXY_LY) {
        this.ZL_WCHZXY_LY = ZL_WCHZXY_LY;
    }

    @JsonProperty("FD7_HRFSSGJCZQY")
    public String getFD7_HRFSSGJCZQY() {
        return FD7_HRFSSGJCZQY;
    }

    @JsonProperty("FD7_HRFSSGJCZQY")
    public void setFD7_HRFSSGJCZQY(String FD7_HRFSSGJCZQY) {
        this.FD7_HRFSSGJCZQY = FD7_HRFSSGJCZQY;
    }

    @JsonProperty("FD8_ZRGQBLHJ")
    public String getFD8_ZRGQBLHJ() {
        return FD8_ZRGQBLHJ;
    }

    @JsonProperty("FD8_ZRGQBLHJ")
    public void setFD8_ZRGQBLHJ(String FD8_ZRGQBLHJ) {
        this.FD8_ZRGQBLHJ = FD8_ZRGQBLHJ;
    }

    @JsonProperty("ZL_ZJYQSJBG_BGH")
    public String getZL_ZJYQSJBG_BGH() {
        return ZL_ZJYQSJBG_BGH;
    }

    @JsonProperty("ZL_ZJYQSJBG_BGH")
    public void setZL_ZJYQSJBG_BGH(String ZL_ZJYQSJBG_BGH) {
        this.ZL_ZJYQSJBG_BGH = ZL_ZJYQSJBG_BGH;
    }

    @JsonProperty("FD7_HRFSSGZJGJG")
    public String getFD7_HRFSSGZJGJG() {
        return FD7_HRFSSGZJGJG;
    }

    @JsonProperty("FD7_HRFSSGZJGJG")
    public void setFD7_HRFSSGZJGJG(String FD7_HRFSSGZJGJG) {
        this.FD7_HRFSSGZJGJG = FD7_HRFSSGZJGJG;
    }

    @JsonProperty("JB_SFZDYJSJ")
    public String getJB_SFZDYJSJ() {
        return JB_SFZDYJSJ;
    }

    @JsonProperty("JB_SFZDYJSJ")
    public void setJB_SFZDYJSJ(String JB_SFZDYJSJ) {
        this.JB_SFZDYJSJ = JB_SFZDYJSJ;
    }

    @JsonProperty("ZL_SYZCCZXY_LY")
    public String getZL_SYZCCZXY_LY() {
        return ZL_SYZCCZXY_LY;
    }

    @JsonProperty("ZL_SYZCCZXY_LY")
    public void setZL_SYZCCZXY_LY(String ZL_SYZCCZXY_LY) {
        this.ZL_SYZCCZXY_LY = ZL_SYZCCZXY_LY;
    }

    @JsonProperty("JB_HJCZE")
    public String getJB_HJCZE() {
        return JB_HJCZE;
    }

    @JsonProperty("JB_HJCZE")
    public void setJB_HJCZE(String JB_HJCZE) {
        this.JB_HJCZE = JB_HJCZE;
    }

    @JsonProperty("JB_GYKGCZ")
    public String getJB_GYKGCZ() {
        return JB_GYKGCZ;
    }

    @JsonProperty("JB_GYKGCZ")
    public void setJB_GYKGCZ(String JB_GYKGCZ) {
        this.JB_GYKGCZ = JB_GYKGCZ;
    }

    @JsonProperty("ZL_GQZR_YW")
    public String getZL_GQZR_YW() {
        return ZL_GQZR_YW;
    }

    @JsonProperty("ZL_GQZR_YW")
    public void setZL_GQZR_YW(String ZL_GQZR_YW) {
        this.ZL_GQZR_YW = ZL_GQZR_YW;
    }

    @JsonProperty("ZL_PGBA_PGBGH")
    public String getZL_PGBA_PGBGH() {
        return ZL_PGBA_PGBGH;
    }

    @JsonProperty("ZL_PGBA_PGBGH")
    public void setZL_PGBA_PGBGH(String ZL_PGBA_PGBGH) {
        this.ZL_PGBA_PGBGH = ZL_PGBA_PGBGH;
    }

    @JsonProperty("JB_HJSDSBZ")
    public String getJB_HJSDSBZ() {
        return JB_HJSDSBZ;
    }

    @JsonProperty("JB_HJSDSBZ")
    public void setJB_HJSDSBZ(String JB_HJSDSBZ) {
        this.JB_HJSDSBZ = JB_HJSDSBZ;
    }

    @JsonProperty("XX_ZGBZ_XZGB")
    public String getXX_ZGBZ_XZGB() {
        return XX_ZGBZ_XZGB;
    }

    @JsonProperty("XX_ZGBZ_XZGB")
    public void setXX_ZGBZ_XZGB(String XX_ZGBZ_XZGB) {
        this.XX_ZGBZ_XZGB = XX_ZGBZ_XZGB;
    }

    @JsonProperty("ZL_SJBG_BGH")
    public String getZL_SJBG_BGH() {
        return ZL_SJBG_BGH;
    }

    @JsonProperty("ZL_SJBG_BGH")
    public void setZL_SJBG_BGH(String ZL_SJBG_BGH) {
        this.ZL_SJBG_BGH = ZL_SJBG_BGH;
    }

    @JsonProperty("ZL_ZHEPGBAB_YW")
    public String getZL_ZHEPGBAB_YW() {
        return ZL_ZHEPGBAB_YW;
    }

    @JsonProperty("ZL_ZHEPGBAB_YW")
    public void setZL_ZHEPGBAB_YW(String ZL_ZHEPGBAB_YW) {
        this.ZL_ZHEPGBAB_YW = ZL_ZHEPGBAB_YW;
    }

    @JsonProperty("ZL_PGBA_LY")
    public String getZL_PGBA_LY() {
        return ZL_PGBA_LY;
    }

    @JsonProperty("ZL_PGBA_LY")
    public void setZL_PGBA_LY(String ZL_PGBA_LY) {
        this.ZL_PGBA_LY = ZL_PGBA_LY;
    }

    @JsonProperty("ZL_JCJG_JGD")
    public String getZL_JCJG_JGD() {
        return ZL_JCJG_JGD;
    }

    @JsonProperty("ZL_JCJG_JGD")
    public void setZL_JCJG_JGD(String ZL_JCJG_JGD) {
        this.ZL_JCJG_JGD = ZL_JCJG_JGD;
    }

    @JsonProperty("XX_BDQYSJJZCZ")
    public String getXX_BDQYSJJZCZ() {
        return XX_BDQYSJJZCZ;
    }

    @JsonProperty("XX_BDQYSJJZCZ")
    public void setXX_BDQYSJJZCZ(String XX_BDQYSJJZCZ) {
        this.XX_BDQYSJJZCZ = XX_BDQYSJJZCZ;
    }

    @JsonProperty("ZL_PCGG_LY")
    public String getZL_PCGG_LY() {
        return ZL_PCGG_LY;
    }

    @JsonProperty("ZL_PCGG_LY")
    public void setZL_PCGG_LY(String ZL_PCGG_LY) {
        this.ZL_PCGG_LY = ZL_PCGG_LY;
    }

    @JsonProperty("XX_GKFXGS")
    public String getXX_GKFXGS() {
        return XX_GKFXGS;
    }

    @JsonProperty("XX_GKFXGS")
    public void setXX_GKFXGS(String XX_GKFXGS) {
        this.XX_GKFXGS = XX_GKFXGS;
    }

    @JsonProperty("AF_CURRENT_NODE")
    public String getAF_CURRENT_NODE() {
        return AF_CURRENT_NODE;
    }

    @JsonProperty("AF_CURRENT_NODE")
    public void setAF_CURRENT_NODE(String AF_CURRENT_NODE) {
        this.AF_CURRENT_NODE = AF_CURRENT_NODE;
    }

    @JsonProperty("XX_ZHYBZ")
    public String getXX_ZHYBZ() {
        return XX_ZHYBZ;
    }

    @JsonProperty("XX_ZHYBZ")
    public void setXX_ZHYBZ(String XX_ZHYBZ) {
        this.XX_ZHYBZ = XX_ZHYBZ;
    }

    @JsonProperty("ZL_ZHEPGBAB_LY")
    public String getZL_ZHEPGBAB_LY() {
        return ZL_ZHEPGBAB_LY;
    }

    @JsonProperty("ZL_ZHEPGBAB_LY")
    public void setZL_ZHEPGBAB_LY(String ZL_ZHEPGBAB_LY) {
        this.ZL_ZHEPGBAB_LY = ZL_ZHEPGBAB_LY;
    }

    @JsonProperty("FD1_CZZJ")
    public String getFD1_CZZJ() {
        return FD1_CZZJ;
    }

    @JsonProperty("FD1_CZZJ")
    public void setFD1_CZZJ(String FD1_CZZJ) {
        this.FD1_CZZJ = FD1_CZZJ;
    }

    @JsonProperty("JB_GYFRCZSBSBZ")
    public String getJB_GYFRCZSBSBZ() {
        return JB_GYFRCZSBSBZ;
    }

    @JsonProperty("JB_GYFRCZSBSBZ")
    public void setJB_GYFRCZSBSBZ(String JB_GYFRCZSBSBZ) {
        this.JB_GYFRCZSBSBZ = JB_GYFRCZSBSBZ;
    }

    @JsonProperty("FD1_HFBZCLB")
    public String getFD1_HFBZCLB() {
        return FD1_HFBZCLB;
    }

    @JsonProperty("FD1_HFBZCLB")
    public void setFD1_HFBZCLB(String FD1_HFBZCLB) {
        this.FD1_HFBZCLB = FD1_HFBZCLB;
    }

    @JsonProperty("XX_BDQYSJJZCZ_BGZ")
    public String getXX_BDQYSJJZCZ_BGZ() {
        return XX_BDQYSJJZCZ_BGZ;
    }

    @JsonProperty("XX_BDQYSJJZCZ_BGZ")
    public void setXX_BDQYSJJZCZ_BGZ(String XX_BDQYSJJZCZ_BGZ) {
        this.XX_BDQYSJJZCZ_BGZ = XX_BDQYSJJZCZ_BGZ;
    }

    @JsonProperty("JB_ZZXS")
    public String getJB_ZZXS() {
        return JB_ZZXS;
    }

    @JsonProperty("JB_ZZXS")
    public void setJB_ZZXS(String JB_ZZXS) {
        this.JB_ZZXS = JB_ZZXS;
    }

    @JsonProperty("XX_YYTZGQZHBDQYGQBL")
    public String getXX_YYTZGQZHBDQYGQBL() {
        return XX_YYTZGQZHBDQYGQBL;
    }

    @JsonProperty("XX_YYTZGQZHBDQYGQBL")
    public void setXX_YYTZGQZHBDQYGQBL(String XX_YYTZGQZHBDQYGQBL) {
        this.XX_YYTZGQZHBDQYGQBL = XX_YYTZGQZHBDQYGQBL;
    }

    @JsonProperty("JB_ZCZBBZ")
    public String getJB_ZCZBBZ() {
        return JB_ZCZBBZ;
    }

    @JsonProperty("JB_ZCZBBZ")
    public void setJB_ZCZBBZ(String JB_ZCZBBZ) {
        this.JB_ZCZBBZ = JB_ZCZBBZ;
    }

    @JsonProperty("JB_HJQYSBSBZ")
    public String getJB_HJQYSBSBZ() {
        return JB_HJQYSBSBZ;
    }

    @JsonProperty("JB_HJQYSBSBZ")
    public void setJB_HJQYSBSBZ(String JB_HJQYSBSBZ) {
        this.JB_HJQYSBSBZ = JB_HJQYSBSBZ;
    }

    @JsonProperty("FD1_ZFZJ")
    public String getFD1_ZFZJ() {
        return FD1_ZFZJ;
    }

    @JsonProperty("FD1_ZFZJ")
    public void setFD1_ZFZJ(String FD1_ZFZJ) {
        this.FD1_ZFZJ = FD1_ZFZJ;
    }

    @JsonProperty("FD1_PGZHJ")
    public String getFD1_PGZHJ() {
        return FD1_PGZHJ;
    }

    @JsonProperty("FD1_PGZHJ")
    public void setFD1_PGZHJ(String FD1_PGZHJ) {
        this.FD1_PGZHJ = FD1_PGZHJ;
    }

    @JsonProperty("FD7_HZJZR")
    public String getFD7_HZJZR() {
        return FD7_HZJZR;
    }

    @JsonProperty("FD7_HZJZR")
    public void setFD7_HZJZR(String FD7_HZJZR) {
        this.FD7_HZJZR = FD7_HZJZR;
    }

    @JsonProperty("FD1_PGZ")
    public String getFD1_PGZ() {
        return FD1_PGZ;
    }

    @JsonProperty("FD1_PGZ")
    public void setFD1_PGZ(String FD1_PGZ) {
        this.FD1_PGZ = FD1_PGZ;
    }

    @JsonProperty("FD1_JZZJ")
    public String getFD1_JZZJ() {
        return FD1_JZZJ;
    }

    @JsonProperty("FD1_JZZJ")
    public void setFD1_JZZJ(String FD1_JZZJ) {
        this.FD1_JZZJ = FD1_JZZJ;
    }

    @JsonProperty("JB_BLQSHZT")
    public String getJB_BLQSHZT() {
        return JB_BLQSHZT;
    }

    @JsonProperty("JB_BLQSHZT")
    public void setJB_BLQSHZT(String JB_BLQSHZT) {
        this.JB_BLQSHZT = JB_BLQSHZT;
    }

    @JsonProperty("FD2_SGJGHJ")
    public String getFD2_SGJGHJ() {
        return FD2_SGJGHJ;
    }

    @JsonProperty("FD2_SGJGHJ")
    public void setFD2_SGJGHJ(String FD2_SGJGHJ) {
        this.FD2_SGJGHJ = FD2_SGJGHJ;
    }

    @JsonProperty("JB_JYZK")
    public String getJB_JYZK() {
        return JB_JYZK;
    }

    @JsonProperty("JB_JYZK")
    public void setJB_JYZK(String JB_JYZK) {
        this.JB_JYZK = JB_JYZK;
    }

    @JsonProperty("XX_ZHEYYZHDZCPGZ")
    public String getXX_ZHEYYZHDZCPGZ() {
        return XX_ZHEYYZHDZCPGZ;
    }

    @JsonProperty("XX_ZHEYYZHDZCPGZ")
    public void setXX_ZHEYYZHDZCPGZ(String XX_ZHEYYZHDZCPGZ) {
        this.XX_ZHEYYZHDZCPGZ = XX_ZHEYYZHDZCPGZ;
    }

    @JsonProperty("RG_UNITSTATE")
    public String getRG_UNITSTATE() {
        return RG_UNITSTATE;
    }

    @JsonProperty("RG_UNITSTATE")
    public void setRG_UNITSTATE(String RG_UNITSTATE) {
        this.RG_UNITSTATE = RG_UNITSTATE;
    }

    @JsonProperty("FD7_HRFMC")
    public String getFD7_HRFMC() {
        return FD7_HRFMC;
    }

    @JsonProperty("FD7_HRFMC")
    public void setFD7_HRFMC(String FD7_HRFMC) {
        this.FD7_HRFMC = FD7_HRFMC;
    }

    @JsonProperty("ZL_PGBA_HZWJH")
    public String getZL_PGBA_HZWJH() {
        return ZL_PGBA_HZWJH;
    }

    @JsonProperty("ZL_PGBA_HZWJH")
    public void setZL_PGBA_HZWJH(String ZL_PGBA_HZWJH) {
        this.ZL_PGBA_HZWJH = ZL_PGBA_HZWJH;
    }

    @JsonProperty("ZL_XBPGBAB_YW")
    public String getZL_XBPGBAB_YW() {
        return ZL_XBPGBAB_YW;
    }

    @JsonProperty("ZL_XBPGBAB_YW")
    public void setZL_XBPGBAB_YW(String ZL_XBPGBAB_YW) {
        this.ZL_XBPGBAB_YW = ZL_XBPGBAB_YW;
    }

    @JsonProperty("ZL_TZXY_YW")
    public String getZL_TZXY_YW() {
        return ZL_TZXY_YW;
    }

    @JsonProperty("ZL_TZXY_YW")
    public void setZL_TZXY_YW(String ZL_TZXY_YW) {
        this.ZL_TZXY_YW = ZL_TZXY_YW;
    }

    @JsonProperty("RG_TYPE")
    public String getRG_TYPE() {
        return RG_TYPE;
    }

    @JsonProperty("RG_TYPE")
    public void setRG_TYPE(String RG_TYPE) {
        this.RG_TYPE = RG_TYPE;
    }

    @JsonProperty("FD4_ZGSLHJ")
    public String getFD4_ZGSLHJ() {
        return FD4_ZGSLHJ;
    }

    @JsonProperty("FD4_ZGSLHJ")
    public void setFD4_ZGSLHJ(String FD4_ZGSLHJ) {
        this.FD4_ZGSLHJ = FD4_ZGSLHJ;
    }

    @JsonProperty("XX_CXQYZGBZ")
    public String getXX_CXQYZGBZ() {
        return XX_CXQYZGBZ;
    }

    @JsonProperty("XX_CXQYZGBZ")
    public void setXX_CXQYZGBZ(String XX_CXQYZGBZ) {
        this.XX_CXQYZGBZ = XX_CXQYZGBZ;
    }

    @JsonProperty("JB_GYFRSBS")
    public String getJB_GYFRSBS() {
        return JB_GYFRSBS;
    }

    @JsonProperty("JB_GYFRSBS")
    public void setJB_GYFRSBS(String JB_GYFRSBS) {
        this.JB_GYFRSBS = JB_GYFRSBS;
    }

    @JsonProperty("ZL_FLXYS_LY")
    public String getZL_FLXYS_LY() {
        return ZL_FLXYS_LY;
    }

    @JsonProperty("ZL_FLXYS_LY")
    public void setZL_FLXYS_LY(String ZL_FLXYS_LY) {
        this.ZL_FLXYS_LY = ZL_FLXYS_LY;
    }

    @JsonProperty("XX_ZGBZ_JS")
    public String getXX_ZGBZ_JS() {
        return XX_ZGBZ_JS;
    }

    @JsonProperty("XX_ZGBZ_JS")
    public void setXX_ZGBZ_JS(String XX_ZGBZ_JS) {
        this.XX_ZGBZ_JS = XX_ZGBZ_JS;
    }

    @JsonProperty("JB_GSBLZK")
    public String getJB_GSBLZK() {
        return JB_GSBLZK;
    }

    @JsonProperty("JB_GSBLZK")
    public void setJB_GSBLZK(String JB_GSBLZK) {
        this.JB_GSBLZK = JB_GSBLZK;
    }

    @JsonProperty("XX_QSCCSFZYQCZW")
    public String getXX_QSCCSFZYQCZW() {
        return XX_QSCCSFZYQCZW;
    }

    @JsonProperty("XX_QSCCSFZYQCZW")
    public void setXX_QSCCSFZYQCZW(String XX_QSCCSFZYQCZW) {
        this.XX_QSCCSFZYQCZW = XX_QSCCSFZYQCZW;
    }

    @JsonProperty("ZL_BDPGBA_ZJJGMC")
    public String getZL_BDPGBA_ZJJGMC() {
        return ZL_BDPGBA_ZJJGMC;
    }

    @JsonProperty("ZL_BDPGBA_ZJJGMC")
    public void setZL_BDPGBA_ZJJGMC(String ZL_BDPGBA_ZJJGMC) {
        this.ZL_BDPGBA_ZJJGMC = ZL_BDPGBA_ZJJGMC;
    }

    @JsonProperty("FD_SJZCJ")
    public String getFD_SJZCJ() {
        return FD_SJZCJ;
    }

    @JsonProperty("FD_SJZCJ")
    public void setFD_SJZCJ(String FD_SJZCJ) {
        this.FD_SJZCJ = FD_SJZCJ;
    }

    @JsonProperty("FD2_SJJZCZHJ")
    public String getFD2_SJJZCZHJ() {
        return FD2_SJJZCZHJ;
    }

    @JsonProperty("FD2_SJJZCZHJ")
    public void setFD2_SJJZCZHJ(String FD2_SJJZCZHJ) {
        this.FD2_SJJZCZHJ = FD2_SJJZCZHJ;
    }

    @JsonProperty("ZL_TJPGBAB_HZWJH")
    public String getZL_TJPGBAB_HZWJH() {
        return ZL_TJPGBAB_HZWJH;
    }

    @JsonProperty("ZL_TJPGBAB_HZWJH")
    public void setZL_TJPGBAB_HZWJH(String ZL_TJPGBAB_HZWJH) {
        this.ZL_TJPGBAB_HZWJH = ZL_TJPGBAB_HZWJH;
    }

    @JsonProperty("JB_GSDJXGZL")
    public String getJB_GSDJXGZL() {
        return JB_GSDJXGZL;
    }

    @JsonProperty("JB_GSDJXGZL")
    public void setJB_GSDJXGZL(String JB_GSDJXGZL) {
        this.JB_GSDJXGZL = JB_GSDJXGZL;
    }

    @JsonProperty("ZL_JCWJ_LY")
    public String getZL_JCWJ_LY() {
        return ZL_JCWJ_LY;
    }

    @JsonProperty("ZL_JCWJ_LY")
    public void setZL_JCWJ_LY(String ZL_JCWJ_LY) {
        this.ZL_JCWJ_LY = ZL_JCWJ_LY;
    }

    @JsonProperty("ZL_YWBLSQWJ")
    public String getZL_YWBLSQWJ() {
        return ZL_YWBLSQWJ;
    }

    @JsonProperty("ZL_YWBLSQWJ")
    public void setZL_YWBLSQWJ(String ZL_YWBLSQWJ) {
        this.ZL_YWBLSQWJ = ZL_YWBLSQWJ;
    }

    @JsonProperty("ZL_BDPGBA_LY")
    public String getZL_BDPGBA_LY() {
        return ZL_BDPGBA_LY;
    }

    @JsonProperty("ZL_BDPGBA_LY")
    public void setZL_BDPGBA_LY(String ZL_BDPGBA_LY) {
        this.ZL_BDPGBA_LY = ZL_BDPGBA_LY;
    }

    @JsonProperty("FD2_SJJZCZ")
    public String getFD2_SJJZCZ() {
        return FD2_SJJZCZ;
    }

    @JsonProperty("FD2_SJJZCZ")
    public void setFD2_SJJZCZ(String FD2_SJJZCZ) {
        this.FD2_SJJZCZ = FD2_SJJZCZ;
    }

    @JsonProperty("FD8_CJJHJ")
    public String getFD8_CJJHJ() {
        return FD8_CJJHJ;
    }

    @JsonProperty("FD8_CJJHJ")
    public void setFD8_CJJHJ(String FD8_CJJHJ) {
        this.FD8_CJJHJ = FD8_CJJHJ;
    }

    @JsonProperty("FD2_SGFS")
    public String getFD2_SGFS() {
        return FD2_SGFS;
    }

    @JsonProperty("FD2_SGFS")
    public void setFD2_SGFS(String FD2_SGFS) {
        this.FD2_SGFS = FD2_SGFS;
    }

    @JsonProperty("FD8_CJJ")
    public String getFD8_CJJ() {
        return FD8_CJJ;
    }

    @JsonProperty("FD8_CJJ")
    public void setFD8_CJJ(String FD8_CJJ) {
        this.FD8_CJJ = FD8_CJJ;
    }

    @JsonProperty("XX_PCFPSQSK")
    public String getXX_PCFPSQSK() {
        return XX_PCFPSQSK;
    }

    @JsonProperty("XX_PCFPSQSK")
    public void setXX_PCFPSQSK(String XX_PCFPSQSK) {
        this.XX_PCFPSQSK = XX_PCFPSQSK;
    }

    @JsonProperty("XX_GYQY")
    public String getXX_GYQY() {
        return XX_GYQY;
    }

    @JsonProperty("XX_GYQY")
    public void setXX_GYQY(String XX_GYQY) {
        this.XX_GYQY = XX_GYQY;
    }

    @JsonProperty("XX_SFZJ")
    public String getXX_SFZJ() {
        return XX_SFZJ;
    }

    @JsonProperty("XX_SFZJ")
    public void setXX_SFZJ(String XX_SFZJ) {
        this.XX_SFZJ = XX_SFZJ;
    }

    @JsonProperty("JB_BYZLY")
    public String getJB_BYZLY() {
        return JB_BYZLY;
    }

    @JsonProperty("JB_BYZLY")
    public void setJB_BYZLY(String JB_BYZLY) {
        this.JB_BYZLY = JB_BYZLY;
    }

    @JsonProperty("ZL_BXBPGBAB_YW")
    public String getZL_BXBPGBAB_YW() {
        return ZL_BXBPGBAB_YW;
    }

    @JsonProperty("ZL_BXBPGBAB_YW")
    public void setZL_BXBPGBAB_YW(String ZL_BXBPGBAB_YW) {
        this.ZL_BXBPGBAB_YW = ZL_BXBPGBAB_YW;
    }

    @JsonProperty("ZL_YYZZ_LY")
    public String getZL_YYZZ_LY() {
        return ZL_YYZZ_LY;
    }

    @JsonProperty("ZL_YYZZ_LY")
    public void setZL_YYZZ_LY(String ZL_YYZZ_LY) {
        this.ZL_YYZZ_LY = ZL_YYZZ_LY;
    }

    @JsonProperty("ZL_ZJYQSJBG_LY")
    public String getZL_ZJYQSJBG_LY() {
        return ZL_ZJYQSJBG_LY;
    }

    @JsonProperty("ZL_ZJYQSJBG_LY")
    public void setZL_ZJYQSJBG_LY(String ZL_ZJYQSJBG_LY) {
        this.ZL_ZJYQSJBG_LY = ZL_ZJYQSJBG_LY;
    }

    @JsonProperty("XX_FXGS")
    public String getXX_FXGS() {
        return XX_FXGS;
    }

    @JsonProperty("XX_FXGS")
    public void setXX_FXGS(String XX_FXGS) {
        this.XX_FXGS = XX_FXGS;
    }

    @JsonProperty("FD_CZRMC")
    public String getFD_CZRMC() {
        return FD_CZRMC;
    }

    @JsonProperty("FD_CZRMC")
    public void setFD_CZRMC(String FD_CZRMC) {
        this.FD_CZRMC = FD_CZRMC;
    }

    @JsonProperty("FD_RJZB")
    public String getFD_RJZB() {
        return FD_RJZB;
    }

    @JsonProperty("FD_RJZB")
    public void setFD_RJZB(String FD_RJZB) {
        this.FD_RJZB = FD_RJZB;
    }

    @JsonProperty("XX_BFLQYPGJZCZ")
    public String getXX_BFLQYPGJZCZ() {
        return XX_BFLQYPGJZCZ;
    }

    @JsonProperty("XX_BFLQYPGJZCZ")
    public void setXX_BFLQYPGJZCZ(String XX_BFLQYPGJZCZ) {
        this.XX_BFLQYPGJZCZ = XX_BFLQYPGJZCZ;
    }

    @JsonProperty("FD8_SRGQSJJZCZHJ")
    public String getFD8_SRGQSJJZCZHJ() {
        return FD8_SRGQSJJZCZHJ;
    }

    @JsonProperty("FD8_SRGQSJJZCZHJ")
    public void setFD8_SRGQSJJZCZHJ(String FD8_SRGQSJJZCZHJ) {
        this.FD8_SRGQSJJZCZHJ = FD8_SRGQSJJZCZHJ;
    }

    @JsonProperty("XX_AZRYZS")
    public String getXX_AZRYZS() {
        return XX_AZRYZS;
    }

    @JsonProperty("XX_AZRYZS")
    public void setXX_AZRYZS(String XX_AZRYZS) {
        this.XX_AZRYZS = XX_AZRYZS;
    }

    @JsonProperty("FD8_SRGQPGJZCZHJ")
    public String getFD8_SRGQPGJZCZHJ() {
        return FD8_SRGQPGJZCZHJ;
    }

    @JsonProperty("FD8_SRGQPGJZCZHJ")
    public void setFD8_SRGQPGJZCZHJ(String FD8_SRGQPGJZCZHJ) {
        this.FD8_SRGQPGJZCZHJ = FD8_SRGQPGJZCZHJ;
    }

    @JsonProperty("FD2_BZ")
    public String getFD2_BZ() {
        return FD2_BZ;
    }

    @JsonProperty("FD2_BZ")
    public void setFD2_BZ(String FD2_BZ) {
        this.FD2_BZ = FD2_BZ;
    }

    @JsonProperty("XX_PCFYHGYZW")
    public String getXX_PCFYHGYZW() {
        return XX_PCFYHGYZW;
    }

    @JsonProperty("XX_PCFYHGYZW")
    public void setXX_PCFYHGYZW(String XX_PCFYHGYZW) {
        this.XX_PCFYHGYZW = XX_PCFYHGYZW;
    }

    @JsonProperty("AF_CURRENTUNITID")
    public String getAF_CURRENTUNITID() {
        return AF_CURRENTUNITID;
    }

    @JsonProperty("AF_CURRENTUNITID")
    public void setAF_CURRENTUNITID(String AF_CURRENTUNITID) {
        this.AF_CURRENTUNITID = AF_CURRENTUNITID;
    }

    @JsonProperty("XX_ZGBZ_TZXZ")
    public String getXX_ZGBZ_TZXZ() {
        return XX_ZGBZ_TZXZ;
    }

    @JsonProperty("XX_ZGBZ_TZXZ")
    public void setXX_ZGBZ_TZXZ(String XX_ZGBZ_TZXZ) {
        this.XX_ZGBZ_TZXZ = XX_ZGBZ_TZXZ;
    }

    @JsonProperty("XX_BDQYPGJZCZ_BZZ")
    public String getXX_BDQYPGJZCZ_BZZ() {
        return XX_BDQYPGJZCZ_BZZ;
    }

    @JsonProperty("XX_BDQYPGJZCZ_BZZ")
    public void setXX_BDQYPGJZCZ_BZZ(String XX_BDQYPGJZCZ_BZZ) {
        this.XX_BDQYPGJZCZ_BZZ = XX_BDQYPGJZCZ_BZZ;
    }

    @JsonProperty("FD8_SRFXZ")
    public String getFD8_SRFXZ() {
        return FD8_SRFXZ;
    }

    @JsonProperty("FD8_SRFXZ")
    public void setFD8_SRFXZ(String FD8_SRFXZ) {
        this.FD8_SRFXZ = FD8_SRFXZ;
    }

    @JsonProperty("ZL_ZXGG_MTMC")
    public String getZL_ZXGG_MTMC() {
        return ZL_ZXGG_MTMC;
    }

    @JsonProperty("ZL_ZXGG_MTMC")
    public void setZL_ZXGG_MTMC(String ZL_ZXGG_MTMC) {
        this.ZL_ZXGG_MTMC = ZL_ZXGG_MTMC;
    }

    @JsonProperty("ZL_JZGG_MTMC")
    public String getZL_JZGG_MTMC() {
        return ZL_JZGG_MTMC;
    }

    @JsonProperty("ZL_JZGG_MTMC")
    public void setZL_JZGG_MTMC(String ZL_JZGG_MTMC) {
        this.ZL_JZGG_MTMC = ZL_JZGG_MTMC;
    }

    @JsonProperty("XX_ZGBZ_GQCZ")
    public String getXX_ZGBZ_GQCZ() {
        return XX_ZGBZ_GQCZ;
    }

    @JsonProperty("XX_ZGBZ_GQCZ")
    public void setXX_ZGBZ_GQCZ(String XX_ZGBZ_GQCZ) {
        this.XX_ZGBZ_GQCZ = XX_ZGBZ_GQCZ;
    }

    @JsonProperty("XX_YYTZDGQPGZ")
    public String getXX_YYTZDGQPGZ() {
        return XX_YYTZDGQPGZ;
    }

    @JsonProperty("XX_YYTZDGQPGZ")
    public void setXX_YYTZDGQPGZ(String XX_YYTZDGQPGZ) {
        this.XX_YYTZDGQPGZ = XX_YYTZDGQPGZ;
    }

    @JsonProperty("JB_HJRJZB")
    public String getJB_HJRJZB() {
        return JB_HJRJZB;
    }

    @JsonProperty("JB_HJRJZB")
    public void setJB_HJRJZB(String JB_HJRJZB) {
        this.JB_HJRJZB = JB_HJRJZB;
    }

    @JsonProperty("XX_QSFY")
    public String getXX_QSFY() {
        return XX_QSFY;
    }

    @JsonProperty("XX_QSFY")
    public void setXX_QSFY(String XX_QSFY) {
        this.XX_QSFY = XX_QSFY;
    }

    @JsonProperty("FD1_CZF")
    public String getFD1_CZF() {
        return FD1_CZF;
    }

    @JsonProperty("FD1_CZF")
    public void setFD1_CZF(String FD1_CZF) {
        this.FD1_CZF = FD1_CZF;
    }

    @JsonProperty("JB_GSDJRQ")
    public String getJB_GSDJRQ() {
        return JB_GSDJRQ;
    }

    @JsonProperty("JB_GSDJRQ")
    public void setJB_GSDJRQ(String JB_GSDJRQ) {
        this.JB_GSDJRQ = JB_GSDJRQ;
    }

    @JsonProperty("JB_HJRJZBBZ")
    public String getJB_HJRJZBBZ() {
        return JB_HJRJZBBZ;
    }

    @JsonProperty("JB_HJRJZBBZ")
    public void setJB_HJRJZBBZ(String JB_HJRJZBBZ) {
        this.JB_HJRJZBBZ = JB_HJRJZBBZ;
    }

    @JsonProperty("XX_QSFPBC")
    public String getXX_QSFPBC() {
        return XX_QSFPBC;
    }

    @JsonProperty("XX_QSFPBC")
    public void setXX_QSFPBC(String XX_QSFPBC) {
        this.XX_QSFPBC = XX_QSFPBC;
    }

    @JsonProperty("XX_BDQYPGJZCZ_BGZ")
    public String getXX_BDQYPGJZCZ_BGZ() {
        return XX_BDQYPGJZCZ_BGZ;
    }

    @JsonProperty("XX_BDQYPGJZCZ_BGZ")
    public void setXX_BDQYPGJZCZ_BGZ(String XX_BDQYPGJZCZ_BGZ) {
        this.XX_BDQYPGJZCZ_BGZ = XX_BDQYPGJZCZ_BGZ;
    }

    @JsonProperty("ZL_GSZXZM_LY")
    public String getZL_GSZXZM_LY() {
        return ZL_GSZXZM_LY;
    }

    @JsonProperty("ZL_GSZXZM_LY")
    public void setZL_GSZXZM_LY(String ZL_GSZXZM_LY) {
        this.ZL_GSZXZM_LY = ZL_GSZXZM_LY;
    }

    @JsonProperty("JB_RELA")
    public String getJB_RELA() {
        return JB_RELA;
    }

    @JsonProperty("JB_RELA")
    public void setJB_RELA(String JB_RELA) {
        this.JB_RELA = JB_RELA;
    }

    @JsonProperty("JB_ZCRQ")
    public String getJB_ZCRQ() {
        return JB_ZCRQ;
    }

    @JsonProperty("JB_ZCRQ")
    public void setJB_ZCRQ(String JB_ZCRQ) {
        this.JB_ZCRQ = JB_ZCRQ;
    }

    @JsonProperty("ZL_JCWJ_WJH")
    public String getZL_JCWJ_WJH() {
        return ZL_JCWJ_WJH;
    }

    @JsonProperty("ZL_JCWJ_WJH")
    public void setZL_JCWJ_WJH(String ZL_JCWJ_WJH) {
        this.ZL_JCWJ_WJH = ZL_JCWJ_WJH;
    }

    @JsonProperty("FD7_WCHZLB")
    public String getFD7_WCHZLB() {
        return FD7_WCHZLB;
    }

    @JsonProperty("FD7_WCHZLB")
    public void setFD7_WCHZLB(String FD7_WCHZLB) {
        this.FD7_WCHZLB = FD7_WCHZLB;
    }

    @JsonProperty("JB_HGQY")
    public String getJB_HGQY() {
        return JB_HGQY;
    }

    @JsonProperty("JB_HGQY")
    public void setJB_HGQY(String JB_HGQY) {
        this.JB_HGQY = JB_HGQY;
    }

    @JsonProperty("ZL_GYTDBA_YW")
    public String getZL_GYTDBA_YW() {
        return ZL_GYTDBA_YW;
    }

    @JsonProperty("ZL_GYTDBA_YW")
    public void setZL_GYTDBA_YW(String ZL_GYTDBA_YW) {
        this.ZL_GYTDBA_YW = ZL_GYTDBA_YW;
    }

    @JsonProperty("JB_ZCZBBZXZ")
    public String getJB_ZCZBBZXZ() {
        return JB_ZCZBBZXZ;
    }

    @JsonProperty("JB_ZCZBBZXZ")
    public void setJB_ZCZBBZXZ(String JB_ZCZBBZXZ) {
        this.JB_ZCZBBZXZ = JB_ZCZBBZXZ;
    }

    @JsonProperty("JB_QYJC")
    public String getJB_QYJC() {
        return JB_QYJC;
    }

    @JsonProperty("JB_QYJC")
    public void setJB_QYJC(String JB_QYJC) {
        this.JB_QYJC = JB_QYJC;
    }

    @JsonProperty("FD7_BZ")
    public String getFD7_BZ() {
        return FD7_BZ;
    }

    @JsonProperty("FD7_BZ")
    public void setFD7_BZ(String FD7_BZ) {
        this.FD7_BZ = FD7_BZ;
    }

    @JsonProperty("ZL_YXHZTZS_YW")
    public String getZL_YXHZTZS_YW() {
        return ZL_YXHZTZS_YW;
    }

    @JsonProperty("ZL_YXHZTZS_YW")
    public void setZL_YXHZTZS_YW(String ZL_YXHZTZS_YW) {
        this.ZL_YXHZTZS_YW = ZL_YXHZTZS_YW;
    }

    @JsonProperty("FD8_ZRFSSGZJGJG")
    public String getFD8_ZRFSSGZJGJG() {
        return FD8_ZRFSSGZJGJG;
    }

    @JsonProperty("FD8_ZRFSSGZJGJG")
    public void setFD8_ZRFSSGZJGJG(String FD8_ZRFSSGZJGJG) {
        this.FD8_ZRFSSGZJGJG = FD8_ZRFSSGZJGJG;
    }

    @JsonProperty("ZL_JCJG_YW")
    public String getZL_JCJG_YW() {
        return ZL_JCJG_YW;
    }

    @JsonProperty("ZL_JCJG_YW")
    public void setZL_JCJG_YW(String ZL_JCJG_YW) {
        this.ZL_JCJG_YW = ZL_JCJG_YW;
    }

    @JsonProperty("JB_SSGZJGJG")
    public String getJB_SSGZJGJG() {
        return JB_SSGZJGJG;
    }

    @JsonProperty("JB_SSGZJGJG")
    public void setJB_SSGZJGJG(String JB_SSGZJGJG) {
        this.JB_SSGZJGJG = JB_SSGZJGJG;
    }

    @JsonProperty("ZL_PCGG_GGRQ")
    public String getZL_PCGG_GGRQ() {
        return ZL_PCGG_GGRQ;
    }

    @JsonProperty("ZL_PCGG_GGRQ")
    public void setZL_PCGG_GGRQ(String ZL_PCGG_GGRQ) {
        this.ZL_PCGG_GGRQ = ZL_PCGG_GGRQ;
    }

    @JsonProperty("ZL_QYZC_LY")
    public String getZL_QYZC_LY() {
        return ZL_QYZC_LY;
    }

    @JsonProperty("ZL_QYZC_LY")
    public void setZL_QYZC_LY(String ZL_QYZC_LY) {
        this.ZL_QYZC_LY = ZL_QYZC_LY;
    }

    @JsonProperty("JB_ZCZB")
    public String getJB_ZCZB() {
        return JB_ZCZB;
    }

    @JsonProperty("JB_ZCZB")
    public void setJB_ZCZB(String JB_ZCZB) {
        this.JB_ZCZB = JB_ZCZB;
    }

    @JsonProperty("XX_FXJG")
    public String getXX_FXJG() {
        return XX_FXJG;
    }

    @JsonProperty("XX_FXJG")
    public void setXX_FXJG(String XX_FXJG) {
        this.XX_FXJG = XX_FXJG;
    }

    @JsonProperty("ZL_ZJYQSJBG_ZJJG")
    public String getZL_ZJYQSJBG_ZJJG() {
        return ZL_ZJYQSJBG_ZJJG;
    }

    @JsonProperty("ZL_ZJYQSJBG_ZJJG")
    public void setZL_ZJYQSJBG_ZJJG(String ZL_ZJYQSJBG_ZJJG) {
        this.ZL_ZJYQSJBG_ZJJG = ZL_ZJYQSJBG_ZJJG;
    }

    @JsonProperty("XX_SYJZCCZSR")
    public String getXX_SYJZCCZSR() {
        return XX_SYJZCCZSR;
    }

    @JsonProperty("XX_SYJZCCZSR")
    public void setXX_SYJZCCZSR(String XX_SYJZCCZSR) {
        this.XX_SYJZCCZSR = XX_SYJZCCZSR;
    }

    @JsonProperty("ZL_GSZXZM_ZXRQ")
    public String getZL_GSZXZM_ZXRQ() {
        return ZL_GSZXZM_ZXRQ;
    }

    @JsonProperty("ZL_GSZXZM_ZXRQ")
    public void setZL_GSZXZM_ZXRQ(String ZL_GSZXZM_ZXRQ) {
        this.ZL_GSZXZM_ZXRQ = ZL_GSZXZM_ZXRQ;
    }

    @JsonProperty("ZL_JCWJ_YW")
    public String getZL_JCWJ_YW() {
        return ZL_JCWJ_YW;
    }

    @JsonProperty("ZL_JCWJ_YW")
    public void setZL_JCWJ_YW(String ZL_JCWJ_YW) {
        this.ZL_JCWJ_YW = ZL_JCWJ_YW;
    }

    @JsonProperty("ZL_PCGG_YW")
    public String getZL_PCGG_YW() {
        return ZL_PCGG_YW;
    }

    @JsonProperty("ZL_PCGG_YW")
    public void setZL_PCGG_YW(String ZL_PCGG_YW) {
        this.ZL_PCGG_YW = ZL_PCGG_YW;
    }

    @JsonProperty("FD2_YGQCYFXZ")
    public String getFD2_YGQCYFXZ() {
        return FD2_YGQCYFXZ;
    }

    @JsonProperty("FD2_YGQCYFXZ")
    public void setFD2_YGQCYFXZ(String FD2_YGQCYFXZ) {
        this.FD2_YGQCYFXZ = FD2_YGQCYFXZ;
    }

    @JsonProperty("ZL_BDPGBA_HZDWMC")
    public String getZL_BDPGBA_HZDWMC() {
        return ZL_BDPGBA_HZDWMC;
    }

    @JsonProperty("ZL_BDPGBA_HZDWMC")
    public void setZL_BDPGBA_HZDWMC(String ZL_BDPGBA_HZDWMC) {
        this.ZL_BDPGBA_HZDWMC = ZL_BDPGBA_HZDWMC;
    }

    @JsonProperty("JB_QTQYSBS")
    public String getJB_QTQYSBS() {
        return JB_QTQYSBS;
    }

    @JsonProperty("JB_QTQYSBS")
    public void setJB_QTQYSBS(String JB_QTQYSBS) {
        this.JB_QTQYSBS = JB_QTQYSBS;
    }

    @JsonProperty("ZL_XBPGBAB_LY")
    public String getZL_XBPGBAB_LY() {
        return ZL_XBPGBAB_LY;
    }

    @JsonProperty("ZL_XBPGBAB_LY")
    public void setZL_XBPGBAB_LY(String ZL_XBPGBAB_LY) {
        this.ZL_XBPGBAB_LY = ZL_XBPGBAB_LY;
    }

    @JsonProperty("HH_COMPANY_NAME")
    public String getHH_COMPANY_NAME() {
        return HH_COMPANY_NAME;
    }

    @JsonProperty("HH_COMPANY_NAME")
    public void setHH_COMPANY_NAME(String HH_COMPANY_NAME) {
        this.HH_COMPANY_NAME = HH_COMPANY_NAME;
    }

    @JsonProperty("HH_CREDIT_CODE")
    public String getHH_CREDIT_CODE() {
        return HH_CREDIT_CODE;
    }

    @JsonProperty("HH_CREDIT_CODE")
    public void setHH_CREDIT_CODE(String HH_CREDIT_CODE) {
        this.HH_CREDIT_CODE = HH_CREDIT_CODE;
    }

    @JsonProperty("HH_ZXSWHHR")
    public String getHH_ZXSWHHR() {
        return HH_ZXSWHHR;
    }

    @JsonProperty("HH_ZXSWHHR")
    public void setHH_ZXSWHHR(String HH_ZXSWHHR) {
        this.HH_ZXSWHHR = HH_ZXSWHHR;
    }

    @JsonProperty("HH_ZXSWHHR_CODE")
    public String getHH_ZXSWHHR_CODE() {
        return HH_ZXSWHHR_CODE;
    }

    @JsonProperty("HH_ZXSWHHR_CODE")
    public void setHH_ZXSWHHR_CODE(String HH_ZXSWHHR_CODE) {
        this.HH_ZXSWHHR_CODE = HH_ZXSWHHR_CODE;
    }

    @JsonProperty("SETUP_DATE")
    public String getSETUP_DATE() {
        return SETUP_DATE;
    }

    @JsonProperty("SETUP_DATE")
    public void setSETUP_DATE(String SETUP_DATE) {
        this.SETUP_DATE = SETUP_DATE;
    }

    @JsonProperty("HH_QX")
    public String getHH_QX() {
        return HH_QX;
    }

    @JsonProperty("HH_QX")
    public void setHH_QX(String HH_QX) {
        this.HH_QX = HH_QX;
    }

    @JsonProperty("HH_ZYJYCS")
    public String getHH_ZYJYCS() {
        return HH_ZYJYCS;
    }

    @JsonProperty("HH_ZYJYCS")
    public void setHH_ZYJYCS(String HH_ZYJYCS) {
        this.HH_ZYJYCS = HH_ZYJYCS;
    }

    @JsonProperty("HH_SFSMTZJJ")
    public String getHH_SFSMTZJJ() {
        return HH_SFSMTZJJ;
    }

    @JsonProperty("HH_SFSMTZJJ")
    public void setHH_SFSMTZJJ(String HH_SFSMTZJJ) {
        this.HH_SFSMTZJJ = HH_SFSMTZJJ;
    }

    @JsonProperty("HH_JYFW")
    public String getHH_JYFW() {
        return HH_JYFW;
    }

    @JsonProperty("HH_JYFW")
    public void setHH_JYFW(String HH_JYFW) {
        this.HH_JYFW = HH_JYFW;
    }

    @JsonProperty("HH_RJCZE")
    public String getHH_RJCZE() {
        return HH_RJCZE;
    }

    @JsonProperty("HH_RJCZE")
    public void setHH_RJCZE(String HH_RJCZE) {
        this.HH_RJCZE = HH_RJCZE;
    }

    @JsonProperty("HH_RJCZEBZ")
    public String getHH_RJCZEBZ() {
        return HH_RJCZEBZ;
    }

    @JsonProperty("HH_RJCZEBZ")
    public void setHH_RJCZEBZ(String HH_RJCZEBZ) {
        this.HH_RJCZEBZ = HH_RJCZEBZ;
    }

    @JsonProperty("HH_SJCZE")
    public String getHH_SJCZE() {
        return HH_SJCZE;
    }

    @JsonProperty("HH_SJCZE")
    public void setHH_SJCZE(String HH_SJCZE) {
        this.HH_SJCZE = HH_SJCZE;
    }

    @JsonProperty("HH_SJCZEBZ")
    public String getHH_SJCZEBZ() {
        return HH_SJCZEBZ;
    }

    @JsonProperty("HH_SJCZEBZ")
    public void setHH_SJCZEBZ(String HH_SJCZEBZ) {
        this.HH_SJCZEBZ = HH_SJCZEBZ;
    }

    @JsonProperty("HH_GJCZQY")
    public String getHH_GJCZQY() {
        return HH_GJCZQY;
    }

    @JsonProperty("HH_GJCZQY")
    public void setHH_GJCZQY(String HH_GJCZQY) {
        this.HH_GJCZQY = HH_GJCZQY;
    }

    @JsonProperty("HH_GJCZQY_CODE")
    public String getHH_GJCZQY_CODE() {
        return HH_GJCZQY_CODE;
    }

    @JsonProperty("HH_GJCZQY_CODE")
    public void setHH_GJCZQY_CODE(String HH_GJCZQY_CODE) {
        this.HH_GJCZQY_CODE = HH_GJCZQY_CODE;
    }

    @JsonProperty("HH_CZQYID")
    public String getHH_CZQYID() {
        return HH_CZQYID;
    }

    @JsonProperty("HH_CZQYID")
    public void setHH_CZQYID(String HH_CZQYID) {
        this.HH_CZQYID = HH_CZQYID;
    }

    @JsonProperty("HH_CZQY")
    public String getHH_CZQY() {
        return HH_CZQY;
    }

    @JsonProperty("HH_CZQY")
    public void setHH_CZQY(String HH_CZQY) {
        this.HH_CZQY = HH_CZQY;
    }

    @JsonProperty("HH_CZQY_CODE")
    public String getHH_CZQY_CODE() {
        return HH_CZQY_CODE;
    }

    @JsonProperty("HH_CZQY_CODE")
    public void setHH_CZQY_CODE(String HH_CZQY_CODE) {
        this.HH_CZQY_CODE = HH_CZQY_CODE;
    }

    @JsonProperty("HH_HHXY")
    public String getHH_HHXY() {
        return HH_HHXY;
    }

    @JsonProperty("HH_HHXY")
    public void setHH_HHXY(String HH_HHXY) {
        this.HH_HHXY = HH_HHXY;
    }

    @JsonProperty("HJRJCZE")
    public String getHJRJCZE() {
        return HJRJCZE;
    }

    @JsonProperty("HJRJCZE")
    public void setHJRJCZE(String HJRJCZE) {
        this.HJRJCZE = HJRJCZE;
    }

    @JsonProperty("HJRJCZBL")
    public String getHJRJCZBL() {
        return HJRJCZBL;
    }

    @JsonProperty("HJRJCZBL")
    public void setHJRJCZBL(String HJRJCZBL) {
        this.HJRJCZBL = HJRJCZBL;
    }

    @JsonProperty("HJSJCZE")
    public String getHJSJCZE() {
        return HJSJCZE;
    }

    @JsonProperty("HJSJCZE")
    public void setHJSJCZE(String HJSJCZE) {
        this.HJSJCZE = HJSJCZE;
    }

    @JsonProperty("BDLX")
    public String getBDLX() {
        return BDLX;
    }

    @JsonProperty("BDLX")
    public void setBDLX(String BDLX) {
        this.BDLX = BDLX;
    }

    @JsonProperty("BDMC")
    public String getBDMC() {
        return BDMC;
    }

    @JsonProperty("BDMC")
    public void setBDMC(String BDMC) {
        this.BDMC = BDMC;
    }

    @JsonProperty("CODE")
    public String getCODE() {
        return CODE;
    }

    @JsonProperty("CODE")
    public void setCODE(String CODE) {
        this.CODE = CODE;
    }

    @JsonProperty("SSHY")
    public String getSSHY() {
        return SSHY;
    }

    @JsonProperty("SSHY")
    public void setSSHY(String SSHY) {
        this.SSHY = SSHY;
    }

    @JsonProperty("ADDRESS")
    public String getADDRESS() {
        return ADDRESS;
    }

    @JsonProperty("ADDRESS")
    public void setADDRESS(String ADDRESS) {
        this.ADDRESS = ADDRESS;
    }

    @JsonProperty("TZE")
    public String getTZE() {
        return TZE;
    }

    @JsonProperty("TZE")
    public void setTZE(String TZE) {
        this.TZE = TZE;
    }

    @JsonProperty("TZBL")
    public String getTZBL() {
        return TZBL;
    }

    @JsonProperty("TZBL")
    public void setTZBL(String TZBL) {
        this.TZBL = TZBL;
    }

    @JsonProperty("SFSJKZ")
    public String getSFSJKZ() {
        return SFSJKZ;
    }

    @JsonProperty("SFSJKZ")
    public void setSFSJKZ(String SFSJKZ) {
        this.SFSJKZ = SFSJKZ;
    }

    @JsonProperty("NAME")
    public String getNAME() {
        return NAME;
    }

    @JsonProperty("NAME")
    public void setNAME(String NAME) {
        this.NAME = NAME;
    }

    @JsonProperty("HHR_CODE")
    public String getHHR_CODE() {
        return HHR_CODE;
    }

    @JsonProperty("HHR_CODE")
    public void setHHR_CODE(String HHR_CODE) {
        this.HHR_CODE = HHR_CODE;
    }

    @JsonProperty("TYPE")
    public String getTYPE() {
        return TYPE;
    }

    @JsonProperty("TYPE")
    public void setTYPE(String TYPE) {
        this.TYPE = TYPE;
    }

    @JsonProperty("CATEGORY")
    public String getCATEGORY() {
        return CATEGORY;
    }

    @JsonProperty("CATEGORY")
    public void setCATEGORY(String CATEGORY) {
        this.CATEGORY = CATEGORY;
    }

    @JsonProperty("RJCZE")
    public String getRJCZE() {
        return RJCZE;
    }

    @JsonProperty("RJCZE")
    public void setRJCZE(String RJCZE) {
        this.RJCZE = RJCZE;
    }

    @JsonProperty("RJCZBL")
    public String getRJCZBL() {
        return RJCZBL;
    }

    @JsonProperty("RJCZBL")
    public void setRJCZBL(String RJCZBL) {
        this.RJCZBL = RJCZBL;
    }

    @JsonProperty("SJCZE")
    public String getSJCZE() {
        return SJCZE;
    }

    @JsonProperty("SJCZE")
    public void setSJCZE(String SJCZE) {
        this.SJCZE = SJCZE;
    }

    @JsonProperty("CZFS")
    public String getCZFS() {
        return CZFS;
    }

    @JsonProperty("CZFS")
    public void setCZFS(String CZFS) {
        this.CZFS = CZFS;
    }

    @JsonProperty("JFQX")
    public String getJFQX() {
        return JFQX;
    }

    @JsonProperty("JFQX")
    public void setJFQX(String JFQX) {
        this.JFQX = JFQX;
    }

    @JsonProperty("HH_SSZB")
    public String getHH_SSZB() {
        return HH_SSZB;
    }

    @JsonProperty("HH_SSZB")
    public void setHH_SSZB(String HH_SSZB) {
        this.HH_SSZB = HH_SSZB;
    }
}
