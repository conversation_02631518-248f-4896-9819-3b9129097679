package com.zjhc.gzwcq.cockpit.entity.enums;

public enum QylbEnum {

    QYLB_1("1","1-国有全资企业"),
    QYLB_2("2","2-国有绝对控股企业"),
    QYLB_3("3","3-国有实际控制企业"),
    QYLB_4("4","4-国有参股企业");

    private String value;
    private String text;

    QylbEnum(String value, String text){
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    /**
     * @param value
     * @return
     */
    public static QylbEnum getByValue(String value){
        for(QylbEnum x:values()){
            if(x.getValue().equals(value)){
                return x;
            }
        }
        return null;
    }

}
