package com.zjhc.gzwcq.cockpit.entity;

import lombok.Data;

/**
 * 驾驶舱省-地市数据
 */
@Data
public class CockpitProvinceData {
    /**
     * 企业组织id
     */
    private String unitId;
    /**
     * 企业名称
     */
    private String qymc;
    /**
     * 企业类别
     */
    private String qylb;
    /**
     * 组织形式
     */
    private String zzxs;
    /**
     * 企业级次
     */
    private String qyjc;
    /**
     * 组织父级id
     */
    private String parents;

    /**
     * 统计数量
     */
    private Integer count;

}
