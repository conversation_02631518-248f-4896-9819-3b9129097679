package com.zjhc.gzwcq.dataQueryModel.entity;

import org.apache.commons.lang3.StringUtils;

/**
 * 查询结果类
 */
public class DataQueryModelVo extends DataQueryModel{

    private static final long serialVersionUID = 11L;
    private String situation;//产权登记情形

    //以下为经济行为分析增加的字段
    private Integer no;//序号
    private Long nums;//办理次数
    private String situationCode;//登记情形字典码
    private String jbxxId;//基本信息id
    private String orgId;//组织id

    public String getSituation() {
        if (StringUtils.isNotBlank(this.JB_ZYCQDJQX)){
            return "占有登记-"+JB_ZYCQDJQX;
        }else if (StringUtils.isNotBlank(this.JB_BDCQDJQX)){
            return "变动登记-"+JB_BDCQDJQX;
        }else if (StringUtils.isNotBlank(this.JB_ZXCQDJQX)){
            return "注销登记-"+JB_ZXCQDJQX;
        }else {
            return "";
        }
    }

    public void setSituation(String situation) {
        if (StringUtils.isNotBlank(this.JB_ZYCQDJQX)){
            this.situation = "占有登记-"+JB_ZYCQDJQX;
        }else if (StringUtils.isNotBlank(this.JB_BDCQDJQX)){
            this.situation = "变动登记-"+JB_BDCQDJQX;
        }else if (StringUtils.isNotBlank(this.JB_ZXCQDJQX)){
            this.situation = "注销登记-"+JB_ZXCQDJQX;
        }else {
            this.situation = "";
        }
    }

    public Integer getNo() {
        return no;
    }

    public void setNo(Integer no) {
        this.no = no;
    }

    public Long getNums() {
        return nums;
    }

    public void setNums(Long nums) {
        this.nums = nums;
    }

    public String getSituationCode() {
        return situationCode;
    }

    public void setSituationCode(String situationCode) {
        this.situationCode = situationCode;
    }

    public String getJbxxId() {
        return jbxxId;
    }

    public void setJbxxId(String jbxxId) {
        this.jbxxId = jbxxId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}
