package com.zjhc.gzwcq.cockpit.entity.enums;

public enum ZJSCityIDEnum {
    ZJSGZW("39DC82B5C0000021A568D4D612672F5A","浙江省国资委",0),
    HANGZHOU("3AB6E26D800000015C7379BC3D9A7481","杭州市",1),
    HUZHOU("3AB6E33040000041E70CEF59F0A4129F","湖州市",3),
    JIAXING("3AB6E37A200000018F0C345355DC505B","嘉兴市",4),
    JINHUA("3AB6E3AB8000000140C5044BA4F3122C","金华市",6),
    LISHUI("3AB6E404A0000021AA35BDC77A0BDC44","丽水市",10),
    SHAOXING("3AB6E49F000000017BF63F6852E50FD0","绍兴市",5),
    TAIZHOU("3AB6E4D240000041932C73BB9C7786BE","台州市",9),
    ZHOUSHAN("3AB6EF5300000041BDB53D07E150FD0F","舟山市",8),
    QUZHOU("3AB6EFD900000061B45E38AB570AE290","衢州市",7),
    WENZHOU("3AB6F62420000001A51A62BD62EA13B5","温州市",2),
    ZJSGZWDG("53129A8420000021177AD54D2465FF2F","浙江省国资委代管企业",-1);;

    private String id;

    private String cityName;

    private int sort;

    ZJSCityIDEnum(String id,String cityName,int sort){
        this.id = id;
        this.cityName = cityName;
        this.sort = sort;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    /**
     * @param id
     * @return
     */
    public static ZJSCityIDEnum getByValue(String id){
        for(ZJSCityIDEnum x:values()){
            if(x.getId().equals(id)){
                if(ZJSGZWDG.getId().equals(id)){
                    return ZJSGZW;
                }else{
                    return x;
                }
            }
        }
        return null;
    }

    /**
     * @param cityName
     * @return
     */
    public static ZJSCityIDEnum getByCityName(String cityName){
        for(ZJSCityIDEnum x:values()){
            if(x.getCityName().equals(cityName)){
                if(ZJSGZWDG.getCityName().equals(cityName)){
                    return ZJSGZW;
                }else{
                    return x;
                }
            }
        }
        return null;
    }
}
