package com.zjhc.gzwcq.cockpit.client.hystrix;

import com.zjhc.gzwcq.cockpit.client.CockpitClient;
import com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import java.util.Map;

@Component
public class CockpitHystrix implements CockpitClient {
    @Override
    public Map<String, Object> qylbPrefectureAndCity() {
        throw new RuntimeException("操作失败");
    }

    @Override
    public Map<String, Object> zzxsPrefectureAndCity() {
        throw new RuntimeException("操作失败");
    }

    @Override
    public Map<String, Object> qyjcPrefectureAndCity() {
        throw new RuntimeException("操作失败");
    }

    @Override
    public Map<String, Object> qylbByOneLevel() {
        throw new RuntimeException("操作失败");
    }

    @Override
    public Map<String, Object> zzxsByOneLevel() {
        throw new RuntimeException("操作失败");
    }

    @Override
    public Map<String, Object> qyjcByOneLevel() {
        throw new RuntimeException("操作失败");
    }

    /**
     * 省属企业考核数量
     */
    @Override
    public List<CockpitSSQYKH> getSSQYKHSL(String year) {
        throw new RuntimeException("操作失败");
    }

}
