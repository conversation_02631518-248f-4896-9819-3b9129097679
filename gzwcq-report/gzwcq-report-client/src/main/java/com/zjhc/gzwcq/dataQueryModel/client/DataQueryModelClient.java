package com.zjhc.gzwcq.dataQueryModel.client;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.dataQueryModel.client.hystrix.DataQueryModelHystrix;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(path = "gzwcq/jwt/dataQueryModelRemoteApi", value = "gzwcq-report-service",fallback= DataQueryModelHystrix.class)
public interface DataQueryModelClient {

    @RequestMapping(value="/queryByPage",method= RequestMethod.POST)
    BootstrapTableModel<DataQueryModelVo> queryByPage(@RequestBody DataQueryModelParam param) throws IllegalAccessException;

    @RequestMapping(value="/selectForList",method= RequestMethod.POST)
    List<DataQueryModelVo> selectForList(@RequestBody DataQueryModelParam param) throws IllegalAccessException;

    @RequestMapping(value="/economicAnalysis",method= RequestMethod.POST)
    List<DataQueryModelVo> economicAnalysis(@RequestBody DataQueryModelParam param) throws IllegalAccessException;

    @RequestMapping(value="/economicDetail",method= RequestMethod.POST)
    BootstrapTableModel<DataQueryModelVo> economicDetail(@RequestBody DataQueryModelParam param) throws IllegalAccessException;

    @RequestMapping(value="/selectForDetailList",method= RequestMethod.POST)
    List<DataQueryModelVo> selectForDetailList(@RequestBody DataQueryModelParam param) throws IllegalAccessException;
    @RequestMapping(value="/treeExport",method= RequestMethod.POST)
    ResponseEntity<byte[]> treeExport(DataQueryModelParam param);
}
