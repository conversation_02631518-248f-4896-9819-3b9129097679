package com.zjhc.gzwcq.dataSearchTemp.client.hystrix;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.dataSearchTemp.client.DataSearchTempFeignClient;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class DataSearchTempFeignHystrix implements DataSearchTempFeignClient {


    /**
     * 列表查询
     */
    @Override
    public BootstrapTableModel<DataSearchTempVo> queryByPage(DataSearchTempParam dataSearchTempParam) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
     */
    @Override
    public void insert(DataSearchTemp dataSearchTemp) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 按对象中的主键进行删除，
     */
    @Override
    public void deleteByPrimaryKeys(Map<String, Object> map) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 按对象中的主键进行删除，
     */
    @Override
    public void deleteByPrimaryKey(String id) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 按对象中的主键进行所有非空属性的修改
     */
    @Override
    public void updateIgnoreNull(DataSearchTemp dataSearchTemp) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 更新
     */
    @Override
    public void update(DataSearchTemp dataSearchTemp) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 通过ID查询数据
     */
    @Override
    public DataSearchTemp selectDataSearchTempByPrimaryKey(DataSearchTemp dataSearchTemp) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    @Override
    public List<DataSearchTempVo> selectForList(DataSearchTempParam param) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 数据唯一性验证
     */
    @Override
    public boolean validateUniqueParam(DataSearchTemp dataSearchTemp) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 保存单个对象
     * <P>存在则更新，不存在则新增
     */
    @Override
    public void saveOne(DataSearchTemp dataSearchTemp) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 保存多个对象
     */
    @Override
    public void multipleSaveAndEdit(DataSearchTemp[] objs) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 查询我的模板
     */
    @Override
    public List<DataSearchTempVo> selectMyTemps(DataSearchTempParam param) {
        throw new RuntimeException("操作失败");
    }

    /**
     * 查询共享模板(不包括自己的)
     */
    @Override
    public List<DataSearchTempVo> selectSharedTemps(DataSearchTempParam param) {
        throw new RuntimeException("操作失败");
    }
}