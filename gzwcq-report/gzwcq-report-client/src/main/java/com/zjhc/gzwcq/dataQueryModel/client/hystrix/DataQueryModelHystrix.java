package com.zjhc.gzwcq.dataQueryModel.client.hystrix;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.dataQueryModel.client.DataQueryModelClient;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DataQueryModelHystrix implements DataQueryModelClient {

    @Override
    public BootstrapTableModel<DataQueryModelVo> queryByPage(DataQueryModelParam param) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public List<DataQueryModelVo> selectForList(DataQueryModelParam param) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public List<DataQueryModelVo> economicAnalysis(DataQueryModelParam param) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public BootstrapTableModel<DataQueryModelVo> economicDetail(DataQueryModelParam param) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public List<DataQueryModelVo> selectForDetailList(DataQueryModelParam param) {
        throw new RuntimeException("操作失败");
    }

    @Override
    public ResponseEntity<byte[]> treeExport(DataQueryModelParam param) {
        throw new RuntimeException("操作失败");
    }
}
