package com.zjhc.gzwcq.dataSearchTemp.client;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.dataSearchTemp.client.hystrix.DataSearchTempFeignHystrix;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(path = "gzwcq/jwt/dataSearchTempRemoteApi", value = "gzwcq-report-service",fallback=DataSearchTempFeignHystrix.class)
public interface DataSearchTempFeignClient {
  
  	
  	/**
     * 列表查询
     */
  	@RequestMapping(value="/queryByPage",method=RequestMethod.POST)
	public BootstrapTableModel<DataSearchTempVo> queryByPage(@RequestBody DataSearchTempParam dataSearchTempParam);

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
  	@RequestMapping(value="/insert",method=RequestMethod.POST)
	void insert(@RequestBody DataSearchTemp dataSearchTemp);

	/**
	 * 按对象中的主键进行删除，
	 */
  	@RequestMapping(value="/deleteByPrimaryKeys",method=RequestMethod.POST)
	void deleteByPrimaryKeys(@RequestBody Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
    @RequestMapping(value="/deleteByPrimaryKey",method=RequestMethod.POST)
	void deleteByPrimaryKey(@RequestParam("id") String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
  	@RequestMapping(value="/updateIgnoreNull",method=RequestMethod.POST)
	void updateIgnoreNull(@RequestBody DataSearchTemp dataSearchTemp);
	
	/**
	* 更新
	*/
  	@RequestMapping(value="/update",method=RequestMethod.POST)
	void update(@RequestBody DataSearchTemp dataSearchTemp);
  
	/**
	 *通过ID查询数据
	 */
  	@RequestMapping(value="/selectDataSearchTempByPrimaryKey",method=RequestMethod.POST)
	DataSearchTemp selectDataSearchTempByPrimaryKey(@RequestBody DataSearchTemp dataSearchTemp);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
  	@RequestMapping(value="/selectForList",method=RequestMethod.POST)
	List<DataSearchTempVo> selectForList(@RequestBody DataSearchTempParam param);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
  	@RequestMapping(value="/validateUniqueParam",method=RequestMethod.POST)
	boolean validateUniqueParam(@RequestBody DataSearchTemp dataSearchTemp);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
  	@RequestMapping(value="/saveOne",method=RequestMethod.POST)
	void saveOne(@RequestBody DataSearchTemp dataSearchTemp);
	
	/**
	* 保存多个对象
	*/
  	@RequestMapping(value="/multipleSaveAndEdit",method=RequestMethod.POST)
	void multipleSaveAndEdit(@RequestBody DataSearchTemp[] objs);

	/**
	 * 查询我的模板
	 */
	@PostMapping(value="/selectMyTemps")
	List<DataSearchTempVo> selectMyTemps(@RequestBody DataSearchTempParam param);

	/**
	 * 查询共享模板(不包括自己的)
	 */
	@PostMapping(value="/selectSharedTemps")
	List<DataSearchTempVo> selectSharedTemps(@RequestBody DataSearchTempParam param);
}