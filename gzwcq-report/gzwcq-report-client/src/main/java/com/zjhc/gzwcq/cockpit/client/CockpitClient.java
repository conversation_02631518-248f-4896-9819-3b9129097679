package com.zjhc.gzwcq.cockpit.client;

import com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH;
import com.zjhc.gzwcq.dataQueryModel.client.hystrix.DataQueryModelHystrix;
import com.zjhc.gzwcq.cockpit.client.hystrix.CockpitHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

@FeignClient(path = "gzwcq/jwt/cockpitRemoteApi", value = "gzwcq-report-service",fallback= CockpitHystrix.class)
public interface CockpitClient {

    @RequestMapping(value="/qylbPrefectureAndCity",method= RequestMethod.POST)
    Map<String,Object> qylbPrefectureAndCity();

    @RequestMapping(value="/zzxsPrefectureAndCity",method= RequestMethod.POST)
    Map<String,Object> zzxsPrefectureAndCity();

    @RequestMapping(value="/qyjcPrefectureAndCity",method= RequestMethod.POST)
    Map<String,Object> qyjcPrefectureAndCity();

    @RequestMapping(value="/qylbByOneLevel",method= RequestMethod.POST)
    Map<String,Object> qylbByOneLevel();

    @RequestMapping(value="/zzxsByOneLevel",method= RequestMethod.POST)
    Map<String,Object> zzxsByOneLevel();

    @RequestMapping(value="/qyjcByOneLevel",method= RequestMethod.POST)
    Map<String,Object> qyjcByOneLevel();


    /**
     * 省属企业考核数量
     */
    @RequestMapping(value="/validateUniqueParam",method= RequestMethod.POST)
    List<CockpitSSQYKH> getSSQYKHSL(@RequestBody String year);

}
