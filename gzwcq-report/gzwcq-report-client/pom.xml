<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.zjhc.gzw.gzwcq-parent</groupId>
    <artifactId>gzwcq-report</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-report</groupId>
  <artifactId>gzwcq-report-client</artifactId>
  
  <dependencies>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent.gzwcq-report</groupId>
			<artifactId>gzwcq-report-entity</artifactId>
			<version>${wfw.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zjhc.gzw.gzwcq-parent</groupId>
			<artifactId>gzwcq-common</artifactId>
			<version>${wfw.version}</version>
		</dependency>
		
		<!-- feign 传递文件依赖 -->
		<dependency>
			<groupId>io.github.openfeign.form</groupId>
			<artifactId>feign-form-spring</artifactId>
		</dependency>
	</dependencies>
</project>