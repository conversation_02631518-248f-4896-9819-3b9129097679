<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.dataQueryModel.mapper.IDataQueryModelMapper">

    <resultMap type="com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModel" id="baseResultMap">
    </resultMap>

    <resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo" extends="baseResultMap">
    </resultMap>

    <sql id="columns">
    </sql>

    <!--带别名的列-->
    <sql id="columnsAlias">
    </sql>

    <sql id="vals">
    </sql>

    <!-- 给where查询的表起别名t,方便多表关联查询 -->
    <sql id="whereSql">
    </sql>

    <select id="queryByPage" resultType="com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo">
        select *,view_cq_jbxxb.id jbxxId,view_cq_jbxxb.unitid orgId
        <!--cq_jbxxb.JB_QYMC,cq_jbxxb.JB_ZZJGDM,cq_jbxxb.JB_GJCZQY,cq_jbxxb.JB_ZYCQDJQX,
        cq_jbxxb.JB_BDCQDJQX,cq_jbxxb.JB_ZXCQDJQX,cq_jbxxb.JB_ZCRQ,cq_jbxxb.JB_QYLB,cq_jbxxb.JB_QYJC
        <if test="param.select != null and !param.select.isEmpty() and param.select.size()>0">
            ,
            <foreach collection="param.select" item="s">
                ${s.tableName}.${s.fieldName}
            </foreach>
        </if>-->
        from view_cq_jbxxb view_cq_jbxxb
        join rg_business_info rbi on view_cq_jbxxb.id=rbi.jbxx_id
        <choose>
            <when test="param.timeFilter == '1'.toString()">
                <choose>
                    <when test="param.queryMode != null and param.queryMode == '1'.toString()">
                        <choose>
                            <when test="param.JB_ZCRQ != null and param.JB_ZCRQ != ''">
                                join (SELECT t1.UNITID,max(t.RG_TIMEMARK) maxdate FROM rg_business_info t
                                  JOIN view_cq_jbxxb t1 ON t.jbxx_id = t1.id
                                  where date_format(t.RG_TIMEMARK,'%Y-%m-%d') &lt;= #{param.JB_ZCRQ}
                                  group by t1.unitid) temp1 on rbi.RG_TIMEMARK = temp1.maxdate and view_cq_jbxxb.unitid = temp1.unitid
                            </when>
                            <otherwise>
                                join (SELECT t1.UNITID,max(t.RG_TIMEMARK) maxdate FROM rg_business_info t
                                  JOIN view_cq_jbxxb t1 ON t.jbxx_id = t1.id
                                  where date_format(t.RG_TIMEMARK,'%Y-%m-%d') &lt;= date_format(now(),'%Y-%m-%d')
                                group by t1.unitid) temp1 on rbi.RG_TIMEMARK = temp1.maxdate and view_cq_jbxxb.unitid = temp1.unitid
                            </otherwise>
                        </choose>
                    </when>
                    <when test="param.queryMode != null and param.queryMode == '2'.toString()">
                        <choose>
                            <when test="param.JB_ZCRQ != null and param.JB_ZCRQ != ''">
                                join (SELECT t1.UNITID,max(t.RG_TIMEMARK) maxdate FROM rg_business_info t
                                  JOIN view_cq_jbxxb t1 ON t.jbxx_id = t1.id
                                  where t.RG_UNITSTATE = '2' AND t1.JB_SHZT = '4'
                                  and date_format(t.RG_TIMEMARK,'%Y-%m-%d') &lt;= #{param.JB_ZCRQ}
                                group by t1.unitid) temp1 on rbi.RG_TIMEMARK = temp1.maxdate and view_cq_jbxxb.unitid = temp1.unitid
                            </when>
                            <otherwise>
                                join (SELECT t1.UNITID,max(t.RG_TIMEMARK) maxdate FROM rg_business_info t
                                  JOIN view_cq_jbxxb t1 ON t.jbxx_id = t1.id
                                  where t.RG_UNITSTATE = '2' AND t1.JB_SHZT = '4'
                                  and date_format(t.RG_TIMEMARK,'%Y-%m-%d') &lt;= date_format(now(),'%Y-%m-%d')
                                group by t1.unitid) temp1 on rbi.RG_TIMEMARK = temp1.maxdate and view_cq_jbxxb.unitid = temp1.unitid
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        <choose>
                            <when test="param.JB_ZCRQ != null and param.JB_ZCRQ != ''">
                                and date_format(rbi.RG_TIMEMARK,'%Y-%m-%d') &lt;= #{param.JB_ZCRQ}
                            </when>
                            <otherwise>
                                and date_format(rbi.RG_TIMEMARK,'%Y-%m-%d') &lt;= date_format(now(),'%Y-%m-%d')
                            </otherwise>
                        </choose>
                    </otherwise>
                </choose>
            </when>
            <when test="param.timeFilter == '2'.toString()">
                <if test="param.beginDate != null and param.beginDate != ''
                                               and param.endDate != null and param.endDate != ''">
                    and date_format(rbi.RG_TIMEMARK,'%Y-%m-%d') between #{param.beginDate} and #{param.endDate}
                </if>
            </when>
        </choose>
        <if test="param.joinTableName != null and !param.joinTableName.isEmpty() and param.joinTableName.size()>0">
            <foreach collection="param.joinTableName" item="tName">
                <if test="tName != null and tName != ''">
                    join ${tName} ${tName} on view_cq_jbxxb.id = ${tName}.jbxx_id
                </if>
            </foreach>
        </if>
        <if test="param.leftJoinTableName != null and !param.leftJoinTableName.isEmpty() and param.leftJoinTableName.size()>0">
            <foreach collection="param.leftJoinTableName" item="tName">
                <if test="tName != null and tName != ''">
                    left join ${tName} ${tName} on view_cq_jbxxb.id = ${tName}.jbxx_id
                </if>
            </foreach>
        </if>
        <where>
            <if test="param.rgType == null or param.rgType == ''">
                ((view_cq_jbxxb.jb_zycqdjqx is null or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt = '4')
                and view_cq_jbxxb.unitid in (select t1.ORGANIZATION_ID from (
                <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                    (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                </foreach>)as t1)
                <!--占有未通过的使用出资人id,通过的使用自己的unitId-->
                or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt !='4' and view_cq_jbxxb.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
                <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                    (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                </foreach>) as t2))
            </if>
            <if test="param.JB_ZZJGDM != null and param.JB_ZZJGDM != ''">
                and view_cq_jbxxb.JB_ZZJGDM like concat('%',#{param.JB_ZZJGDM},'%')
            </if>
            <if test="param.JB_QYMC != null and param.JB_QYMC != ''">
                and view_cq_jbxxb.JB_QYMC like concat('%',#{param.JB_QYMC},'%')
            </if>
            <if test="param.zyhySet != null and !param.zyhySet.isEmpty() and param.zyhySet.size()>0">
                and
                <foreach collection="param.zyhySet" item="item" separator="or" open="(" close=")">
                    <if test="item != null and item != ''">
                        find_in_set(#{item},view_cq_jbxxb.JB_ZYHY)
                    </if>
                </foreach>
            </if>
            <if test="param.zcdSet != null and !param.zcdSet.isEmpty() and param.zcdSet.size()>0">
                and view_cq_jbxxb.JB_ZCD in 
                <foreach collection="param.zcdSet" item="item" separator="," open="(" close=")">
	                <if test="item != null and item != ''">
	                    #{item}
	                </if>
            	</foreach>
            </if>

            <!--多层级字典筛选-->
            <if test="param.multiLevelListCheckBox != null and !param.multiLevelListCheckBox.isEmpty() and param.multiLevelListCheckBox.size()>0">
                <foreach collection="param.multiLevelListCheckBox" item="item">
                    and
                    <if test="item != null and item.dicVals != null and !item.dicVals.isEmpty() and item.dicVals.size()>0">
                        <foreach collection="item.dicVals" item="itm" separator="or" open="(" close=")">
                            find_in_set(#{itm},${item.tableName}.${item.fieldName})
                        </foreach>
                    </if>
                </foreach>
            </if>

            <!--单层级字典筛选-->
            <if test="param.multiLevelListRadio != null and !param.multiLevelListRadio.isEmpty() and param.multiLevelListRadio.size()>0">
                <foreach collection="param.multiLevelListRadio" item="item">
                    and ${item.tableName}.${item.fieldName} in
                    <if test="item != null and item.dicVals != null and !item.dicVals.isEmpty() and item.dicVals.size()>0">
                        <foreach collection="item.dicVals" item="itm" separator="," open="(" close=")">
                            #{itm}
                        </foreach>
                    </if>
                </foreach>
            </if>

            <if test="param.JB_QYJC != null and param.JB_QYJC != ''">
                and view_cq_jbxxb.JB_QYJC = #{param.JB_QYJC}
            </if>
            <if test="param.JB_QYLB != null and param.JB_QYLB != ''">
                and view_cq_jbxxb.JB_QYLB = #{param.JB_QYLB}
            </if>
            <choose>
                <when test="param.rgType == '0'.toString()">
                    and ((view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt = '4')
                    and view_cq_jbxxb.unitid in (select t1.ORGANIZATION_ID from (
                    <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                        (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                    </foreach>)as t1)
                    <!--占有未通过的使用出资人id,通过的使用自己的unitId-->
                    or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt !='4' and view_cq_jbxxb.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
                    <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                        (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                    </foreach>) as t2))
                </when>
                <when test="param.rgType == '1'.toString()">
                    and view_cq_jbxxb.JB_BDCQDJQX is not null
                    and view_cq_jbxxb.unitid in (select tt.ORGANIZATION_ID from (
                    <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                        (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                    </foreach>) tt)
                </when>
                <when test="param.rgType == '3'.toString()">
                    and view_cq_jbxxb.JB_ZXCQDJQX is not null
                    and view_cq_jbxxb.unitid in (select tt.ORGANIZATION_ID from (
                    <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                        (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
                    </foreach>) tt)
                </when>
            </choose>
            <if test="param.dicWhere != null and !param.dicWhere.isEmpty() and param.dicWhere.size()>0">
                <foreach collection="param.dicWhere" item="dic">
                    <if test="dic.tableName != null and dic.tableName != ''
                                and dic.fieldName != null and dic.fieldName != ''
                                and dic.fieldValue != null and dic.fieldValue != ''">
                        and ${dic.tableName}.${dic.fieldName} = #{dic.fieldValue}
                    </if>
                </foreach>
            </if>
            <if test="param.textWhere != null and !param.textWhere.isEmpty() and param.textWhere.size()>0">
                <foreach collection="param.textWhere" item="text">
                    <if test="text.tableName != null and text.tableName != ''
                                and text.fieldName != null and text.fieldName != ''
                                and text.fieldValue != null and text.fieldValue != ''">
                        and ${text.tableName}.${text.fieldName} like concat('%',#{text.fieldValue},'%')
                    </if>
                </foreach>
            </if>
            <if test="param.dateWhere != null and !param.dateWhere.isEmpty() and param.dateWhere.size()>0">
                <foreach collection="param.dateWhere" item="date">
                    <if test="date.tableName != null and date.tableName != ''
                                and date.fieldName != null and date.fieldName != ''
                                and date.fieldValue != null and date.fieldValue != ''">
                        and date_format(${date.tableName}.${date.fieldName},'%Y-%m-%d') = #{date.fieldValue}
                    </if>
                </foreach>
            </if>
            <if test="param.dateRangeWhere != null and !param.dateRangeWhere.isEmpty() and param.dateRangeWhere.size()>0">
                <foreach collection="param.dateRangeWhere" item="dateRange">
                    <if test="dateRange.tableName != null and dateRange.tableName != ''
                                and dateRange.fieldName != null and dateRange.fieldName != ''
                                and dateRange.fieldValue != null and dateRange.fieldValue != ''">
                        and date_format(${dateRange.tableName}.${dateRange.fieldName},'%Y-%m-%d')
                        between substring_index(#{dateRange.fieldValue},',', 1) and substring_index(#{dateRange.fieldValue},',', -1)
                    </if>
                </foreach>
            </if>
            <if test="param.numWhere != null and !param.numWhere.isEmpty() and param.numWhere.size()>0">
                <foreach collection="param.numWhere" item="num">
                    <if test="num.tableName != null and num.tableName != ''
                                and num.fieldName != null and num.fieldName != ''
                                and num.fieldValue != null and num.fieldValue != ''">
                        and ${num.tableName}.${num.fieldName} between substring_index(#{num.fieldValue},',', 1)
                        and substring_index(#{num.fieldValue},',', -1)
                    </if>
                </foreach>
            </if>
            <!--组织筛选-->
            <if test="param.selectedOrganizationIds != null and !param.selectedOrganizationIds.isEmpty() and param.selectedOrganizationIds.size()>0">
                and (((view_cq_jbxxb.jb_zycqdjqx is null or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt = '4')
                and view_cq_jbxxb.unitid in
                <foreach collection="param.selectedOrganizationIds" open="(" close=")" separator="," item="sid">
                    #{sid}
                </foreach>)
                <!--占有未通过的使用出资人id,通过的使用自己的unitId-->
                or (view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt !='4' and view_cq_jbxxb.jb_czrzzjgid in
                <foreach collection="param.selectedOrganizationIds" open="(" close=")" separator="," item="sid">
                    #{sid}
                </foreach>)
                )
            </if>
        </where>
        order by rbi.RG_TIMEMARK desc
    </select>

    <select id="economicAnalysis" resultType="com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo">
        select view_cq_jbxxb.JB_ZYCQDJQX,view_cq_jbxxb.JB_BDCQDJQX,view_cq_jbxxb.JB_ZXCQDJQX,
               count(view_cq_jbxxb.id) nums
        <if test="param.numSelect != null and !param.numSelect.isEmpty() and param.numSelect.size()>0">
            <foreach collection="param.numSelect" item="num">
                <if test="num.tableName != null and num.tableName != ''
                                and num.fieldName != null and num.fieldName != ''">
                    ,sum(ifnull(${num.tableName}.${num.fieldName},0)) ${num.fieldName}
                </if>
            </foreach>
        </if>
           from view_cq_jbxxb view_cq_jbxxb left join rg_business_info rbi on view_cq_jbxxb.id=rbi.jbxx_id
        <if test="param.joinTableName != null and !param.joinTableName.isEmpty() and param.joinTableName.size()>0">
            <foreach collection="param.joinTableName" item="tName">
                <if test="tName != null and tName != ''">
                    join ${tName} ${tName} on view_cq_jbxxb.id = ${tName}.jbxx_id
                </if>
            </foreach>
        </if>
        <if test="param.leftJoinTableName != null and !param.leftJoinTableName.isEmpty() and param.leftJoinTableName.size()>0">
            <foreach collection="param.leftJoinTableName" item="tName">
                <if test="tName != null and tName != ''">
                    left join ${tName} ${tName} on view_cq_jbxxb.id = ${tName}.jbxx_id
                </if>
            </foreach>
        </if>
        <where>
            ((view_cq_jbxxb.jb_zycqdjqx is null or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt = '4')
            and view_cq_jbxxb.unitid in (select t1.ORGANIZATION_ID from (
            <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
            </foreach>)as t1)
            <!--占有未通过的使用出资人id,通过的使用自己的unitId-->
            or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt !='4' and view_cq_jbxxb.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
            <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
            </foreach>) as t2))
            <if test="param.situations != null and !param.situations.isEmpty() and param.situations.size()>0">
                and
                (
                <foreach collection="param.situations" item="situation" separator="or">
                    <if test="situation != null and situation != ''">
                        view_cq_jbxxb.JB_ZYCQDJQX = #{situation}
                        or view_cq_jbxxb.JB_BDCQDJQX = #{situation}
                        or view_cq_jbxxb.JB_ZXCQDJQX = #{situation}
                    </if>
                </foreach>
                )
            </if>
            <if test="param.beginDate != null and param.beginDate != ''
                               and param.endDate != null and param.endDate != ''">
                and date_format(rbi.RG_TIMEMARK,'%Y-%m-%d') between #{param.beginDate} and #{param.endDate}
            </if>

            <!--多层级字典筛选-->
            <if test="param.multiLevelListCheckBox != null and !param.multiLevelListCheckBox.isEmpty() and param.multiLevelListCheckBox.size()>0">
                <foreach collection="param.multiLevelListCheckBox" item="item">
                    and
                    <if test="item != null and item.dicVals != null and !item.dicVals.isEmpty() and item.dicVals.size()>0">
                        <foreach collection="item.dicVals" item="itm" separator="or" open="(" close=")">
                            find_in_set(#{itm},${item.tableName}.${item.fieldName})
                        </foreach>
                    </if>
                </foreach>
            </if>

            <!--单层级字典筛选-->
            <if test="param.multiLevelListRadio != null and !param.multiLevelListRadio.isEmpty() and param.multiLevelListRadio.size()>0">
                <foreach collection="param.multiLevelListRadio" item="item">
                    and ${item.tableName}.${item.fieldName} in
                    <if test="item != null and item.dicVals != null and !item.dicVals.isEmpty() and item.dicVals.size()>0">
                        <foreach collection="item.dicVals" item="itm" separator="," open="(" close=")">
                            #{itm}
                        </foreach>
                    </if>
                </foreach>
            </if>

            <if test="param.dicWhere != null and !param.dicWhere.isEmpty() and param.dicWhere.size()>0">
                <foreach collection="param.dicWhere" item="dic">
                    <if test="dic.tableName != null and dic.tableName != ''
                                and dic.fieldName != null and dic.fieldName != ''
                                and dic.fieldValue != null and dic.fieldValue != ''">
                        and ${dic.tableName}.${dic.fieldName} = #{dic.fieldValue}
                    </if>
                </foreach>
            </if>
            <if test="param.textWhere != null and !param.textWhere.isEmpty() and param.textWhere.size()>0">
                <foreach collection="param.textWhere" item="text">
                    <if test="text.tableName != null and text.tableName != ''
                                and text.fieldName != null and text.fieldName != ''
                                and text.fieldValue != null and text.fieldValue != ''">
                        and ${text.tableName}.${text.fieldName} like concat('%',#{text.fieldValue},'%')
                    </if>
                </foreach>
            </if>
            <if test="param.dateWhere != null and !param.dateWhere.isEmpty() and param.dateWhere.size()>0">
                <foreach collection="param.dateWhere" item="date">
                    <if test="date.tableName != null and date.tableName != ''
                                and date.fieldName != null and date.fieldName != ''
                                and date.fieldValue != null and date.fieldValue != ''">
                        and date_format(${date.tableName}.${date.fieldName},'%Y-%m-%d') = #{date.fieldValue}
                    </if>
                </foreach>
            </if>
            <if test="param.dateRangeWhere != null and !param.dateRangeWhere.isEmpty() and param.dateRangeWhere.size()>0">
                <foreach collection="param.dateRangeWhere" item="dateRange">
                    <if test="dateRange.tableName != null and dateRange.tableName != ''
                                and dateRange.fieldName != null and dateRange.fieldName != ''
                                and dateRange.fieldValue != null and dateRange.fieldValue != ''">
                        and date_format(${dateRange.tableName}.${dateRange.fieldName},'%Y-%m-%d')
                        between substring_index(#{dateRange.fieldValue},',', 1) and substring_index(#{dateRange.fieldValue},',', -1)
                    </if>
                </foreach>
            </if>
            <if test="param.numWhere != null and !param.numWhere.isEmpty() and param.numWhere.size()>0">
                <foreach collection="param.numWhere" item="num">
                    <if test="num.tableName != null and num.tableName != ''
                                and num.fieldName != null and num.fieldName != ''
                                and num.fieldValue != null and num.fieldValue != ''">
                        and ${num.tableName}.${num.fieldName} between substring_index(#{num.fieldValue},',', 1)
                        and substring_index(#{num.fieldValue},',', -1)
                    </if>
                </foreach>
            </if>
            and
            ((view_cq_jbxxb.JB_ZYCQDJQX is not null and view_cq_jbxxb.JB_BDCQDJQX is null and view_cq_jbxxb.JB_ZXCQDJQX is null)
            or (view_cq_jbxxb.JB_ZYCQDJQX is null and view_cq_jbxxb.JB_BDCQDJQX is not null and view_cq_jbxxb.JB_ZXCQDJQX is null)
            or (view_cq_jbxxb.JB_ZYCQDJQX is null and view_cq_jbxxb.JB_BDCQDJQX is null and view_cq_jbxxb.JB_ZXCQDJQX is not null))
        </where>
        group by view_cq_jbxxb.JB_ZYCQDJQX, view_cq_jbxxb.JB_BDCQDJQX, view_cq_jbxxb.JB_ZXCQDJQX
        order by concat(ifnull(view_cq_jbxxb.JB_ZYCQDJQX,''),ifnull(view_cq_jbxxb.JB_BDCQDJQX,''),ifnull(view_cq_jbxxb.JB_ZXCQDJQX,''))+0
    </select>

    <select id="economicDetail" resultType="com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo">
        select *,view_cq_jbxxb.id jbxxId,view_cq_jbxxb.unitid orgId
        from view_cq_jbxxb view_cq_jbxxb left join rg_business_info rbi on view_cq_jbxxb.id=rbi.jbxx_id
        <if test="param.joinTableName != null and !param.joinTableName.isEmpty() and param.joinTableName.size()>0">
            <foreach collection="param.joinTableName" item="tName">
                <if test="tName != null and tName != ''">
                    join ${tName} ${tName} on view_cq_jbxxb.id = ${tName}.jbxx_id
                </if>
            </foreach>
        </if>
        <if test="param.leftJoinTableName != null and !param.leftJoinTableName.isEmpty() and param.leftJoinTableName.size()>0">
            <foreach collection="param.leftJoinTableName" item="tName">
                <if test="tName != null and tName != ''">
                    left join ${tName} ${tName} on view_cq_jbxxb.id = ${tName}.jbxx_id
                </if>
            </foreach>
        </if>
        <where>
            ((view_cq_jbxxb.jb_zycqdjqx is null or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt = '4')
            and view_cq_jbxxb.unitid in (select t1.ORGANIZATION_ID from (
            <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
            </foreach>)as t1)
            <!--占有未通过的使用出资人id,通过的使用自己的unitId-->
            or view_cq_jbxxb.jb_zycqdjqx is not null and view_cq_jbxxb.jb_shzt !='4' and view_cq_jbxxb.jb_czrzzjgid in (select t2.ORGANIZATION_ID from (
            <foreach collection="auditHostingList" item="item" separator="union" index="index" open="(" close=")">
                (select ORGANIZATION_ID from sys_organization where FIND_IN_SET(#{item},PARENTS))
            </foreach>) as t2))
            <if test="param.situationCode != null and param.situationCode != ''">
                and (view_cq_jbxxb.JB_ZYCQDJQX = #{param.situationCode}
                or view_cq_jbxxb.JB_BDCQDJQX = #{param.situationCode}
                or view_cq_jbxxb.JB_ZXCQDJQX = #{param.situationCode})
            </if>
            <if test="param.beginDate != null and param.beginDate != ''
                               and param.endDate != null and param.endDate != ''">
                and date_format(rbi.RG_TIMEMARK,'%Y-%m-%d') between #{param.beginDate} and #{param.endDate}
            </if>

            <!--多层级字典筛选-->
            <if test="param.multiLevelListCheckBox != null and !param.multiLevelListCheckBox.isEmpty() and param.multiLevelListCheckBox.size()>0">
                <foreach collection="param.multiLevelListCheckBox" item="item">
                    and
                    <if test="item != null and item.dicVals != null and !item.dicVals.isEmpty() and item.dicVals.size()>0">
                        <foreach collection="item.dicVals" item="itm" separator="or" open="(" close=")">
                            find_in_set(#{itm},${item.tableName}.${item.fieldName})
                        </foreach>
                    </if>
                </foreach>
            </if>

            <!--单层级字典筛选-->
            <if test="param.multiLevelListRadio != null and !param.multiLevelListRadio.isEmpty() and param.multiLevelListRadio.size()>0">
                <foreach collection="param.multiLevelListRadio" item="item">
                    and ${item.tableName}.${item.fieldName} in
                    <if test="item != null and item.dicVals != null and !item.dicVals.isEmpty() and item.dicVals.size()>0">
                        <foreach collection="item.dicVals" item="itm" separator="," open="(" close=")">
                            #{itm}
                        </foreach>
                    </if>
                </foreach>
            </if>

            <if test="param.dicWhere != null and !param.dicWhere.isEmpty() and param.dicWhere.size()>0">
                <foreach collection="param.dicWhere" item="dic">
                    <if test="dic.tableName != null and dic.tableName != ''
                                and dic.fieldName != null and dic.fieldName != ''
                                and dic.fieldValue != null and dic.fieldValue != ''">
                        and ${dic.tableName}.${dic.fieldName} = #{dic.fieldValue}
                    </if>
                </foreach>
            </if>
            <if test="param.textWhere != null and !param.textWhere.isEmpty() and param.textWhere.size()>0">
                <foreach collection="param.textWhere" item="text">
                    <if test="text.tableName != null and text.tableName != ''
                                and text.fieldName != null and text.fieldName != ''
                                and text.fieldValue != null and text.fieldValue != ''">
                        and ${text.tableName}.${text.fieldName} like concat('%',#{text.fieldValue},'%')
                    </if>
                </foreach>
            </if>
            <if test="param.dateWhere != null and !param.dateWhere.isEmpty() and param.dateWhere.size()>0">
                <foreach collection="param.dateWhere" item="date">
                    <if test="date.tableName != null and date.tableName != ''
                                and date.fieldName != null and date.fieldName != ''
                                and date.fieldValue != null and date.fieldValue != ''">
                        and date_format(${date.tableName}.${date.fieldName},'%Y-%m-%d') = #{date.fieldValue}
                    </if>
                </foreach>
            </if>
            <if test="param.dateRangeWhere != null and !param.dateRangeWhere.isEmpty() and param.dateRangeWhere.size()>0">
                <foreach collection="param.dateRangeWhere" item="dateRange">
                    <if test="dateRange.tableName != null and dateRange.tableName != ''
                                and dateRange.fieldName != null and dateRange.fieldName != ''
                                and dateRange.fieldValue != null and dateRange.fieldValue != ''">
                        and date_format(${dateRange.tableName}.${dateRange.fieldName},'%Y-%m-%d')
                        between substring_index(#{dateRange.fieldValue},',', 1) and substring_index(#{dateRange.fieldValue},',', -1)
                    </if>
                </foreach>
            </if>
            <if test="param.numWhere != null and !param.numWhere.isEmpty() and param.numWhere.size()>0">
                <foreach collection="param.numWhere" item="num">
                    <if test="num.tableName != null and num.tableName != ''
                                and num.fieldName != null and num.fieldName != ''
                                and num.fieldValue != null and num.fieldValue != ''">
                        and ${num.tableName}.${num.fieldName} between substring_index(#{num.fieldValue},',', 1)
                        and substring_index(#{num.fieldValue},',', -1)
                    </if>
                </foreach>
            </if>
        </where>
        order by rbi.RG_TIMEMARK desc
    </select>
</mapper>