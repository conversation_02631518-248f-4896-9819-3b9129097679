package com.zjhc.gzwcq.cockpit.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData;
import com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH;
import com.zjhc.gzwcq.cockpit.entity.OneLevelOrg;
import com.zjhc.gzwcq.cockpit.entity.enums.QyjcEnum;
import com.zjhc.gzwcq.cockpit.entity.enums.QylbEnum;
import com.zjhc.gzwcq.cockpit.entity.enums.ZJSCityIDEnum;
import com.zjhc.gzwcq.cockpit.entity.enums.ZzxsEnum;
import com.zjhc.gzwcq.cockpit.mapper.CockpitMapper;
import com.zjhc.gzwcq.cockpit.service.api.ICockpitService;
import com.zjhc.gzwcq.screen.entity.TimelinessRate;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Log4j
@Service
public class CockpitServiceImpl implements ICockpitService {
    @Resource
    private CockpitMapper cockpitMapper;

    private final String provinceMapName = "浙江省总数";
    private final String cityMapName = "浙江省各地市";

    /**
     * @description 过时，性能慢，新的方法见 qylbPrefectureAndCityV2
     */
    @Deprecated
    @Override
    public Map<String, Object> qylbPrefectureAndCity() {
        Map<String, Object> re = new HashMap<>();
        Map<String, Object> province = new HashMap<>();
        re.put(provinceMapName, province);
        List<Map<String, Object>> city = new ArrayList<>();
        re.put(cityMapName, city);
        List<CockpitProvinceData> baseData = cockpitMapper.qylbPrefectureAndCity();

        for (CockpitProvinceData pd : baseData) {
            String[] parentsArr = pd.getParents().split(",");
            if (StringUtils.isEmpty(parentsArr[1]) || ZJSCityIDEnum.getByValue(parentsArr[1]) == null
                    || StringUtils.isEmpty(pd.getQylb()) || QylbEnum.getByValue(pd.getQylb()) == null) {
                continue;
            }
            String qylbText = QylbEnum.getByValue(pd.getQylb()).getText();
            prefectureAndCityAddNum(re, parentsArr[1], qylbText);
        }
        sortByCity((List<Map<String, Object>>) re.get(cityMapName));
        return re;
    }

    /**
     * @description 过时，性能慢，新的方法见 zzxsPrefectureAndCityV2
     */
    @Deprecated
    @Override
    public Map<String, Object> zzxsPrefectureAndCity() {
        Map<String, Object> re = new HashMap<>();
        Map<String, Object> province = new HashMap<>();
        re.put(provinceMapName, province);
        List<Map<String, Object>> city = new ArrayList<>();
        re.put(cityMapName, city);
        List<CockpitProvinceData> baseData = cockpitMapper.zzxsPrefectureAndCity();

        for (CockpitProvinceData pd : baseData) {
            String[] parentsArr = pd.getParents().split(",");
            if (StringUtils.isEmpty(pd.getZzxs()) || StringUtils.isEmpty(parentsArr[1])) {
                continue;
            }
            //组织形式大小类转换
//            String zzxsVal = Integer.parseInt(pd.getZzxs()) / 10 + "0";
            if (ZJSCityIDEnum.getByValue(parentsArr[1]) == null
                    || ZzxsEnum.getByValue(pd.getZzxs()) == null) {
                continue;
            }
            String zzxsText = ZzxsEnum.getByValue(pd.getZzxs()).getText();
            prefectureAndCityAddNum(re, parentsArr[1], zzxsText);
        }
        sortByCity((List<Map<String, Object>>) re.get(cityMapName));
        return re;
    }

    /**
     * @description 过时，性能慢，新的方法见 qyjcPrefectureAndCityV2
     */
    @Deprecated
    @Override
    public Map<String, Object> qyjcPrefectureAndCity() {
        Map<String, Object> re = new HashMap<>();
        Map<String, Object> province = new HashMap<>();
        re.put(provinceMapName, province);
        List<Map<String, Object>> city = new ArrayList<>();
        re.put(cityMapName, city);
        List<CockpitProvinceData> baseData = cockpitMapper.qyjcPrefectureAndCity();

        for (CockpitProvinceData pd : baseData) {
            String[] parentsArr = pd.getParents().split(",");
            if (StringUtils.isEmpty(pd.getQyjc()) || StringUtils.isEmpty(parentsArr[1])) {
                continue;
            }
            //企业级次转数字
            int qyjcVal = Integer.parseInt(pd.getQyjc());
            if (ZJSCityIDEnum.getByValue(parentsArr[1]) == null
                    || QyjcEnum.getByValue(qyjcVal) == null) {
                continue;
            }
            String qyjcText = QyjcEnum.getByValue(qyjcVal).getText();
            prefectureAndCityAddNum(re, parentsArr[1], qyjcText);
        }
        sortByCity((List<Map<String, Object>>) re.get(cityMapName));
        return re;
    }

    @Override
    public Map<String, Object> qylbByOneLevel() {
        Map<String, Object> re = new HashMap<>();
        Map<String, Object> province = new HashMap<>();
        re.put(provinceMapName, province);
        List<Map<String, Object>> city = new ArrayList<>();
        re.put(cityMapName, city);

        //查询浙江省国资委+国资委代管 下面的一级企业列表
        List<OneLevelOrg> oneLevelOrgList = cockpitMapper.queryOneLevelOrg();
        //查询浙江省国资委+国资委代管 下面的非一级企业列表
        List<CockpitProvinceData> baseData = cockpitMapper.qylbByOneLevel();

        for (CockpitProvinceData pd : baseData) {
            String parents = pd.getParents();
            if (StringUtils.isEmpty(pd.getQylb()) || StringUtils.isEmpty(parents)) {
                continue;
            }
            //查找所属一级企业
            Optional<OneLevelOrg> op = oneLevelOrgList.stream().filter(o -> parents.contains(o.getOrgId())).findFirst();
            if (!op.isPresent() || QylbEnum.getByValue(pd.getQylb()) == null) {
                continue;
            }
            String qylbText = QylbEnum.getByValue(pd.getQylb()).getText();
            String oneLevelName = op.get().getOrgName();
            //去除浙江省跟有限公司，减少名字长度
            oneLevelName = oneLevelName.replaceAll("浙江省", "")
                    .replaceAll("有限公司", "");
            addNum(re, cityMapName, oneLevelName, qylbText);
        }
        return re;
    }

    @Override
    public Map<String, Object> zzxsByOneLevel() {
        Map<String, Object> re = new HashMap<>();
        Map<String, Object> province = new HashMap<>();
        re.put(provinceMapName, province);
        List<Map<String, Object>> city = new ArrayList<>();
        re.put(cityMapName, city);

        //查询浙江省国资委+国资委代管 下面的一级企业列表
        List<OneLevelOrg> oneLevelOrgList = cockpitMapper.queryOneLevelOrg();
        //查询浙江省国资委+国资委代管 下面的非一级企业列表
        List<CockpitProvinceData> baseData = cockpitMapper.zzxsByOneLevel();

        for (CockpitProvinceData pd : baseData) {
            String parents = pd.getParents();
            if (StringUtils.isEmpty(pd.getZzxs()) || StringUtils.isEmpty(parents)) {
                continue;
            }
            //组织形式大小类转换
//            String zzxsVal = Integer.parseInt(pd.getZzxs()) / 10 + "0";
            //查找所属一级企业
            Optional<OneLevelOrg> op = oneLevelOrgList.stream().filter(o -> parents.contains(o.getOrgId())).findFirst();
            if (!op.isPresent() || ZzxsEnum.getByValue(pd.getZzxs()) == null) {
                continue;
            }
            String zzxsText = ZzxsEnum.getByValue(pd.getZzxs()).getText();
            String oneLevelName = op.get().getOrgName();
            //去除浙江省跟有限公司，减少名字长度
            oneLevelName = oneLevelName.replaceAll("浙江省", "")
                    .replaceAll("有限公司", "");
            addNum(re, cityMapName, oneLevelName, zzxsText);
        }
        return re;
    }

    @Override
    public Map<String, Object> qyjcByOneLevel() {
        Map<String, Object> re = new HashMap<>();
        Map<String, Object> province = new HashMap<>();
        re.put(provinceMapName, province);
        List<Map<String, Object>> city = new ArrayList<>();
        re.put(cityMapName, city);

        //查询浙江省国资委+国资委代管 下面的一级企业列表
        List<OneLevelOrg> oneLevelOrgList = cockpitMapper.queryOneLevelOrg();
        //查询浙江省国资委+国资委代管 下面的非一级企业列表
        List<CockpitProvinceData> baseData = cockpitMapper.qyjcByOneLevel();

        for (CockpitProvinceData pd : baseData) {
            String parents = pd.getParents();
            if (StringUtils.isEmpty(pd.getQyjc()) || StringUtils.isEmpty(parents)) {
                continue;
            }
            //企业级次转数字
            int qyjcVal = Integer.parseInt(pd.getQyjc());
            //查找所属一级企业
            Optional<OneLevelOrg> op = oneLevelOrgList.stream().filter(o -> parents.contains(o.getOrgId())).findFirst();
            if (!op.isPresent() || QyjcEnum.getByValue(qyjcVal) == null) {
                continue;
            }
            String qyjcText = QyjcEnum.getByValue(qyjcVal).getText();
            String oneLevelName = op.get().getOrgName();
            //去除浙江省跟有限公司，减少名字长度
            oneLevelName = oneLevelName.replaceAll("浙江省", "")
                    .replaceAll("有限公司", "");
            addNum(re, cityMapName, oneLevelName, qyjcText);
        }
        return re;
    }

    private void prefectureAndCityAddNum(Map<String, Object> re, String cityId, String typeText) {
        addNum(re, provinceMapName, null, typeText);
        switch (ZJSCityIDEnum.getByValue(cityId)) {
            case ZJSGZW:
                addNum(re, cityMapName, ZJSCityIDEnum.ZJSGZW.getCityName(), typeText);
                break;
            case HANGZHOU:
                addNum(re, cityMapName, ZJSCityIDEnum.HANGZHOU.getCityName(), typeText);
                break;
            case HUZHOU:
                addNum(re, cityMapName, ZJSCityIDEnum.HUZHOU.getCityName(), typeText);
                break;
            case JIAXING:
                addNum(re, cityMapName, ZJSCityIDEnum.JIAXING.getCityName(), typeText);
                break;
            case JINHUA:
                addNum(re, cityMapName, ZJSCityIDEnum.JINHUA.getCityName(), typeText);
                break;
            case LISHUI:
                addNum(re, cityMapName, ZJSCityIDEnum.LISHUI.getCityName(), typeText);
                break;
            case SHAOXING:
                addNum(re, cityMapName, ZJSCityIDEnum.SHAOXING.getCityName(), typeText);
                break;
            case TAIZHOU:
                addNum(re, cityMapName, ZJSCityIDEnum.TAIZHOU.getCityName(), typeText);
                break;
            case ZHOUSHAN:
                addNum(re, cityMapName, ZJSCityIDEnum.ZHOUSHAN.getCityName(), typeText);
                break;
            case QUZHOU:
                addNum(re, cityMapName, ZJSCityIDEnum.QUZHOU.getCityName(), typeText);
                break;
            case WENZHOU:
                addNum(re, cityMapName, ZJSCityIDEnum.WENZHOU.getCityName(), typeText);
                break;
            default:
                break;
        }
    }


    private void addNum(Map<String, Object> re, String mapName, String cityName, String typeName) {
        if (provinceMapName.equals(mapName)) {  //全省
            Map<String, Object> pcMap = (Map<String, Object>) re.get(mapName);
            Integer typeNum = (Integer) pcMap.get(typeName);
            if (null == typeNum) {
                pcMap.put(typeName, 1);
            } else {
                pcMap.put(typeName, typeNum + 1);
            }
        } else if (cityMapName.equals(mapName)) { //各地市
            List<Map<String, Object>> pcMap = (List<Map<String, Object>>) re.get(mapName);
            Optional<Map<String, Object>> op = pcMap.stream().filter(p -> p.get("cityName").equals(cityName)).findFirst();
            if (op.isPresent()) {
                Map<String, Object> ctMap = op.get();
                Integer typeNum = (Integer) ctMap.get(typeName);
                if (null == typeNum) {
                    ctMap.put(typeName, 1);
                } else {
                    ctMap.put(typeName, typeNum + 1);
                }
            } else {
                Map<String, Object> ctMap = new HashMap<>();
                ctMap.put("cityName", cityName);
                ZJSCityIDEnum zjsCityIDEnum = ZJSCityIDEnum.getByCityName(cityName);
                if (zjsCityIDEnum != null) {
                    ctMap.put("sort", zjsCityIDEnum.getSort());
                }
                ctMap.put(typeName, 1);
                pcMap.add(ctMap);
            }
        }
    }

    private void sortByCity(List<Map<String, Object>> pcMap) {
        pcMap.sort((map1, map2) -> Integer.parseInt(map1.get("sort").toString())
                - Integer.parseInt(map2.get("sort").toString()));
        for (Map<String, Object> map : pcMap) {
            map.remove("sort");
        }
    }

    /**
     * 省属企业考核数量
     */
    @Override
    public List<CockpitSSQYKH> getSSQYKHSL(String year) {
        List<CockpitSSQYKH> ssqykhsl = null;
        try {
            //浙江省国资委 39DC82B5C0000021A568D4D612672F5A 及 浙江省国资委代管 53129A8420000021177AD54D2465FF2F 下面一级企业
            ssqykhsl = cockpitMapper.getSSQYKHSL();
            //查询所有登记数据列表
            List<TimelinessRate> tlList = cockpitMapper.queryTimelinessRateList(year);
            //将所有BUSINESS_INFO_ID拉出来查询
            List<String> idList = tlList.stream().map(TimelinessRate::getBusinessInfoId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idList)) {
                //所有提交流程第一次到国资委的时间，流程以BUSINESS_INFO_ID为准
                List<TimelinessRate> dateList = cockpitMapper.queryAllApprovalDate(idList);
                //分组计算
                for (CockpitSSQYKH ssqykh : ssqykhsl) {
                    int all = 0, tl = 0; //分母/分子
                    List<TimelinessRate> rateList = tlList.stream().filter(attr -> attr.getParents().contains(ssqykh.getUnitId())).collect(Collectors.toList());
                    Date nowDate = new Date();
                    for (TimelinessRate t : rateList) {
                        //不是已办工商的，算在及时里
                        if ("1".equals(t.getJbSfybgs())) {
                            //根据BUSINESS_INFO_ID筛选国资委时间
                            Optional<TimelinessRate> op = dateList.stream().filter(d -> d.getBusinessInfoId()
                                    .equals(t.getBusinessInfoId())).findFirst();
                            if (op.isPresent()) {
                                if (op.get().getAfProcessDate() != null && t.getJbGsdjrq() != null) {
                                    //一级企业审核通过的时间间隔在30天内
                                    long differTime = op.get().getAfProcessDate().getTime() - t.getJbGsdjrq().getTime();
                                    if ((differTime / (1000 * 3600 * 24)) <= 30) {
                                        tl++;
                                    }
                                }
                                all++;
                            } else {  //未到国资委的
                                long differTime = nowDate.getTime() - t.getJbGsdjrq().getTime();
                                if ((differTime / (1000 * 3600 * 24)) > 30) {  //30天内不计入登记数量，30天外算不及时
                                    all++;
                                }
                            }
                        } else {  //已办工商不为是的，直接算及时
                            all++;
                            tl++;
                        }
                    }
                    ssqykh.setRegisterNum(all);
                    ssqykh.setInTimeRegisterNum(tl);
                }
                //退回
                //将所有BUSINESS_INFO_ID拉出来查询
                List<TimelinessRate> infoIdList = cockpitMapper.queryAllSubmitList(year);
                List<String> idsList = infoIdList.stream().map(TimelinessRate::getBusinessInfoId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(idsList)) {
                    //总退回次数
                    List<String> timelinessRates = cockpitMapper.queryAllReturnList(idsList, year);
                    //按一级企业分组
                    for (CockpitSSQYKH ssqykh : ssqykhsl) {
                        //提交次数
                        long submitRegisterNum = infoIdList.stream().filter(attr -> ssqykh.getUnitId().contains(attr.getParents())).count();
                        //退回次数
                        long returnRegisterNum = timelinessRates.stream().filter(a -> ssqykh.getUnitId().contains(a)).count();
                        ssqykh.setSubmitRegisterNum(submitRegisterNum);
                        ssqykh.setReturnRegisterNum(returnRegisterNum);
                    }
                }

            }
            //汇总
            long registerNum = 0;
            long inTimeRegisterNum = 0;
            long submitRegisterNum = 0;
            long returnRegisterNum = 0;
            for (CockpitSSQYKH cockpitSSQYKH : ssqykhsl) {
                registerNum += cockpitSSQYKH.getRegisterNum();
                inTimeRegisterNum += cockpitSSQYKH.getInTimeRegisterNum();
                submitRegisterNum += cockpitSSQYKH.getSubmitRegisterNum();
                returnRegisterNum += cockpitSSQYKH.getReturnRegisterNum();
                String unitName = cockpitSSQYKH.getUnitName();
                if (!StringUtils.isBlank(unitName)) {
                    cockpitSSQYKH.setUnitName(unitName
                            .replaceFirst("浙江省", "")
                            .replaceFirst("股份有限公司", "")
                            .replaceFirst("有限公司", ""));
                }
            }
            CockpitSSQYKH cockpitSSQYKH = new CockpitSSQYKH();
            cockpitSSQYKH.setRegisterNum(registerNum);
            cockpitSSQYKH.setInTimeRegisterNum(inTimeRegisterNum);
            cockpitSSQYKH.setSubmitRegisterNum(submitRegisterNum);
            cockpitSSQYKH.setReturnRegisterNum(returnRegisterNum);
            cockpitSSQYKH.setUnitName("浙江省汇总");
            ssqykhsl.add(cockpitSSQYKH);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("省属企业考核数量:报错"+e);
        }
        return ssqykhsl;
    }
    
    
    
    
    
    // ========================== 优化 ===============================


    
    
    
    
    
    /**
     * @description 省地市企业户数统计（企业类别），优化了查询性能
     * <AUTHOR>
     * @date 2025/7/26 09:43
     */
    @Override
    public Map<String, Object> qylbPrefectureAndCityV2() {
        return prefectureAndCityV2(cockpitMapper.qylbPrefectureAndCityV2(), (data) -> {
            if (StrUtil.isBlank(data.getQylb())) return null;
            QylbEnum qylbEnum = QylbEnum.getByValue(data.getQylb());
            return qylbEnum == null ? null : qylbEnum.getText();
        });
    }

    /**
     * @description 省地市企业户数统计（组织形式），优化了查询性能
     * <AUTHOR>
     * @date 2025/7/26 09:43
     */
    @Override
    public Map<String, Object> zzxsPrefectureAndCityV2() {
        return prefectureAndCityV2(cockpitMapper.zzxsPrefectureAndCityV2(), (data) -> {
            if (StrUtil.isBlank(data.getZzxs())) return null;
            ZzxsEnum typeEnum = ZzxsEnum.getByValue(Integer.parseInt(data.getZzxs()) / 10 + "0");
            return typeEnum == null ? null : typeEnum.getText();
        });
    }

    /**
     * @description 省地市企业户数统计（企业级次），优化了查询性能
     * <AUTHOR>
     * @date 2025/7/26 09:43
     */
    @Override
    public Map<String, Object> qyjcPrefectureAndCityV2() {
        return prefectureAndCityV2(cockpitMapper.qyjcPrefectureAndCityV2(), (data) -> {
            if (StrUtil.isBlank(data.getQyjc())) return null;
            QyjcEnum typeEnum = QyjcEnum.getByValue(Integer.parseInt(data.getQyjc()));
            return typeEnum == null ? null : typeEnum.getText();
        });
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @description 省地市企业户数统计（企业类别），优化了查询性能
     * <AUTHOR>
     * @date 2025/7/26 09:43
     */
    private Map<String, Object> prefectureAndCityV2(List<CockpitProvinceData> baseData, Function<CockpitProvinceData, String> typeConvert) {
        Map<String, Integer> province = new HashMap<>();
        Map<String, Map<String, Object>> city = new HashMap<>();

        for (CockpitProvinceData pd : baseData) {
            // 参数校验
            if (pd == null || pd.getParents() == null) continue;
            ZJSCityIDEnum cityIDEnum = ZJSCityIDEnum.getByValue(pd.getParents());
            String type = typeConvert.apply(pd);
            if (cityIDEnum == null || StrUtil.isBlank(type)) continue;

            String cityName = cityIDEnum.getCityName();

            // 省
            province.put(type, province.getOrDefault(type, 0) + pd.getCount());

            // 地市
            Map<String, Object> cityMap = city.getOrDefault(cityName, new HashMap<>());
            cityMap.put(type, (Integer) cityMap.getOrDefault(type, 0) + pd.getCount());
            cityMap.put("cityName", cityName);
            city.put(cityName, cityMap);
        }

        HashMap<String, Object> res = new HashMap<>();
        res.put(provinceMapName, province);

        // 排序
        List<Map<String, Object>> cityList = Arrays.stream(ZJSCityIDEnum.values()).sorted(Comparator.comparingInt(ZJSCityIDEnum::getSort)).map(e -> city.get(e.getCityName())).filter(Objects::nonNull).collect(Collectors.toList());
        res.put(cityMapName, cityList);

        return res;
    }

    /**
     * 省属企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    @Override
    public Map<String, Object> qylbByOneLevelV2() {
        return oneLevelV2(cockpitMapper.qylbByOneLevelV2(), (data) -> {
            if (StrUtil.isBlank(data.getQylb())) return null;
            QylbEnum qylbEnum = QylbEnum.getByValue(data.getQylb());
            return qylbEnum == null ? null : qylbEnum.getText();
        });
    }

    /**
     * 省属企业户数统计-企业户数图-按组织形式 性能优化
     * <AUTHOR>
     */
    @Override
    public Map<String, Object> zzxsByOneLevelV2() {
        return oneLevelV2(cockpitMapper.zzxsByOneLevelV2(), (data) -> {
            if (StrUtil.isBlank(data.getZzxs())) return null;
            ZzxsEnum typeEnum = ZzxsEnum.getByValue(Integer.parseInt(data.getZzxs()) / 10 + "0");
            return typeEnum == null ? null : typeEnum.getText();
        });
    }

    /**
     * 省属企业户数统计-企业户数图-按企业级次 性能优化
     * <AUTHOR>
     */
    @Override
    public Map<String, Object> qyjcByOneLevelV2() {
        return oneLevelV2(cockpitMapper.qyjcByOneLevelV2(), (data) -> {
            if (StrUtil.isBlank(data.getQyjc())) return null;
            QyjcEnum typeEnum = QyjcEnum.getByValue(Integer.parseInt(data.getQyjc()));
            return typeEnum == null ? null : typeEnum.getText();
        });
    }

    /**
     * 省属企业户数统计-企业户数图-按类别 通用方法
     * <AUTHOR>
     */
    private Map<String, Object> oneLevelV2(List<CockpitProvinceData> list, Function<CockpitProvinceData, String> convert) {
        Map<String, Map<String, Object>> cityMap = new HashMap<>();

        for (CockpitProvinceData data : list) {

            String type = convert.apply(data);
            if (StrUtil.isBlank(type)) continue;

            Map<String, Object> typeMap = cityMap.computeIfAbsent(data.getQymc(), k -> new HashMap<>());
            typeMap.put(type, (Integer) typeMap.getOrDefault(type, 0) + data.getCount());
            typeMap.put("cityName", data.getQymc());
        }

        return MapUtil.<String, Object>builder(cityMapName, cityMap.values()).build();
    }

}


