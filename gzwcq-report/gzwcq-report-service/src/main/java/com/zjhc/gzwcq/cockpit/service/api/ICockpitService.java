package com.zjhc.gzwcq.cockpit.service.api;
import com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH;
import java.util.List;
import java.util.Map;

public interface ICockpitService {

    /**
     * 省属企业考核数量
     */
    List<CockpitSSQYKH> getSSQYKHSL(String year);
    /**
     * 省地市企业户数统计-企业户数图-按企业类别
     * @return
     */
    Map<String,Object> qylbPrefectureAndCity();

    /**
     * 省地市企业户数统计-企业户数图-按组织形式
     * @return
     */
    Map<String,Object> zzxsPrefectureAndCity();

    /**
     * 省地市企业户数统计-企业户数图-按企业级次
     * @return
     */
    Map<String,Object> qyjcPrefectureAndCity();

    /**
     * 省属企业户数统计-企业户数图-按企业类别
     * @return
     */
    Map<String,Object> qylbByOneLevel();

    /**
     * 省属企业户数统计-企业户数图-按组织形式
     * @return
     */
    Map<String,Object> zzxsByOneLevel();

    /**
     * 省属企业户数统计-企业户数图-按企业级次
     * @return
     */
    Map<String,Object> qyjcByOneLevel();

    /**
     * 省地市企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    Map<String, Object> qylbPrefectureAndCityV2();

    /**
     * 省地市企业户数统计-企业户数图-按企业类别 性能优化
     *
     * <AUTHOR>
     */
    Map<String, Object> zzxsPrefectureAndCityV2();

    /**
     * 省地市企业户数统计-企业户数图-按企业类别 性能优化
     *
     * <AUTHOR>
     */
    Map<String, Object> qyjcPrefectureAndCityV2();

    /**
     * 省属企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    Map<String, Object> qylbByOneLevelV2();
    
    /**
     * 省属企业户数统计-企业户数图-按组织形式 性能优化
     * <AUTHOR>
     */
    Map<String, Object> zzxsByOneLevelV2();
    
    /**
     * 省属企业户数统计-企业户数图-按企业级次 性能优化
     * <AUTHOR>
     */
    Map<String, Object> qyjcByOneLevelV2();
}
