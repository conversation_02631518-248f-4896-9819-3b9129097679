package com.zjhc.gzwcq.dataSearchTemp.service.api;

import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo;

import java.util.List;
import java.util.Map;

public interface IDataSearchTempService {

	//不保存的查询条件
	String[] UNSAVED_CONDITIONS = {"limit","pageNumber","timeFilter","JB_ZCRQ","queryMode"};

	//不回显的不是自己的公开的查询条件
	String[] NOT_RETURNED_PUBLIC_CONDITIONS = {"selectedOrganizationIds"};

	/**
	 * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
	 */
	void insert(DataSearchTemp dataSearchTemp);

	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKeys(Map<String, Object> map);
  
  	/**
	 * 按对象中的主键进行删除，
	 */
	void deleteByPrimaryKey(String id);
		
	/**
	 * 按对象中的主键进行所有非空属性的修改
	 */
	void updateIgnoreNull(DataSearchTemp dataSearchTemp);
	
	/**
	* 更新
	*/
	void update(DataSearchTemp dataSearchTemp);
	
	/**
	 *根据页面查询条件查询数据并分页
	 */
	List<DataSearchTempVo> queryDataSearchTempByPage(DataSearchTempParam dataSearchTempParam);
	
	/**
	 *分页查询总条数
	 */
	long queryTotalDataSearchTemps(DataSearchTempParam dataSearchTempParam);
  
	
	/**
	 *通过ID查询数据
	 */
	DataSearchTemp selectDataSearchTempByPrimaryKey(DataSearchTemp dataSearchTemp);
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	List<DataSearchTempVo> selectForList(DataSearchTempParam param);
	
	/**
	 * 数据唯一性验证
	 *<P>代码生成，必要时可以使用
	 * */
	boolean validateUniqueParam(DataSearchTemp dataSearchTemp);
	
	/**
	* 保存单个对象
	* <P>存在则更新，不存在则新增
	*/
	void saveOne(DataSearchTemp dataSearchTemp);
	
	/**
	* 保存多个对象
	*/
	void multipleSaveAndEdit(DataSearchTemp[] objs);

	/**
	 * 查询我的模板
	 */
	List<DataSearchTempVo> selectMyTemps(DataSearchTempParam param);

	/**
	 * 查询共享模板(不包括自己的)
	 */
	List<DataSearchTempVo> selectSharedTemps(DataSearchTempParam param);

	/**
	 * 移除查询条件tempJson中指定的字段
	 * @param temp 查询模板对象
	 * @param conditions 需要移除的字段值
	 */
	<T extends DataSearchTemp> void removeConditions(T temp, String... conditions);
}