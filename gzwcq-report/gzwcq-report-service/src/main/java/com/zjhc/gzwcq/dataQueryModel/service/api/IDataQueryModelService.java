package com.zjhc.gzwcq.dataQueryModel.service.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.dict.model.Dictionary;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Set;

public interface IDataQueryModelService {

    /**
     * 数据查询_分页查询
     */
    BootstrapTableModel<DataQueryModelVo> queryByPage(DataQueryModelParam param) throws IllegalAccessException;

    /**
     * 数据查询_不分页查询
     */
    List<DataQueryModelVo> selectForList(DataQueryModelParam param) throws IllegalAccessException;

    /**
     * 经济行为分析_结果页
     */
    List<DataQueryModelVo> economicAnalysis(DataQueryModelParam param) throws IllegalAccessException;

    /**
     * 经济行为分析_明细页
     */
    BootstrapTableModel<DataQueryModelVo> economicDetail(DataQueryModelParam param) throws IllegalAccessException;

    List<DataQueryModelVo> selectForDetailList(DataQueryModelParam param) throws IllegalAccessException;

    /**
     * 产权树导出
     */
    ResponseEntity<byte[]> treeExport(DataQueryModelParam param);
    
    public void getValsByTypeCodeAndParent(Set<String> childrenVals, String typeCode,long parent);
    
    public void paramTransfer(DataQueryModelParam param);
}
