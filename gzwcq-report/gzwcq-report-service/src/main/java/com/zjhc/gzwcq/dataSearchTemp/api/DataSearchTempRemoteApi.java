package com.zjhc.gzwcq.dataSearchTemp.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.dataSearchTemp.client.DataSearchTempFeignClient;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo;
import com.zjhc.gzwcq.dataSearchTemp.service.api.IDataSearchTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/dataSearchTempRemoteApi")
@Api(value = "dataSearchTemp接口文档", tags = "数据查询模板")
public class DataSearchTempRemoteApi implements DataSearchTempFeignClient {

    @Autowired
    private IDataSearchTempService dataSearchTempService;

    /**
     * 分页查询列表
     */
    @Override
    @ApiOperation(value = "分页查询列表")
    public BootstrapTableModel<DataSearchTempVo> queryByPage(@RequestBody DataSearchTempParam dataSearchTempParam) {
        BootstrapTableModel<DataSearchTempVo> model = new BootstrapTableModel<>();
        model.setRows(dataSearchTempService.queryDataSearchTempByPage(dataSearchTempParam));
        model.setTotal(dataSearchTempService.queryTotalDataSearchTemps(dataSearchTempParam));
        return model;
    }

    /**
     * 将对象保存，返回该条记录的操作数量，保存成功之后，将主键填充到参数对象中
     */
    @Override
    @ApiOperation(value = "新增")
    public void insert(@RequestBody DataSearchTemp dataSearchTemp) {
        dataSearchTempService.insert(dataSearchTemp);
    }

    /**
     * 按对象中的主键进行删除，
     */
    @Override
    @ApiOperation(value = "批量删除")
    public void deleteByPrimaryKeys(@RequestBody Map<String, Object> map) {
        dataSearchTempService.deleteByPrimaryKeys(map);
    }

    /**
     * 按对象中的主键进行删除，
     */
    @Override
    @ApiOperation(value = "单条删除")
    public void deleteByPrimaryKey(@RequestParam("id") String id) {
        dataSearchTempService.deleteByPrimaryKey(id);
    }

    /**
     * 按对象中的主键进行所有非空属性的修改
     */
    @Override
    @ApiOperation(value = "更新忽略空字段")
    public void updateIgnoreNull(@RequestBody DataSearchTemp dataSearchTemp) {
        dataSearchTempService.updateIgnoreNull(dataSearchTemp);
    }

    /**
     * 更新
     */
    @Override
    @ApiOperation(value = "更新全部字段")
    public void update(@RequestBody DataSearchTemp dataSearchTemp) {
        dataSearchTempService.update(dataSearchTemp);
    }

    /**
     * 通过ID查询数据
     */
    @Override
    @ApiOperation(value = "根据主键查询")
    public DataSearchTemp selectDataSearchTempByPrimaryKey(@RequestBody DataSearchTemp dataSearchTemp) {
        return dataSearchTempService.selectDataSearchTempByPrimaryKey(dataSearchTemp);
    }

    /**
     * 根据部分属性对象查询全部结果，不分页
     */
    @Override
    @ApiOperation(value = "查询列表")
    public List<DataSearchTempVo> selectForList(@RequestBody DataSearchTempParam param) {
        return dataSearchTempService.selectForList(param);
    }

    /**
     * 数据唯一性验证
     * <P>代码生成，必要时可以使用
     */
    @Override
    @ApiOperation(value = "唯一性校验")
    public boolean validateUniqueParam(@RequestBody DataSearchTemp dataSearchTemp) {
        return dataSearchTempService.validateUniqueParam(dataSearchTemp);
    }

    /**
     * 保存单个对象
     * <P>存在则更新，不存在则新增
     */
    @Override
    @ApiOperation(value = "插入更新")
    public void saveOne(@RequestBody DataSearchTemp dataSearchTemp) {
        dataSearchTempService.saveOne(dataSearchTemp);
    }

    /**
     * 保存多个对象
     */
    @Override
    @ApiOperation(value = "批量插入更新")
    public void multipleSaveAndEdit(@RequestBody DataSearchTemp[] objs) {
        dataSearchTempService.multipleSaveAndEdit(objs);
    }

    /**
     * 查询我的模板
     */
    @Override
    @ApiOperation(value = "查询我的模板")
    public List<DataSearchTempVo> selectMyTemps(@RequestBody DataSearchTempParam param) {
        return dataSearchTempService.selectMyTemps(param);
    }

    /**
     * 查询共享模板(不包括自己的)
     */
    @Override
    @ApiOperation(value = "查询共享模板(不包括自己的)")
    public List<DataSearchTempVo> selectSharedTemps(@RequestBody DataSearchTempParam param) {
        return dataSearchTempService.selectSharedTemps(param);
    }
}