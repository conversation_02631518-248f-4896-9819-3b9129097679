package com.zjhc.gzwcq.cockpit.api;

import com.zjhc.gzwcq.cockpit.client.CockpitClient;
import com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH;
import com.zjhc.gzwcq.cockpit.service.api.ICockpitService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value="/cockpitRemoteApi")
public class CockpitRemoteApi implements CockpitClient {

    @Resource
    public ICockpitService cockpitService;

    /**
     * 省属企业考核数量
     */
    @Override
    public List<CockpitSSQYKH> getSSQYKHSL(String year) {
        return cockpitService.getSSQYKHSL(year);
    }

    @Override
    public Map<String,Object> qylbPrefectureAndCity(){
        return cockpitService.qylbPrefectureAndCityV2();
    }

    @Override
    public Map<String,Object> zzxsPrefectureAndCity(){
        return cockpitService.zzxsPrefectureAndCityV2();
    }

    @Override
    public Map<String,Object> qyjcPrefectureAndCity(){
        return cockpitService.qyjcPrefectureAndCityV2();
    }

    /**
     * 省属企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    @Override
    public Map<String,Object> qylbByOneLevel(){
        return cockpitService.qylbByOneLevelV2();
    }

    /**
     * 省属企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    @Override
    public Map<String,Object> zzxsByOneLevel(){
        return cockpitService.zzxsByOneLevelV2();
    }

    /**
     * 省属企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    @Override
    public Map<String,Object> qyjcByOneLevel(){
        return cockpitService.qyjcByOneLevelV2();
    }

}
