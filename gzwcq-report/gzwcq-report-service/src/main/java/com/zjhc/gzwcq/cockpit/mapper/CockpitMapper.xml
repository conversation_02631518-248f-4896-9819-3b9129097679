<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.cockpit.mapper.CockpitMapper">

    <select id="qylbPrefectureAndCity" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
        select a.ORGANIZATION_ID as unitId,b.JB_QYL<PERSON> as qylb,left(a.PARENTS, 65) as parents
        from sys_organization a
        LEFT JOIN
         (
         select x.UNITID,x.JB_QYLB from (
				select a.UNITID,a.JB_QYLB from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
         ) b on b.UNITID = a.ORGANIZATION_ID
        where FIND_IN_SET('39DC82B5A000000125E54F37FE103416',a.PARENTS)
        and a.ORGANIZATION_ID != '39DC82B5A000000125E54F37FE103416'
        and a.isdeleted = 'N'
        group by a.ORGANIZATION_ID
    </select>

    <select id="zzxsPrefectureAndCity" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
        select a.ORGANIZATION_ID as unitId,b.JB_ZZXS as zzxs, left(a.PARENTS, 65) as parents
        from sys_organization a
        LEFT JOIN
         (
         select x.UNITID,x.JB_ZZXS from (
				select a.UNITID,a.JB_ZZXS from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
         ) b on b.UNITID = a.ORGANIZATION_ID
        where FIND_IN_SET('39DC82B5A000000125E54F37FE103416',a.PARENTS)
        and a.ORGANIZATION_ID != '39DC82B5A000000125E54F37FE103416'
        and a.isdeleted = 'N'
        group by a.ORGANIZATION_ID
    </select>

    <select id="qyjcPrefectureAndCity" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
        select a.ORGANIZATION_ID as unitId,b.JB_QYJC as qyjc,left(a.PARENTS, 65) as parents
        from sys_organization a
        LEFT JOIN
         (
         select x.UNITID,x.JB_QYJC from (
				select a.UNITID,a.JB_QYJC from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
         ) b on b.UNITID = a.ORGANIZATION_ID
        where FIND_IN_SET('39DC82B5A000000125E54F37FE103416',a.PARENTS)
        and a.ORGANIZATION_ID != '39DC82B5A000000125E54F37FE103416'
        and a.isdeleted = 'N'
        group by a.ORGANIZATION_ID
    </select>

    <select id="queryOneLevelOrg" resultType="com.zjhc.gzwcq.cockpit.entity.OneLevelOrg">
        select a.ORGANIZATION_ID as orgId,a.ORGANIZATION_NAME as OrgName
        from sys_organization a
				where a.isdeleted = 'N'
				and a.BUSINESS_LEVEL = '01'
        and FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',a.PARENTS)
				GROUP BY a.ORGANIZATION_ID
union all
        select a.ORGANIZATION_ID as orgId,a.ORGANIZATION_NAME as OrgName
        from sys_organization a
				where a.isdeleted = 'N'
        and FIND_IN_SET('53129A8420000021177AD54D2465FF2F',a.PARENTS)
				and a.BUSINESS_LEVEL = '01'
				GROUP BY a.ORGANIZATION_ID
    </select>

    <select id="qylbByOneLevel" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
        select a.PARENTS as parents, b.JB_QYLB as qylb
        from sys_organization a
				LEFT JOIN (
				select x.UNITID,x.JB_QYLB from (
				select a.UNITID,a.JB_QYLB from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
				) b on a.ORGANIZATION_ID = b.UNITID
				where a.isdeleted = 'N'
				and a.BUSINESS_LEVEL != '01'
        and FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',a.PARENTS)
				GROUP BY a.ORGANIZATION_ID
union all
select a.PARENTS as parents, b.JB_QYLB as qylb
        from sys_organization a
				LEFT JOIN (
				select x.UNITID,x.JB_QYLB from (
				select a.UNITID,a.JB_QYLB from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
				) b on a.ORGANIZATION_ID = b.UNITID
				where a.isdeleted = 'N'
				and a.BUSINESS_LEVEL != '01'
        and FIND_IN_SET('53129A8420000021177AD54D2465FF2F',a.PARENTS)
				GROUP BY a.ORGANIZATION_ID
    </select>

    <select id="zzxsByOneLevel" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		select a.PARENTS as parents, b.JB_ZZXS as zzxs
        from sys_organization a
				LEFT JOIN (
				select x.UNITID,x.JB_ZZXS from (
				select a.UNITID,a.JB_ZZXS from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
				) b on a.ORGANIZATION_ID = b.UNITID
				where a.isdeleted = 'N'
				and a.BUSINESS_LEVEL != '01'
        and FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',a.PARENTS)
				GROUP BY a.ORGANIZATION_ID
union all
select a.PARENTS as parents, b.JB_ZZXS as zzxs
        from sys_organization a
				LEFT JOIN (
				select x.UNITID,x.JB_ZZXS from (
				select a.UNITID,a.JB_ZZXS from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
				) b on a.ORGANIZATION_ID = b.UNITID
				where a.isdeleted = 'N'
				and a.BUSINESS_LEVEL != '01'
        and FIND_IN_SET('53129A8420000021177AD54D2465FF2F',a.PARENTS)
				GROUP BY a.ORGANIZATION_ID
    </select>

    <select id="qyjcByOneLevel" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		select a.PARENTS as parents, b.JB_QYJC as qyjc
        from sys_organization a
				LEFT JOIN (
				select x.UNITID,x.JB_QYJC from (
				select a.UNITID,a.JB_QYJC from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
				) b on a.ORGANIZATION_ID = b.UNITID
				where a.isdeleted = 'N'
				and a.BUSINESS_LEVEL != '01'
        and FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',a.PARENTS)
				GROUP BY a.ORGANIZATION_ID
union all
select a.PARENTS as parents, b.JB_QYJC as zzxs
        from sys_organization a
				LEFT JOIN (
				select x.UNITID,x.JB_QYJC from (
				select a.UNITID,a.JB_QYJC from view_cq_jbxxb_noDelete a
				LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
				where a.JB_SHZT = '4'
				ORDER BY b.RG_TIMEMARK desc
				limit 10000000) x
				GROUP BY x.UNITID
				) b on a.ORGANIZATION_ID = b.UNITID
				where a.isdeleted = 'N'
				and a.BUSINESS_LEVEL != '01'
        and FIND_IN_SET('53129A8420000021177AD54D2465FF2F',a.PARENTS)
				GROUP BY a.ORGANIZATION_ID
    </select>

    <select id="getSSQYKHSL" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH">
        select ORGANIZATION_ID unitId,ORGANIZATION_NAME unitName from sys_organization
        where isdeleted = 'N' and  BUSINESS_LEVEL = '01'
          and ( FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',PARENTS) or
                FIND_IN_SET('53129A8420000021177AD54D2465FF2F',PARENTS) )

    </select>

    <select id="queryTimelinessRateList" resultType="com.zjhc.gzwcq.screen.entity.TimelinessRate">
		select a.BUSINESS_INFO_ID as businessInfoId,
		c.JB_SFYBGS as jbSfybgs, c.jb_gsdjrq as jbGsdjrq,a.PARENTS
		from (
		select n.* from (
		select a.CREATE_TIME,a.BUSINESS_INFO_ID,a.AF_PROCESSUNITID,d.PARENTS from rg_auditflow_history a
		LEFT JOIN sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where a.AF_PROCESSTYPE = '3'  and (FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',PARENTS) or
		FIND_IN_SET('53129A8420000021177AD54D2465FF2F',PARENTS) )
		<choose>
			<when test="year != null and year != ''">
				and YEAR(a.CREATE_TIME) = #{year}
			</when>
			<otherwise>
				and YEAR(a.CREATE_TIME) = YEAR(NOW())
			</otherwise>
		</choose>
		ORDER BY CREATE_TIME limit 10000000
		) n GROUP BY n.BUSINESS_INFO_ID
		) a
		LEFT JOIN rg_business_info b on a.BUSINESS_INFO_ID = b.id
		left JOIN view_cq_jbxxb_noDelete c on b.JBXX_ID = c.id
		LEFT JOIN sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where c.BUSINESS_NATURE = '1'
    </select>

    <select id="queryAllApprovalDate" resultType="com.zjhc.gzwcq.screen.entity.TimelinessRate">
		select z.BUSINESS_INFO_ID as businessInfoId,
		z.CREATE_TIME as afProcessDate,z.PARENTS
		from(
		select a.BUSINESS_INFO_ID,a.CREATE_TIME,b.PARENTS from rg_auditflow_history a
		LEFT JOIN sys_organization b on a.AF_PROCESSUNITID = b.ORGANIZATION_ID
		where b.BUSINESSTYPE = '1'
		and a.BUSINESS_INFO_ID in
		<foreach collection="list" separator="," open="(" close=")" index="i" item="item">
			#{item}
		</foreach>
		ORDER BY a.CREATE_TIME limit 1000000) z
		GROUP BY z.BUSINESS_INFO_ID
	</select>

    <select id="queryAllSubmitList" resultType="com.zjhc.gzwcq.screen.entity.TimelinessRate">
		select a.BUSINESS_INFO_ID businessInfoId,d.PARENTS
		from rg_auditflow_history a
		left JOIN (
		select UNITID,BUSINESS_NATURE from view_cq_jbxxb_noDelete
		GROUP BY UNITID
		) c on a.AF_UNITID = c.UNITID
		LEFT JOIN sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where a.AF_PROCESSTYPE = '3'
		and c.BUSINESS_NATURE = '1' and ( FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',PARENTS) or
		FIND_IN_SET('53129A8420000021177AD54D2465FF2F',PARENTS) )
		<choose>
			<when test="year != null and year != ''">
				and YEAR(a.CREATE_TIME) = #{year}
			</when>
			<otherwise>
				and YEAR(a.CREATE_TIME) = YEAR(NOW())
			</otherwise>
		</choose>
	</select>

    <select id="queryAllReturnList" resultType="java.lang.String">
		select so.PARENTS from (select a.BUSINESS_INFO_ID
		from rg_auditflow_history a
		left join sys_organization d on a.AF_PROCESSUNITID = d.ORGANIZATION_ID
		where a.AF_PROCESSTYPE = '2'
		and d.BUSINESSTYPE = '1'
		and a.BUSINESS_INFO_ID in
		<foreach collection="list" separator="," open="(" close=")" index="i" item="item">
			#{item}
		</foreach>
		<choose>
			<when test="year != null and year != ''">
				and YEAR(a.CREATE_TIME) = #{year}
			</when>
			<otherwise>
				and YEAR(a.CREATE_TIME) = YEAR(NOW())
			</otherwise>
		</choose>) t
		left join (select BUSINESS_INFO_ID,AF_PROCESSUNITID from rg_auditflow_history where AF_PROCESSTYPE = '3' group by BUSINESS_INFO_ID)  r on r.BUSINESS_INFO_ID = t.BUSINESS_INFO_ID
		left join sys_organization so on r.AF_PROCESSUNITID = so.ORGANIZATION_ID and so.isdeleted = 'N'
	</select>
	

    <select id="qylbPrefectureAndCityV2" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		SELECT 
			parents,
			qylb,
			COUNT(*) AS count
		FROM (
			SELECT
				a.ORGANIZATION_ID AS unitId,
				b.JB_QYLB AS qylb,
				SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 2), ',', -1) as parents
			FROM sys_organization a
			JOIN (
				SELECT x.UNITID, x.JB_QYLB
				FROM (
					SELECT a.UNITID, a.JB_QYLB
					FROM view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b ON a.id = b.JBXX_ID
					WHERE a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK DESC
					LIMIT 10000000
				) x
				GROUP BY x.UNITID
			) b ON b.UNITID = a.ORGANIZATION_ID and b.JB_QYLB is not null
			WHERE FIND_IN_SET('39DC82B5A000000125E54F37FE103416', a.PARENTS)
			  AND a.ORGANIZATION_ID != '39DC82B5A000000125E54F37FE103416'
			  AND a.isdeleted = 'N'
		) t
		GROUP BY parents, qylb
    </select>
	
	
    <select id="zzxsPrefectureAndCityV2" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		SELECT 
			parents,
			zzxs,
			COUNT(*) AS count
		FROM (
			SELECT
				a.ORGANIZATION_ID AS unitId,
				b.JB_ZZXS AS zzxs,
				SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 2), ',', -1) as parents
			FROM sys_organization a
			JOIN (
				SELECT x.UNITID, x.JB_ZZXS
				FROM (
					SELECT a.UNITID, a.JB_ZZXS
					FROM view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b ON a.id = b.JBXX_ID
					WHERE a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK DESC
					LIMIT 10000000
				) x
				GROUP BY x.UNITID
			) b ON b.UNITID = a.ORGANIZATION_ID and b.JB_ZZXS is not null
			WHERE FIND_IN_SET('39DC82B5A000000125E54F37FE103416', a.PARENTS)
			  AND a.ORGANIZATION_ID != '39DC82B5A000000125E54F37FE103416'
			  AND a.isdeleted = 'N'
		) t
		GROUP BY parents, zzxs
    </select>
	
	
    <select id="qyjcPrefectureAndCityV2" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		SELECT 
			parents,
			qyjc,
			COUNT(*) AS count
		FROM (
			SELECT
				a.ORGANIZATION_ID AS unitId,
				b.JB_QYJC AS qyjc,
				SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 2), ',', -1) as parents
			FROM sys_organization a
			JOIN (
				SELECT x.UNITID, x.JB_QYJC
				FROM (
					SELECT a.UNITID, a.JB_QYJC
					FROM view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b ON a.id = b.JBXX_ID
					WHERE a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK DESC
					LIMIT 10000000
				) x
				GROUP BY x.UNITID
			) b ON b.UNITID = a.ORGANIZATION_ID and b.JB_QYJC is not null
			WHERE FIND_IN_SET('39DC82B5A000000125E54F37FE103416', a.PARENTS)
			  AND a.ORGANIZATION_ID != '39DC82B5A000000125E54F37FE103416'
			  AND a.isdeleted = 'N'
		) t
		GROUP BY parents, qyjc
    </select>
	
	

    <select id="qylbByOneLevelV2" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		select
			t.qylb, count(*) as count,replace(replace(o.ORGANIZATION_NAME, '浙江省', ''), '有限公司', '') as qymc
		from (
			select SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 3), ',', -1) AS parents, b.JB_QYLB as qylb
			from sys_organization a
					LEFT JOIN (
					select x.UNITID,x.JB_QYLB from (
					select a.UNITID,a.JB_QYLB from view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
					where a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK desc
					limit 10000000) x
					GROUP BY x.UNITID
					) b on a.ORGANIZATION_ID = b.UNITID
					where a.isdeleted = 'N'
					and a.BUSINESS_LEVEL != '01' and JB_QYLB is not null
			and (FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',a.PARENTS) or FIND_IN_SET('53129A8420000021177AD54D2465FF2F',a.PARENTS))
			union all
			-- 综合资产经营公司托管节点
			select SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 4), ',', -1) AS parents, b.JB_QYLB as qylb
			from sys_organization a
					LEFT JOIN (
					select x.UNITID,x.JB_QYLB from (
					select a.UNITID,a.JB_QYLB from view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
					where a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK desc
					limit 10000000) x
					GROUP BY x.UNITID
					) b on a.ORGANIZATION_ID = b.UNITID
					where a.isdeleted = 'N'
					and a.BUSINESS_LEVEL != '01' and b.JB_QYLB is not null
			and FIND_IN_SET('3B123053C0000001B43B17219C8B258D',a.PARENTS)
		) t
		inner join sys_organization o on o.ORGANIZATION_ID = t.PARENTS
		where o.ORGANIZATION_ID != '3B123053C0000001B43B17219C8B258D'
		GROUP BY t.parents, t.qylb
    </select>

    <select id="zzxsByOneLevelV2" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		select
			t.zzxs, count(*) as count, replace(replace(o.ORGANIZATION_NAME, '浙江省', ''), '有限公司', '') as qymc
		from (
			select SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 3), ',', -1) AS parents, b.JB_ZZXS as zzxs
			from sys_organization a
					LEFT JOIN (
					select x.UNITID,x.JB_ZZXS from (
					select a.UNITID,a.JB_ZZXS from view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
					where a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK desc
					limit 10000000) x
					GROUP BY x.UNITID
					) b on a.ORGANIZATION_ID = b.UNITID
					where a.isdeleted = 'N'
					and a.BUSINESS_LEVEL != '01' and JB_ZZXS is not null
			and (FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',a.PARENTS) or FIND_IN_SET('53129A8420000021177AD54D2465FF2F',a.PARENTS))
			union all
			-- 综合资产经营公司托管节点
			select SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 4), ',', -1) AS parents, b.JB_ZZXS as zzxs
			from sys_organization a
					LEFT JOIN (
					select x.UNITID,x.JB_ZZXS from (
					select a.UNITID,a.JB_ZZXS from view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
					where a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK desc
					limit 10000000) x
					GROUP BY x.UNITID
					) b on a.ORGANIZATION_ID = b.UNITID
					where a.isdeleted = 'N'
					and a.BUSINESS_LEVEL != '01' and b.JB_ZZXS is not null
			and FIND_IN_SET('3B123053C0000001B43B17219C8B258D',a.PARENTS)
		) t
		inner join sys_organization o on o.ORGANIZATION_ID = t.PARENTS
		where o.ORGANIZATION_ID != '3B123053C0000001B43B17219C8B258D'
		GROUP BY t.parents, t.zzxs
    </select>

    <select id="qyjcByOneLevelV2" resultType="com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData">
		select
			t.qyjc, count(*) as count, replace(replace(o.ORGANIZATION_NAME, '浙江省', ''), '有限公司', '') as qymc
		from (
			select SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 3), ',', -1) AS parents, b.JB_QYJC as qyjc
			from sys_organization a
					LEFT JOIN (
					select x.UNITID,x.JB_QYJC from (
					select a.UNITID,a.JB_QYJC from view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
					where a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK desc
					limit 10000000) x
					GROUP BY x.UNITID
					) b on a.ORGANIZATION_ID = b.UNITID
					where a.isdeleted = 'N'
					and a.BUSINESS_LEVEL != '01' and JB_QYJC is not null
			and (FIND_IN_SET('39DC82B5C0000021A568D4D612672F5A',a.PARENTS) or FIND_IN_SET('53129A8420000021177AD54D2465FF2F',a.PARENTS))
			union all
			-- 综合资产经营公司托管节点
			select SUBSTRING_INDEX(SUBSTRING_INDEX(a.PARENTS, ',', 4), ',', -1) AS parents, b.JB_QYJC as qyjc
			from sys_organization a
					LEFT JOIN (
					select x.UNITID,x.JB_QYJC from (
					select a.UNITID,a.JB_QYJC from view_cq_jbxxb_noDelete a
					LEFT JOIN rg_business_info b on a.id = b.JBXX_ID
					where a.JB_SHZT = '4'
					ORDER BY b.RG_TIMEMARK desc
					limit 10000000) x
					GROUP BY x.UNITID
					) b on a.ORGANIZATION_ID = b.UNITID
					where a.isdeleted = 'N'
					and a.BUSINESS_LEVEL != '01' and b.JB_QYJC is not null
			and FIND_IN_SET('3B123053C0000001B43B17219C8B258D',a.PARENTS)
		) t
		inner join sys_organization o on o.ORGANIZATION_ID = t.PARENTS
		where o.ORGANIZATION_ID != '3B123053C0000001B43B17219C8B258D'
		GROUP BY t.parents, t.qyjc
    </select>

	
</mapper>