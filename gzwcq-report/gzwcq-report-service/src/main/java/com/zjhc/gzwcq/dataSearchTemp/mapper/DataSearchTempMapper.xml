<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhc.gzwcq.dataSearchTemp.mapper.IDataSearchTempMapper">

	<resultMap type="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp" id="baseResultMap">
		<id column="id" property="id"/>
		<result column="name" property="name"/>
		<result column="descript" property="descript"/>
		<result column="share" property="share"/>
		<result column="temp_json" property="tempJson"/>
		<result column="create_user" property="createUser"/>
		<result column="create_time" property="createTime"/>
		<result column="last_update_user" property="lastUpdateUser"/>
		<result column="last_update_time" property="lastUpdateTime"/>
	</resultMap>
	
	<resultMap id="baseResultMapExt" type="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo" extends="baseResultMap">
	</resultMap>

	<sql id="columns">
		id, 
		name, 
		descript, 
		share, 
		temp_json, 
		create_user, 
		create_time, 
		last_update_user, 
		last_update_time
	</sql>
	
	<!--带别名的列-->
	<sql id="columnsAlias">
		t.id, 
		t.name, 
		t.descript, 
		t.share, 
		t.temp_json, 
		t.create_user, 
		t.create_time, 
		t.last_update_user, 
		t.last_update_time
	</sql>
	
	<sql id="vals">
		#{id}, 
		#{name}, 
		#{descript}, 
		#{share}, 
		#{tempJson}, 
		#{createUser}, 
		#{createTime}, 
		#{lastUpdateUser}, 
		#{lastUpdateTime}
	</sql>
		
	<!-- 给where查询的表起别名t,方便多表关联查询 -->
	<sql id="whereSql">
		<if test="id != null and id != ''">
			and t.id = #{id}
		</if>
		<if test="name != null and name != ''">
			and t.name = #{name}
		</if>
		<if test="descript != null and descript != ''">
			and t.descript = #{descript}
		</if>
		<if test="share != null">
			and t.share = #{share}
		</if>
		<if test="tempJson != null and tempJson != ''">
			and t.temp_json = #{tempJson}
		</if>
		<if test="createUser != null and createUser != ''">
			and t.create_user = #{createUser}
		</if>
		<if test="createTime != null">
			and t.create_time = #{createTime}
		</if>
		<if test="lastUpdateUser != null and lastUpdateUser != ''">
			and t.last_update_user = #{lastUpdateUser}
		</if>
		<if test="lastUpdateTime != null">
			and t.last_update_time = #{lastUpdateTime}
		</if>
	</sql>
	
	

	<insert id="insert" parameterType="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp" useGeneratedKeys="true" keyProperty="id">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(), '-', '') as id from dual
        </selectKey>
		insert into cq_data_search_temp (<include refid="columns" />) 
		values (<include refid="vals" />)
	</insert>
	
    <!-- 逻辑删除 -->
	<update id="logicDeleteByPrimaryKeys" parameterType="java.util.Map">
		update cq_data_search_temp set isDeleted = 'Y' where
		id in
		<foreach collection="dataSearchTemps" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</update>
	
	<update id="logicDeleteByPrimaryKey" parameterType="java.util.Map">
		update cq_data_search_temp set isDeleted = 'Y' where id = #{id}
	</update>
	
	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeys" parameterType="java.util.Map">
		delete from cq_data_search_temp  where
		id in
		<foreach collection="dataSearchTemps" open="(" close=")" separator="," item="id">
		    #{id}
		</foreach>
	</delete>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from cq_data_search_temp  where id = #{id}
	</delete>
	
	<select id="selectDataSearchTempByPrimaryKey" resultMap="baseResultMapExt">
		select 
		<include refid="columns"/>
		from cq_data_search_temp
		where id=#{id}
	</select>

	<update id="updateIgnoreNull">
		update cq_data_search_temp
		<set>
			<if test="name != null">
				name=#{name},
			</if>
			<if test="descript != null">
				descript=#{descript},
			</if>
			<if test="share != null">
				share=#{share},
			</if>
			<if test="tempJson != null">
				temp_json=#{tempJson},
			</if>
			<if test="createUser != null">
				create_user=#{createUser},
			</if>
			<if test="createTime != null">
				create_time=#{createTime},
			</if>
			<if test="lastUpdateUser != null">
				last_update_user=#{lastUpdateUser},
			</if>
			<if test="lastUpdateTime != null">
				last_update_time=#{lastUpdateTime}
			</if>
		</set>
		where id=#{id}
	</update>
	
	<!-- 更新 -->
	<update id="update">
		update cq_data_search_temp
		<set>
			name=#{name},
			descript=#{descript},
			share=#{share},
			temp_json=#{tempJson},
			create_user=#{createUser},
			create_time=#{createTime},
			last_update_user=#{lastUpdateUser},
			last_update_time=#{lastUpdateTime}
		</set>
		where id=#{id}
	</update>
	
	
	<!-- 根据部分属性对象查询全部结果，不分页 -->
	<select id="selectForList" parameterType="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_data_search_temp t
		where 1 = 1
		<include refid="whereSql" />
	</select>	

	<select id="queryTotalDataSearchTemps" parameterType="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam" resultType="java.lang.Long">
		select
			count(id)
		from cq_data_search_temp t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 列表页查询 -->
	<select id="queryDataSearchTempForList" parameterType="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam" resultMap="baseResultMapExt">
		select
		<include refid="columnsAlias"/>
		from
			cq_data_search_temp t
		where 1=1
		<include refid="whereSql" />
	</select>
	
	<!-- 根据唯一性参数查询数据 -->
	<select id="selectForUnique" parameterType="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp" resultMap="baseResultMapExt">
		select <include refid="columnsAlias"/> from cq_data_search_temp t
		where t.id != #{id}
			<if test="name != null and name != ''">
				and t.name = #{name}
			</if>
			<if test="descript != null and descript != ''">
				and t.descript = #{descript}
			</if>
			<if test="share != null">
				and t.share = #{share}
			</if>
			<if test="tempJson != null and tempJson != ''">
				and t.temp_json = #{tempJson}
			</if>
			<if test="createUser != null and createUser != ''">
				and t.create_user = #{createUser}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="lastUpdateUser != null and lastUpdateUser != ''">
				and t.last_update_user = #{lastUpdateUser}
			</if>
			<if test="lastUpdateTime != null">
				and t.last_update_time = #{lastUpdateTime}
			</if>
	</select>

	<select id="selectSharedTemps" parameterType="com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam" resultMap="baseResultMapExt">
		select
		<include refid="columns"/>
		from
		cq_data_search_temp
		where create_user != #{createUser} and share = #{share}
		order by create_time desc
	</select>
</mapper>