package com.zjhc.gzwcq.dataQueryModel.mapper;

import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface IDataQueryModelMapper {

    List<DataQueryModelVo> queryByPage(@Param("param") DataQueryModelParam param,
                                       @Param("auditHostingList") Set<String> auditHostingList);

    List<DataQueryModelVo> economicAnalysis(@Param("param") DataQueryModelParam param,
                                            @Param("auditHostingList")Set<String> auditHostingList);

    List<DataQueryModelVo> economicDetail(@Param("param") DataQueryModelParam param,
                                          @Param("auditHostingList")Set<String> auditHostingList);
}
