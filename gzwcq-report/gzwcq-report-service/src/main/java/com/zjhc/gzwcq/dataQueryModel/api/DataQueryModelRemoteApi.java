package com.zjhc.gzwcq.dataQueryModel.api;

import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.zjhc.gzwcq.dataQueryModel.client.DataQueryModelClient;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo;
import com.zjhc.gzwcq.dataQueryModel.service.api.IDataQueryModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@RequestMapping(value="/dataQueryModelRemoteApi")
public class DataQueryModelRemoteApi implements DataQueryModelClient {

    @Autowired
    private IDataQueryModelService dataQueryModelService;

    @Override
    public BootstrapTableModel<DataQueryModelVo> queryByPage(@RequestBody DataQueryModelParam param) throws IllegalAccessException {
        return dataQueryModelService.queryByPage(param);
    }

    @Override
    public List<DataQueryModelVo> selectForList(@RequestBody DataQueryModelParam param) throws IllegalAccessException {
        return dataQueryModelService.selectForList(param);
    }

    @Override
    public List<DataQueryModelVo> economicAnalysis(@RequestBody DataQueryModelParam param) throws IllegalAccessException {
        return dataQueryModelService.economicAnalysis(param);
    }

    @Override
    public BootstrapTableModel<DataQueryModelVo> economicDetail(@RequestBody DataQueryModelParam param) throws IllegalAccessException {
        return dataQueryModelService.economicDetail(param);
    }

    @Override
    public List<DataQueryModelVo> selectForDetailList(@RequestBody DataQueryModelParam param) throws IllegalAccessException {
        return dataQueryModelService.selectForDetailList(param);
    }

    @Override
    public ResponseEntity<byte[]> treeExport(DataQueryModelParam param) {
        return dataQueryModelService.treeExport(param);
    }
}
