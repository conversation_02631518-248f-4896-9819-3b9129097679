package com.zjhc.gzwcq.dataSearchTemp.mapper;

import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IDataSearchTempMapper {
	
	/*保存对象*/
	void insert(DataSearchTemp dataSearchTemp);
	
	//物理删除
	void deleteByPrimaryKeys(Map<String, Object> map);
	
	void deleteByPrimaryKey(@Param("id")String id);
	
	//逻辑删除
	void logicDeleteByPrimaryKeys(Map<String, Object> map);
	
	void logicDeleteByPrimaryKey(Map<String, Object> map);
	
	/*根据非空属性更新对象信息*/
	void updateIgnoreNull(DataSearchTemp dataSearchTemp);
	
	/**更新*/
	void update(DataSearchTemp dataSearchTemp);
	
	/*分页查询对象*/
	List<DataSearchTempVo> queryDataSearchTempForList(DataSearchTempParam dataSearchTempParam);
	
	/*数据总量查询*/
	long queryTotalDataSearchTemps(DataSearchTempParam dataSearchTempParam);
	
	/*根据主键查询对象*/
	DataSearchTemp selectDataSearchTempByPrimaryKey(DataSearchTemp dataSearchTemp);
	
	/*根据部分属性对象查询全部结果，不分页*/
	List<DataSearchTempVo> selectForList(DataSearchTemp dataSearchTemp);
	
	/**
	 * 数据唯一性验证
	 * */
	List<DataSearchTemp> selectForUnique(DataSearchTemp dataSearchTemp);

	/**
	 * 查询共享模板(不包括自己的)
	 */
	List<DataSearchTempVo> selectSharedTemps(DataSearchTempParam param);
}