package com.zjhc.gzwcq.dataQueryModel.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.boot.IAdmin.common.domain.BootstrapTableModel;
import com.boot.IAdmin.common.utils.Constants;
import com.boot.IAdmin.dict.model.Dictionary;
import com.boot.iAdmin.access.service.impl.OrganizationService;
import com.boot.iAdmin.cache.core.DictCacheStrategy;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModel;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelVo;
import com.zjhc.gzwcq.dataQueryModel.enums.MultiLevelDicFieldEnum;
import com.zjhc.gzwcq.dataQueryModel.mapper.IDataQueryModelMapper;
import com.zjhc.gzwcq.dataQueryModel.service.api.IDataQueryModelService;
import com.zjhc.gzwcq.sxzbpz.client.SxzbpzFeignClient;
import com.zjhc.gzwcq.sxzbpz.entity.Sxzbpz;
import com.zjhc.gzwcq.sxzbpz.entity.SxzbpzVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataQueryModelServiceImpl implements IDataQueryModelService {

    @Resource
    private IDataQueryModelMapper dataQueryModelMapper;
    @Resource
    private SxzbpzFeignClient sxzbpzFeignClient;
    @Autowired(required = false)
    private DictCacheStrategy dictCacheStrategy;
    @Resource
    private OrganizationService organizationService;
    
    private final static String BASIC_CQ_TABLE = "view_cq_jbxxb";

    /**
     * 数据查询(分页)
     */
    @Override
    public BootstrapTableModel<DataQueryModelVo> queryByPage(DataQueryModelParam param) {
        this.paramTransfer(param);
        BootstrapTableModel<DataQueryModelVo> model = new BootstrapTableModel<>();
        Set<String> visibles = organizationService.getVisibles();
        PageHelper.startPage(param.getPageNumber(), param.getLimit());
        List<DataQueryModelVo> vos = dataQueryModelMapper.queryByPage(param,visibles);
        this.dicFieldsTransfer(vos);
        PageInfo<DataQueryModelVo> pageInfo = new PageInfo<>(vos);
        model.setRows(pageInfo.getList());
        model.setTotal(pageInfo.getTotal());
        return model;
    }

    /**
     * 数据查询(不分页)
     */
    @Override
    public List<DataQueryModelVo> selectForList(DataQueryModelParam param) {
        this.paramTransfer(param);
        List<DataQueryModelVo> vos = dataQueryModelMapper.queryByPage(param,organizationService.getVisibles());
        this.dicFieldsTransfer(vos);
        return vos;
    }

    /**
     * 经济行为分析结果页
     */
    @Override
    public List<DataQueryModelVo> economicAnalysis(DataQueryModelParam param) {
        this.paramTransfer(param);
        //展示字段处理,只有数字类型合计才有意义,并且是结果页指标
        if (CollectionUtils.isNotEmpty(param.getSelect())){
            List<SxzbpzVo> numSelect = new ArrayList<>();
            for (SxzbpzVo sxzbpzVo : param.getSelect()) {
                if (Constants.FIELD_TYPE_NUM.equals(sxzbpzVo.getType()) &&
                        sxzbpzVo.getEconomicBehaviorAnalysis() == 1  &&
                        sxzbpzVo.getStatistics() == 1){
                    numSelect.add(sxzbpzVo);
                }
            }
            param.setNumSelect(numSelect);
        }
        List<DataQueryModelVo> vos = dataQueryModelMapper.economicAnalysis(param,organizationService.getVisibles());
        if (CollectionUtils.isNotEmpty(vos)){
            for (int i = 0; i < vos.size(); i++) {
                vos.get(i).setNo(i+1);
                if (StringUtils.isNotBlank(vos.get(i).getJB_ZYCQDJQX())){
                    vos.get(i).setSituationCode(vos.get(i).getJB_ZYCQDJQX());
                }else if (StringUtils.isNotBlank(vos.get(i).getJB_BDCQDJQX())){
                    vos.get(i).setSituationCode(vos.get(i).getJB_BDCQDJQX());
                }else if (StringUtils.isNotBlank(vos.get(i).getJB_ZXCQDJQX())){
                    vos.get(i).setSituationCode(vos.get(i).getJB_ZXCQDJQX());
                }
            }
        }
        //字典转换
        this.dicFieldsTransfer(vos);
        return vos;
    }

    /**
     * 经济行为分析_明细页
     */
    @Override
    public BootstrapTableModel<DataQueryModelVo> economicDetail(DataQueryModelParam param) {
        this.paramTransfer(param);
        BootstrapTableModel<DataQueryModelVo> model = new BootstrapTableModel<>();
        Set<String> visibles = organizationService.getVisibles();
        PageHelper.startPage(param.getPageNumber(), param.getLimit());
        List<DataQueryModelVo> vos = dataQueryModelMapper.economicDetail(param,visibles);
        this.dicFieldsTransfer(vos);
        PageInfo<DataQueryModelVo> pageInfo = new PageInfo<>(vos);
        model.setRows(pageInfo.getList());
        model.setTotal(pageInfo.getTotal());
        return model;
    }

    /**
     * 经济行为分析_明细页(不分页)
     */
    @Override
    public List<DataQueryModelVo> selectForDetailList(DataQueryModelParam param) {
        this.paramTransfer(param);
        List<DataQueryModelVo> vos = dataQueryModelMapper.economicDetail(param,organizationService.getVisibles());
        for (int i = 0; i < vos.size(); i++) {
            vos.get(i).setNo(i+1);
        }
        this.dicFieldsTransfer(vos);
        return vos;
    }

    /**
     * 转换查询条件
     */
    @Override
    public void paramTransfer(DataQueryModelParam param){
        List<SxzbpzVo> where = param.getWhere();
        List<SxzbpzVo> select = param.getSelect();
        Set<String> joinSet = new HashSet<>();//join的表名
        Set<String> leftJoinSet = new HashSet<>();//left join的表名
        List<SxzbpzVo> multiLevelSetCheckBox = new ArrayList<>();//多选字典的筛选条件
        List<SxzbpzVo> multiLevelSetRadio = new ArrayList<>();//单选字典的筛选条件
        //查询条件
        if (CollectionUtils.isNotEmpty(where)) {
            List<SxzbpzVo> dicWhere = new ArrayList<>();
            List<SxzbpzVo> textWhere = new ArrayList<>();
            List<SxzbpzVo> dateWhere = new ArrayList<>();
            List<SxzbpzVo> dateRangeWhere = new ArrayList<>();
            List<SxzbpzVo> numWhere = new ArrayList<>();
            for (SxzbpzVo vo : where) {
                //必须不是空才可以join
                if (StringUtils.isNotBlank(vo.getFieldName()) && vo.getFieldValue() != null){
                    joinSet.add(vo.getTableName());
                }
                //针对日期区间和数字区间,转换为字符串形式,以逗号隔开
                if (vo.getFieldValue() instanceof Collection){
                    Collection<Object> objs = (Collection<Object>) vo.getFieldValue();
                    vo.setFieldValue(objs.toArray()[0]+","+objs.toArray()[1]);
                }
                //字典
                if (Constants.FIELD_TYPE_DIC.equals(vo.getType())) {
                    //20220429变更:多层级字典筛选需要查出所选字典值及其下级的所有数据
                    if (vo.getFieldValue() != null && StringUtils.isNotBlank(vo.getFieldValue().toString())
                            && EnumUtils.isValidEnum(MultiLevelDicFieldEnum.class,vo.getFieldName())){
                        Set<String> childrenVals = new HashSet<>();
                        childrenVals.add(vo.getFieldValue().toString());
                        Dictionary dic = dictCacheStrategy.getDicByVal(vo.getDicType(), vo.getFieldValue().toString());
                        if (dic != null) {
                            //递归查子级字典val
                            this.getValsByTypeCodeAndParent(childrenVals, vo.getDicType(), dic.getId());
                        }
                        vo.setDicVals(childrenVals);
                        if (MultiLevelDicFieldEnum.valueOf(vo.getFieldName()).isCheckBox()){
                            //多选
                            multiLevelSetCheckBox.add(vo);
                        }else {
                            //单选
                            multiLevelSetRadio.add(vo);
                        }
                    }else {
                        dicWhere.add(vo);
                    }
                //文本
                } else if (Constants.FIELD_TYPE_TEXT.equals(vo.getType())) {
                    textWhere.add(vo);
                //日期
                } else if (Constants.FIELD_TYPE_DATE.equals(vo.getType())) {
                    dateWhere.add(vo);
                //日期区间
                } else if (Constants.FIELD_TYPE_DATE_RANGE.equals(vo.getType())) {
                    dateRangeWhere.add(vo);
                //数字
                } else if (Constants.FIELD_TYPE_NUM.equals(vo.getType())) {
                    numWhere.add(vo);
                }
            }
            param.setDicWhere(dicWhere);
            param.setTextWhere(textWhere);
            param.setDateWhere(dateWhere);
            param.setDateRangeWhere(dateRangeWhere);
            param.setNumWhere(numWhere);
            param.setMultiLevelListCheckBox(multiLevelSetCheckBox);
            param.setMultiLevelListRadio(multiLevelSetRadio);
        }
        //展示字段
        if (CollectionUtils.isNotEmpty(select)){
            for (SxzbpzVo vo : select) {
                leftJoinSet.add(vo.getTableName());
            }
        }
        //将leftJoin的表中与join的表重复的删除(join的表参与筛选,优先级更高)
        leftJoinSet.removeAll(joinSet);
        //除掉多余的jbxxb表
        joinSet.removeIf(BASIC_CQ_TABLE::equals);
        leftJoinSet.removeIf(BASIC_CQ_TABLE::equals);
        param.setJoinTableName(joinSet);
        param.setLeftJoinTableName(leftJoinSet);
        //20220427变更：注册地筛选要查出所选注册地及以下的注册地的全部
        if (StringUtils.isNotBlank(param.getJB_ZCD())) {
        	Dictionary zcd = dictCacheStrategy.getDicByVal("ZCD", param.getJB_ZCD());
        	if (zcd != null) {
        		//递归查出该注册地及以下的注册地
        		Set<String> childrenVals = new HashSet<>();
        		childrenVals.add(param.getJB_ZCD());
        		this.getValsByTypeCodeAndParent(childrenVals, "ZCD", zcd.getId());
        		param.setZcdSet(childrenVals);
        	}
        }
        //20220428变更：主要行业筛选要查出所选主要行业及以下的全部
        if (StringUtils.isNotBlank(param.getJB_ZYHY())) {
            Dictionary zyhy = dictCacheStrategy.getDicByVal("INDUSTRY_CLASSIFICATION_TREE", param.getJB_ZYHY());
            if (zyhy != null) {
                //递归查出该行业及以下的行业
                Set<String> childrenVals = new HashSet<>();
                childrenVals.add(param.getJB_ZYHY());
                this.getValsByTypeCodeAndParent(childrenVals, "INDUSTRY_CLASSIFICATION_TREE", zyhy.getId());
                param.setZyhySet(childrenVals);
            }
        }
    }

    /**
     * 递归获取字典子级vals
     */
    @Override
    public void getValsByTypeCodeAndParent(Set<String> childrenVals, String typeCode,long parent){
    	List<Dictionary> children = dictCacheStrategy.getByParent(typeCode, parent);
    	for (Dictionary d : children) {
    		childrenVals.add(d.getVal());
    		this.getValsByTypeCodeAndParent(childrenVals, typeCode, d.getId());
    	}
    }

    /**
     * 将设置为字典类型的字段转换为其对应的实际值
     */
    private void dicFieldsTransfer(List<DataQueryModelVo> vos) {
        if (CollectionUtils.isNotEmpty(vos)) {
            Class<DataQueryModel> clz = DataQueryModel.class;
            //获取所有字段
            Field[] fields = clz.getDeclaredFields();
            //存放字段Field和对应字典type_code的Map
            Map<Field, String> map;
            //获取所有被设置为字典类型的字段
            List<Sxzbpz> dicSxs = sxzbpzFeignClient.selectForList(new Sxzbpz()).stream().filter(sxzbpz ->
                    Constants.FIELD_TYPE_DIC.equals(sxzbpz.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dicSxs)) {
                map = new HashMap<>(dicSxs.size());
                for (Sxzbpz dicSx : dicSxs) {
                    for (Field field : fields) {
                        field.setAccessible(true);
                        if (field.getName().equals(dicSx.getFieldName())) {
                            map.put(field, dicSx.getDicType());
                            break;
                        }
                    }
                }
                //将查询结果集合中所有的字典类型字段值全部由字典key替换为字典value
                for (DataQueryModelVo vo : vos) {
                    map.entrySet().stream().parallel().forEach((entry) -> {
                        try {
                            Field field = entry.getKey();
                            String value = entry.getValue();
                            Object obj = field.get(vo);
                            String dicText = "";
                            if (obj != null){
                                StringBuilder builder = new StringBuilder();
                                String[] dicVals = ((String) obj).split(",");
                                for (String val : dicVals) {
                                    String text = dictCacheStrategy.getTextByVal(value, val);
                                    if(StringUtils.isNotBlank(text)){
                                        builder.append(text).append(",");
                                    }
                                }
                                if (builder.length() > 0){
                                    dicText = builder.substring(0,builder.length()-1);
                                }
                            }
                            field.set(vo,dicText);
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    });
                }
            }
        }
    }

    /**
     * 产权树导出
     */
    @Override
    public ResponseEntity<byte[]> treeExport(DataQueryModelParam param) {

        String fileName ="组织机构表树.xls";
        //拿到数据
        //organizationService.selectOrganization();
        //表头
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        List<List<String>> header = new ArrayList<>();
        //判断有多少层级,就动态添加多少列
        for (int i = 0;i<=3;i++) {
            List<String> cellContain1 = new ArrayList<>();
            cellContain1.add("企业名称");
            header.add(cellContain1);
        }

        List<String> cellContain3 = new ArrayList<>();
        cellContain3.add("企业代码");
        header.add(cellContain3);
        List<String> cellContain4 = new ArrayList<>();
        cellContain4.add("所属国资监管机构");
        header.add(cellContain4);
        List<String> cellContain5 = new ArrayList<>();
        cellContain5.add("创建人");
        header.add(cellContain5);

        //数据
        List<List<Object>> dataList = new ArrayList<>();
        List<Object> list = new ArrayList<>();
        dataList.add(list);


        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 自动居中
        headWriteCellStyle.setWrapped(true);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        //设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //设置 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        //创建流
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData("attachment",
                new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        //生成easyexcel的流
        EasyExcel.write(out).head(header).sheet("sheet1")
                .registerWriteHandler(horizontalCellStyleStrategy)
                .doWrite(dataList);
        return new ResponseEntity<>(out.toByteArray(), headers, HttpStatus.CREATED);
    }



}
