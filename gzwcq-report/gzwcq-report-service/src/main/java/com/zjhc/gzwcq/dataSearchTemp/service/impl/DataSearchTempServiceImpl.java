package com.zjhc.gzwcq.dataSearchTemp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.boot.iAdmin.access.common.SpringSecurityUserTools;
import com.boot.iAdmin.access.model.user.SysUser;
import com.github.pagehelper.PageHelper;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTemp;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempParam;
import com.zjhc.gzwcq.dataSearchTemp.entity.DataSearchTempVo;
import com.zjhc.gzwcq.dataSearchTemp.mapper.IDataSearchTempMapper;
import com.zjhc.gzwcq.dataSearchTemp.service.api.IDataSearchTempService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DataSearchTempServiceImpl implements IDataSearchTempService {
	
	@Resource
	private IDataSearchTempMapper dataSearchTempMapper;

	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void insert(DataSearchTemp dataSearchTemp){
		dataSearchTemp.setCreateUser(this.getCurrentUserId());//创建人
		dataSearchTemp.setCreateTime(new Date());//创建时间
		dataSearchTemp.setLastUpdateUser(this.getCurrentUserId());//更新人
		dataSearchTemp.setLastUpdateTime(new Date());//更新时间
		//将不存的查询条件移除
		this.removeConditions(dataSearchTemp, UNSAVED_CONDITIONS);
		dataSearchTempMapper.insert(dataSearchTemp);
	}

	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKeys(Map<String,Object> map){
		dataSearchTempMapper.deleteByPrimaryKeys(map);
	}
  
  	/**
	 * 删除一个对象
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void deleteByPrimaryKey(String id) {
		dataSearchTempMapper.deleteByPrimaryKey(id);
	}

	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void updateIgnoreNull(DataSearchTemp dataSearchTemp){
		dataSearchTemp.setLastUpdateUser(this.getCurrentUserId());//更新人
		dataSearchTemp.setLastUpdateTime(new Date());//更新时间
		dataSearchTempMapper.updateIgnoreNull(dataSearchTemp);
	}
	
	/**
	* 更新
	*/
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void update(DataSearchTemp dataSearchTemp){
		dataSearchTemp.setLastUpdateUser(this.getCurrentUserId());//更新人
		dataSearchTemp.setLastUpdateTime(new Date());//更新时间
		dataSearchTempMapper.update(dataSearchTemp);
	}

	@Override
	public List<DataSearchTempVo> queryDataSearchTempByPage(DataSearchTempParam dataSearchTempParam) {
      	//分页
      	PageHelper.startPage(dataSearchTempParam.getPageNumber(),dataSearchTempParam.getLimit(),false);
		return dataSearchTempMapper.queryDataSearchTempForList(dataSearchTempParam);
	}

	@Override
	public DataSearchTemp selectDataSearchTempByPrimaryKey(DataSearchTemp DataSearchTemp) {
		return dataSearchTempMapper.selectDataSearchTempByPrimaryKey(DataSearchTemp);
	}

	@Override
	public long queryTotalDataSearchTemps(DataSearchTempParam dataSearchTempParam) {
		return dataSearchTempMapper.queryTotalDataSearchTemps(dataSearchTempParam);
	}
	
	/**
	*根据部分属性对象查询全部结果，不分页
	*/
	@Override
	public List<DataSearchTempVo> selectForList(DataSearchTempParam param){
		return dataSearchTempMapper.selectForList(param);
	}
	
	/**
	 * 数据唯一性验证
	 * */
	@Override
	public boolean validateUniqueParam(DataSearchTemp dataSearchTemp) {
		return dataSearchTempMapper.selectForUnique(dataSearchTemp).isEmpty();
	}
	
	/**
	 * 保存单个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void saveOne(DataSearchTemp dataSearchTemp) {
		if(StrUtil.isBlank(dataSearchTemp.getId())) {
			this.insert(dataSearchTemp);
		}else {
			this.updateIgnoreNull(dataSearchTemp);
		}
	}
	
	/**
	 * 保存多个对象
	 * <p>存在则更新，不存在则新增
	 * */
	@Override
  	@Transactional(rollbackFor=Exception.class)
	public void multipleSaveAndEdit(DataSearchTemp[] objs) {
		for(DataSearchTemp dataSearchTemp : objs) {
			this.saveOne(dataSearchTemp);
		}
	}

	/**
	 * 查询我的模板
	 */
	@Override
	public List<DataSearchTempVo> selectMyTemps(DataSearchTempParam param) {
		param.setCreateUser(this.getCurrentUserId());
		return this.selectForList(param).parallelStream()
				.sorted(Comparator.comparing(DataSearchTempVo::getCreateTime).reversed()).collect(Collectors.toList());
	}

	/**
	 * 查询共享模板(不包括自己的)
	 */
	@Override
	public List<DataSearchTempVo> selectSharedTemps(DataSearchTempParam param) {
		param.setCreateUser(this.getCurrentUserId());
		param.setShare(1);
		List<DataSearchTempVo> vos = dataSearchTempMapper.selectSharedTemps(param);
		//共享模板的查询范围要去掉
		vos.forEach(vo -> this.removeConditions(vo,NOT_RETURNED_PUBLIC_CONDITIONS));
		return vos;
	}

	/**
	 * 移除查询条件tempJson中指定的字段
	 * @param temp 查询模板对象
	 * @param conditions 需要移除的字段值
	 */
	@Override
	public <T extends DataSearchTemp> void removeConditions(T temp, String... conditions) {
		if (temp != null && conditions != null && StrUtil.isNotBlank(temp.getTempJson())){
			JSONObject jsonObject = JSON.parseObject(temp.getTempJson());
			for (String condition : conditions) {
				jsonObject.remove(condition);
			}
			temp.setTempJson(jsonObject.toJSONString());
		}
	}

	/**
	 * 获取当前登陆人id
	 */
	private String getCurrentUserId(){
		return ((SysUser) SpringSecurityUserTools.instance().getUser(null)).getUser_id();
	}
}
