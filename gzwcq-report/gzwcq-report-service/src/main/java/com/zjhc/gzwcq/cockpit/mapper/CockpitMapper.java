package com.zjhc.gzwcq.cockpit.mapper;



import com.zjhc.gzwcq.cockpit.entity.CockpitProvinceData;
import com.zjhc.gzwcq.cockpit.entity.CockpitSSQYKH;
import com.zjhc.gzwcq.cockpit.entity.OneLevelOrg;
import com.zjhc.gzwcq.screen.entity.TimelinessRate;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


public interface CockpitMapper {
    /**
     * 省地市企业户数统计-企业户数图-按企业类别
     * @return
     */
    List<CockpitProvinceData> qylbPrefectureAndCity();

    /**
     * 省地市企业户数统计-企业户数图-按组织形式
     * @return
     */
    List<CockpitProvinceData> zzxsPrefectureAndCity();

    /**
     * 省地市企业户数统计-企业户数图-按企业级次
     * @return
     */
    List<CockpitProvinceData>qyjcPrefectureAndCity();

    /**
     * 省属企业户数统计-一级企业
     * @return
     */
    List<OneLevelOrg> queryOneLevelOrg();

    /**
     * 省属企业户数统计-企业户数图-按企业类别
     * @return
     */
    List<CockpitProvinceData> qylbByOneLevel();
    /**
     * 省属企业户数统计-企业户数图-按组织形式
     * @return
     */
    List<CockpitProvinceData> zzxsByOneLevel();

    /**
     * 省属企业户数统计-企业户数图-按企业级次
     * @return
     */
    List<CockpitProvinceData> qyjcByOneLevel();


    /**
     * 省属企业考核数量
     */
    List<CockpitSSQYKH> getSSQYKHSL();

    List<TimelinessRate> queryTimelinessRateList(@Param("year") String year);
    /**
     * 查询第一次提交到国资委的时间
     * @param
     * @return
     */
    List<TimelinessRate> queryAllApprovalDate(@Param("list") List<String> idList);

    /**
     * 查询退回率提交次数
     * @param type  1本企业，2本级及以下企业
     * @param orgId  登陆人组织id
     * @return
     */
    List<TimelinessRate> queryAllSubmitList(@Param("year")String year);

    /**
     * 查询退回率退回次数
     * @param businessInfoIds
     * @param year
     * @return
     */
    List<String> queryAllReturnList(@Param("list")List<String> businessInfoIds,@Param("year")String year);


    /**
     * 省地市企业户数统计-企业户数图-按企业级次 性能优化
     * <AUTHOR>
     */
    List<CockpitProvinceData> qyjcPrefectureAndCityV2();


    /**
     * 省地市企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    List<CockpitProvinceData> qylbPrefectureAndCityV2();


    /**
     * 省地市企业户数统计-企业户数图-按组织形式 性能优化
     * <AUTHOR>
     */
    List<CockpitProvinceData> zzxsPrefectureAndCityV2();

    /**
     * 省属企业户数统计-企业户数图-按企业类别 性能优化
     * <AUTHOR>
     */
    List<CockpitProvinceData> qylbByOneLevelV2();

    /**
     * 省属企业户数统计-企业户数图-按组织形式 性能优化
     * <AUTHOR>
     */
    List<CockpitProvinceData> zzxsByOneLevelV2();

    /**
     * 省属企业户数统计-企业户数图-按企业级次 性能优化
     * <AUTHOR>
     */
    List<CockpitProvinceData> qyjcByOneLevelV2();
}
