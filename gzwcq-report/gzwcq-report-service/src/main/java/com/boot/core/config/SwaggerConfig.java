package com.boot.core.config;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import io.swagger.annotations.ApiOperation;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.paths.RelativePathProvider;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * API生成器
 * http://swagger-ui.html
 * */
@EnableSwagger2
public class SwaggerConfig {
	
	@Autowired
	private ServletContext servletContext;
	
	@Value("${server.servlet.context-path}")
	private String contextPath;
	
	/**
     * 创建一个Docket对象
     * 调用select()方法，
     * 生成ApiSelectorBuilder对象实例，该对象负责定义外漏的API入口
     * 通过使用RequestHandlerSelectors和PathSelectors来提供Predicate，在此我们使用any()方法，将所有API都通过Swagger进行文档管理
     * @return
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .globalOperationParameters(buildToken())//添加全局token
                .pathProvider(new RelativePathProvider(servletContext) {//增加访问前缀
		            @Override
		            public String getApplicationBasePath() {
		                return contextPath+"/jwt";
		            }
		        })
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))//指定显示该注解修饰的接口
//                .apis(RequestHandlerSelectors.withClassAnnotation(Controller.class))
                .paths(PathSelectors.any())
                .build();
    }
    
    // 创建api的基本信息
    private ApiInfo apiInfo() {
    	return new ApiInfoBuilder()
                //标题
                .title("项目使用Swagger2构建RESTful APIs")
                //简介
                .description("")
                //服务条款
                .termsOfServiceUrl("")
                //作者个人信息
                .contact(new Contact("","",""))
                //版本
                .version("1.0")
                .build();
    }
    
    //创建统一TOKEN
    private List<Parameter> buildToken(){
    	List<Parameter> parameters = new ArrayList<>();
        parameters.add(new ParameterBuilder()
                .name("Authorization")
                .description("认证token")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false)
                .build());
        return parameters;
    }
}