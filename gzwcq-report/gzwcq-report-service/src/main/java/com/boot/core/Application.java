package com.boot.core;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.boot.IAdmin.common.utils.SpringContextUtil;
import com.boot.core.config.DataSourceFilterConfig;
import com.boot.core.config.MvcConfig;
import com.boot.core.config.SwaggerConfig;
import com.boot.iAdmin.jwt.EnableServiceJWT;

/**
 * 容器启动类
 * @fix:通过TOMCAT等外部web容器启动必须实现SpringBootServletInitializer，main方法启动可以不实现SpringBootServletInitializer
 * */
//禁用springboot自动数据源配置 exclude排除
@SpringBootApplication(scanBasePackages= {"com.boot","com.zjhc"})
//加载配置类
@Import({MvcConfig.class,DataSourceFilterConfig.class,SwaggerConfig.class})
//mapper扫描包
@MapperScan(value="com.**.mapper.**")
@EnableTransactionManagement
@EnableServiceJWT//开启JWT支持
@EnableDiscoveryClient // 开启服务发现
@EnableFeignClients(basePackages= {"com.zjhc"}) // 开启Feign
public class Application extends SpringBootServletInitializer {
	
	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);//内置web容器启动项目
		SpringContextUtil.setApplicationContext(context);//缓存IOC容器方便代码获取容器
	}
	
	/**
	 * 实现该方法用于使用外部tomcat启动时，让spring容器加载当前配置类，能够正确读取配置信息（主要是ComponentScan让容器知道扫描哪些包）从而能够正确初始化spring容器并顺利加载所有配置以及装载所需的bean
	 * @fix:如果是通过main方法启动系统则可不实现该方法
	 * */
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(Application.class);
	}
	
}
