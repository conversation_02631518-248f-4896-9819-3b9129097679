#自定义属性
com:
  auto:
    mvc: 
      urlMappings:
        - /jwt/*
    aop:
      log:
        saveAll: false
    security:
      logoutUrl: /access/login?errorCode=LOGOUT #登出地址/auto/access/login?errorCode=LOGOUT
      loginSuccessUrl : /access/index #登录成功跳转地址
      loginFailUrl : /access/login?errorCode=AUTHENTICATION_FAILURE #登录失败跳转地址
      loginFormUrl : /access/login?errorCode=TIMEOUT #超时登录地址
      errorPage403 : /resource/page/error/403.jsp #权限不足跳转地址
      isCluster : true #是否使用分布式缓存共享权限
      sessionStoreRedis: true #session是否存储在redis中
      sessionStoreKey: ${com.environment.sessionStoreKey} #session中存储的key
      sessionControlIgnores: #忽略session并发控制用户集合
       # - admin
      redis_metadata_key: ${com.environment.redis_metadata_key} #redis中缓存KEY值
      maximumSessions: -1 #session并发控制，默认值为-1
      passwordEncoder: org.springframework.security.crypto.password.Pbkdf2PasswordEncoder #密码加密策略
      ignores:
        - /access/login
        - /
        - /**/swagger-resources/**
        - /**/v2/**
        - /**/webjars/**
        - /**/swagger-ui.html
        - /actuator/** #springboot 监控
        - /jwt/** #JWT请求不经过security
    cache:
      enable: true #是否启用系统缓存
      nameSpace: ${com.environment.cache-name}
