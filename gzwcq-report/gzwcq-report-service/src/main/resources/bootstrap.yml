gzwcq:
  environment:
    #测试
    #server-addr: 127.0.0.1:8848
    #namespace: 0c319f00-9c28-409b-ac88-6007b1c80cd4
    #生产
#    server-addr: 10.10.180.247:8848
#    namespace: aa22a2cb-ccc1-44b5-b2bb-1f60e769b8b5
    #本地 #www.hhycrm.com:30848
    server-addr: 127.0.0.1:8848
    namespace: 2d0c13f2-ca1b-4f00-a409-e6adc8af6044

spring:
  application:
    name: gzwcq-report-service #微服务唯一标识
  main:
    allow-bean-definition-overriding: true #支持多个接口使用@FeignClient调用同一个服务
  cloud:
    nacos:
      config:
        #注册中心地址
        server-addr: ${gzwcq.environment.server-addr}
        username: nacos
        password: admin@123
        file-extension: yaml
        prefix: gzwcq-report-service
        #命名空间
        namespace: ${gzwcq.environment.namespace}
      discovery:
        enabled: true
        #nacos注册中心地址
        server-addr: ${gzwcq.environment.server-addr}
        username: nacos
        password: admin@123
        register-enabled: true
        #命名空间
        namespace: ${gzwcq.environment.namespace}
#  zipkin:
#    base-url: http://127.0.0.1:9411/ #zipkin地址
#    discovery-client-enabled: false  #不用开启服务发现
  sleuth:
    sampler:
      probability: 1.0 #采样百分比
        
feign:
  hystrix:
    enabled: false #关闭降级，否则无法拿到远程服务异常信息
  httpclient:
    enabled: true #指定feign使用httpclient远程调用，需要依赖jar feign-httpclient
    
hystrix:
  # 线程池
  threadpool:
    default:
      #默认为10,基本得原则时保持线程池尽可能小，他主要是为了释放压力，防止资源被阻塞
      coreSize: 50
      ## 最大排队长度。默认-1,不能动态调整
      maxQueueSize: 1000
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 15000
            
ribbon:
  OkToRetryOnAllOperations: false #对所有操作请求都进行重试,默认false
  ReadTimeout: 30000   #负载均衡超时时间，默认值5000
  ConnectTimeout: 10000 #ribbon请求连接的超时时间，默认值2000
  MaxAutoRetries: 0     #对当前实例的重试次数，默认0
  MaxAutoRetriesNextServer: 1 #对切换实例的重试次数，默认1