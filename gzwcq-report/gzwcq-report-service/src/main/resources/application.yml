server:
  port: 8002
  servlet:
    context-path: /gzwcq
    session:
      timeout: 1800 # Session timeout. If a duration suffix is not specified, seconds will be used
  max-http-header-size: 102400 #100 KB
  error: #因为boot2.3.x版本可能考虑信息安全问题,默认设置无法返回远程调用异常
    include-message: always
    include-exception: true

spring:
  profiles:
    active:
     - dev #环境，dev/pst/prod
    include:
    - access
    - mysql #数据库
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
  thymeleaf:
    cache: false
    encoding: utf-8
    mode: html5
    prefix: /resource/page
    suffix: .html
  security:
    filter:
      dispatcher-types: request #设置只拦截request类型请求
  jackson:
    time-zone: GMT+8 #时区
    serialization:
      write-dates-as-timestamps: true #springboot 2.0以上版本默认返回string,修改转为long
  redis: 
    host: ${com.environment.redis.host}
    port: ${com.environment.redis.port}
    password: ${com.environment.redis.password}
    jedis:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 100
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 3000
        # 连接池中的最大空闲连接
        max-idle: 50
        # 连接池中的最小空闲连接
        min-idle: 10
    # 连接超时时间（毫秒）
    timeout: 3000
  session:
    store-type: redis
    timeout: 3600
    redis:
      namespace: spring-session-${spring.application.name} #命名空间，防止多个系统使用同一个redis管理sessin时互相冲突

mybatis:
  configuration:
    map-underscore-to-camel-case: false #驼峰自动转换 user_name ==> userName
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #日志,只会在控制台输出
    call-setters-on-nulls: true #map没值返回为null
  mapper-locations:
  - classpath*:com/**/mapper/**/*Mapper.xml

#mybatis分页插件
pagehelper:
    helperDialect: mysql
    reasonable: true #分页合理化参数，默认值为false。当该参数设置为 true 时，pageNum<=0 时会查询第一页， pageNum>pages（超过总数时），会查询最后一页。默认false 时，直接根据参数进行查询

#加载所有的端点/默认只加载了 info / health
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"
            
#日志配置 logback/log4j2
logging:
  file:
    name: logs/${spring.application.name}.log #日志输出路径
    max-size: 100MB
    max-history: 10
  level:
    root: INFO #日志级别
    com.boot.iAdmin.access.mapper: DEBUG #sql,会在控制台和文件中输出
    com.zjhc.gzwcq: DEBUG
    springfox: WARN
    io.swagger.models.parameters: ERROR
    com.alibaba.nacos.client: WARN
  pattern:
    dateformat: yyyy-MM-dd HH:mm:ss.SSS #时间格式
    console: "%d{yyyy-MM-dd HH:mm:ss.S} [${spring.application.name},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}][%thread]:%p:%c%n[MESSAGE]%m%n%n" #控制台输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss.S} [${spring.application.name},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}][%thread]:%p:%c%n[MESSAGE]%m%n%n" #日志文件中输出格式
    