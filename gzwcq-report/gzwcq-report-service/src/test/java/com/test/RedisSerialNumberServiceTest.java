package com.test;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.boot.core.Application;
import com.zjhc.gzwcq.dataQueryModel.entity.DataQueryModelParam;
import com.zjhc.gzwcq.dataQueryModel.service.api.IDataQueryModelService;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RedisSerialNumberServiceTest {

	@Autowired
	private IDataQueryModelService service;

	@Test
	public void test() {
		Set<String> childrenVals = new HashSet<String>();
		DataQueryModelParam param = new DataQueryModelParam();
		param.setJB_ZCD("120106");
		service.paramTransfer(param);
		
//		System.out.println(Arrays.toString(childrenVals.toArray()));
	}
	@Test
	public  void  test001(){
		String[] strings = new String[]{"3AF8579C20000001C04E617AD116201B","3AF8579C40000021F94AA8411605D4DB","3AF8579C400001019892A118F6A67A14","3AF8579C40000161601226C0BE08E27B","	 			3AF8579C400001A1184C8847C8CA9186","3AF8579CA0000381AC93F71DCCB294FF","4C4B0B4E80000001FB8909BB04CD9980","3AF8579C400002011AE99F7A976951EB"," 3AF8579C60000081E287E773EE1F2B1F","3AF8579CA00001E1557C0D715EA08BAD","3AF8579C6000010155F6FF8BD9D8CF1E","3AF8579C60000461DBE3C8B0F5312703"," 3AF8579C800000E159E19BA0EA0E0581","3AF8579CA000000150A300FEFF46038B","3AF8579CA000018115E5EAFC09A41B4E","3AF85AA3E00000C160EEBC9B3C484D91"," 4C30F838800000213A73D999ABFE8A33","4C3107CFE0000001A57FFD667B1208FB","4C30F95F00000021999D85D746A80310","4C2FA6F5C0000001AA87A3A7F0C5DB69"," 4C30FB0760000021B96C7EC3E8E29AC7","4C4AFC6D00000001E4C37B5EA27315D8","4C30FB81E000002133448FF0D1225F13","4C30FBDD6000002186FC69A3D8B389B6"," 4C4B1099C0000001408770C43760047A","5B413887A0000001AFD32B1EB9D31D95","5B463E7240000041BAF7771829F07DBE","6C8EFCBFC0000021D221537EB4E60D52"," 6CB87B8CA0000041CA9D5ACA197413F7","4C311101A0000001C12427210C0CF6C8","6C8F4383000000016CB7D6E8746EA147","6C8F4929C00000011BCCED30FFA63371"," 3AF8579C20000001C04E617AD116201B","3AF8579C40000021F94AA8411605D4DB","3AF8579C400001019892A118F6A67A14","3AF8579C40000161601226C0BE08E27B"," 3AF8579C400001A1184C8847C8CA9186","3AF8579CA0000381AC93F71DCCB294FF","4C4B0B4E80000001FB8909BB04CD9980","3AF8579C400002011AE99F7A976951EB"," 3AF8579C60000081E287E773EE1F2B1F","3AF8579CA00001E1557C0D715EA08BAD","3AF8579C6000010155F6FF8BD9D8CF1E","3AF8579C60000461DBE3C8B0F5312703"," 3AF8579C800000E159E19BA0EA0E0581","3AF8579CA000000150A300FEFF46038B","3AF8579CA000018115E5EAFC09A41B4E","3AF85AA3E00000C160EEBC9B3C484D91"," 4C30F838800000213A73D999ABFE8A33","4C3107CFE0000001A57FFD667B1208FB","4C30F95F00000021999D85D746A80310","4C2FA6F5C0000001AA87A3A7F0C5DB69"," 4C30FB0760000021B96C7EC3E8E29AC7","4C4AFC6D00000001E4C37B5EA27315D8","4C30FB81E000002133448FF0D1225F13","4C30FBDD6000002186FC69A3D8B389B6"," 4C4B1099C0000001408770C43760047A","5B413887A0000001AFD32B1EB9D31D95","5B463E7240000041BAF7771829F07DBE","6C8EFCBFC0000021D221537EB4E60D52"," 6CB87B8CA0000041CA9D5ACA197413F7","4C311101A0000001C12427210C0CF6C8","6C8F4383000000016CB7D6E8746EA147","6C8F4929C00000011BCCED30FFA63371"};
		List<String> collect = Arrays.stream(strings).distinct().collect(Collectors.toList());
		for (String s : collect) {
			System.out.println("s = " + s);
		}
		System.out.println("collect = " + collect);
		int size = collect.size();
		System.err.println("size = " + size);

	}

	public static void main(String[] args) {
		String[] strings = new String[]{ "3AF8579C20000001C04E617AD116201B"," 3AF8579C40000021F94AA8411605D4DB"," 3AF8579C400001019892A118F6A67A14"," 3AF8579C40000161601226C0BE08E27B"," 3AF8579C400001A1184C8847C8CA9186"," 3AF8579CA0000381AC93F71DCCB294FF"," 4C4B0B4E80000001FB8909BB04CD9980"," 3AF8579C400002011AE99F7A976951EB"," 3AF8579C60000081E287E773EE1F2B1F"," 3AF8579CA00001E1557C0D715EA08BAD"," 3AF8579C6000010155F6FF8BD9D8CF1E"," 3AF8579C60000461DBE3C8B0F5312703"," 3AF8579C800000E159E19BA0EA0E0581"," 3AF8579CA000000150A300FEFF46038B"," 3AF8579CA000018115E5EAFC09A41B4E"," 3AF85AA3E00000C160EEBC9B3C484D91"," 4C30F838800000213A73D999ABFE8A33"," 4C3107CFE0000001A57FFD667B1208FB"," 4C30F95F00000021999D85D746A80310"," 4C2FA6F5C0000001AA87A3A7F0C5DB69"," 4C30FB0760000021B96C7EC3E8E29AC7"," 4C4AFC6D00000001E4C37B5EA27315D8"," 4C30FB81E000002133448FF0D1225F13"," 4C30FBDD6000002186FC69A3D8B389B6"," 4C4B1099C0000001408770C43760047A"," 5B413887A0000001AFD32B1EB9D31D95"," 5B463E7240000041BAF7771829F07DBE"," 6C8EFCBFC0000021D221537EB4E60D52"," 6CB87B8CA0000041CA9D5ACA197413F7"," 4C311101A0000001C12427210C0CF6C8"," 6C8F4383000000016CB7D6E8746EA147"," 6C8F4929C00000011BCCED30FFA63371"," 3AF8579C20000001C04E617AD116201B"," 3AF8579C40000021F94AA8411605D4DB"," 3AF8579C400001019892A118F6A67A14"," 3AF8579C40000161601226C0BE08E27B"," 3AF8579C400001A1184C8847C8CA9186"," 3AF8579CA0000381AC93F71DCCB294FF"," 4C4B0B4E80000001FB8909BB04CD9980"," 3AF8579C400002011AE99F7A976951EB"," 3AF8579C60000081E287E773EE1F2B1F"," 3AF8579CA00001E1557C0D715EA08BAD"," 3AF8579C6000010155F6FF8BD9D8CF1E"," 3AF8579C60000461DBE3C8B0F5312703"," 3AF8579C800000E159E19BA0EA0E0581"," 3AF8579CA000000150A300FEFF46038B"," 3AF8579CA000018115E5EAFC09A41B4E"," 3AF85AA3E00000C160EEBC9B3C484D91"," 4C30F838800000213A73D999ABFE8A33"," 4C3107CFE0000001A57FFD667B1208FB"," 4C30F95F00000021999D85D746A80310"," 4C2FA6F5C0000001AA87A3A7F0C5DB69"," 4C30FB0760000021B96C7EC3E8E29AC7"," 4C4AFC6D00000001E4C37B5EA27315D8"," 4C30FB81E000002133448FF0D1225F13"," 4C30FBDD6000002186FC69A3D8B389B6"," 4C4B1099C0000001408770C43760047A"," 5B413887A0000001AFD32B1EB9D31D95"," 5B463E7240000041BAF7771829F07DBE"," 6C8EFCBFC0000021D221537EB4E60D52"," 6CB87B8CA0000041CA9D5ACA197413F7"," 4C311101A0000001C12427210C0CF6C8"," 6C8F4383000000016CB7D6E8746EA147"," 6C8F4929C00000011BCCED30FFA63371"};
		int length = strings.length;
		System.out.println("原长度 = " + length);
		List<String> collect = Arrays.stream(strings).distinct().collect(Collectors.toList());
//			for (String s : collect) {
//				System.out.println("s = " + s);
//			}
			System.out.println("collect = " + collect);
			String pro = "";
		for (String s : collect) {
			pro += ",'"+s+"'";
		}
			int size = collect.size();
			System.err.println("现长度 = " + size);
		System.out.println("pro = " + pro);
	}
}
